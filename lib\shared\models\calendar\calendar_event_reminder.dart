import 'package:freezed_annotation/freezed_annotation.dart';

part 'calendar_event_reminder.freezed.dart';
part 'calendar_event_reminder.g.dart';

/// Enum for reminder types
enum ReminderType {
  /// Notification reminder
  notification,

  /// Email reminder
  email,

  /// SMS reminder
  sms,

  /// WhatsApp reminder
  whatsapp,
}

/// Extension on [ReminderType] for additional functionality
extension ReminderTypeX on ReminderType {
  /// Get the display name for the reminder type
  String get displayName {
    switch (this) {
      case ReminderType.notification:
        return 'Notification';
      case ReminderType.email:
        return 'Email';
      case ReminderType.sms:
        return 'SMS';
      case ReminderType.whatsapp:
        return 'WhatsApp';
    }
  }

  /// Get the description for the reminder type
  String get description {
    switch (this) {
      case ReminderType.notification:
        return 'Push notification on your device';
      case ReminderType.email:
        return 'Email notification';
      case ReminderType.sms:
        return 'SMS notification';
      case ReminderType.whatsapp:
        return 'WhatsApp message';
    }
  }

  /// Get the icon for the reminder type
  String get icon {
    switch (this) {
      case ReminderType.notification:
        return 'notifications';
      case ReminderType.email:
        return 'email';
      case ReminderType.sms:
        return 'message';
      case ReminderType.whatsapp:
        return 'whatsapp';
    }
  }
}

/// Model for calendar event reminder
@freezed
abstract class CalendarEventReminder with _$CalendarEventReminder {
  /// Creates a [CalendarEventReminder]
  const factory CalendarEventReminder({
    /// The type of reminder
    required ReminderType type,

    /// The time before the event to send the reminder (in minutes)
    required int timeBeforeEvent,

    /// Whether the reminder is enabled
    @Default(true) bool isEnabled,

    /// The custom message for the reminder
    String? customMessage,
  }) = _CalendarEventReminder;

  /// Creates a [CalendarEventReminder] from JSON
  factory CalendarEventReminder.fromJson(Map<String, dynamic> json) =>
      _$CalendarEventReminderFromJson(json);
}

/// Extension on [CalendarEventReminder] for additional functionality
extension CalendarEventReminderX on CalendarEventReminder {
  /// Get the display time for the reminder
  String get displayTime {
    if (timeBeforeEvent < 60) {
      return '$timeBeforeEvent minutes before';
    } else if (timeBeforeEvent < 1440) {
      return '${(timeBeforeEvent / 60).floor()} hours before';
    } else {
      return '${(timeBeforeEvent / 1440).floor()} days before';
    }
  }

  /// Get the description for the reminder
  String get description {
    return '${type.displayName} $displayTime';
  }

  /// Get the icon for the reminder
  String get icon => type.icon;
}
