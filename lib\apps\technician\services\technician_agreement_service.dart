import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../widgets/dialogs/technician_legal_agreement_dialog.dart';

/// Service to handle the technician legal agreement
class TechnicianAgreementService {
  static const String _agreementKey = 'technician_legal_agreement_accepted';
  static const String _agreementDateKey = 'technician_legal_agreement_date';
  static const int _agreementValidityDays = 90; // Reconfirm every 90 days

  /// Checks if the technician has already accepted the agreement
  static Future<bool> hasAcceptedAgreement() async {
    final prefs = await SharedPreferences.getInstance();
    final isAccepted = prefs.getBool(_agreementKey) ?? false;

    debugPrint(
        'TechnicianAgreementService: hasAcceptedAgreement() = $isAccepted');

    if (!isAccepted) {
      debugPrint('TechnicianAgreementService: Agreement not accepted');
      return false;
    }

    // Check if the agreement acceptance has expired
    final agreementDateStr = prefs.getString(_agreementDateKey);
    if (agreementDateStr == null) {
      debugPrint('TechnicianAgreementService: No agreement date found');
      return false;
    }

    try {
      final agreementDate = DateTime.parse(agreementDateStr);
      final now = DateTime.now();
      final difference = now.difference(agreementDate).inDays;

      // If the agreement is older than the validity period, it's expired
      if (difference > _agreementValidityDays) {
        debugPrint('TechnicianAgreementService: Agreement expired');
        return false;
      }

      debugPrint('TechnicianAgreementService: Agreement is valid');
      return true;
    } catch (e) {
      debugPrint('TechnicianAgreementService: Error parsing date: $e');
      return false;
    }
  }

  /// Saves the technician's agreement acceptance
  static Future<bool> saveAgreementAcceptance(bool accepted) async {
    debugPrint(
        'TechnicianAgreementService: saveAgreementAcceptance($accepted)');
    try {
      final prefs = await SharedPreferences.getInstance();
      final boolResult = await prefs.setBool(_agreementKey, accepted);
      debugPrint('TechnicianAgreementService: setBool result: $boolResult');

      if (accepted) {
        // Save the agreement date
        final now = DateTime.now().toIso8601String();
        final dateResult = await prefs.setString(_agreementDateKey, now);
        debugPrint(
            'TechnicianAgreementService: Saved agreement date: $now, result: $dateResult');
      }

      // Force a small delay to ensure the data is written
      await Future.delayed(const Duration(milliseconds: 100));

      // Verify the save was successful
      final savedValue = prefs.getBool(_agreementKey);
      debugPrint(
          'TechnicianAgreementService: Verification - saved value is $savedValue');

      return savedValue == accepted;
    } catch (e) {
      debugPrint('TechnicianAgreementService: Error saving agreement: $e');
      return false;
    }
  }

  /// Shows the agreement dialog if needed
  /// Returns true if the technician has accepted, false otherwise
  static Future<bool> checkAgreement(BuildContext context) async {
    debugPrint('TechnicianAgreementService: checkAgreement() called');

    // Check if the technician has already accepted
    final hasAlreadyAccepted = await hasAcceptedAgreement();
    if (hasAlreadyAccepted) {
      debugPrint('TechnicianAgreementService: Agreement already accepted');
      return true;
    }

    try {
      debugPrint('TechnicianAgreementService: Showing agreement dialog');
      // Show the agreement dialog directly using a safer approach
      final accepted = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => const TechnicianLegalAgreementDialog(),
      );
      debugPrint('TechnicianAgreementService: Dialog result: $accepted');

      // If the technician accepted, save the acceptance
      if (accepted == true) {
        debugPrint('TechnicianAgreementService: Agreement accepted, saving');

        // Save with retry mechanism
        bool saveSuccess = false;
        for (int i = 0; i < 3; i++) {
          try {
            final saveResult = await saveAgreementAcceptance(true);
            debugPrint(
                'TechnicianAgreementService: Save attempt ${i + 1} result: $saveResult');

            if (saveResult) {
              saveSuccess = true;
              break;
            }

            // Wait before retrying
            await Future.delayed(const Duration(milliseconds: 300));
          } catch (e) {
            debugPrint(
                'TechnicianAgreementService: Error in save attempt ${i + 1}: $e');
          }
        }

        if (saveSuccess) {
          debugPrint(
              'TechnicianAgreementService: Agreement saved successfully');

          // Verify the agreement was saved
          final verifyAccepted = await hasAcceptedAgreement();
          debugPrint(
              'TechnicianAgreementService: Final verification - hasAcceptedAgreement() = $verifyAccepted');

          return true;
        } else {
          debugPrint(
              'TechnicianAgreementService: Failed to save agreement after multiple attempts');
          return false;
        }
      } else {
        debugPrint('TechnicianAgreementService: Agreement rejected');
        return false;
      }
    } catch (e) {
      debugPrint('TechnicianAgreementService: Error showing dialog: $e');
      return false;
    }
  }
}
