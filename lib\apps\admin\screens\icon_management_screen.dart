import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shivish/shared/providers/icon_provider.dart';
import 'package:shivish/shared/models/icon/icon_model.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/services/icon/icon_service.dart';

class IconManagementScreen extends ConsumerWidget {
  const IconManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final iconsAsyncValue = ref.watch(iconsProvider);
    final iconService = ref.watch(iconServiceProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Icon Management'),
      ),
      body: iconsAsyncValue.when(
        data: (icons) => _buildIconList(context, icons, iconService),
        loading: () => const LoadingIndicator(),
        error: (error, stackTrace) => ErrorMessage(message: error.toString()),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddIconDialog(context, iconService),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildIconList(
    BuildContext context,
    List<IconModel> icons,
    IconService iconService,
  ) {
    return ListView.builder(
      itemCount: icons.length,
      itemBuilder: (context, index) {
        final icon = icons[index];
        return ListTile(
          leading: Image.network(
            icon.url,
            width: 48,
            height: 48,
            errorBuilder: (context, error, stackTrace) =>
                const Icon(Icons.error),
          ),
          title: Text(icon.name),
          subtitle: Text(icon.description),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Switch(
                value: icon.isActive,
                onChanged: (value) =>
                    iconService.toggleIconStatus(icon.id, value),
              ),
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => _showEditDialog(context, icon, iconService),
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => _showDeleteDialog(context, icon, iconService),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _showAddIconDialog(
    BuildContext context,
    IconService iconService,
  ) async {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    File? selectedImage;

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add New Icon'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(labelText: 'Name'),
                ),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(labelText: 'Description'),
                ),
                const SizedBox(height: 16),
                if (selectedImage != null)
                  Image.file(
                    selectedImage!,
                    height: 100,
                    width: 100,
                  ),
                ElevatedButton(
                  onPressed: () async {
                    final picker = ImagePicker();
                    final image = await picker.pickImage(
                      source: ImageSource.gallery,
                    );
                    if (image != null) {
                      setState(() {
                        selectedImage = File(image.path);
                      });
                    }
                  },
                  child: const Text('Select Image'),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: selectedImage == null
                  ? null
                  : () async {
                      final url = await iconService.uploadIconImage(
                        selectedImage!.path,
                      );
                      await iconService.addIcon(
                        name: nameController.text,
                        description: descriptionController.text,
                        url: url,
                      );
                      if (context.mounted) {
                        Navigator.pop(context);
                      }
                    },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showEditDialog(
    BuildContext context,
    IconModel icon,
    IconService iconService,
  ) async {
    final nameController = TextEditingController(text: icon.name);
    final descriptionController = TextEditingController(text: icon.description);

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Icon'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(labelText: 'Name'),
            ),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(labelText: 'Description'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              await iconService.updateIcon(
                id: icon.id,
                name: nameController.text,
                description: descriptionController.text,
              );
              if (context.mounted) {
                Navigator.pop(context);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  Future<void> _showDeleteDialog(
    BuildContext context,
    IconModel icon,
    IconService iconService,
  ) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Icon'),
        content: Text('Are you sure you want to delete ${icon.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              await iconService.deleteIcon(icon.id, icon.url);
              if (context.mounted) {
                Navigator.pop(context);
              }
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
