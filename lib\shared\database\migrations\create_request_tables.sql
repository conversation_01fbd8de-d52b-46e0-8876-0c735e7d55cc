-- Migration: Create request tables for role-based user registration
-- This migration creates the missing request tables for seller, priest, and technician registration requests

-- Se<PERSON> requests table
CREATE TABLE IF NOT EXISTS seller_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    uid VARCHAR(255) NOT NULL, -- Supabase Auth UID
    email VARCHAR(255) NOT NULL,
    display_name VA<PERSON>HAR(255) NOT NULL,
    phone_number VARCHAR(20),
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    documents JSONB DEFAULT '[]', -- Store documents as JSON array
    business_name VARCHAR(255),
    business_type VARCHAR(100),
    business_address JSONB,
    business_phone VARCHAR(20),
    business_email VARCHAR(255),
    gst_number VARCHAR(50),
    pan_number VARCHAR(20),
    rejection_reason TEXT,
    approved_by VARC<PERSON>R(255),
    approved_at TIMESTAMP WITH TIME ZONE,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}'
);

-- Priest requests table
CREATE TABLE IF NOT EXISTS priest_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    uid VARCHAR(255) NOT NULL, -- Supabase Auth UID
    email VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    documents JSONB DEFAULT '[]', -- Store documents as JSON array
    specializations TEXT[] DEFAULT '{}',
    languages TEXT[] DEFAULT '{}',
    services TEXT[] DEFAULT '{}',
    experience_years INTEGER DEFAULT 0,
    qualification_details TEXT,
    temple_affiliation VARCHAR(255),
    location JSONB,
    availability JSONB DEFAULT '{}',
    pricing JSONB DEFAULT '{}',
    rejection_reason TEXT,
    approved_by VARCHAR(255),
    approved_at TIMESTAMP WITH TIME ZONE,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}'
);

-- Technician requests table
CREATE TABLE IF NOT EXISTS technician_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    uid VARCHAR(255) NOT NULL, -- Supabase Auth UID
    email VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    documents JSONB DEFAULT '[]', -- Store documents as JSON array
    skills TEXT[] DEFAULT '{}',
    certifications TEXT[] DEFAULT '{}',
    experience_years INTEGER DEFAULT 0,
    service_areas TEXT[] DEFAULT '{}',
    qualification_details TEXT,
    tools_equipment TEXT[] DEFAULT '{}',
    location JSONB,
    availability JSONB DEFAULT '{}',
    pricing JSONB DEFAULT '{}',
    rejection_reason TEXT,
    approved_by VARCHAR(255),
    approved_at TIMESTAMP WITH TIME ZONE,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}'
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_seller_requests_uid ON seller_requests(uid);
CREATE INDEX IF NOT EXISTS idx_seller_requests_status ON seller_requests(status);
CREATE INDEX IF NOT EXISTS idx_seller_requests_email ON seller_requests(email);

CREATE INDEX IF NOT EXISTS idx_priest_requests_uid ON priest_requests(uid);
CREATE INDEX IF NOT EXISTS idx_priest_requests_status ON priest_requests(status);
CREATE INDEX IF NOT EXISTS idx_priest_requests_email ON priest_requests(email);

CREATE INDEX IF NOT EXISTS idx_technician_requests_uid ON technician_requests(uid);
CREATE INDEX IF NOT EXISTS idx_technician_requests_status ON technician_requests(status);
CREATE INDEX IF NOT EXISTS idx_technician_requests_email ON technician_requests(email);

-- Add triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_seller_requests_updated_at BEFORE UPDATE ON seller_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_priest_requests_updated_at BEFORE UPDATE ON priest_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_technician_requests_updated_at BEFORE UPDATE ON technician_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
