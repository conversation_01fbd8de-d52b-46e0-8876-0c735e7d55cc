import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/services/update/update_service.dart';
import 'package:shivish/shared/services/chat/chat_service.dart';

class AboutScreen extends ConsumerStatefulWidget {
  const AboutScreen({super.key});

  @override
  ConsumerState<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends ConsumerState<AboutScreen> {
  PackageInfo? _packageInfo;
  bool _isCheckingUpdate = false;

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  Future<void> _loadPackageInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _packageInfo = packageInfo;
    });
  }

  Future<void> _launchUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      if (mounted) {
        _showSnackBar(context, 'Could not launch URL');
      }
    }
  }

  Future<void> _checkForUpdates() async {
    if (_isCheckingUpdate) return;

    setState(() => _isCheckingUpdate = true);

    try {
      final updateService = ref.read(updateServiceProvider);
      final hasUpdate = await updateService.checkForUpdates(
        currentVersion: _packageInfo?.version ?? '',
      );

      if (mounted) {
        if (hasUpdate) {
          _showSnackBar(context, 'A new version is available!');
          // Show update dialog
          _showUpdateDialog();
        } else {
          _showSnackBar(context, 'You are using the latest version');
        }
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(context, 'Failed to check for updates: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() => _isCheckingUpdate = false);
      }
    }
  }

  void _showUpdateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Available'),
        content: const Text(
            'A new version of Shivish  is available. Would you like to update now?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _launchUrl('market://details?id=com.shivish');
            },
            child: const Text('Update Now'),
          ),
        ],
      ),
    );
  }

  Future<void> _startLiveChat() async {
    try {
      final chatService = ref.read(chatServiceProvider);
      await chatService.startChat();
    } catch (e) {
      if (mounted) {
        _showSnackBar(context, 'Failed to start chat: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'About',
      ),
      body: ListView(
        children: [
          _buildAppInfo(),
          const Divider(),
          _buildSupportSection(),
          const Divider(),
          _buildLegalSection(),
        ],
      ),
    );
  }

  Widget _buildAppInfo() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          const Icon(
            Icons.shopping_cart,
            size: 64,
            color: Colors.blue,
          ),
          const SizedBox(height: 16),
          Text(
            'Shivish ',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          if (_packageInfo != null) ...[
            const SizedBox(height: 8),
            Text(
              'Version ${_packageInfo!.version}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _isCheckingUpdate ? null : _checkForUpdates,
              icon: _isCheckingUpdate
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.update),
              label:
                  Text(_isCheckingUpdate ? 'Checking...' : 'Check for Updates'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSupportSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Support',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        ListTile(
          leading: const Icon(Icons.email),
          title: const Text('Contact Support'),
          subtitle: const Text('Get help from our support team'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _launchUrl('mailto:<EMAIL>'),
        ),
        ListTile(
          leading: const Icon(Icons.chat),
          title: const Text('Live Chat'),
          subtitle: const Text('Chat with our support team'),
          trailing: const Icon(Icons.chevron_right),
          onTap: _startLiveChat,
        ),
        ListTile(
          leading: const Icon(Icons.phone),
          title: const Text('Call Support'),
          subtitle: const Text('Call our support team'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _launchUrl('tel:+1234567890'),
        ),
      ],
    );
  }

  Widget _buildLegalSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Legal',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        ListTile(
          leading: const Icon(Icons.description),
          title: const Text('Terms of Service'),
          subtitle: const Text('Read our terms of service'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _launchUrl('https://shivish.com/terms'),
        ),
        ListTile(
          leading: const Icon(Icons.privacy_tip),
          title: const Text('Privacy Policy'),
          subtitle: const Text('Read our privacy policy'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _launchUrl('https://shivish.com/privacy'),
        ),
        ListTile(
          leading: const Icon(Icons.copyright),
          title: const Text('Copyright'),
          subtitle: Text(
            '© ${DateTime.now().year} Shivish. All rights reserved.',
          ),
        ),
      ],
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
