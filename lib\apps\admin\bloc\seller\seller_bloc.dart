import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/shared/models/seller.dart';
import 'package:shivish/shared/services/seller_service.dart';
import 'package:shivish/shared/utils/logger.dart';

part 'seller_event.dart';
part 'seller_state.dart';
part 'seller_bloc.freezed.dart';

@injectable
class SellerBloc extends Bloc<SellerEvent, SellerState> {
  final SellerService _sellerService;
  final _logger = getLogger('AdminSellerBloc');
  StreamSubscription<List<Seller>>? _sellerSubscription;
  List<Seller> _cachedSellers = [];
  bool _isRealtimeEnabled = false;
  int _currentOffset = 0;
  static const int _pageSize = 20;

  SellerBloc(this._sellerService) : super(const SellerState()) {
    on<LoadSellers>(_onLoadSellers);
    on<LoadMoreSellers>(_onLoadMoreSellers);
    on<UpdateSellerStatus>(_onUpdateSellerStatus);
    on<UpdateSellerPerformance>(_onUpdateSellerPerformance);
    on<DeleteSeller>(_onDeleteSeller);
    on<CreateSeller>(_onCreateSeller);
    on<StartRealtimeUpdates>(_onStartRealtimeUpdates);
    on<StopRealtimeUpdates>(_onStopRealtimeUpdates);
  }

  Future<void> _onLoadSellers(
    LoadSellers event,
    Emitter<SellerState> emit,
  ) async {
    try {
      _logger.info('Loading sellers');
      emit(state.copyWith(isLoading: true, error: null));
      _currentOffset = 0; // Reset offset for fresh load
      final sellers = await _sellerService.getSellers(
        limit: _pageSize,
        offset: _currentOffset,
      );
      _currentOffset = sellers.length;
      _logger.info('Successfully loaded ${sellers.length} sellers');
      emit(state.copyWith(
        isLoading: false,
        sellers: sellers,
        hasMore: sellers.length >= _pageSize,
      ));
    } catch (e) {
      _logger.severe('Failed to load sellers: $e');
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onLoadMoreSellers(
    LoadMoreSellers event,
    Emitter<SellerState> emit,
  ) async {
    try {
      if (!state.hasMore || state.isLoading) return;
      _logger.info('Loading more sellers from offset: $_currentOffset');
      emit(state.copyWith(isLoading: true, error: null));

      final moreSellers = await _sellerService.getSellers(
        limit: _pageSize,
        offset: _currentOffset,
      );

      if (moreSellers.isEmpty) {
        _logger.info('No more sellers to load');
        emit(state.copyWith(
          isLoading: false,
          hasMore: false,
        ));
        return;
      }

      _currentOffset += moreSellers.length;
      _logger.info('Successfully loaded ${moreSellers.length} more sellers. Total: ${state.sellers.length + moreSellers.length}');
      emit(state.copyWith(
        isLoading: false,
        sellers: [...state.sellers, ...moreSellers],
        hasMore: moreSellers.length >= _pageSize,
      ));
    } catch (e) {
      _logger.severe('Failed to load more sellers: $e');
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onUpdateSellerStatus(
    UpdateSellerStatus event,
    Emitter<SellerState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! Loaded) return;

      _logger.info('Updating seller status: ${event.seller.id} -> active: ${event.isActive}, suspended: ${event.isSuspended}');
      final updatedSeller = event.seller.copyWith(
        isActive: event.isActive,
        isSuspended: event.isSuspended,
        updatedAt: DateTime.now(),
      );

      final updatedSellers = currentState.sellers.map((seller) {
        return seller.id == updatedSeller.id ? updatedSeller : seller;
      }).toList();

      emit(currentState.copyWith(sellers: updatedSellers));

      await _sellerService.updateSeller(updatedSeller);
      _logger.info('Successfully updated seller status: ${event.seller.id}');
    } catch (e) {
      _logger.severe('Failed to update seller status: $e');
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onUpdateSellerPerformance(
    UpdateSellerPerformance event,
    Emitter<SellerState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! Loaded) return;

      _logger.info('Updating seller performance: ${event.seller.id} -> rating: ${event.rating}');
      final updatedSeller = event.seller.copyWith(
        rating: event.rating,
        totalReviews: event.totalReviews,
        totalOrders: event.totalOrders,
        totalProducts: event.totalProducts,
        totalRevenue: event.totalRevenue,
        updatedAt: DateTime.now(),
      );

      final updatedSellers = currentState.sellers.map((seller) {
        return seller.id == updatedSeller.id ? updatedSeller : seller;
      }).toList();

      emit(currentState.copyWith(sellers: updatedSellers));

      await _sellerService.updateSeller(updatedSeller);
      _logger.info('Successfully updated seller performance: ${event.seller.id}');
    } catch (e) {
      _logger.severe('Failed to update seller performance: $e');
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onDeleteSeller(
    DeleteSeller event,
    Emitter<SellerState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! Loaded) return;

      _logger.info('Deleting seller: ${event.seller.id}');
      final updatedSellers = currentState.sellers
          .where((seller) => seller.id != event.seller.id)
          .toList();

      emit(currentState.copyWith(sellers: updatedSellers));

      await _sellerService.deleteSeller(event.seller.id);
      _logger.info('Successfully deleted seller: ${event.seller.id}');
    } catch (e) {
      _logger.severe('Failed to delete seller: $e');
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onCreateSeller(
    CreateSeller event,
    Emitter<SellerState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! Loaded) return;

      _logger.info('Creating new seller');
      final newSeller = await _sellerService.createSeller(event.seller);
      final updatedSellers = [newSeller, ...currentState.sellers];

      _logger.info('Successfully created seller: ${newSeller.id}');
      emit(currentState.copyWith(sellers: updatedSellers));
    } catch (e) {
      _logger.severe('Failed to create seller: $e');
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onStartRealtimeUpdates(
    StartRealtimeUpdates event,
    Emitter<SellerState> emit,
  ) async {
    if (_isRealtimeEnabled) return;
    _isRealtimeEnabled = true;

    try {
      _logger.info('Starting realtime updates for sellers');
      final currentState = state;
      if (currentState is Loaded) {
        _cachedSellers = List.from(currentState.sellers);
      }

      _sellerSubscription = _sellerService.watchSellers().listen(
        (sellers) {
          _logger.info('Received realtime update: ${sellers.length} sellers');
          add(const LoadSellers());
        },
        onError: (error) {
          _logger.warning('Realtime updates error: $error');
          if (_cachedSellers.isNotEmpty) {
            emit(state.copyWith(
              sellers: _cachedSellers,
              error: 'Real-time updates error: $error',
            ));
          } else {
            emit(state.copyWith(error: 'Real-time updates error: $error'));
          }
        },
      );
    } catch (e) {
      _logger.severe('Failed to start realtime updates: $e');
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onStopRealtimeUpdates(
    StopRealtimeUpdates event,
    Emitter<SellerState> emit,
  ) async {
    _logger.info('Stopping realtime updates for sellers');
    _isRealtimeEnabled = false;
    await _sellerSubscription?.cancel();
    _sellerSubscription = null;
  }

  @override
  Future<void> close() async {
    await _sellerSubscription?.cancel();
    return super.close();
  }
}
