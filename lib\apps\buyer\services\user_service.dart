import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../shared/models/user/user_model.dart';
import '../../../shared/utils/logger.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';

final userServiceProvider = Provider<UserService>(
  (ref) => UserService(),
);

class UserService {
  final DatabaseService _databaseService;
  final _logger = getLogger('UserService');

  UserService({DatabaseService? databaseService})
      : _databaseService = databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment());

  // No need for data sanitization with hybrid storage - data is already in correct format

  Future<UserModel?> getCurrentUser() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return null;

      final userData = await _databaseService.find('users', user.id);
      if (userData == null) return null;

      _logger.info('Getting current user: ${user.id}');

      return UserModel.fromJson(userData);
    } catch (e, stackTrace) {
      _logger.severe('Error getting current user', e, stackTrace);
      rethrow;
    }
  }

  Future<UserModel?> getUserById(String userId) async {
    try {
      final userData = await _databaseService.find('users', userId);
      if (userData == null) return null;

      return UserModel.fromJson(userData);
    } catch (e, stackTrace) {
      _logger.severe('Error getting user by ID: $userId', e, stackTrace);
      rethrow;
    }
  }

  Future<UserModel?> getUserByEmail(String email) async {
    try {
      final allUsers = await _databaseService.getAll('users');
      final matchingUsers = allUsers.where((userData) => userData['email'] == email).toList();

      if (matchingUsers.isEmpty) return null;

      return UserModel.fromJson(matchingUsers.first);
    } catch (e, stackTrace) {
      _logger.severe('Error getting user by email: $email', e, stackTrace);
      rethrow;
    }
  }

  Future<void> updateUser(UserModel user) async {
    try {
      await _databaseService.update('users', user.id, user.toJson());
      _logger.info('User updated: ${user.id}');
    } catch (e, stackTrace) {
      _logger.severe('Error updating user: ${user.id}', e, stackTrace);
      rethrow;
    }
  }

  Future<void> deleteUser() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return;

      await _databaseService.delete('users', user.id);
      // Note: Supabase user deletion should be handled through admin API or RLS policies
      // For now, we'll just delete the user document from hybrid storage
      _logger.info('User document deleted: ${user.id}');
    } catch (e, stackTrace) {
      _logger.severe('Error deleting user', e, stackTrace);
      rethrow;
    }
  }
}
