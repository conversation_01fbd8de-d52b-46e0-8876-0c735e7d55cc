-- Temple System Database Schema

-- Temples table
CREATE TABLE IF NOT EXISTS temples (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL, -- ancient, modern, heritage, pilgrimage, local
    status TEXT NOT NULL DEFAULT 'pending', -- pending, approved, rejected, suspended, active, inactive
    
    -- Location
    address TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    pincode TEXT NOT NULL,
    latitude REAL DEFAULT 0.0,
    longitude REAL DEFAULT 0.0,
    
    -- Contact
    phone TEXT NOT NULL,
    email TEXT,
    website TEXT,
    
    -- Management
    managed_by TEXT, -- User ID of temple manager
    manager_name TEXT,
    registration_number TEXT,
    
    -- Details
    deities TEXT, -- JSON array of deity names
    facilities TEXT, -- JSON array of facilities
    history TEXT,
    significance TEXT,
    architecture TEXT,
    festivals TEXT, -- JSON array of festivals
    
    -- Media
    cover_image TEXT,
    images TEXT, -- JSON array of image URLs
    
    -- Verification
    is_verified BOOLEAN DEFAULT FALSE,
    verification_notes TEXT,
    rejection_reason TEXT,
    
    -- Ratings
    rating REAL DEFAULT 0.0,
    total_reviews INTEGER DEFAULT 0,
    
    -- Metadata
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- Temple darshan services
CREATE TABLE IF NOT EXISTS temple_darshans (
    id TEXT PRIMARY KEY,
    temple_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL, -- general, special, vip, abhishek, aarti
    price REAL NOT NULL DEFAULT 0.0,
    duration INTEGER NOT NULL DEFAULT 30, -- in minutes
    max_devotees INTEGER DEFAULT 1,
    advance_booking_days INTEGER DEFAULT 7,
    cancellation_hours INTEGER DEFAULT 24,
    requirements TEXT, -- JSON array of requirements
    rules TEXT, -- JSON array of rules
    is_active BOOLEAN DEFAULT TRUE,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (temple_id) REFERENCES temples(id)
);

-- Temple seva services
CREATE TABLE IF NOT EXISTS temple_sevas (
    id TEXT PRIMARY KEY,
    temple_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL, -- puja, abhishek, aarti, prasadam, donation, annadanam, special
    price REAL NOT NULL DEFAULT 0.0,
    duration INTEGER NOT NULL DEFAULT 30, -- in minutes
    max_devotees INTEGER DEFAULT 1,
    advance_booking_days INTEGER DEFAULT 7,
    cancellation_hours INTEGER DEFAULT 24,
    requirements TEXT, -- JSON array of requirements
    rules TEXT, -- JSON array of rules
    is_active BOOLEAN DEFAULT TRUE,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (temple_id) REFERENCES temples(id)
);

-- Temple timings
CREATE TABLE IF NOT EXISTS temple_timings (
    id TEXT PRIMARY KEY,
    temple_id TEXT NOT NULL,
    day_of_week INTEGER NOT NULL, -- 0=Sunday, 1=Monday, etc.
    opening_time TEXT NOT NULL, -- HH:MM format
    closing_time TEXT NOT NULL, -- HH:MM format
    is_closed BOOLEAN DEFAULT FALSE,
    special_timings TEXT, -- JSON for special occasions
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (temple_id) REFERENCES temples(id)
);

-- Temple bookings
CREATE TABLE IF NOT EXISTS temple_bookings (
    id TEXT PRIMARY KEY,
    booking_number TEXT UNIQUE NOT NULL,
    temple_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    
    -- Devotee information
    devotee_name TEXT NOT NULL,
    devotee_phone TEXT NOT NULL,
    devotee_email TEXT,
    devotee_city TEXT,
    devotee_state TEXT,
    devotee_gotram TEXT,
    devotee_nakshatram TEXT,
    devotee_rashi TEXT,
    
    -- Booking details
    visit_date TEXT NOT NULL, -- ISO date
    total_amount REAL NOT NULL DEFAULT 0.0,
    gst_amount REAL NOT NULL DEFAULT 0.0,
    status TEXT NOT NULL DEFAULT 'pending', -- pending, confirmed, completed, cancelled
    payment_status TEXT NOT NULL DEFAULT 'pending', -- pending, paid, refunded
    
    -- Additional info
    special_requirements TEXT,
    notes TEXT,
    cancellation_reason TEXT,
    
    -- Timestamps
    paid_at TEXT,
    cancelled_at TEXT,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    
    FOREIGN KEY (temple_id) REFERENCES temples(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Temple booking items (services booked)
CREATE TABLE IF NOT EXISTS temple_booking_items (
    id TEXT PRIMARY KEY,
    booking_id TEXT NOT NULL,
    service_id TEXT NOT NULL, -- darshan or seva ID
    service_type TEXT NOT NULL, -- 'darshan' or 'seva'
    service_name TEXT NOT NULL,
    time_slot TEXT, -- HH:MM format
    quantity INTEGER NOT NULL DEFAULT 1,
    price REAL NOT NULL,
    total_price REAL NOT NULL,
    special_instructions TEXT,
    created_at TEXT NOT NULL,
    FOREIGN KEY (booking_id) REFERENCES temple_bookings(id)
);

-- Temple reviews
CREATE TABLE IF NOT EXISTS temple_reviews (
    id TEXT PRIMARY KEY,
    temple_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    booking_id TEXT,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (temple_id) REFERENCES temples(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (booking_id) REFERENCES temple_bookings(id)
);

-- Temple notifications
CREATE TABLE IF NOT EXISTS temple_notifications (
    id TEXT PRIMARY KEY,
    temple_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    type TEXT NOT NULL, -- booking_created, booking_confirmed, booking_cancelled, etc.
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data TEXT, -- JSON data
    is_read BOOLEAN DEFAULT FALSE,
    created_at TEXT NOT NULL,
    FOREIGN KEY (temple_id) REFERENCES temples(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_temples_status ON temples(status);
CREATE INDEX IF NOT EXISTS idx_temples_city ON temples(city);
CREATE INDEX IF NOT EXISTS idx_temples_state ON temples(state);
CREATE INDEX IF NOT EXISTS idx_temples_type ON temples(type);
CREATE INDEX IF NOT EXISTS idx_temples_managed_by ON temples(managed_by);
CREATE INDEX IF NOT EXISTS idx_temples_location ON temples(latitude, longitude);

CREATE INDEX IF NOT EXISTS idx_temple_bookings_temple_id ON temple_bookings(temple_id);
CREATE INDEX IF NOT EXISTS idx_temple_bookings_user_id ON temple_bookings(user_id);
CREATE INDEX IF NOT EXISTS idx_temple_bookings_visit_date ON temple_bookings(visit_date);
CREATE INDEX IF NOT EXISTS idx_temple_bookings_status ON temple_bookings(status);
CREATE INDEX IF NOT EXISTS idx_temple_bookings_booking_number ON temple_bookings(booking_number);

CREATE INDEX IF NOT EXISTS idx_temple_booking_items_booking_id ON temple_booking_items(booking_id);
CREATE INDEX IF NOT EXISTS idx_temple_booking_items_service_id ON temple_booking_items(service_id);

CREATE INDEX IF NOT EXISTS idx_temple_reviews_temple_id ON temple_reviews(temple_id);
CREATE INDEX IF NOT EXISTS idx_temple_reviews_user_id ON temple_reviews(user_id);

CREATE INDEX IF NOT EXISTS idx_temple_notifications_temple_id ON temple_notifications(temple_id);
CREATE INDEX IF NOT EXISTS idx_temple_notifications_user_id ON temple_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_temple_notifications_is_read ON temple_notifications(is_read);
