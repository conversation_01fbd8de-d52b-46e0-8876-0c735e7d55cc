import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/seller/presentation/providers/notifications_provider.dart';
import 'package:shivish/apps/seller/presentation/cubits/notifications_cubit.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/models/notification/notification_model.dart';
import 'package:shivish/shared/models/notification/notification_type.dart';
import 'package:shivish/shared/models/notification/notification_status.dart';

class NotificationsScreen extends ConsumerWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the notifications cubit from the provider
    final notificationsCubit = ref.watch(notificationsCubitProvider);

    return Scaffold(
      appBar: const AppToolbar(title: 'Notifications'),
      body: StreamBuilder<NotificationsState>(
        stream: notificationsCubit.stream,
        initialData: notificationsCubit.state,
        builder: (context, snapshot) {
          final state = snapshot.data ?? const NotificationsState.loading();

          return state.when(
            loading: () => const LoadingIndicator(),
            error: (message) => Center(
              child: Text(
                'Error: $message',
                style: const TextStyle(color: Colors.red),
              ),
            ),
            loaded: (notifications) => _buildContent(context, notifications),
          );
        },
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    List<NotificationModel> notifications,
  ) {
    if (notifications.isEmpty) {
      return const Center(child: Text('No notifications'));
    }

    return ListView.builder(
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        final isUnread = notification.status == NotificationStatus.unread;

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          color: isUnread ? Colors.blue.shade50 : null,
          child: ListTile(
            leading: Icon(
              _getNotificationIcon(notification.type),
              color: _getNotificationColor(notification.type),
            ),
            title: Text(
              notification.title,
              style: TextStyle(
                fontWeight: isUnread ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(notification.body),
                const SizedBox(height: 4),
                Text(
                  _formatTimestamp(notification.createdAt),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            trailing: isUnread
                ? Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  )
                : null,
            onTap: () => _handleNotificationTap(context, notification),
          ),
        );
      },
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.order:
        return Icons.shopping_cart;
      case NotificationType.general:
        return Icons.notifications;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.booking:
        return Icons.calendar_today;
      case NotificationType.event:
        return Icons.event;
      case NotificationType.chat:
        return Icons.chat;
      case NotificationType.system:
        return Icons.info;
      case NotificationType.verification:
        return Icons.verified_user;
      case NotificationType.deliveryRequest:
        return Icons.local_shipping;
      case NotificationType.statusUpdate:
        return Icons.update;
      case NotificationType.earnings:
        return Icons.attach_money;
      default:
        // Handle all other notification types (buyer-specific, etc.)
        return Icons.notifications;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.order:
        return Colors.blue;
      case NotificationType.general:
        return Colors.grey;
      case NotificationType.payment:
        return Colors.green;
      case NotificationType.booking:
        return Colors.orange;
      case NotificationType.event:
        return Colors.purple;
      case NotificationType.chat:
        return Colors.teal;
      case NotificationType.system:
        return Colors.red;
      case NotificationType.verification:
        return Colors.indigo;
      case NotificationType.deliveryRequest:
        return Colors.brown;
      case NotificationType.statusUpdate:
        return Colors.cyan;
      case NotificationType.earnings:
        return Colors.amber;
      default:
        // Handle all other notification types (buyer-specific, etc.)
        return Colors.grey;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _handleNotificationTap(
    BuildContext context,
    NotificationModel notification,
  ) {
    switch (notification.type) {
      case NotificationType.order:
        if (notification.data['orderId'] != null) {
          context.go('/seller/orders/${notification.data['orderId']}');
        } else {
          context.go('/seller/orders');
        }
        break;
      case NotificationType.payment:
        if (notification.data['paymentId'] != null) {
          context.go('/seller/payments/${notification.data['paymentId']}');
        } else {
          context.go('/seller/payments');
        }
        break;
      case NotificationType.booking:
        if (notification.data['bookingId'] != null) {
          context.go('/seller/bookings/${notification.data['bookingId']}');
        }
        break;
      case NotificationType.event:
        if (notification.data['eventId'] != null) {
          context.go('/seller/events/${notification.data['eventId']}');
        }
        break;
      case NotificationType.verification:
        context.go('/seller/profile/verification');
        break;
      case NotificationType.system:
        if (notification.data['action'] != null) {
          _handleSystemAction(context, notification.data['action']);
        }
        break;
      default:
        // Handle other notification types
        break;
    }
  }

  void _handleSystemAction(BuildContext context, String action) {
    switch (action) {
      case 'update_app':
        // Handle app update notification
        break;
      case 'maintenance':
        // Handle maintenance notification
        break;
      case 'security':
        // Handle security notification
        break;
      default:
        break;
    }
  }
}
