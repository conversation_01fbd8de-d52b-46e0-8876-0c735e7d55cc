import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final paymentSettingsProvider =
    AsyncNotifierProvider<PaymentSettingsNotifier, Map<String, dynamic>>(() {
  return PaymentSettingsNotifier();
});

class PaymentSettingsNotifier extends AsyncNotifier<Map<String, dynamic>> {
  late final DatabaseService _databaseService;

  @override
  Future<Map<String, dynamic>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadPaymentSettings();
  }

  Future<Map<String, dynamic>> _loadPaymentSettings() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('users', userId);
      return userData?['payment_settings'] as Map<String, dynamic>? ??
          {
            'bank_name': '',
            'account_number': '',
            'routing_number': '',
            'account_holder_name': '',
            'swift_code': '',
            'payment_method': 'bank_transfer',
          };
    } catch (e) {
      debugPrint('Failed to load payment settings: $e');
      throw Exception('Failed to load payment settings: $e');
    }
  }

  Future<void> updatePaymentSettings(Map<String, dynamic> settings) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Validate required fields
      if (settings['bank_name']?.toString().isEmpty ?? true) {
        throw Exception('Bank name is required');
      }
      if (settings['account_number']?.toString().isEmpty ?? true) {
        throw Exception('Account number is required');
      }

      await _databaseService.update('users', userId, {
        'payment_settings': settings,
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData(settings);
    } catch (e) {
      debugPrint('Failed to update payment settings: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}
