import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/providers/ai_assistant_provider.dart';
import '../../../shared/models/voice/ai_assistant_character.dart';
import '../../../shared/ui_components/navigation/back_button_handler.dart';
import '../../../shared/services/voice/voice_command_service.dart';
import '../admin_routes.dart';

class AdminAIAssistantSettingsScreen extends ConsumerWidget {
  const AdminAIAssistantSettingsScreen({super.key});

  /// Test the voice for the selected gender
  void _testVoice(AIAssistantGender gender) {
    final voiceService = VoiceCommandService();

    // Speak a test phrase based on the gender
    if (gender == AIAssistantGender.female) {
      voiceService.speak("Hello, I'm <PERSON><PERSON>, your female voice assistant.");
    } else {
      voiceService.speak("Hello, I'm Shi<PERSON>, your male voice assistant.");
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final genderAsync = ref.watch(aiAssistantGenderStateProvider);
    final floatEnabledAsync = ref.watch(aiAssistantFloatEnabledStateProvider);
    final characterAsync = ref.watch(aiAssistantCharacterProvider);
    final theme = Theme.of(context);

    return BackButtonHandler(
      fallbackRoute: AdminRoutes.settings,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('AI Assistant Settings'),
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              } else {
                context.go(AdminRoutes.settings);
              }
            },
          ),
        ),
        body: ListView(
          children: [
            // Enable/disable floating assistant
            SwitchListTile(
              title: const Text('Show Floating Assistant'),
              subtitle: const Text(
                'Display an animated assistant when using voice commands',
              ),
              secondary: Icon(
                floatEnabledAsync.value == true
                    ? Icons.visibility
                    : Icons.visibility_off,
                color: floatEnabledAsync.value == true
                    ? theme.colorScheme.primary
                    : Colors.grey,
              ),
              value: floatEnabledAsync.value ?? true,
              onChanged: (value) {
                ref
                    .read(aiAssistantFloatEnabledStateProvider.notifier)
                    .toggle();
              },
            ),
            const Divider(),

            // Assistant character selection
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Assistant Character',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Choose your assistant character. This will change both the appearance and voice of your assistant.',
                    style: TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 16),

                  // Character selection cards
                  Row(
                    children: [
                      // Male character
                      Expanded(
                        child: _buildCharacterCard(
                          context,
                          ref,
                          AIAssistantGender.male,
                          'Shiv',
                          'assets/images/assistant/male/neutral.png',
                          isSelected:
                              genderAsync.value == AIAssistantGender.male,
                          onTap: () {
                            ref
                                .read(aiAssistantGenderStateProvider.notifier)
                                .setGender(AIAssistantGender.male);
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Female character
                      Expanded(
                        child: _buildCharacterCard(
                          context,
                          ref,
                          AIAssistantGender.female,
                          'Isha',
                          'assets/images/assistant/female/neutral.png',
                          isSelected:
                              genderAsync.value == AIAssistantGender.female,
                          onTap: () {
                            ref
                                .read(aiAssistantGenderStateProvider.notifier)
                                .setGender(AIAssistantGender.female);
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const Divider(),

            // Preview section
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Preview',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Preview of selected character
                  characterAsync.when(
                    data: (character) =>
                        _buildPreviewSection(context, character),
                    loading: () =>
                        const Center(child: CircularProgressIndicator()),
                    error: (_, __) => const Center(
                      child: Text('Failed to load character preview'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCharacterCard(
    BuildContext context,
    WidgetRef ref,
    AIAssistantGender gender,
    String name,
    String imagePath, {
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: isSelected ? 4 : 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: isSelected ? theme.colorScheme.primary : Colors.transparent,
            width: 2,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Character image
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                ),
                padding: const EdgeInsets.all(8),
                child: ClipOval(
                  child: Image.asset(
                    imagePath,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        gender == AIAssistantGender.male
                            ? Icons.face
                            : Icons.face_3,
                        size: 60,
                        color: theme.colorScheme.primary,
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: 12),
              // Character name
              Text(
                name,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              // Selected indicator
              if (isSelected)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: theme.colorScheme.primary,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Selected',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              const SizedBox(height: 8),
              // Test voice button
              ElevatedButton.icon(
                onPressed: () => _testVoice(gender),
                icon: const Icon(Icons.volume_up, size: 16),
                label: const Text('Test Voice'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primaryContainer,
                  foregroundColor: theme.colorScheme.onPrimaryContainer,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  textStyle: const TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPreviewSection(
      BuildContext context, AIAssistantCharacter character) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Preview title
        Text(
          'Expressions',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Expression grid
        GridView.count(
          crossAxisCount: 3,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          children: [
            _buildExpressionPreview(
              context,
              character,
              AIAssistantExpression.neutral,
              'Neutral',
            ),
            _buildExpressionPreview(
              context,
              character,
              AIAssistantExpression.listening,
              'Listening',
            ),
            _buildExpressionPreview(
              context,
              character,
              AIAssistantExpression.thinking,
              'Thinking',
            ),
            _buildExpressionPreview(
              context,
              character,
              AIAssistantExpression.speaking,
              'Speaking',
            ),
            _buildExpressionPreview(
              context,
              character,
              AIAssistantExpression.happy,
              'Happy',
            ),
            _buildExpressionPreview(
              context,
              character,
              AIAssistantExpression.confused,
              'Confused',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildExpressionPreview(
    BuildContext context,
    AIAssistantCharacter character,
    AIAssistantExpression expression,
    String label,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Expression image
        Container(
          width: 70,
          height: 70,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
          ),
          padding: const EdgeInsets.all(4),
          child: ClipOval(
            child: Image.asset(
              character.getExpressionAsset(expression),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.face,
                  size: 40,
                  color: theme.colorScheme.primary,
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 8),
        // Expression label
        Text(
          label,
          style: theme.textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
