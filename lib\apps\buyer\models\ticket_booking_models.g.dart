// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_booking_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BookingResponse _$BookingResponseFromJson(Map<String, dynamic> json) =>
    BookingResponse(
      bookingId: json['bookingId'] as String,
      status: json['status'] as String,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      currency: json['currency'] as String,
      bookingDate: DateTime.parse(json['bookingDate'] as String),
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$BookingResponseToJson(BookingResponse instance) =>
    <String, dynamic>{
      'bookingId': instance.bookingId,
      'status': instance.status,
      'totalAmount': instance.totalAmount,
      'currency': instance.currency,
      'bookingDate': instance.bookingDate.toIso8601String(),
      'additionalInfo': instance.additionalInfo,
    };

BookingStatus _$BookingStatusFromJson(Map<String, dynamic> json) =>
    BookingStatus(
      bookingId: json['bookingId'] as String,
      status: json['status'] as String,
      pnr: json['pnr'] as String?,
      confirmedAt: json['confirmedAt'] == null
          ? null
          : DateTime.parse(json['confirmedAt'] as String),
      details: json['details'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$BookingStatusToJson(BookingStatus instance) =>
    <String, dynamic>{
      'bookingId': instance.bookingId,
      'status': instance.status,
      'pnr': instance.pnr,
      'confirmedAt': instance.confirmedAt?.toIso8601String(),
      'details': instance.details,
    };

CancellationResponse _$CancellationResponseFromJson(
  Map<String, dynamic> json,
) => CancellationResponse(
  bookingId: json['bookingId'] as String,
  cancelled: json['cancelled'] as bool,
  refundAmount: (json['refundAmount'] as num).toDouble(),
  refundStatus: json['refundStatus'] as String,
  refundDate: json['refundDate'] == null
      ? null
      : DateTime.parse(json['refundDate'] as String),
);

Map<String, dynamic> _$CancellationResponseToJson(
  CancellationResponse instance,
) => <String, dynamic>{
  'bookingId': instance.bookingId,
  'cancelled': instance.cancelled,
  'refundAmount': instance.refundAmount,
  'refundStatus': instance.refundStatus,
  'refundDate': instance.refundDate?.toIso8601String(),
};

Passenger _$PassengerFromJson(Map<String, dynamic> json) => Passenger(
  firstName: json['firstName'] as String,
  lastName: json['lastName'] as String,
  gender: json['gender'] as String,
  dateOfBirth: DateTime.parse(json['dateOfBirth'] as String),
  passportNumber: json['passportNumber'] as String?,
  nationality: json['nationality'] as String?,
);

Map<String, dynamic> _$PassengerToJson(Passenger instance) => <String, dynamic>{
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'gender': instance.gender,
  'dateOfBirth': instance.dateOfBirth.toIso8601String(),
  'passportNumber': instance.passportNumber,
  'nationality': instance.nationality,
};

FlightSearchRequest _$FlightSearchRequestFromJson(Map<String, dynamic> json) =>
    FlightSearchRequest(
      origin: json['origin'] as String,
      destination: json['destination'] as String,
      departureDate: DateTime.parse(json['departureDate'] as String),
      returnDate: json['returnDate'] == null
          ? null
          : DateTime.parse(json['returnDate'] as String),
      adults: (json['adults'] as num).toInt(),
      children: (json['children'] as num?)?.toInt() ?? 0,
      infants: (json['infants'] as num?)?.toInt() ?? 0,
      classType: json['classType'] as String? ?? 'Economy',
      isRoundTrip: json['isRoundTrip'] as bool? ?? false,
    );

Map<String, dynamic> _$FlightSearchRequestToJson(
  FlightSearchRequest instance,
) => <String, dynamic>{
  'origin': instance.origin,
  'destination': instance.destination,
  'departureDate': instance.departureDate.toIso8601String(),
  'returnDate': instance.returnDate?.toIso8601String(),
  'adults': instance.adults,
  'children': instance.children,
  'infants': instance.infants,
  'classType': instance.classType,
  'isRoundTrip': instance.isRoundTrip,
};

FlightSearchResponse _$FlightSearchResponseFromJson(
  Map<String, dynamic> json,
) => FlightSearchResponse(
  flights: (json['flights'] as List<dynamic>)
      .map((e) => Flight.fromJson(e as Map<String, dynamic>))
      .toList(),
  totalResults: (json['totalResults'] as num).toInt(),
  searchId: json['searchId'] as String,
);

Map<String, dynamic> _$FlightSearchResponseToJson(
  FlightSearchResponse instance,
) => <String, dynamic>{
  'flights': instance.flights.map((e) => e.toJson()).toList(),
  'totalResults': instance.totalResults,
  'searchId': instance.searchId,
};

Flight _$FlightFromJson(Map<String, dynamic> json) => Flight(
  flightId: json['flightId'] as String,
  airline: json['airline'] as String,
  flightNumber: json['flightNumber'] as String,
  origin: json['origin'] as String,
  destination: json['destination'] as String,
  departureTime: DateTime.parse(json['departureTime'] as String),
  arrivalTime: DateTime.parse(json['arrivalTime'] as String),
  duration: json['duration'] as String,
  price: (json['price'] as num).toDouble(),
  currency: json['currency'] as String,
  availableSeats: (json['availableSeats'] as num).toInt(),
  aircraftType: json['aircraftType'] as String,
);

Map<String, dynamic> _$FlightToJson(Flight instance) => <String, dynamic>{
  'flightId': instance.flightId,
  'airline': instance.airline,
  'flightNumber': instance.flightNumber,
  'origin': instance.origin,
  'destination': instance.destination,
  'departureTime': instance.departureTime.toIso8601String(),
  'arrivalTime': instance.arrivalTime.toIso8601String(),
  'duration': instance.duration,
  'price': instance.price,
  'currency': instance.currency,
  'availableSeats': instance.availableSeats,
  'aircraftType': instance.aircraftType,
};

FlightDetails _$FlightDetailsFromJson(Map<String, dynamic> json) =>
    FlightDetails(
      flight: Flight.fromJson(json['flight'] as Map<String, dynamic>),
      amenities: (json['amenities'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      baggage: json['baggage'] as Map<String, dynamic>,
      cancellationPolicy: (json['cancellationPolicy'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$FlightDetailsToJson(FlightDetails instance) =>
    <String, dynamic>{
      'flight': instance.flight.toJson(),
      'amenities': instance.amenities,
      'baggage': instance.baggage,
      'cancellationPolicy': instance.cancellationPolicy,
    };

FlightBookingRequest _$FlightBookingRequestFromJson(
  Map<String, dynamic> json,
) => FlightBookingRequest(
  flightId: json['flightId'] as String,
  passengers: (json['passengers'] as List<dynamic>)
      .map((e) => Passenger.fromJson(e as Map<String, dynamic>))
      .toList(),
  contactEmail: json['contactEmail'] as String,
  contactPhone: json['contactPhone'] as String,
  preferences: json['preferences'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$FlightBookingRequestToJson(
  FlightBookingRequest instance,
) => <String, dynamic>{
  'flightId': instance.flightId,
  'passengers': instance.passengers.map((e) => e.toJson()).toList(),
  'contactEmail': instance.contactEmail,
  'contactPhone': instance.contactPhone,
  'preferences': instance.preferences,
};

BusSearchRequest _$BusSearchRequestFromJson(Map<String, dynamic> json) =>
    BusSearchRequest(
      origin: json['origin'] as String,
      destination: json['destination'] as String,
      journeyDate: DateTime.parse(json['journeyDate'] as String),
      passengers: (json['passengers'] as num).toInt(),
    );

Map<String, dynamic> _$BusSearchRequestToJson(BusSearchRequest instance) =>
    <String, dynamic>{
      'origin': instance.origin,
      'destination': instance.destination,
      'journeyDate': instance.journeyDate.toIso8601String(),
      'passengers': instance.passengers,
    };

BusSearchResponse _$BusSearchResponseFromJson(Map<String, dynamic> json) =>
    BusSearchResponse(
      buses: (json['buses'] as List<dynamic>)
          .map((e) => Bus.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalResults: (json['totalResults'] as num).toInt(),
      searchId: json['searchId'] as String,
    );

Map<String, dynamic> _$BusSearchResponseToJson(BusSearchResponse instance) =>
    <String, dynamic>{
      'buses': instance.buses.map((e) => e.toJson()).toList(),
      'totalResults': instance.totalResults,
      'searchId': instance.searchId,
    };

Bus _$BusFromJson(Map<String, dynamic> json) => Bus(
  busId: json['busId'] as String,
  operator: json['operator'] as String,
  busType: json['busType'] as String,
  origin: json['origin'] as String,
  destination: json['destination'] as String,
  departureTime: DateTime.parse(json['departureTime'] as String),
  arrivalTime: DateTime.parse(json['arrivalTime'] as String),
  duration: json['duration'] as String,
  price: (json['price'] as num).toDouble(),
  currency: json['currency'] as String,
  availableSeats: (json['availableSeats'] as num).toInt(),
  amenities: (json['amenities'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$BusToJson(Bus instance) => <String, dynamic>{
  'busId': instance.busId,
  'operator': instance.operator,
  'busType': instance.busType,
  'origin': instance.origin,
  'destination': instance.destination,
  'departureTime': instance.departureTime.toIso8601String(),
  'arrivalTime': instance.arrivalTime.toIso8601String(),
  'duration': instance.duration,
  'price': instance.price,
  'currency': instance.currency,
  'availableSeats': instance.availableSeats,
  'amenities': instance.amenities,
};

BusBookingRequest _$BusBookingRequestFromJson(Map<String, dynamic> json) =>
    BusBookingRequest(
      busId: json['busId'] as String,
      passengers: (json['passengers'] as List<dynamic>)
          .map((e) => Passenger.fromJson(e as Map<String, dynamic>))
          .toList(),
      contactEmail: json['contactEmail'] as String,
      contactPhone: json['contactPhone'] as String,
      selectedSeats: (json['selectedSeats'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$BusBookingRequestToJson(BusBookingRequest instance) =>
    <String, dynamic>{
      'busId': instance.busId,
      'passengers': instance.passengers.map((e) => e.toJson()).toList(),
      'contactEmail': instance.contactEmail,
      'contactPhone': instance.contactPhone,
      'selectedSeats': instance.selectedSeats,
    };

HotelSearchRequest _$HotelSearchRequestFromJson(Map<String, dynamic> json) =>
    HotelSearchRequest(
      city: json['city'] as String,
      checkIn: DateTime.parse(json['checkIn'] as String),
      checkOut: DateTime.parse(json['checkOut'] as String),
      rooms: (json['rooms'] as num).toInt(),
      adults: (json['adults'] as num).toInt(),
      children: (json['children'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$HotelSearchRequestToJson(HotelSearchRequest instance) =>
    <String, dynamic>{
      'city': instance.city,
      'checkIn': instance.checkIn.toIso8601String(),
      'checkOut': instance.checkOut.toIso8601String(),
      'rooms': instance.rooms,
      'adults': instance.adults,
      'children': instance.children,
    };

HotelSearchResponse _$HotelSearchResponseFromJson(Map<String, dynamic> json) =>
    HotelSearchResponse(
      hotels: (json['hotels'] as List<dynamic>)
          .map((e) => Hotel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalResults: (json['totalResults'] as num).toInt(),
      searchId: json['searchId'] as String,
    );

Map<String, dynamic> _$HotelSearchResponseToJson(
  HotelSearchResponse instance,
) => <String, dynamic>{
  'hotels': instance.hotels.map((e) => e.toJson()).toList(),
  'totalResults': instance.totalResults,
  'searchId': instance.searchId,
};

Hotel _$HotelFromJson(Map<String, dynamic> json) => Hotel(
  hotelId: json['hotelId'] as String,
  name: json['name'] as String,
  address: json['address'] as String,
  rating: (json['rating'] as num).toDouble(),
  price: (json['price'] as num).toDouble(),
  currency: json['currency'] as String,
  amenities: (json['amenities'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  images: (json['images'] as List<dynamic>).map((e) => e as String).toList(),
  description: json['description'] as String,
);

Map<String, dynamic> _$HotelToJson(Hotel instance) => <String, dynamic>{
  'hotelId': instance.hotelId,
  'name': instance.name,
  'address': instance.address,
  'rating': instance.rating,
  'price': instance.price,
  'currency': instance.currency,
  'amenities': instance.amenities,
  'images': instance.images,
  'description': instance.description,
};

HotelDetails _$HotelDetailsFromJson(Map<String, dynamic> json) => HotelDetails(
  hotel: Hotel.fromJson(json['hotel'] as Map<String, dynamic>),
  roomTypes: (json['roomTypes'] as List<dynamic>)
      .map((e) => RoomType.fromJson(e as Map<String, dynamic>))
      .toList(),
  policies: (json['policies'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  location: json['location'] as Map<String, dynamic>,
);

Map<String, dynamic> _$HotelDetailsToJson(HotelDetails instance) =>
    <String, dynamic>{
      'hotel': instance.hotel.toJson(),
      'roomTypes': instance.roomTypes.map((e) => e.toJson()).toList(),
      'policies': instance.policies,
      'location': instance.location,
    };

RoomType _$RoomTypeFromJson(Map<String, dynamic> json) => RoomType(
  roomId: json['roomId'] as String,
  name: json['name'] as String,
  price: (json['price'] as num).toDouble(),
  currency: json['currency'] as String,
  maxOccupancy: (json['maxOccupancy'] as num).toInt(),
  amenities: (json['amenities'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  isAvailable: json['isAvailable'] as bool,
);

Map<String, dynamic> _$RoomTypeToJson(RoomType instance) => <String, dynamic>{
  'roomId': instance.roomId,
  'name': instance.name,
  'price': instance.price,
  'currency': instance.currency,
  'maxOccupancy': instance.maxOccupancy,
  'amenities': instance.amenities,
  'isAvailable': instance.isAvailable,
};

HotelBookingRequest _$HotelBookingRequestFromJson(Map<String, dynamic> json) =>
    HotelBookingRequest(
      hotelId: json['hotelId'] as String,
      roomId: json['roomId'] as String,
      checkIn: DateTime.parse(json['checkIn'] as String),
      checkOut: DateTime.parse(json['checkOut'] as String),
      guests: (json['guests'] as List<dynamic>)
          .map((e) => Guest.fromJson(e as Map<String, dynamic>))
          .toList(),
      contactEmail: json['contactEmail'] as String,
      contactPhone: json['contactPhone'] as String,
      specialRequests: json['specialRequests'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$HotelBookingRequestToJson(
  HotelBookingRequest instance,
) => <String, dynamic>{
  'hotelId': instance.hotelId,
  'roomId': instance.roomId,
  'checkIn': instance.checkIn.toIso8601String(),
  'checkOut': instance.checkOut.toIso8601String(),
  'guests': instance.guests.map((e) => e.toJson()).toList(),
  'contactEmail': instance.contactEmail,
  'contactPhone': instance.contactPhone,
  'specialRequests': instance.specialRequests,
};

Guest _$GuestFromJson(Map<String, dynamic> json) => Guest(
  firstName: json['firstName'] as String,
  lastName: json['lastName'] as String,
  gender: json['gender'] as String,
  dateOfBirth: json['dateOfBirth'] == null
      ? null
      : DateTime.parse(json['dateOfBirth'] as String),
);

Map<String, dynamic> _$GuestToJson(Guest instance) => <String, dynamic>{
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'gender': instance.gender,
  'dateOfBirth': instance.dateOfBirth?.toIso8601String(),
};

TrainSearchRequest _$TrainSearchRequestFromJson(Map<String, dynamic> json) =>
    TrainSearchRequest(
      origin: json['origin'] as String,
      destination: json['destination'] as String,
      journeyDate: DateTime.parse(json['journeyDate'] as String),
      classType: json['classType'] as String,
      passengers: (json['passengers'] as num).toInt(),
    );

Map<String, dynamic> _$TrainSearchRequestToJson(TrainSearchRequest instance) =>
    <String, dynamic>{
      'origin': instance.origin,
      'destination': instance.destination,
      'journeyDate': instance.journeyDate.toIso8601String(),
      'classType': instance.classType,
      'passengers': instance.passengers,
    };

TrainSearchResponse _$TrainSearchResponseFromJson(Map<String, dynamic> json) =>
    TrainSearchResponse(
      trains: (json['trains'] as List<dynamic>)
          .map((e) => Train.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalResults: (json['totalResults'] as num).toInt(),
      searchId: json['searchId'] as String,
    );

Map<String, dynamic> _$TrainSearchResponseToJson(
  TrainSearchResponse instance,
) => <String, dynamic>{
  'trains': instance.trains.map((e) => e.toJson()).toList(),
  'totalResults': instance.totalResults,
  'searchId': instance.searchId,
};

Train _$TrainFromJson(Map<String, dynamic> json) => Train(
  trainId: json['trainId'] as String,
  trainNumber: json['trainNumber'] as String,
  trainName: json['trainName'] as String,
  origin: json['origin'] as String,
  destination: json['destination'] as String,
  departureTime: DateTime.parse(json['departureTime'] as String),
  arrivalTime: DateTime.parse(json['arrivalTime'] as String),
  duration: json['duration'] as String,
  classPrices: (json['classPrices'] as Map<String, dynamic>).map(
    (k, e) => MapEntry(k, (e as num).toDouble()),
  ),
  availableSeats: Map<String, int>.from(json['availableSeats'] as Map),
  amenities: (json['amenities'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$TrainToJson(Train instance) => <String, dynamic>{
  'trainId': instance.trainId,
  'trainNumber': instance.trainNumber,
  'trainName': instance.trainName,
  'origin': instance.origin,
  'destination': instance.destination,
  'departureTime': instance.departureTime.toIso8601String(),
  'arrivalTime': instance.arrivalTime.toIso8601String(),
  'duration': instance.duration,
  'classPrices': instance.classPrices,
  'availableSeats': instance.availableSeats,
  'amenities': instance.amenities,
};

TrainBookingRequest _$TrainBookingRequestFromJson(Map<String, dynamic> json) =>
    TrainBookingRequest(
      trainId: json['trainId'] as String,
      classType: json['classType'] as String,
      passengers: (json['passengers'] as List<dynamic>)
          .map((e) => Passenger.fromJson(e as Map<String, dynamic>))
          .toList(),
      contactEmail: json['contactEmail'] as String,
      contactPhone: json['contactPhone'] as String,
      preferences: json['preferences'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$TrainBookingRequestToJson(
  TrainBookingRequest instance,
) => <String, dynamic>{
  'trainId': instance.trainId,
  'classType': instance.classType,
  'passengers': instance.passengers.map((e) => e.toJson()).toList(),
  'contactEmail': instance.contactEmail,
  'contactPhone': instance.contactPhone,
  'preferences': instance.preferences,
};
