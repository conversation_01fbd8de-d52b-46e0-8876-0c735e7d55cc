import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/auth_provider.dart';
import '../../buyer_routes.dart';

class EmailVerificationScreen extends ConsumerStatefulWidget {
  const EmailVerificationScreen({super.key});

  @override
  ConsumerState<EmailVerificationScreen> createState() =>
      _EmailVerificationScreenState();
}

class _EmailVerificationScreenState
    extends ConsumerState<EmailVerificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  bool _isResendEnabled = true;
  int _resendCountdown = 60;

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  void _onVerifyPressed() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        debugPrint(
            'EmailVerificationScreen: Verifying email with code: ${_codeController.text.trim()}');
        await ref.read(authStateProvider.notifier).verifyPhoneNumber(
              verificationId: ref.read(authStateProvider).verificationId ?? '',
              smsCode: _codeController.text.trim(),
            );

        debugPrint('EmailVerificationScreen: Email verification successful');
        if (mounted) {
          // Navigate to phone verification screen
          debugPrint(
              'EmailVerificationScreen: Navigating to phone verification screen');
          context.go(BuyerRoutes.phoneVerification);
        }
      } catch (e) {
        debugPrint(
            'EmailVerificationScreen: Error during email verification: $e');
        // Show error message to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Verification failed: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _onResendCodePressed() async {
    if (_isResendEnabled) {
      try {
        debugPrint('EmailVerificationScreen: Resending verification code');
        await ref.read(authStateProvider.notifier).sendPhoneVerificationCode(
              ref.read(authStateProvider).user?.phone ?? '',
            );

        debugPrint(
            'EmailVerificationScreen: Verification code resent successfully');
        setState(() {
          _isResendEnabled = false;
          _resendCountdown = 60;
        });

        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            _startResendCountdown();
          }
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content:
                  Text('Verification code resent. Please check your email.'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        debugPrint(
            'EmailVerificationScreen: Error resending verification code: $e');
        // Show error message to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to resend code: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _startResendCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          if (_resendCountdown > 0) {
            _resendCountdown--;
            _startResendCountdown();
          } else {
            _isResendEnabled = true;
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify Email'),
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Verify Your Email',
                    style: theme.textTheme.headlineMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'We\'ve sent a verification code to your email address. Please enter it below.',
                    style: theme.textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  TextFormField(
                    controller: _codeController,
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.done,
                    decoration: const InputDecoration(
                      labelText: 'Verification Code',
                      hintText: 'Enter 6-digit code',
                      prefixIcon: Icon(Icons.lock_outline),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter the verification code';
                      }
                      if (value.length != 6) {
                        return 'Code must be 6 digits';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  Consumer(
                    builder: (context, ref, child) {
                      final authState = ref.watch(authStateProvider);
                      final isLoading = authState.isLoading;

                      return ElevatedButton(
                        onPressed: isLoading ? null : _onVerifyPressed,
                        child: isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Text('Verify'),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: _isResendEnabled ? _onResendCodePressed : null,
                    child: Text(
                      _isResendEnabled
                          ? 'Resend Code'
                          : 'Resend Code in $_resendCountdown seconds',
                    ),
                  ),
                  const SizedBox(height: 24),
                  Consumer(
                    builder: (context, ref, child) {
                      final authState = ref.watch(authStateProvider);
                      return authState.error != null
                          ? Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: Text(
                                authState.error!,
                                style: TextStyle(
                                  color: theme.colorScheme.error,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            )
                          : const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
