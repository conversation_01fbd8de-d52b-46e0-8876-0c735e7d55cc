import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/bookmark_provider.dart';
import '../../../../shared/providers/priest_provider.dart';
import 'priest_booking_form_screen.dart';

class PriestDetailsScreen extends ConsumerWidget {
  final String priestId;

  const PriestDetailsScreen({super.key, required this.priestId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final priestAsync = ref.watch(priestByIdProvider(priestId));
    final authState = ref.watch(authProvider);
    final userId = authState?.id;
    final bookmarksAsync = userId != null
        ? ref.watch(bookmarkProvider(userId))
        : null;

    return Scaffold(
      body: priestAsync.when(
        data: (priest) {
          if (priest == null) {
            return const Center(child: Text('Priest not found'));
          }

          return CustomScrollView(
            slivers: [
              SliverAppBar(
                expandedHeight: 200,
                pinned: true,
                flexibleSpace: FlexibleSpaceBar(
                  background: Image.network(
                    priest.coverImage ?? '',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[300],
                        child: const Icon(Icons.person, size: 64),
                      );
                    },
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.share),
                    onPressed: () {
                      share_plus.SharePlus.instance.share(
                        share_plus.ShareParams(
                          text:
                              'Check out ${priest.name} on Shivish!\n\n'
                              'Specialization: ${priest.specializations.isNotEmpty ? priest.specializations.first : "General"}\n'
                              'Rating: ${priest.rating.toStringAsFixed(1)} ⭐\n'
                              'Experience: ${priest.experience.length} years\n\n'
                              'Services: ${priest.services.join(", ")}\n'
                              'Languages: ${priest.languages.join(", ")}\n\n'
                              'Book now: https://shivish.com/priest/${priest.id}',
                        ),
                      );
                    },
                  ),
                  if (userId != null)
                    IconButton(
                      icon:
                          bookmarksAsync?.when(
                            data: (bookmarks) => Icon(
                              bookmarks.contains(priestId)
                                  ? Icons.bookmark
                                  : Icons.bookmark_border,
                            ),
                            loading: () => const Icon(Icons.bookmark_border),
                            error: (_, __) => const Icon(Icons.bookmark_border),
                          ) ??
                          const Icon(Icons.bookmark_border),
                      onPressed: () {
                        ref
                            .read(bookmarkProvider(userId).notifier)
                            .toggleBookmark(priestId);
                      },
                    ),
                ],
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 40,
                            backgroundImage: NetworkImage(
                              priest.profileImage ?? '',
                            ),
                            onBackgroundImageError: (_, __) {
                              // Handle error
                            },
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  priest.name,
                                  style: Theme.of(
                                    context,
                                  ).textTheme.headlineSmall,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  priest.specializations.isNotEmpty
                                      ? priest.specializations.first
                                      : "General",
                                  style: Theme.of(
                                    context,
                                  ).textTheme.titleMedium,
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    const Icon(Icons.star, color: Colors.amber),
                                    const SizedBox(width: 4),
                                    Text(
                                      priest.rating.toStringAsFixed(1),
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodyLarge,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      '(${priest.totalReviews} reviews)',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodyMedium,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'About',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(priest.description),
                      const SizedBox(height: 24),
                      Text(
                        'Services',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: priest.services
                            .map((service) => Chip(label: Text(service)))
                            .toList(),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Languages',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: priest.languages
                            .map((language) => Chip(label: Text(language)))
                            .toList(),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Certifications',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: priest.certifications.length,
                        itemBuilder: (context, index) {
                          return ListTile(
                            leading: const Icon(Icons.verified),
                            title: Text(priest.certifications[index]),
                          );
                        },
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Experience',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: priest.experience.length,
                        itemBuilder: (context, index) {
                          return ListTile(
                            leading: const Icon(Icons.work),
                            title: Text(priest.experience[index]),
                          );
                        },
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) =>
                                    PriestBookingFormScreen(priestId: priestId),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: const Text('Book Now'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(child: Text('Error: $error')),
      ),
    );
  }
}
