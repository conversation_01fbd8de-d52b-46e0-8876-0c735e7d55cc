import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/models/booking/booking_model.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final paymentServiceProvider = Provider<PaymentService>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return PaymentService(databaseService);
});

class PaymentService {
  final DatabaseService _databaseService;

  PaymentService(this._databaseService);

  Future<void> processPayment(BookingModel booking) async {
    try {
      // Validate booking
      if (booking.id.isEmpty || booking.totalAmount <= 0) {
        throw Exception('Invalid booking data');
      }

      // Calculate commission (e.g., 10% of total amount)
      final commission = booking.totalAmount * 0.1;
      final technicianAmount = booking.totalAmount - commission;

      final now = DateTime.now().toIso8601String();
      final paymentId = '${booking.id}_${DateTime.now().millisecondsSinceEpoch}';

      // Create payment record
      final paymentData = {
        'id': paymentId,
        'booking_id': booking.id,
        'technician_id': booking.providerId,
        'customer_id': booking.customerId,
        'amount': booking.totalAmount,
        'commission': commission,
        'technician_amount': technicianAmount,
        'status': 'completed',
        'payment_method': 'digital',
        'transaction_id': paymentId,
        'created_at': now,
        'updated_at': now,
        'is_deleted': false,
      };

      // Update booking payment status
      final bookingData = {
        'payment_status': 'completed',
        'payment_id': paymentId,
        'updated_at': now,
      };

      // Execute both operations
      await _databaseService.create('payments', paymentData);
      await _databaseService.update('bookings', booking.id, bookingData);

    } catch (e) {
      debugPrint('Failed to process payment: $e');
      throw Exception('Failed to process payment: $e');
    }
  }

  Future<void> processRefund(BookingModel booking, String reason) async {
    try {
      // Validate inputs
      if (booking.id.isEmpty || reason.isEmpty) {
        throw Exception('Invalid refund data');
      }

      final now = DateTime.now().toIso8601String();
      final refundId = 'refund_${booking.id}_${DateTime.now().millisecondsSinceEpoch}';

      // Create refund record
      final refundData = {
        'id': refundId,
        'booking_id': booking.id,
        'technician_id': booking.providerId,
        'customer_id': booking.customerId,
        'amount': booking.totalAmount,
        'reason': reason,
        'status': 'pending',
        'refund_method': 'original_payment',
        'created_at': now,
        'updated_at': now,
        'is_deleted': false,
      };

      // Update booking payment status
      final bookingData = {
        'payment_status': 'refunded',
        'refund_id': refundId,
        'refund_reason': reason,
        'refunded_at': now,
        'updated_at': now,
      };

      // Execute both operations
      await _databaseService.create('refunds', refundData);
      await _databaseService.update('bookings', booking.id, bookingData);

    } catch (e) {
      debugPrint('Failed to process refund: $e');
      throw Exception('Failed to process refund: $e');
    }
  }

  Stream<List<Map<String, dynamic>>> streamTechnicianPayments(
      String technicianId) {
    try {
      return _databaseService.watchCollection(
        'payments',
        where: 'technician_id = ? AND is_deleted = ?',
        whereParams: [technicianId, false],
        orderBy: 'created_at DESC',
      );
    } catch (e) {
      debugPrint('Error streaming technician payments: $e');
      return Stream.value([]);
    }
  }

  Stream<List<Map<String, dynamic>>> streamTechnicianSettlements(
      String technicianId) {
    try {
      return _databaseService.watchCollection(
        'settlements',
        where: 'technician_id = ? AND is_deleted = ?',
        whereParams: [technicianId, false],
        orderBy: 'created_at DESC',
      );
    } catch (e) {
      debugPrint('Error streaming technician settlements: $e');
      return Stream.value([]);
    }
  }

  Future<Map<String, dynamic>> getTechnicianPaymentSummary(
      String technicianId) async {
    try {
      final payments = await _databaseService.getAll(
        'payments',
        where: 'technician_id = ? AND is_deleted = ?',
        whereParams: [technicianId, false],
      );

      final settlements = await _databaseService.getAll(
        'settlements',
        where: 'technician_id = ? AND is_deleted = ?',
        whereParams: [technicianId, false],
      );

      double totalEarnings = 0;
      double totalCommission = 0;
      double totalSettled = 0;
      double pendingSettlement = 0;
      int totalTransactions = payments.length;

      for (final payment in payments) {
        totalEarnings += (payment['amount'] as num?)?.toDouble() ?? 0;
        totalCommission += (payment['commission'] as num?)?.toDouble() ?? 0;
      }

      for (final settlement in settlements) {
        totalSettled += (settlement['amount'] as num?)?.toDouble() ?? 0;
      }

      pendingSettlement = totalEarnings - totalCommission - totalSettled;

      return {
        'total_earnings': totalEarnings,
        'total_commission': totalCommission,
        'total_settled': totalSettled,
        'pending_settlement': pendingSettlement,
        'total_transactions': totalTransactions,
        'net_earnings': totalEarnings - totalCommission,
        'last_updated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Failed to get payment summary: $e');
      return {
        'total_earnings': 0.0,
        'total_commission': 0.0,
        'total_settled': 0.0,
        'pending_settlement': 0.0,
        'total_transactions': 0,
        'net_earnings': 0.0,
        'error': 'Failed to load payment summary',
      };
    }
  }

  Future<void> generateInvoice(String paymentId) async {
    try {
      final paymentData = await _databaseService.find('payments', paymentId);

      if (paymentData == null) {
        throw Exception('Payment not found');
      }

      final now = DateTime.now().toIso8601String();
      final invoiceId = 'invoice_${paymentId}_${DateTime.now().millisecondsSinceEpoch}';

      final invoiceData = {
        'id': invoiceId,
        'payment_id': paymentId,
        'technician_id': paymentData['technician_id'],
        'customer_id': paymentData['customer_id'],
        'booking_id': paymentData['booking_id'],
        'amount': paymentData['amount'],
        'commission': paymentData['commission'],
        'technician_amount': paymentData['technician_amount'],
        'invoice_number': 'INV-${DateTime.now().millisecondsSinceEpoch}',
        'status': 'generated',
        'created_at': now,
        'updated_at': now,
        'is_deleted': false,
      };

      await _databaseService.create('invoices', invoiceData);
    } catch (e) {
      debugPrint('Failed to generate invoice: $e');
      throw Exception('Failed to generate invoice: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getTechnicianInvoices(String technicianId) async {
    try {
      return await _databaseService.getAll(
        'invoices',
        where: 'technician_id = ? AND is_deleted = ?',
        whereParams: [technicianId, false],
        orderBy: 'created_at DESC',
      );
    } catch (e) {
      debugPrint('Failed to get technician invoices: $e');
      return [];
    }
  }

  Future<void> createSettlement({
    required String technicianId,
    required double amount,
    required String bankAccount,
  }) async {
    try {
      final now = DateTime.now().toIso8601String();
      final settlementId = 'settlement_${technicianId}_${DateTime.now().millisecondsSinceEpoch}';

      final settlementData = {
        'id': settlementId,
        'technician_id': technicianId,
        'amount': amount,
        'bank_account': bankAccount,
        'status': 'pending',
        'settlement_date': now,
        'created_at': now,
        'updated_at': now,
        'is_deleted': false,
      };

      await _databaseService.create('settlements', settlementData);
    } catch (e) {
      debugPrint('Failed to create settlement: $e');
      throw Exception('Failed to create settlement: $e');
    }
  }

  Future<void> updatePaymentStatus(String paymentId, String status) async {
    try {
      await _databaseService.update('payments', paymentId, {
        'status': status,
        'status_updated_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to update payment status: $e');
      throw Exception('Failed to update payment status: $e');
    }
  }
}
