import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../providers/auth_provider.dart';
import '../../../shared/utils/logger.dart';

class AdminRequestsScreen extends ConsumerWidget {
  const AdminRequestsScreen({super.key});

  /// Helper method to parse date from various formats
  DateTime? _parseDate(dynamic dateValue) {
    if (dateValue == null) return null;

    if (dateValue is DateTime) {
      return dateValue;
    } else if (dateValue is String) {
      try {
        return DateTime.parse(dateValue);
      } catch (e) {
        getLogger('AdminRequestsScreen').warning('Failed to parse date string: $dateValue');
        return null;
      }
    } else if (dateValue is Map<String, dynamic>) {
      // Handle potential timestamp objects from database
      if (dateValue.containsKey('_seconds')) {
        return DateTime.fromMillisecondsSinceEpoch(
          (dateValue['_seconds'] as int) * 1000,
        );
      }
    }

    getLogger('AdminRequestsScreen').warning('Unknown date format: ${dateValue.runtimeType}');
    return null;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final authState = ref.watch(adminAuthStateProvider);
    final authNotifier = ref.read(adminAuthStateProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Requests'),
      ),
      body: authState.pendingAdminRequests.isEmpty
          ? Center(
              child: Text(
                'No pending admin requests',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(179),
                ),
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: authState.pendingAdminRequests.length,
              itemBuilder: (context, index) {
                final request = authState.pendingAdminRequests[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CircleAvatar(
                              backgroundColor: theme.colorScheme.primary,
                              child: Text(
                                request['displayName'][0].toUpperCase(),
                                style: TextStyle(
                                  color: theme.colorScheme.onPrimary,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    request['displayName'],
                                    style: theme.textTheme.titleMedium,
                                  ),
                                  Text(
                                    request['email'],
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: theme.colorScheme.onSurface
                                          .withAlpha(179),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Reason for Request:',
                          style: theme.textTheme.titleSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(request['reason']),
                        const SizedBox(height: 16),
                        Text(
                          'Requested ${() {
                            final requestedAt = _parseDate(request['requestedAt']);
                            return requestedAt != null
                                ? timeago.format(requestedAt)
                                : 'Unknown time';
                          }()}',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(179),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(
                              onPressed: () {
                                showDialog(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    title: const Text('Reject Request'),
                                    content: TextFormField(
                                      decoration: const InputDecoration(
                                        labelText: 'Reason for rejection',
                                        hintText: 'Enter reason for rejection',
                                      ),
                                      maxLines: 3,
                                      onChanged: (value) {
                                        if (value.isNotEmpty) {
                                          authNotifier.rejectAdminRequest(
                                            request['id'],
                                            value,
                                          );
                                          Navigator.pop(context);
                                        }
                                      },
                                    ),
                                  ),
                                );
                              },
                              child: const Text('Reject'),
                            ),
                            const SizedBox(width: 8),
                            ElevatedButton(
                              onPressed: () {
                                authNotifier.approveAdminRequest(request['id']);
                              },
                              child: const Text('Approve'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }
}
