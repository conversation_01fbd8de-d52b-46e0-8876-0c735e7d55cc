// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_offer_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BankOfferModel {

 String get id; String get title; String get description; String get bankName; String? get bankLogoUrl; BankOfferType get offerType; double get value; String? get code; DateTime get startDate; DateTime get endDate; List<String> get applicableProductIds; List<String> get applicableCategories; double get minimumPurchaseAmount; double? get maximumDiscountAmount; bool get isActive; DateTime get createdAt; DateTime get updatedAt; bool get isDeleted; String? get termsAndConditions;
/// Create a copy of BankOfferModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BankOfferModelCopyWith<BankOfferModel> get copyWith => _$BankOfferModelCopyWithImpl<BankOfferModel>(this as BankOfferModel, _$identity);

  /// Serializes this BankOfferModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BankOfferModel&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.bankLogoUrl, bankLogoUrl) || other.bankLogoUrl == bankLogoUrl)&&(identical(other.offerType, offerType) || other.offerType == offerType)&&(identical(other.value, value) || other.value == value)&&(identical(other.code, code) || other.code == code)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&const DeepCollectionEquality().equals(other.applicableProductIds, applicableProductIds)&&const DeepCollectionEquality().equals(other.applicableCategories, applicableCategories)&&(identical(other.minimumPurchaseAmount, minimumPurchaseAmount) || other.minimumPurchaseAmount == minimumPurchaseAmount)&&(identical(other.maximumDiscountAmount, maximumDiscountAmount) || other.maximumDiscountAmount == maximumDiscountAmount)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.termsAndConditions, termsAndConditions) || other.termsAndConditions == termsAndConditions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,title,description,bankName,bankLogoUrl,offerType,value,code,startDate,endDate,const DeepCollectionEquality().hash(applicableProductIds),const DeepCollectionEquality().hash(applicableCategories),minimumPurchaseAmount,maximumDiscountAmount,isActive,createdAt,updatedAt,isDeleted,termsAndConditions]);

@override
String toString() {
  return 'BankOfferModel(id: $id, title: $title, description: $description, bankName: $bankName, bankLogoUrl: $bankLogoUrl, offerType: $offerType, value: $value, code: $code, startDate: $startDate, endDate: $endDate, applicableProductIds: $applicableProductIds, applicableCategories: $applicableCategories, minimumPurchaseAmount: $minimumPurchaseAmount, maximumDiscountAmount: $maximumDiscountAmount, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, termsAndConditions: $termsAndConditions)';
}


}

/// @nodoc
abstract mixin class $BankOfferModelCopyWith<$Res>  {
  factory $BankOfferModelCopyWith(BankOfferModel value, $Res Function(BankOfferModel) _then) = _$BankOfferModelCopyWithImpl;
@useResult
$Res call({
 String id, String title, String description, String bankName, String? bankLogoUrl, BankOfferType offerType, double value, String? code, DateTime startDate, DateTime endDate, List<String> applicableProductIds, List<String> applicableCategories, double minimumPurchaseAmount, double? maximumDiscountAmount, bool isActive, DateTime createdAt, DateTime updatedAt, bool isDeleted, String? termsAndConditions
});




}
/// @nodoc
class _$BankOfferModelCopyWithImpl<$Res>
    implements $BankOfferModelCopyWith<$Res> {
  _$BankOfferModelCopyWithImpl(this._self, this._then);

  final BankOfferModel _self;
  final $Res Function(BankOfferModel) _then;

/// Create a copy of BankOfferModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? title = null,Object? description = null,Object? bankName = null,Object? bankLogoUrl = freezed,Object? offerType = null,Object? value = null,Object? code = freezed,Object? startDate = null,Object? endDate = null,Object? applicableProductIds = null,Object? applicableCategories = null,Object? minimumPurchaseAmount = null,Object? maximumDiscountAmount = freezed,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,Object? termsAndConditions = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,bankName: null == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String,bankLogoUrl: freezed == bankLogoUrl ? _self.bankLogoUrl : bankLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,offerType: null == offerType ? _self.offerType : offerType // ignore: cast_nullable_to_non_nullable
as BankOfferType,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,applicableProductIds: null == applicableProductIds ? _self.applicableProductIds : applicableProductIds // ignore: cast_nullable_to_non_nullable
as List<String>,applicableCategories: null == applicableCategories ? _self.applicableCategories : applicableCategories // ignore: cast_nullable_to_non_nullable
as List<String>,minimumPurchaseAmount: null == minimumPurchaseAmount ? _self.minimumPurchaseAmount : minimumPurchaseAmount // ignore: cast_nullable_to_non_nullable
as double,maximumDiscountAmount: freezed == maximumDiscountAmount ? _self.maximumDiscountAmount : maximumDiscountAmount // ignore: cast_nullable_to_non_nullable
as double?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,termsAndConditions: freezed == termsAndConditions ? _self.termsAndConditions : termsAndConditions // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [BankOfferModel].
extension BankOfferModelPatterns on BankOfferModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BankOfferModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BankOfferModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BankOfferModel value)  $default,){
final _that = this;
switch (_that) {
case _BankOfferModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BankOfferModel value)?  $default,){
final _that = this;
switch (_that) {
case _BankOfferModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String title,  String description,  String bankName,  String? bankLogoUrl,  BankOfferType offerType,  double value,  String? code,  DateTime startDate,  DateTime endDate,  List<String> applicableProductIds,  List<String> applicableCategories,  double minimumPurchaseAmount,  double? maximumDiscountAmount,  bool isActive,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  String? termsAndConditions)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BankOfferModel() when $default != null:
return $default(_that.id,_that.title,_that.description,_that.bankName,_that.bankLogoUrl,_that.offerType,_that.value,_that.code,_that.startDate,_that.endDate,_that.applicableProductIds,_that.applicableCategories,_that.minimumPurchaseAmount,_that.maximumDiscountAmount,_that.isActive,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.termsAndConditions);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String title,  String description,  String bankName,  String? bankLogoUrl,  BankOfferType offerType,  double value,  String? code,  DateTime startDate,  DateTime endDate,  List<String> applicableProductIds,  List<String> applicableCategories,  double minimumPurchaseAmount,  double? maximumDiscountAmount,  bool isActive,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  String? termsAndConditions)  $default,) {final _that = this;
switch (_that) {
case _BankOfferModel():
return $default(_that.id,_that.title,_that.description,_that.bankName,_that.bankLogoUrl,_that.offerType,_that.value,_that.code,_that.startDate,_that.endDate,_that.applicableProductIds,_that.applicableCategories,_that.minimumPurchaseAmount,_that.maximumDiscountAmount,_that.isActive,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.termsAndConditions);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String title,  String description,  String bankName,  String? bankLogoUrl,  BankOfferType offerType,  double value,  String? code,  DateTime startDate,  DateTime endDate,  List<String> applicableProductIds,  List<String> applicableCategories,  double minimumPurchaseAmount,  double? maximumDiscountAmount,  bool isActive,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  String? termsAndConditions)?  $default,) {final _that = this;
switch (_that) {
case _BankOfferModel() when $default != null:
return $default(_that.id,_that.title,_that.description,_that.bankName,_that.bankLogoUrl,_that.offerType,_that.value,_that.code,_that.startDate,_that.endDate,_that.applicableProductIds,_that.applicableCategories,_that.minimumPurchaseAmount,_that.maximumDiscountAmount,_that.isActive,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.termsAndConditions);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BankOfferModel implements BankOfferModel {
  const _BankOfferModel({required this.id, required this.title, required this.description, required this.bankName, this.bankLogoUrl, required this.offerType, required this.value, this.code, required this.startDate, required this.endDate, required final  List<String> applicableProductIds, required final  List<String> applicableCategories, required this.minimumPurchaseAmount, this.maximumDiscountAmount, this.isActive = false, required this.createdAt, required this.updatedAt, this.isDeleted = false, this.termsAndConditions}): _applicableProductIds = applicableProductIds,_applicableCategories = applicableCategories;
  factory _BankOfferModel.fromJson(Map<String, dynamic> json) => _$BankOfferModelFromJson(json);

@override final  String id;
@override final  String title;
@override final  String description;
@override final  String bankName;
@override final  String? bankLogoUrl;
@override final  BankOfferType offerType;
@override final  double value;
@override final  String? code;
@override final  DateTime startDate;
@override final  DateTime endDate;
 final  List<String> _applicableProductIds;
@override List<String> get applicableProductIds {
  if (_applicableProductIds is EqualUnmodifiableListView) return _applicableProductIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_applicableProductIds);
}

 final  List<String> _applicableCategories;
@override List<String> get applicableCategories {
  if (_applicableCategories is EqualUnmodifiableListView) return _applicableCategories;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_applicableCategories);
}

@override final  double minimumPurchaseAmount;
@override final  double? maximumDiscountAmount;
@override@JsonKey() final  bool isActive;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool isDeleted;
@override final  String? termsAndConditions;

/// Create a copy of BankOfferModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BankOfferModelCopyWith<_BankOfferModel> get copyWith => __$BankOfferModelCopyWithImpl<_BankOfferModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BankOfferModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BankOfferModel&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.bankLogoUrl, bankLogoUrl) || other.bankLogoUrl == bankLogoUrl)&&(identical(other.offerType, offerType) || other.offerType == offerType)&&(identical(other.value, value) || other.value == value)&&(identical(other.code, code) || other.code == code)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&const DeepCollectionEquality().equals(other._applicableProductIds, _applicableProductIds)&&const DeepCollectionEquality().equals(other._applicableCategories, _applicableCategories)&&(identical(other.minimumPurchaseAmount, minimumPurchaseAmount) || other.minimumPurchaseAmount == minimumPurchaseAmount)&&(identical(other.maximumDiscountAmount, maximumDiscountAmount) || other.maximumDiscountAmount == maximumDiscountAmount)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.termsAndConditions, termsAndConditions) || other.termsAndConditions == termsAndConditions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,title,description,bankName,bankLogoUrl,offerType,value,code,startDate,endDate,const DeepCollectionEquality().hash(_applicableProductIds),const DeepCollectionEquality().hash(_applicableCategories),minimumPurchaseAmount,maximumDiscountAmount,isActive,createdAt,updatedAt,isDeleted,termsAndConditions]);

@override
String toString() {
  return 'BankOfferModel(id: $id, title: $title, description: $description, bankName: $bankName, bankLogoUrl: $bankLogoUrl, offerType: $offerType, value: $value, code: $code, startDate: $startDate, endDate: $endDate, applicableProductIds: $applicableProductIds, applicableCategories: $applicableCategories, minimumPurchaseAmount: $minimumPurchaseAmount, maximumDiscountAmount: $maximumDiscountAmount, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, termsAndConditions: $termsAndConditions)';
}


}

/// @nodoc
abstract mixin class _$BankOfferModelCopyWith<$Res> implements $BankOfferModelCopyWith<$Res> {
  factory _$BankOfferModelCopyWith(_BankOfferModel value, $Res Function(_BankOfferModel) _then) = __$BankOfferModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String title, String description, String bankName, String? bankLogoUrl, BankOfferType offerType, double value, String? code, DateTime startDate, DateTime endDate, List<String> applicableProductIds, List<String> applicableCategories, double minimumPurchaseAmount, double? maximumDiscountAmount, bool isActive, DateTime createdAt, DateTime updatedAt, bool isDeleted, String? termsAndConditions
});




}
/// @nodoc
class __$BankOfferModelCopyWithImpl<$Res>
    implements _$BankOfferModelCopyWith<$Res> {
  __$BankOfferModelCopyWithImpl(this._self, this._then);

  final _BankOfferModel _self;
  final $Res Function(_BankOfferModel) _then;

/// Create a copy of BankOfferModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? title = null,Object? description = null,Object? bankName = null,Object? bankLogoUrl = freezed,Object? offerType = null,Object? value = null,Object? code = freezed,Object? startDate = null,Object? endDate = null,Object? applicableProductIds = null,Object? applicableCategories = null,Object? minimumPurchaseAmount = null,Object? maximumDiscountAmount = freezed,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,Object? termsAndConditions = freezed,}) {
  return _then(_BankOfferModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,bankName: null == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String,bankLogoUrl: freezed == bankLogoUrl ? _self.bankLogoUrl : bankLogoUrl // ignore: cast_nullable_to_non_nullable
as String?,offerType: null == offerType ? _self.offerType : offerType // ignore: cast_nullable_to_non_nullable
as BankOfferType,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,code: freezed == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String?,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,applicableProductIds: null == applicableProductIds ? _self._applicableProductIds : applicableProductIds // ignore: cast_nullable_to_non_nullable
as List<String>,applicableCategories: null == applicableCategories ? _self._applicableCategories : applicableCategories // ignore: cast_nullable_to_non_nullable
as List<String>,minimumPurchaseAmount: null == minimumPurchaseAmount ? _self.minimumPurchaseAmount : minimumPurchaseAmount // ignore: cast_nullable_to_non_nullable
as double,maximumDiscountAmount: freezed == maximumDiscountAmount ? _self.maximumDiscountAmount : maximumDiscountAmount // ignore: cast_nullable_to_non_nullable
as double?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,termsAndConditions: freezed == termsAndConditions ? _self.termsAndConditions : termsAndConditions // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
