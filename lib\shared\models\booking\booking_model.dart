import 'package:freezed_annotation/freezed_annotation.dart';

part 'booking_model.freezed.dart';
part 'booking_model.g.dart';

enum BookingType {
  @JsonValue(0)
  priest,
  @JsonValue(1)
  technician,
}

enum BookingStatus {
  @JsonValue(0)
  pending,
  @JsonValue(1)
  confirmed,
  @JsonValue(2)
  inProgress,
  @JsonValue(3)
  completed,
  @JsonValue(4)
  cancelled,
  @JsonValue(5)
  rescheduled,
  @JsonValue(6)
  noShow,
  @JsonValue(7)
  rejected
}

enum PaymentStatus {
  @JsonValue(0)
  pending,
  @JsonValue(1)
  completed,
  @JsonValue(2)
  failed,
  @JsonValue(3)
  refunded,
  @JsonValue(4)
  partiallyRefunded,
}

@freezed
abstract class BookingAddress with _$BookingAddress {
  const factory BookingAddress({
    required String street,
    required String city,
    required String state,
    required String country,
    required String postalCode,
    String? landmark,
    required String contactName,
    required String contactPhone,
    String? contactEmail,
    double? latitude,
    double? longitude,
  }) = _BookingAddress;

  factory BookingAddress.fromJson(Map<String, dynamic> json) =>
      _$BookingAddressFromJson(json);

  factory BookingAddress.empty() => const BookingAddress(
        street: '',
        city: '',
        state: '',
        country: '',
        postalCode: '',
        contactName: '',
        contactPhone: '',
      );
}

@freezed
abstract class BookingModel with _$BookingModel {
  const factory BookingModel({
    required String id,
    required String bookingNumber,
    required String customerId,
    required String providerId,
    required BookingType type,
    required BookingStatus status,
    required PaymentStatus paymentStatus,
    required DateTime bookingDate,
    required DateTime startTime,
    required DateTime endTime,
    required BookingAddress serviceLocation,
    required List<String> services,
    required double subtotalAmount,
    required double taxAmount,
    required double discountAmount,
    required double totalAmount,
    String? paymentMethod,
    String? paymentId,
    String? transactionId,
    String? refundId,
    String? notes,
    String? customerNotes,
    String? providerNotes,
    String? cancellationReason,
    String? refundReason,
    String? customerName,
    String? customerPhone,
    String? customerEmail,
    String? providerName,
    String? providerPhone,
    String? providerEmail,
    @Default(false) bool reminderSent,
    DateTime? reminderSentAt,
    @Default(false) bool confirmationSent,
    DateTime? confirmationSentAt,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isDeleted,
    double? rating,
    String? review,
    List<String>? attachments,
  }) = _BookingModel;

  factory BookingModel.fromJson(Map<String, dynamic> json) =>
      _$BookingModelFromJson(json);

  factory BookingModel.empty() => BookingModel(
        id: '',
        bookingNumber: '',
        customerId: '',
        providerId: '',
        type: BookingType.priest,
        status: BookingStatus.pending,
        paymentStatus: PaymentStatus.pending,
        bookingDate: DateTime.now(),
        startTime: DateTime.now(),
        endTime: DateTime.now().add(const Duration(hours: 1)),
        serviceLocation: BookingAddress.empty(),
        services: [],
        subtotalAmount: 0.0,
        taxAmount: 0.0,
        discountAmount: 0.0,
        totalAmount: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        rating: null,
        review: null,
        attachments: null,
      );
}
