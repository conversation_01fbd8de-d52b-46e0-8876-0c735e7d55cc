import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/apps/seller/domain/models/promotion_model.dart';
import 'package:shivish/apps/seller/domain/models/campaign_model.dart';
import 'package:shivish/apps/seller/domain/repositories/marketing_repository.dart';

part 'marketing_cubit.freezed.dart';
part 'marketing_cubit.g.dart';

@freezed
sealed class MarketingState with _$MarketingState {
  const factory MarketingState({
    @Default([]) List<PromotionModel> promotions,
    @Default([]) List<CampaignModel> campaigns,
    @Default(false) bool isLoading,
    String? error,
  }) = _MarketingState;

  const MarketingState._();

  bool get hasError => error != null;
  String? get errorMessage => error;

  factory MarketingState.fromJson(Map<String, dynamic> json) =>
      _$MarketingStateFromJson(json);
}

class MarketingCubit extends Cubit<MarketingState> {
  final MarketingRepository _repository;

  MarketingCubit(this._repository) : super(const MarketingState());

  Future<void> loadPromotions() async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      final promotions = await _repository.getPromotions();
      emit(state.copyWith(
        promotions: promotions,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> loadCampaigns() async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      final campaigns = await _repository.getCampaigns();
      emit(state.copyWith(
        campaigns: campaigns,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> createPromotion(PromotionModel promotion) async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      await _repository.createPromotion(promotion);
      await loadPromotions();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> updatePromotion(PromotionModel promotion) async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      await _repository.updatePromotion(promotion);
      await loadPromotions();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> deletePromotion(String id) async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      await _repository.deletePromotion(id);
      await loadPromotions();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> togglePromotionStatus(String id) async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      final promotion = state.promotions.firstWhere((p) => p.id == id);
      final updatedPromotion = promotion.copyWith(
        isActive: !promotion.isActive,
        updatedAt: DateTime.now(),
      );
      await _repository.updatePromotion(updatedPromotion);
      await loadPromotions();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> createCampaign(CampaignModel campaign) async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      await _repository.createCampaign(campaign);
      await loadCampaigns();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> updateCampaign(CampaignModel campaign) async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      await _repository.updateCampaign(campaign);
      await loadCampaigns();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> deleteCampaign(String id) async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      await _repository.deleteCampaign(id);
      await loadCampaigns();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> updateCampaignStatus(String id, CampaignStatus status) async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      final campaign = state.campaigns.firstWhere((c) => c.id == id);
      final updatedCampaign = campaign.copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );
      await _repository.updateCampaign(updatedCampaign);
      await loadCampaigns();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> updateCampaignMetrics(
    String id, {
    int? impressions,
    int? clicks,
    int? conversions,
    double? spent,
  }) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      await _repository.updateCampaignMetrics(
        id,
        impressions: impressions,
        clicks: clicks,
        conversions: conversions,
        spent: spent,
      );

      final campaign = state.campaigns.firstWhere((c) => c.id == id);
      final updatedCampaign = campaign.copyWith(
        impressions: impressions != null
            ? campaign.impressions + impressions
            : campaign.impressions,
        clicks: clicks != null ? campaign.clicks + clicks : campaign.clicks,
        conversions: conversions != null
            ? campaign.conversions + conversions
            : campaign.conversions,
        spent: spent != null ? campaign.spent + spent : campaign.spent,
      );

      final campaigns =
          state.campaigns.map((c) => c.id == id ? updatedCampaign : c).toList();
      emit(state.copyWith(campaigns: campaigns, isLoading: false));
    } catch (e) {
      emit(state.copyWith(error: e.toString(), isLoading: false));
    }
  }

  void clearError() {
    emit(state.copyWith(error: null));
  }
}
