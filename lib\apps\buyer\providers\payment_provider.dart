import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../notifiers/payment_notifier.dart';
import '../states/payment_state.dart';
import '../../../shared/services/payment/payment_service_provider.dart';
import '../../../shared/models/payment/card_model.dart';

/// Main payment provider for the buyer app
final paymentProvider =
    StateNotifierProvider<PaymentNotifier, PaymentState>((ref) {
  final paymentService = ref.watch(paymentServiceProvider);
  return PaymentNotifier(paymentService: paymentService);
});

/// Provider for user's saved cards
final userCardsProvider = Provider<AsyncValue<List<CardModel>>>((ref) {
  // Use ref.read instead of ref.watch to avoid circular dependencies
  final paymentState = ref.read(paymentProvider);
  return AsyncValue.data(paymentState.cards);
});
