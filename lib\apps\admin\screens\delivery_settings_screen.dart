import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/delivery/delivery_provider_model.dart';
import '../../../shared/services/delivery/delivery_service.dart';
import '../../../shared/ui_components/inputs/text_field.dart';
import '../../../shared/ui_components/cards/app_card.dart';
import '../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../shared/ui_components/errors/error_message.dart';

class DeliverySettingsScreen extends ConsumerStatefulWidget {
  const DeliverySettingsScreen({super.key});

  @override
  ConsumerState<DeliverySettingsScreen> createState() => _DeliverySettingsScreenState();
}

class _DeliverySettingsScreenState extends ConsumerState<DeliverySettingsScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  List<DeliveryProviderModel> _providers = [];

  // Ecom Express settings
  final _ecomApiUrlController = TextEditingController();
  final _ecomTrackingUrlController = TextEditingController();
  final _ecomApiKeyController = TextEditingController();

  // Local delivery settings
  final _localMaxDistanceMetroController = TextEditingController();
  final _localMaxDistanceOtherController = TextEditingController();
  final _localBaseChargeController = TextEditingController();
  final _localPerKmChargeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _ecomApiUrlController.dispose();
    _ecomTrackingUrlController.dispose();
    _ecomApiKeyController.dispose();
    _localMaxDistanceMetroController.dispose();
    _localMaxDistanceOtherController.dispose();
    _localBaseChargeController.dispose();
    _localPerKmChargeController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final deliveryService = ref.read(deliveryServiceProvider);

      // Initialize default settings if needed
      await deliveryService.initializeDefaultDeliverySettings();

      // Load providers
      final providers = await deliveryService.getDeliveryProviders();

      setState(() {
        _providers = providers;

        // Set controller values for Ecom Express
        final ecomProvider = providers.firstWhere(
          (p) => p.type == DeliveryProviderType.ecomExpress,
          orElse: () => DeliveryProviderModel.ecomExpress(),
        );

        _ecomApiUrlController.text = ecomProvider.config['api_url'] ?? 'https://api.ecomexpress.in';
        _ecomTrackingUrlController.text = ecomProvider.config['tracking_url'] ?? 'https://track.ecomexpress.in';
        _ecomApiKeyController.text = ecomProvider.config['api_key'] ?? '';

        // Set controller values for Local Delivery
        final localProvider = providers.firstWhere(
          (p) => p.type == DeliveryProviderType.localDelivery,
          orElse: () => DeliveryProviderModel.localDelivery(),
        );

        _localMaxDistanceMetroController.text = (localProvider.config['max_distance_metro'] ?? 5.0).toString();
        _localMaxDistanceOtherController.text = (localProvider.config['max_distance_other'] ?? 10.0).toString();
        _localBaseChargeController.text = (localProvider.config['base_charge'] ?? 30.0).toString();
        _localPerKmChargeController.text = (localProvider.config['per_km_charge'] ?? 10.0).toString();
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading delivery settings: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final deliveryService = ref.read(deliveryServiceProvider);

      // Update Ecom Express provider
      final ecomProvider = _providers.firstWhere(
        (p) => p.type == DeliveryProviderType.ecomExpress,
        orElse: () => DeliveryProviderModel.ecomExpress(),
      );

      final updatedEcomProvider = ecomProvider.copyWith(
        config: {
          'api_url': _ecomApiUrlController.text,
          'tracking_url': _ecomTrackingUrlController.text,
          'api_key': _ecomApiKeyController.text,
        },
      );

      // Update Local Delivery provider
      final localProvider = _providers.firstWhere(
        (p) => p.type == DeliveryProviderType.localDelivery,
        orElse: () => DeliveryProviderModel.localDelivery(),
      );

      final updatedLocalProvider = localProvider.copyWith(
        config: {
          'max_distance_metro': double.tryParse(_localMaxDistanceMetroController.text) ?? 5.0,
          'max_distance_other': double.tryParse(_localMaxDistanceOtherController.text) ?? 10.0,
          'base_charge': double.tryParse(_localBaseChargeController.text) ?? 30.0,
          'per_km_charge': double.tryParse(_localPerKmChargeController.text) ?? 10.0,
        },
      );

      // Save providers
      await deliveryService.saveDeliveryProvider(updatedEcomProvider);
      await deliveryService.saveDeliveryProvider(updatedLocalProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Delivery settings saved successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error saving delivery settings: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving settings: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Delivery Settings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSettings,
            tooltip: 'Reload settings',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _errorMessage != null && _providers.isEmpty
              ? ErrorMessage(
                  message: _errorMessage!,
                  onRetry: _loadSettings,
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Ecom Express Settings
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Text(
                          'Ecom Express Settings',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ),
                      AppCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Configure Ecom Express for delivery of normal orders (electronics, kitchenware, etc.) that are far from the buyer.',
                              style: TextStyle(fontSize: 14),
                            ),
                            const SizedBox(height: 16),
                            AppTextField(
                              controller: _ecomApiUrlController,
                              label: 'API URL',
                              hint: 'https://api.ecomexpress.in',
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _ecomTrackingUrlController,
                              label: 'Tracking URL',
                              hint: 'https://track.ecomexpress.in',
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _ecomApiKeyController,
                              label: 'API Key',
                              hint: 'Enter your Ecom Express API key',
                              obscureText: true,
                            ),
                            const SizedBox(height: 12),
                            SwitchListTile(
                              title: const Text('Enable Ecom Express'),
                              value: _providers
                                  .firstWhere(
                                    (p) => p.type == DeliveryProviderType.ecomExpress,
                                    orElse: () => DeliveryProviderModel.ecomExpress(),
                                  )
                                  .isActive,
                              onChanged: (value) {
                                setState(() {
                                  final index = _providers.indexWhere(
                                    (p) => p.type == DeliveryProviderType.ecomExpress,
                                  );
                                  if (index >= 0) {
                                    _providers[index] = _providers[index].copyWith(isActive: value);
                                  }
                                });
                              },
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Local Delivery Settings
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Text(
                          'Local Delivery Settings',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ),
                      AppCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Configure local delivery for food, list purchases, and other goods within specified distance range.',
                              style: TextStyle(fontSize: 14),
                            ),
                            const SizedBox(height: 16),
                            AppTextField(
                              controller: _localMaxDistanceMetroController,
                              label: 'Max Distance in Metro Cities (km)',
                              hint: '5.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _localMaxDistanceOtherController,
                              label: 'Max Distance in Other Places (km)',
                              hint: '10.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _localBaseChargeController,
                              label: 'Base Delivery Charge',
                              hint: '30.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _localPerKmChargeController,
                              label: 'Per Kilometer Charge',
                              hint: '10.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            SwitchListTile(
                              title: const Text('Enable Local Delivery'),
                              value: _providers
                                  .firstWhere(
                                    (p) => p.type == DeliveryProviderType.localDelivery,
                                    orElse: () => DeliveryProviderModel.localDelivery(),
                                  )
                                  .isActive,
                              onChanged: (value) {
                                setState(() {
                                  final index = _providers.indexWhere(
                                    (p) => p.type == DeliveryProviderType.localDelivery,
                                  );
                                  if (index >= 0) {
                                    _providers[index] = _providers[index].copyWith(isActive: value);
                                  }
                                });
                              },
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Save Button
                      Center(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _saveSettings,
                          child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(strokeWidth: 2)
                              )
                            : const Text('Save Settings'),
                        ),
                      ),
                    ],
                  ),
                ),
    );
  }
}
