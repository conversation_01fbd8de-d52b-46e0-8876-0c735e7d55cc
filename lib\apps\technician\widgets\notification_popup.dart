import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/notification/notification_model.dart';
import '../../../shared/models/notification/notification_type.dart';
import '../technician_routes.dart';

/// Widget for displaying popup notifications in the technician app
class TechnicianNotificationPopup extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onDismiss;
  final VoidCallback? onTap;

  const TechnicianNotificationPopup({
    super.key,
    required this.notification,
    this.onDismiss,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: InkWell(
          onTap: () {
            onTap?.call();
            _handleNotificationTap(context);
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getNotificationColor(notification.type),
                  child: Icon(
                    _getNotificationIcon(notification.type),
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        notification.title,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        notification.body,
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  onPressed: onDismiss,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleNotificationTap(BuildContext context) {
    switch (notification.type) {
      case NotificationType.booking:
        if (notification.data['bookingId'] != null) {
          context.go('/technician/bookings/${notification.data['bookingId']}');
        } else {
          context.go(TechnicianRoutes.bookings);
        }
        break;
      case NotificationType.payment:
        if (notification.data['paymentId'] != null) {
          context.go(
            '${TechnicianRoutes.payments}/${notification.data['paymentId']}',
          );
        } else {
          context.go(TechnicianRoutes.payments);
        }
        break;
      case NotificationType.verification:
        context.go(TechnicianRoutes.profileSettings);
        break;
      case NotificationType.system:
        if (notification.data['action'] != null) {
          _handleSystemAction(context, notification.data['action']);
        } else {
          context.go(TechnicianRoutes.settings);
        }
        break;
      case NotificationType.general:
        if (notification.data['category'] != null) {
          _handleGeneralNotification(context, notification.data['category']);
        } else {
          context.go(TechnicianRoutes.notifications);
        }
        break;
      default:
        // Navigate to notifications screen for other types
        context.go(TechnicianRoutes.notifications);
        break;
    }
  }

  void _handleSystemAction(BuildContext context, String action) {
    switch (action) {
      case 'profile_update':
        context.go(TechnicianRoutes.profileSettings);
        break;
      case 'service_update':
        context.go(TechnicianRoutes.services);
        break;
      case 'working_hours':
        context.go(TechnicianRoutes.workingHours);
        break;
      case 'availability':
        context.go(TechnicianRoutes.workingHours);
        break;
      case 'app_update':
        // Handle app update notification
        break;
      case 'maintenance':
        // Handle maintenance notification
        break;
      default:
        context.go(TechnicianRoutes.settings);
        break;
    }
  }

  void _handleGeneralNotification(BuildContext context, String category) {
    switch (category) {
      case 'booking_request':
        context.go(TechnicianRoutes.bookings);
        break;
      case 'payment_received':
        context.go(TechnicianRoutes.payments);
        break;
      case 'service_reminder':
        context.go(TechnicianRoutes.services);
        break;
      case 'calendar_update':
        context.go(TechnicianRoutes.bookingCalendar);
        break;
      case 'review_received':
        context.go(TechnicianRoutes.notifications);
        break;
      default:
        context.go(TechnicianRoutes.notifications);
        break;
    }
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.booking:
        return Icons.book_online;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.verification:
        return Icons.verified_user;
      case NotificationType.system:
        return Icons.warning;
      case NotificationType.general:
        return Icons.handyman;
      default:
        return Icons.notifications;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.booking:
        return Colors.blue;
      case NotificationType.payment:
        return Colors.green;
      case NotificationType.verification:
        return Colors.indigo;
      case NotificationType.system:
        return Colors.red;
      case NotificationType.general:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}

/// Service for showing notification popups
class TechnicianNotificationPopupService {
  static OverlayEntry? _currentOverlay;

  /// Show a notification popup
  static void showNotificationPopup(
    BuildContext context,
    NotificationModel notification, {
    Duration duration = const Duration(seconds: 4),
  }) {
    // Remove any existing popup
    hideNotificationPopup();

    final overlay = Overlay.of(context);

    _currentOverlay = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 16,
        left: 0,
        right: 0,
        child: TechnicianNotificationPopup(
          notification: notification,
          onDismiss: hideNotificationPopup,
          onTap: hideNotificationPopup,
        ),
      ),
    );

    overlay.insert(_currentOverlay!);

    // Auto-hide after duration
    Future.delayed(duration, () {
      hideNotificationPopup();
    });
  }

  /// Hide the current notification popup
  static void hideNotificationPopup() {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }
}
