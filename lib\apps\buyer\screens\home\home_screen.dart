import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/product_provider.dart';
import '../../utils/auto_refresh.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/refreshable_screen.dart';
import '../../widgets/navigation/buyer_drawer.dart';
import '../../widgets/navigation/transparent_app_bar.dart';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/product/product_grid.dart';
import '../../buyer_routes.dart';
import '../../../../shared/ui_components/navigation/bottom_nav_back_handler.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

/// Simple test notification utility for the home screen
class _TestNotifications {
  static final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();

  static Future<void> _ensureInitialized() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings();
    const settings = InitializationSettings(android: androidSettings, iOS: iosSettings);

    await _notifications.initialize(settings);
  }

  static Future<void> sendTestOrderNotification() async {
    await _ensureInitialized();
    await _notifications.show(
      1,
      'Order Update',
      'Your order #12345 has been confirmed and is being prepared!',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'order_channel',
          'Order Notifications',
          channelDescription: 'Order status updates',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(),
      ),
    );
  }

  static Future<void> sendTestDeliveryNotification() async {
    await _ensureInitialized();
    await _notifications.show(
      2,
      'Delivery Update',
      'Your order is out for delivery and will arrive in 15 minutes!',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'delivery_channel',
          'Delivery Notifications',
          channelDescription: 'Delivery status updates',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(),
      ),
    );
  }

  static Future<void> sendTestHoroscopeNotification() async {
    await _ensureInitialized();
    await _notifications.show(
      3,
      'Daily Horoscope',
      'Your stars are aligned today! Check your horoscope for good fortune.',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'horoscope_channel',
          'Daily Horoscope',
          channelDescription: 'Daily horoscope notifications',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
        ),
        iOS: DarwinNotificationDetails(),
      ),
    );
  }

  static Future<void> sendTestHealthcareNotification() async {
    await _ensureInitialized();
    await _notifications.show(
      4,
      'Healthcare Reminder',
      'Don\'t forget your appointment with Dr. Smith tomorrow at 2:00 PM.',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'healthcare_channel',
          'Healthcare Reminders',
          channelDescription: 'Healthcare appointment reminders',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(),
      ),
    );
  }

  static Future<void> sendAllTestNotifications() async {
    await sendTestOrderNotification();
    await Future.delayed(const Duration(seconds: 1));
    await sendTestDeliveryNotification();
    await Future.delayed(const Duration(seconds: 1));
    await sendTestHoroscopeNotification();
    await Future.delayed(const Duration(seconds: 1));
    await sendTestHealthcareNotification();
  }
}

class ModernHomeScreen extends ConsumerStatefulWidget {
  const ModernHomeScreen({super.key});

  @override
  ConsumerState<ModernHomeScreen> createState() => _ModernHomeScreenState();
}

class _ModernHomeScreenState extends ConsumerState<ModernHomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load products when the screen initializes
    Future.microtask(() {
      ref.read(productsProvider.notifier).getProducts();
    });
  }

  @override
  Widget build(BuildContext context) {
    // Add debug print to confirm home screen is being built
    debugPrint('Building ModernHomeScreen widget');

    // Add auto-refresh functionality using the AutoRefresh utility
    AutoRefresh.addAutoRefresh(
      ref: ref,
      callback: () {
        // Silently refresh products
        ref.read(productsProvider.notifier).getProducts(silent: true);
      },
      logRefreshes: true,
    );

    final productsAsync = ref.watch(productsProvider);
    final theme = Theme.of(context);

    return BottomNavBackHandler(
      child: RefreshableScreen(
        // Add auto-refresh functionality
        onRefresh: () {
          // Silently refresh products
          ref.read(productsProvider.notifier).getProducts(silent: true);
        },
        // Don't show debug info with time display
        showDebugInfo: false,
        child: Scaffold(
          extendBodyBehindAppBar: true,
          backgroundColor: theme.colorScheme.surface,
          appBar: const ModernTransparentAppBar(
            showBackButton: false,
          ),
          drawer: const BuyerDrawer(),
          body: RefreshIndicator(
            onRefresh: () async {
              await ref.read(productsProvider.notifier).getProducts();
            },
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // Hero section with banner carousel
                const SliverToBoxAdapter(
                  child: ModernHeroSection(),
                ),

                // Category section
                const SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.only(top: 24),
                    child: ModernCategorySection(),
                  ),
                ),

                // Featured products section
                SliverToBoxAdapter(
                  child: ModernSectionHeader(
                    title: 'Featured Products',
                    actionText: 'See All',
                    onActionTap: () => context.push(BuyerRoutes.categories),
                  ),
                ),

                // Products grid
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  sliver: productsAsync.when(
                    data: (products) {
                      if (products.isEmpty) {
                        return const SliverToBoxAdapter(
                          child: Center(
                            child: Text('No products available'),
                          ),
                        );
                      }

                      // Get featured products (first 4 or less)
                      final featuredProducts = products.take(4).toList();

                      return ModernProductGrid(
                        products: featuredProducts,
                        crossAxisCount: 2,
                        childAspectRatio: 0.75, // Increased from 0.7 to make cards slightly less tall
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                      );
                    },
                    loading: () => const SliverToBoxAdapter(
                      child: Padding(
                        padding: EdgeInsets.all(32.0),
                        child: Center(child: LoadingIndicator()),
                      ),
                    ),
                    error: (error, stackTrace) => SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(32.0),
                        child: ErrorView(
                          message: error.toString(),
                          onRetry: () =>
                              ref.read(productsProvider.notifier).getProducts(),
                        ),
                      ),
                    ),
                  ),
                ),

                // Recommended products section
                SliverToBoxAdapter(
                  child: ModernSectionHeader(
                    title: 'Recommended For You',
                    actionText: 'See All',
                    onActionTap: () => context.push(BuyerRoutes.categories),
                  ),
                ),

                // Recommended products grid
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  sliver: productsAsync.when(
                    data: (products) {
                      if (products.isEmpty) {
                        return const SliverToBoxAdapter(
                          child: Center(
                            child: Text('No products available'),
                          ),
                        );
                      }

                      // Get recommended products (next 4 after featured or random if not enough)
                      final recommendedProducts = products.length > 4
                          ? products.skip(4).take(4).toList()
                          : products;

                      return ModernProductGrid(
                        products: recommendedProducts,
                        crossAxisCount: 2,
                        childAspectRatio: 0.75, // Increased from 0.7 to make cards slightly less tall
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                      );
                    },
                    loading: () => const SliverToBoxAdapter(
                      child: Padding(
                        padding: EdgeInsets.all(32.0),
                        child: Center(child: LoadingIndicator()),
                      ),
                    ),
                    error: (error, stackTrace) => SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(32.0),
                        child: ErrorView(
                          message: error.toString(),
                          onRetry: () =>
                              ref.read(productsProvider.notifier).getProducts(),
                        ),
                      ),
                    ),
                  ),
                ),
                // Bottom padding
                const SliverToBoxAdapter(
                  child: SizedBox(height: 100),
                ),
              ],
            ),
          ),
          // Add floating action button to test notifications (debug mode only)
          floatingActionButton: kDebugMode ? FloatingActionButton(
            onPressed: () async {
              // Show a dialog to choose which notification to test
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Test Notifications'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ListTile(
                        leading: const Icon(Icons.shopping_cart),
                        title: const Text('Order Update'),
                        onTap: () {
                          Navigator.pop(context);
                          _TestNotifications.sendTestOrderNotification();
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.local_shipping),
                        title: const Text('Delivery Update'),
                        onTap: () {
                          Navigator.pop(context);
                          _TestNotifications.sendTestDeliveryNotification();
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.star),
                        title: const Text('Daily Horoscope'),
                        onTap: () {
                          Navigator.pop(context);
                          _TestNotifications.sendTestHoroscopeNotification();
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.medical_services),
                        title: const Text('Healthcare Reminder'),
                        onTap: () {
                          Navigator.pop(context);
                          _TestNotifications.sendTestHealthcareNotification();
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.notifications),
                        title: const Text('All Notifications'),
                        onTap: () {
                          Navigator.pop(context);
                          _TestNotifications.sendAllTestNotifications();
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
            tooltip: 'Test Notifications',
            child: const Icon(Icons.notifications),
          ) : null,
        ),
      ),
    );
  }
}

