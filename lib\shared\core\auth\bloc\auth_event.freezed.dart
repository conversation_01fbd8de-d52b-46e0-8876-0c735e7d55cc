// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthEventCopyWith<$Res> {
  factory $AuthEventCopyWith(AuthEvent value, $Res Function(AuthEvent) then) =
      _$AuthEventCopyWithImpl<$Res, AuthEvent>;
}

/// @nodoc
class _$AuthEventCopyWithImpl<$Res, $Val extends AuthEvent>
    implements $AuthEventCopyWith<$Res> {
  _$AuthEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CheckAuthStatusImplCopyWith<$Res> {
  factory _$$CheckAuthStatusImplCopyWith(_$CheckAuthStatusImpl value,
          $Res Function(_$CheckAuthStatusImpl) then) =
      __$$CheckAuthStatusImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckAuthStatusImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$CheckAuthStatusImpl>
    implements _$$CheckAuthStatusImplCopyWith<$Res> {
  __$$CheckAuthStatusImplCopyWithImpl(
      _$CheckAuthStatusImpl _value, $Res Function(_$CheckAuthStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckAuthStatusImpl implements CheckAuthStatus {
  const _$CheckAuthStatusImpl();

  @override
  String toString() {
    return 'AuthEvent.checkAuthStatus()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckAuthStatusImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) {
    return checkAuthStatus();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) {
    return checkAuthStatus?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (checkAuthStatus != null) {
      return checkAuthStatus();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) {
    return checkAuthStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) {
    return checkAuthStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (checkAuthStatus != null) {
      return checkAuthStatus(this);
    }
    return orElse();
  }
}

abstract class CheckAuthStatus implements AuthEvent {
  const factory CheckAuthStatus() = _$CheckAuthStatusImpl;
}

/// @nodoc
abstract class _$$SignInWithEmailAndPasswordImplCopyWith<$Res> {
  factory _$$SignInWithEmailAndPasswordImplCopyWith(
          _$SignInWithEmailAndPasswordImpl value,
          $Res Function(_$SignInWithEmailAndPasswordImpl) then) =
      __$$SignInWithEmailAndPasswordImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LoginRequest request});

  $LoginRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$SignInWithEmailAndPasswordImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SignInWithEmailAndPasswordImpl>
    implements _$$SignInWithEmailAndPasswordImplCopyWith<$Res> {
  __$$SignInWithEmailAndPasswordImplCopyWithImpl(
      _$SignInWithEmailAndPasswordImpl _value,
      $Res Function(_$SignInWithEmailAndPasswordImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$SignInWithEmailAndPasswordImpl(
      null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as LoginRequest,
    ));
  }

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LoginRequestCopyWith<$Res> get request {
    return $LoginRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$SignInWithEmailAndPasswordImpl implements SignInWithEmailAndPassword {
  const _$SignInWithEmailAndPasswordImpl(this.request);

  @override
  final LoginRequest request;

  @override
  String toString() {
    return 'AuthEvent.signInWithEmailAndPassword(request: $request)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignInWithEmailAndPasswordImpl &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SignInWithEmailAndPasswordImplCopyWith<_$SignInWithEmailAndPasswordImpl>
      get copyWith => __$$SignInWithEmailAndPasswordImplCopyWithImpl<
          _$SignInWithEmailAndPasswordImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) {
    return signInWithEmailAndPassword(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) {
    return signInWithEmailAndPassword?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (signInWithEmailAndPassword != null) {
      return signInWithEmailAndPassword(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) {
    return signInWithEmailAndPassword(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) {
    return signInWithEmailAndPassword?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (signInWithEmailAndPassword != null) {
      return signInWithEmailAndPassword(this);
    }
    return orElse();
  }
}

abstract class SignInWithEmailAndPassword implements AuthEvent {
  const factory SignInWithEmailAndPassword(final LoginRequest request) =
      _$SignInWithEmailAndPasswordImpl;

  LoginRequest get request;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SignInWithEmailAndPasswordImplCopyWith<_$SignInWithEmailAndPasswordImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SignInWithGoogleImplCopyWith<$Res> {
  factory _$$SignInWithGoogleImplCopyWith(_$SignInWithGoogleImpl value,
          $Res Function(_$SignInWithGoogleImpl) then) =
      __$$SignInWithGoogleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SignInWithGoogleImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SignInWithGoogleImpl>
    implements _$$SignInWithGoogleImplCopyWith<$Res> {
  __$$SignInWithGoogleImplCopyWithImpl(_$SignInWithGoogleImpl _value,
      $Res Function(_$SignInWithGoogleImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SignInWithGoogleImpl implements SignInWithGoogle {
  const _$SignInWithGoogleImpl();

  @override
  String toString() {
    return 'AuthEvent.signInWithGoogle()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SignInWithGoogleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) {
    return signInWithGoogle();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) {
    return signInWithGoogle?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (signInWithGoogle != null) {
      return signInWithGoogle();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) {
    return signInWithGoogle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) {
    return signInWithGoogle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (signInWithGoogle != null) {
      return signInWithGoogle(this);
    }
    return orElse();
  }
}

abstract class SignInWithGoogle implements AuthEvent {
  const factory SignInWithGoogle() = _$SignInWithGoogleImpl;
}

/// @nodoc
abstract class _$$SignInWithAppleImplCopyWith<$Res> {
  factory _$$SignInWithAppleImplCopyWith(_$SignInWithAppleImpl value,
          $Res Function(_$SignInWithAppleImpl) then) =
      __$$SignInWithAppleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SignInWithAppleImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SignInWithAppleImpl>
    implements _$$SignInWithAppleImplCopyWith<$Res> {
  __$$SignInWithAppleImplCopyWithImpl(
      _$SignInWithAppleImpl _value, $Res Function(_$SignInWithAppleImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SignInWithAppleImpl implements SignInWithApple {
  const _$SignInWithAppleImpl();

  @override
  String toString() {
    return 'AuthEvent.signInWithApple()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SignInWithAppleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) {
    return signInWithApple();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) {
    return signInWithApple?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (signInWithApple != null) {
      return signInWithApple();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) {
    return signInWithApple(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) {
    return signInWithApple?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (signInWithApple != null) {
      return signInWithApple(this);
    }
    return orElse();
  }
}

abstract class SignInWithApple implements AuthEvent {
  const factory SignInWithApple() = _$SignInWithAppleImpl;
}

/// @nodoc
abstract class _$$RegisterImplCopyWith<$Res> {
  factory _$$RegisterImplCopyWith(
          _$RegisterImpl value, $Res Function(_$RegisterImpl) then) =
      __$$RegisterImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RegisterRequest request});

  $RegisterRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$RegisterImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$RegisterImpl>
    implements _$$RegisterImplCopyWith<$Res> {
  __$$RegisterImplCopyWithImpl(
      _$RegisterImpl _value, $Res Function(_$RegisterImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$RegisterImpl(
      null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RegisterRequest,
    ));
  }

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RegisterRequestCopyWith<$Res> get request {
    return $RegisterRequestCopyWith<$Res>(_value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$RegisterImpl implements Register {
  const _$RegisterImpl(this.request);

  @override
  final RegisterRequest request;

  @override
  String toString() {
    return 'AuthEvent.register(request: $request)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterImpl &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterImplCopyWith<_$RegisterImpl> get copyWith =>
      __$$RegisterImplCopyWithImpl<_$RegisterImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) {
    return register(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) {
    return register?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (register != null) {
      return register(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) {
    return register(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) {
    return register?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (register != null) {
      return register(this);
    }
    return orElse();
  }
}

abstract class Register implements AuthEvent {
  const factory Register(final RegisterRequest request) = _$RegisterImpl;

  RegisterRequest get request;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegisterImplCopyWith<_$RegisterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SignOutImplCopyWith<$Res> {
  factory _$$SignOutImplCopyWith(
          _$SignOutImpl value, $Res Function(_$SignOutImpl) then) =
      __$$SignOutImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SignOutImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SignOutImpl>
    implements _$$SignOutImplCopyWith<$Res> {
  __$$SignOutImplCopyWithImpl(
      _$SignOutImpl _value, $Res Function(_$SignOutImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SignOutImpl implements SignOut {
  const _$SignOutImpl();

  @override
  String toString() {
    return 'AuthEvent.signOut()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SignOutImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) {
    return signOut();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) {
    return signOut?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (signOut != null) {
      return signOut();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) {
    return signOut(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) {
    return signOut?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (signOut != null) {
      return signOut(this);
    }
    return orElse();
  }
}

abstract class SignOut implements AuthEvent {
  const factory SignOut() = _$SignOutImpl;
}

/// @nodoc
abstract class _$$ResetPasswordImplCopyWith<$Res> {
  factory _$$ResetPasswordImplCopyWith(
          _$ResetPasswordImpl value, $Res Function(_$ResetPasswordImpl) then) =
      __$$ResetPasswordImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String email});
}

/// @nodoc
class __$$ResetPasswordImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$ResetPasswordImpl>
    implements _$$ResetPasswordImplCopyWith<$Res> {
  __$$ResetPasswordImplCopyWithImpl(
      _$ResetPasswordImpl _value, $Res Function(_$ResetPasswordImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
  }) {
    return _then(_$ResetPasswordImpl(
      null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ResetPasswordImpl implements ResetPassword {
  const _$ResetPasswordImpl(this.email);

  @override
  final String email;

  @override
  String toString() {
    return 'AuthEvent.resetPassword(email: $email)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResetPasswordImpl &&
            (identical(other.email, email) || other.email == email));
  }

  @override
  int get hashCode => Object.hash(runtimeType, email);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResetPasswordImplCopyWith<_$ResetPasswordImpl> get copyWith =>
      __$$ResetPasswordImplCopyWithImpl<_$ResetPasswordImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) {
    return resetPassword(email);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) {
    return resetPassword?.call(email);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (resetPassword != null) {
      return resetPassword(email);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) {
    return resetPassword(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) {
    return resetPassword?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (resetPassword != null) {
      return resetPassword(this);
    }
    return orElse();
  }
}

abstract class ResetPassword implements AuthEvent {
  const factory ResetPassword(final String email) = _$ResetPasswordImpl;

  String get email;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResetPasswordImplCopyWith<_$ResetPasswordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateProfileImplCopyWith<$Res> {
  factory _$$UpdateProfileImplCopyWith(
          _$UpdateProfileImpl value, $Res Function(_$UpdateProfileImpl) then) =
      __$$UpdateProfileImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? displayName, String? photoUrl, String? phoneNumber});
}

/// @nodoc
class __$$UpdateProfileImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$UpdateProfileImpl>
    implements _$$UpdateProfileImplCopyWith<$Res> {
  __$$UpdateProfileImplCopyWithImpl(
      _$UpdateProfileImpl _value, $Res Function(_$UpdateProfileImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? displayName = freezed,
    Object? photoUrl = freezed,
    Object? phoneNumber = freezed,
  }) {
    return _then(_$UpdateProfileImpl(
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$UpdateProfileImpl implements UpdateProfile {
  const _$UpdateProfileImpl(
      {this.displayName, this.photoUrl, this.phoneNumber});

  @override
  final String? displayName;
  @override
  final String? photoUrl;
  @override
  final String? phoneNumber;

  @override
  String toString() {
    return 'AuthEvent.updateProfile(displayName: $displayName, photoUrl: $photoUrl, phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateProfileImpl &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, displayName, photoUrl, phoneNumber);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateProfileImplCopyWith<_$UpdateProfileImpl> get copyWith =>
      __$$UpdateProfileImplCopyWithImpl<_$UpdateProfileImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) {
    return updateProfile(displayName, photoUrl, phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) {
    return updateProfile?.call(displayName, photoUrl, phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (updateProfile != null) {
      return updateProfile(displayName, photoUrl, phoneNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) {
    return updateProfile(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) {
    return updateProfile?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (updateProfile != null) {
      return updateProfile(this);
    }
    return orElse();
  }
}

abstract class UpdateProfile implements AuthEvent {
  const factory UpdateProfile(
      {final String? displayName,
      final String? photoUrl,
      final String? phoneNumber}) = _$UpdateProfileImpl;

  String? get displayName;
  String? get photoUrl;
  String? get phoneNumber;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateProfileImplCopyWith<_$UpdateProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteAccountImplCopyWith<$Res> {
  factory _$$DeleteAccountImplCopyWith(
          _$DeleteAccountImpl value, $Res Function(_$DeleteAccountImpl) then) =
      __$$DeleteAccountImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteAccountImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$DeleteAccountImpl>
    implements _$$DeleteAccountImplCopyWith<$Res> {
  __$$DeleteAccountImplCopyWithImpl(
      _$DeleteAccountImpl _value, $Res Function(_$DeleteAccountImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteAccountImpl implements DeleteAccount {
  const _$DeleteAccountImpl();

  @override
  String toString() {
    return 'AuthEvent.deleteAccount()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DeleteAccountImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) {
    return deleteAccount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) {
    return deleteAccount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (deleteAccount != null) {
      return deleteAccount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) {
    return deleteAccount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) {
    return deleteAccount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (deleteAccount != null) {
      return deleteAccount(this);
    }
    return orElse();
  }
}

abstract class DeleteAccount implements AuthEvent {
  const factory DeleteAccount() = _$DeleteAccountImpl;
}

/// @nodoc
abstract class _$$VerifyPhoneImplCopyWith<$Res> {
  factory _$$VerifyPhoneImplCopyWith(
          _$VerifyPhoneImpl value, $Res Function(_$VerifyPhoneImpl) then) =
      __$$VerifyPhoneImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String phoneNumber});
}

/// @nodoc
class __$$VerifyPhoneImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$VerifyPhoneImpl>
    implements _$$VerifyPhoneImplCopyWith<$Res> {
  __$$VerifyPhoneImplCopyWithImpl(
      _$VerifyPhoneImpl _value, $Res Function(_$VerifyPhoneImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
  }) {
    return _then(_$VerifyPhoneImpl(
      null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$VerifyPhoneImpl implements VerifyPhone {
  const _$VerifyPhoneImpl(this.phoneNumber);

  @override
  final String phoneNumber;

  @override
  String toString() {
    return 'AuthEvent.verifyPhone(phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyPhoneImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyPhoneImplCopyWith<_$VerifyPhoneImpl> get copyWith =>
      __$$VerifyPhoneImplCopyWithImpl<_$VerifyPhoneImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) {
    return verifyPhone(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) {
    return verifyPhone?.call(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (verifyPhone != null) {
      return verifyPhone(phoneNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) {
    return verifyPhone(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) {
    return verifyPhone?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (verifyPhone != null) {
      return verifyPhone(this);
    }
    return orElse();
  }
}

abstract class VerifyPhone implements AuthEvent {
  const factory VerifyPhone(final String phoneNumber) = _$VerifyPhoneImpl;

  String get phoneNumber;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifyPhoneImplCopyWith<_$VerifyPhoneImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$VerifyPhoneCodeImplCopyWith<$Res> {
  factory _$$VerifyPhoneCodeImplCopyWith(_$VerifyPhoneCodeImpl value,
          $Res Function(_$VerifyPhoneCodeImpl) then) =
      __$$VerifyPhoneCodeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String code});
}

/// @nodoc
class __$$VerifyPhoneCodeImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$VerifyPhoneCodeImpl>
    implements _$$VerifyPhoneCodeImplCopyWith<$Res> {
  __$$VerifyPhoneCodeImplCopyWithImpl(
      _$VerifyPhoneCodeImpl _value, $Res Function(_$VerifyPhoneCodeImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
  }) {
    return _then(_$VerifyPhoneCodeImpl(
      null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$VerifyPhoneCodeImpl implements VerifyPhoneCode {
  const _$VerifyPhoneCodeImpl(this.code);

  @override
  final String code;

  @override
  String toString() {
    return 'AuthEvent.verifyPhoneCode(code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyPhoneCodeImpl &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, code);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyPhoneCodeImplCopyWith<_$VerifyPhoneCodeImpl> get copyWith =>
      __$$VerifyPhoneCodeImplCopyWithImpl<_$VerifyPhoneCodeImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkAuthStatus,
    required TResult Function(LoginRequest request) signInWithEmailAndPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(RegisterRequest request) register,
    required TResult Function() signOut,
    required TResult Function(String email) resetPassword,
    required TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)
        updateProfile,
    required TResult Function() deleteAccount,
    required TResult Function(String phoneNumber) verifyPhone,
    required TResult Function(String code) verifyPhoneCode,
  }) {
    return verifyPhoneCode(code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkAuthStatus,
    TResult? Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(RegisterRequest request)? register,
    TResult? Function()? signOut,
    TResult? Function(String email)? resetPassword,
    TResult? Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult? Function()? deleteAccount,
    TResult? Function(String phoneNumber)? verifyPhone,
    TResult? Function(String code)? verifyPhoneCode,
  }) {
    return verifyPhoneCode?.call(code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkAuthStatus,
    TResult Function(LoginRequest request)? signInWithEmailAndPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(RegisterRequest request)? register,
    TResult Function()? signOut,
    TResult Function(String email)? resetPassword,
    TResult Function(
            String? displayName, String? photoUrl, String? phoneNumber)?
        updateProfile,
    TResult Function()? deleteAccount,
    TResult Function(String phoneNumber)? verifyPhone,
    TResult Function(String code)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (verifyPhoneCode != null) {
      return verifyPhoneCode(code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CheckAuthStatus value) checkAuthStatus,
    required TResult Function(SignInWithEmailAndPassword value)
        signInWithEmailAndPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(Register value) register,
    required TResult Function(SignOut value) signOut,
    required TResult Function(ResetPassword value) resetPassword,
    required TResult Function(UpdateProfile value) updateProfile,
    required TResult Function(DeleteAccount value) deleteAccount,
    required TResult Function(VerifyPhone value) verifyPhone,
    required TResult Function(VerifyPhoneCode value) verifyPhoneCode,
  }) {
    return verifyPhoneCode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CheckAuthStatus value)? checkAuthStatus,
    TResult? Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(Register value)? register,
    TResult? Function(SignOut value)? signOut,
    TResult? Function(ResetPassword value)? resetPassword,
    TResult? Function(UpdateProfile value)? updateProfile,
    TResult? Function(DeleteAccount value)? deleteAccount,
    TResult? Function(VerifyPhone value)? verifyPhone,
    TResult? Function(VerifyPhoneCode value)? verifyPhoneCode,
  }) {
    return verifyPhoneCode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CheckAuthStatus value)? checkAuthStatus,
    TResult Function(SignInWithEmailAndPassword value)?
        signInWithEmailAndPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(Register value)? register,
    TResult Function(SignOut value)? signOut,
    TResult Function(ResetPassword value)? resetPassword,
    TResult Function(UpdateProfile value)? updateProfile,
    TResult Function(DeleteAccount value)? deleteAccount,
    TResult Function(VerifyPhone value)? verifyPhone,
    TResult Function(VerifyPhoneCode value)? verifyPhoneCode,
    required TResult orElse(),
  }) {
    if (verifyPhoneCode != null) {
      return verifyPhoneCode(this);
    }
    return orElse();
  }
}

abstract class VerifyPhoneCode implements AuthEvent {
  const factory VerifyPhoneCode(final String code) = _$VerifyPhoneCodeImpl;

  String get code;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifyPhoneCodeImplCopyWith<_$VerifyPhoneCodeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
