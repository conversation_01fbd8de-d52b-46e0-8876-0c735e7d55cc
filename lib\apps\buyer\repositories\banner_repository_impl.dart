import '../domain/entities/banner/banner_model.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';
import 'banner_repository.dart';

/// Hybrid storage implementation of [BannerRepository]
class BannerRepositoryImpl implements BannerRepository {
  /// Creates a [BannerRepositoryImpl]
  BannerRepositoryImpl({
    DatabaseService? databaseService,
  }) : _databaseService = databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment());

  final DatabaseService _databaseService;
  final _logger = getLogger('BannerRepositoryImpl');
  final String _collection = 'banners';

  @override
  Future<List<BannerModel>> getActiveBanners() async {
    try {
      // Get all banners and filter in memory
      final allBanners = await _databaseService.getAll(_collection);
      final now = DateTime.now();

      final activeBanners = allBanners.where((bannerData) {
        final isActive = bannerData['isActive'] == true;

        if (!isActive) return false;

        final startDateStr = bannerData['startDate'] as String?;
        final endDateStr = bannerData['endDate'] as String?;

        if (startDateStr == null || endDateStr == null) return false;

        final startDate = DateTime.tryParse(startDateStr);
        final endDate = DateTime.tryParse(endDateStr);

        return startDate != null &&
               endDate != null &&
               now.isAfter(startDate) &&
               now.isBefore(endDate);
      }).toList();

      // Sort by priority (descending)
      activeBanners.sort((a, b) {
        final priorityA = a['priority'] as int? ?? 0;
        final priorityB = b['priority'] as int? ?? 0;
        return priorityB.compareTo(priorityA);
      });

      return activeBanners
          .map((bannerData) => BannerModel.fromJson(bannerData))
          .toList();
    } catch (e) {
      _logger.severe('Error getting active banners: $e');
      return [];
    }
  }

  @override
  Future<BannerModel?> getBannerById(String id) async {
    try {
      final bannerData = await _databaseService.find(_collection, id);
      if (bannerData == null) return null;

      return BannerModel.fromJson(bannerData);
    } catch (e) {
      _logger.severe('Error getting banner by ID: $e');
      return null;
    }
  }

  @override
  Future<List<BannerModel>> getBannersByActionType(String actionType) async {
    try {
      // Get all active banners first
      final activeBanners = await getActiveBanners();

      // Filter by action type
      return activeBanners
          .where((banner) => banner.actionType == actionType)
          .toList();
    } catch (e) {
      _logger.severe('Error getting banners by action type: $e');
      return [];
    }
  }

  @override
  Future<List<BannerModel>> getBannersByPriorityRange({
    required int minPriority,
    required int maxPriority,
  }) async {
    try {
      // Get all active banners first
      final activeBanners = await getActiveBanners();

      // Filter by priority range
      return activeBanners
          .where((banner) =>
              banner.priority >= minPriority &&
              banner.priority <= maxPriority)
          .toList();
    } catch (e) {
      _logger.severe('Error getting banners by priority range: $e');
      return [];
    }
  }
}
