import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../apps/executor/executor_routes.dart';
import '../../auth/bloc/auth_bloc.dart';
import '../../auth/bloc/auth_event.dart';
import '../../auth/bloc/auth_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ExecutorDrawer extends StatelessWidget {
  const ExecutorDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Drawer(
          child: Column(
            children: [
              UserAccountsDrawerHeader(
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                ),
                currentAccountPicture: CircleAvatar(
                  backgroundImage: state.maybeMap(
                    authenticated: (state) => state.user.photoUrl != null
                        ? NetworkImage(state.user.photoUrl!)
                        : null,
                    orElse: () => null,
                  ),
                  child: state.maybeMap(
                    authenticated: (state) => state.user.photoUrl == null
                        ? Text(
                            state.user.displayName.isNotEmpty
                                ? state.user.displayName
                                    .substring(0, 1)
                                    .toUpperCase()
                                : 'E',
                            style: theme.textTheme.headlineMedium?.copyWith(
                              color: theme.colorScheme.onPrimary,
                            ),
                          )
                        : null,
                    orElse: () => const Icon(Icons.person_outline_rounded),
                  ),
                ),
                accountName: Text(
                  state.maybeMap(
                    authenticated: (state) =>
                        state.user.displayName.isNotEmpty ? state.user.displayName : 'Executor',
                    orElse: () => 'Executor',
                  ),
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                accountEmail: Text(
                  state.maybeMap(
                    authenticated: (state) => state.user.email,
                    orElse: () => '',
                  ),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ),
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    _DrawerItem(
                      icon: Icons.home_outlined,
                      label: 'Home',
                      onTap: () => _navigateTo(context, ExecutorRoutes.home),
                    ),
                    _DrawerItem(
                      icon: Icons.inventory_2_outlined,
                      label: 'Products',
                      onTap: () => _navigateTo(
                          context, ExecutorRoutes.productVerification),
                    ),
                    _DrawerItem(
                      icon: Icons.store_outlined,
                      label: 'Sellers',
                      onTap: () => _navigateTo(
                          context, ExecutorRoutes.sellerVerification),
                    ),
                    _DrawerItem(
                      icon: Icons.temple_hindu_outlined,
                      label: 'Priests',
                      onTap: () => _navigateTo(
                          context, ExecutorRoutes.priestVerification),
                    ),
                    _DrawerItem(
                      icon: Icons.handyman_outlined,
                      label: 'Technicians',
                      onTap: () => _navigateTo(
                          context, ExecutorRoutes.technicianVerification),
                    ),
                    _DrawerItem(
                      icon: Icons.event_outlined,
                      label: 'Events',
                      onTap: () =>
                          _navigateTo(context, ExecutorRoutes.eventManagement),
                    ),
                    _DrawerItem(
                      icon: Icons.rate_review_outlined,
                      label: 'Reviews',
                      onTap: () =>
                          _navigateTo(context, ExecutorRoutes.reviewManagement),
                    ),
                    _DrawerItem(
                      icon: Icons.play_circle_outline_rounded,
                      label: 'Media',
                      onTap: () =>
                          _navigateTo(context, ExecutorRoutes.mediaManagement),
                    ),
                    const Divider(),
                    _DrawerItem(
                      icon: Icons.person_outline_rounded,
                      label: 'Profile',
                      onTap: () => _navigateTo(context, ExecutorRoutes.profile),
                    ),
                    const Divider(),
                    _DrawerItem(
                      icon: Icons.logout_rounded,
                      label: 'Sign Out',
                      onTap: () {
                        context.read<AuthBloc>().add(const AuthEvent.signOut());
                        context.go(ExecutorRoutes.login);
                      },
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Version 1.0.0',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _navigateTo(BuildContext context, String route) {
    context.pop();
    context.go(route);
  }
}

class _DrawerItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _DrawerItem({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
      ),
      title: Text(
        label,
        style: theme.textTheme.bodyLarge?.copyWith(
          color: theme.colorScheme.onSurface,
        ),
      ),
      onTap: onTap,
      dense: true,
      horizontalTitleGap: 0,
    );
  }
}
