import 'package:freezed_annotation/freezed_annotation.dart';

part 'schedule_model.freezed.dart';
part 'schedule_model.g.dart';

@freezed
sealed class ScheduleModel with _$ScheduleModel {
  const factory ScheduleModel({
    required String id,
    required bool isOpen24x7,
    required Map<String, DaySchedule> weeklySchedule,
    required List<HolidaySchedule> holidays,
    required String timeZone,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _ScheduleModel;

  factory ScheduleModel.fromJson(Map<String, dynamic> json) =>
      _$ScheduleModelFromJson(json);
}

@freezed
sealed class DaySchedule with _$DaySchedule {
  const factory DaySchedule({
    required bool isOpen,
    required List<TimeSlot> slots,
  }) = _DaySchedule;

  factory DaySchedule.fromJson(Map<String, dynamic> json) =>
      _$DayScheduleFromJson(json);
}

@freezed
sealed class TimeSlot with _$TimeSlot {
  const factory TimeSlot({
    required String openTime, // 24-hour format HH:mm
    required String closeTime, // 24-hour format HH:mm
    String? breakStart, // Optional break time
    String? breakEnd, // Optional break time
  }) = _TimeSlot;

  factory TimeSlot.fromJson(Map<String, dynamic> json) =>
      _$TimeSlotFromJson(json);
}

@freezed
sealed class HolidaySchedule with _$HolidaySchedule {
  const factory HolidaySchedule({
    required String id,
    required String name,
    String? description,
    required DateTime startDate,
    required DateTime endDate,
    @Default(false) bool isRecurring,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _HolidaySchedule;

  factory HolidaySchedule.fromJson(Map<String, dynamic> json) =>
      _$HolidayScheduleFromJson(json);
}
