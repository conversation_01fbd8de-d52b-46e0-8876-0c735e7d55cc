import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/models/technician/technician.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final profileSettingsProvider =
    AsyncNotifierProvider<ProfileSettingsNotifier, Technician?>(() {
  return ProfileSettingsNotifier();
});

class ProfileSettingsNotifier extends AsyncNotifier<Technician?> {
  late final DatabaseService _databaseService;

  @override
  Future<Technician?> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadProfileSettings();
  }

  Future<Technician?> _loadProfileSettings() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('technicians', userId);
      if (userData == null) return null;
      return Technician.fromJson(userData);
    } catch (e) {
      debugPrint('Failed to load profile settings: $e');
      throw Exception('Failed to load profile settings: $e');
    }
  }

  Future<void> updateProfileSettings(Map<String, dynamic> settings) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Validate required fields
      if (settings['name']?.toString().isEmpty ?? true) {
        throw Exception('Name is required');
      }
      if (settings['email']?.toString().isEmpty ?? true) {
        throw Exception('Email is required');
      }

      final updateData = {
        ...settings,
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _databaseService.update('technicians', userId, updateData);

      final updatedProfile = await _loadProfileSettings();
      state = AsyncData(updatedProfile);
    } catch (e) {
      debugPrint('Failed to update profile settings: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> updateProfileImage(String imageUrl) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      await _databaseService.update('technicians', userId, {
        'profile_image': imageUrl,
        'updated_at': DateTime.now().toIso8601String(),
      });

      final updatedProfile = await _loadProfileSettings();
      state = AsyncData(updatedProfile);
    } catch (e) {
      debugPrint('Failed to update profile image: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> updateSpecializations(List<String> specializations) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      if (specializations.isEmpty) {
        throw Exception('At least one specialization is required');
      }

      await _databaseService.update('technicians', userId, {
        'specializations': specializations,
        'updated_at': DateTime.now().toIso8601String(),
      });

      final updatedProfile = await _loadProfileSettings();
      state = AsyncData(updatedProfile);
    } catch (e) {
      debugPrint('Failed to update specializations: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}
