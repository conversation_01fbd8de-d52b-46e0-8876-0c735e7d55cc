import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/database/services/database_service.dart';
import '../../../../shared/database/config/database_config.dart';
import '../../../../shared/utils/logger.dart';
import '../../providers/auth_provider.dart';
import '../../buyer_routes.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _loginIdentifierController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isLoading = false;
  String? _errorMessage;

  // Login identifier type
  String _loginType = 'email'; // Can be 'email', 'phone', or 'userId'

  @override
  void dispose() {
    _loginIdentifierController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _signIn() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final identifier = _loginIdentifierController.text.trim();
      final password = _passwordController.text;

      // Determine login method based on identifier format
      if (_loginType == 'email' || identifier.contains('@')) {
        // Email login
        await ref.read(authStateProvider.notifier).signInWithEmailAndPassword(
              email: identifier,
              password: password,
            );
      } else if (_loginType == 'phone' ||
          RegExp(r'^[0-9+\s-]+$').hasMatch(identifier)) {
        // Phone number login - convert to email format if needed
        final email = await _getEmailFromPhone(identifier);
        if (email != null) {
          await ref.read(authStateProvider.notifier).signInWithEmailAndPassword(
                email: email,
                password: password,
              );
        } else {
          throw Exception('No account found with this phone number');
        }
      } else {
        // User ID login (email without domain)
        final email = '$<EMAIL>';
        await ref.read(authStateProvider.notifier).signInWithEmailAndPassword(
              email: email,
              password: password,
            );
      }

      // Mark welcome screen as shown to prevent it from showing on app restart
      if (mounted) {
        // Use a separate method to handle post-login navigation
        _handleSuccessfulLogin();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleSuccessfulLogin() {
    if (mounted) {
      BuyerRoutes.navigateToHome(context);
    }
  }

  // Helper method to get email from phone number
  Future<String?> _getEmailFromPhone(String phone) async {
    try {
      // Normalize phone number by removing spaces, dashes, etc.
      final normalizedPhone = phone.replaceAll(RegExp(r'[\s-()]'), '');

      // Query hybrid storage to find user with this phone number
      final databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
      final allUsers = await databaseService.getAll('users');

      final matchingUsers = allUsers.where((userData) =>
          userData['phoneNumber'] == normalizedPhone ||
          userData['phone'] == normalizedPhone
      ).toList();

      if (matchingUsers.isNotEmpty) {
        return matchingUsers.first['email'] as String?;
      }
      return null;
    } catch (e) {
      final logger = getLogger('LoginScreen');
      logger.severe('Error getting email from phone: $e');
      debugPrint('Error getting email from phone: $e');
      return null;
    }
  }

  // Auto-detection of login type is now handled in the TextFormField's onChanged callback

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final authState = ref.watch(authStateProvider);
    final authNotifier = ref.read(authStateProvider.notifier);

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withValues(alpha: 0.1),
              theme.colorScheme.surface,
              theme.colorScheme.primary.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Stack(
              children: [
                // Background design elements
                Positioned(
                  top: -100,
                  right: -100,
                  child: Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          theme.colorScheme.primary.withValues(alpha: 0.2),
                          theme.colorScheme.primary.withValues(alpha: 0.1),
                        ],
                      ),
                    ),
                  ),
                ),
                Positioned(
                  bottom: -120,
                  left: -60,
                  child: Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          theme.colorScheme.secondary.withValues(alpha: 0.2),
                          theme.colorScheme.secondary.withValues(alpha: 0.1),
                        ],
                      ),
                    ),
                  ),
                ),
                // Main content
                Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Logo and Title
                        SizedBox(height: size.height * 0.1),
                        Center(
                          child: Image.asset(
                            'assets/images/flavors/shivish.png',
                            width: 120,
                            height: 120,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                Icons.shopping_bag,
                                size: 80,
                                color: theme.colorScheme.primary,
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 24),
                        Text(
                          'Welcome to SHIVISH',
                          style: theme.textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Sign in to continue shopping',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(179),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 32),

                        // Login Identifier Field
                        TextFormField(
                          controller: _loginIdentifierController,
                          keyboardType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                          decoration: InputDecoration(
                            labelText: 'Email / Phone / User ID',
                            hintText: 'Enter your email, phone, or user ID',
                            prefixIcon: const Icon(Icons.person_outline),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: theme.colorScheme.outline.withAlpha(128),
                              ),
                            ),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your login identifier';
                            }
                            return null;
                          },
                          onChanged: (value) {
                            // Automatically detect the login type based on input
                            if (value.contains('@')) {
                              setState(() {
                                _loginType = 'email';
                              });
                            } else if (RegExp(r'^[0-9+\s-]+$')
                                .hasMatch(value)) {
                              setState(() {
                                _loginType = 'phone';
                              });
                            } else {
                              setState(() {
                                _loginType = 'userId';
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 16),

                        // Password Field
                        TextFormField(
                          controller: _passwordController,
                          obscureText: !_isPasswordVisible,
                          textInputAction: TextInputAction.done,
                          decoration: InputDecoration(
                            labelText: 'Password',
                            prefixIcon: const Icon(Icons.lock_outline),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _isPasswordVisible
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                              ),
                              onPressed: () {
                                setState(() {
                                  _isPasswordVisible = !_isPasswordVisible;
                                });
                              },
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: theme.colorScheme.outline.withAlpha(128),
                              ),
                            ),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your password';
                            }
                            if (value.length < 6) {
                              return 'Password must be at least 6 characters';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // Forgot Password
                        Align(
                          alignment: Alignment.centerRight,
                          child: TextButton(
                            onPressed: () {
                              context.push(BuyerRoutes.forgotPassword);
                            },
                            child: Text(
                              'Forgot Password?',
                              style: TextStyle(
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Sign In Button
                        ElevatedButton(
                          onPressed: _isLoading ? null : _signIn,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : const Text(
                                  'Sign In',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                        const SizedBox(height: 24),

                        // Error Message
                        if (_errorMessage != null)
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.error.withAlpha(26),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              _errorMessage!,
                              style: TextStyle(
                                color: theme.colorScheme.error,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        const SizedBox(height: 24),

                        // Sign Up Link
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Don\'t have an account?',
                              style: theme.textTheme.bodyMedium,
                            ),
                            TextButton(
                              onPressed: () {
                                context.push(BuyerRoutes.register);
                              },
                              child: Text(
                                'Sign Up',
                                style: TextStyle(
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),

                        if (authState.isBiometricAvailable)
                          OutlinedButton.icon(
                            onPressed: _isLoading
                                ? null
                                : (authState.isBiometricEnabled
                                    ? () async {
                                        debugPrint(
                                            'Login screen: Attempting biometric sign in');
                                        try {
                                          await authNotifier
                                              .signInWithBiometrics();
                                          if (authState.error != null) {
                                            setState(() {
                                              _errorMessage = authState.error;
                                            });
                                          }
                                        } catch (e) {
                                          setState(() {
                                            _errorMessage = e.toString();
                                          });
                                        }
                                      }
                                    : () async {
                                        debugPrint(
                                            'Login screen: Enabling biometric auth');
                                        try {
                                          await authNotifier
                                              .toggleBiometricAuth(true);
                                          if (authState.error != null) {
                                            setState(() {
                                              _errorMessage = authState.error;
                                            });
                                          }
                                        } catch (e) {
                                          setState(() {
                                            _errorMessage = e.toString();
                                          });
                                        }
                                      }),
                            icon: const Icon(Icons.fingerprint),
                            label: Text(
                              authState.isBiometricEnabled
                                  ? 'Sign in with Biometrics'
                                  : 'Enable Biometric Authentication',
                            ),
                          ),
                        const SizedBox(height: 16),
                        OutlinedButton.icon(
                          onPressed: _isLoading
                              ? null
                              : () async {
                                  debugPrint(
                                      'Login screen: Attempting Google sign in');
                                  setState(() {
                                    _isLoading = true;
                                    _errorMessage = null;
                                  });
                                  try {
                                    await authNotifier.signInWithGoogle();
                                    // Check if the widget is still mounted after async operation
                                    if (!mounted) return;

                                    // Now we can safely use context and setState
                                    if (authState.error != null) {
                                      setState(() {
                                        _errorMessage = authState.error;
                                        _isLoading = false;
                                      });
                                    } else if (authState.user != null) {
                                      // Successfully signed in, navigate to home
                                      debugPrint(
                                          'Google sign in successful, navigating to home');

                                      // Use the same method for handling successful login
                                      _handleSuccessfulLogin();
                                    } else {
                                      setState(() {
                                        _isLoading = false;
                                      });
                                    }
                                  } catch (e) {
                                    debugPrint('Google sign in error: $e');
                                    setState(() {
                                      _errorMessage = e.toString();
                                      _isLoading = false;
                                    });
                                  }
                                },
                          // Use a better Google icon
                          icon: Icon(
                            Icons.g_translate,
                            size: 20,
                            color: theme.colorScheme.primary,
                          ),
                          label: const Text('Sign in with Google'),
                        ),
                        const SizedBox(height: 16),
                        TextButton(
                          onPressed: () {
                            // Create temporary controllers for the dialog
                            final otpController = TextEditingController(
                                text: _loginIdentifierController.text);

                            // Default to email tab if input contains @, otherwise phone
                            bool isEmailTab =
                                _loginIdentifierController.text.contains('@');

                            showDialog(
                              context: context,
                              builder: (dialogContext) => StatefulBuilder(
                                builder: (context, setDialogState) =>
                                    AlertDialog(
                                  title: const Text('Sign in with OTP'),
                                  content: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      // Tab selector
                                      Row(
                                        children: [
                                          Expanded(
                                            child: InkWell(
                                              onTap: () {
                                                setDialogState(() {
                                                  isEmailTab = true;
                                                });
                                              },
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        vertical: 10),
                                                decoration: BoxDecoration(
                                                  border: Border(
                                                    bottom: BorderSide(
                                                      color: isEmailTab
                                                          ? theme.colorScheme
                                                              .primary
                                                          : Colors
                                                              .grey.shade300,
                                                      width: 2,
                                                    ),
                                                  ),
                                                ),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons.email_outlined,
                                                      color: isEmailTab
                                                          ? theme.colorScheme
                                                              .primary
                                                          : Colors.grey,
                                                      size: 20,
                                                    ),
                                                    const SizedBox(width: 8),
                                                    Text(
                                                      'Email',
                                                      style: TextStyle(
                                                        color: isEmailTab
                                                            ? theme.colorScheme
                                                                .primary
                                                            : Colors.grey,
                                                        fontWeight: isEmailTab
                                                            ? FontWeight.bold
                                                            : FontWeight.normal,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: InkWell(
                                              onTap: () {
                                                setDialogState(() {
                                                  isEmailTab = false;
                                                });
                                              },
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        vertical: 10),
                                                decoration: BoxDecoration(
                                                  border: Border(
                                                    bottom: BorderSide(
                                                      color: !isEmailTab
                                                          ? theme.colorScheme
                                                              .primary
                                                          : Colors
                                                              .grey.shade300,
                                                      width: 2,
                                                    ),
                                                  ),
                                                ),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons.phone_outlined,
                                                      color: !isEmailTab
                                                          ? theme.colorScheme
                                                              .primary
                                                          : Colors.grey,
                                                      size: 20,
                                                    ),
                                                    const SizedBox(width: 8),
                                                    Text(
                                                      'Phone',
                                                      style: TextStyle(
                                                        color: !isEmailTab
                                                            ? theme.colorScheme
                                                                .primary
                                                            : Colors.grey,
                                                        fontWeight: !isEmailTab
                                                            ? FontWeight.bold
                                                            : FontWeight.normal,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 16),
                                      // Input field
                                      TextFormField(
                                        controller: otpController,
                                        decoration: InputDecoration(
                                          labelText: isEmailTab
                                              ? 'Email'
                                              : 'Phone Number',
                                          prefixIcon: Icon(isEmailTab
                                              ? Icons.email_outlined
                                              : Icons.phone_outlined),
                                          hintText: isEmailTab
                                              ? 'Enter your email'
                                              : 'Enter your phone number',
                                        ),
                                        keyboardType: isEmailTab
                                            ? TextInputType.emailAddress
                                            : TextInputType.phone,
                                      ),
                                    ],
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () =>
                                          Navigator.pop(dialogContext),
                                      child: const Text('Cancel'),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        final value = otpController.text.trim();
                                        bool isValid = false;

                                        if (isEmailTab) {
                                          // Validate email
                                          isValid = value.isNotEmpty &&
                                              value.contains('@');
                                        } else {
                                          // Validate phone number
                                          isValid = value.isNotEmpty &&
                                              RegExp(r'^[0-9+\s-]+$')
                                                  .hasMatch(value);
                                        }

                                        if (isValid) {
                                          // Close dialog first
                                          Navigator.pop(dialogContext);

                                          // Show immediate feedback
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                  'Sending OTP to your ${isEmailTab ? 'email' : 'phone'}...'),
                                            ),
                                          );

                                          // Send OTP
                                          if (isEmailTab) {
                                            authNotifier.signInWithOTP(value);
                                          } else {
                                            // For phone, you might need to implement a different method
                                            // or modify the existing one to handle phone numbers
                                            authNotifier
                                                .signInWithPhoneOTP(value);
                                          }
                                        }
                                      },
                                      child: const Text('Send OTP'),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                          child: const Text('Sign in with OTP'),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
