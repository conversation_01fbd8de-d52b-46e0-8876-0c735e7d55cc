import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/models/booking/booking_model.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final bookingServiceProvider = Provider<BookingService>((ref) {
  return BookingService();
});

class BookingService {
  final DatabaseService _databaseService;
  static const String _bookingsCollection = 'bookings';

  BookingService() : _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());

  Future<BookingModel> createBooking(BookingModel booking) async {
    try {
      final bookingData = booking.toJson();
      bookingData['created_at'] = DateTime.now().toIso8601String();
      bookingData['updated_at'] = DateTime.now().toIso8601String();
      bookingData['is_deleted'] = false;

      final result = await _databaseService.create(_bookingsCollection, bookingData);
      return booking.copyWith(id: result['id'] as String);
    } catch (e) {
      debugPrint('Error creating booking: $e');
      rethrow;
    }
  }

  Future<BookingModel?> getBooking(String id) async {
    try {
      final data = await _databaseService.find(_bookingsCollection, id);
      if (data == null) return null;
      return BookingModel.fromJson(data);
    } catch (e) {
      debugPrint('Error getting booking: $e');
      return null;
    }
  }

  Future<void> updateBooking(BookingModel booking) async {
    try {
      final bookingData = booking.toJson();
      bookingData['updated_at'] = DateTime.now().toIso8601String();

      await _databaseService.update(_bookingsCollection, booking.id, bookingData);
    } catch (e) {
      debugPrint('Error updating booking: $e');
      rethrow;
    }
  }

  Future<void> deleteBooking(String id) async {
    try {
      await _databaseService.update(_bookingsCollection, id, {
        'is_deleted': true,
        'deleted_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error deleting booking: $e');
      rethrow;
    }
  }

  Stream<List<BookingModel>> streamTechnicianBookings(String technicianId) {
    try {
      return _databaseService.watchCollection(
        _bookingsCollection,
        where: 'provider_id = ? AND is_deleted = ?',
        whereParams: [technicianId, false],
        orderBy: 'booking_date DESC',
      ).map((docs) => docs
          .map((doc) => BookingModel.fromJson(doc))
          .toList());
    } catch (e) {
      debugPrint('Error streaming technician bookings: $e');
      return Stream.value([]);
    }
  }

  Stream<List<BookingModel>> streamTechnicianBookingsByStatus(
    String technicianId,
    BookingStatus status,
  ) {
    try {
      return _databaseService.watchCollection(
        _bookingsCollection,
        where: 'provider_id = ? AND status = ? AND is_deleted = ?',
        whereParams: [technicianId, status.index, false],
        orderBy: 'booking_date DESC',
      ).map((docs) => docs
          .map((doc) => BookingModel.fromJson(doc))
          .toList());
    } catch (e) {
      debugPrint('Error streaming technician bookings by status: $e');
      return Stream.value([]);
    }
  }

  Future<int> getTechnicianBookingCount(String technicianId) async {
    try {
      return await _databaseService.count(
        _bookingsCollection,
        where: 'provider_id = ? AND is_deleted = ?',
        whereParams: [technicianId, false],
      );
    } catch (e) {
      debugPrint('Error getting technician booking count: $e');
      return 0;
    }
  }

  Future<Map<BookingStatus, int>> getTechnicianBookingCountsByStatus(
    String technicianId,
  ) async {
    try {
      final counts = <BookingStatus, int>{};
      for (final status in BookingStatus.values) {
        final count = await _databaseService.count(
          _bookingsCollection,
          where: 'provider_id = ? AND status = ? AND is_deleted = ?',
          whereParams: [technicianId, status.index, false],
        );
        counts[status] = count;
      }
      return counts;
    } catch (e) {
      debugPrint('Error getting technician booking counts by status: $e');
      return {};
    }
  }
}
