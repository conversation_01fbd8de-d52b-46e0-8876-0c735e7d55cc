import '../../../../shared/models/seller.dart';

class SellerState {
  final bool isLoading;
  final Seller? seller;
  final String? error;
  final bool isAuthenticated;
  final bool isProfileComplete;
  final bool isApproved;

  const SellerState({
    this.isLoading = false,
    this.seller,
    this.error,
    this.isAuthenticated = false,
    this.isProfileComplete = false,
    this.isApproved = false,
  });

  SellerState copyWith({
    bool? isLoading,
    Seller? seller,
    String? error,
    bool? isAuthenticated,
    bool? isProfileComplete,
    bool? isApproved,
  }) {
    return SellerState(
      isLoading: isLoading ?? this.isLoading,
      seller: seller ?? this.seller,
      error: error ?? this.error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      isApproved: isApproved ?? this.isApproved,
    );
  }
}
