import 'package:flutter/material.dart';
import '../widgets/seller_list_item.dart';
import '../../../shared/models/seller.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';

class SellerManagementScreen extends StatefulWidget {
  const SellerManagementScreen({super.key});

  @override
  State<SellerManagementScreen> createState() => _SellerManagementScreenState();
}

class _SellerManagementScreenState extends State<SellerManagementScreen> {
  final _logger = getLogger('SellerManagementScreen');
  final _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
  final _scrollController = ScrollController();
  late Stream<List<Seller>> _sellersStream;
  List<Seller> _sellers = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _initializeDatabase();
  }

  Future<void> _initializeDatabase() async {
    try {
      await _databaseService.initialize();
      _setupSellersStream();
    } catch (e) {
      _logger.severe('Error initializing database: $e');
      setState(() {
        _error = 'Error initializing database: $e';
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _databaseService.close();
    super.dispose();
  }

  void _setupSellersStream() {
    try {
      _sellersStream = _databaseService.watchCollection(
        'sellers',
        where: 'isDeleted = ?',
        whereParams: [false],
        orderBy: 'createdAt DESC',
      ).map((sellerData) {
        _logger.info('Found ${sellerData.length} sellers in collection');

        // Print all sellers for debugging
        for (var data in sellerData) {
          _logger.info('Seller ID: ${data['id']}, Data: $data');
        }

        return sellerData.map((data) {
          try {
            // Parse ISO 8601 timestamps to DateTime
            DateTime? createdAt;
            if (data['createdAt'] is String) {
              createdAt = DateTime.tryParse(data['createdAt']);
            }

            DateTime? updatedAt;
            if (data['updatedAt'] is String) {
              updatedAt = DateTime.tryParse(data['updatedAt']);
            }

            DateTime? lastLoginAt;
            if (data['lastLoginAt'] is String) {
              lastLoginAt = DateTime.tryParse(data['lastLoginAt']);
            }

            DateTime? approvedAt;
            if (data['approvedAt'] is String) {
              approvedAt = DateTime.tryParse(data['approvedAt']);
            }

            // Create a modified data map with converted timestamps
            final modifiedData = {
              ...data,
              'createdAt': createdAt,
              'updatedAt': updatedAt,
              'lastLoginAt': lastLoginAt,
              'approvedAt': approvedAt,
            };

            return Seller.fromJson(modifiedData);
          } catch (e) {
            _logger.warning('Error converting seller document: $e');
            // Return a basic seller object with minimal data to avoid breaking the UI
            return Seller(
              id: data['id'] ?? 'unknown',
              businessName: data['businessName'] as String? ?? 'Unknown',
              email: data['email'] as String? ?? 'Unknown',
              isApproved: data['isApproved'] as bool? ?? false,
              isActive: data['isActive'] as bool? ?? false,
              isSuspended: data['isSuspended'] as bool? ?? false,
            );
          }
        }).toList();
      });
    } catch (e) {
      debugPrint('Error setting up sellers stream: $e');
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }

    // Listen to the stream
    _sellersStream.listen(
      (sellers) {
        setState(() {
          _sellers = sellers;
          _isLoading = false;
          _error = null;
        });
      },
      onError: (error) {
        setState(() {
          _error = error.toString();
          _isLoading = false;
        });
      },
    );
  }

  void _onScroll() {
    // Implement pagination if needed
  }

  void _refreshSellers() {
    setState(() {
      _isLoading = true;
    });
    _setupSellersStream();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Seller Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _refreshSellers,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading && _sellers.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null && _sellers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error: $_error',
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _refreshSellers,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_sellers.isEmpty) {
      return const Center(
        child: Text('No sellers found'),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _refreshSellers();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(8),
        itemCount: _sellers.length,
        itemBuilder: (context, index) {
          final seller = _sellers[index];
          return SellerListItem(
            seller: seller,
            onUpdateStatus: (isActive, isSuspended) {
              _updateSellerStatus(seller, isActive, isSuspended);
            },
            onUpdatePerformance: (
              rating,
              totalReviews,
              totalOrders,
              totalProducts,
              totalRevenue,
            ) {
              _updateSellerPerformance(
                seller,
                rating,
                totalReviews,
                totalOrders,
                totalProducts,
                totalRevenue,
              );
            },
            onDelete: () {
              _deleteSeller(seller);
            },
          );
        },
      ),
    );
  }

  void _updateSellerStatus(Seller seller, bool isActive, bool isSuspended) async {
    try {
      await _databaseService.update('sellers', seller.id, {
        'isActive': isActive,
        'isSuspended': isSuspended,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Seller status updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating seller status: $e')),
        );
      }
    }
  }

  void _updateSellerPerformance(
    Seller seller,
    double rating,
    int totalReviews,
    int totalOrders,
    int totalProducts,
    double totalRevenue,
  ) async {
    try {
      await _databaseService.update('sellers', seller.id, {
        'rating': rating,
        'totalReviews': totalReviews,
        'totalOrders': totalOrders,
        'totalProducts': totalProducts,
        'totalRevenue': totalRevenue,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Seller performance updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating seller performance: $e')),
        );
      }
    }
  }

  void _deleteSeller(Seller seller) async {
    if (!mounted) return;

    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Seller'),
        content: Text('Are you sure you want to delete ${seller.businessName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await _databaseService.update('sellers', seller.id, {
          'isDeleted': true,
          'updatedAt': DateTime.now().toIso8601String(),
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Seller deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting seller: $e')),
          );
        }
      }
    }
  }
}
