import 'package:freezed_annotation/freezed_annotation.dart';
import 'lab_test_category.dart';

part 'lab_test_model.freezed.dart';
part 'lab_test_model.g.dart';

enum LabTestStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('scheduled')
  scheduled,
  @JsonValue('sample_collected')
  sampleCollected,
  @JsonValue('processing')
  processing,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled
}

@freezed
abstract class LabTestModel with _$LabTestModel {
  const factory LabTestModel({
    required String id,
    required String testNumber,
    required String name,
    required String description,
    required String hospitalId,
    required String hospitalName,
    required String patientId,
    required String patientName,
    required String patientPhone,
    String? patientEmail,
    String? doctorId,
    String? doctorName,
    required DateTime testDate,
    String? timeSlot,
    required LabTestStatus status,
    required LabTestCategory category,
    required double price,
    required bool isPaid,
    String? paymentId,
    String? reportUrl,
    String? notes,
    String? sampleType,
    int? turnaroundTime,
    String? instructions,
    bool? isAvailable,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isDeleted,
    DateTime? deletedAt,
  }) = _LabTestModel;

  factory LabTestModel.fromJson(Map<String, dynamic> json) =>
      _$LabTestModelFromJson(json);

  /// Creates an empty [LabTestModel]
  factory LabTestModel.empty() => LabTestModel(
        id: '',
        testNumber: '',
        name: '',
        description: '',
        hospitalId: '',
        hospitalName: '',
        patientId: '',
        patientName: '',
        patientPhone: '',
        testDate: DateTime.now(),
        status: LabTestStatus.pending,
        category: LabTestCategory.other,
        price: 0.0,
        isPaid: false,
        sampleType: '',
        turnaroundTime: 24,
        instructions: '',
        isAvailable: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
}
