import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/banner/banner_model.dart';
import '../../repositories/banner_repository.dart';
import '../../../../../shared/services/analytics/banner_analytics_service.dart';
import '../../../../../shared/services/cache/banner_cache_service.dart';

final bannerProvider =
    StateNotifierProvider<BannerNotifier, AsyncValue<List<BannerModel>>>((ref) {
  final repository = ref.watch(bannerRepositoryProvider);
  return BannerNotifier(repository);
});

class BannerNotifier extends StateNotifier<AsyncValue<List<BannerModel>>> {
  final BannerRepository _repository;

  BannerNotifier(this._repository) : super(const AsyncValue.loading());

  Future<void> loadBanners() async {
    try {
      state = const AsyncValue.loading();

      // Try to get cached banners first
      final cachedBanners = await BannerCacheService.getCachedBanners();
      if (cachedBanners != null) {
        state = AsyncValue.data(cachedBanners);
        // Track cached banner views
        for (final banner in cachedBanners) {
          await BannerAnalyticsService.trackBannerView(banner);
        }
      }

      // Fetch fresh banners from repository
      final banners = await _repository.getActiveBanners();

      // Cache the fresh banners
      await BannerCacheService.cacheBanners(banners);

      // Update state with fresh banners
      state = AsyncValue.data(banners);

      // Track fresh banner views
      for (final banner in banners) {
        await BannerAnalyticsService.trackBannerView(banner);
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      // Track error
      await BannerAnalyticsService.trackBannerError(
        'unknown',
        error.toString(),
      );
    }
  }

  Future<void> handleBannerTap(BannerModel banner) async {
    try {
      await BannerAnalyticsService.trackBannerTap(banner);
    } catch (error) {
      await BannerAnalyticsService.trackBannerError(
        banner.id,
        error.toString(),
      );
    }
  }
}
