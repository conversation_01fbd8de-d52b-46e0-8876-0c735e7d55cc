import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../shared/services/temple/temple_dashboard_service.dart';
import '../../../shared/core/service_locator.dart';

/// Temple dashboard state
@immutable
class TempleDashboardState {
  final int? todayBookings;
  final int? monthlyBookings;
  final double? totalRevenue;
  final List<Map<String, dynamic>>? recentBookings;
  final Map<String, int>? serviceStats;
  final bool isLoading;
  final String? error;

  const TempleDashboardState({
    this.todayBookings,
    this.monthlyBookings,
    this.totalRevenue,
    this.recentBookings,
    this.serviceStats,
    this.isLoading = false,
    this.error,
  });

  TempleDashboardState copyWith({
    int? todayBookings,
    int? monthlyBookings,
    double? totalRevenue,
    List<Map<String, dynamic>>? recentBookings,
    Map<String, int>? serviceStats,
    bool? isLoading,
    String? error,
  }) {
    return TempleDashboardState(
      todayBookings: todayBookings ?? this.todayBookings,
      monthlyBookings: monthlyBookings ?? this.monthlyBookings,
      totalRevenue: totalRevenue ?? this.totalRevenue,
      recentBookings: recentBookings ?? this.recentBookings,
      serviceStats: serviceStats ?? this.serviceStats,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Temple dashboard notifier
class TempleDashboardNotifier extends StateNotifier<TempleDashboardState> {
  final TempleDashboardService _dashboardService;

  TempleDashboardNotifier(this._dashboardService)
    : super(const TempleDashboardState());

  /// Load dashboard data
  Future<void> loadDashboardData(String templeId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final data = await _dashboardService.getDashboardData(templeId);

      state = state.copyWith(
        todayBookings: data['todayBookings'] as int?,
        monthlyBookings: data['monthlyBookings'] as int?,
        totalRevenue: data['totalRevenue'] as double?,
        recentBookings: data['recentBookings'] as List<Map<String, dynamic>>?,
        serviceStats: data['serviceStats'] as Map<String, int>?,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Refresh dashboard data
  Future<void> refreshDashboard(String templeId) async {
    await loadDashboardData(templeId);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Temple dashboard provider
final templeDashboardProvider =
    StateNotifierProvider<TempleDashboardNotifier, TempleDashboardState>((ref) {
      final dashboardService = ref.watch(templeDashboardServiceProvider);
      return TempleDashboardNotifier(dashboardService);
    });

/// Temple dashboard service provider
final templeDashboardServiceProvider = Provider<TempleDashboardService>((ref) {
  return serviceLocator<TempleDashboardService>();
});
