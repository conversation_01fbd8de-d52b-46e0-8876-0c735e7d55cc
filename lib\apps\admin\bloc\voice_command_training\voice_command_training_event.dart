import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/voice_command_training.dart';

part 'voice_command_training_event.freezed.dart';

@freezed
sealed class VoiceCommandTrainingEvent with _$VoiceCommandTrainingEvent {
  const factory VoiceCommandTrainingEvent.loadVoiceCommandTrainings() =
      LoadVoiceCommandTrainings;
  const factory VoiceCommandTrainingEvent.createVoiceCommandTraining(
      VoiceCommandTraining training) = CreateVoiceCommandTraining;
  const factory VoiceCommandTrainingEvent.updateVoiceCommandTraining(
      VoiceCommandTraining training) = UpdateVoiceCommandTraining;
  const factory VoiceCommandTrainingEvent.deleteVoiceCommandTraining(
      String id) = DeleteVoiceCommandTraining;
  const factory VoiceCommandTrainingEvent.updateVoiceCommandTrainingStatus(
      String id, bool isEnabled) = UpdateVoiceCommandTrainingStatus;
}
