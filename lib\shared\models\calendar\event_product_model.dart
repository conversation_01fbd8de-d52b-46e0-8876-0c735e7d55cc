// Event product model

/// Model for products in calendar events
class EventProduct {
  /// Creates an [EventProduct]
  EventProduct({
    required this.id,
    required this.name,
    this.description,
    this.price,
    this.imageUrl,
    this.unit,
    this.quantity,
  });

  /// Product ID
  final String id;

  /// Product name
  final String name;

  /// Product description
  final String? description;

  /// Product price
  final double? price;

  /// Product image URL
  final String? imageUrl;

  /// Product unit (kg, ltr, etc.)
  final String? unit;

  /// Product quantity
  final double? quantity;

  /// Creates an [EventProduct] from JSON
  factory EventProduct.fromJson(Map<String, dynamic> json) => EventProduct(
        id: json['id'] as String,
        name: json['name'] as String,
        description: json['description'] as String?,
        price: json['price'] as double?,
        imageUrl: json['imageUrl'] as String?,
        unit: json['unit'] as String?,
        quantity: json['quantity'] as double?,
      );

  /// Converts this [EventProduct] to JSON
  Map<String, dynamic> to<PERSON><PERSON>() => {
        'id': id,
        'name': name,
        'description': description,
        'price': price,
        'imageUrl': imageUrl,
        'unit': unit,
        'quantity': quantity,
      };
}
