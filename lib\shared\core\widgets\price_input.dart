import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

class PriceInput extends StatefulWidget {
  final String label;
  final double value;
  final Function(double) onChanged;
  final String? currencySymbol;
  final String? errorText;
  final bool showComparison;
  final double? comparisonValue;

  const PriceInput({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
    this.currencySymbol = '₹',
    this.errorText,
    this.showComparison = false,
    this.comparisonValue,
  });

  @override
  State<PriceInput> createState() => _PriceInputState();
}

class _PriceInputState extends State<PriceInput> {
  late TextEditingController _controller;
  late NumberFormat _currencyFormat;
  Color? _textColor;
  Timer? _colorTimer;

  @override
  void initState() {
    super.initState();
    _currencyFormat = NumberFormat.currency(
      symbol: widget.currencySymbol,
      decimalDigits: 2,
    );
    _controller = TextEditingController(
      text: _currencyFormat
          .format(widget.value)
          .replaceAll(widget.currencySymbol!, ''),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _colorTimer?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(PriceInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      final newText = _currencyFormat
          .format(widget.value)
          .replaceAll(widget.currencySymbol!, '');
      if (_controller.text != newText) {
        _controller.text = newText;
        _showChangeAnimation();
      }
    }
  }

  void _showChangeAnimation() {
    setState(() {
      _textColor = widget.value > (widget.comparisonValue ?? widget.value)
          ? Colors.green
          : widget.value < (widget.comparisonValue ?? widget.value)
              ? Colors.red
              : null;
    });

    _colorTimer?.cancel();
    _colorTimer = Timer(const Duration(milliseconds: 1500), () {
      if (mounted) {
        setState(() {
          _textColor = null;
        });
      }
    });
  }

  void _handleChanged(String value) {
    if (value.isEmpty) {
      widget.onChanged(0);
      return;
    }

    final cleanValue = value.replaceAll(RegExp(r'[^\d.]'), '');
    final parsedValue = double.tryParse(cleanValue);
    if (parsedValue != null) {
      widget.onChanged(parsedValue);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: _controller,
          decoration: InputDecoration(
            labelText: widget.label,
            prefixText: widget.currencySymbol,
            errorText: widget.errorText,
            border: const OutlineInputBorder(),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 16,
            ),
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
          ],
          style: TextStyle(
            color: _textColor,
            fontWeight: _textColor != null ? FontWeight.bold : null,
          ),
          onChanged: _handleChanged,
        ),
        if (widget.showComparison && widget.comparisonValue != null) ...[
          const SizedBox(height: 4),
          Text(
            'Previous: ${_currencyFormat.format(widget.comparisonValue)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  decoration: TextDecoration.lineThrough,
                ),
          ),
        ],
      ],
    );
  }
}
