import 'package:injectable/injectable.dart';
import 'package:shivish/apps/admin/models/user.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/utils/logger.dart';

@injectable
class UserService {
  final DatabaseService _databaseService;
  final _logger = getLogger('AdminUserService');

  UserService(this._databaseService);

  Future<List<User>> getUsers() async {
    try {
      _logger.info('Fetching all users from database');
      final data = await _databaseService.getAll('users');

      if (data.isEmpty) {
        _logger.info('No users found in database');
        return [];
      }

      final users = data.map((userData) {
        // Ensure the user data has an ID
        final userMap = Map<String, dynamic>.from(userData);
        if (!userMap.containsKey('id')) {
          userMap['id'] = userMap['_id'] ?? '';
        }
        return User.fromJson(userMap);
      }).toList();

      _logger.info('Successfully fetched ${users.length} users');
      return users;
    } catch (e) {
      _logger.severe('Failed to load users: $e');
      throw Exception('Failed to load users: $e');
    }
  }

  Future<User> addUser(User user) async {
    try {
      _logger.info('Adding new user: ${user.email}');

      // Create user data without ID (will be generated by database)
      final userData = user.toJson();
      userData.remove('id'); // Remove ID if present, let database generate it

      final result = await _databaseService.create('users', userData);

      final newUser = user.copyWith(id: result['id']);
      _logger.info('Successfully added user with ID: ${result['id']}');

      return newUser;
    } catch (e) {
      _logger.severe('Failed to add user: $e');
      throw Exception('Failed to add user: $e');
    }
  }

  Future<User> updateUser(User user) async {
    try {
      _logger.info('Updating user: ${user.id}');

      if (user.id.isEmpty) {
        throw ArgumentError('User ID cannot be empty for update operation');
      }

      // Prepare update data without ID and timestamps
      final updateData = user.toJson();
      updateData.remove('id');
      updateData.remove('created_at'); // Don't update creation timestamp
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final result = await _databaseService.update('users', user.id, updateData);

      if (result == null) {
        throw Exception('User not found with ID: ${user.id}');
      }

      _logger.info('Successfully updated user: ${user.id}');
      return user;
    } catch (e) {
      _logger.severe('Failed to update user: $e');
      throw Exception('Failed to update user: $e');
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      _logger.info('Deleting user: $userId');

      if (userId.isEmpty) {
        throw ArgumentError('User ID cannot be empty for delete operation');
      }

      final success = await _databaseService.delete('users', userId);

      if (!success) {
        throw Exception('User not found with ID: $userId');
      }

      _logger.info('Successfully deleted user: $userId');
    } catch (e) {
      _logger.severe('Failed to delete user: $e');
      throw Exception('Failed to delete user: $e');
    }
  }

  /// Get user by ID
  Future<User?> getUserById(String userId) async {
    try {
      _logger.info('Fetching user by ID: $userId');

      if (userId.isEmpty) {
        throw ArgumentError('User ID cannot be empty');
      }

      final userData = await _databaseService.find('users', userId);

      if (userData == null) {
        _logger.info('User not found with ID: $userId');
        return null;
      }

      final user = User.fromJson(userData);
      _logger.info('Successfully fetched user: $userId');

      return user;
    } catch (e) {
      _logger.severe('Failed to get user by ID: $e');
      throw Exception('Failed to get user by ID: $e');
    }
  }

  /// Search users by email
  Future<List<User>> searchUsersByEmail(String email) async {
    try {
      _logger.info('Searching users by email: $email');

      final data = await _databaseService.search('users', email, ['email']);

      final users = data.map((userData) {
        final userMap = Map<String, dynamic>.from(userData);
        if (!userMap.containsKey('id')) {
          userMap['id'] = userMap['_id'] ?? '';
        }
        return User.fromJson(userMap);
      }).toList();

      _logger.info('Found ${users.length} users matching email: $email');
      return users;
    } catch (e) {
      _logger.severe('Failed to search users by email: $e');
      throw Exception('Failed to search users by email: $e');
    }
  }
}
