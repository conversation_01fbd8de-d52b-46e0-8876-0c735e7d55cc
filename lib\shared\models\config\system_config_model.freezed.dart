// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'system_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SystemConfigModel {

 SecurityConfigModel get security; RefundConfigModel get refund; AIConfigModel get ai; VoiceCommandConfigModel get voiceCommand; ChatbotConfigModel get chatbot; Map<String, dynamic> get additionalSettings;
/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SystemConfigModelCopyWith<SystemConfigModel> get copyWith => _$SystemConfigModelCopyWithImpl<SystemConfigModel>(this as SystemConfigModel, _$identity);

  /// Serializes this SystemConfigModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SystemConfigModel&&(identical(other.security, security) || other.security == security)&&(identical(other.refund, refund) || other.refund == refund)&&(identical(other.ai, ai) || other.ai == ai)&&(identical(other.voiceCommand, voiceCommand) || other.voiceCommand == voiceCommand)&&(identical(other.chatbot, chatbot) || other.chatbot == chatbot)&&const DeepCollectionEquality().equals(other.additionalSettings, additionalSettings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,security,refund,ai,voiceCommand,chatbot,const DeepCollectionEquality().hash(additionalSettings));

@override
String toString() {
  return 'SystemConfigModel(security: $security, refund: $refund, ai: $ai, voiceCommand: $voiceCommand, chatbot: $chatbot, additionalSettings: $additionalSettings)';
}


}

/// @nodoc
abstract mixin class $SystemConfigModelCopyWith<$Res>  {
  factory $SystemConfigModelCopyWith(SystemConfigModel value, $Res Function(SystemConfigModel) _then) = _$SystemConfigModelCopyWithImpl;
@useResult
$Res call({
 SecurityConfigModel security, RefundConfigModel refund, AIConfigModel ai, VoiceCommandConfigModel voiceCommand, ChatbotConfigModel chatbot, Map<String, dynamic> additionalSettings
});


$SecurityConfigModelCopyWith<$Res> get security;$RefundConfigModelCopyWith<$Res> get refund;$AIConfigModelCopyWith<$Res> get ai;$VoiceCommandConfigModelCopyWith<$Res> get voiceCommand;$ChatbotConfigModelCopyWith<$Res> get chatbot;

}
/// @nodoc
class _$SystemConfigModelCopyWithImpl<$Res>
    implements $SystemConfigModelCopyWith<$Res> {
  _$SystemConfigModelCopyWithImpl(this._self, this._then);

  final SystemConfigModel _self;
  final $Res Function(SystemConfigModel) _then;

/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? security = null,Object? refund = null,Object? ai = null,Object? voiceCommand = null,Object? chatbot = null,Object? additionalSettings = null,}) {
  return _then(_self.copyWith(
security: null == security ? _self.security : security // ignore: cast_nullable_to_non_nullable
as SecurityConfigModel,refund: null == refund ? _self.refund : refund // ignore: cast_nullable_to_non_nullable
as RefundConfigModel,ai: null == ai ? _self.ai : ai // ignore: cast_nullable_to_non_nullable
as AIConfigModel,voiceCommand: null == voiceCommand ? _self.voiceCommand : voiceCommand // ignore: cast_nullable_to_non_nullable
as VoiceCommandConfigModel,chatbot: null == chatbot ? _self.chatbot : chatbot // ignore: cast_nullable_to_non_nullable
as ChatbotConfigModel,additionalSettings: null == additionalSettings ? _self.additionalSettings : additionalSettings // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}
/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SecurityConfigModelCopyWith<$Res> get security {
  
  return $SecurityConfigModelCopyWith<$Res>(_self.security, (value) {
    return _then(_self.copyWith(security: value));
  });
}/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RefundConfigModelCopyWith<$Res> get refund {
  
  return $RefundConfigModelCopyWith<$Res>(_self.refund, (value) {
    return _then(_self.copyWith(refund: value));
  });
}/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AIConfigModelCopyWith<$Res> get ai {
  
  return $AIConfigModelCopyWith<$Res>(_self.ai, (value) {
    return _then(_self.copyWith(ai: value));
  });
}/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VoiceCommandConfigModelCopyWith<$Res> get voiceCommand {
  
  return $VoiceCommandConfigModelCopyWith<$Res>(_self.voiceCommand, (value) {
    return _then(_self.copyWith(voiceCommand: value));
  });
}/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChatbotConfigModelCopyWith<$Res> get chatbot {
  
  return $ChatbotConfigModelCopyWith<$Res>(_self.chatbot, (value) {
    return _then(_self.copyWith(chatbot: value));
  });
}
}


/// Adds pattern-matching-related methods to [SystemConfigModel].
extension SystemConfigModelPatterns on SystemConfigModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SystemConfigModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SystemConfigModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SystemConfigModel value)  $default,){
final _that = this;
switch (_that) {
case _SystemConfigModel():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SystemConfigModel value)?  $default,){
final _that = this;
switch (_that) {
case _SystemConfigModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( SecurityConfigModel security,  RefundConfigModel refund,  AIConfigModel ai,  VoiceCommandConfigModel voiceCommand,  ChatbotConfigModel chatbot,  Map<String, dynamic> additionalSettings)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SystemConfigModel() when $default != null:
return $default(_that.security,_that.refund,_that.ai,_that.voiceCommand,_that.chatbot,_that.additionalSettings);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( SecurityConfigModel security,  RefundConfigModel refund,  AIConfigModel ai,  VoiceCommandConfigModel voiceCommand,  ChatbotConfigModel chatbot,  Map<String, dynamic> additionalSettings)  $default,) {final _that = this;
switch (_that) {
case _SystemConfigModel():
return $default(_that.security,_that.refund,_that.ai,_that.voiceCommand,_that.chatbot,_that.additionalSettings);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( SecurityConfigModel security,  RefundConfigModel refund,  AIConfigModel ai,  VoiceCommandConfigModel voiceCommand,  ChatbotConfigModel chatbot,  Map<String, dynamic> additionalSettings)?  $default,) {final _that = this;
switch (_that) {
case _SystemConfigModel() when $default != null:
return $default(_that.security,_that.refund,_that.ai,_that.voiceCommand,_that.chatbot,_that.additionalSettings);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SystemConfigModel implements SystemConfigModel {
  const _SystemConfigModel({required this.security, required this.refund, required this.ai, required this.voiceCommand, required this.chatbot, required final  Map<String, dynamic> additionalSettings}): _additionalSettings = additionalSettings;
  factory _SystemConfigModel.fromJson(Map<String, dynamic> json) => _$SystemConfigModelFromJson(json);

@override final  SecurityConfigModel security;
@override final  RefundConfigModel refund;
@override final  AIConfigModel ai;
@override final  VoiceCommandConfigModel voiceCommand;
@override final  ChatbotConfigModel chatbot;
 final  Map<String, dynamic> _additionalSettings;
@override Map<String, dynamic> get additionalSettings {
  if (_additionalSettings is EqualUnmodifiableMapView) return _additionalSettings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_additionalSettings);
}


/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SystemConfigModelCopyWith<_SystemConfigModel> get copyWith => __$SystemConfigModelCopyWithImpl<_SystemConfigModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SystemConfigModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SystemConfigModel&&(identical(other.security, security) || other.security == security)&&(identical(other.refund, refund) || other.refund == refund)&&(identical(other.ai, ai) || other.ai == ai)&&(identical(other.voiceCommand, voiceCommand) || other.voiceCommand == voiceCommand)&&(identical(other.chatbot, chatbot) || other.chatbot == chatbot)&&const DeepCollectionEquality().equals(other._additionalSettings, _additionalSettings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,security,refund,ai,voiceCommand,chatbot,const DeepCollectionEquality().hash(_additionalSettings));

@override
String toString() {
  return 'SystemConfigModel(security: $security, refund: $refund, ai: $ai, voiceCommand: $voiceCommand, chatbot: $chatbot, additionalSettings: $additionalSettings)';
}


}

/// @nodoc
abstract mixin class _$SystemConfigModelCopyWith<$Res> implements $SystemConfigModelCopyWith<$Res> {
  factory _$SystemConfigModelCopyWith(_SystemConfigModel value, $Res Function(_SystemConfigModel) _then) = __$SystemConfigModelCopyWithImpl;
@override @useResult
$Res call({
 SecurityConfigModel security, RefundConfigModel refund, AIConfigModel ai, VoiceCommandConfigModel voiceCommand, ChatbotConfigModel chatbot, Map<String, dynamic> additionalSettings
});


@override $SecurityConfigModelCopyWith<$Res> get security;@override $RefundConfigModelCopyWith<$Res> get refund;@override $AIConfigModelCopyWith<$Res> get ai;@override $VoiceCommandConfigModelCopyWith<$Res> get voiceCommand;@override $ChatbotConfigModelCopyWith<$Res> get chatbot;

}
/// @nodoc
class __$SystemConfigModelCopyWithImpl<$Res>
    implements _$SystemConfigModelCopyWith<$Res> {
  __$SystemConfigModelCopyWithImpl(this._self, this._then);

  final _SystemConfigModel _self;
  final $Res Function(_SystemConfigModel) _then;

/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? security = null,Object? refund = null,Object? ai = null,Object? voiceCommand = null,Object? chatbot = null,Object? additionalSettings = null,}) {
  return _then(_SystemConfigModel(
security: null == security ? _self.security : security // ignore: cast_nullable_to_non_nullable
as SecurityConfigModel,refund: null == refund ? _self.refund : refund // ignore: cast_nullable_to_non_nullable
as RefundConfigModel,ai: null == ai ? _self.ai : ai // ignore: cast_nullable_to_non_nullable
as AIConfigModel,voiceCommand: null == voiceCommand ? _self.voiceCommand : voiceCommand // ignore: cast_nullable_to_non_nullable
as VoiceCommandConfigModel,chatbot: null == chatbot ? _self.chatbot : chatbot // ignore: cast_nullable_to_non_nullable
as ChatbotConfigModel,additionalSettings: null == additionalSettings ? _self._additionalSettings : additionalSettings // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SecurityConfigModelCopyWith<$Res> get security {
  
  return $SecurityConfigModelCopyWith<$Res>(_self.security, (value) {
    return _then(_self.copyWith(security: value));
  });
}/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RefundConfigModelCopyWith<$Res> get refund {
  
  return $RefundConfigModelCopyWith<$Res>(_self.refund, (value) {
    return _then(_self.copyWith(refund: value));
  });
}/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AIConfigModelCopyWith<$Res> get ai {
  
  return $AIConfigModelCopyWith<$Res>(_self.ai, (value) {
    return _then(_self.copyWith(ai: value));
  });
}/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VoiceCommandConfigModelCopyWith<$Res> get voiceCommand {
  
  return $VoiceCommandConfigModelCopyWith<$Res>(_self.voiceCommand, (value) {
    return _then(_self.copyWith(voiceCommand: value));
  });
}/// Create a copy of SystemConfigModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChatbotConfigModelCopyWith<$Res> get chatbot {
  
  return $ChatbotConfigModelCopyWith<$Res>(_self.chatbot, (value) {
    return _then(_self.copyWith(chatbot: value));
  });
}
}

// dart format on
