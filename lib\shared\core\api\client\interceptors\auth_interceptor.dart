import 'package:dio/dio.dart';

class AuthInterceptor extends Interceptor {
  String? _accessToken;
  final String _tokenType = 'Bearer';

  void setAccessToken(String token) {
    _accessToken = token;
  }

  void clearAccessToken() {
    _accessToken = null;
  }

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) {
    if (_accessToken != null) {
      options.headers['Authorization'] = '$_tokenType $_accessToken';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      // Handle token refresh here
      // You can use a token refresh mechanism or logout the user
      clearAccessToken();
    }
    handler.next(err);
  }

  @override
  void onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) {
    // Handle token from response if needed
    final token = response.headers.value('Authorization');
    if (token != null) {
      setAccessToken(token.replaceFirst('$_tokenType ', ''));
    }
    handler.next(response);
  }
}
