import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter/foundation.dart';
import 'analytics_service.dart';
import 'analytics_events.dart';

class PerformanceMonitoring {
  final FirebasePerformance _performance = FirebasePerformance.instance;
  final AnalyticsService _analytics;

  PerformanceMonitoring(this._analytics);

  // Track HTTP request traces
  Future<void> trackHttpRequest({
    required String url,
    required HttpMethod method,
    required int responseTime,
    int? responseCode,
  }) async {
    try {
      final trace = _performance.newHttpMetric(url, method);
      await trace.start();

      trace.responsePayloadSize = responseTime;
      if (responseCode != null) {
        trace.httpResponseCode = responseCode;
      }

      await trace.stop();

      // Log to analytics for aggregated analysis
      await _analytics.logEvent(
        name: AnalyticsEvents.networkLatency,
        parameters: {
          AnalyticsParams.endpoint: url,
          AnalyticsParams.latencyMs: responseTime,
        },
      );
    } catch (e) {
      debugPrint('Error tracking HTTP request: $e');
    }
  }

  // Track custom traces
  Future<Trace> startTrace(String name) async {
    try {
      final trace = _performance.newTrace(name);
      await trace.start();
      return trace;
    } catch (e) {
      debugPrint('Error starting trace: $e');
      rethrow;
    }
  }

  Future<void> stopTrace(Trace trace) async {
    try {
      await trace.stop();
    } catch (e) {
      debugPrint('Error stopping trace: $e');
    }
  }

  // Track app startup time
  Future<void> trackAppStartup({required Duration duration}) async {
    try {
      await _analytics.logEvent(
        name: AnalyticsEvents.appStartup,
        parameters: {
          'duration_ms': duration.inMilliseconds,
        },
      );
    } catch (e) {
      debugPrint('Error tracking app startup: $e');
    }
  }

  // Track UI rendering performance
  Future<void> trackRenderLatency({
    required String screenName,
    required Duration duration,
  }) async {
    try {
      await _analytics.logEvent(
        name: AnalyticsEvents.renderLatency,
        parameters: {
          AnalyticsParams.screenName: screenName,
          'duration_ms': duration.inMilliseconds,
        },
      );
    } catch (e) {
      debugPrint('Error tracking render latency: $e');
    }
  }

  // Track memory usage
  Future<void> trackMemoryUsage({
    required String screenName,
    required int memoryBytes,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'memory_usage',
        parameters: {
          AnalyticsParams.screenName: screenName,
          'memory_bytes': memoryBytes,
        },
      );
    } catch (e) {
      debugPrint('Error tracking memory usage: $e');
    }
  }

  // Track frame rate drops
  Future<void> trackFrameDrop({
    required String screenName,
    required int droppedFrames,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'frame_drop',
        parameters: {
          AnalyticsParams.screenName: screenName,
          'dropped_frames': droppedFrames,
        },
      );
    } catch (e) {
      debugPrint('Error tracking frame drop: $e');
    }
  }
}
