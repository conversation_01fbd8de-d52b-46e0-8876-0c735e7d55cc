// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ProductEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductEvent()';
}


}

/// @nodoc
class $ProductEventCopyWith<$Res>  {
$ProductEventCopyWith(ProductEvent _, $Res Function(ProductEvent) __);
}


/// Adds pattern-matching-related methods to [ProductEvent].
extension ProductEventPatterns on ProductEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _LoadProducts value)?  loadProducts,TResult Function( _LoadMoreProducts value)?  loadMoreProducts,TResult Function( _UpdateProductStatus value)?  updateProductStatus,TResult Function( _DeleteProduct value)?  deleteProduct,TResult Function( _ApplyFilters value)?  applyFilters,required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LoadProducts() when loadProducts != null:
return loadProducts(_that);case _LoadMoreProducts() when loadMoreProducts != null:
return loadMoreProducts(_that);case _UpdateProductStatus() when updateProductStatus != null:
return updateProductStatus(_that);case _DeleteProduct() when deleteProduct != null:
return deleteProduct(_that);case _ApplyFilters() when applyFilters != null:
return applyFilters(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _LoadProducts value)  loadProducts,required TResult Function( _LoadMoreProducts value)  loadMoreProducts,required TResult Function( _UpdateProductStatus value)  updateProductStatus,required TResult Function( _DeleteProduct value)  deleteProduct,required TResult Function( _ApplyFilters value)  applyFilters,}){
final _that = this;
switch (_that) {
case _LoadProducts():
return loadProducts(_that);case _LoadMoreProducts():
return loadMoreProducts(_that);case _UpdateProductStatus():
return updateProductStatus(_that);case _DeleteProduct():
return deleteProduct(_that);case _ApplyFilters():
return applyFilters(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _LoadProducts value)?  loadProducts,TResult? Function( _LoadMoreProducts value)?  loadMoreProducts,TResult? Function( _UpdateProductStatus value)?  updateProductStatus,TResult? Function( _DeleteProduct value)?  deleteProduct,TResult? Function( _ApplyFilters value)?  applyFilters,}){
final _that = this;
switch (_that) {
case _LoadProducts() when loadProducts != null:
return loadProducts(_that);case _LoadMoreProducts() when loadMoreProducts != null:
return loadMoreProducts(_that);case _UpdateProductStatus() when updateProductStatus != null:
return updateProductStatus(_that);case _DeleteProduct() when deleteProduct != null:
return deleteProduct(_that);case _ApplyFilters() when applyFilters != null:
return applyFilters(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  loadProducts,TResult Function()?  loadMoreProducts,TResult Function( ProductModel product,  String status,  String? notes,  bool isActive)?  updateProductStatus,TResult Function( String id)?  deleteProduct,TResult Function( Map<String, dynamic> filters)?  applyFilters,required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LoadProducts() when loadProducts != null:
return loadProducts();case _LoadMoreProducts() when loadMoreProducts != null:
return loadMoreProducts();case _UpdateProductStatus() when updateProductStatus != null:
return updateProductStatus(_that.product,_that.status,_that.notes,_that.isActive);case _DeleteProduct() when deleteProduct != null:
return deleteProduct(_that.id);case _ApplyFilters() when applyFilters != null:
return applyFilters(_that.filters);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  loadProducts,required TResult Function()  loadMoreProducts,required TResult Function( ProductModel product,  String status,  String? notes,  bool isActive)  updateProductStatus,required TResult Function( String id)  deleteProduct,required TResult Function( Map<String, dynamic> filters)  applyFilters,}) {final _that = this;
switch (_that) {
case _LoadProducts():
return loadProducts();case _LoadMoreProducts():
return loadMoreProducts();case _UpdateProductStatus():
return updateProductStatus(_that.product,_that.status,_that.notes,_that.isActive);case _DeleteProduct():
return deleteProduct(_that.id);case _ApplyFilters():
return applyFilters(_that.filters);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  loadProducts,TResult? Function()?  loadMoreProducts,TResult? Function( ProductModel product,  String status,  String? notes,  bool isActive)?  updateProductStatus,TResult? Function( String id)?  deleteProduct,TResult? Function( Map<String, dynamic> filters)?  applyFilters,}) {final _that = this;
switch (_that) {
case _LoadProducts() when loadProducts != null:
return loadProducts();case _LoadMoreProducts() when loadMoreProducts != null:
return loadMoreProducts();case _UpdateProductStatus() when updateProductStatus != null:
return updateProductStatus(_that.product,_that.status,_that.notes,_that.isActive);case _DeleteProduct() when deleteProduct != null:
return deleteProduct(_that.id);case _ApplyFilters() when applyFilters != null:
return applyFilters(_that.filters);case _:
  return null;

}
}

}

/// @nodoc


class _LoadProducts implements ProductEvent {
  const _LoadProducts();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadProducts);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductEvent.loadProducts()';
}


}




/// @nodoc


class _LoadMoreProducts implements ProductEvent {
  const _LoadMoreProducts();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadMoreProducts);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductEvent.loadMoreProducts()';
}


}




/// @nodoc


class _UpdateProductStatus implements ProductEvent {
  const _UpdateProductStatus(this.product, this.status, this.notes, this.isActive);
  

 final  ProductModel product;
 final  String status;
 final  String? notes;
 final  bool isActive;

/// Create a copy of ProductEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateProductStatusCopyWith<_UpdateProductStatus> get copyWith => __$UpdateProductStatusCopyWithImpl<_UpdateProductStatus>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateProductStatus&&(identical(other.product, product) || other.product == product)&&(identical(other.status, status) || other.status == status)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}


@override
int get hashCode => Object.hash(runtimeType,product,status,notes,isActive);

@override
String toString() {
  return 'ProductEvent.updateProductStatus(product: $product, status: $status, notes: $notes, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class _$UpdateProductStatusCopyWith<$Res> implements $ProductEventCopyWith<$Res> {
  factory _$UpdateProductStatusCopyWith(_UpdateProductStatus value, $Res Function(_UpdateProductStatus) _then) = __$UpdateProductStatusCopyWithImpl;
@useResult
$Res call({
 ProductModel product, String status, String? notes, bool isActive
});


$ProductModelCopyWith<$Res> get product;

}
/// @nodoc
class __$UpdateProductStatusCopyWithImpl<$Res>
    implements _$UpdateProductStatusCopyWith<$Res> {
  __$UpdateProductStatusCopyWithImpl(this._self, this._then);

  final _UpdateProductStatus _self;
  final $Res Function(_UpdateProductStatus) _then;

/// Create a copy of ProductEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? product = null,Object? status = null,Object? notes = freezed,Object? isActive = null,}) {
  return _then(_UpdateProductStatus(
null == product ? _self.product : product // ignore: cast_nullable_to_non_nullable
as ProductModel,null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of ProductEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProductModelCopyWith<$Res> get product {
  
  return $ProductModelCopyWith<$Res>(_self.product, (value) {
    return _then(_self.copyWith(product: value));
  });
}
}

/// @nodoc


class _DeleteProduct implements ProductEvent {
  const _DeleteProduct(this.id);
  

 final  String id;

/// Create a copy of ProductEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DeleteProductCopyWith<_DeleteProduct> get copyWith => __$DeleteProductCopyWithImpl<_DeleteProduct>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DeleteProduct&&(identical(other.id, id) || other.id == id));
}


@override
int get hashCode => Object.hash(runtimeType,id);

@override
String toString() {
  return 'ProductEvent.deleteProduct(id: $id)';
}


}

/// @nodoc
abstract mixin class _$DeleteProductCopyWith<$Res> implements $ProductEventCopyWith<$Res> {
  factory _$DeleteProductCopyWith(_DeleteProduct value, $Res Function(_DeleteProduct) _then) = __$DeleteProductCopyWithImpl;
@useResult
$Res call({
 String id
});




}
/// @nodoc
class __$DeleteProductCopyWithImpl<$Res>
    implements _$DeleteProductCopyWith<$Res> {
  __$DeleteProductCopyWithImpl(this._self, this._then);

  final _DeleteProduct _self;
  final $Res Function(_DeleteProduct) _then;

/// Create a copy of ProductEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,}) {
  return _then(_DeleteProduct(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _ApplyFilters implements ProductEvent {
  const _ApplyFilters(final  Map<String, dynamic> filters): _filters = filters;
  

 final  Map<String, dynamic> _filters;
 Map<String, dynamic> get filters {
  if (_filters is EqualUnmodifiableMapView) return _filters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_filters);
}


/// Create a copy of ProductEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ApplyFiltersCopyWith<_ApplyFilters> get copyWith => __$ApplyFiltersCopyWithImpl<_ApplyFilters>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ApplyFilters&&const DeepCollectionEquality().equals(other._filters, _filters));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_filters));

@override
String toString() {
  return 'ProductEvent.applyFilters(filters: $filters)';
}


}

/// @nodoc
abstract mixin class _$ApplyFiltersCopyWith<$Res> implements $ProductEventCopyWith<$Res> {
  factory _$ApplyFiltersCopyWith(_ApplyFilters value, $Res Function(_ApplyFilters) _then) = __$ApplyFiltersCopyWithImpl;
@useResult
$Res call({
 Map<String, dynamic> filters
});




}
/// @nodoc
class __$ApplyFiltersCopyWithImpl<$Res>
    implements _$ApplyFiltersCopyWith<$Res> {
  __$ApplyFiltersCopyWithImpl(this._self, this._then);

  final _ApplyFilters _self;
  final $Res Function(_ApplyFilters) _then;

/// Create a copy of ProductEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? filters = null,}) {
  return _then(_ApplyFilters(
null == filters ? _self._filters : filters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

// dart format on
