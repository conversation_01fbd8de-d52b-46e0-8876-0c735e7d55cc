import 'package:flutter/material.dart';
import 'package:shivish/apps/seller/domain/models/schedule_model.dart';

class HolidayDialog extends StatefulWidget {
  final HolidaySchedule? initialHoliday;
  final ValueChanged<HolidaySchedule> onSave;

  const HolidayDialog({
    super.key,
    this.initialHoliday,
    required this.onSave,
  });

  @override
  State<HolidayDialog> createState() => _HolidayDialogState();
}

class _HolidayDialogState extends State<HolidayDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  late DateTime _startDate;
  late DateTime _endDate;
  bool _isRecurring = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialHoliday != null) {
      _nameController.text = widget.initialHoliday!.name;
      _descriptionController.text = widget.initialHoliday!.description ?? '';
      _startDate = widget.initialHoliday!.startDate;
      _endDate = widget.initialHoliday!.endDate;
      _isRecurring = widget.initialHoliday!.isRecurring;
    } else {
      _startDate = DateTime.now();
      _endDate = DateTime.now().add(const Duration(days: 1));
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(
    BuildContext context,
    DateTime initialDate,
    void Function(DateTime) onSelect,
  ) async {
    final DateTime? date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );
    if (date != null) {
      onSelect(date);
    }
  }

  bool _validateDates() {
    return _startDate.isBefore(_endDate) ||
        _startDate.isAtSameMomentAs(_endDate);
  }

  void _handleSave() {
    if (!_formKey.currentState!.validate() || !_validateDates()) {
      if (!_validateDates()) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('End date must be after start date'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    widget.onSave(
      HolidaySchedule(
        id: widget.initialHoliday?.id ??
            'holiday_${DateTime.now().millisecondsSinceEpoch}',
        name: _nameController.text,
        description: _descriptionController.text.isEmpty
            ? null
            : _descriptionController.text,
        startDate: _startDate,
        endDate: _endDate,
        isRecurring: _isRecurring,
        createdAt: widget.initialHoliday?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title:
          Text(widget.initialHoliday == null ? 'Add Holiday' : 'Edit Holiday'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Holiday Name',
                  hintText: 'Enter holiday name',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a holiday name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  hintText: 'Enter holiday description',
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),
              ListTile(
                title: const Text('Start Date'),
                trailing: TextButton(
                  onPressed: () => _selectDate(
                    context,
                    _startDate,
                    (date) => setState(() => _startDate = date),
                  ),
                  child: Text(
                    '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                  ),
                ),
              ),
              ListTile(
                title: const Text('End Date'),
                trailing: TextButton(
                  onPressed: () => _selectDate(
                    context,
                    _endDate,
                    (date) => setState(() => _endDate = date),
                  ),
                  child: Text(
                    '${_endDate.day}/${_endDate.month}/${_endDate.year}',
                  ),
                ),
              ),
              SwitchListTile(
                title: const Text('Recurring Holiday'),
                subtitle: const Text('Repeat this holiday every year'),
                value: _isRecurring,
                onChanged: (value) => setState(() => _isRecurring = value),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _handleSave,
          child: const Text('Save'),
        ),
      ],
    );
  }
}
