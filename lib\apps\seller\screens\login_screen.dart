import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/providers/auth_provider.dart';
import '../application/seller_provider.dart';
import '../theme/seller_theme.dart';
import '../seller_routes.dart';

class LoginScreen extends ConsumerStatefulWidget {
  /// Callback function that will be called when login is successful
  final VoidCallback? onLoginSuccess;

  /// Custom function to handle register button press
  final void Function(BuildContext context)? onRegisterPressed;

  const LoginScreen({
    super.key,
    this.onLoginSuccess,
    this.onRegisterPressed,
  });

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  String? _errorMessage;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    debugPrint('SellerLoginScreen: initState called');
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your email';
    }
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your password';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  Future<void> _handleLogin() async {
    debugPrint('SellerLoginScreen: _handleLogin called');
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        debugPrint(
            'SellerLoginScreen: Attempting login with email: ${_emailController.text}');
        final authService = ref.read(authServiceProvider);
        final user = await authService.signInWithEmail(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        );

        if (user.user != null) {
          debugPrint(
              'SellerLoginScreen: Login successful, getting seller data');

          // Use the correct sellerProvider from application/seller_provider.dart
          await ref.read(sellerProvider.notifier).getSeller(user.user!.id);

          if (mounted) {
            // Show a success message
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Login successful!'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 1),
              ),
            );

            debugPrint('SellerLoginScreen: Login successful, calling callback');

            // Wait a moment for the snackbar to be visible
            await Future.delayed(const Duration(milliseconds: 300));

            // Call the onLoginSuccess callback if provided
            if (widget.onLoginSuccess != null && mounted) {
              debugPrint('SellerLoginScreen: Calling onLoginSuccess callback');
              widget.onLoginSuccess!();
            } else {
              debugPrint(
                  'SellerLoginScreen: No onLoginSuccess callback provided');

              // Try to navigate using GoRouter as a fallback
              if (mounted) {
                try {
                  debugPrint(
                      'SellerLoginScreen: Trying to navigate with GoRouter');

                  // First, show a success message to the user
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Login successful! Redirecting to home...'),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 2),
                    ),
                  );

                  // Add a delay before navigation to ensure the UI is ready
                  Future.delayed(const Duration(milliseconds: 500), () {
                    if (mounted) {
                      // Use the correct route path from SellerRoutes
                      debugPrint(
                          'SellerLoginScreen: Attempting to navigate to home');

                      // Use the SellerRoutes constant for home
                      try {
                        SellerRoutes.navigateToHome(context);
                        debugPrint(
                            'SellerLoginScreen: Navigation to home attempted using SellerRoutes');
                      } catch (e) {
                        debugPrint(
                            'SellerLoginScreen: Error using SellerRoutes: $e');

                        // Fallback to direct navigation
                        try {
                          context.go('/seller/home');
                          debugPrint(
                              'SellerLoginScreen: Direct navigation to home attempted');
                        } catch (e2) {
                          debugPrint(
                              'SellerLoginScreen: Error with direct navigation: $e2');
                        }
                      }
                    }
                  });
                } catch (e) {
                  debugPrint(
                      'SellerLoginScreen: GoRouter navigation failed: $e');

                  // Fallback to Navigator if GoRouter fails
                  try {
                    Navigator.of(context).pushReplacementNamed('/seller/home');
                    debugPrint(
                        'SellerLoginScreen: Fallback navigation attempted');
                  } catch (navError) {
                    debugPrint(
                        'SellerLoginScreen: All navigation methods failed: $navError');
                  }
                }
              }
            }
          }
        }
      } catch (e) {
        debugPrint('SellerLoginScreen: Login error: $e');
        if (mounted) {
          setState(() {
            _errorMessage = e.toString();
          });
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _onRegisterPressed() {
    debugPrint('SellerLoginScreen: Register button pressed');

    // If custom register handler is provided, use it
    if (widget.onRegisterPressed != null) {
      debugPrint('SellerLoginScreen: Using custom register handler');
      widget.onRegisterPressed!(context);
      return;
    }

    // Use GoRouter for navigation
    debugPrint('SellerLoginScreen: Using GoRouter for registration');
    context.go(SellerRoutes.register);
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('SellerLoginScreen: build method called');

    final theme = Theme.of(context);
    final primaryColor = SellerTheme.primaryColor;
    final secondaryColor = SellerTheme.primaryLightColor;

    debugPrint('SellerLoginScreen: Building login screen');

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              primaryColor,
              secondaryColor,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  const SizedBox(height: 40),
                  // Logo and App Name
                  Column(
                    children: [
                      // App Logo
                      SizedBox(
                        width: 120,
                        height: 120,
                        child: Image.asset(
                          'assets/images/flavors/seller.png',
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            debugPrint('Error loading seller logo: $error');
                            return Icon(
                              Icons.store,
                              size: 60,
                              color: Colors.white,
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 24),
                      // App Name
                      Text(
                        'Shivish Seller',
                        style: theme.textTheme.headlineMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Sign in to continue',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: Colors.white.withAlpha(204),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 40),

                  // Login Form
                  Card(
                    elevation: 8,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Email Field
                            TextFormField(
                              controller: _emailController,
                              decoration: InputDecoration(
                                labelText: 'Email',
                                prefixIcon: const Icon(Icons.email_outlined),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                              ),
                              keyboardType: TextInputType.emailAddress,
                              textInputAction: TextInputAction.next,
                              validator: _validateEmail,
                            ),
                            const SizedBox(height: 16),

                            // Password Field
                            TextFormField(
                              controller: _passwordController,
                              decoration: InputDecoration(
                                labelText: 'Password',
                                prefixIcon: const Icon(Icons.lock_outline),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscurePassword
                                        ? Icons.visibility_outlined
                                        : Icons.visibility_off_outlined,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscurePassword = !_obscurePassword;
                                    });
                                  },
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                              ),
                              obscureText: _obscurePassword,
                              textInputAction: TextInputAction.done,
                              validator: _validatePassword,
                              onFieldSubmitted: (_) => _handleLogin(),
                            ),

                            const SizedBox(height: 8),
                            Align(
                              alignment: Alignment.centerRight,
                              child: TextButton(
                                onPressed: () {
                                  // Try to use GoRouter if available, otherwise show a dialog
                                  try {
                                    context.push('/seller/forgot-password');
                                  } catch (e) {
                                    debugPrint(
                                        'SellerLoginScreen: GoRouter not available for forgot password: $e');
                                    // Show a dialog instead
                                    showDialog(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                        title: const Text('Forgot Password'),
                                        content: const Text(
                                            'Please contact support to reset your password.'),
                                        actions: [
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.pop(context),
                                            child: const Text('OK'),
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                },
                                child: const Text('Forgot Password?'),
                              ),
                            ),

                            if (_errorMessage != null) ...[
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border:
                                      Border.all(color: Colors.red.shade200),
                                ),
                                child: Text(
                                  _errorMessage!,
                                  style: TextStyle(
                                    color: Colors.red.shade800,
                                  ),
                                ),
                              ),
                            ],

                            const SizedBox(height: 24),

                            // Login Button
                            ElevatedButton(
                              onPressed: _isLoading ? null : _handleLogin,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: primaryColor,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 2,
                              ),
                              child: _isLoading
                                  ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    )
                                  : const Text(
                                      'Sign In',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Register Button
                  TextButton(
                    onPressed: _onRegisterPressed,
                    child: RichText(
                      text: TextSpan(
                        text: 'Don\'t have an account? ',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withAlpha(204),
                        ),
                        children: [
                          TextSpan(
                            text: 'Sign Up',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
