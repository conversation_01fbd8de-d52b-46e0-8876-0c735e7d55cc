import 'package:flutter/material.dart';
import '../../../shared/models/config/storage_config_model.dart';

/// Dialog for adding or editing a storage provider
class StorageProviderDialog extends StatefulWidget {
  final StorageProviderConfig? provider;

  const StorageProviderDialog({super.key, this.provider});

  @override
  State<StorageProviderDialog> createState() => _StorageProviderDialogState();
}

class _StorageProviderDialogState extends State<StorageProviderDialog> {
  final _formKey = GlobalKey<FormState>();
  late String _name;
  late StorageProviderType _type;
  late bool _isEnabled;
  late Map<String, dynamic> _settings;

  // Google Drive specific settings
  String _apiKey = '';
  String _folderId = '';

  // Local storage specific settings
  String _basePath = '';

  @override
  void initState() {
    super.initState();

    // Initialize with existing provider or defaults
    if (widget.provider != null) {
      _name = widget.provider!.name;
      _type = widget.provider!.type;
      _isEnabled = widget.provider!.isEnabled;
      _settings = Map<String, dynamic>.from(widget.provider!.settings);

      // Extract specific settings based on type
      if (_type == StorageProviderType.googleDrive) {
        _apiKey = _settings['apiKey'] as String? ?? '';
        _folderId = _settings['folderId'] as String? ?? '';
      } else if (_type == StorageProviderType.local) {
        _basePath = _settings['basePath'] as String? ?? '';
      }
    } else {
      _name = '';
      _type = StorageProviderType.local;
      _isEnabled = true;
      _settings = {};
      _basePath = 'storage';
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.provider == null ? 'Add Storage Provider' : 'Edit Storage Provider'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Provider Name',
                  hintText: 'e.g., local, google_drive, etc.',
                  border: OutlineInputBorder(),
                ),
                initialValue: _name,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
                onChanged: (value) {
                  _name = value;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<StorageProviderType>(
                decoration: const InputDecoration(
                  labelText: 'Provider Type',
                  border: OutlineInputBorder(),
                ),
                value: _type,
                items: StorageProviderType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.toString().split('.').last),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _type = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('Enabled'),
                value: _isEnabled,
                onChanged: (value) {
                  setState(() {
                    _isEnabled = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              const Text(
                'Provider Settings',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              // Provider-specific settings
              if (_type == StorageProviderType.googleDrive) ...[
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'API Key',
                    border: OutlineInputBorder(),
                    hintText: 'Your Google Drive API Key',
                    helperText: 'Get this from Google Cloud Console',
                  ),
                  initialValue: _apiKey,
                  onChanged: (value) {
                    _apiKey = value;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Root Folder ID',
                    border: OutlineInputBorder(),
                    hintText: 'Google Drive Folder ID',
                    helperText: 'ID of the folder where files will be stored',
                  ),
                  initialValue: _folderId,
                  onChanged: (value) {
                    _folderId = value;
                  },
                ),
                const SizedBox(height: 16),
                OutlinedButton.icon(
                  icon: const Icon(Icons.help_outline),
                  label: const Text('How to set up Google Drive'),
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Google Drive Setup Instructions'),
                        content: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: const [
                              Text('1. Go to Google Cloud Console'),
                              Text('2. Create a new project'),
                              Text('3. Enable the Google Drive API'),
                              Text('4. Create API credentials'),
                              Text('5. Copy the API Key'),
                              SizedBox(height: 16),
                              Text('For the Folder ID:'),
                              Text('1. Create a folder in Google Drive'),
                              Text('2. Open the folder in your browser'),
                              Text('3. The ID is in the URL after "folders/"'),
                            ],
                          ),
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('Close'),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ] else if (_type == StorageProviderType.local) ...[
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Base Path',
                    border: OutlineInputBorder(),
                    hintText: 'e.g., D:/SHIVISH_STORAGE or /path/to/storage',
                    helperText: 'For Windows paths, use forward slashes (D:/SHIVISH_STORAGE) or escaped backslashes (D:\\\\SHIVISH_STORAGE)',
                  ),
                  initialValue: _basePath,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a base path';
                    }

                    // Check if path is valid
                    if (value.contains('\\') && !value.contains('\\\\')) {
                      return 'Windows paths should use forward slashes (/) or escaped backslashes (\\\\)';
                    }

                    return null;
                  },
                  onChanged: (value) {
                    _basePath = value;
                  },
                ),
              ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _save,
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _save() {
    if (_formKey.currentState!.validate()) {
      // Prepare settings based on provider type
      if (_type == StorageProviderType.googleDrive) {
        _settings = {
          'apiKey': _apiKey,
          'folderId': _folderId,
        };
      } else if (_type == StorageProviderType.local) {
        _settings = {
          'basePath': _basePath,
        };
      }

      // Create provider config
      final provider = StorageProviderConfig(
        type: _type,
        name: _name,
        isEnabled: _isEnabled,
        settings: _settings,
      );

      // Return the provider
      Navigator.of(context).pop(provider);
    }
  }
}
