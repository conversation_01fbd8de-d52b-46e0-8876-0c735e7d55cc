import 'package:flutter/material.dart';
import 'package:shivish/shared/models/payment_gateway.dart';

class PaymentGatewayConfigDialog extends StatefulWidget {
  final PaymentGateway gateway;

  const PaymentGatewayConfigDialog({
    super.key,
    required this.gateway,
  });

  @override
  State<PaymentGatewayConfigDialog> createState() =>
      _PaymentGatewayConfigDialogState();
}

class _PaymentGatewayConfigDialogState
    extends State<PaymentGatewayConfigDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _apiKeyController;
  late TextEditingController _apiSecretController;
  late TextEditingController _merchantIdController;
  late TextEditingController _transactionFeeController;
  late TextEditingController _minimumAmountController;
  late TextEditingController _maximumAmountController;

  @override
  void initState() {
    super.initState();
    _apiKeyController = TextEditingController(
      text: widget.gateway.credentials['api_key'] ?? '',
    );
    _apiSecretController = TextEditingController(
      text: widget.gateway.credentials['api_secret'] ?? '',
    );
    _merchantIdController = TextEditingController(
      text: widget.gateway.credentials['merchant_id'] ?? '',
    );
    _transactionFeeController =
        TextEditingController(text: widget.gateway.transactionFee.toString());
    _minimumAmountController =
        TextEditingController(text: widget.gateway.minimumAmount.toString());
    _maximumAmountController =
        TextEditingController(text: widget.gateway.maximumAmount.toString());
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    _apiSecretController.dispose();
    _merchantIdController.dispose();
    _transactionFeeController.dispose();
    _minimumAmountController.dispose();
    _maximumAmountController.dispose();
    super.dispose();
  }

  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      final updatedGateway = widget.gateway.copyWith(
        credentials: {
          'api_key': _apiKeyController.text,
          'api_secret': _apiSecretController.text,
          'merchant_id': _merchantIdController.text,
        },
        transactionFee: double.parse(_transactionFeeController.text),
        minimumAmount: double.parse(_minimumAmountController.text),
        maximumAmount: double.parse(_maximumAmountController.text),
        updatedAt: DateTime.now(),
      );
      Navigator.of(context).pop(updatedGateway);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      title: Text('Configure ${widget.gateway.name}'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'API Credentials',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _apiKeyController,
                decoration: const InputDecoration(
                  labelText: 'API Key',
                  border: OutlineInputBorder(),
                  hintText: 'Enter API key',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter API key';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _apiSecretController,
                decoration: const InputDecoration(
                  labelText: 'API Secret',
                  border: OutlineInputBorder(),
                  hintText: 'Enter API secret',
                ),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter API secret';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _merchantIdController,
                decoration: const InputDecoration(
                  labelText: 'Merchant ID',
                  border: OutlineInputBorder(),
                  hintText: 'Enter merchant ID',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter merchant ID';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              Text(
                'Transaction Settings',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _transactionFeeController,
                decoration: const InputDecoration(
                  labelText: 'Transaction Fee (%)',
                  border: OutlineInputBorder(),
                  hintText: 'Enter transaction fee percentage',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter transaction fee';
                  }
                  final fee = double.tryParse(value);
                  if (fee == null || fee < 0 || fee > 100) {
                    return 'Please enter a valid fee between 0 and 100';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _minimumAmountController,
                decoration: const InputDecoration(
                  labelText: 'Minimum Amount',
                  border: OutlineInputBorder(),
                  hintText: 'Enter minimum transaction amount',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter minimum amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount < 0) {
                    return 'Please enter a valid amount';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _maximumAmountController,
                decoration: const InputDecoration(
                  labelText: 'Maximum Amount',
                  border: OutlineInputBorder(),
                  hintText: 'Enter maximum transaction amount',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter maximum amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount < 0) {
                    return 'Please enter a valid amount';
                  }
                  final minAmount =
                      double.tryParse(_minimumAmountController.text);
                  if (minAmount != null && amount <= minAmount) {
                    return 'Maximum amount must be greater than minimum amount';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _submitForm,
          child: const Text('Save'),
        ),
      ],
    );
  }
}
