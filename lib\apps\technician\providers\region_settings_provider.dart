import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final regionSettingsProvider =
    AsyncNotifierProvider<RegionSettingsNotifier, Map<String, String>>(() {
  return RegionSettingsNotifier();
});

class RegionSettingsNotifier extends AsyncNotifier<Map<String, String>> {
  late final DatabaseService _databaseService;

  @override
  Future<Map<String, String>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadRegionSettings();
  }

  Future<Map<String, String>> _loadRegionSettings() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('users', userId);
      final settings = userData?['region_settings'] as Map<String, dynamic>?;

      return {
        'region': settings?['region'] as String? ?? 'IN',
        'timezone': settings?['timezone'] as String? ?? 'Asia/Kolkata',
        'currency': settings?['currency'] as String? ?? 'INR',
        'language': settings?['language'] as String? ?? 'en',
        'date_format': settings?['date_format'] as String? ?? 'dd/MM/yyyy',
      };
    } catch (e) {
      debugPrint('Failed to load region settings: $e');
      throw Exception('Failed to load region settings: $e');
    }
  }

  Future<void> saveRegionSettings({
    required String region,
    required String timezone,
    required String currency,
    String? language,
    String? dateFormat,
  }) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Validate inputs
      if (region.isEmpty || timezone.isEmpty || currency.isEmpty) {
        throw Exception('Region, timezone, and currency are required');
      }

      final settings = {
        'region': region,
        'timezone': timezone,
        'currency': currency,
        'language': language ?? 'en',
        'date_format': dateFormat ?? 'dd/MM/yyyy',
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _databaseService.update('users', userId, {
        'region_settings': settings,
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData({
        'region': region,
        'timezone': timezone,
        'currency': currency,
        'language': language ?? 'en',
        'date_format': dateFormat ?? 'dd/MM/yyyy',
      });
    } catch (e) {
      debugPrint('Failed to save region settings: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}
