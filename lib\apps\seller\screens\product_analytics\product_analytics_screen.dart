import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:shivish/apps/seller/presentation/cubits/product_analytics/product_analytics_cubit.dart';
import 'package:shivish/apps/seller/presentation/cubits/product_analytics/product_analytics_state.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';

class ProductAnalyticsScreen extends ConsumerWidget {
  const ProductAnalyticsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsState = ref.watch(productAnalyticsCubitProvider);
    final analyticsCubit = ref.read(productAnalyticsCubitProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Product Analytics'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => analyticsCubit.refreshAnalytics(),
          ),
        ],
      ),
      body: analyticsState.when(
        loading: () => const LoadingIndicator(),
        error: (error, stackTrace) => ErrorMessage(
          message: error.toString(),
          onRetry: () => analyticsCubit.refreshAnalytics(),
        ),
        data: (analytics) => RefreshIndicator(
          onRefresh: () => analyticsCubit.refreshAnalytics(),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildMetricsGrid(analytics),
                const SizedBox(height: 24),
                _buildTrendsGrid(analytics),
                const SizedBox(height: 24),
                _buildTopProducts(analytics),
                const SizedBox(height: 24),
                _buildProductTrends(analytics),
                const SizedBox(height: 24),
                _buildInventoryStatus(analytics),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMetricsGrid(ProductAnalyticsState analytics) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildMetricCard(
          'Total Sales',
          '₹${analytics.totalSales.toStringAsFixed(2)}',
          Icons.attach_money,
          Colors.blue,
        ),
        _buildMetricCard(
          'Total Orders',
          analytics.totalOrders.toString(),
          Icons.shopping_cart,
          Colors.green,
        ),
        _buildMetricCard(
          'Average Order Value',
          '₹${analytics.averageOrderValue.toStringAsFixed(2)}',
          Icons.trending_up,
          Colors.orange,
        ),
        _buildMetricCard(
          'Conversion Rate',
          '${analytics.conversionRate.toStringAsFixed(1)}%',
          Icons.percent,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildTrendsGrid(ProductAnalyticsState analytics) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildTrendCard(
          'Sales Trend',
          analytics.salesTrend,
          Icons.attach_money,
          Colors.blue,
        ),
        _buildTrendCard(
          'Orders Trend',
          analytics.ordersTrend,
          Icons.shopping_cart,
          Colors.green,
        ),
        _buildTrendCard(
          'AOV Trend',
          analytics.aovTrend,
          Icons.trending_up,
          Colors.orange,
        ),
        _buildTrendCard(
          'Conversion Trend',
          analytics.conversionTrend,
          Icons.percent,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendCard(
    String title,
    double trend,
    IconData icon,
    Color color,
  ) {
    final isPositive = trend > 0;
    final trendColor = isPositive ? Colors.green : Colors.red;
    final trendIcon = isPositive ? Icons.arrow_upward : Icons.arrow_downward;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(trendIcon, color: trendColor, size: 20),
                Text(
                  '${trend.abs().toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: trendColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopProducts(ProductAnalyticsState analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Top Products',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: analytics.topProductsData.fold<double>(
                    0,
                    (max, group) => group.barRods.first.toY > max
                        ? group.barRods.first.toY
                        : max,
                  ),
                  barGroups: analytics.topProductsData,
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          return Text('Product ${value.toInt() + 1}');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          return Text('₹${value.toInt()}');
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductTrends(ProductAnalyticsState analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Sales Trend',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          return Text('Day ${value.toInt() + 1}');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          return Text('₹${value.toInt()}');
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: analytics.salesData,
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 3,
                      dotData: const FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventoryStatus(ProductAnalyticsState analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Inventory Status',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: analytics.inventoryStatus.length,
              itemBuilder: (context, index) {
                final status = analytics.inventoryStatus[index];
                return ListTile(
                  title: Text(status.productName),
                  subtitle: LinearProgressIndicator(
                    value: status.stockPercentage / 100,
                    backgroundColor: Colors.grey[200],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      status.stockPercentage < 20
                          ? Colors.red
                          : status.stockPercentage < 50
                              ? Colors.orange
                              : Colors.green,
                    ),
                  ),
                  trailing: Text(
                    '${status.currentStock} units',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
