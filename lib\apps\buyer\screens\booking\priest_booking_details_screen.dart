import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/booking/booking_model.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/booking_provider.dart';
import 'priest_review_screen.dart';

class PriestBookingDetailsScreen extends ConsumerWidget {
  final String bookingId;

  const PriestBookingDetailsScreen({
    super.key,
    required this.bookingId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(authProvider);
    if (user == null) {
      return const Center(child: Text('Please login to view booking details'));
    }

    final bookingAsync = ref.watch(bookingStateNotifierProvider(user.id));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Details'),
      ),
      body: bookingAsync.when(
        data: (bookings) {
          final booking = bookings.firstWhere(
            (b) => b.id == bookingId,
            orElse: () => throw Exception('Booking not found'),
          );

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(context, booking),
                const SizedBox(height: 24),
                _buildServiceDetails(context, booking),
                const SizedBox(height: 24),
                _buildLocationDetails(context, booking),
                const SizedBox(height: 24),
                _buildContactDetails(context, booking),
                const SizedBox(height: 24),
                _buildPaymentDetails(context, booking),
                const SizedBox(height: 24),
                _buildBookingActions(context, booking),
              ],
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, BookingModel booking) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Booking #${booking.bookingNumber}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                _buildStatusChip(context, booking.status),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Date: ${_formatDate(booking.bookingDate)}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            Text(
              'Time: ${_formatTime(booking.startTime)} - ${_formatTime(booking.endTime)}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceDetails(BuildContext context, BookingModel booking) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Service Details',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Services: ${booking.services.join(", ")}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            Text(
              'Provider: ${booking.providerName}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            if (booking.notes != null && booking.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Notes: ${booking.notes}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLocationDetails(BuildContext context, BookingModel booking) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Location Details',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Address: ${booking.serviceLocation.street}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            Text(
              'City: ${booking.serviceLocation.city}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            Text(
              'State: ${booking.serviceLocation.state}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            Text(
              'Pincode: ${booking.serviceLocation.postalCode}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            if (booking.serviceLocation.landmark != null) ...[
              const SizedBox(height: 4),
              Text(
                'Landmark: ${booking.serviceLocation.landmark}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildContactDetails(BuildContext context, BookingModel booking) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contact Details',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Name: ${booking.serviceLocation.contactName}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            Text(
              'Phone: ${booking.serviceLocation.contactPhone}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            if (booking.serviceLocation.contactEmail != null) ...[
              Text(
                'Email: ${booking.serviceLocation.contactEmail}',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetails(BuildContext context, BookingModel booking) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Details',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Subtotal',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                Text(
                  '₹${booking.subtotalAmount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tax',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                Text(
                  '₹${booking.taxAmount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
            if (booking.discountAmount > 0) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Discount',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  Text(
                    '₹${booking.discountAmount.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ],
              ),
            ],
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Amount',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Text(
                  '₹${booking.totalAmount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Payment Status',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                _buildPaymentStatusChip(context, booking.paymentStatus),
              ],
            ),
            if (booking.paymentMethod != null) ...[
              const SizedBox(height: 4),
              Text(
                'Payment Method: ${booking.paymentMethod}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBookingActions(BuildContext context, BookingModel booking) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Booking Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            if (booking.status == BookingStatus.completed &&
                booking.paymentStatus == PaymentStatus.completed)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => PriestReviewScreen(
                          bookingId: bookingId,
                        ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.star),
                  label: const Text('Add Review'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, BookingStatus status) {
    final (color, text) = switch (status) {
      BookingStatus.pending => (Colors.orange, 'Pending'),
      BookingStatus.confirmed => (Colors.blue, 'Confirmed'),
      BookingStatus.inProgress => (Colors.purple, 'In Progress'),
      BookingStatus.completed => (Colors.green, 'Completed'),
      BookingStatus.cancelled => (Colors.red, 'Cancelled'),
      BookingStatus.noShow => (Colors.red, 'No Show'),
      BookingStatus.rescheduled => (Colors.purple, 'Rescheduled'),
      BookingStatus.rejected => (Colors.red, 'Rejected'),
    };

    return Chip(
      label: Text(text),
      backgroundColor: color.withValues(alpha: 0.1),
      labelStyle: TextStyle(color: color),
      side: BorderSide(color: color),
    );
  }

  Widget _buildPaymentStatusChip(BuildContext context, PaymentStatus status) {
    final (color, text) = switch (status) {
      PaymentStatus.pending => (Colors.orange, 'Pending'),
      PaymentStatus.completed => (Colors.green, 'Completed'),
      PaymentStatus.failed => (Colors.red, 'Failed'),
      PaymentStatus.refunded => (Colors.blue, 'Refunded'),
      PaymentStatus.partiallyRefunded => (Colors.purple, 'Partially Refunded'),
    };

    return Chip(
      label: Text(text),
      backgroundColor: color.withValues(alpha: 0.1),
      labelStyle: TextStyle(color: color),
      side: BorderSide(color: color),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
