import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/ticket_booking_models.dart';
import '../../../shared/services/api_config_service.dart';

/// Service for integrating with EasyMyTrip B2B API
/// Configuration is managed through ApiConfigService and can be set from admin app
class EasyMyTripService {
  final ApiConfigService _apiConfigService;
  final http.Client _httpClient;

  // Cache configuration to avoid repeated async calls
  EasyMyTripConfig? _cachedConfig;
  DateTime? _configCacheTime;

  EasyMyTripService({
    required ApiConfigService apiConfigService,
    http.Client? httpClient,
  }) : _apiConfigService = apiConfigService,
       _httpClient = httpClient ?? http.Client();

  /// Get current configuration with caching
  Future<EasyMyTripConfig> _getConfig() async {
    // Cache config for 5 minutes to avoid repeated async calls
    if (_cachedConfig != null &&
        _configCacheTime != null &&
        DateTime.now().difference(_configCacheTime!).inMinutes < 5) {
      return _cachedConfig!;
    }

    _cachedConfig = await _apiConfigService.getEasyMyTripConfig();
    _configCacheTime = DateTime.now();
    return _cachedConfig!;
  }

  /// Get headers for API requests
  Future<Map<String, String>> _getHeaders() async {
    final config = await _getConfig();
    return config.headers;
  }

  // FLIGHT BOOKING METHODS

  /// Search for flights
  Future<FlightSearchResponse> searchFlights(
    FlightSearchRequest request,
  ) async {
    try {
      print(
        'EasyMyTrip: Searching flights: ${request.origin} -> ${request.destination}',
      );

      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.post(
        Uri.parse('${config.baseUrl}/flights/search'),
        headers: headers,
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return FlightSearchResponse.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Flight search failed: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error searching flights: $e');
      rethrow;
    }
  }

  /// Get flight details
  Future<FlightDetails> getFlightDetails(String flightId) async {
    try {
      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.get(
        Uri.parse('${config.baseUrl}/flights/$flightId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return FlightDetails.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Failed to get flight details: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error getting flight details: $e');
      rethrow;
    }
  }

  /// Book a flight
  Future<BookingResponse> bookFlight(FlightBookingRequest request) async {
    try {
      print('EasyMyTrip: Booking flight: ${request.flightId}');

      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.post(
        Uri.parse('${config.baseUrl}/flights/book'),
        headers: headers,
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return BookingResponse.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Flight booking failed: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error booking flight: $e');
      rethrow;
    }
  }

  // BUS BOOKING METHODS

  /// Search for buses
  Future<BusSearchResponse> searchBuses(BusSearchRequest request) async {
    try {
      print(
        'EasyMyTrip: Searching buses: ${request.origin} -> ${request.destination}',
      );

      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.post(
        Uri.parse('${config.baseUrl}/buses/search'),
        headers: headers,
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return BusSearchResponse.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Bus search failed: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error searching buses: $e');
      rethrow;
    }
  }

  /// Book a bus
  Future<BookingResponse> bookBus(BusBookingRequest request) async {
    try {
      print('EasyMyTrip: Booking bus: ${request.busId}');

      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.post(
        Uri.parse('${config.baseUrl}/buses/book'),
        headers: headers,
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return BookingResponse.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Bus booking failed: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error booking bus: $e');
      rethrow;
    }
  }

  // HOTEL BOOKING METHODS

  /// Search for hotels
  Future<HotelSearchResponse> searchHotels(HotelSearchRequest request) async {
    try {
      print('EasyMyTrip: Searching hotels in: ${request.city}');

      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.post(
        Uri.parse('${config.baseUrl}/hotels/search'),
        headers: headers,
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return HotelSearchResponse.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Hotel search failed: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error searching hotels: $e');
      rethrow;
    }
  }

  /// Get hotel details
  Future<HotelDetails> getHotelDetails(String hotelId) async {
    try {
      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.get(
        Uri.parse('${config.baseUrl}/hotels/$hotelId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return HotelDetails.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Failed to get hotel details: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error getting hotel details: $e');
      rethrow;
    }
  }

  /// Book a hotel
  Future<BookingResponse> bookHotel(HotelBookingRequest request) async {
    try {
      print('EasyMyTrip: Booking hotel: ${request.hotelId}');

      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.post(
        Uri.parse('${config.baseUrl}/hotels/book'),
        headers: headers,
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return BookingResponse.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Hotel booking failed: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error booking hotel: $e');
      rethrow;
    }
  }

  // TRAIN BOOKING METHODS

  /// Search for trains
  Future<TrainSearchResponse> searchTrains(TrainSearchRequest request) async {
    try {
      print(
        'EasyMyTrip: Searching trains: ${request.origin} -> ${request.destination}',
      );

      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.post(
        Uri.parse('${config.baseUrl}/trains/search'),
        headers: headers,
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return TrainSearchResponse.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Train search failed: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error searching trains: $e');
      rethrow;
    }
  }

  /// Book a train
  Future<BookingResponse> bookTrain(TrainBookingRequest request) async {
    try {
      print('EasyMyTrip: Booking train: ${request.trainId}');

      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.post(
        Uri.parse('${config.baseUrl}/trains/book'),
        headers: headers,
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return BookingResponse.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Train booking failed: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error booking train: $e');
      rethrow;
    }
  }

  // COMMON METHODS

  /// Get booking status
  Future<BookingStatus> getBookingStatus(String bookingId) async {
    try {
      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.get(
        Uri.parse('${config.baseUrl}/bookings/$bookingId/status'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return BookingStatus.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Failed to get booking status: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error getting booking status: $e');
      rethrow;
    }
  }

  /// Cancel booking
  Future<CancellationResponse> cancelBooking(
    String bookingId,
    String reason,
  ) async {
    try {
      final config = await _getConfig();
      final headers = await _getHeaders();

      final response = await _httpClient.post(
        Uri.parse('${config.baseUrl}/bookings/$bookingId/cancel'),
        headers: headers,
        body: jsonEncode({'reason': reason}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return CancellationResponse.fromJson(data);
      } else {
        throw EasyMyTripException(
          'Failed to cancel booking: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      print('EasyMyTrip Error: Error cancelling booking: $e');
      rethrow;
    }
  }

  /// Check if service is properly configured
  Future<bool> isConfigured() async {
    try {
      return await _apiConfigService.isEasyMyTripConfigured();
    } catch (e) {
      print('EasyMyTrip Error: Failed to check configuration: $e');
      return false;
    }
  }

  /// Get current configuration status
  Future<ApiConfigStatus> getConfigurationStatus() async {
    try {
      return await _apiConfigService.getConfigurationStatus();
    } catch (e) {
      print('EasyMyTrip Error: Failed to get configuration status: $e');
      throw EasyMyTripException(
        'Failed to get configuration status',
        e.toString(),
      );
    }
  }

  /// Clear cached configuration (force reload on next request)
  void clearConfigCache() {
    _cachedConfig = null;
    _configCacheTime = null;
  }

  /// Test API connection with current configuration
  Future<bool> testConnection() async {
    try {
      final config = await _getConfig();

      // Make a simple test request (you can implement a specific test endpoint)
      final response = await _httpClient.get(
        Uri.parse('${config.baseUrl}/health'),
        headers: await _getHeaders(),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('EasyMyTrip Error: Connection test failed: $e');
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    _httpClient.close();
    clearConfigCache();
  }
}

/// Custom exception for EasyMyTrip API errors
class EasyMyTripException implements Exception {
  final String message;
  final String? details;

  EasyMyTripException(this.message, [this.details]);

  @override
  String toString() =>
      'EasyMyTripException: $message${details != null ? '\nDetails: $details' : ''}';
}
