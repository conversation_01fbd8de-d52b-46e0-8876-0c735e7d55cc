import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/models/product/product_model.dart';
import 'package:shivish/shared/services/product/product_service.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';

final productsProvider =
    StateNotifierProvider<ProductsNotifier, AsyncValue<List<ProductModel>>>(
  (ref) => ProductsNotifier(
    ref.watch(productServiceProvider),
    AuthService(),
  ),
);

class ProductsNotifier extends StateNotifier<AsyncValue<List<ProductModel>>> {
  final ProductService _productService;
  final AuthService _authService;

  ProductsNotifier(this._productService, this._authService)
      : super(const AsyncValue.loading()) {
    loadProducts();
  }

  Future<void> loadProducts() async {
    try {
      state = const AsyncValue.loading();
      final sellerId = _authService.currentUser?.id;
      if (sellerId == null) {
        state =
            const AsyncValue.error("User not authenticated", StackTrace.empty);
        return;
      }

      final productsStream = _productService.getProductsBySeller(sellerId);
      final products = await productsStream.first;
      state = AsyncValue.data(products);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addProduct(ProductModel product) async {
    try {
      await _productService.createOrUpdateProduct(product);
      await loadProducts();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateProduct(ProductModel product) async {
    try {
      await _productService.createOrUpdateProduct(product);
      await loadProducts();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteProduct(String id) async {
    try {
      await _productService.deleteProduct(id);
      await loadProducts();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  // Refresh products manually
  Future<void> refreshProducts() async {
    await loadProducts();
  }
}
