import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';
import 'package:shivish/shared/services/storage_service.dart';
import 'dart:io';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

final profilePictureProvider =
    AsyncNotifierProvider<ProfilePictureNotifier, String?>(() {
  return ProfilePictureNotifier();
});

class ProfilePictureNotifier extends AsyncNotifier<String?> {
  late final DatabaseService _databaseService;
  late final StorageService _storageService;

  @override
  Future<String?> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    _storageService = ref.read(storageServiceProvider);
    return _loadProfilePicture();
  }

  Future<String?> _loadProfilePicture() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('users', userId);
      return userData?['profile_image'] as String?;
    } catch (e) {
      debugPrint('Failed to load profile picture: $e');
      throw Exception('Failed to load profile picture: $e');
    }
  }

  Future<void> updateProfilePicture(File imageFile) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Validate image file
      if (!imageFile.existsSync()) {
        throw Exception('Image file does not exist');
      }

      final fileSize = await imageFile.length();
      if (fileSize > 5 * 1024 * 1024) { // 5MB limit
        throw Exception('Image file is too large (max 5MB)');
      }

      // Upload image to storage service
      final fileName = 'profile_pictures/${userId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final downloadUrl = await _storageService.uploadFile(
        file: imageFile,
        path: fileName,
      );

      // Update profile image URL in database
      await _databaseService.update('users', userId, {
        'profile_image': downloadUrl,
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData(downloadUrl);
    } catch (e) {
      debugPrint('Failed to update profile picture: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> removeProfilePicture() async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Remove profile image URL from database
      await _databaseService.update('users', userId, {
        'profile_image': null,
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = const AsyncData(null);
    } catch (e) {
      debugPrint('Failed to remove profile picture: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}
