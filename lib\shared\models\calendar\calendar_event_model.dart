import 'package:freezed_annotation/freezed_annotation.dart';
import 'calendar_event_type.dart';
import 'calendar_event_visibility.dart';
import 'calendar_event_status.dart';
import 'calendar_event_recurrence.dart';
import 'calendar_event_reminder.dart';
import 'event_product_model.dart';

part 'calendar_event_model.freezed.dart';
part 'calendar_event_model.g.dart';

/// Model for calendar events
@freezed
abstract class CalendarEventModel with _$CalendarEventModel {
  /// Creates a [CalendarEventModel]
  const factory CalendarEventModel({
    required String id,
    required String title,
    required String description,
    required DateTime startDate,
    required DateTime endDate,
    required CalendarEventType type,
    required CalendarEventVisibility visibility,
    required CalendarEventStatus status,
    required bool isRecurring,
    CalendarEventRecurrence? recurrenceRule,
    required bool hasReminder,
    CalendarEventReminder? reminder,
    required DateTime createdAt,
    required DateTime updatedAt,
    String? userId, // User ID of the event creator
    List<EventProduct>? products,

    // Contact information for greeting messages
    String? contactName,
    String? contactPhone,
    String? relationship,
    bool? sendGreeting,
    String? customGreetingMessage,
  }) = _CalendarEventModel;

  /// Creates a [CalendarEventModel] from JSON
  factory CalendarEventModel.fromJson(Map<String, dynamic> json) =>
      _$CalendarEventModelFromJson(json);

  /// Creates an empty [CalendarEventModel]
  factory CalendarEventModel.empty() => CalendarEventModel(
        id: '',
        title: '',
        description: '',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(hours: 1)),
        type: CalendarEventType.user,
        visibility: CalendarEventVisibility.private,
        status: CalendarEventStatus.scheduled,
        isRecurring: false,
        hasReminder: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        userId: null,
        contactName: null,
        contactPhone: null,
        relationship: null,
        sendGreeting: false,
        customGreetingMessage: null,
      );
}
