import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/domain/models/upi_payment_settings.dart';
import 'package:shivish/apps/seller/domain/models/payment_model.dart';
import 'package:shivish/apps/seller/application/seller_provider.dart';

// Provider for UPI payment settings
final upiPaymentSettingsProvider =
    Provider.family<AsyncValue<UpiPaymentSettings?>, String>((ref, sellerId) {
  final sellerAsync = ref.watch(sellerProvider);

  return sellerAsync.when(
    data: (seller) {
      if (seller == null) {
        return const AsyncValue.data(null);
      }

      final paymentSettings = seller.paymentSettings;
      if (paymentSettings.containsKey('upiSettings')) {
        return AsyncValue.data(
          UpiPaymentSettings(
            upiId: paymentSettings['upiSettings']['upiId'] ?? '',
            isVerified: paymentSettings['upiSettings']['isVerified'] ?? false,
            isEnabled: paymentSettings['upiSettings']['isEnabled'] ?? true,
            verifiedAt: paymentSettings['upiSettings']['verifiedAt'] != null
                ? DateTime.parse(paymentSettings['upiSettings']['verifiedAt'])
                : null,
            updatedAt: paymentSettings['upiSettings']['updatedAt'] != null
                ? DateTime.parse(paymentSettings['upiSettings']['updatedAt'])
                : null,
          ),
        );
      }

      return const AsyncValue.data(null);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Provider for updating UPI payment settings
final updateUpiPaymentSettingsProvider =
    Provider<Future<bool> Function(UpiPaymentSettings)>((ref) {
  return (UpiPaymentSettings settings) async {
    try {
      final sellerAsync = ref.read(sellerProvider);
      final seller = sellerAsync.value;

      if (seller == null) {
        throw Exception('Seller not found');
      }

      // Create a copy of the existing payment settings
      final paymentSettings = Map<String, dynamic>.from(seller.paymentSettings);

      // Update the UPI settings
      paymentSettings['upiSettings'] = {
        'upiId': settings.upiId,
        'isVerified': settings.isVerified,
        'isEnabled': settings.isEnabled,
        'verifiedAt': settings.verifiedAt?.toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // Create an updated seller with the new payment settings
      final updatedSeller = seller.copyWith(
        paymentSettings: paymentSettings,
        updatedAt: DateTime.now(),
      );

      // Update the seller in the database
      await ref.read(sellerProvider.notifier).updateSeller(updatedSeller);

      return true;
    } catch (e) {
      return false;
    }
  };
});

// Provider for bank accounts
final bankAccountsProvider =
    FutureProvider<List<BankAccountModel>>((ref) async {
  final sellerAsync = ref.watch(sellerProvider);

  return sellerAsync.when(
    data: (seller) {
      if (seller == null) {
        return [];
      }

      final paymentSettings = seller.paymentSettings;
      if (paymentSettings.containsKey('bankAccounts')) {
        final accounts = paymentSettings['bankAccounts'] as List<dynamic>;
        return accounts
            .map((account) => BankAccountModel.fromJson(account))
            .toList();
      }

      return [];
    },
    loading: () => [],
    error: (error, stackTrace) => [],
  );
});

// Provider for payment settings
class PaymentSettingsNotifier extends StateNotifier<AsyncValue<void>> {
  final Ref _ref;

  PaymentSettingsNotifier(this._ref) : super(const AsyncValue.data(null));

  Future<void> updateUpiSettings(String upiId) async {
    state = const AsyncValue.loading();

    try {
      final sellerAsync = _ref.read(sellerProvider);
      final seller = sellerAsync.value;

      if (seller == null) {
        throw Exception('Seller not found');
      }

      // Create a copy of the existing payment settings
      final paymentSettings = Map<String, dynamic>.from(seller.paymentSettings);

      // Update the UPI settings
      paymentSettings['upiSettings'] = {
        'upiId': upiId,
        'isVerified': false,
        'isEnabled': true,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // Create an updated seller with the new payment settings
      final updatedSeller = seller.copyWith(
        paymentSettings: paymentSettings,
        updatedAt: DateTime.now(),
      );

      // Update the seller in the database
      await _ref.read(sellerProvider.notifier).updateSeller(updatedSeller);

      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> addBankAccount(BankAccountModel account) async {
    state = const AsyncValue.loading();

    try {
      final sellerAsync = _ref.read(sellerProvider);
      final seller = sellerAsync.value;

      if (seller == null) {
        throw Exception('Seller not found');
      }

      // Create a copy of the existing payment settings
      final paymentSettings = Map<String, dynamic>.from(seller.paymentSettings);

      // Get existing bank accounts or create empty list
      final accounts = paymentSettings.containsKey('bankAccounts')
          ? List<Map<String, dynamic>>.from(paymentSettings['bankAccounts'])
          : <Map<String, dynamic>>[];

      // Add the new account
      accounts.add(account.toJson());

      // Update payment settings
      paymentSettings['bankAccounts'] = accounts;

      // Create an updated seller with the new payment settings
      final updatedSeller = seller.copyWith(
        paymentSettings: paymentSettings,
        updatedAt: DateTime.now(),
      );

      // Update the seller in the database
      await _ref.read(sellerProvider.notifier).updateSeller(updatedSeller);

      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> updateBankAccount(BankAccountModel account) async {
    state = const AsyncValue.loading();

    try {
      final sellerAsync = _ref.read(sellerProvider);
      final seller = sellerAsync.value;

      if (seller == null) {
        throw Exception('Seller not found');
      }

      // Create a copy of the existing payment settings
      final paymentSettings = Map<String, dynamic>.from(seller.paymentSettings);

      // Get existing bank accounts
      if (!paymentSettings.containsKey('bankAccounts')) {
        throw Exception('No bank accounts found');
      }

      final accounts =
          List<Map<String, dynamic>>.from(paymentSettings['bankAccounts']);

      // Find and update the account
      final index = accounts.indexWhere((a) => a['id'] == account.id);
      if (index == -1) {
        throw Exception('Bank account not found');
      }

      accounts[index] = account.toJson();

      // Update payment settings
      paymentSettings['bankAccounts'] = accounts;

      // Create an updated seller with the new payment settings
      final updatedSeller = seller.copyWith(
        paymentSettings: paymentSettings,
        updatedAt: DateTime.now(),
      );

      // Update the seller in the database
      await _ref.read(sellerProvider.notifier).updateSeller(updatedSeller);

      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> deleteBankAccount(String accountId) async {
    state = const AsyncValue.loading();

    try {
      final sellerAsync = _ref.read(sellerProvider);
      final seller = sellerAsync.value;

      if (seller == null) {
        throw Exception('Seller not found');
      }

      // Create a copy of the existing payment settings
      final paymentSettings = Map<String, dynamic>.from(seller.paymentSettings);

      // Get existing bank accounts
      if (!paymentSettings.containsKey('bankAccounts')) {
        throw Exception('No bank accounts found');
      }

      final accounts =
          List<Map<String, dynamic>>.from(paymentSettings['bankAccounts']);

      // Remove the account
      final newAccounts = accounts.where((a) => a['id'] != accountId).toList();

      // Update payment settings
      paymentSettings['bankAccounts'] = newAccounts;

      // Create an updated seller with the new payment settings
      final updatedSeller = seller.copyWith(
        paymentSettings: paymentSettings,
        updatedAt: DateTime.now(),
      );

      // Update the seller in the database
      await _ref.read(sellerProvider.notifier).updateSeller(updatedSeller);

      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> setPrimaryBankAccount(String accountId) async {
    state = const AsyncValue.loading();

    try {
      final sellerAsync = _ref.read(sellerProvider);
      final seller = sellerAsync.value;

      if (seller == null) {
        throw Exception('Seller not found');
      }

      // Create a copy of the existing payment settings
      final paymentSettings = Map<String, dynamic>.from(seller.paymentSettings);

      // Get existing bank accounts
      if (!paymentSettings.containsKey('bankAccounts')) {
        throw Exception('No bank accounts found');
      }

      final accounts =
          List<Map<String, dynamic>>.from(paymentSettings['bankAccounts']);

      // Update all accounts to not be primary
      final updatedAccounts = accounts.map((account) {
        account['isPrimary'] = account['id'] == accountId;
        return account;
      }).toList();

      // Update payment settings
      paymentSettings['bankAccounts'] = updatedAccounts;

      // Create an updated seller with the new payment settings
      final updatedSeller = seller.copyWith(
        paymentSettings: paymentSettings,
        updatedAt: DateTime.now(),
      );

      // Update the seller in the database
      await _ref.read(sellerProvider.notifier).updateSeller(updatedSeller);

      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }
}

// Provider for payment settings notifier
final paymentSettingsProvider =
    StateNotifierProvider<PaymentSettingsNotifier, AsyncValue<void>>((ref) {
  return PaymentSettingsNotifier(ref);
});
