// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'price_management_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PriceHistoryEntry {

 String get productId; String get productName; double get oldPrice; double get newPrice; String get reason; String get updatedBy; DateTime get timestamp;
/// Create a copy of PriceHistoryEntry
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PriceHistoryEntryCopyWith<PriceHistoryEntry> get copyWith => _$PriceHistoryEntryCopyWithImpl<PriceHistoryEntry>(this as PriceHistoryEntry, _$identity);

  /// Serializes this PriceHistoryEntry to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PriceHistoryEntry&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.oldPrice, oldPrice) || other.oldPrice == oldPrice)&&(identical(other.newPrice, newPrice) || other.newPrice == newPrice)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.updatedBy, updatedBy) || other.updatedBy == updatedBy)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,productName,oldPrice,newPrice,reason,updatedBy,timestamp);

@override
String toString() {
  return 'PriceHistoryEntry(productId: $productId, productName: $productName, oldPrice: $oldPrice, newPrice: $newPrice, reason: $reason, updatedBy: $updatedBy, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class $PriceHistoryEntryCopyWith<$Res>  {
  factory $PriceHistoryEntryCopyWith(PriceHistoryEntry value, $Res Function(PriceHistoryEntry) _then) = _$PriceHistoryEntryCopyWithImpl;
@useResult
$Res call({
 String productId, String productName, double oldPrice, double newPrice, String reason, String updatedBy, DateTime timestamp
});




}
/// @nodoc
class _$PriceHistoryEntryCopyWithImpl<$Res>
    implements $PriceHistoryEntryCopyWith<$Res> {
  _$PriceHistoryEntryCopyWithImpl(this._self, this._then);

  final PriceHistoryEntry _self;
  final $Res Function(PriceHistoryEntry) _then;

/// Create a copy of PriceHistoryEntry
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? productId = null,Object? productName = null,Object? oldPrice = null,Object? newPrice = null,Object? reason = null,Object? updatedBy = null,Object? timestamp = null,}) {
  return _then(_self.copyWith(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,oldPrice: null == oldPrice ? _self.oldPrice : oldPrice // ignore: cast_nullable_to_non_nullable
as double,newPrice: null == newPrice ? _self.newPrice : newPrice // ignore: cast_nullable_to_non_nullable
as double,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,updatedBy: null == updatedBy ? _self.updatedBy : updatedBy // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [PriceHistoryEntry].
extension PriceHistoryEntryPatterns on PriceHistoryEntry {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PriceHistoryEntry value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PriceHistoryEntry() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PriceHistoryEntry value)  $default,){
final _that = this;
switch (_that) {
case _PriceHistoryEntry():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PriceHistoryEntry value)?  $default,){
final _that = this;
switch (_that) {
case _PriceHistoryEntry() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String productId,  String productName,  double oldPrice,  double newPrice,  String reason,  String updatedBy,  DateTime timestamp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PriceHistoryEntry() when $default != null:
return $default(_that.productId,_that.productName,_that.oldPrice,_that.newPrice,_that.reason,_that.updatedBy,_that.timestamp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String productId,  String productName,  double oldPrice,  double newPrice,  String reason,  String updatedBy,  DateTime timestamp)  $default,) {final _that = this;
switch (_that) {
case _PriceHistoryEntry():
return $default(_that.productId,_that.productName,_that.oldPrice,_that.newPrice,_that.reason,_that.updatedBy,_that.timestamp);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String productId,  String productName,  double oldPrice,  double newPrice,  String reason,  String updatedBy,  DateTime timestamp)?  $default,) {final _that = this;
switch (_that) {
case _PriceHistoryEntry() when $default != null:
return $default(_that.productId,_that.productName,_that.oldPrice,_that.newPrice,_that.reason,_that.updatedBy,_that.timestamp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PriceHistoryEntry implements PriceHistoryEntry {
  const _PriceHistoryEntry({required this.productId, required this.productName, required this.oldPrice, required this.newPrice, required this.reason, required this.updatedBy, required this.timestamp});
  factory _PriceHistoryEntry.fromJson(Map<String, dynamic> json) => _$PriceHistoryEntryFromJson(json);

@override final  String productId;
@override final  String productName;
@override final  double oldPrice;
@override final  double newPrice;
@override final  String reason;
@override final  String updatedBy;
@override final  DateTime timestamp;

/// Create a copy of PriceHistoryEntry
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PriceHistoryEntryCopyWith<_PriceHistoryEntry> get copyWith => __$PriceHistoryEntryCopyWithImpl<_PriceHistoryEntry>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PriceHistoryEntryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PriceHistoryEntry&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.oldPrice, oldPrice) || other.oldPrice == oldPrice)&&(identical(other.newPrice, newPrice) || other.newPrice == newPrice)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.updatedBy, updatedBy) || other.updatedBy == updatedBy)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,productId,productName,oldPrice,newPrice,reason,updatedBy,timestamp);

@override
String toString() {
  return 'PriceHistoryEntry(productId: $productId, productName: $productName, oldPrice: $oldPrice, newPrice: $newPrice, reason: $reason, updatedBy: $updatedBy, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class _$PriceHistoryEntryCopyWith<$Res> implements $PriceHistoryEntryCopyWith<$Res> {
  factory _$PriceHistoryEntryCopyWith(_PriceHistoryEntry value, $Res Function(_PriceHistoryEntry) _then) = __$PriceHistoryEntryCopyWithImpl;
@override @useResult
$Res call({
 String productId, String productName, double oldPrice, double newPrice, String reason, String updatedBy, DateTime timestamp
});




}
/// @nodoc
class __$PriceHistoryEntryCopyWithImpl<$Res>
    implements _$PriceHistoryEntryCopyWith<$Res> {
  __$PriceHistoryEntryCopyWithImpl(this._self, this._then);

  final _PriceHistoryEntry _self;
  final $Res Function(_PriceHistoryEntry) _then;

/// Create a copy of PriceHistoryEntry
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? productId = null,Object? productName = null,Object? oldPrice = null,Object? newPrice = null,Object? reason = null,Object? updatedBy = null,Object? timestamp = null,}) {
  return _then(_PriceHistoryEntry(
productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productName: null == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String,oldPrice: null == oldPrice ? _self.oldPrice : oldPrice // ignore: cast_nullable_to_non_nullable
as double,newPrice: null == newPrice ? _self.newPrice : newPrice // ignore: cast_nullable_to_non_nullable
as double,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,updatedBy: null == updatedBy ? _self.updatedBy : updatedBy // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

/// @nodoc
mixin _$PriceManagementState {

 List<ProductModel> get products; List<PriceHistoryEntry> get priceHistory; bool get isLoading; String? get error;
/// Create a copy of PriceManagementState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PriceManagementStateCopyWith<PriceManagementState> get copyWith => _$PriceManagementStateCopyWithImpl<PriceManagementState>(this as PriceManagementState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PriceManagementState&&const DeepCollectionEquality().equals(other.products, products)&&const DeepCollectionEquality().equals(other.priceHistory, priceHistory)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(products),const DeepCollectionEquality().hash(priceHistory),isLoading,error);

@override
String toString() {
  return 'PriceManagementState(products: $products, priceHistory: $priceHistory, isLoading: $isLoading, error: $error)';
}


}

/// @nodoc
abstract mixin class $PriceManagementStateCopyWith<$Res>  {
  factory $PriceManagementStateCopyWith(PriceManagementState value, $Res Function(PriceManagementState) _then) = _$PriceManagementStateCopyWithImpl;
@useResult
$Res call({
 List<ProductModel> products, List<PriceHistoryEntry> priceHistory, bool isLoading, String? error
});




}
/// @nodoc
class _$PriceManagementStateCopyWithImpl<$Res>
    implements $PriceManagementStateCopyWith<$Res> {
  _$PriceManagementStateCopyWithImpl(this._self, this._then);

  final PriceManagementState _self;
  final $Res Function(PriceManagementState) _then;

/// Create a copy of PriceManagementState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? products = null,Object? priceHistory = null,Object? isLoading = null,Object? error = freezed,}) {
  return _then(_self.copyWith(
products: null == products ? _self.products : products // ignore: cast_nullable_to_non_nullable
as List<ProductModel>,priceHistory: null == priceHistory ? _self.priceHistory : priceHistory // ignore: cast_nullable_to_non_nullable
as List<PriceHistoryEntry>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [PriceManagementState].
extension PriceManagementStatePatterns on PriceManagementState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PriceManagementState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PriceManagementState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PriceManagementState value)  $default,){
final _that = this;
switch (_that) {
case _PriceManagementState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PriceManagementState value)?  $default,){
final _that = this;
switch (_that) {
case _PriceManagementState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<ProductModel> products,  List<PriceHistoryEntry> priceHistory,  bool isLoading,  String? error)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PriceManagementState() when $default != null:
return $default(_that.products,_that.priceHistory,_that.isLoading,_that.error);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<ProductModel> products,  List<PriceHistoryEntry> priceHistory,  bool isLoading,  String? error)  $default,) {final _that = this;
switch (_that) {
case _PriceManagementState():
return $default(_that.products,_that.priceHistory,_that.isLoading,_that.error);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<ProductModel> products,  List<PriceHistoryEntry> priceHistory,  bool isLoading,  String? error)?  $default,) {final _that = this;
switch (_that) {
case _PriceManagementState() when $default != null:
return $default(_that.products,_that.priceHistory,_that.isLoading,_that.error);case _:
  return null;

}
}

}

/// @nodoc


class _PriceManagementState implements PriceManagementState {
  const _PriceManagementState({final  List<ProductModel> products = const [], final  List<PriceHistoryEntry> priceHistory = const [], this.isLoading = false, this.error}): _products = products,_priceHistory = priceHistory;
  

 final  List<ProductModel> _products;
@override@JsonKey() List<ProductModel> get products {
  if (_products is EqualUnmodifiableListView) return _products;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_products);
}

 final  List<PriceHistoryEntry> _priceHistory;
@override@JsonKey() List<PriceHistoryEntry> get priceHistory {
  if (_priceHistory is EqualUnmodifiableListView) return _priceHistory;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_priceHistory);
}

@override@JsonKey() final  bool isLoading;
@override final  String? error;

/// Create a copy of PriceManagementState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PriceManagementStateCopyWith<_PriceManagementState> get copyWith => __$PriceManagementStateCopyWithImpl<_PriceManagementState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PriceManagementState&&const DeepCollectionEquality().equals(other._products, _products)&&const DeepCollectionEquality().equals(other._priceHistory, _priceHistory)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_products),const DeepCollectionEquality().hash(_priceHistory),isLoading,error);

@override
String toString() {
  return 'PriceManagementState(products: $products, priceHistory: $priceHistory, isLoading: $isLoading, error: $error)';
}


}

/// @nodoc
abstract mixin class _$PriceManagementStateCopyWith<$Res> implements $PriceManagementStateCopyWith<$Res> {
  factory _$PriceManagementStateCopyWith(_PriceManagementState value, $Res Function(_PriceManagementState) _then) = __$PriceManagementStateCopyWithImpl;
@override @useResult
$Res call({
 List<ProductModel> products, List<PriceHistoryEntry> priceHistory, bool isLoading, String? error
});




}
/// @nodoc
class __$PriceManagementStateCopyWithImpl<$Res>
    implements _$PriceManagementStateCopyWith<$Res> {
  __$PriceManagementStateCopyWithImpl(this._self, this._then);

  final _PriceManagementState _self;
  final $Res Function(_PriceManagementState) _then;

/// Create a copy of PriceManagementState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? products = null,Object? priceHistory = null,Object? isLoading = null,Object? error = freezed,}) {
  return _then(_PriceManagementState(
products: null == products ? _self._products : products // ignore: cast_nullable_to_non_nullable
as List<ProductModel>,priceHistory: null == priceHistory ? _self._priceHistory : priceHistory // ignore: cast_nullable_to_non_nullable
as List<PriceHistoryEntry>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc
mixin _$PriceManagementEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PriceManagementEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PriceManagementEvent()';
}


}

/// @nodoc
class $PriceManagementEventCopyWith<$Res>  {
$PriceManagementEventCopyWith(PriceManagementEvent _, $Res Function(PriceManagementEvent) __);
}


/// Adds pattern-matching-related methods to [PriceManagementEvent].
extension PriceManagementEventPatterns on PriceManagementEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _LoadPrices value)?  loadPrices,TResult Function( _UpdatePrice value)?  updatePrice,TResult Function( _UpdateDiscount value)?  updateDiscount,TResult Function( _BulkUpdatePrices value)?  bulkUpdatePrices,TResult Function( _FilterHistory value)?  filterHistory,required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LoadPrices() when loadPrices != null:
return loadPrices(_that);case _UpdatePrice() when updatePrice != null:
return updatePrice(_that);case _UpdateDiscount() when updateDiscount != null:
return updateDiscount(_that);case _BulkUpdatePrices() when bulkUpdatePrices != null:
return bulkUpdatePrices(_that);case _FilterHistory() when filterHistory != null:
return filterHistory(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _LoadPrices value)  loadPrices,required TResult Function( _UpdatePrice value)  updatePrice,required TResult Function( _UpdateDiscount value)  updateDiscount,required TResult Function( _BulkUpdatePrices value)  bulkUpdatePrices,required TResult Function( _FilterHistory value)  filterHistory,}){
final _that = this;
switch (_that) {
case _LoadPrices():
return loadPrices(_that);case _UpdatePrice():
return updatePrice(_that);case _UpdateDiscount():
return updateDiscount(_that);case _BulkUpdatePrices():
return bulkUpdatePrices(_that);case _FilterHistory():
return filterHistory(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _LoadPrices value)?  loadPrices,TResult? Function( _UpdatePrice value)?  updatePrice,TResult? Function( _UpdateDiscount value)?  updateDiscount,TResult? Function( _BulkUpdatePrices value)?  bulkUpdatePrices,TResult? Function( _FilterHistory value)?  filterHistory,}){
final _that = this;
switch (_that) {
case _LoadPrices() when loadPrices != null:
return loadPrices(_that);case _UpdatePrice() when updatePrice != null:
return updatePrice(_that);case _UpdateDiscount() when updateDiscount != null:
return updateDiscount(_that);case _BulkUpdatePrices() when bulkUpdatePrices != null:
return bulkUpdatePrices(_that);case _FilterHistory() when filterHistory != null:
return filterHistory(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  loadPrices,TResult Function( String productId,  double newPrice)?  updatePrice,TResult Function( String productId,  double discount)?  updateDiscount,TResult Function( BulkUpdateType updateType,  double value)?  bulkUpdatePrices,TResult Function( PriceHistoryFilter filter)?  filterHistory,required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LoadPrices() when loadPrices != null:
return loadPrices();case _UpdatePrice() when updatePrice != null:
return updatePrice(_that.productId,_that.newPrice);case _UpdateDiscount() when updateDiscount != null:
return updateDiscount(_that.productId,_that.discount);case _BulkUpdatePrices() when bulkUpdatePrices != null:
return bulkUpdatePrices(_that.updateType,_that.value);case _FilterHistory() when filterHistory != null:
return filterHistory(_that.filter);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  loadPrices,required TResult Function( String productId,  double newPrice)  updatePrice,required TResult Function( String productId,  double discount)  updateDiscount,required TResult Function( BulkUpdateType updateType,  double value)  bulkUpdatePrices,required TResult Function( PriceHistoryFilter filter)  filterHistory,}) {final _that = this;
switch (_that) {
case _LoadPrices():
return loadPrices();case _UpdatePrice():
return updatePrice(_that.productId,_that.newPrice);case _UpdateDiscount():
return updateDiscount(_that.productId,_that.discount);case _BulkUpdatePrices():
return bulkUpdatePrices(_that.updateType,_that.value);case _FilterHistory():
return filterHistory(_that.filter);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  loadPrices,TResult? Function( String productId,  double newPrice)?  updatePrice,TResult? Function( String productId,  double discount)?  updateDiscount,TResult? Function( BulkUpdateType updateType,  double value)?  bulkUpdatePrices,TResult? Function( PriceHistoryFilter filter)?  filterHistory,}) {final _that = this;
switch (_that) {
case _LoadPrices() when loadPrices != null:
return loadPrices();case _UpdatePrice() when updatePrice != null:
return updatePrice(_that.productId,_that.newPrice);case _UpdateDiscount() when updateDiscount != null:
return updateDiscount(_that.productId,_that.discount);case _BulkUpdatePrices() when bulkUpdatePrices != null:
return bulkUpdatePrices(_that.updateType,_that.value);case _FilterHistory() when filterHistory != null:
return filterHistory(_that.filter);case _:
  return null;

}
}

}

/// @nodoc


class _LoadPrices implements PriceManagementEvent {
  const _LoadPrices();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadPrices);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PriceManagementEvent.loadPrices()';
}


}




/// @nodoc


class _UpdatePrice implements PriceManagementEvent {
  const _UpdatePrice(this.productId, this.newPrice);
  

 final  String productId;
 final  double newPrice;

/// Create a copy of PriceManagementEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdatePriceCopyWith<_UpdatePrice> get copyWith => __$UpdatePriceCopyWithImpl<_UpdatePrice>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdatePrice&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.newPrice, newPrice) || other.newPrice == newPrice));
}


@override
int get hashCode => Object.hash(runtimeType,productId,newPrice);

@override
String toString() {
  return 'PriceManagementEvent.updatePrice(productId: $productId, newPrice: $newPrice)';
}


}

/// @nodoc
abstract mixin class _$UpdatePriceCopyWith<$Res> implements $PriceManagementEventCopyWith<$Res> {
  factory _$UpdatePriceCopyWith(_UpdatePrice value, $Res Function(_UpdatePrice) _then) = __$UpdatePriceCopyWithImpl;
@useResult
$Res call({
 String productId, double newPrice
});




}
/// @nodoc
class __$UpdatePriceCopyWithImpl<$Res>
    implements _$UpdatePriceCopyWith<$Res> {
  __$UpdatePriceCopyWithImpl(this._self, this._then);

  final _UpdatePrice _self;
  final $Res Function(_UpdatePrice) _then;

/// Create a copy of PriceManagementEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? productId = null,Object? newPrice = null,}) {
  return _then(_UpdatePrice(
null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,null == newPrice ? _self.newPrice : newPrice // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

/// @nodoc


class _UpdateDiscount implements PriceManagementEvent {
  const _UpdateDiscount(this.productId, this.discount);
  

 final  String productId;
 final  double discount;

/// Create a copy of PriceManagementEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateDiscountCopyWith<_UpdateDiscount> get copyWith => __$UpdateDiscountCopyWithImpl<_UpdateDiscount>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateDiscount&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.discount, discount) || other.discount == discount));
}


@override
int get hashCode => Object.hash(runtimeType,productId,discount);

@override
String toString() {
  return 'PriceManagementEvent.updateDiscount(productId: $productId, discount: $discount)';
}


}

/// @nodoc
abstract mixin class _$UpdateDiscountCopyWith<$Res> implements $PriceManagementEventCopyWith<$Res> {
  factory _$UpdateDiscountCopyWith(_UpdateDiscount value, $Res Function(_UpdateDiscount) _then) = __$UpdateDiscountCopyWithImpl;
@useResult
$Res call({
 String productId, double discount
});




}
/// @nodoc
class __$UpdateDiscountCopyWithImpl<$Res>
    implements _$UpdateDiscountCopyWith<$Res> {
  __$UpdateDiscountCopyWithImpl(this._self, this._then);

  final _UpdateDiscount _self;
  final $Res Function(_UpdateDiscount) _then;

/// Create a copy of PriceManagementEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? productId = null,Object? discount = null,}) {
  return _then(_UpdateDiscount(
null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,null == discount ? _self.discount : discount // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

/// @nodoc


class _BulkUpdatePrices implements PriceManagementEvent {
  const _BulkUpdatePrices(this.updateType, this.value);
  

 final  BulkUpdateType updateType;
 final  double value;

/// Create a copy of PriceManagementEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BulkUpdatePricesCopyWith<_BulkUpdatePrices> get copyWith => __$BulkUpdatePricesCopyWithImpl<_BulkUpdatePrices>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BulkUpdatePrices&&(identical(other.updateType, updateType) || other.updateType == updateType)&&(identical(other.value, value) || other.value == value));
}


@override
int get hashCode => Object.hash(runtimeType,updateType,value);

@override
String toString() {
  return 'PriceManagementEvent.bulkUpdatePrices(updateType: $updateType, value: $value)';
}


}

/// @nodoc
abstract mixin class _$BulkUpdatePricesCopyWith<$Res> implements $PriceManagementEventCopyWith<$Res> {
  factory _$BulkUpdatePricesCopyWith(_BulkUpdatePrices value, $Res Function(_BulkUpdatePrices) _then) = __$BulkUpdatePricesCopyWithImpl;
@useResult
$Res call({
 BulkUpdateType updateType, double value
});




}
/// @nodoc
class __$BulkUpdatePricesCopyWithImpl<$Res>
    implements _$BulkUpdatePricesCopyWith<$Res> {
  __$BulkUpdatePricesCopyWithImpl(this._self, this._then);

  final _BulkUpdatePrices _self;
  final $Res Function(_BulkUpdatePrices) _then;

/// Create a copy of PriceManagementEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? updateType = null,Object? value = null,}) {
  return _then(_BulkUpdatePrices(
null == updateType ? _self.updateType : updateType // ignore: cast_nullable_to_non_nullable
as BulkUpdateType,null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

/// @nodoc


class _FilterHistory implements PriceManagementEvent {
  const _FilterHistory(this.filter);
  

 final  PriceHistoryFilter filter;

/// Create a copy of PriceManagementEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FilterHistoryCopyWith<_FilterHistory> get copyWith => __$FilterHistoryCopyWithImpl<_FilterHistory>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FilterHistory&&(identical(other.filter, filter) || other.filter == filter));
}


@override
int get hashCode => Object.hash(runtimeType,filter);

@override
String toString() {
  return 'PriceManagementEvent.filterHistory(filter: $filter)';
}


}

/// @nodoc
abstract mixin class _$FilterHistoryCopyWith<$Res> implements $PriceManagementEventCopyWith<$Res> {
  factory _$FilterHistoryCopyWith(_FilterHistory value, $Res Function(_FilterHistory) _then) = __$FilterHistoryCopyWithImpl;
@useResult
$Res call({
 PriceHistoryFilter filter
});




}
/// @nodoc
class __$FilterHistoryCopyWithImpl<$Res>
    implements _$FilterHistoryCopyWith<$Res> {
  __$FilterHistoryCopyWithImpl(this._self, this._then);

  final _FilterHistory _self;
  final $Res Function(_FilterHistory) _then;

/// Create a copy of PriceManagementEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? filter = null,}) {
  return _then(_FilterHistory(
null == filter ? _self.filter : filter // ignore: cast_nullable_to_non_nullable
as PriceHistoryFilter,
  ));
}


}

// dart format on
