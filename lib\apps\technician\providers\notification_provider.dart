import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/models/notification/notification_model.dart';
import 'package:shivish/shared/models/notification/notification_status.dart';
import 'package:shivish/shared/services/notification/notification_service.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

final technicianNotificationsProvider = StreamProvider<List<NotificationModel>>(
  (ref) {
    final userId = ref.watch(authProvider)?.id;
    final notificationService = ref.watch(notificationServiceProvider);

    if (userId == null) {
      return Stream.value([]);
    }

    try {
      return notificationService.getNotifications(userId);
    } catch (e) {
      debugPrint('Error streaming notifications: $e');
      return Stream.value([]);
    }
  },
);

/// Provider for unread notifications count
final unreadNotificationsCountProvider = StreamProvider<int>((ref) {
  final notifications = ref.watch(technicianNotificationsProvider);
  return notifications.when(
    data: (notificationList) => Stream.value(
      notificationList
          .where((n) => n.status == NotificationStatus.unread)
          .length,
    ),
    loading: () => Stream.value(0),
    error: (_, __) => Stream.value(0),
  );
});

/// Provider for notification actions
final technicianNotificationActionsProvider = Provider((ref) {
  final notificationService = ref.watch(notificationServiceProvider);
  return TechnicianNotificationActions(notificationService);
});

/// Class for handling notification actions
class TechnicianNotificationActions {
  final NotificationService _notificationService;

  TechnicianNotificationActions(this._notificationService);

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _notificationService.markAsRead(notificationId);
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    }
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _notificationService.deleteNotification(notificationId);
    } catch (e) {
      debugPrint('Error deleting notification: $e');
    }
  }

  /// Clear all notifications for user
  Future<void> clearAllNotifications(String userId) async {
    try {
      await _notificationService.clearAllNotifications(userId);
    } catch (e) {
      debugPrint('Error clearing all notifications: $e');
    }
  }
}
