import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../shared/models/temple/temple_model.dart';
import '../../../shared/models/temple/temple_booking_model.dart';
import '../../../shared/services/temple/temple_service.dart';
import '../../../shared/core/service_locator.dart';

/// Temple state class
@immutable
class TempleState {
  final List<Temple> nearbyTemples;
  final List<Temple> popularTemples;
  final List<Temple> pilgrimageTemples;
  final List<Temple> allTemples;
  final List<Temple> searchResults;
  final List<TempleBooking> userBookings;
  final bool isLoading;
  final String? error;
  final double? userLatitude;
  final double? userLongitude;
  final TempleSearchFilters? currentFilters;

  const TempleState({
    this.nearbyTemples = const [],
    this.popularTemples = const [],
    this.pilgrimageTemples = const [],
    this.allTemples = const [],
    this.searchResults = const [],
    this.userBookings = const [],
    this.isLoading = false,
    this.error,
    this.userLatitude,
    this.userLongitude,
    this.currentFilters,
  });

  TempleState copyWith({
    List<Temple>? nearbyTemples,
    List<Temple>? popularTemples,
    List<Temple>? pilgrimageTemples,
    List<Temple>? allTemples,
    List<Temple>? searchResults,
    List<TempleBooking>? userBookings,
    bool? isLoading,
    String? error,
    double? userLatitude,
    double? userLongitude,
    TempleSearchFilters? currentFilters,
  }) {
    return TempleState(
      nearbyTemples: nearbyTemples ?? this.nearbyTemples,
      popularTemples: popularTemples ?? this.popularTemples,
      pilgrimageTemples: pilgrimageTemples ?? this.pilgrimageTemples,
      allTemples: allTemples ?? this.allTemples,
      searchResults: searchResults ?? this.searchResults,
      userBookings: userBookings ?? this.userBookings,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      userLatitude: userLatitude ?? this.userLatitude,
      userLongitude: userLongitude ?? this.userLongitude,
      currentFilters: currentFilters ?? this.currentFilters,
    );
  }
}

/// Temple notifier class
class TempleNotifier extends StateNotifier<TempleState> {
  final TempleService _templeService;

  TempleNotifier(this._templeService) : super(const TempleState());

  /// Update user location
  void updateUserLocation(double latitude, double longitude) {
    state = state.copyWith(userLatitude: latitude, userLongitude: longitude);
  }

  /// Load nearby temples
  Future<void> loadNearbyTemples() async {
    if (state.userLatitude == null || state.userLongitude == null) {
      return;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final temples = await _templeService.getNearbyTemples(
        state.userLatitude!,
        state.userLongitude!,
        radius: 50, // 50 km radius
      );
      state = state.copyWith(nearbyTemples: temples, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Load popular temples
  Future<void> loadPopularTemples() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temples = await _templeService.getPopularTemples();
      state = state.copyWith(popularTemples: temples, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Load famous temples
  Future<void> loadFamousTemples() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temples = await _templeService.getFamousTemples();
      state = state.copyWith(popularTemples: temples, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Load pilgrimage temples
  Future<void> loadPilgrimageTemples() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temples = await _templeService.getPilgrimageTemples();
      state = state.copyWith(pilgrimageTemples: temples, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Load open temples
  Future<void> loadOpenTemples() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temples = await _templeService.getOpenTemples();
      state = state.copyWith(allTemples: temples, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Search temples
  Future<void> searchTemples(String query) async {
    if (query.trim().isEmpty) {
      state = state.copyWith(searchResults: []);
      return;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final temples = await _templeService.searchTemples(query);
      state = state.copyWith(searchResults: temples, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Apply filters
  Future<void> applyFilters(TempleSearchFilters filters) async {
    state = state.copyWith(
      isLoading: true,
      error: null,
      currentFilters: filters,
    );

    try {
      final temples = await _templeService.getFilteredTemples(filters);
      state = state.copyWith(allTemples: temples, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Load user bookings
  Future<void> loadUserBookings(String userId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final bookings = await _templeService.getUserBookings(userId);
      state = state.copyWith(userBookings: bookings, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Book temple service
  Future<TempleBooking?> bookTempleService({
    required String templeId,
    required List<TempleBookingItem> items,
    required DevoteeInfo devoteeInfo,
    required DateTime visitDate,
    String? userId,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final booking = await _templeService.createBooking(
        templeId: templeId,
        items: items,
        devoteeInfo: devoteeInfo,
        visitDate: visitDate,
        userId: userId,
      );

      // Refresh user bookings
      if (booking != null) {
        final updatedBookings = [...state.userBookings, booking];
        state = state.copyWith(userBookings: updatedBookings, isLoading: false);
      }

      return booking;
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return null;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Temple service provider
final templeServiceProvider = Provider<TempleService>((ref) {
  return serviceLocator<TempleService>();
});

/// Temple provider
final templeProvider = StateNotifierProvider<TempleNotifier, TempleState>((
  ref,
) {
  final templeService = ref.watch(templeServiceProvider);
  return TempleNotifier(templeService);
});

/// Individual temple provider
final templeDetailsProvider = FutureProvider.family<Temple?, String>((
  ref,
  templeId,
) async {
  final templeService = ref.watch(templeServiceProvider);
  return await templeService.getTempleById(templeId);
});

/// Temple booking provider
final templeBookingProvider = FutureProvider.family<TempleBooking?, String>((
  ref,
  bookingId,
) async {
  final templeService = ref.watch(templeServiceProvider);
  return await templeService.getBookingById(bookingId);
});
