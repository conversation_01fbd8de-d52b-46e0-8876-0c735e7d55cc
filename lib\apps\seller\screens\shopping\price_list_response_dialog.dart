import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';
import '../../providers/shopping/shopping_list_provider.dart';

/// Dialog for sellers to respond to a shopping list request with pricing
class PriceListResponseDialog extends ConsumerStatefulWidget {
  /// Creates a [PriceListResponseDialog]
  const PriceListResponseDialog({
    required this.list,
    super.key,
  });

  /// The shopping list to respond to
  final ShoppingListModel list;

  @override
  ConsumerState<PriceListResponseDialog> createState() =>
      _PriceListResponseDialogState();
}

class _PriceListResponseDialogState
    extends ConsumerState<PriceListResponseDialog> {
  String _priceType = 'itemized';
  double _overallTotalPrice = 0.0;
  final List<ShoppingListItem> _updatedItems = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeItems();
  }

  void _initializeItems() {
    // Create a copy of the items with initial prices
    _updatedItems.clear();
    for (final item in widget.list.items) {
      _updatedItems.add(item);
    }

    // Calculate initial total price
    _overallTotalPrice = _updatedItems.fold(
        0.0, (total, item) => total + (item.price * item.quantity));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.maxFinite,
        constraints: const BoxConstraints(maxWidth: 500),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Respond to Shopping List Request',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'List: ${widget.list.name}',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            // Price type selection
            Text(
              'Price Type',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Itemized Pricing'),
                    subtitle: const Text('Set price for each item'),
                    value: 'itemized',
                    groupValue: _priceType,
                    onChanged: (value) {
                      setState(() {
                        _priceType = value!;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Total Price Only'),
                    subtitle: const Text('Set one overall price'),
                    value: 'total_only',
                    groupValue: _priceType,
                    onChanged: (value) {
                      setState(() {
                        _priceType = value!;
                      });
                    },
                  ),
                ),
              ],
            ),

            const Divider(),

            // Different UI based on price type
            if (_priceType == 'itemized')
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _updatedItems.length,
                  itemBuilder: (context, index) {
                    final item = _updatedItems[index];
                    return ListTile(
                      title: Text(item.name),
                      subtitle: Text('Quantity: ${item.quantity}'),
                      trailing: SizedBox(
                        width: 100,
                        child: TextFormField(
                          initialValue: item.price.toString(),
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            labelText: 'Price',
                            prefixText: '\$',
                          ),
                          onChanged: (value) {
                            final price = double.tryParse(value);
                            if (price != null) {
                              setState(() {
                                _updatedItems[index] =
                                    item.copyWith(price: price);
                                _overallTotalPrice = _updatedItems.fold(
                                    0.0,
                                    (total, item) =>
                                        total + (item.price * item.quantity));
                              });
                            }
                          },
                        ),
                      ),
                    );
                  },
                ),
              )
            else
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                child: TextFormField(
                  initialValue: _overallTotalPrice.toString(),
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'Overall Total Price',
                    prefixText: '\$',
                  ),
                  onChanged: (value) {
                    final price = double.tryParse(value);
                    if (price != null) {
                      setState(() {
                        _overallTotalPrice = price;
                      });
                    }
                  },
                ),
              ),

            const Divider(),

            // Total price display
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total Price:',
                    style: theme.textTheme.titleMedium,
                  ),
                  Text(
                    '₹${_overallTotalPrice.toStringAsFixed(2)}',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                FilledButton(
                  onPressed: _isLoading ? null : _sendPriceList,
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Send Price List'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _sendPriceList() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Create updated list with pricing information
      final updatedList = widget.list.copyWith(
        items: _priceType == 'itemized' ? _updatedItems : widget.list.items,
        priceType: _priceType,
        overallTotalPrice:
            _priceType == 'total_only' ? _overallTotalPrice : null,
        totalPrice: _priceType == 'itemized'
            ? _overallTotalPrice
            : widget.list.totalPrice,
        hasPriceList: true,
        priceListViewed: false,
        status: 'priced',
      );

      // Update the shopping list
      await ref
          .read(sellerShoppingListServiceProvider)
          .updateShoppingList(updatedList);

      if (mounted) {
        Navigator.of(context).pop(true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Price list sent successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send price list: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
