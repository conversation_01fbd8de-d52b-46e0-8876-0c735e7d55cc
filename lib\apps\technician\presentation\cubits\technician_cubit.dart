import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../../../../shared/models/technician/technician.dart';
import '../../domain/use_cases/get_technician_use_case.dart';
import '../../domain/use_cases/update_technician_use_case.dart';
import 'technician_cubit_state.dart';

@injectable
class TechnicianCubit extends Cubit<TechnicianCubitState> {
  final GetTechnicianUseCase _getTechnicianUseCase;
  final UpdateTechnicianUseCase _updateTechnicianUseCase;

  TechnicianCubit(
    this._getTechnicianUseCase,
    this._updateTechnicianUseCase,
  ) : super(TechnicianCubitState.initial());

  Future<void> getTechnician(String id) async {
    emit(state.copyWith(isLoading: true, hasError: false));
    try {
      final technician = await _getTechnicianUseCase(id);
      emit(state.copyWith(
        isLoading: false,
        technician: technician,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  void streamTechnician(String id) {
    _getTechnicianUseCase.stream(id).listen(
      (technician) {
        emit(state.copyWith(
          isLoading: false,
          technician: technician,
        ));
      },
      onError: (e) {
        emit(state.copyWith(
          isLoading: false,
          hasError: true,
          errorMessage: e.toString(),
        ));
      },
    );
  }

  Future<void> updateTechnician(Technician technician) async {
    emit(state.copyWith(isLoading: true, hasError: false));
    try {
      await _updateTechnicianUseCase(technician);
      emit(state.copyWith(
        isLoading: false,
        technician: technician,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }
}
