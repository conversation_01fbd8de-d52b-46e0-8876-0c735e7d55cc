import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../providers/infrastructure_provider.dart';
import '../../models/traffic_config.dart';
import '../../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../../shared/ui_components/errors/error_message.dart';

/// Main infrastructure dashboard screen
class InfrastructureDashboardScreen extends ConsumerWidget {
  const InfrastructureDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardState = ref.watch(infrastructureDashboardProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Infrastructure Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            onPressed: () => context.push('/admin/infrastructure/add-server'),
            tooltip: 'Add Server',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => context.push('/admin/infrastructure/settings'),
            tooltip: 'Settings',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refreshAll(ref),
          ),
        ],
      ),
      body: dashboardState.isLoading
          ? const LoadingIndicator()
          : dashboardState.hasError
          ? ErrorMessage(
              message: dashboardState.errorMessage ?? 'Unknown error',
              onRetry: () => _refreshAll(ref),
            )
          : _buildDashboard(context, ref, dashboardState),
    );
  }

  Widget _buildDashboard(
    BuildContext context,
    WidgetRef ref,
    InfrastructureDashboardState state,
  ) {
    // Check if any servers are configured
    final serverConfigsAsync = ref.watch(serverConfigsProvider);

    return serverConfigsAsync.when(
      data: (serverConfigs) {
        if (serverConfigs.isEmpty) {
          return _buildEmptyState(context);
        }
        return _buildDashboardContent(context, state);
      },
      loading: () => const LoadingIndicator(),
      error: (error, stack) => ErrorMessage(
        message: 'Failed to load server configurations: $error',
        onRetry: () => ref.refresh(serverConfigsProvider),
      ),
    );
  }

  Widget _buildDashboardContent(
    BuildContext context,
    InfrastructureDashboardState state,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatusOverview(context, state),
          const SizedBox(height: 24),
          _buildQuickActions(context),
          const SizedBox(height: 24),
          _buildTrafficChart(context, state),
          const SizedBox(height: 24),
          _buildServerHealth(context, state),
          const SizedBox(height: 24),
          _buildRecentAlerts(context, state),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.dns_outlined,
              size: 80,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 24),
            Text(
              'No Servers Configured',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Get started by adding your first server configuration.\nThis will enable traffic routing and load balancing.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => context.push('/admin/infrastructure/add-server'),
              icon: const Icon(Icons.add),
              label: const Text('Add Your First Server'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
            const SizedBox(height: 16),
            OutlinedButton.icon(
              onPressed: () => _showServerSetupGuide(context),
              icon: const Icon(Icons.help_outline),
              label: const Text('Setup Guide'),
            ),
          ],
        ),
      ),
    );
  }

  void _showServerSetupGuide(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Server Setup Guide'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'To set up your infrastructure, you need to configure:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Text('1. Environment (production, staging, development)'),
              Text('2. AWS Region for cloud resources'),
              Text('3. Datacenter IP for private servers'),
              Text('4. Database connection (PostgreSQL)'),
              Text('5. Redis cache connection'),
              Text('6. Performance and security settings'),
              SizedBox(height: 16),
              Text(
                'Each environment should have its own server configuration for proper isolation.',
                style: TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.push('/admin/infrastructure/add-server');
            },
            child: const Text('Add Server'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusOverview(
    BuildContext context,
    InfrastructureDashboardState state,
  ) {
    final trafficConfig = state.trafficConfig.value;
    final serverStatus = state.serverStatus.value;
    final analytics = state.trafficAnalytics.value;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.dashboard, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'System Overview',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatusCard(
                    'Overall Health',
                    state.isHealthy ? 'Healthy' : 'Issues Detected',
                    state.isHealthy ? Colors.green : Colors.red,
                    state.isHealthy ? Icons.check_circle : Icons.error,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatusCard(
                    'Traffic Mode',
                    trafficConfig?.routingMode.displayName ?? 'Unknown',
                    Colors.blue,
                    Icons.traffic,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatusCard(
                    'AWS Status',
                    serverStatus?.awsStatus.displayName ?? 'Unknown',
                    _getServerHealthColor(serverStatus?.awsStatus),
                    Icons.cloud,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatusCard(
                    'Datacenter Status',
                    serverStatus?.datacenterStatus.displayName ?? 'Unknown',
                    _getServerHealthColor(serverStatus?.datacenterStatus),
                    Icons.dns,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatusCard(
                    'Total Requests',
                    '${analytics?.totalRequests ?? 0}',
                    Colors.purple,
                    Icons.analytics,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatusCard(
                    'Critical Alerts',
                    '${state.criticalAlertsCount}',
                    state.criticalAlertsCount > 0 ? Colors.red : Colors.green,
                    Icons.warning,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                _buildActionButton(
                  'Traffic Control',
                  Icons.traffic,
                  Colors.blue,
                  () => context.push('/admin/infrastructure/traffic'),
                ),
                _buildActionButton(
                  'Server Config',
                  Icons.settings,
                  Colors.green,
                  () => context.push('/admin/infrastructure/servers'),
                ),
                _buildActionButton(
                  'Load Balancer',
                  Icons.balance,
                  Colors.orange,
                  () => context.push('/admin/infrastructure/load-balancer'),
                ),
                _buildActionButton(
                  'Auto Scaling',
                  Icons.auto_awesome,
                  Colors.purple,
                  () => context.push('/admin/infrastructure/auto-scaling'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  Widget _buildTrafficChart(
    BuildContext context,
    InfrastructureDashboardState state,
  ) {
    final analytics = state.trafficAnalytics.value;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Traffic Distribution',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: analytics != null
                  ? PieChart(
                      PieChartData(
                        sections: [
                          PieChartSectionData(
                            value: analytics.awsRequests.toDouble(),
                            title: 'AWS\n${analytics.awsRequests}',
                            color: Colors.blue,
                            radius: 80,
                            titleStyle: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          PieChartSectionData(
                            value: analytics.datacenterRequests.toDouble(),
                            title:
                                'Datacenter\n${analytics.datacenterRequests}',
                            color: Colors.green,
                            radius: 80,
                            titleStyle: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                        centerSpaceRadius: 40,
                        sectionsSpace: 2,
                      ),
                    )
                  : const Center(child: Text('No data available')),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServerHealth(
    BuildContext context,
    InfrastructureDashboardState state,
  ) {
    final serverStatus = state.serverStatus.value;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Server Health',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (serverStatus != null) ...[
              Row(
                children: [
                  Expanded(
                    child: _buildHealthIndicator(
                      'AWS Servers',
                      serverStatus.awsStatus,
                      serverStatus.awsServers.length,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildHealthIndicator(
                      'Datacenter Servers',
                      serverStatus.datacenterStatus,
                      serverStatus.datacenterServers.length,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Last checked: ${_formatDateTime(serverStatus.lastChecked)}',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ] else
              const Text('Server status unavailable'),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthIndicator(
    String title,
    ServerHealth health,
    int serverCount,
  ) {
    Color color;
    IconData icon;

    switch (health) {
      case ServerHealth.healthy:
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case ServerHealth.warning:
        color = Colors.orange;
        icon = Icons.warning;
        break;
      case ServerHealth.critical:
        color = Colors.red;
        icon = Icons.error;
        break;
      case ServerHealth.unknown:
        color = Colors.grey;
        icon = Icons.help;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
          Text(
            health.displayName,
            style: TextStyle(color: color, fontWeight: FontWeight.bold),
          ),
          Text(
            '$serverCount servers',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentAlerts(
    BuildContext context,
    InfrastructureDashboardState state,
  ) {
    final alerts = state.alerts.value ?? [];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Recent Alerts',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () => context.push('/admin/infrastructure/alerts'),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (alerts.isEmpty)
              const Text('No recent alerts')
            else
              ...alerts.take(5).map((alert) => _buildAlertItem(alert)),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertItem(InfrastructureAlert alert) {
    return ListTile(
      leading: Icon(_getAlertIcon(alert.severity), color: alert.severity.color),
      title: Text(alert.title),
      subtitle: Text(alert.message),
      trailing: Text(
        _formatDateTime(alert.timestamp),
        style: const TextStyle(fontSize: 12),
      ),
      onTap: () {
        // Handle alert tap
      },
    );
  }

  IconData _getAlertIcon(AlertSeverity severity) {
    switch (severity) {
      case AlertSeverity.critical:
        return Icons.error;
      case AlertSeverity.warning:
        return Icons.warning;
      case AlertSeverity.info:
        return Icons.info;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _refreshAll(WidgetRef ref) {
    ref.invalidate(trafficConfigProvider);
    ref.invalidate(serverStatusProvider);
    ref.invalidate(trafficAnalyticsProvider);
    ref.invalidate(infrastructureAlertsProvider);
    ref.invalidate(serverConfigsProvider);
  }

  /// Get color based on server health status
  Color _getServerHealthColor(ServerHealth? health) {
    switch (health) {
      case ServerHealth.healthy:
        return Colors.green;
      case ServerHealth.warning:
        return Colors.orange;
      case ServerHealth.critical:
        return Colors.red;
      case ServerHealth.unknown:
      case null:
        return Colors.grey;
    }
  }
}
