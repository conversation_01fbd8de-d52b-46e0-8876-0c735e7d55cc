import 'package:flutter/material.dart';

/// Dialog to confirm deletion of an item
class DeleteConfirmationDialog extends StatelessWidget {
  /// Creates a [DeleteConfirmationDialog]
  const DeleteConfirmationDialog({
    required this.itemName,
    super.key,
  });

  /// The name of the item to delete
  final String itemName;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Delete Item'),
      content: Text(
        'Are you sure you want to delete "$itemName"?',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('CANCEL'),
        ),
        FilledButton(
          onPressed: () => Navigator.of(context).pop(true),
          style: FilledButton.styleFrom(
            backgroundColor: Colors.red,
          ),
          child: const Text('DELETE'),
        ),
      ],
    );
  }
}
