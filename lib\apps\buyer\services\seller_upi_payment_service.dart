import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:upi_india/upi_india.dart';
import '../../../shared/models/favorite_seller.dart';
import '../../../shared/models/payment/payment_model.dart';
import '../../../shared/utils/logger.dart';

/// Provider for the seller UPI payment service
final sellerUpiPaymentServiceProvider = Provider<SellerUpiPaymentService>((ref) {
  return SellerUpiPaymentService();
});

/// Service to handle UPI payments to sellers
class SellerUpiPaymentService {
  final UpiIndia _upiIndia = UpiIndia();
  List<UpiApp>? _apps;
  final _logger = getLogger('SellerUpiPaymentService');

  /// Get all available UPI apps on the device
  Future<List<UpiApp>> getUpiApps() async {
    try {
      _logger.info('Getting UPI apps');
      _apps = await _upiIndia.getAllUpiApps();
      return _apps ?? [];
    } catch (e, stackTrace) {
      _logger.severe(
          'Error getting UPI apps\nError: $e\nStack trace: $stackTrace');
      return [];
    }
  }

  /// Start a UPI payment to a seller
  Future<PaymentModel?> startPaymentToSeller({
    required FavoriteSeller seller,
    required double amount,
    required String description,
    required UpiApp app,
  }) async {
    try {
      _logger.info('Starting UPI payment to seller: ${seller.businessName}');
      
      if (seller.upiId == null || seller.upiId!.isEmpty) {
        throw Exception('Seller does not have a UPI ID');
      }

      final String transactionRefId = 'SHIVISH-${DateTime.now().millisecondsSinceEpoch}';
      
      final UpiResponse response = await _upiIndia.startTransaction(
        app: app,
        receiverUpiId: seller.upiId!,
        receiverName: seller.businessName,
        transactionRefId: transactionRefId,
        transactionNote: description,
        amount: amount,
      );

      if (response.status == UpiPaymentStatus.SUCCESS) {
        _logger.info('UPI payment successful to seller: ${seller.businessName}');
        return PaymentModel(
          id: response.transactionId ?? transactionRefId,
          paymentNumber: transactionRefId,
          orderId: transactionRefId,
          customerId: '', // Set by provider
          merchantId: seller.id,
          type: PaymentType.purchase,
          status: PaymentStatus.completed,
          method: PaymentMethod.upi,
          gateway: PaymentGateway.upi,
          amount: amount,
          taxAmount: 0, // Set by provider
          feeAmount: 0, // Set by provider
          totalAmount: amount,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      } else if (response.status == UpiPaymentStatus.FAILURE) {
        _logger.severe(
            'UPI payment failed to seller: ${seller.businessName}\nStatus: ${response.status}');
        throw Exception('Payment failed: ${response.status}');
      } else if (response.status == UpiPaymentStatus.SUBMITTED) {
        _logger.info('UPI payment submitted to seller: ${seller.businessName}');
        return PaymentModel(
          id: response.transactionId ?? transactionRefId,
          paymentNumber: transactionRefId,
          orderId: transactionRefId,
          customerId: '', // Set by provider
          merchantId: seller.id,
          type: PaymentType.purchase,
          status: PaymentStatus.processing,
          method: PaymentMethod.upi,
          gateway: PaymentGateway.upi,
          amount: amount,
          taxAmount: 0, // Set by provider
          feeAmount: 0, // Set by provider
          totalAmount: amount,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      } else {
        _logger.severe(
            'UPI payment to seller ${seller.businessName} returned unknown status: ${response.status}');
        throw Exception('Payment returned unknown status: ${response.status}');
      }
    } on PlatformException catch (e) {
      _logger.severe('Platform exception during UPI payment: ${e.message}');
      throw Exception('UPI payment failed: ${e.message}');
    } catch (e, stackTrace) {
      _logger.severe(
          'Error during UPI payment\nError: $e\nStack trace: $stackTrace');
      throw Exception('UPI payment failed: $e');
    }
  }

  /// Generate a UPI payment URL for a seller
  String generateUpiPaymentUrl({
    required FavoriteSeller seller,
    required double amount,
    required String description,
  }) {
    try {
      if (seller.upiId == null || seller.upiId!.isEmpty) {
        throw Exception('Seller does not have a UPI ID');
      }

      final String transactionRefId = 'SHIVISH-${DateTime.now().millisecondsSinceEpoch}';
      
      // Format: upi://pay?pa=UPI_ID&pn=NAME&am=AMOUNT&tn=NOTE&tr=REFERENCE
      final uriString = "upi://pay?pa=${Uri.encodeComponent(seller.upiId!)}"
          "&pn=${Uri.encodeComponent(seller.businessName)}"
          "&am=${amount.toString()}"
          "&tn=${Uri.encodeComponent(description)}"
          "&tr=${Uri.encodeComponent(transactionRefId)}"
          "&cu=INR";
      
      return uriString;
    } catch (e) {
      _logger.severe('Error generating UPI payment URL: $e');
      throw Exception('Failed to generate UPI payment URL: $e');
    }
  }
}
