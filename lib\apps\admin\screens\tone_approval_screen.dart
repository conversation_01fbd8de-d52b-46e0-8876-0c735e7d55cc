import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/alarm/tone_model.dart';
import '../../../shared/providers/tone_provider.dart';
import '../../../shared/providers/auth_provider.dart';
import '../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../shared/ui_components/errors/error_message.dart';
import '../../../shared/core/navigation/widgets/admin_bottom_nav_bar.dart';
import '../../../shared/utils/logger.dart';
import '../admin_routes.dart';
import '../widgets/tone_creation_drawer.dart';

class ToneApprovalScreen extends ConsumerStatefulWidget {
  const ToneApprovalScreen({super.key});

  @override
  ConsumerState<ToneApprovalScreen> createState() => _ToneApprovalScreenState();
}

class _ToneApprovalScreenState extends ConsumerState<ToneApprovalScreen> {
  @override
  Widget build(BuildContext context) {
    final pendingTonesAsync = ref.watch(pendingTonesProvider);
    final user = ref.watch(authProvider);
    if (user == null) {
      return const Center(child: Text('Please sign in to approve tones'));
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tone Approvals'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we can pop the current route
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else {
              // If we can't pop, use GoRouter to navigate back to tones screen
              context.go(AdminRoutes.tones);
            }
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(pendingTonesProvider);
            },
          ),
        ],
      ),
      bottomNavigationBar: const AdminBottomNavBar(
        currentIndex: 3, // Tones tab is selected
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showToneCreationDrawer();
        },
        child: const Icon(Icons.add),
      ),
      body: pendingTonesAsync.when(
        data: (tones) {
          if (tones.isEmpty) {
            return const Center(child: Text('No pending tones to approve'));
          }

          return ListView.builder(
            itemCount: tones.length,
            itemBuilder: (context, index) {
              final tone = tones[index];
              return _ToneApprovalCard(
                tone: tone,
                onApprove: () => _approveTone(tone.id, user.id),
                onReject: () => _showRejectDialog(tone.id, user.id),
              );
            },
          );
        },
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stack) {
          getLogger(
            'ToneApprovalScreen',
          ).error('Error loading pending tones: $error', error, stack);
          return Center(
            child: ErrorMessage(
              message: 'Failed to load pending tones. Please try again.',
              onRetry: () => ref.invalidate(pendingTonesProvider),
            ),
          );
        },
      ),
    );
  }

  Future<void> _approveTone(String toneId, String adminId) async {
    try {
      await ref.read(
        approveToneProvider({'toneId': toneId, 'adminId': adminId}).future,
      );

      // Refresh the pending tones list
      ref.invalidate(pendingTonesProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tone approved successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to approve tone: $e')));
      }
    }
  }

  Future<void> _showRejectDialog(String toneId, String adminId) async {
    final reasonController = TextEditingController();

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Tone'),
        content: TextField(
          controller: reasonController,
          decoration: const InputDecoration(
            labelText: 'Rejection Reason',
            hintText: 'Enter reason for rejection',
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (reasonController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please provide a rejection reason'),
                  ),
                );
                return;
              }

              try {
                await ref.read(
                  rejectToneProvider({
                    'toneId': toneId,
                    'adminId': adminId,
                    'reason': reasonController.text.trim(),
                  }).future,
                );

                // Refresh the pending tones list
                ref.invalidate(pendingTonesProvider);

                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Tone rejected successfully')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Failed to reject tone: $e')),
                  );
                }
              }
            },
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  void _showToneCreationDrawer() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: ToneCreationDrawer(
          onToneCreated: () {
            // Refresh the tones list after a new tone is created
            ref.invalidate(pendingTonesProvider);
          },
        ),
      ),
    );
  }
}

class _ToneApprovalCard extends StatelessWidget {
  final ToneModel tone;
  final VoidCallback onApprove;
  final VoidCallback onReject;

  const _ToneApprovalCard({
    required this.tone,
    required this.onApprove,
    required this.onReject,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    tone.name,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                Text(
                  tone.category,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Uploaded by: ${tone.uploadedBy}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'Duration: ${tone.duration} seconds',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(onPressed: onReject, child: const Text('Reject')),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: onApprove,
                  child: const Text('Approve'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
