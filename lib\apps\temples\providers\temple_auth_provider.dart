import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../shared/models/temple/temple_model.dart';
import '../../../shared/models/user/user_model.dart';
import '../../../shared/services/auth/auth_service.dart';
import '../../../shared/services/temple/temple_service.dart';
import '../../../shared/core/service_locator.dart';

/// Temple authentication state
@immutable
class TempleAuthState {
  final bool isAuthenticated;
  final Temple? temple;
  final String? userId;
  final bool isLoading;
  final String? error;

  const TempleAuthState({
    this.isAuthenticated = false,
    this.temple,
    this.userId,
    this.isLoading = false,
    this.error,
  });

  TempleAuthState copyWith({
    bool? isAuthenticated,
    Temple? temple,
    String? userId,
    bool? isLoading,
    String? error,
  }) {
    return TempleAuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      temple: temple ?? this.temple,
      userId: userId ?? this.userId,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Temple authentication notifier
class TempleAuthNotifier extends StateNotifier<TempleAuthState> {
  final AuthService _authService;
  final TempleService _templeService;

  TempleAuthNotifier(this._authService, this._templeService)
    : super(const TempleAuthState());

  /// Login temple manager
  Future<bool> login(String email, String password) async {
    final TempleAuthState currentState = state;
    state = currentState.copyWith(isLoading: true, error: null);

    try {
      final user = await _authService.signInWithEmailAndPassword(
        email,
        password,
      );

      if (user != null) {
        // Check if user is a temple manager
        final temple = await _templeService.getTempleByManagerId(user.id);

        if (temple != null) {
          state = state.copyWith(
            isAuthenticated: true,
            temple: temple,
            userId: user.id,
            isLoading: false,
          );
          return true;
        } else {
          state = state.copyWith(
            error: 'No temple found for this account',
            isLoading: false,
          );
          return false;
        }
      } else {
        state = state.copyWith(error: 'Invalid credentials', isLoading: false);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Register new temple
  Future<bool> registerTemple({
    required String templeName,
    required String description,
    required TempleType type,
    required String address,
    required String city,
    required String stateName,
    required String pincode,
    required String phone,
    required String email,
    required List<String> deities,
    required List<String> facilities,
    required String managerName,
    required String managerEmail,
    required String password,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Create user account for temple manager
      // Create user model for temple manager
      final userModel = UserModel(
        id: _generateUserId(),
        email: managerEmail,
        displayName: managerName,
        phoneNumber: phone,
        role: UserRole.templeManager,
        status: UserStatus.active,
        firstName: managerName.split(' ').first,
        lastName: managerName.split(' ').length > 1
            ? managerName.split(' ').last
            : '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final user = await _authService.createUserWithEmailAndPassword(
        managerEmail,
        password,
        userModel,
      );

      if (user != null) {
        // Create temple record
        final temple = Temple(
          id: _generateTempleId(),
          name: templeName,
          description: description,
          type: type,
          status: TempleStatus.pending,
          location: TempleLocation(
            address: address,
            city: city,
            state: stateName,
            country: 'India', // Default country
            pincode: pincode,
            latitude: 0.0, // Will be geocoded later
            longitude: 0.0,
          ),
          contact: TempleContact(
            phone: phone,
            email: email.isNotEmpty ? email : null,
          ),
          weeklyTimings: {
            'monday': const TempleTimings(
              openTime: '06:00',
              closeTime: '20:00',
            ),
            'tuesday': const TempleTimings(
              openTime: '06:00',
              closeTime: '20:00',
            ),
            'wednesday': const TempleTimings(
              openTime: '06:00',
              closeTime: '20:00',
            ),
            'thursday': const TempleTimings(
              openTime: '06:00',
              closeTime: '20:00',
            ),
            'friday': const TempleTimings(
              openTime: '06:00',
              closeTime: '20:00',
            ),
            'saturday': const TempleTimings(
              openTime: '06:00',
              closeTime: '20:00',
            ),
            'sunday': const TempleTimings(
              openTime: '06:00',
              closeTime: '20:00',
            ),
          },
          deities: deities,
          festivals: [],
          darshans: [],
          sevas: [],
          images: [],
          facilities: facilities,
          languages: ['Hindi', 'English'], // Default languages
          rating: 0.0,
          totalReviews: 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          managedBy: user.id,
          managerName: managerName,
        );

        // Save temple to database
        final createdTemple = await _templeService.createTemple(temple);

        if (createdTemple != null) {
          state = state.copyWith(
            isAuthenticated: true,
            temple: temple,
            userId: user.id,
            isLoading: false,
          );
          return true;
        } else {
          // Delete user account if temple creation failed
          await _authService.deleteUser(user.id);
          state = state.copyWith(
            error: 'Failed to create temple record',
            isLoading: false,
          );
          return false;
        }
      } else {
        state = state.copyWith(
          error: 'Failed to create user account',
          isLoading: false,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Logout
  Future<void> logout() async {
    try {
      await _authService.signOut();
      state = const TempleAuthState();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Reset password
  Future<bool> resetPassword(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _authService.resetPassword(email);
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Check authentication status
  Future<void> checkAuthStatus() async {
    state = state.copyWith(isLoading: true);

    try {
      final user = _authService.currentUser;

      if (user != null) {
        final temple = await _templeService.getTempleByManagerId(user.id);

        if (temple != null) {
          state = state.copyWith(
            isAuthenticated: true,
            temple: temple,
            userId: user.id,
            isLoading: false,
          );
        } else {
          state = state.copyWith(isLoading: false);
        }
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Update temple information
  Future<bool> updateTemple(Temple updatedTemple) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _templeService.updateTemple(
        updatedTemple.id,
        updatedTemple,
      );

      if (result != null) {
        state = state.copyWith(temple: updatedTemple, isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          error: 'Failed to update temple',
          isLoading: false,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Generate unique temple ID
  String _generateTempleId() {
    return 'temple_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Generate unique user ID
  String _generateUserId() {
    return 'USR_${DateTime.now().millisecondsSinceEpoch}';
  }
}

/// Temple auth provider
final templeAuthProvider =
    StateNotifierProvider<TempleAuthNotifier, TempleAuthState>((ref) {
      final authService = ref.watch(authServiceProvider);
      final templeService = ref.watch(templeServiceProvider);
      return TempleAuthNotifier(authService, templeService);
    });

/// Auth service provider
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

/// Temple service provider
final templeServiceProvider = Provider<TempleService>((ref) {
  return serviceLocator<TempleService>();
});
