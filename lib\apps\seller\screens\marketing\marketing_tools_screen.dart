import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/presentation/cubits/marketing_cubit.dart';
import 'package:shivish/apps/seller/screens/marketing/campaign_form.dart';
import 'package:shivish/apps/seller/screens/marketing/campaigns_tab.dart';
import 'package:shivish/apps/seller/screens/marketing/promotion_form.dart';
import 'package:shivish/apps/seller/screens/marketing/promotions_tab.dart';

class MarketingToolsScreen extends StatefulWidget {
  const MarketingToolsScreen({super.key});

  @override
  State<MarketingToolsScreen> createState() => _MarketingToolsScreenState();
}

class _MarketingToolsScreenState extends State<MarketingToolsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    final cubit = context.read<MarketingCubit>();
    cubit.loadPromotions();
    cubit.loadCampaigns();
  }

  void _createNew() {
    showDialog(
      context: context,
      builder: (context) => _tabController.index == 0
          ? const PromotionForm()
          : const CampaignForm(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Marketing Tools'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Promotions'),
            Tab(text: 'Campaigns'),
          ],
        ),
      ),
      body: BlocBuilder<MarketingCubit, MarketingState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    state.errorMessage ?? 'An error occurred',
                    style:
                        TextStyle(color: Theme.of(context).colorScheme.error),
                  ),
                  const SizedBox(height: 16),
                  FilledButton(
                    onPressed: _loadData,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: const [
              PromotionsTab(),
              CampaignsTab(),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNew,
        child: const Icon(Icons.add),
      ),
    );
  }
}
