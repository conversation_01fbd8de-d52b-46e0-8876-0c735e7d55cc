import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/admin/bloc/technician/technician_event.dart';
import 'package:shivish/apps/admin/bloc/technician/technician_state.dart';
import 'package:shivish/shared/models/technician/technician.dart';
import 'package:shivish/shared/services/technician_service.dart';
import 'package:shivish/shared/utils/logger.dart';

@injectable
class TechnicianBloc extends Bloc<TechnicianEvent, TechnicianState> {
  final TechnicianService _technicianService;
  final _logger = getLogger('AdminTechnicianBloc');
  List<Technician> _technicians = [];
  Map<String, dynamic> _filters = {};
  static const int _pageSize = 20;
  int _currentOffset = 0;

  TechnicianBloc(this._technicianService)
      : super(const TechnicianState.initial()) {
    on<TechnicianEvent>((event, emit) async {
      await event.when(
        loadTechnicians: () => _onLoadTechnicians(emit),
        loadMoreTechnicians: () => _onLoadMoreTechnicians(emit),
        updateTechnicianStatus: (technician, status, notes, isActive) =>
            _onUpdateTechnicianStatus(
          emit,
          technician,
          status,
          notes,
          isActive,
        ),
        deleteTechnician: (id) => _onDeleteTechnician(emit, id),
        applyFilters: (filters) => _onApplyFilters(emit, filters),
      );
    });
  }

  Future<void> _onLoadTechnicians(Emitter<TechnicianState> emit) async {
    try {
      _logger.info('Loading technicians with filters: $_filters');
      emit(const TechnicianState.loading());
      _currentOffset = 0; // Reset offset for fresh load
      _technicians = await _technicianService.getTechnicians(
        limit: _pageSize,
        filters: _filters,
      );
      _currentOffset = _technicians.length;
      _logger.info('Successfully loaded ${_technicians.length} technicians');
      emit(TechnicianState.loaded(_technicians));
    } catch (e) {
      _logger.severe('Failed to load technicians: $e');
      emit(TechnicianState.error(e.toString()));
    }
  }

  Future<void> _onLoadMoreTechnicians(Emitter<TechnicianState> emit) async {
    try {
      if (_technicians.isEmpty) return;
      _logger.info('Loading more technicians from offset: $_currentOffset');
      emit(TechnicianState.loadingMore(_technicians));

      // For now, we'll reload all technicians with a larger limit
      // This is not optimal but works until we add proper offset support to TechnicianService
      final allTechnicians = await _technicianService.getTechnicians(
        limit: _currentOffset + _pageSize,
        filters: _filters,
      );

      if (allTechnicians.length > _technicians.length) {
        _currentOffset = allTechnicians.length;
        _technicians = allTechnicians;
        _logger.info('Successfully loaded ${allTechnicians.length} total technicians');
        emit(TechnicianState.loaded(_technicians));
      } else {
        _logger.info('No more technicians to load');
        emit(TechnicianState.loaded(_technicians));
      }
    } catch (e) {
      _logger.severe('Failed to load more technicians: $e');
      emit(TechnicianState.error(e.toString()));
    }
  }

  Future<void> _onUpdateTechnicianStatus(
    Emitter<TechnicianState> emit,
    Technician technician,
    String status,
    String? notes,
    bool isActive,
  ) async {
    try {
      _logger.info('Updating technician status: ${technician.id} -> $status (active: $isActive)');
      final updatedTechnician = technician.copyWith(
        verificationStatus: status,
        verificationNotes: notes,
        isActive: isActive,
        updatedAt: DateTime.now(),
      );

      await _technicianService.updateTechnician(updatedTechnician);

      _technicians = _technicians.map((t) {
        return t.id == technician.id ? updatedTechnician : t;
      }).toList();

      _logger.info('Successfully updated technician status: ${technician.id}');
      emit(TechnicianState.loaded(_technicians));
    } catch (e) {
      _logger.severe('Failed to update technician status: $e');
      emit(TechnicianState.error(e.toString()));
    }
  }

  Future<void> _onDeleteTechnician(
    Emitter<TechnicianState> emit,
    String id,
  ) async {
    try {
      _logger.info('Deleting technician: $id');
      await _technicianService.deleteTechnician(id);

      _technicians = _technicians.where((t) => t.id != id).toList();

      _logger.info('Successfully deleted technician: $id');
      emit(TechnicianState.loaded(_technicians));
    } catch (e) {
      _logger.severe('Failed to delete technician: $e');
      emit(TechnicianState.error(e.toString()));
    }
  }

  Future<void> _onApplyFilters(
    Emitter<TechnicianState> emit,
    Map<String, dynamic> filters,
  ) async {
    try {
      _logger.info('Applying filters: $filters');
      _filters = filters;
      _currentOffset = 0; // Reset offset when applying filters
      emit(const TechnicianState.loading());

      _technicians = await _technicianService.getTechnicians(
        limit: _pageSize,
        filters: _filters,
      );

      _currentOffset = _technicians.length;
      _logger.info('Successfully applied filters. Loaded ${_technicians.length} technicians');
      emit(TechnicianState.loaded(_technicians));
    } catch (e) {
      _logger.severe('Failed to apply filters: $e');
      emit(TechnicianState.error(e.toString()));
    }
  }
}
