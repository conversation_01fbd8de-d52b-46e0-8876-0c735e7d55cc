import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../repositories/category_repository.dart';
import '../providers/category_provider.dart';
import 'category_state.dart';

/// Provider for root categories
final rootCategoriesProvider =
    StateNotifierProvider<CategoryNotifier, CategoryState>((ref) {
  final repository = ref.watch(categoryRepositoryProvider);
  return CategoryNotifier(repository)..loadRootCategories();
});

/// Notifier for managing category state
class CategoryNotifier extends StateNotifier<CategoryState> {
  /// Creates a [CategoryNotifier]
  CategoryNotifier(this._repository) : super(const CategoryState.initial());

  final CategoryRepository _repository;

  /// Load all active categories
  Future<void> loadActiveCategories() async {
    state = const CategoryState.loading();

    try {
      final categories = await _repository.getActiveCategories();
      state = CategoryState.loaded(categories: categories);
    } catch (e) {
      state = CategoryState.error(message: e.toString());
    }
  }

  /// Load root categories
  Future<void> loadRootCategories() async {
    state = const CategoryState.loading();

    try {
      final categories = await _repository.getRootCategories();
      state = CategoryState.loaded(categories: categories);
    } catch (e) {
      state = CategoryState.error(message: e.toString());
    }
  }

  /// Load child categories
  Future<void> loadChildCategories(String parentId) async {
    state = const CategoryState.loading();

    try {
      final categories = await _repository.getChildCategories(parentId);
      state = CategoryState.loaded(categories: categories);
    } catch (e) {
      state = CategoryState.error(message: e.toString());
    }
  }

  /// Load categories by priority range
  Future<void> loadCategoriesByPriorityRange({
    required int minPriority,
    required int maxPriority,
  }) async {
    state = const CategoryState.loading();

    try {
      final categories = await _repository.getCategoriesByPriorityRange(
        minPriority: minPriority,
        maxPriority: maxPriority,
      );
      state = CategoryState.loaded(categories: categories);
    } catch (e) {
      state = CategoryState.error(message: e.toString());
    }
  }
}
