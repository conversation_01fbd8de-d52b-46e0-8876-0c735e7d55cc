// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'calendar_event_reminder.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CalendarEventReminder {

/// The type of reminder
 ReminderType get type;/// The time before the event to send the reminder (in minutes)
 int get timeBeforeEvent;/// Whether the reminder is enabled
 bool get isEnabled;/// The custom message for the reminder
 String? get customMessage;
/// Create a copy of CalendarEventReminder
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CalendarEventReminderCopyWith<CalendarEventReminder> get copyWith => _$CalendarEventReminderCopyWithImpl<CalendarEventReminder>(this as CalendarEventReminder, _$identity);

  /// Serializes this CalendarEventReminder to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CalendarEventReminder&&(identical(other.type, type) || other.type == type)&&(identical(other.timeBeforeEvent, timeBeforeEvent) || other.timeBeforeEvent == timeBeforeEvent)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.customMessage, customMessage) || other.customMessage == customMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,timeBeforeEvent,isEnabled,customMessage);

@override
String toString() {
  return 'CalendarEventReminder(type: $type, timeBeforeEvent: $timeBeforeEvent, isEnabled: $isEnabled, customMessage: $customMessage)';
}


}

/// @nodoc
abstract mixin class $CalendarEventReminderCopyWith<$Res>  {
  factory $CalendarEventReminderCopyWith(CalendarEventReminder value, $Res Function(CalendarEventReminder) _then) = _$CalendarEventReminderCopyWithImpl;
@useResult
$Res call({
 ReminderType type, int timeBeforeEvent, bool isEnabled, String? customMessage
});




}
/// @nodoc
class _$CalendarEventReminderCopyWithImpl<$Res>
    implements $CalendarEventReminderCopyWith<$Res> {
  _$CalendarEventReminderCopyWithImpl(this._self, this._then);

  final CalendarEventReminder _self;
  final $Res Function(CalendarEventReminder) _then;

/// Create a copy of CalendarEventReminder
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? timeBeforeEvent = null,Object? isEnabled = null,Object? customMessage = freezed,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ReminderType,timeBeforeEvent: null == timeBeforeEvent ? _self.timeBeforeEvent : timeBeforeEvent // ignore: cast_nullable_to_non_nullable
as int,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,customMessage: freezed == customMessage ? _self.customMessage : customMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [CalendarEventReminder].
extension CalendarEventReminderPatterns on CalendarEventReminder {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CalendarEventReminder value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CalendarEventReminder() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CalendarEventReminder value)  $default,){
final _that = this;
switch (_that) {
case _CalendarEventReminder():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CalendarEventReminder value)?  $default,){
final _that = this;
switch (_that) {
case _CalendarEventReminder() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( ReminderType type,  int timeBeforeEvent,  bool isEnabled,  String? customMessage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CalendarEventReminder() when $default != null:
return $default(_that.type,_that.timeBeforeEvent,_that.isEnabled,_that.customMessage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( ReminderType type,  int timeBeforeEvent,  bool isEnabled,  String? customMessage)  $default,) {final _that = this;
switch (_that) {
case _CalendarEventReminder():
return $default(_that.type,_that.timeBeforeEvent,_that.isEnabled,_that.customMessage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( ReminderType type,  int timeBeforeEvent,  bool isEnabled,  String? customMessage)?  $default,) {final _that = this;
switch (_that) {
case _CalendarEventReminder() when $default != null:
return $default(_that.type,_that.timeBeforeEvent,_that.isEnabled,_that.customMessage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CalendarEventReminder implements CalendarEventReminder {
  const _CalendarEventReminder({required this.type, required this.timeBeforeEvent, this.isEnabled = true, this.customMessage});
  factory _CalendarEventReminder.fromJson(Map<String, dynamic> json) => _$CalendarEventReminderFromJson(json);

/// The type of reminder
@override final  ReminderType type;
/// The time before the event to send the reminder (in minutes)
@override final  int timeBeforeEvent;
/// Whether the reminder is enabled
@override@JsonKey() final  bool isEnabled;
/// The custom message for the reminder
@override final  String? customMessage;

/// Create a copy of CalendarEventReminder
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CalendarEventReminderCopyWith<_CalendarEventReminder> get copyWith => __$CalendarEventReminderCopyWithImpl<_CalendarEventReminder>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CalendarEventReminderToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CalendarEventReminder&&(identical(other.type, type) || other.type == type)&&(identical(other.timeBeforeEvent, timeBeforeEvent) || other.timeBeforeEvent == timeBeforeEvent)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.customMessage, customMessage) || other.customMessage == customMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,timeBeforeEvent,isEnabled,customMessage);

@override
String toString() {
  return 'CalendarEventReminder(type: $type, timeBeforeEvent: $timeBeforeEvent, isEnabled: $isEnabled, customMessage: $customMessage)';
}


}

/// @nodoc
abstract mixin class _$CalendarEventReminderCopyWith<$Res> implements $CalendarEventReminderCopyWith<$Res> {
  factory _$CalendarEventReminderCopyWith(_CalendarEventReminder value, $Res Function(_CalendarEventReminder) _then) = __$CalendarEventReminderCopyWithImpl;
@override @useResult
$Res call({
 ReminderType type, int timeBeforeEvent, bool isEnabled, String? customMessage
});




}
/// @nodoc
class __$CalendarEventReminderCopyWithImpl<$Res>
    implements _$CalendarEventReminderCopyWith<$Res> {
  __$CalendarEventReminderCopyWithImpl(this._self, this._then);

  final _CalendarEventReminder _self;
  final $Res Function(_CalendarEventReminder) _then;

/// Create a copy of CalendarEventReminder
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? timeBeforeEvent = null,Object? isEnabled = null,Object? customMessage = freezed,}) {
  return _then(_CalendarEventReminder(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ReminderType,timeBeforeEvent: null == timeBeforeEvent ? _self.timeBeforeEvent : timeBeforeEvent // ignore: cast_nullable_to_non_nullable
as int,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,customMessage: freezed == customMessage ? _self.customMessage : customMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
