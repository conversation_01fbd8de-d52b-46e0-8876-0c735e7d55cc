import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/alarm/alarm_model.dart';
import '../../../../shared/providers/alarm_settings_provider.dart';
import '../../../../shared/providers/mock_alarm_settings_provider.dart';
import '../../../../shared/providers/alarm_state_provider.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/ui_components/navigation/bottom_nav_back_handler.dart';
import '../../widgets/alarm_list_item.dart';
import '../../widgets/add_alarm_dialog.dart';
import '../../widgets/alarm/countdown_timer_widget.dart';
import '../../widgets/alarm/sleep_analytics_widget.dart';
import '../../widgets/alarm/alarm_drawer_manager.dart';

import '../../buyer_routes.dart';
import 'alarm_settings_screen.dart';

class AlarmScreen extends ConsumerWidget {
  const AlarmScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(authProvider);
    if (user == null) {
      return const Center(child: Text('Please login to view alarms'));
    }

    // Use our state notifier provider for alarms
    final alarms = ref.watch(alarmStateNotifierProvider);

    // Try to use the real alarm settings provider, fall back to mock if it fails
    try {
      ref.watch(alarmSettingsProvider);
    } catch (e) {
      // If the real provider fails, use the mock provider
      ref.watch(mockAlarmSettingsProvider);
    }

    return BottomNavBackHandler(
      fallbackRoute: BuyerRoutes.home,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Alarms'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go(BuyerRoutes.home),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AlarmSettingsScreen(),
                  ),
                );
              },
            ),
          ],
        ),
        body: Row(
          children: [
            // Left side dark UI with countdown and sleep analytics (20-30% width)
            Expanded(
              flex: 35, // 30% of screen width
              child: Container(
                color: Colors.black87,
                child: Column(
                  children: [
                    // Countdown timer for next alarm
                    Expanded(
                      flex: 35, // 40% of left panel
                      child: CountdownTimerWidget(
                        nextAlarm: alarms.isNotEmpty
                            ? alarms.firstWhere(
                                (alarm) => alarm.isEnabled,
                                orElse: () => alarms.first,
                              )
                            : null,
                      ),
                    ),
                    // Sleep analytics graphs
                    Expanded(
                      flex: 70, // 60% of left panel
                      child: const SleepAnalyticsWidget(),
                    ),
                  ],
                ),
              ),
            ),
            // Right side with alarm list (70-80% width)
            Expanded(
              flex: 70, // 70% of screen width
              child: Stack(
                children: [
                  _buildAlarmList(context, ref, alarms),
                  // Three round buttons in the bottom right corner
                  Positioned(
                    right: 16,
                    bottom: 80,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        // Default alarm button
                        FloatingActionButton(
                          heroTag: 'default',
                          backgroundColor: Colors.green,
                          mini: true,
                          child: Image.asset(
                            'assets/icons/defaultalarm.png',
                            width: 28,
                            height: 28,
                            color: Colors.white,
                          ),
                          onPressed: () =>
                              AlarmDrawerManager.showDefaultAlarmDrawer(
                                  context, ref),
                        ),
                        const SizedBox(height: 12),
                        // AI alarm button
                        FloatingActionButton(
                          heroTag: 'ai',
                          backgroundColor: Colors.purple,
                          mini: true,
                          child: Image.asset(
                            'assets/icons/aialarm.png',
                            width: 28,
                            height: 28,
                            color: Colors.white,
                          ),
                          onPressed: () => AlarmDrawerManager.showAIAlarmDrawer(
                              context, ref),
                        ),
                        const SizedBox(height: 12),
                        // Custom alarm button
                        FloatingActionButton(
                          heroTag: 'custom',
                          backgroundColor: Colors.blue,
                          onPressed: () =>
                              AlarmDrawerManager.showCustomAlarmDrawer(
                                  context, ref),
                          child: Image.asset(
                            'assets/icons/customisealarm.png',
                            width: 28,
                            height: 28,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlarmList(
    BuildContext context,
    WidgetRef ref,
    List<AlarmModel> alarms,
  ) {
    if (alarms.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.alarm_off,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No alarms set',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Tap + to add an alarm',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: alarms.length,
      itemBuilder: (context, index) {
        final alarm = alarms[index];
        return AlarmListItem(
          alarm: alarm,
          onToggle: (isEnabled) {
            // Update the alarm state using our state notifier
            ref
                .read(alarmStateNotifierProvider.notifier)
                .toggleAlarm(alarm.id, isEnabled);

            // Show a snackbar to confirm the action
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Alarm ${isEnabled ? 'enabled' : 'disabled'}'),
                duration: const Duration(seconds: 1),
              ),
            );
          },
          onEdit: () => _showEditAlarmDialog(context, ref, alarm),
          onDelete: () => _showDeleteAlarmDialog(context, ref, alarm),
        );
      },
    );
  }

  Future<void> _showEditAlarmDialog(
    BuildContext context,
    WidgetRef ref,
    AlarmModel alarm,
  ) async {
    final result = await showDialog<AlarmModel>(
      context: context,
      builder: (context) => AddAlarmDialog(alarm: alarm),
    );

    if (result != null && context.mounted) {
      // Update the alarm using our state notifier
      ref.read(alarmStateNotifierProvider.notifier).updateAlarm(result);

      // Show confirmation
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Alarm updated'),
          duration: Duration(seconds: 1),
        ),
      );
    }
  }

  Future<void> _showDeleteAlarmDialog(
    BuildContext context,
    WidgetRef ref,
    AlarmModel alarm,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Alarm'),
        content: Text('Are you sure you want to delete "${alarm.label}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && context.mounted) {
      // Store the alarm before deleting it (for undo functionality)
      final deletedAlarm = alarm;

      // Delete the alarm using our state notifier
      final alarmNotifier = ref.read(alarmStateNotifierProvider.notifier);
      alarmNotifier.deleteAlarm(alarm.id);

      // Show a snackbar with undo option
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Alarm deleted'),
          duration: const Duration(seconds: 2),
          action: SnackBarAction(
            label: 'Undo',
            onPressed: () {
              // Restore the deleted alarm
              alarmNotifier.addAlarm(deletedAlarm);

              // Show confirmation
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Alarm restored'),
                  duration: Duration(seconds: 1),
                ),
              );
            },
          ),
        ),
      );
    }
  }
}
