import 'package:flutter/material.dart';
import 'package:shivish/apps/admin/widgets/technician_delete_dialog.dart';
import 'package:shivish/apps/admin/widgets/technician_status_dialog.dart';
import 'package:shivish/shared/models/technician/technician.dart';

class TechnicianListItem extends StatelessWidget {
  final Technician technician;
  final Function(Technician, String, String?, bool) onStatusUpdate;
  final Function(String) onDelete;

  const TechnicianListItem({
    super.key,
    required this.technician,
    required this.onStatusUpdate,
    required this.onDelete,
  });

  Color _getStatusColor(String status) {
    switch (status) {
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.orange;
    }
  }

  void _showStatusDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => TechnicianStatusDialog(
        technician: technician,
        onStatusUpdate: onStatusUpdate,
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => TechnicianDeleteDialog(
        technician: technician,
        onDelete: onDelete,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar
                CircleAvatar(
                  radius: 24,
                  backgroundImage: technician.profileImage != null
                      ? NetworkImage(technician.profileImage!)
                      : null,
                  child: technician.profileImage == null
                      ? Text(technician.name[0].toUpperCase(), style: const TextStyle(fontSize: 20))
                      : null,
                ),
                const SizedBox(width: 12),

                // Main info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name and email
                      Text(
                        technician.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        technician.email,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),

                      // Phone and experience
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.phone, size: 14, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            technician.phone,
                            style: const TextStyle(fontSize: 14),
                          ),
                          const SizedBox(width: 16),
                          const Icon(Icons.work, size: 14, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            '${technician.experienceYears} years',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ),

                      // Service areas
                      if (technician.serviceAreas.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.location_on, size: 14, color: Colors.grey),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                technician.serviceAreas.join(', '),
                                style: const TextStyle(fontSize: 14),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],

                      // Status
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getStatusColor(technician.verificationStatus).withAlpha(25),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: _getStatusColor(technician.verificationStatus)),
                            ),
                            child: Text(
                              technician.verificationStatus.toUpperCase(),
                              style: TextStyle(
                                color: _getStatusColor(technician.verificationStatus),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: (technician.isActive ? Colors.green : Colors.red).withAlpha(25),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: technician.isActive ? Colors.green : Colors.red),
                            ),
                            child: Text(
                              technician.isActive ? 'ACTIVE' : 'INACTIVE',
                              style: TextStyle(
                                color: technician.isActive ? Colors.green : Colors.red,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Action buttons
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Status update button
                    IconButton(
                      icon: const Icon(Icons.edit_note, color: Colors.blue),
                      tooltip: 'Update Status',
                      onPressed: () => _showStatusDialog(context),
                    ),

                    // Delete button
                    IconButton(
                      icon: const Icon(Icons.delete_outline, color: Colors.red),
                      tooltip: 'Delete',
                      onPressed: () => _showDeleteDialog(context),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
