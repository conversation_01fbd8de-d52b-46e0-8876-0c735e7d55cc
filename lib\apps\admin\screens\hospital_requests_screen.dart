import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/apps/admin/providers/auth_provider.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';

final _logger = getLogger('HospitalRequestsScreen');

/// Provider for pending hospital requests using hybrid database
final pendingHospitalRequestsProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final databaseService = DatabaseService(DatabaseConfig.fromEnvironment());

  try {
    _logger.info('Loading pending hospital requests');
    final hospitalRequests = await databaseService.getAll(
      'hospital_requests',
      where: 'status = ?',
      whereParams: ['pending'],
      orderBy: 'created_at DESC',
    );

    _logger.info('Successfully loaded ${hospitalRequests.length} pending hospital requests');
    return hospitalRequests;
  } catch (e) {
    _logger.severe('Error loading pending hospital requests: $e');
    throw Exception('Failed to load hospital requests: $e');
  }
});

/// Screen for admin to approve hospital requests
class HospitalRequestsScreen extends ConsumerStatefulWidget {
  const HospitalRequestsScreen({super.key});

  @override
  ConsumerState<HospitalRequestsScreen> createState() => _HospitalRequestsScreenState();
}

class _HospitalRequestsScreenState extends ConsumerState<HospitalRequestsScreen> {
  bool _isLoading = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    final pendingRequestsAsync = ref.watch(pendingHospitalRequestsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Hospital Requests'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Refresh the provider and ignore the result
              final _ = ref.refresh(pendingHospitalRequestsProvider);
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _errorMessage != null
              ? Center(
                  child: ErrorMessage(
                    message: _errorMessage!,
                    onRetry: () {
                      // Refresh the provider and ignore the result
                      final _ = ref.refresh(pendingHospitalRequestsProvider);
                    },
                  ),
                )
              : pendingRequestsAsync.when(
                  data: (requests) {
                    if (requests.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.local_hospital,
                              size: 64,
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No Pending Requests',
                              style: Theme.of(context).textTheme.titleLarge,
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'There are no pending hospital requests at this time.',
                              style: Theme.of(context).textTheme.bodyMedium,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: requests.length,
                      itemBuilder: (context, index) {
                        final request = requests[index];
                        return _buildRequestCard(request);
                      },
                    );
                  },
                  loading: () => const Center(child: LoadingIndicator()),
                  error: (error, _) => Center(
                    child: ErrorMessage(
                      message: 'Error loading hospital requests: $error',
                      onRetry: () {
                        // Refresh the provider and ignore the result
                        final _ = ref.refresh(pendingHospitalRequestsProvider);
                      },
                    ),
                  ),
                ),
    );
  }

  Widget _buildRequestCard(Map<String, dynamic> request) {
    final createdAt = request['created_at'] ?? request['createdAt'];
    DateTime createdAtDate = DateTime.now();

    if (createdAt is String) {
      try {
        createdAtDate = DateTime.parse(createdAt);
      } catch (e) {
        _logger.warning('Failed to parse createdAt string: $createdAt');
      }
    } else if (createdAt is Map<String, dynamic> && createdAt.containsKey('_seconds')) {
      createdAtDate = DateTime.fromMillisecondsSinceEpoch(
        (createdAt['_seconds'] as int) * 1000,
      );
    }

    final timeAgo = timeago.format(createdAtDate);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.local_hospital, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    request['hospitalName'] ?? 'Unknown Hospital',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.amber.shade300),
                  ),
                  child: const Text(
                    'Pending',
                    style: TextStyle(
                      color: Colors.amber,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const Divider(),
            _buildInfoRow('Owner', request['ownerName'] ?? 'Unknown'),
            _buildInfoRow('Email', request['email'] ?? 'Unknown'),
            _buildInfoRow('Phone', request['phone'] ?? 'Unknown'),
            _buildInfoRow('Requested', timeAgo),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => _showRejectDialog(request['id']),
                  child: const Text('Reject'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => _approveHospital(request['id']),
                  child: const Text('Approve'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _approveHospital(String requestId) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
      _logger.info('Approving hospital request: $requestId');

      // Get request document
      final requestData = await databaseService.find('hospital_requests', requestId);
      if (requestData == null) {
        throw Exception('Hospital request not found');
      }

      final userId = requestData['user_id'] as String;
      final hospitalId = requestData['hospital_id'] as String;
      final now = DateTime.now().toIso8601String();

      // Update hospital document
      await databaseService.update('hospitals', hospitalId, {
        'is_approved': true,
        'is_active': true,
        'approved_at': now,
        'approved_by': ref.read(adminAuthStateProvider).user?.id ?? 'admin',
        'updated_at': now,
      });

      // Update hospital user document
      await databaseService.update('hospital_users', userId, {
        'is_approved': true,
        'is_active': true,
        'updated_at': now,
      });

      // Update request status
      await databaseService.update('hospital_requests', requestId, {
        'status': 'approved',
        'approved_at': now,
        'approved_by': ref.read(adminAuthStateProvider).user?.id ?? 'admin',
        'updated_at': now,
      });

      _logger.info('Successfully approved hospital request: $requestId');

      // Refresh the list and ignore the result
      final _ = ref.refresh(pendingHospitalRequestsProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Hospital approved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error approving hospital: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _showRejectDialog(String requestId) async {
    final reasonController = TextEditingController();

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Hospital Request'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejection:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.isNotEmpty) {
                Navigator.pop(context);
                _rejectHospital(requestId, reasonController.text);
              }
            },
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Colors.red,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  Future<void> _rejectHospital(String requestId, String reason) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
      _logger.info('Rejecting hospital request: $requestId with reason: $reason');

      // Update request status
      final now = DateTime.now().toIso8601String();
      await databaseService.update('hospital_requests', requestId, {
        'status': 'rejected',
        'rejection_reason': reason,
        'rejected_at': now,
        'rejected_by': ref.read(adminAuthStateProvider).user?.id ?? 'admin',
        'updated_at': now,
      });

      _logger.info('Successfully rejected hospital request: $requestId');

      // Refresh the list and ignore the result
      final _ = ref.refresh(pendingHospitalRequestsProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Hospital request rejected'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error rejecting hospital: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
