import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/temple/temple_model.dart';
import '../../providers/admin_temple_provider.dart';
import '../../widgets/temple/temple_approval_card.dart';
import '../../widgets/common/admin_app_bar.dart';
import '../../../../shared/widgets/app_loading_indicator.dart';
import '../../../../shared/widgets/app_error_widget.dart';

class TempleApprovalScreen extends ConsumerStatefulWidget {
  const TempleApprovalScreen({super.key});

  @override
  ConsumerState<TempleApprovalScreen> createState() =>
      _TempleApprovalScreenState();
}

class _TempleApprovalScreenState extends ConsumerState<TempleApprovalScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadTemples();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadTemples() {
    ref.read(adminTempleProvider.notifier).loadPendingTemples();
    ref.read(adminTempleProvider.notifier).loadApprovedTemples();
    ref.read(adminTempleProvider.notifier).loadRejectedTemples();
    ref.read(adminTempleProvider.notifier).loadAllTemples();
  }

  @override
  Widget build(BuildContext context) {
    final templeState = ref.watch(adminTempleProvider);

    return Scaffold(
      appBar: AdminAppBar(
        title: 'Temple Management',
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadTemples),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () => _showAnalytics(context),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildStatsCards(templeState),
          _buildTabBar(),
          Expanded(child: _buildTabContent(templeState)),
        ],
      ),
    );
  }

  Widget _buildStatsCards(dynamic templeState) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'Pending',
              templeState.pendingTemples?.length ?? 0,
              Colors.orange,
              Icons.pending,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Approved',
              templeState.approvedTemples?.length ?? 0,
              Colors.green,
              Icons.check_circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Rejected',
              templeState.rejectedTemples?.length ?? 0,
              Colors.red,
              Icons.cancel,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Total',
              templeState.allTemples?.length ?? 0,
              Colors.blue,
              Icons.temple_hindu,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, int count, Color color, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: Theme.of(context).primaryColor,
        unselectedLabelColor: Colors.grey,
        indicatorColor: Theme.of(context).primaryColor,
        tabs: const [
          Tab(text: 'Pending'),
          Tab(text: 'Approved'),
          Tab(text: 'Rejected'),
          Tab(text: 'All'),
        ],
      ),
    );
  }

  Widget _buildTabContent(dynamic templeState) {
    if (templeState.isLoading) {
      return const AppLoadingIndicator();
    }

    if (templeState.error != null) {
      return AppErrorWidget(message: templeState.error!, onRetry: _loadTemples);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildTempleList(templeState.pendingTemples ?? [], 'pending'),
        _buildTempleList(templeState.approvedTemples ?? [], 'approved'),
        _buildTempleList(templeState.rejectedTemples ?? [], 'rejected'),
        _buildTempleList(templeState.allTemples ?? [], 'all'),
      ],
    );
  }

  Widget _buildTempleList(List<Temple> temples, String type) {
    if (temples.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.temple_hindu, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No ${type == 'all' ? '' : type} temples found',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: temples.length,
      itemBuilder: (context, index) {
        final temple = temples[index];
        return TempleApprovalCard(
          temple: temple,
          onApprove: type == 'pending' ? () => _approveTemple(temple) : null,
          onReject: type == 'pending' ? () => _rejectTemple(temple) : null,
          onView: () => _viewTempleDetails(temple),
          onEdit: type != 'rejected' ? () => _editTemple(temple) : null,
        );
      },
    );
  }

  void _approveTemple(Temple temple) async {
    final confirmed = await _showConfirmationDialog(
      'Approve Temple',
      'Are you sure you want to approve ${temple.name}?',
      'Approve',
      Colors.green,
    );

    if (confirmed) {
      final success = await ref
          .read(adminTempleProvider.notifier)
          .approveTemple(temple.id, 'Temple approved by admin');

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${temple.name} has been approved'),
            backgroundColor: Colors.green,
          ),
        );
        _loadTemples();
      }
    }
  }

  void _rejectTemple(Temple temple) async {
    final reason = await _showRejectDialog(temple);
    if (reason != null && reason.isNotEmpty) {
      final success = await ref
          .read(adminTempleProvider.notifier)
          .rejectTemple(temple.id, reason);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${temple.name} has been rejected'),
            backgroundColor: Colors.red,
          ),
        );
        _loadTemples();
      }
    }
  }

  void _viewTempleDetails(Temple temple) {
    context.push('/admin/temples/${temple.id}');
  }

  void _editTemple(Temple temple) {
    context.push('/admin/temples/${temple.id}/edit');
  }

  Future<bool> _showConfirmationDialog(
    String title,
    String message,
    String actionText,
    Color actionColor,
  ) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(backgroundColor: actionColor),
                child: Text(actionText),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<String?> _showRejectDialog(Temple temple) async {
    final controller = TextEditingController();

    return await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reject ${temple.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Please provide a reason for rejection:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'Enter rejection reason...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(controller.text),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  void _showAnalytics(BuildContext context) {
    // Navigate to temple analytics screen
    context.push('/admin/temples/analytics');
  }
}
