import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/apps/technician/widgets/technician_app_toolbar.dart';
import 'package:shivish/apps/technician/technician_routes.dart';
import 'package:shivish/apps/technician/screens/settings/account_settings_screen.dart';
import 'package:shivish/apps/technician/screens/settings/notification_settings_screen.dart';
import 'package:shivish/apps/technician/screens/settings/payment_settings_screen.dart';
import 'package:shivish/apps/technician/screens/settings/phone_number_settings_screen.dart';
import 'package:shivish/apps/technician/screens/settings/pricing_settings_screen.dart';
import 'package:shivish/apps/technician/screens/settings/profile_picture_settings_screen.dart';
import 'package:shivish/apps/technician/screens/settings/recovery_codes_screen.dart';
import 'package:shivish/apps/technician/screens/settings/services_settings_screen.dart';
import 'package:shivish/apps/technician/screens/settings/service_areas_settings_screen.dart';
import 'package:shivish/shared/providers/auth_provider.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: TechnicianAppToolbar.simple(
        title: 'Settings',
        fallbackRoute: TechnicianRoutes.home,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSection(
            context,
            'Account',
            [
              _buildSettingTile(
                context,
                'Profile Picture',
                Icons.account_circle,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProfilePictureSettingsScreen(),
                  ),
                ),
              ),
              _buildSettingTile(
                context,
                'Account Settings',
                Icons.person,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AccountSettingsScreen(),
                  ),
                ),
              ),
              _buildSettingTile(
                context,
                'Phone Number',
                Icons.phone,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PhoneNumberSettingsScreen(),
                  ),
                ),
              ),
              _buildSettingTile(
                context,
                'Recovery Codes',
                Icons.security,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const RecoveryCodesScreen(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            context,
            'Services',
            [
              _buildSettingTile(
                context,
                'Service Areas',
                Icons.location_on,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ServiceAreasSettingsScreen(),
                  ),
                ),
              ),
              _buildSettingTile(
                context,
                'Services',
                Icons.home_repair_service,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ServicesSettingsScreen(),
                  ),
                ),
              ),
              _buildSettingTile(
                context,
                'Pricing',
                Icons.attach_money,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PricingSettingsScreen(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            context,
            'Payments',
            [
              _buildSettingTile(
                context,
                'Payment Settings',
                Icons.payment,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PaymentSettingsScreen(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            context,
            'Notifications',
            [
              _buildSettingTile(
                context,
                'Notification Settings',
                Icons.notifications,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const NotificationSettingsScreen(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          AppButton(
            onPressed: () async {
              try {
                await ref.read(authProvider.notifier).signOut();
                if (context.mounted) {
                  Navigator.of(context).pushReplacementNamed('/login');
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to sign out: ${e.toString()}'),
                      backgroundColor: Theme.of(context).colorScheme.error,
                    ),
                  );
                }
              }
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, List<Widget> tiles) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        Card(
          child: Column(
            children: tiles,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingTile(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }
}
