import 'package:flutter/material.dart';
import '../widgets/navigation/buyer_app_bar.dart';
import '../buyer_routes.dart';

/// Utility class for wrapping screens with consistent UI elements
class ScreenWrapper {
  /// Wraps a screen with a consistent app bar and back button handling
  ///
  /// This ensures that:
  /// 1. All screens have a consistent app bar
  /// 2. Back buttons navigate to the previous screen
  /// 3. The Android back button works properly
  static Widget wrapScreen({
    required BuildContext context,
    required Widget body,
    required String title,
    String? fallbackRoute,
    List<Widget>? actions,
    bool showBackButton = true,
    Widget? drawer,
    Widget? bottomNavigationBar,
    FloatingActionButton? floatingActionButton,
    Color? backgroundColor,
  }) {
    return Scaffold(
      appBar: BuyerAppBar(
        title: Text(title),
        showBackButton: showBackButton,
        fallbackRoute: fallbackRoute ?? BuyerRoutes.home,
      ),
      drawer: drawer,
      body: body,
      bottomNavigationBar: bottomNavigationBar,
      floatingActionButton: floatingActionButton,
      backgroundColor: backgroundColor,
    );
  }
}
