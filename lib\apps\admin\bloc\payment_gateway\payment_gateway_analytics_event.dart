part of 'payment_gateway_analytics_bloc.dart';

@freezed
abstract class PaymentGatewayAnalyticsEvent with _$PaymentGatewayAnalyticsEvent {
  const factory PaymentGatewayAnalyticsEvent.loadAnalytics({
    required PaymentGateway gateway,
    required DateTimeRange dateRange,
  }) = LoadAnalytics;

  const factory PaymentGatewayAnalyticsEvent.updateDateRange({
    required DateTimeRange dateRange,
  }) = UpdateDateRange;
}
