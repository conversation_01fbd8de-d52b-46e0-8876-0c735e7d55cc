import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/shared/models/event/event_model.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/apps/admin/admin_routes.dart';
import '../bloc/event_management/admin_event_management_bloc.dart';
import '../bloc/event_management/admin_event_management_event.dart';
import '../bloc/event_management/admin_event_management_state.dart';
import 'event_approval_card.dart';

class AdminEventManagementScreen extends StatefulWidget {
  const AdminEventManagementScreen({super.key});

  @override
  State<AdminEventManagementScreen> createState() =>
      _AdminEventManagementScreenState();
}

class _AdminEventManagementScreenState
    extends State<AdminEventManagementScreen> {
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    _loadCurrentUserId();
  }

  Future<void> _loadCurrentUserId() async {
    final authService = context.read<AuthService>();
    final user = await authService.getCurrentUser();
    if (user != null) {
      setState(() {
        _currentUserId = user.id;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Event Management'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we can pop the current route
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else {
              // If we can't pop, use GoRouter to navigate back to home
              context.go(AdminRoutes.home);
            }
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.push(AdminRoutes.eventsCreate),
          ),
        ],
      ),
      body: BlocBuilder<AdminEventManagementBloc, AdminEventManagementState>(
        builder: (context, state) {
          return state.when(
            initial: () => const Center(child: CircularProgressIndicator()),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (message) => Center(
              child: Text(
                message,
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ),
            loadedPendingEvents: (events) => _buildEventsList(context, events),
            eventApproved: (message) =>
                const Center(child: Text('Event approved')),
            eventRejected: (message) =>
                const Center(child: Text('Event rejected')),
            eventCreated: (message) =>
                const Center(child: Text('Event created')),
            eventUpdated: (message) =>
                const Center(child: Text('Event updated')),
          );
        },
      ),
    );
  }

  Widget _buildEventsList(BuildContext context, List<EventModel> events) {
    if (events.isEmpty) {
      return const Center(child: Text('No pending events'));
    }

    if (_currentUserId == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return ListView.builder(
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        return EventApprovalCard(
          event: event,
          onApprove: () {
            context.read<AdminEventManagementBloc>().add(
                  AdminEventManagementEvent.approveEvent(
                    eventId: event.id,
                    adminId: _currentUserId!,
                  ),
                );
          },
          onReject: () {
            String? rejectionReason;
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Reject Event'),
                content: TextField(
                  decoration: const InputDecoration(
                    labelText: 'Rejection Reason',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  onChanged: (value) {
                    rejectionReason = value;
                  },
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      if (rejectionReason != null) {
                        context.read<AdminEventManagementBloc>().add(
                              AdminEventManagementEvent.rejectEvent(
                                eventId: event.id,
                                reason: rejectionReason!,
                                adminId: _currentUserId!,
                              ),
                            );
                      }
                      Navigator.pop(context);
                    },
                    child: const Text('Reject'),
                  ),
                ],
              ),
            );
          },
          onEdit: () => context.push(
            '${AdminRoutes.eventsEdit}/${event.id}',
            extra: event,
          ),
        );
      },
    );
  }
}
