import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'list_submission_order.freezed.dart';
part 'list_submission_order.g.dart';

@freezed
sealed class ListSubmissionOrder with _$ListSubmissionOrder {
  const factory ListSubmissionOrder({
    required String id,
    required String submissionId,
    required String buyerId,
    required String sellerId,
    required double totalAmount,
    required List<ListSubmissionOrderItem> items,
    required DateTime createdAt,
    @Default(false) bool isPaid,
    String? paymentId,
    String? deliveryId,
    @Default('pending') String status,
  }) = _ListSubmissionOrder;

  factory ListSubmissionOrder.fromJson(Map<String, dynamic> json) =>
      _$ListSubmissionOrderFromJson(json);
}

@freezed
sealed class ListSubmissionOrderItem with _$ListSubmissionOrderItem {
  const factory ListSubmissionOrderItem({
    required String id,
    required String name,
    required int quantity,
    required double price,
    String? notes,
  }) = _ListSubmissionOrderItem;

  factory ListSubmissionOrderItem.fromJson(Map<String, dynamic> json) =>
      _$ListSubmissionOrderItemFromJson(json);
}
