import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/services/notification/notification_service.dart';
import '../../../shared/models/notification/notification_model.dart';
import '../../../shared/models/notification/notification_status.dart';
import '../../../shared/models/notification/notification_type.dart';
import 'auth_provider.dart';

part 'notification_provider.g.dart';

@riverpod
class NotificationsNotifier extends _$NotificationsNotifier {
  @override
  Future<List<NotificationModel>> build() async {
    final userId = ref.watch(authStateProvider).user?.id;
    if (userId == null) return [];

    final notificationService = ref.watch(notificationServiceProvider);
    final notifications = await notificationService
        .getNotifications(userId)
        .first;
    return notifications;
  }

  Future<void> deleteNotification(String notificationId) async {
    final userId = ref.watch(authStateProvider).user?.id;
    if (userId == null) return;

    final notificationService = ref.read(notificationServiceProvider);
    await notificationService.deleteNotification(notificationId);
    ref.invalidateSelf();
  }

  Future<void> restoreNotification(NotificationModel notification) async {
    final userId = ref.watch(authStateProvider).user?.id;
    if (userId == null) return;

    final notificationService = ref.read(notificationServiceProvider);
    await notificationService.markAsRead(notification.id);
    ref.invalidateSelf();
  }

  Future<void> markAsRead(String notificationId) async {
    final userId = ref.watch(authStateProvider).user?.id;
    if (userId == null) return;

    final notificationService = ref.read(notificationServiceProvider);
    await notificationService.markAsRead(notificationId);
    ref.invalidateSelf();
  }

  Future<void> clearAllNotifications() async {
    final userId = ref.watch(authStateProvider).user?.id;
    if (userId == null) return;

    final notificationService = ref.read(notificationServiceProvider);
    await notificationService.clearAllNotifications(userId);
    ref.invalidateSelf();
  }
}

final notificationsProvider = notificationsNotifierProvider;

/// Provider for unread notifications count
@riverpod
Stream<int> unreadNotificationsCount(Ref ref) async* {
  final userId = ref.watch(authStateProvider).user?.id;
  if (userId == null) {
    yield 0;
    return;
  }

  final notificationService = ref.watch(notificationServiceProvider);

  await for (final notifications in notificationService.getNotifications(
    userId,
  )) {
    final unreadCount = notifications
        .where(
          (notification) => notification.status == NotificationStatus.unread,
        )
        .length;
    yield unreadCount;
  }
}

/// Provider for buyer-specific notification counts by type
@riverpod
Stream<Map<NotificationType, int>> buyerNotificationCountsByType(
  Ref ref,
) async* {
  final userId = ref.watch(authStateProvider).user?.id;
  if (userId == null) {
    yield {};
    return;
  }

  final notificationService = ref.watch(notificationServiceProvider);

  await for (final notifications in notificationService.getNotifications(
    userId,
  )) {
    final countsByType = <NotificationType, int>{};

    // Initialize buyer-specific types
    for (final type in [
      NotificationType.healthcareReminder,
      NotificationType.dailyHoroscope,
      NotificationType.panchangam,
      NotificationType.shoppingListAlert,
      NotificationType.safetyTracking,
      NotificationType.friendTracking,
      NotificationType.alarmNotification,
      NotificationType.calendarReminder,
      NotificationType.promotions,
      NotificationType.festivalReminder,
      NotificationType.priceAlert,
      NotificationType.lowStockAlert,
      NotificationType.rideBooking,
      NotificationType.ticketBooking,
      NotificationType.medicineOrder,
      NotificationType.doctorConsultation,
      NotificationType.aiAssistant,
      NotificationType.technicianBooking,
      NotificationType.priestBooking,
    ]) {
      countsByType[type] = 0;
    }

    // Count notifications by type
    for (final notification in notifications) {
      if (notification.status == NotificationStatus.unread) {
        countsByType[notification.type] =
            (countsByType[notification.type] ?? 0) + 1;
      }
    }

    yield countsByType;
  }
}

/// Provider for healthcare reminder notifications
@riverpod
Stream<List<NotificationModel>> healthcareReminderNotifications(
  Ref ref,
) async* {
  final userId = ref.watch(authStateProvider).user?.id;
  if (userId == null) {
    yield [];
    return;
  }

  final notificationService = ref.watch(notificationServiceProvider);

  await for (final notifications in notificationService.getNotifications(
    userId,
  )) {
    final healthcareNotifications = notifications
        .where(
          (notification) =>
              notification.type == NotificationType.healthcareReminder,
        )
        .toList();
    yield healthcareNotifications;
  }
}

/// Provider for calendar reminder notifications
@riverpod
Stream<List<NotificationModel>> calendarReminderNotifications(Ref ref) async* {
  final userId = ref.watch(authStateProvider).user?.id;
  if (userId == null) {
    yield [];
    return;
  }

  final notificationService = ref.watch(notificationServiceProvider);

  await for (final notifications in notificationService.getNotifications(
    userId,
  )) {
    final calendarNotifications = notifications
        .where(
          (notification) =>
              notification.type == NotificationType.calendarReminder ||
              notification.type == NotificationType.festivalReminder ||
              notification.type == NotificationType.panchangam,
        )
        .toList();
    yield calendarNotifications;
  }
}

/// Provider for shopping list alert notifications
@riverpod
Stream<List<NotificationModel>> shoppingListAlertNotifications(Ref ref) async* {
  final userId = ref.watch(authStateProvider).user?.id;
  if (userId == null) {
    yield [];
    return;
  }

  final notificationService = ref.watch(notificationServiceProvider);

  await for (final notifications in notificationService.getNotifications(
    userId,
  )) {
    final shoppingNotifications = notifications
        .where(
          (notification) =>
              notification.type == NotificationType.shoppingListAlert ||
              notification.type == NotificationType.priceAlert ||
              notification.type == NotificationType.lowStockAlert,
        )
        .toList();
    yield shoppingNotifications;
  }
}

/// Provider for safety and tracking notifications
@riverpod
Stream<List<NotificationModel>> safetyTrackingNotifications(Ref ref) async* {
  final userId = ref.watch(authStateProvider).user?.id;
  if (userId == null) {
    yield [];
    return;
  }

  final notificationService = ref.watch(notificationServiceProvider);

  await for (final notifications in notificationService.getNotifications(
    userId,
  )) {
    final safetyNotifications = notifications
        .where(
          (notification) =>
              notification.type == NotificationType.safetyTracking ||
              notification.type == NotificationType.friendTracking,
        )
        .toList();
    yield safetyNotifications;
  }
}

/// Provider for booking-related notifications (ride, ticket, technician, priest)
@riverpod
Stream<List<NotificationModel>> bookingNotifications(Ref ref) async* {
  final userId = ref.watch(authStateProvider).user?.id;
  if (userId == null) {
    yield [];
    return;
  }

  final notificationService = ref.watch(notificationServiceProvider);

  await for (final notifications in notificationService.getNotifications(
    userId,
  )) {
    final bookingNotifications = notifications
        .where(
          (notification) =>
              notification.type == NotificationType.rideBooking ||
              notification.type == NotificationType.ticketBooking ||
              notification.type == NotificationType.technicianBooking ||
              notification.type == NotificationType.priestBooking ||
              notification.type == NotificationType.booking,
        )
        .toList();
    yield bookingNotifications;
  }
}

/// Provider for healthcare-related notifications
@riverpod
Stream<List<NotificationModel>> healthcareNotifications(Ref ref) async* {
  final userId = ref.watch(authStateProvider).user?.id;
  if (userId == null) {
    yield [];
    return;
  }

  final notificationService = ref.watch(notificationServiceProvider);

  await for (final notifications in notificationService.getNotifications(
    userId,
  )) {
    final healthcareNotifications = notifications
        .where(
          (notification) =>
              notification.type == NotificationType.healthcareReminder ||
              notification.type == NotificationType.medicineOrder ||
              notification.type == NotificationType.doctorConsultation,
        )
        .toList();
    yield healthcareNotifications;
  }
}

/// Provider for AI assistant notifications
@riverpod
Stream<List<NotificationModel>> aiAssistantNotifications(Ref ref) async* {
  final userId = ref.watch(authStateProvider).user?.id;
  if (userId == null) {
    yield [];
    return;
  }

  final notificationService = ref.watch(notificationServiceProvider);

  await for (final notifications in notificationService.getNotifications(
    userId,
  )) {
    final aiNotifications = notifications
        .where(
          (notification) => notification.type == NotificationType.aiAssistant,
        )
        .toList();
    yield aiNotifications;
  }
}
