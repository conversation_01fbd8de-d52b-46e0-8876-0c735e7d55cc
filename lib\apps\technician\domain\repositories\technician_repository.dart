import '../../../../shared/models/technician/technician.dart';

abstract class TechnicianRepository {
  Future<Technician?> getTechnician(String id);
  Future<void> updateTechnician(Technician technician);
  Stream<Technician?> streamTechnician(String id);
  Future<List<Technician>> getTechnicians();
  Future<void> createTech<PERSON>ian(Technician technician);
  Future<void> deleteTechnician(String id);
}
