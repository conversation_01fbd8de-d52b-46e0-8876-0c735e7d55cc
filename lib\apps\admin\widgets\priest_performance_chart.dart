import 'package:flutter/material.dart';
import '../../../shared/models/priest.dart';

class PriestPerformanceChart extends StatelessWidget {
  final Priest priest;

  const PriestPerformanceChart({
    super.key,
    required this.priest,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Metrics',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildMetricCard(
                  context,
                  'Total Bookings',
                  priest.totalBookings.toString(),
                  Icons.calendar_today,
                ),
                _buildMetricCard(
                  context,
                  'Total Services',
                  priest.totalServices.toString(),
                  Icons.church,
                ),
                _buildMetricCard(
                  context,
                  'Total Revenue',
                  '₹${priest.totalRevenue.toStringAsFixed(2)}',
                  Icons.currency_rupee,
                ),
              ],
            ),
            const SizedBox(height: 24),
            Text(
              'Rating: ${priest.rating.toStringAsFixed(1)} / 5.0',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: priest.rating / 5.0,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(
                _getRatingColor(priest.rating),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Based on ${priest.totalReviews} reviews',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 24),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      ),
    );
  }

  Color _getRatingColor(double rating) {
    if (rating >= 4.5) return Colors.green;
    if (rating >= 4.0) return Colors.lightGreen;
    if (rating >= 3.5) return Colors.orange;
    if (rating >= 3.0) return Colors.deepOrange;
    return Colors.red;
  }
}
