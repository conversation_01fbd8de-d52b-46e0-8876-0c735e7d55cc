// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_NotificationSettingsModel _$NotificationSettingsModelFromJson(
  Map<String, dynamic> json,
) => _NotificationSettingsModel(
  id: json['id'] as String,
  userId: json['userId'] as String,
  pushEnabled: json['pushEnabled'] as bool? ?? true,
  emailEnabled: json['emailEnabled'] as bool? ?? true,
  smsEnabled: json['smsEnabled'] as bool? ?? true,
  inAppEnabled: json['inAppEnabled'] as bool? ?? true,
  typeSettings: (json['typeSettings'] as Map<String, dynamic>).map(
    (k, e) => MapEntry($enumDecode(_$NotificationTypeEnumMap, k), e as bool),
  ),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isDeleted: json['isDeleted'] as bool? ?? false,
);

Map<String, dynamic> _$NotificationSettingsModelToJson(
  _NotificationSettingsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'pushEnabled': instance.pushEnabled,
  'emailEnabled': instance.emailEnabled,
  'smsEnabled': instance.smsEnabled,
  'inAppEnabled': instance.inAppEnabled,
  'typeSettings': instance.typeSettings.map(
    (k, e) => MapEntry(_$NotificationTypeEnumMap[k]!, e),
  ),
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isDeleted': instance.isDeleted,
};

const _$NotificationTypeEnumMap = {
  NotificationType.general: 'general',
  NotificationType.order: 'order',
  NotificationType.payment: 'payment',
  NotificationType.booking: 'booking',
  NotificationType.event: 'event',
  NotificationType.chat: 'chat',
  NotificationType.system: 'system',
  NotificationType.verification: 'verification',
  NotificationType.deliveryRequest: 'delivery_request',
  NotificationType.statusUpdate: 'status_update',
  NotificationType.earnings: 'earnings',
  NotificationType.healthcareReminder: 'healthcare_reminder',
  NotificationType.dailyHoroscope: 'daily_horoscope',
  NotificationType.panchangam: 'panchangam',
  NotificationType.shoppingListAlert: 'shopping_list_alert',
  NotificationType.safetyTracking: 'safety_tracking',
  NotificationType.friendTracking: 'friend_tracking',
  NotificationType.alarmNotification: 'alarm_notification',
  NotificationType.calendarReminder: 'calendar_reminder',
  NotificationType.promotions: 'promotions',
  NotificationType.festivalReminder: 'festival_reminder',
  NotificationType.priceAlert: 'price_alert',
  NotificationType.lowStockAlert: 'low_stock_alert',
  NotificationType.rideBooking: 'ride_booking',
  NotificationType.ticketBooking: 'ticket_booking',
  NotificationType.medicineOrder: 'medicine_order',
  NotificationType.doctorConsultation: 'doctor_consultation',
  NotificationType.aiAssistant: 'ai_assistant',
  NotificationType.technicianBooking: 'technician_booking',
  NotificationType.priestBooking: 'priest_booking',
};
