import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/presentation/cubits/analytics_cubit.dart';
import 'package:shivish/apps/seller/screens/analytics/widgets/customer_analytics_tab.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';

class CustomerAnalyticsScreen extends StatelessWidget {
  const CustomerAnalyticsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer Analytics'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<AnalyticsCubit>().loadCustomerAnalytics();
            },
          ),
        ],
      ),
      body: BlocBuilder<AnalyticsCubit, AnalyticsState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const LoadingIndicator();
          }

          if (state.error != null) {
            return ErrorMessage(
              message: state.error!,
              onRetry: () {
                context.read<AnalyticsCubit>().loadCustomerAnalytics();
              },
            );
          }

          return const CustomerAnalyticsTab();
        },
      ),
    );
  }
}
