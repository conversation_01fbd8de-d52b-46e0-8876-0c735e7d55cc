import 'package:flutter/material.dart';

/// Custom navigation observer to debug navigation events
class NavigationObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugPrint('NavigationObserver: PUSHED route=${route.settings.name}, previousRoute=${previousRoute?.settings.name}');
    super.didPush(route, previousRoute);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugPrint('NavigationObserver: POPPED route=${route.settings.name}, previousRoute=${previousRoute?.settings.name}');
    super.didPop(route, previousRoute);
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugPrint('NavigationObserver: REMOVED route=${route.settings.name}, previousRoute=${previousRoute?.settings.name}');
    super.didRemove(route, previousRoute);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    debugPrint('NavigationObserver: REPLACED newRoute=${newRoute?.settings.name}, oldRoute=${oldRoute?.settings.name}');
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }
}
