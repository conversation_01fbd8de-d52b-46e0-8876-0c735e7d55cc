import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/shared/models/payment/payment_method.dart';
import 'package:shivish/shared/providers/payment_provider.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/utils/logger.dart';
import '../../buyer_routes.dart';

class ListSubmissionOrderPaymentScreen extends ConsumerStatefulWidget {
  final String orderId;
  final double amount;
  final String submissionId;

  const ListSubmissionOrderPaymentScreen({
    super.key,
    required this.orderId,
    required this.amount,
    required this.submissionId,
  });

  @override
  ConsumerState<ListSubmissionOrderPaymentScreen> createState() =>
      _ListSubmissionOrderPaymentScreenState();
}

class _ListSubmissionOrderPaymentScreenState
    extends ConsumerState<ListSubmissionOrderPaymentScreen> {
  final _logger = getLogger('ListSubmissionOrderPaymentScreen');

  @override
  void initState() {
    super.initState();
    _loadSavedPaymentMethods();
  }

  Future<void> _loadSavedPaymentMethods() async {
    try {
      await ref.read(paymentProvider.notifier).loadSavedPaymentMethods();
    } catch (e, st) {
      _logger.severe('Error loading payment methods', e, st);
    }
  }

  Future<void> _handlePaymentMethodSelected(PaymentMethod method) async {
    try {
      await ref.read(paymentProvider.notifier).initiatePayment(
            orderId: widget.orderId,
            amount: widget.amount,
            method: method,
          );
    } catch (e, st) {
      _logger.severe('Error initiating payment', e, st);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to initiate payment: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _handleAddNewCard() async {
    final result = await context.push<PaymentMethod>(BuyerRoutes.addCard);
    if (result != null && mounted) {
      await _handlePaymentMethodSelected(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(paymentProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Stack(
        children: [
          _buildContent(state),
          if (state.isLoading) const LoadingIndicator(),
        ],
      ),
    );
  }

  Widget _buildContent(PaymentState state) {
    if (state.error != null) {
      return Center(
        child: ErrorMessage(
          message: state.error!,
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildAmountCard(),
          const SizedBox(height: 24),
          _buildSavedPaymentMethods(
              List<PaymentMethod>.from(state.savedPaymentMethods)),
          const SizedBox(height: 24),
          _buildAddNewCardButton(),
        ],
      ),
    );
  }

  Widget _buildAmountCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Amount to Pay',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              '₹${widget.amount.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSavedPaymentMethods(List<PaymentMethod> methods) {
    if (methods.isEmpty) {
      return const Center(
        child: Text('No saved payment methods'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Saved Payment Methods',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: methods.length,
          separatorBuilder: (_, __) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final method = methods[index];
            return _buildPaymentMethodCard(method);
          },
        ),
      ],
    );
  }

  Widget _buildPaymentMethodCard(PaymentMethod method) {
    return Card(
      child: InkWell(
        onTap: () => _handlePaymentMethodSelected(method),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Icon(
                _getPaymentMethodIcon(method.type),
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      method.title,
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Text(
                      method.subtitle,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              const Icon(Icons.chevron_right),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddNewCardButton() {
    return OutlinedButton.icon(
      onPressed: _handleAddNewCard,
      icon: const Icon(Icons.add),
      label: const Text('Add New Card'),
    );
  }

  IconData _getPaymentMethodIcon(PaymentMethodType type) {
    switch (type) {
      case PaymentMethodType.card:
        return Icons.credit_card;
      case PaymentMethodType.upi:
        return Icons.account_balance;
      case PaymentMethodType.netbanking:
        return Icons.account_balance_wallet;
    }
  }
}
