import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/shared/services/settings/settings_service.dart';

part 'settings_cubit.freezed.dart';

@freezed
sealed class SettingsState with _$SettingsState {
  const factory SettingsState.loading() = _Loading;
  const factory SettingsState.error(String message) = _Error;
  const factory SettingsState.loaded(Settings settings) = _Loaded;
}

class SettingsCubit extends Cubit<SettingsState> {
  final SettingsService _settingsService;

  SettingsCubit(this._settingsService) : super(const SettingsState.loading()) {
    loadSettings();
  }

  Future<void> loadSettings() async {
    try {
      emit(const SettingsState.loading());
      final settings = await _settingsService.getSettings();
      emit(SettingsState.loaded(settings));
    } catch (e) {
      emit(SettingsState.error(e.toString()));
    }
  }

  Future<void> toggleOrderNotifications(bool value) async {
    try {
      final currentState = state;
      currentState.maybeWhen(
        loaded: (settings) async {
          final updatedSettings = Settings(
            orderNotifications: value,
            productNotifications: settings.productNotifications,
            systemNotifications: settings.systemNotifications,
            theme: settings.theme,
            language: settings.language,
            currency: settings.currency,
          );
          await _settingsService.updateSettings(updatedSettings);
          emit(SettingsState.loaded(updatedSettings));
        },
        orElse: () {},
      );
    } catch (e) {
      emit(SettingsState.error(e.toString()));
    }
  }

  Future<void> toggleProductNotifications(bool value) async {
    try {
      final currentState = state;
      currentState.maybeWhen(
        loaded: (settings) async {
          final updatedSettings = Settings(
            orderNotifications: settings.orderNotifications,
            productNotifications: value,
            systemNotifications: settings.systemNotifications,
            theme: settings.theme,
            language: settings.language,
            currency: settings.currency,
          );
          await _settingsService.updateSettings(updatedSettings);
          emit(SettingsState.loaded(updatedSettings));
        },
        orElse: () {},
      );
    } catch (e) {
      emit(SettingsState.error(e.toString()));
    }
  }

  Future<void> toggleSystemNotifications(bool value) async {
    try {
      final currentState = state;
      currentState.maybeWhen(
        loaded: (settings) async {
          final updatedSettings = Settings(
            orderNotifications: settings.orderNotifications,
            productNotifications: settings.productNotifications,
            systemNotifications: value,
            theme: settings.theme,
            language: settings.language,
            currency: settings.currency,
          );
          await _settingsService.updateSettings(updatedSettings);
          emit(SettingsState.loaded(updatedSettings));
        },
        orElse: () {},
      );
    } catch (e) {
      emit(SettingsState.error(e.toString()));
    }
  }
}

class Settings {
  final bool orderNotifications;
  final bool productNotifications;
  final bool systemNotifications;
  final String theme;
  final String language;
  final String currency;

  const Settings({
    required this.orderNotifications,
    required this.productNotifications,
    required this.systemNotifications,
    required this.theme,
    required this.language,
    required this.currency,
  });
}
