import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/auth_provider.dart';
import '../../buyer_routes.dart';

class PhoneVerificationScreen extends ConsumerStatefulWidget {
  const PhoneVerificationScreen({super.key});

  @override
  ConsumerState<PhoneVerificationScreen> createState() =>
      _PhoneVerificationScreenState();
}

class _PhoneVerificationScreenState
    extends ConsumerState<PhoneVerificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _codeController = TextEditingController();
  bool _isCodeSent = false;
  bool _isResendEnabled = true;
  int _resendCountdown = 60;

  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  void _onSendCodePressed() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        debugPrint(
            'PhoneVerificationScreen: Sending verification code to phone: ${_phoneController.text.trim()}');
        final phoneNumber = _phoneController.text.trim();
        debugPrint('PhoneVerificationScreen: Sending verification code to: $phoneNumber');
        await ref.read(authStateProvider.notifier).sendPhoneVerificationCode(
              phoneNumber,
            );

        debugPrint(
            'PhoneVerificationScreen: Verification code sent successfully');
        setState(() {
          _isCodeSent = true;
          _isResendEnabled = false;
          _resendCountdown = 60;
        });

        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            _startResendCountdown();
          }
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Verification code sent. Please check your phone.'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        debugPrint(
            'PhoneVerificationScreen: Error sending verification code: $e');
        // Show error message to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to send code: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _onVerifyPressed() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        debugPrint(
            'PhoneVerificationScreen: Verifying phone with code: ${_codeController.text.trim()}');
        await ref.read(authStateProvider.notifier).verifyPhoneNumber(
              verificationId: ref.read(authStateProvider).verificationId ?? '',
              smsCode: _codeController.text.trim(),
            );

        debugPrint('PhoneVerificationScreen: Phone verification successful');
        // Navigation will be handled by the auth state listener
      } catch (e) {
        debugPrint(
            'PhoneVerificationScreen: Error during phone verification: $e');
        // Show error message to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Verification failed: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _onResendCodePressed() async {
    if (_isResendEnabled) {
      try {
        debugPrint(
            'PhoneVerificationScreen: Resending verification code to phone: ${_phoneController.text.trim()}');
        final phoneNumber = _phoneController.text.trim();
        debugPrint('PhoneVerificationScreen: Resending verification code to: $phoneNumber');
        await ref.read(authStateProvider.notifier).sendPhoneVerificationCode(
              phoneNumber,
            );

        debugPrint(
            'PhoneVerificationScreen: Verification code resent successfully');
        setState(() {
          _isResendEnabled = false;
          _resendCountdown = 60;
        });

        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            _startResendCountdown();
          }
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content:
                  Text('Verification code resent. Please check your phone.'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        debugPrint(
            'PhoneVerificationScreen: Error resending verification code: $e');
        // Show error message to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to resend code: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _startResendCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          if (_resendCountdown > 0) {
            _resendCountdown--;
            _startResendCountdown();
          } else {
            _isResendEnabled = true;
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(authStateProvider);

    // Listen for successful phone verification
    ref.listen<AuthState>(authStateProvider, (previous, next) {
      if (next.isPhoneVerified && !next.isLoading && next.error == null) {
        debugPrint('PhoneVerificationScreen: Phone verification detected, navigating to home');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Phone verification successful!'),
              backgroundColor: Colors.green,
            ),
          );
          context.go(BuyerRoutes.home);
        }
      } else if (next.error != null && previous?.error != next.error) {
        debugPrint('PhoneVerificationScreen: Auth error detected: ${next.error}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${next.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify Phone'),
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Verify Your Phone',
                    style: theme.textTheme.headlineMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _isCodeSent
                        ? 'We\'ve sent a verification code to your phone number. Please enter it below.'
                        : 'Enter your phone number to receive a verification code.',
                    style: theme.textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  TextFormField(
                    controller: _phoneController,
                    keyboardType: TextInputType.phone,
                    textInputAction: TextInputAction.next,
                    decoration: const InputDecoration(
                      labelText: 'Phone Number',
                      hintText: 'Enter your phone number',
                      prefixIcon: Icon(Icons.phone_outlined),
                      prefixText: '+91 ',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your phone number';
                      }
                      if (value.length != 10) {
                        return 'Please enter a valid 10-digit phone number';
                      }
                      return null;
                    },
                  ),
                  if (_isCodeSent) ...[
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _codeController,
                      keyboardType: TextInputType.number,
                      textInputAction: TextInputAction.done,
                      decoration: const InputDecoration(
                        labelText: 'Verification Code',
                        hintText: 'Enter 6-digit code',
                        prefixIcon: Icon(Icons.lock_outline),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter the verification code';
                        }
                        if (value.length != 6) {
                          return 'Code must be 6 digits';
                        }
                        return null;
                      },
                    ),
                  ],
                  const SizedBox(height: 24),
                  Consumer(
                    builder: (context, ref, child) {
                      final authState = ref.watch(authStateProvider);
                      final isLoading = authState.isLoading;

                      return ElevatedButton(
                        onPressed: isLoading
                            ? null
                            : _isCodeSent
                                ? _onVerifyPressed
                                : _onSendCodePressed,
                        child: isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(_isCodeSent ? 'Verify' : 'Send Code'),
                      );
                    },
                  ),
                  if (_isCodeSent) ...[
                    const SizedBox(height: 16),
                    TextButton(
                      onPressed: _isResendEnabled ? _onResendCodePressed : null,
                      child: Text(
                        _isResendEnabled
                            ? 'Resend Code'
                            : 'Resend Code in $_resendCountdown seconds',
                      ),
                    ),
                  ],
                  const SizedBox(height: 24),
                  Consumer(
                    builder: (context, ref, child) {
                      final authState = ref.watch(authStateProvider);
                      return authState.error != null
                          ? Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: Text(
                                authState.error!,
                                style: TextStyle(
                                  color: theme.colorScheme.error,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            )
                          : const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
