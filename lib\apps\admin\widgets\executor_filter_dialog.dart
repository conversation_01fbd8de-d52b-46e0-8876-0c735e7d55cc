import 'package:flutter/material.dart';

class ExecutorFilterDialog extends StatefulWidget {
  final Map<String, dynamic> currentFilters;

  const ExecutorFilterDialog({
    super.key,
    required this.currentFilters,
  });

  @override
  State<ExecutorFilterDialog> createState() => _ExecutorFilterDialogState();
}

class _ExecutorFilterDialogState extends State<ExecutorFilterDialog> {
  late String? _status;
  late String? _role;

  @override
  void initState() {
    super.initState();
    _status = widget.currentFilters['status'] as String?;
    _role = widget.currentFilters['role'] as String?;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Filter Executors'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DropdownButtonFormField<String?>(
            value: _status,
            decoration: const InputDecoration(
              labelText: 'Status',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(
                value: null,
                child: Text('All'),
              ),
              DropdownMenuItem(
                value: 'active',
                child: Text('Active'),
              ),
              DropdownMenuItem(
                value: 'inactive',
                child: Text('Inactive'),
              ),
              DropdownMenuItem(
                value: 'pending',
                child: Text('Pending'),
              ),
            ],
            onChanged: (value) {
              setState(() {
                _status = value;
              });
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String?>(
            value: _role,
            decoration: const InputDecoration(
              labelText: 'Role',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(
                value: null,
                child: Text('All'),
              ),
              DropdownMenuItem(
                value: 'admin',
                child: Text('Admin'),
              ),
              DropdownMenuItem(
                value: 'manager',
                child: Text('Manager'),
              ),
              DropdownMenuItem(
                value: 'supervisor',
                child: Text('Supervisor'),
              ),
            ],
            onChanged: (value) {
              setState(() {
                _role = value;
              });
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context, {
              'status': null,
              'role': null,
            });
          },
          child: const Text('Clear'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context, {
              'status': _status,
              'role': _role,
            });
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }
}
