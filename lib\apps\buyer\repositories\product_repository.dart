import '../../../shared/models/product/product_model.dart';

/// Repository interface for managing products
abstract class ProductRepository {
  /// Get featured products
  Future<List<ProductModel>> getFeaturedProducts();

  /// Get latest products
  Future<List<ProductModel>> getLatestProducts();

  /// Get product by ID
  Future<ProductModel?> getProductById(String id);

  /// Get products by category
  Future<List<ProductModel>> getProductsByCategory(String categoryId);

  /// Get products by seller
  Future<List<ProductModel>> getProductsBySeller(String sellerId);

  /// Search products
  Future<List<ProductModel>> searchProducts(String query);

  /// Get products by IDs
  Future<List<ProductModel>> getProductsByIds(List<String> ids);

  /// Get products with filters
  Future<List<ProductModel>> getProductsWithFilters({
    String? categoryId,
    String? sellerId,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
    bool? sortAscending,
    ProductStatus? status,
    ProductType? type,
    bool? isFeatured,
    bool? isApproved,
    bool? isNew,
    String? searchQuery,
    int? limit,
  });

  /// Get product with full seller details
  Future<ProductModel> getProductWithSellerDetails(String id);

  /// Get all products
  Future<List<ProductModel>> getProducts();
}
