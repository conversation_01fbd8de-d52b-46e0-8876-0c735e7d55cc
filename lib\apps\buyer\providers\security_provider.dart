import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../shared/services/auth/auth_service.dart';
import '../../../shared/providers/auth_provider.dart';

// Provider for password management
final passwordProvider = AsyncNotifierProvider<PasswordNotifier, void>(() {
  return PasswordNotifier();
});

class PasswordNotifier extends AsyncNotifier<void> {
  late final AuthService _authService;

  @override
  Future<void> build() async {
    _authService = ref.read(authServiceProvider);
  }

  Future<void> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    state = const AsyncLoading();
    try {
      // First reauthenticate with current password
      await _authService.reauthenticate(currentPassword);
      // Then update password
      await _authService.updatePassword(newPassword);
      state = const AsyncData(null);
    } catch (e, st) {
      state = AsyncError(e, st);
      rethrow;
    }
  }
}

// Provider for two-factor authentication
final twoFactorAuthProvider = StateNotifierProvider<TwoFactorAuthNotifier, bool>((ref) {
  return TwoFactorAuthNotifier(ref);
});

class TwoFactorAuthNotifier extends StateNotifier<bool> {
  final Ref _ref;
  final _secureStorage = const FlutterSecureStorage();
  static const _twoFactorEnabledKey = 'buyer_2fa_enabled';

  TwoFactorAuthNotifier(this._ref) : super(false) {
    _loadTwoFactorStatus();
  }

  Future<void> _loadTwoFactorStatus() async {
    try {
      final value = await _secureStorage.read(key: _twoFactorEnabledKey);
      state = value == 'true';
    } catch (e) {
      debugPrint('Error loading 2FA status: $e');
      state = false;
    }
  }

  Future<void> setTwoFactorEnabled(bool enabled) async {
    try {
      // Use _ref to access auth service for user verification
      final authService = _ref.read(authServiceProvider);
      final user = await authService.getCurrentUser();

      if (user == null) {
        throw Exception('User not authenticated');
      }

      await _secureStorage.write(
        key: _twoFactorEnabledKey,
        value: enabled.toString(),
      );
      state = enabled;
    } catch (e) {
      debugPrint('Error setting 2FA status: $e');
      rethrow;
    }
  }
}

// Provider for managing connected devices
final connectedDevicesProvider = AsyncNotifierProvider<ConnectedDevicesNotifier, List<Map<String, dynamic>>>(() {
  return ConnectedDevicesNotifier();
});

class ConnectedDevicesNotifier extends AsyncNotifier<List<Map<String, dynamic>>> {
  @override
  Future<List<Map<String, dynamic>>> build() async {
    return _loadConnectedDevices();
  }

  Future<List<Map<String, dynamic>>> _loadConnectedDevices() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return [];

      // In a real app, you would fetch this from Firebase
      // For now, we'll return a mock list with the current device
      return [
        {
          'id': 'current_device',
          'name': 'Current Device',
          'lastActive': DateTime.now().toString(),
          'isCurrent': true,
        }
      ];
    } catch (e) {
      debugPrint('Error loading connected devices: $e');
      return [];
    }
  }

  Future<void> removeDevice(String deviceId) async {
    state = const AsyncLoading();
    try {
      // In a real app, you would remove the device from Firebase
      // For now, we'll just simulate it
      await Future.delayed(const Duration(seconds: 1));
      
      final devices = await _loadConnectedDevices();
      state = AsyncData(devices.where((device) => device['id'] != deviceId).toList());
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}

// Biometric authentication provider
class BiometricAuthState {
  final bool isAvailable;
  final bool isEnabled;
  final String? error;

  BiometricAuthState({
    this.isAvailable = false,
    this.isEnabled = false,
    this.error,
  });

  BiometricAuthState copyWith({
    bool? isAvailable,
    bool? isEnabled,
    String? error,
  }) {
    return BiometricAuthState(
      isAvailable: isAvailable ?? this.isAvailable,
      isEnabled: isEnabled ?? this.isEnabled,
      error: error,
    );
  }
}

final biometricAuthStateProvider = StateNotifierProvider<BiometricAuthNotifier, BiometricAuthState>((ref) {
  return BiometricAuthNotifier();
});

class BiometricAuthNotifier extends StateNotifier<BiometricAuthState> {
  final _localAuth = LocalAuthentication();
  final _secureStorage = const FlutterSecureStorage();
  static const _biometricEnabledKey = 'buyer_biometric_enabled';

  BiometricAuthNotifier() : super(BiometricAuthState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      // Check if biometric authentication is available
      final isAvailable = await _checkBiometricAvailability();
      
      // Check if biometric authentication is enabled
      final isEnabled = await _isBiometricEnabled();
      
      state = state.copyWith(
        isAvailable: isAvailable,
        isEnabled: isEnabled,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
      );
    }
  }

  Future<bool> _checkBiometricAvailability() async {
    try {
      final canCheckBiometrics = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return canCheckBiometrics && isDeviceSupported;
    } catch (e) {
      debugPrint('Error checking biometric availability: $e');
      return false;
    }
  }

  Future<bool> _isBiometricEnabled() async {
    try {
      final value = await _secureStorage.read(key: _biometricEnabledKey);
      return value == 'true';
    } catch (e) {
      debugPrint('Error checking if biometric is enabled: $e');
      return false;
    }
  }

  Future<void> setBiometricEnabled(bool enabled) async {
    try {
      if (enabled && !state.isAvailable) {
        throw Exception('Biometric authentication is not available on this device');
      }

      if (enabled) {
        // Authenticate before enabling
        final authenticated = await _localAuth.authenticate(
          localizedReason: 'Authenticate to enable biometric login',
          options: const AuthenticationOptions(
            biometricOnly: true,
            stickyAuth: true,
          ),
        );

        if (!authenticated) {
          throw Exception('Authentication failed');
        }
      }

      await _secureStorage.write(
        key: _biometricEnabledKey,
        value: enabled.toString(),
      );

      state = state.copyWith(isEnabled: enabled);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  Future<bool> authenticate({String reason = 'Authenticate to continue'}) async {
    try {
      if (!state.isAvailable) {
        throw Exception('Biometric authentication is not available on this device');
      }

      final authenticated = await _localAuth.authenticate(
        localizedReason: reason,
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      return authenticated;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }
}

// Auto lock provider
final autoLockProvider = StateNotifierProvider<AutoLockNotifier, bool>((ref) {
  return AutoLockNotifier();
});

class AutoLockNotifier extends StateNotifier<bool> {
  final _prefs = SharedPreferences.getInstance();
  static const _autoLockKey = 'buyer_auto_lock';

  AutoLockNotifier() : super(false) {
    _loadAutoLock();
  }

  Future<void> _loadAutoLock() async {
    try {
      final prefs = await _prefs;
      state = prefs.getBool(_autoLockKey) ?? false;
    } catch (e) {
      debugPrint('Error loading auto lock setting: $e');
    }
  }

  Future<void> setAutoLock(bool enabled) async {
    try {
      final prefs = await _prefs;
      await prefs.setBool(_autoLockKey, enabled);
      state = enabled;
    } catch (e) {
      debugPrint('Error setting auto lock: $e');
      rethrow;
    }
  }
}

// Auto lock timeout provider
final autoLockTimeoutProvider = StateNotifierProvider<AutoLockTimeoutNotifier, int>((ref) {
  return AutoLockTimeoutNotifier();
});

class AutoLockTimeoutNotifier extends StateNotifier<int> {
  final _prefs = SharedPreferences.getInstance();
  static const _autoLockTimeoutKey = 'buyer_auto_lock_timeout';

  AutoLockTimeoutNotifier() : super(5) {
    _loadAutoLockTimeout();
  }

  Future<void> _loadAutoLockTimeout() async {
    try {
      final prefs = await _prefs;
      state = prefs.getInt(_autoLockTimeoutKey) ?? 5;
    } catch (e) {
      debugPrint('Error loading auto lock timeout: $e');
    }
  }

  Future<void> setAutoLockTimeout(int minutes) async {
    try {
      final prefs = await _prefs;
      await prefs.setInt(_autoLockTimeoutKey, minutes);
      state = minutes;
    } catch (e) {
      debugPrint('Error setting auto lock timeout: $e');
      rethrow;
    }
  }
}

// Show balance provider
final showBalanceProvider = StateNotifierProvider<ShowBalanceNotifier, bool>((ref) {
  return ShowBalanceNotifier();
});

class ShowBalanceNotifier extends StateNotifier<bool> {
  final _prefs = SharedPreferences.getInstance();
  static const _showBalanceKey = 'buyer_show_balance';

  ShowBalanceNotifier() : super(true) {
    _loadShowBalance();
  }

  Future<void> _loadShowBalance() async {
    try {
      final prefs = await _prefs;
      state = prefs.getBool(_showBalanceKey) ?? true;
    } catch (e) {
      debugPrint('Error loading show balance setting: $e');
    }
  }

  Future<void> setShowBalance(bool enabled) async {
    try {
      final prefs = await _prefs;
      await prefs.setBool(_showBalanceKey, enabled);
      state = enabled;
    } catch (e) {
      debugPrint('Error setting show balance: $e');
      rethrow;
    }
  }
}

// Transaction notifications provider
final transactionNotificationsProvider = StateNotifierProvider<TransactionNotificationsNotifier, bool>((ref) {
  return TransactionNotificationsNotifier();
});

class TransactionNotificationsNotifier extends StateNotifier<bool> {
  final _prefs = SharedPreferences.getInstance();
  static const _transactionNotificationsKey = 'buyer_transaction_notifications';

  TransactionNotificationsNotifier() : super(true) {
    _loadTransactionNotifications();
  }

  Future<void> _loadTransactionNotifications() async {
    try {
      final prefs = await _prefs;
      state = prefs.getBool(_transactionNotificationsKey) ?? true;
    } catch (e) {
      debugPrint('Error loading transaction notifications setting: $e');
    }
  }

  Future<void> setTransactionNotifications(bool enabled) async {
    try {
      final prefs = await _prefs;
      await prefs.setBool(_transactionNotificationsKey, enabled);
      state = enabled;
    } catch (e) {
      debugPrint('Error setting transaction notifications: $e');
      rethrow;
    }
  }
}
