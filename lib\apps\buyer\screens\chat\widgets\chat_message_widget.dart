import 'package:flutter/material.dart';
import '../../../../../shared/models/chat/chat_model.dart';

class ChatMessageWidget extends StatelessWidget {
  final ChatMessage message;
  final bool isPlaying;
  final VoidCallback onPlayVoice;
  final VoidCallback onStopVoice;

  const ChatMessageWidget({
    super.key,
    required this.message,
    required this.isPlaying,
    required this.onPlayVoice,
    required this.onStopVoice,
  });

  @override
  Widget build(BuildContext context) {
    final isUser = message.sender == ChatSenderType.user;

    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isUser
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: _buildMessageContent(context),
      ),
    );
  }

  Widget _buildMessageContent(BuildContext context) {
    switch (message.type) {
      case ChatMessageType.text:
        return Text(
          message.content,
          style: TextStyle(
            color: message.sender == ChatSenderType.user
                ? Colors.white
                : Theme.of(context).textTheme.bodyLarge?.color,
          ),
        );
      case ChatMessageType.voice:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(
                isPlaying ? Icons.stop : Icons.play_arrow,
                color: message.sender == ChatSenderType.user
                    ? Colors.white
                    : Theme.of(context).iconTheme.color,
              ),
              onPressed: isPlaying ? onStopVoice : onPlayVoice,
            ),
            Text(
              'Voice Message',
              style: TextStyle(
                color: message.sender == ChatSenderType.user
                    ? Colors.white
                    : Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
          ],
        );
      case ChatMessageType.product:
        return Text(
          'Product: ${message.content}',
          style: TextStyle(
            color: message.sender == ChatSenderType.user
                ? Colors.white
                : Theme.of(context).textTheme.bodyLarge?.color,
          ),
        );
      case ChatMessageType.order:
        return Text(
          'Order: ${message.content}',
          style: TextStyle(
            color: message.sender == ChatSenderType.user
                ? Colors.white
                : Theme.of(context).textTheme.bodyLarge?.color,
          ),
        );
    }
  }
}
