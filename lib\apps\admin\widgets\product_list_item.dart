import 'package:flutter/material.dart';
import 'package:shivish/shared/models/product/product_model.dart';
import 'package:shivish/shared/ui_components/badges/status_badge.dart';
import 'package:shivish/apps/admin/widgets/product_status_dialog.dart';
import 'package:shivish/apps/admin/widgets/product_delete_dialog.dart';

class ProductListItem extends StatelessWidget {
  final ProductModel product;
  final Function(ProductModel, Map<String, dynamic>) onStatusUpdate;
  final Function(ProductModel) onDelete;

  const ProductListItem({
    super.key,
    required this.product,
    required this.onStatusUpdate,
    required this.onDelete,
  });

  Future<void> _showStatusDialog(BuildContext context) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => ProductStatusDialog(product: product),
    );

    if (result != null) {
      onStatusUpdate(product, result);
    }
  }

  Future<void> _showDeleteDialog(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ProductDeleteDialog(product: product),
    );

    if (confirmed == true) {
      onDelete(product);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        title: Row(
          children: [
            Text(product.name),
            const SizedBox(width: 8),
            StatusBadge(
              isActive: product.isApproved,
              onToggle: () => _showStatusDialog(context),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Text(
              product.description,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.attach_money,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                Text(
                  product.price.toStringAsFixed(2),
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.inventory_2,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                Text(
                  'Stock: ${product.quantity}',
                  style: theme.textTheme.bodyMedium,
                ),
                if (product.rating != null) ...[
                  const SizedBox(width: 16),
                  Icon(
                    Icons.star,
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  Text(
                    '${product.rating!.toStringAsFixed(1)} (${product.reviewCount ?? 0})',
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _showStatusDialog(context),
              tooltip: 'Update Status',
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteDialog(context),
              tooltip: 'Delete Product',
            ),
          ],
        ),
      ),
    );
  }
}
