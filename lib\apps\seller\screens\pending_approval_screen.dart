import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/seller_routes.dart';
import 'package:shivish/apps/seller/application/seller_provider.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

class PendingApprovalScreen extends ConsumerStatefulWidget {
  const PendingApprovalScreen({super.key});

  @override
  ConsumerState<PendingApprovalScreen> createState() => _PendingApprovalScreenState();
}

class _PendingApprovalScreenState extends ConsumerState<PendingApprovalScreen> {
  StreamSubscription? _sellerSubscription;
  bool _isCheckingStatus = false;
  final DatabaseService _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());

  @override
  void initState() {
    super.initState();
    // Start listening for approval status changes after a short delay
    // to ensure the widget is fully mounted
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _setupApprovalListener();
      }
    });
  }

  @override
  void dispose() {
    // Cancel the subscription when the widget is disposed
    _sellerSubscription?.cancel();
    super.dispose();
  }

  // Set up a listener for changes to the seller's approval status
  void _setupApprovalListener() {
    final userId = ref.read(authServiceProvider).currentUser?.id;
    if (userId == null) return;

    // Listen to the seller document for changes
    _sellerSubscription = _databaseService
        .watchRecord('sellers', userId)
        .listen((data) {
      if (!mounted) return;

      // Check if the document exists and is approved
      if (data != null && data['is_approved'] == true) {
        // Refresh the seller data in the provider
        ref.read(sellerProvider.notifier).getSeller(userId);

        // Navigate to home screen
        _navigateToHome();
      }
    }, onError: (error) {
      debugPrint('Error listening to seller approval status: $error');
    });
  }

  // Check approval status manually
  Future<void> _checkApprovalStatus() async {
    if (_isCheckingStatus) return;

    setState(() {
      _isCheckingStatus = true;
    });

    try {
      final userId = ref.read(authServiceProvider).currentUser?.id;
      if (userId == null) return;

      // Refresh the seller data
      await ref.read(sellerProvider.notifier).getSeller(userId);

      // Get the updated seller
      final seller = ref.read(sellerProvider).value;

      if (seller != null && seller.isApproved) {
        _navigateToHome();
      } else {
        // Show a message that the account is still pending
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Your account is still pending approval.'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error checking approval status: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error checking status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCheckingStatus = false;
        });
      }
    }
  }

  // Navigate to home screen with a success message
  void _navigateToHome() {
    if (!mounted) return;

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Your account has been approved! Welcome to Shivish.'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
      ),
    );

    // Navigate to home screen
    SellerRoutes.navigateToHome(context);
  }

  @override
  Widget build(BuildContext context) {
    final sellerState = ref.watch(sellerProvider);
    final seller = sellerState.value;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Account Pending'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await ref.read(authServiceProvider).signOut();
              if (context.mounted) {
                SellerRoutes.navigateToLogin(context);
              }
            },
          ),
        ],
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.pending_outlined,
                size: 80,
                color: Colors.orange,
              ),
              const SizedBox(height: 24),
              Text(
                'Account Pending Approval',
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Your seller account is currently under review. This usually takes 1-2 business days.',
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              if (seller != null) ...[
                Text(
                  'Business Name: ${seller.businessName}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'Category: ${seller.category.name}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'Registration Date: ${seller.createdAt?.toString().split(' ')[0] ?? 'N/A'}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
              const SizedBox(height: 32),
              const Text(
                'We will notify you via email once your account has been approved.',
                style: TextStyle(
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _isCheckingStatus ? null : _checkApprovalStatus,
                icon: _isCheckingStatus
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2)
                      )
                    : const Icon(Icons.refresh),
                label: const Text('Check Approval Status'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
