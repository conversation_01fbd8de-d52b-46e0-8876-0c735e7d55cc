import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/providers/alarm_settings_provider.dart';
import '../../../../shared/providers/mock_alarm_settings_provider.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../widgets/sound_picker_dialog.dart';

class AlarmSettingsScreen extends ConsumerWidget {
  const AlarmSettingsScreen({super.key});

  // Helper function to update settings with fallback to mock provider
  void _updateSetting(
      WidgetRef ref, Function(dynamic notifier) updateFunction) {
    try {
      updateFunction(ref.read(alarmSettingsProvider.notifier));
    } catch (e) {
      updateFunction(ref.read(mockAlarmSettingsStateProvider.notifier));
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final user = ref.watch(authProvider);
    if (user == null) {
      return const Scaffold(
        body: Center(
          child: Text('Please login to view settings'),
        ),
      );
    }

    // Try to use the real alarm settings provider, fall back to mock if it fails
    AlarmSettings settings;
    try {
      settings = ref.watch(alarmSettingsProvider);
    } catch (e) {
      // If the real provider fails, use the mock provider
      settings = ref.watch(mockAlarmSettingsStateProvider);
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Alarm Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Default Settings',
                    style: theme.textTheme.titleMedium,
                  ),
                ),
                const Divider(height: 0),
                SwitchListTile(
                  title: const Text('Vibrate on Alarm'),
                  subtitle: const Text('Enable vibration when alarm triggers'),
                  value: settings.vibrateOnAlarm,
                  onChanged: (value) {
                    _updateSetting(ref,
                        (notifier) => notifier.updateVibrateOnAlarm(value));
                  },
                ),
                SwitchListTile(
                  title: const Text('Snooze Enabled'),
                  subtitle: const Text('Allow snoozing alarms'),
                  value: settings.snoozeEnabled,
                  onChanged: (value) {
                    _updateSetting(
                        ref, (notifier) => notifier.updateSnoozeEnabled(value));
                  },
                ),
                ListTile(
                  title: const Text('Snooze Duration'),
                  subtitle: const Text('Set default snooze time'),
                  trailing: DropdownButton<int>(
                    value: settings.snoozeDuration,
                    items: const [
                      DropdownMenuItem(
                        value: 5,
                        child: Text('5 minutes'),
                      ),
                      DropdownMenuItem(
                        value: 10,
                        child: Text('10 minutes'),
                      ),
                      DropdownMenuItem(
                        value: 15,
                        child: Text('15 minutes'),
                      ),
                      DropdownMenuItem(
                        value: 30,
                        child: Text('30 minutes'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        _updateSetting(ref,
                            (notifier) => notifier.updateSnoozeDuration(value));
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Sound Settings',
                    style: theme.textTheme.titleMedium,
                  ),
                ),
                const Divider(height: 0),
                ListTile(
                  title: const Text('Default Alarm Sound'),
                  subtitle: Text(settings.defaultAlarmSound),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) => const SoundPickerDialog(),
                    );
                  },
                ),
                ListTile(
                  title: const Text('Volume'),
                  subtitle: const Text('Set default alarm volume'),
                  trailing: SizedBox(
                    width: 200,
                    child: Slider(
                      value: settings.volume,
                      min: 0.0,
                      max: 1.0,
                      divisions: 10,
                      label: '${(settings.volume * 100).round()}%',
                      onChanged: (value) {
                        _updateSetting(
                            ref, (notifier) => notifier.updateVolume(value));
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Fade In Settings',
                    style: theme.textTheme.titleMedium,
                  ),
                ),
                const Divider(height: 0),
                SwitchListTile(
                  title: const Text('Enable Fade In'),
                  subtitle: const Text('Gradually increase alarm volume'),
                  value: settings.fadeInEnabled,
                  onChanged: (value) {
                    _updateSetting(
                        ref, (notifier) => notifier.updateFadeInEnabled(value));
                  },
                ),
                if (settings.fadeInEnabled)
                  ListTile(
                    title: const Text('Fade In Duration'),
                    subtitle: const Text('Set fade in duration in seconds'),
                    trailing: SizedBox(
                      width: 200,
                      child: Slider(
                        value: settings.fadeInDuration.toDouble(),
                        min: 5,
                        max: 60,
                        divisions: 11,
                        label: '${settings.fadeInDuration} seconds',
                        onChanged: (value) {
                          _updateSetting(
                              ref,
                              (notifier) =>
                                  notifier.updateFadeInDuration(value.round()));
                        },
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Auto Dismiss Settings',
                    style: theme.textTheme.titleMedium,
                  ),
                ),
                const Divider(height: 0),
                SwitchListTile(
                  title: const Text('Enable Auto Dismiss'),
                  subtitle:
                      const Text('Automatically dismiss alarm after delay'),
                  value: settings.autoDismissEnabled,
                  onChanged: (value) {
                    _updateSetting(ref,
                        (notifier) => notifier.updateAutoDismissEnabled(value));
                  },
                ),
                if (settings.autoDismissEnabled)
                  ListTile(
                    title: const Text('Auto Dismiss Delay'),
                    subtitle: const Text('Set auto dismiss delay in minutes'),
                    trailing: SizedBox(
                      width: 200,
                      child: Slider(
                        value: settings.autoDismissDelay.toDouble(),
                        min: 1,
                        max: 30,
                        divisions: 29,
                        label: '${settings.autoDismissDelay} minutes',
                        onChanged: (value) {
                          _updateSetting(
                              ref,
                              (notifier) => notifier
                                  .updateAutoDismissDelay(value.round()));
                        },
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'AI Alarm Settings',
                    style: theme.textTheme.titleMedium,
                  ),
                ),
                const Divider(height: 0),
                SwitchListTile(
                  title: const Text('Smart Wake-up'),
                  subtitle: const Text(
                      'AI adjusts wake-up time based on sleep patterns'),
                  value: settings.smartWakeUp,
                  onChanged: (value) {
                    _updateSetting(
                        ref, (notifier) => notifier.updateSmartWakeUp(value));
                  },
                ),
                SwitchListTile(
                  title: const Text('Sleep Tracking'),
                  subtitle: const Text(
                      'Track sleep patterns for better wake-up times'),
                  value: settings.sleepTracking,
                  onChanged: (value) {
                    _updateSetting(
                        ref, (notifier) => notifier.updateSleepTracking(value));
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Alarm Information Display',
                    style: theme.textTheme.titleMedium,
                  ),
                ),
                const Divider(height: 0),
                SwitchListTile(
                  title: const Text('Show Time'),
                  subtitle: const Text('Display alarm time'),
                  value: settings.showAlarmTime,
                  onChanged: (value) {
                    _updateSetting(
                        ref, (notifier) => notifier.updateShowAlarmTime(value));
                  },
                ),
                SwitchListTile(
                  title: const Text('Show Date'),
                  subtitle: const Text('Display alarm date'),
                  value: settings.showAlarmDate,
                  onChanged: (value) {
                    _updateSetting(
                        ref, (notifier) => notifier.updateShowAlarmDate(value));
                  },
                ),
                SwitchListTile(
                  title: const Text('Show Duration'),
                  subtitle: const Text('Display alarm duration'),
                  value: settings.showAlarmDuration,
                  onChanged: (value) {
                    _updateSetting(ref,
                        (notifier) => notifier.updateShowAlarmDuration(value));
                  },
                ),
                SwitchListTile(
                  title: const Text('Show Repeat'),
                  subtitle: const Text('Display repeat settings'),
                  value: settings.showAlarmRepeat,
                  onChanged: (value) {
                    _updateSetting(ref,
                        (notifier) => notifier.updateShowAlarmRepeat(value));
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Notifications',
                    style: theme.textTheme.titleMedium,
                  ),
                ),
                const Divider(height: 0),
                SwitchListTile(
                  title: const Text('Show on Lock Screen'),
                  subtitle:
                      const Text('Display alarm notifications on lock screen'),
                  value: settings.showOnLockScreen,
                  onChanged: (value) {
                    _updateSetting(ref,
                        (notifier) => notifier.updateShowOnLockScreen(value));
                  },
                ),
                SwitchListTile(
                  title: const Text('Sound in Silent Mode'),
                  subtitle: const Text('Play alarm sound even in silent mode'),
                  value: settings.soundInSilentMode,
                  onChanged: (value) {
                    _updateSetting(ref,
                        (notifier) => notifier.updateSoundInSilentMode(value));
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
