/// Model class for a shopping list item
class ShoppingListItem {
  /// Creates a new shopping list item
  ShoppingListItem({
    required this.name,
    this.quantity = 1,
    this.unit = 'kg',
    this.isChecked = false,
  });

  /// The name of the item
  String name;
  
  /// The quantity of the item
  double quantity;
  
  /// The unit of measurement (kg, ltr, pcs, etc.)
  String unit;
  
  /// Whether the item is checked off the list
  bool isChecked;

  /// Create a copy of this item with the given fields replaced with new values
  ShoppingListItem copyWith({
    String? name,
    double? quantity,
    String? unit,
    bool? isChecked,
  }) {
    return ShoppingListItem(
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      isChecked: isChecked ?? this.isChecked,
    );
  }

  /// Convert this item to a map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'quantity': quantity,
      'unit': unit,
      'isChecked': isChecked,
    };
  }

  /// Create an item from a map
  factory ShoppingListItem.fromMap(Map<String, dynamic> map) {
    return ShoppingListItem(
      name: map['name'] as String,
      quantity: map['quantity'] as double,
      unit: map['unit'] as String,
      isChecked: map['isChecked'] as bool,
    );
  }
}
