import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/services/order/order_service.dart';
import 'package:shivish/shared/models/order/order_model.dart';

part 'orders_cubit.freezed.dart';

@freezed
sealed class OrdersState with _$OrdersState {
  const factory OrdersState.initial() = _Initial;
  const factory OrdersState.loading() = _Loading;
  const factory OrdersState.loaded(List<OrderModel> orders) = _Loaded;
  const factory OrdersState.error(String message) = _Error;
}

class OrdersCubit extends Cubit<OrdersState> {
  final OrderService _orderService;

  OrdersCubit(this._orderService) : super(const OrdersState.initial()) {
    loadOrders();
  }

  Future<void> loadOrders() async {
    emit(const OrdersState.loading());
    try {
      final orders = await _orderService.getSellerOrders();
      emit(OrdersState.loaded(orders));
    } catch (e) {
      emit(OrdersState.error(e.toString()));
    }
  }
}
