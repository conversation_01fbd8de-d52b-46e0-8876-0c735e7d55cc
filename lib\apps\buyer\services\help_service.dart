import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shivish/shared/models/help/help_article.dart';
import 'package:shivish/shared/models/help/help_suggestion.dart';
import 'package:shivish/shared/services/chat/chat_service.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';

final helpServiceProvider = Provider<HelpService>((ref) {
  final chatService = ref.watch(chatServiceProvider);
  return HelpService(chatService);
});

class HelpService {
  final DatabaseService _databaseService;
  final SupabaseClient _supabase;
  final ChatService _chatService;
  final _logger = getLogger('HelpService');

  HelpService(this._chatService, {
    DatabaseService? databaseService,
    SupabaseClient? supabase,
  }) : _databaseService = databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment()),
       _supabase = supabase ?? Supabase.instance.client;

  // Mock data for articles
  final List<HelpArticle> _articles = [
    HelpArticle(
      id: '1',
      title: 'How to place an order',
      question: 'How do I place an order?',
      answer: '''
1. Browse through our products
2. Add items to your cart
3. Go to checkout
4. Enter delivery details
5. Choose payment method
6. Confirm your order''',
      status: 'open',
      date: DateTime.now().toString(),
    ),
    HelpArticle(
      id: '2',
      title: 'Payment methods',
      question: 'What payment methods are accepted?',
      answer: '''
We accept the following payment methods:
- Credit/Debit Cards
- UPI
- Net Banking
- Wallet
- Cash on Delivery''',
      status: 'open',
      date: DateTime.now().toString(),
    ),
    HelpArticle(
      id: '3',
      title: 'Return policy',
      question: 'What is your return policy?',
      answer: '''
Our return policy includes:
- 7-day return window
- Product must be unused
- Original packaging required
- Free return shipping''',
      status: 'open',
      date: DateTime.now().toString(),
    ),
  ];

  Future<List<HelpArticle>> getTickets() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return [];

      // Get all tickets for the user
      final allTickets = await _databaseService.getAll('user_tickets');

      final userTickets = allTickets
          .where((ticketData) => ticketData['userId'] == currentUser.id)
          .toList();

      // Sort by date (descending)
      userTickets.sort((a, b) {
        final dateA = DateTime.tryParse(a['date'] as String? ?? '') ?? DateTime.now();
        final dateB = DateTime.tryParse(b['date'] as String? ?? '') ?? DateTime.now();
        return dateB.compareTo(dateA);
      });

      return userTickets
          .map((ticketData) => HelpArticle.fromJson(ticketData))
          .toList();
    } catch (e) {
      _logger.severe('Error getting tickets: $e');
      return [];
    }
  }

  Future<void> submitTicket(HelpArticle ticket) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      final ticketData = {
        'id': ticket.id,
        ...ticket.toJson(),
        'userId': currentUser.id,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      await _databaseService.create('user_tickets', ticketData);
      _logger.info('Ticket submitted successfully: ${ticket.id}');
    } catch (e) {
      _logger.severe('Error submitting ticket: $e');
      throw Exception('Failed to submit ticket: $e');
    }
  }

  Future<void> updateTickets(List<HelpArticle> tickets) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) throw Exception('User not authenticated');

      // Update tickets one by one (hybrid storage doesn't have batch operations)
      for (final ticket in tickets) {
        final ticketData = {
          'id': ticket.id,
          ...ticket.toJson(),
          'userId': currentUser.id,
          'updatedAt': DateTime.now().toIso8601String(),
        };

        await _databaseService.update('user_tickets', ticket.id, ticketData);
      }

      _logger.info('Updated ${tickets.length} tickets successfully');
    } catch (e) {
      _logger.severe('Error updating tickets: $e');
      throw Exception('Failed to update tickets: $e');
    }
  }

  Future<List<HelpArticle>> searchArticles(String query) async {
    if (query.isEmpty) return _articles;

    final queryLower = query.toLowerCase();
    return _articles.where((article) {
      return article.title.toLowerCase().contains(queryLower) ||
          article.question.toLowerCase().contains(queryLower) ||
          article.answer.toLowerCase().contains(queryLower);
    }).toList();
  }

  Future<List<HelpSuggestion>> getSuggestions(String query) async {
    try {
      if (query.isEmpty) return [];

      final queryLower = query.toLowerCase();

      // Get all help suggestions
      final allSuggestions = await _databaseService.getAll('help_suggestions');

      // Filter suggestions that contain the query in their keywords
      final matchingSuggestions = allSuggestions
          .where((suggestionData) {
            final keywords = suggestionData['keywords'] as List<dynamic>? ?? [];
            return keywords.any((keyword) =>
                keyword.toString().toLowerCase().contains(queryLower));
          })
          .toList();

      // Sort by relevance (descending)
      matchingSuggestions.sort((a, b) {
        final relevanceA = a['relevance'] as num? ?? 0;
        final relevanceB = b['relevance'] as num? ?? 0;
        return relevanceB.compareTo(relevanceA);
      });

      // Limit to 5 results
      final limitedSuggestions = matchingSuggestions.take(5).toList();

      return limitedSuggestions
          .map((suggestionData) => HelpSuggestion.fromJson(suggestionData))
          .toList();
    } catch (e) {
      _logger.severe('Error getting suggestions: $e');
      return [];
    }
  }

  Future<void> sendEmail(String subject, String body) async {
    final Uri emailLaunchUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      queryParameters: {
        'subject': subject,
        'body': body,
      },
    );

    if (await canLaunchUrl(emailLaunchUri)) {
      await launchUrl(emailLaunchUri);
    } else {
      throw 'Could not launch email';
    }
  }

  Future<void> initiatePhoneCall(String phoneNumber) async {
    final Uri phoneLaunchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );

    if (await canLaunchUrl(phoneLaunchUri)) {
      await launchUrl(phoneLaunchUri);
    } else {
      throw 'Could not launch phone call';
    }
  }

  Future<String> startLiveChat() async {
    try {
          final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Create a new support chat session
      final chatId = await _chatService.createSupportChat();

      // Send initial greeting message
      await _chatService.sendMessage(
        chatId,
        'Hello! How can we help you today?',
      );

      return chatId;
    } catch (e) {
      throw Exception('Failed to start live chat: $e');
    }
  }
}
