import 'package:freezed_annotation/freezed_annotation.dart';
import '../models/login_request.dart';
import '../models/register_request.dart';

part 'auth_event.freezed.dart';

@freezed
sealed class AuthEvent with _$AuthEvent {
  const factory AuthEvent.checkAuthStatus() = CheckAuthStatus;
  const factory AuthEvent.signInWithEmailAndPassword(LoginRequest request) =
      SignInWithEmailAndPassword;
  const factory AuthEvent.signInWithGoogle() = SignInWithGoogle;
  const factory AuthEvent.signInWithApple() = SignInWithApple;
  const factory AuthEvent.register(RegisterRequest request) = Register;
  const factory AuthEvent.signOut() = SignOut;
  const factory AuthEvent.resetPassword(String email) = ResetPassword;
  const factory AuthEvent.updateProfile({
    String? displayName,
    String? photoUrl,
    String? phoneNumber,
  }) = UpdateProfile;
  const factory AuthEvent.deleteAccount() = DeleteAccount;
  const factory AuthEvent.verifyPhone(String phoneNumber) = VerifyPhone;
  const factory AuthEvent.verifyPhoneCode(String code) = VerifyPhoneCode;
}
