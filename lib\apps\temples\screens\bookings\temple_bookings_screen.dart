import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/temple/temple_booking_model.dart';
import '../../../../shared/theme/traditional_colors.dart';
import '../../providers/temple_auth_provider.dart';
import '../../providers/temple_bookings_provider.dart';

class TempleBookingsScreen extends ConsumerStatefulWidget {
  const TempleBookingsScreen({super.key});

  @override
  ConsumerState<TempleBookingsScreen> createState() =>
      _TempleBookingsScreenState();
}

class _TempleBookingsScreenState extends ConsumerState<TempleBookingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadBookings();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadBookings() {
    final temple = ref.read(templeAuthProvider).temple;
    if (temple != null) {
      ref.read(templeBookingsProvider.notifier).loadBookings(temple.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    final bookingsState = ref.watch(templeBookingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Temple Bookings'),
        backgroundColor: TraditionalColors.templeOrange,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: [
            Tab(text: 'Today (${bookingsState.todayBookings.length})'),
            Tab(text: 'Upcoming (${bookingsState.upcomingBookings.length})'),
            Tab(text: 'Completed (${bookingsState.completedBookings.length})'),
            Tab(text: 'All (${bookingsState.allBookings.length})'),
          ],
        ),
        actions: [
          IconButton(onPressed: _loadBookings, icon: const Icon(Icons.refresh)),
          IconButton(
            onPressed: () => _showFilterDialog(),
            icon: const Icon(Icons.filter_list),
          ),
        ],
      ),
      body: bookingsState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildBookingsList(bookingsState.todayBookings),
                _buildBookingsList(bookingsState.upcomingBookings),
                _buildBookingsList(bookingsState.completedBookings),
                _buildBookingsList(bookingsState.allBookings),
              ],
            ),
    );
  }

  Widget _buildBookingsList(List<TempleBooking> bookings) {
    if (bookings.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async => _loadBookings(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: bookings.length,
        itemBuilder: (context, index) {
          final booking = bookings[index];
          return _buildBookingCard(booking);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.book_online, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          const Text(
            'No bookings found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Bookings will appear here when devotees make reservations',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBookingCard(TempleBooking booking) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      booking.status,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getStatusIcon(booking.status),
                    color: _getStatusColor(booking.status),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        booking.bookingNumber,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        booking.devoteeInfo.name,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                _buildStatusBadge(booking.status),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  _formatDate(booking.visitDate),
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
                const SizedBox(width: 16),
                Icon(Icons.currency_rupee, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  booking.totalAmount.toStringAsFixed(0),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Services: ${booking.items.map((item) => item.name).join(', ')}',
              style: TextStyle(fontSize: 14, color: Colors.grey[700]),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _viewBookingDetails(booking),
                    icon: const Icon(Icons.visibility, size: 16),
                    label: const Text('View Details'),
                  ),
                ),
                const SizedBox(width: 8),
                if (booking.status == TempleBookingStatus.pending)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _confirmBooking(booking),
                      icon: const Icon(Icons.check, size: 16),
                      label: const Text('Confirm'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                if (booking.status == TempleBookingStatus.confirmed)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _completeBooking(booking),
                      icon: const Icon(Icons.done_all, size: 16),
                      label: const Text('Complete'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: TraditionalColors.templeOrange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(TempleBookingStatus status) {
    final color = _getStatusColor(status);
    final text = _getStatusText(status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getStatusColor(TempleBookingStatus status) {
    switch (status) {
      case TempleBookingStatus.pending:
        return Colors.orange;
      case TempleBookingStatus.confirmed:
        return Colors.blue;
      case TempleBookingStatus.completed:
        return Colors.green;
      case TempleBookingStatus.cancelled:
        return Colors.red;
      case TempleBookingStatus.refunded:
        return Colors.purple;
      case TempleBookingStatus.noShow:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(TempleBookingStatus status) {
    switch (status) {
      case TempleBookingStatus.pending:
        return Icons.hourglass_empty;
      case TempleBookingStatus.confirmed:
        return Icons.check_circle;
      case TempleBookingStatus.completed:
        return Icons.done_all;
      case TempleBookingStatus.cancelled:
        return Icons.cancel;
      case TempleBookingStatus.refunded:
        return Icons.money_off;
      case TempleBookingStatus.noShow:
        return Icons.person_off;
    }
  }

  String _getStatusText(TempleBookingStatus status) {
    switch (status) {
      case TempleBookingStatus.pending:
        return 'Pending';
      case TempleBookingStatus.confirmed:
        return 'Confirmed';
      case TempleBookingStatus.completed:
        return 'Completed';
      case TempleBookingStatus.cancelled:
        return 'Cancelled';
      case TempleBookingStatus.refunded:
        return 'Refunded';
      case TempleBookingStatus.noShow:
        return 'No Show';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _viewBookingDetails(TempleBooking booking) {
    context.push('/temples/bookings/${booking.id}');
  }

  void _confirmBooking(TempleBooking booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Booking'),
        content: Text(
          'Confirm booking ${booking.bookingNumber} for ${booking.devoteeInfo.name}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref
                  .read(templeBookingsProvider.notifier)
                  .updateBookingStatus(
                    booking.id,
                    TempleBookingStatus.confirmed,
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  void _completeBooking(TempleBooking booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Complete Booking'),
        content: Text('Mark booking ${booking.bookingNumber} as completed?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref
                  .read(templeBookingsProvider.notifier)
                  .updateBookingStatus(
                    booking.id,
                    TempleBookingStatus.completed,
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: TraditionalColors.templeOrange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Complete'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Bookings'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [Text('Filter options will be implemented here')],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
