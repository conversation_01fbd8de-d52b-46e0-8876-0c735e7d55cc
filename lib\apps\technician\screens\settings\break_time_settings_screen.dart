import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';
import 'package:shivish/shared/models/technician/break_time_settings_model.dart';
import 'package:shivish/apps/technician/providers/break_time_settings_provider.dart';

class BreakTimeSettingsScreen extends ConsumerStatefulWidget {
  const BreakTimeSettingsScreen({super.key});

  @override
  ConsumerState<BreakTimeSettingsScreen> createState() =>
      _BreakTimeSettingsScreenState();
}

class _BreakTimeSettingsScreenState
    extends ConsumerState<BreakTimeSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _lunchStartController = TextEditingController();
  final _lunchEndController = TextEditingController();
  final _teaStartController = TextEditingController();
  final _teaEndController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Load break time settings
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final settings = ref.read(breakTimeSettingsProvider).value;
      if (settings != null) {
        _lunchStartController.text = settings.lunchStartTime;
        _lunchEndController.text = settings.lunchEndTime;
        _teaStartController.text = settings.teaStartTime;
        _teaEndController.text = settings.teaEndTime;
      }
    });
  }

  @override
  void dispose() {
    _lunchStartController.dispose();
    _lunchEndController.dispose();
    _teaStartController.dispose();
    _teaEndController.dispose();
    super.dispose();
  }

  Future<void> _selectTime(
      BuildContext context, TextEditingController controller) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null) {
      controller.text = picked.format(context);
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) return;

    final settings = BreakTimeSettingsModel(
      lunchStartTime: _lunchStartController.text,
      lunchEndTime: _lunchEndController.text,
      teaStartTime: _teaStartController.text,
      teaEndTime: _teaEndController.text,
    );

    try {
      await ref
          .read(breakTimeSettingsProvider.notifier)
          .updateSettings(settings);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Break time settings updated successfully'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Break Time Settings',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Lunch Break',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () =>
                                  _selectTime(context, _lunchStartController),
                              child: AppTextField(
                                controller: _lunchStartController,
                                label: 'Start Time',
                                enabled: false,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select start time';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: GestureDetector(
                              onTap: () =>
                                  _selectTime(context, _lunchEndController),
                              child: AppTextField(
                                controller: _lunchEndController,
                                label: 'End Time',
                                enabled: false,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select end time';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Tea Break',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () =>
                                  _selectTime(context, _teaStartController),
                              child: AppTextField(
                                controller: _teaStartController,
                                label: 'Start Time',
                                enabled: false,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select start time';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: GestureDetector(
                              onTap: () =>
                                  _selectTime(context, _teaEndController),
                              child: AppTextField(
                                controller: _teaEndController,
                                label: 'End Time',
                                enabled: false,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select end time';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              AppButton(
                onPressed: _saveSettings,
                child: const Text('Save Changes'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
