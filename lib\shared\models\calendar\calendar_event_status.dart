/// Enum for calendar event status
enum CalendarEventStatus {
  /// Scheduled event
  scheduled,

  /// Completed event
  completed,

  /// Cancelled event
  cancelled,

  /// Postponed event
  postponed,
}

/// Extension on [CalendarEventStatus] for additional functionality
extension CalendarEventStatusX on CalendarEventStatus {
  /// Get the display name for the event status
  String get displayName {
    switch (this) {
      case CalendarEventStatus.scheduled:
        return 'Scheduled';
      case CalendarEventStatus.completed:
        return 'Completed';
      case CalendarEventStatus.cancelled:
        return 'Cancelled';
      case CalendarEventStatus.postponed:
        return 'Postponed';
    }
  }

  /// Get the description for the event status
  String get description {
    switch (this) {
      case CalendarEventStatus.scheduled:
        return 'Event is scheduled to occur';
      case CalendarEventStatus.completed:
        return 'Event has been completed';
      case CalendarEventStatus.cancelled:
        return 'Event has been cancelled';
      case CalendarEventStatus.postponed:
        return 'Event has been postponed';
    }
  }

  /// Get the icon for the event status
  String get icon {
    switch (this) {
      case CalendarEventStatus.scheduled:
        return 'schedule';
      case CalendarEventStatus.completed:
        return 'check_circle';
      case CalendarEventStatus.cancelled:
        return 'cancel';
      case CalendarEventStatus.postponed:
        return 'postpone';
    }
  }
}
