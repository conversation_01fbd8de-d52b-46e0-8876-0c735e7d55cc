import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/entities/cart/cart_model.dart';

part 'cart_state.freezed.dart';

/// Cart state
@freezed
sealed class CartState with _$CartState {
  /// Initial state
  const factory CartState.initial() = _Initial;

  /// Loading state
  const factory CartState.loading() = _Loading;

  /// Loaded state
  const factory CartState.loaded(CartModel cart) = _Loaded;

  /// Error state
  const factory CartState.error(String message) = _Error;
}
