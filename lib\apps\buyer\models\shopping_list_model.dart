import 'package:freezed_annotation/freezed_annotation.dart';

part 'shopping_list_model.freezed.dart';
part 'shopping_list_model.g.dart';

@freezed
sealed class ShoppingListModel with _$ShoppingListModel {
  const factory ShoppingListModel({
    required String id,
    required String name,
    String? description,
    @Default(0) int itemCount,
    @Default(0.0) double totalPrice,
    @Default(false) bool isShared,
    DateTime? createdAt,
    DateTime? updatedAt,
    required String createdBy,
    @Default([]) List<String> sharedWith,
    @Default([]) List<ShoppingListItemModel> items,
  }) = _ShoppingListModel;

  factory ShoppingListModel.fromJson(Map<String, dynamic> json) =>
      _$ShoppingListModelFromJson(json);
}

@freezed
sealed class ShoppingListItemModel with _$ShoppingListItemModel {
  const factory ShoppingListItemModel({
    required String id,
    required String name,
    String? description,
    @Default(1) int quantity,
    @Default(0.0) double price,
    @Default(false) bool isChecked,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _ShoppingListItemModel;

  factory ShoppingListItemModel.fromJson(Map<String, dynamic> json) =>
      _$ShoppingListItemModelFromJson(json);
}
