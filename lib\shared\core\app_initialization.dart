import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/api_config_provider.dart';

/// Helper class for initializing app dependencies
class AppInitialization {
  static late SharedPreferences _sharedPreferences;

  /// Initialize all app dependencies
  static Future<ProviderContainer> initialize() async {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize SharedPreferences
    _sharedPreferences = await SharedPreferences.getInstance();

    // Create provider container with overrides
    final container = ProviderContainer(
      overrides: [
        sharedPreferencesProvider.overrideWithValue(_sharedPreferences),
      ],
    );

    return container;
  }

  /// Get the initialized SharedPreferences instance
  static SharedPreferences get sharedPreferences => _sharedPreferences;

  /// Create a ProviderScope with proper initialization
  static Widget createApp({
    required Widget child,
    ProviderContainer? container,
  }) {
    if (container != null) {
      return UncontrolledProviderScope(container: container, child: child);
    }

    return ProviderScope(
      overrides: [
        sharedPreferencesProvider.overrideWithValue(_sharedPreferences),
      ],
      child: child,
    );
  }
}

/// Extension to make initialization easier in main()
extension AppInitializationExtension on Widget {
  /// Wrap the app with proper provider initialization
  Widget withProviders() {
    return FutureBuilder<ProviderContainer>(
      future: AppInitialization.initialize(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return AppInitialization.createApp(
            container: snapshot.data,
            child: this,
          );
        }

        // Show loading screen while initializing
        return MaterialApp(
          home: Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(
                    'Initializing...',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
