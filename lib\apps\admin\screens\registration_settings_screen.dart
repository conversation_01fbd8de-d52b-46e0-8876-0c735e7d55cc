import 'package:flutter/material.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';

class RegistrationSettingsScreen extends StatefulWidget {
  const RegistrationSettingsScreen({super.key});

  @override
  State<RegistrationSettingsScreen> createState() => _RegistrationSettingsScreenState();
}

class _RegistrationSettingsScreenState extends State<RegistrationSettingsScreen> {
  final _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
  final _logger = getLogger('RegistrationSettingsScreen');
  bool _isLoading = true;
  bool _documentsRequired = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      _logger.info('Loading registration settings');
      final configData = await _databaseService.find('system_config', 'registration');

      if (configData != null) {
        setState(() {
          _documentsRequired = configData['documents_required'] ?? false;
        });
        _logger.info('Successfully loaded registration settings');
      } else {
        // Create default settings if they don't exist
        _logger.info('Creating default registration settings');
        final now = DateTime.now().toIso8601String();
        await _databaseService.create('system_config', {
          'id': 'registration',
          'documents_required': false,
          'created_at': now,
          'updated_at': now,
        });
        setState(() {
          _documentsRequired = false;
        });
      }
    } catch (e) {
      _logger.severe('Error loading registration settings: $e');
      setState(() {
        _errorMessage = 'Error loading settings: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      _logger.info('Saving registration settings: documents_required = $_documentsRequired');
      await _databaseService.update('system_config', 'registration', {
        'documents_required': _documentsRequired,
        'updated_at': DateTime.now().toIso8601String(),
      });
      _logger.info('Successfully saved registration settings');

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Settings saved successfully')),
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Error saving settings: $e';
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving settings: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Registration Settings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isLoading ? null : _saveSettings,
            tooltip: 'Save Settings',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadSettings,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Document Requirements',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SwitchListTile(
                        title: const Text('Require Documents for Registration'),
                        subtitle: const Text(
                          'When enabled, users must upload required documents during registration',
                        ),
                        value: _documentsRequired,
                        onChanged: (value) {
                          setState(() {
                            _documentsRequired = value;
                          });
                        },
                      ),
                      const SizedBox(height: 24),
                      const Divider(),
                      const SizedBox(height: 16),
                      const Text(
                        'Document Types by Role',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildDocumentTypesList('Seller', [
                        'Business License',
                        'ID Proof',
                        'Address Proof',
                      ]),
                      const SizedBox(height: 16),
                      _buildDocumentTypesList('Priest', [
                        'ID Proof',
                        'Qualification Certificate',
                        'Experience Certificate',
                      ]),
                      const SizedBox(height: 16),
                      _buildDocumentTypesList('Technician', [
                        'ID Proof',
                        'Skill Certificate',
                        'Work Experience Proof',
                      ]),
                      const SizedBox(height: 24),
                      Center(
                        child: ElevatedButton(
                          onPressed: _saveSettings,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 32,
                              vertical: 12,
                            ),
                          ),
                          child: const Text('Save Settings'),
                        ),
                      ),
                    ],
                  ),
                ),
    );
  }

  Widget _buildDocumentTypesList(String role, List<String> documents) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$role Documents',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...documents.map((doc) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      const Icon(Icons.check, size: 16),
                      const SizedBox(width: 8),
                      Text(doc),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
