/// Enum for calendar event recurrence
enum CalendarEventRecurrence {
  /// No recurrence
  none,

  /// Daily recurrence
  daily,

  /// Weekly recurrence
  weekly,

  /// Monthly recurrence
  monthly,

  /// Yearly recurrence
  yearly,

  /// Custom recurrence
  custom,
}

/// Extension on [CalendarEventRecurrence] for additional functionality
extension CalendarEventRecurrenceX on CalendarEventRecurrence {
  /// Get the display name for the event recurrence
  String get displayName {
    switch (this) {
      case CalendarEventRecurrence.none:
        return 'No Recurrence';
      case CalendarEventRecurrence.daily:
        return 'Daily';
      case CalendarEventRecurrence.weekly:
        return 'Weekly';
      case CalendarEventRecurrence.monthly:
        return 'Monthly';
      case CalendarEventRecurrence.yearly:
        return 'Yearly';
      case CalendarEventRecurrence.custom:
        return 'Custom';
    }
  }

  /// Get the description for the event recurrence
  String get description {
    switch (this) {
      case CalendarEventRecurrence.none:
        return 'Event occurs once';
      case CalendarEventRecurrence.daily:
        return 'Event occurs every day';
      case CalendarEventRecurrence.weekly:
        return 'Event occurs every week';
      case CalendarEventRecurrence.monthly:
        return 'Event occurs every month';
      case CalendarEventRecurrence.yearly:
        return 'Event occurs every year';
      case CalendarEventRecurrence.custom:
        return 'Event occurs based on custom rule';
    }
  }

  /// Get the icon for the event recurrence
  String get icon {
    switch (this) {
      case CalendarEventRecurrence.none:
        return 'event';
      case CalendarEventRecurrence.daily:
        return 'today';
      case CalendarEventRecurrence.weekly:
        return 'date_range';
      case CalendarEventRecurrence.monthly:
        return 'calendar_month';
      case CalendarEventRecurrence.yearly:
        return 'calendar_today';
      case CalendarEventRecurrence.custom:
        return 'settings';
    }
  }
}
