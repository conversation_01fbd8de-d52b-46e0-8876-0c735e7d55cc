import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_state.freezed.dart';
part 'app_state.g.dart';

@freezed
sealed class AppState with _$AppState {
  const factory AppState({
    @Default(false) bool isAuthenticated,
    @Default(false) bool isDarkMode,
    @Default('en') String locale,
    @Default(false) bool isOffline,
    @Default(false) bool isLoading,
    String? error,
    @Default({}) Map<String, dynamic> userData,
  }) = _AppState;

  factory AppState.fromJson(Map<String, dynamic> json) =>
      _$AppStateFromJson(json);
}
