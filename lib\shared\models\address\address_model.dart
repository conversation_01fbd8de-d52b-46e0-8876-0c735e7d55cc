import 'package:freezed_annotation/freezed_annotation.dart';

part 'address_model.freezed.dart';
part 'address_model.g.dart';

@freezed
abstract class AddressModel with _$AddressModel {
  const factory AddressModel({
    required String id,
    required String userId,
    required String name,
    required String street,
    required String city,
    required String state,
    required String postalCode,
    required String country,
    String? phone,
    @Default(false) bool isDefault,
    @Default(false) bool isDeleted,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _AddressModel;

  factory AddressModel.fromJson(Map<String, dynamic> json) =>
      _$AddressModelFromJson(json);

  factory AddressModel.empty() => AddressModel(
        id: '',
        userId: '',
        name: '',
        street: '',
        city: '',
        state: '',
        postalCode: '',
        country: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
}
