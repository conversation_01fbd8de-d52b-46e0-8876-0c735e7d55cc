import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../services/seller_agreement_service.dart';
import '../widgets/dialogs/seller_legal_agreement_dialog.dart';

/// Screen that shows the legal agreement for sellers
class AgreementScreen extends StatefulWidget {
  /// Creates an [AgreementScreen]
  ///
  /// If [onComplete] is provided, it will be called when the agreement is accepted or rejected
  /// instead of navigating to the login screen.
  final Function(bool)? onComplete;

  const AgreementScreen({
    super.key,
    this.onComplete,
  });

  @override
  State<AgreementScreen> createState() => _AgreementScreenState();
}

class _AgreementScreenState extends State<AgreementScreen> {
  bool _isLoading = true;
  String _statusMessage = 'Checking agreement status...';
  bool _isShowingDialog = false;

  @override
  void initState() {
    super.initState();
    debugPrint('AgreementScreen: initState called');
    // Use a post-frame callback to ensure the context is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _checkAgreement();
      }
    });
  }

  Future<void> _checkAgreement() async {
    debugPrint('AgreementScreen: _checkAgreement called');
    if (_isShowingDialog) {
      debugPrint('AgreementScreen: Dialog already showing, skipping');
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _statusMessage = 'Checking agreement status...';
      });

      // First check if agreement is already accepted
      final hasAccepted = await SellerAgreementService.hasAcceptedAgreement();
      debugPrint('AgreementScreen: hasAcceptedAgreement = $hasAccepted');

      if (hasAccepted) {
        debugPrint('AgreementScreen: Agreement already accepted, proceeding');
        if (mounted) {
          _completeAgreement(true);
        }
        return;
      }

      // Show the agreement dialog directly
      _isShowingDialog = true;
      debugPrint('AgreementScreen: Showing agreement dialog');

      bool? accepted;
      if (mounted) {
        accepted = await SellerLegalAgreementDialog.show(context);
      }
      _isShowingDialog = false;

      debugPrint('AgreementScreen: Dialog result: $accepted');

      if (mounted) {
        if (accepted == true) {
          debugPrint('AgreementScreen: Agreement accepted, saving');

          // Save the acceptance
          await SellerAgreementService.saveAgreementAcceptance(true);

          // Verify it was saved
          final verified = await SellerAgreementService.hasAcceptedAgreement();
          debugPrint('AgreementScreen: Verified acceptance: $verified');

          if (verified) {
            debugPrint('AgreementScreen: Agreement verified, proceeding');
            _completeAgreement(true);
          } else {
            setState(() {
              _isLoading = false;
              _statusMessage = 'Failed to save agreement. Please try again.';
            });
          }
        } else {
          // If not accepted, show error
          debugPrint('AgreementScreen: Agreement not accepted');
          setState(() {
            _isLoading = false;
            _statusMessage = 'You must accept the agreement to continue.';
          });
          _completeAgreement(false);
        }
      }
    } catch (e) {
      debugPrint('AgreementScreen: Error in _checkAgreement: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _statusMessage = 'Error: $e';
        });
      }
    }
  }

  void _completeAgreement(bool accepted) {
    debugPrint('AgreementScreen: _completeAgreement called with $accepted');

    // If onComplete callback is provided, use it
    if (widget.onComplete != null) {
      debugPrint('AgreementScreen: Calling onComplete callback');
      widget.onComplete!(accepted);
    } else {
      // Otherwise navigate to login screen
      debugPrint(
          'AgreementScreen: No callback provided, navigating to login screen');
      _navigateToLogin();
    }
  }

  void _navigateToLogin() {
    debugPrint('AgreementScreen: Navigating to login screen');
    // Use a small delay to ensure the UI is ready
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        debugPrint('AgreementScreen: Executing navigation to login');
        context.go('/seller/login');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('AgreementScreen: build method called');
    return Scaffold(
      appBar: AppBar(
        title: const Text('Seller Agreement'),
      ),
      body: Center(
        child: _isLoading
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(_statusMessage),
                ],
              )
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Text(
                      _statusMessage,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: _checkAgreement,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 12,
                      ),
                    ),
                    child: const Text('Try Again'),
                  ),
                ],
              ),
      ),
    );
  }
}
