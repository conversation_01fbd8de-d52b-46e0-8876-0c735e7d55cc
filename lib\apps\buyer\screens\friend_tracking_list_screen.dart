import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'friend_tracking_screen.dart';
import '../providers/friend_tracking_provider.dart';
import '../../../shared/providers/auth_provider.dart';

/// Production-ready screen showing all active friend tracking sessions for the current user
///
/// This screen provides:
/// - List of all tracking invitations received by the user
/// - Active and completed tracking sessions
/// - Quick access to live tracking screens
/// - Pull-to-refresh functionality
/// - Error handling and retry mechanisms
class FriendTrackingListScreen extends ConsumerStatefulWidget {
  const FriendTrackingListScreen({super.key});

  @override
  ConsumerState<FriendTrackingListScreen> createState() =>
      _FriendTrackingListScreenState();
}

class _FriendTrackingListScreenState
    extends ConsumerState<FriendTrackingListScreen> {
  bool _isLoading = true;
  String? _errorMessage;
  List<Map<String, dynamic>> _trackingInvitations = [];

  @override
  void initState() {
    super.initState();
    _loadTrackingInvitations();
  }

  /// Load tracking invitations for the current user with comprehensive error handling
  Future<void> _loadTrackingInvitations() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final friendActions = ref.read(friendTrackingActionsProvider);
      final invitations = await friendActions.getUserTrackingInvitations();

      if (!mounted) return;

      setState(() {
        _trackingInvitations = invitations;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      debugPrint('Error loading tracking invitations: $e');
      debugPrint('Stack trace: $stackTrace');

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load tracking sessions. Please try again.';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Friend Tracking'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTrackingInvitations,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading tracking sessions...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(fontSize: 16, color: Colors.red.shade600),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadTrackingInvitations,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_trackingInvitations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'No Active Tracking Sessions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'You\'ll see tracking sessions here when friends\ninvite you to track their rides',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadTrackingInvitations,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _trackingInvitations.length,
        itemBuilder: (context, index) {
          final invitation = _trackingInvitations[index];
          return _buildTrackingCard(invitation);
        },
      ),
    );
  }

  Widget _buildTrackingCard(Map<String, dynamic> invitation) {
    final isActive = invitation['isActive'] as bool;
    final userName = invitation['userName'] as String;
    final role = invitation['role'] as String;
    final createdAt = invitation['createdAt'] as DateTime;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: isActive ? () => _openTrackingScreen(invitation) : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: isActive
                        ? Colors.green.shade100
                        : Colors.grey.shade200,
                    child: Icon(
                      Icons.person,
                      color: isActive
                          ? Colors.green.shade600
                          : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          userName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getRoleDisplayName(role),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: isActive
                          ? Colors.green.shade100
                          : Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      isActive ? 'ACTIVE' : 'ENDED',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: isActive
                            ? Colors.green.shade700
                            : Colors.grey.shade600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Started ${_formatTime(createdAt)}',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                  const Spacer(),
                  if (isActive) ...[
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: Colors.blue.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Live tracking',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),
              if (isActive) ...[
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _openTrackingScreen(invitation),
                    icon: const Icon(Icons.visibility, size: 18),
                    label: const Text('View Live Tracking'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _openTrackingScreen(Map<String, dynamic> invitation) {
    // Get current user's phone from auth provider
    final authState = ref.read(authProvider);
    final currentUserPhone = authState?.phone ?? '';

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FriendTrackingScreen(
          rideId: invitation['rideId'],
          friendPhone: currentUserPhone,
          userName: invitation['userName'],
        ),
      ),
    );
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'start_point_friend':
        return 'Friend at starting point';
      case 'destination_friend':
        return 'Friend at destination';
      default:
        return 'Friend';
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}
