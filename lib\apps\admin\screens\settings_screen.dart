import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/admin/admin_routes.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        children: [
          _buildSection(
            context,
            'General Settings',
            [
              ListTile(
                leading: const Icon(Icons.app_settings_alt),
                title: const Text('App Configuration'),
                subtitle: const Text('Configure app-wide settings'),
                onTap: () => context.push('/admin/settings/app-config'),
              ),
              ListTile(
                leading: const Icon(Icons.security),
                title: const Text('Security'),
                subtitle: const Text('Manage security settings'),
                onTap: () => context.push('/admin/settings/security'),
              ),
              ListTile(
                leading: const Icon(Icons.notifications),
                title: const Text('Notifications'),
                subtitle: const Text('Configure notification settings'),
                onTap: () => context.push('/admin/settings/notifications'),
              ),
              ListTile(
                leading: const Icon(Icons.analytics),
                title: const Text('Analytics'),
                subtitle: const Text('Manage analytics settings'),
                onTap: () => context.push('/admin/settings/analytics'),
              ),
            ],
          ),
          _buildSection(
            context,
            'Media & Branding',
            [
              ListTile(
                leading: const Icon(Icons.image),
                title: const Text('Icon Management'),
                subtitle: const Text('Manage app icons'),
                onTap: () => context.push(AdminRoutes.iconManagement),
              ),
              ListTile(
                leading: const Icon(Icons.image),
                title: const Text('Banner Management'),
                subtitle: const Text('Manage app banners and promotions'),
                onTap: () => context.push(AdminRoutes.banners),
              ),
              ListTile(
                leading: const Icon(Icons.attach_money),
                title: const Text('Banner Pricing'),
                subtitle: const Text('Configure banner pricing settings'),
                onTap: () => context.push(AdminRoutes.bannerPricing),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
      BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        ...children,
      ],
    );
  }
}
