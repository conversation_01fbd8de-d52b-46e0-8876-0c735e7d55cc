import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/temple/temple_model.dart';
import '../../../../shared/theme/traditional_colors.dart';
import '../../providers/temple_auth_provider.dart';
import '../../providers/temple_services_provider.dart';

class TempleServicesScreen extends ConsumerStatefulWidget {
  const TempleServicesScreen({super.key});

  @override
  ConsumerState<TempleServicesScreen> createState() =>
      _TempleServicesScreenState();
}

class _TempleServicesScreenState extends ConsumerState<TempleServicesScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadServices();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadServices() {
    final temple = ref.read(templeAuthProvider).temple;
    if (temple != null) {
      ref.read(templeServicesProvider.notifier).loadServices(temple.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(templeAuthProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Services'),
        backgroundColor: TraditionalColors.templeOrange,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Darshan'),
            Tab(text: 'Seva'),
          ],
        ),
        actions: [
          IconButton(
            onPressed: () => _showAddServiceDialog(),
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDarshanTab(authState.temple?.darshans ?? []),
          _buildSevaTab(authState.temple?.sevas ?? []),
        ],
      ),
    );
  }

  Widget _buildDarshanTab(List<Darshan> darshans) {
    if (darshans.isEmpty) {
      return _buildEmptyState(
        'No darshan services configured',
        'Add your first darshan service',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: darshans.length,
      itemBuilder: (context, index) {
        final darshan = darshans[index];
        return _buildDarshanCard(darshan);
      },
    );
  }

  Widget _buildSevaTab(List<Seva> sevas) {
    if (sevas.isEmpty) {
      return _buildEmptyState(
        'No seva services configured',
        'Add your first seva service',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sevas.length,
      itemBuilder: (context, index) {
        final seva = sevas[index];
        return _buildSevaCard(seva);
      },
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.temple_hindu, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddServiceDialog(),
            icon: const Icon(Icons.add),
            label: const Text('Add Service'),
            style: ElevatedButton.styleFrom(
              backgroundColor: TraditionalColors.templeOrange,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDarshanCard(Darshan darshan) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: TraditionalColors.templeOrange.withValues(
                      alpha: 0.1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.visibility,
                    color: TraditionalColors.templeOrange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        darshan.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getDarshanTypeLabel(darshan.type),
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 16),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 16, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) {
                    if (value == 'edit') {
                      _editDarshan(darshan);
                    } else if (value == 'delete') {
                      _deleteDarshan(darshan);
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              darshan.description,
              style: TextStyle(fontSize: 14, color: Colors.grey[700]),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildInfoChip(
                  '₹${darshan.price.toStringAsFixed(0)}',
                  Icons.currency_rupee,
                ),
                const SizedBox(width: 8),
                _buildInfoChip('${darshan.duration} min', Icons.access_time),
                const SizedBox(width: 8),
                _buildInfoChip(
                  darshan.isActive ? 'Active' : 'Inactive',
                  darshan.isActive ? Icons.check_circle : Icons.cancel,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSevaCard(Seva seva) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.volunteer_activism,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        seva.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getSevaTypeLabel(seva.type),
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 16),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 16, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) {
                    if (value == 'edit') {
                      _editSeva(seva);
                    } else if (value == 'delete') {
                      _deleteSeva(seva);
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              seva.description,
              style: TextStyle(fontSize: 14, color: Colors.grey[700]),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildInfoChip(
                  '₹${seva.price.toStringAsFixed(0)}',
                  Icons.currency_rupee,
                ),
                const SizedBox(width: 8),
                _buildInfoChip('${seva.duration} min', Icons.access_time),
                const SizedBox(width: 8),
                _buildInfoChip(
                  seva.isActive ? 'Active' : 'Inactive',
                  seva.isActive ? Icons.check_circle : Icons.cancel,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }

  void _showAddServiceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Service'),
        content: const Text('Choose the type of service to add:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _addDarshan();
            },
            child: const Text('Add Darshan'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _addSeva();
            },
            child: const Text('Add Seva'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _addDarshan() {
    context.push('/temples/services/add-darshan');
  }

  void _addSeva() {
    context.push('/temples/services/add-seva');
  }

  void _editDarshan(Darshan darshan) {
    context.push('/temples/services/edit-darshan/${darshan.id}');
  }

  void _editSeva(Seva seva) {
    context.push('/temples/services/edit-seva/${seva.id}');
  }

  void _deleteDarshan(Darshan darshan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Darshan'),
        content: Text('Are you sure you want to delete "${darshan.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performDeleteDarshan(darshan);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _deleteSeva(Seva seva) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Seva'),
        content: Text('Are you sure you want to delete "${seva.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performDeleteSeva(seva);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _getDarshanTypeLabel(DarshanType type) {
    switch (type) {
      case DarshanType.general:
        return 'General Darshan';
      case DarshanType.special:
        return 'Special Darshan';
      case DarshanType.vip:
        return 'VIP Darshan';
      case DarshanType.abhishek:
        return 'Abhishek';
      case DarshanType.aarti:
        return 'Aarti';
      case DarshanType.festival:
        return 'Festival Darshan';
    }
  }

  String _getSevaTypeLabel(SevaType type) {
    switch (type) {
      case SevaType.archana:
        return 'Archana';
      case SevaType.abhishek:
        return 'Abhishek';
      case SevaType.aarti:
        return 'Aarti';
      case SevaType.prasadam:
        return 'Prasadam';
      case SevaType.annadanam:
        return 'Annadanam';
      case SevaType.kalyanam:
        return 'Kalyanam';
      case SevaType.homam:
        return 'Homam';
      case SevaType.specialPuja:
        return 'Special Puja';
    }
  }

  /// Perform darshan deletion with error handling
  Future<void> _performDeleteDarshan(Darshan darshan) async {
    final temple = ref.read(templeAuthProvider).temple;
    if (temple == null) {
      _showErrorSnackBar('Temple information not available');
      return;
    }

    try {
      final success = await ref
          .read(templeServicesProvider.notifier)
          .deleteDarshan(temple.id, darshan.id);

      if (success) {
        _showSuccessSnackBar('Darshan "${darshan.name}" deleted successfully');
      } else {
        _showErrorSnackBar('Failed to delete darshan');
      }
    } catch (e) {
      _showErrorSnackBar('Error deleting darshan: $e');
    }
  }

  /// Perform seva deletion with error handling
  Future<void> _performDeleteSeva(Seva seva) async {
    final temple = ref.read(templeAuthProvider).temple;
    if (temple == null) {
      _showErrorSnackBar('Temple information not available');
      return;
    }

    try {
      final success = await ref
          .read(templeServicesProvider.notifier)
          .deleteSeva(temple.id, seva.id);

      if (success) {
        _showSuccessSnackBar('Seva "${seva.name}" deleted successfully');
      } else {
        _showErrorSnackBar('Failed to delete seva');
      }
    } catch (e) {
      _showErrorSnackBar('Error deleting seva: $e');
    }
  }

  /// Show success snackbar
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
