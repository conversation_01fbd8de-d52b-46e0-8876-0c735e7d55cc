import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/seller_routes.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/models/seller.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';

import '../application/seller_provider.dart';

// Create a provider for AuthService
final authServiceProvider = Provider<AuthService>((ref) => AuthService());

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load seller data in initState
    _loadSellerData();
  }

  Future<void> _loadSellerData() async {
    // Check if seller data is already loaded
    final state = ref.read(sellerProvider);
    if (state.value == null && !state.isLoading) {
      debugPrint('SellerHomeScreen: Seller is null, loading data');
      final authService = ref.read(authServiceProvider);
      final userId = authService.currentUser?.id;
      if (userId != null) {
        // Use Future.microtask to avoid calling setState during build
        Future.microtask(() {
          ref.read(sellerProvider.notifier).getSeller(userId);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('SellerHomeScreen: Building home screen');
    final state = ref.watch(sellerProvider);

    if (state.isLoading) {
      debugPrint('SellerHomeScreen: State is loading');
      return const Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight),
          child: SizedBox(), // Empty app bar to maintain consistent layout
        ),
        body: const Center(child: LoadingIndicator()),
      );
    }

    final seller = state.value;
    if (seller == null) {
      debugPrint('SellerHomeScreen: Seller is null');
      return const Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight),
          child: SizedBox(), // Empty app bar to maintain consistent layout
        ),
        body: const Center(child: LoadingIndicator()),
      );
    }

    debugPrint('SellerHomeScreen: Seller data loaded successfully');
    return Scaffold(
      appBar: AppBar(title: Text(seller.businessName)),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeSection(seller),
            const SizedBox(height: 24),
            _buildStatsGrid(seller),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Both product sellers and event service providers use the same add product screen
          // The screen adapts its UI based on the seller's category
          SellerRoutes.navigateToAddProduct(context);
        },
        child: Icon(seller.category.isEventService ? Icons.event : Icons.add),
      ),
    );
  }

  Widget _buildWelcomeSection(Seller seller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: seller.businessLogo != null
                      ? NetworkImage(seller.businessLogo!)
                      : null,
                  child: seller.businessLogo == null
                      ? const Icon(Icons.business, size: 30)
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        seller.businessName,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        seller.businessDescription ?? 'No description',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: seller.category.isEventService
                              ? Colors.purple.withAlpha(25)
                              : Colors.blue.withAlpha(25),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          seller.category.displayName,
                          style: TextStyle(
                            fontSize: 12,
                            color: seller.category.isEventService
                                ? Colors.purple[700]
                                : Colors.blue[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatusChip(
                  'Status',
                  seller.isApproved ? 'Approved' : 'Pending',
                  seller.isApproved ? Colors.green : Colors.orange,
                ),
                _buildStatusChip(
                  'Rating',
                  '${seller.rating.toStringAsFixed(1)} (${seller.totalReviews})',
                  Colors.blue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: color.withAlpha(25), // Using approximately 0.1 opacity (25/255)
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        children: [
          Text(label, style: const TextStyle(fontSize: 14, color: Colors.grey)),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid(Seller seller) {
    final isEventService = seller.category.isEventService;

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildStatCard(
          isEventService ? 'Total Bookings' : 'Total Orders',
          seller.totalOrders.toString(),
          isEventService ? Icons.event_available : Icons.shopping_cart,
          Colors.blue,
        ),
        _buildStatCard(
          isEventService ? 'Service Packages' : 'Total Products',
          seller.totalProducts.toString(),
          isEventService ? Icons.business_center : Icons.inventory,
          Colors.green,
        ),
        _buildStatCard(
          'Revenue',
          CurrencyFormatter.format(seller.totalRevenue),
          Icons.attach_money,
          Colors.orange,
        ),
        _buildStatCard(
          'Rating',
          '${seller.rating.toStringAsFixed(1)}/5.0',
          Icons.star,
          Colors.amber,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
