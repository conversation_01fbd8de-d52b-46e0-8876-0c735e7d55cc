import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/calendar/calendar_event_model.dart';
import '../../../../shared/models/calendar/calendar_event_type.dart';
import '../../../../shared/models/calendar/calendar_event_visibility.dart';
import '../../../../shared/services/calendar/calendar_event_service.dart';
import '../../../../shared/providers/location_provider.dart';

/// Provider for the calendar event service
final adminProductListServiceProvider = Provider<CalendarEventService>((ref) {
  return CalendarEventService();
});

/// Provider to check if there are admin product lists for a specific date
final adminProductListsForDateProvider = FutureProvider.family<bool, DateTime>((
  ref,
  date,
) async {
  final service = ref.watch(adminProductListServiceProvider);
  try {
    // Create a date range for the entire day
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

    // Get events for the date range
    final events = await service.getEventsByDateRange(startOfDay, endOfDay);

    // Filter for admin product list events
    final adminProductLists = events
        .where(
          (event) =>
              event.type == CalendarEventType.productList &&
              event.visibility != CalendarEventVisibility.private,
        )
        .toList();

    return adminProductLists.isNotEmpty;
  } catch (e) {
    // Return false on error
    return false;
  }
});

/// Provider to get admin product lists for a specific date
final adminProductListsProvider =
    FutureProvider.family<List<CalendarEventModel>, DateTime>((
      ref,
      date,
    ) async {
      final service = ref.watch(adminProductListServiceProvider);
      try {
        // Create a date range for the entire day
        final startOfDay = DateTime(date.year, date.month, date.day);
        final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

        // Get events for the date range
        final events = await service.getEventsByDateRange(startOfDay, endOfDay);

        // Get user location
        final userLocation = ref.watch(userLocationProvider);

        // Filter for admin product list events
        return events.where((event) {
          // First filter by event type and visibility
          if (event.type != CalendarEventType.productList ||
              event.visibility == CalendarEventVisibility.private) {
            return false;
          }

          // If user location is not available, show all product lists
          if (userLocation == null) {
            return true;
          }

          // For now, show all product lists regardless of location
          // In a real implementation, we would check if the event's location matches the user's location
          // This would require storing location information in the event's description or in a separate field
          return true;
        }).toList();
      } catch (e) {
        // Return empty list on error
        return [];
      }
    });
