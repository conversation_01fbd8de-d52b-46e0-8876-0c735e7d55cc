import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:latlong2/latlong.dart' as latlong2;
import 'package:intl/intl.dart';
import '../../../../../shared/models/order/order_model.dart';
import '../../../../../shared/providers/order_provider.dart';
import '../../../../../shared/providers/delivery_tracking_provider.dart';
import '../../../../../shared/ui_components/dialogs/contact_support_dialog.dart';
import '../../../../../shared/utils/logger.dart';
import '../../../../../shared/widgets/maps/flutter_map_view.dart';
import '../../../../../shared/ui_components/navigation/back_button_handler.dart';

class OrderTrackingScreen extends ConsumerStatefulWidget {
  final String orderId;

  const OrderTrackingScreen({
    super.key,
    required this.orderId,
  });

  @override
  ConsumerState<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

class _OrderTrackingScreenState extends ConsumerState<OrderTrackingScreen> {
  final logger = getLogger('OrderTrackingScreen');
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();

    // Set up refresh timer to update tracking info every 30 seconds
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      if (mounted) {
        // Refresh the providers
        ref.invalidate(orderStreamProvider(widget.orderId));
        ref.invalidate(orderTrackingProvider(widget.orderId));
      }
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }



  // This method is no longer needed as we're using FlutterMapView

  @override
  Widget build(BuildContext context) {
    final orderAsync = ref.watch(orderStreamProvider(widget.orderId));
    final trackingInfoAsync = ref.watch(orderTrackingProvider(widget.orderId));
    final theme = Theme.of(context);

    return BackButtonHandler(
      fallbackRoute: '/buyer/orders',
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Order Tracking'),
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.pop(),
          ),
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.dark,
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                // Refresh the providers
                ref.invalidate(orderStreamProvider(widget.orderId));
                ref.invalidate(orderTrackingProvider(widget.orderId));
              },
              tooltip: 'Refresh tracking',
            ),
            IconButton(
              icon: const Icon(Icons.support_agent),
              onPressed: () => _showContactSupport(context),
            ),
          ],
        ),
        body: orderAsync.when(
          data: (order) {
            return trackingInfoAsync.when(
              data: (trackingInfo) {
                return _buildOrderTracking(context, order, trackingInfo);
              },
              loading: () => _buildOrderTracking(context, order, null),
              error: (error, stackTrace) {
                logger.severe('Error loading tracking info: $error\n$stackTrace');
                return _buildOrderTracking(context, order, null);
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) {
            logger.severe(
                'Error loading order tracking\nError: $error\nStack trace: $stackTrace');
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading order tracking\n$error',
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _showContactSupport(context),
                    child: const Text('Contact Support'),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildOrderTracking(BuildContext context, OrderModel order, Map<String, dynamic>? trackingInfo) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOrderDetails(order),
          _buildMapWithTracking(order, trackingInfo),
          _buildTrackingInfo(context, order, trackingInfo),
          _buildTimeline(order),
        ],
      ),
    );
  }

  Widget _buildTrackingInfo(BuildContext context, OrderModel order, Map<String, dynamic>? trackingInfo) {
    if (trackingInfo == null) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);

    // Extract estimated delivery time if available
    DateTime? estimatedDeliveryTime;
    if (trackingInfo['estimatedDeliveryTime'] != null) {
      try {
        estimatedDeliveryTime = (trackingInfo['estimatedDeliveryTime'] as dynamic).toDate();
      } catch (e) {
        logger.warning('Error parsing estimated delivery time: $e');
      }
    }

    // Extract distance if available
    double? distanceToDelivery;
    if (trackingInfo['distanceToDelivery'] != null) {
      try {
        distanceToDelivery = trackingInfo['distanceToDelivery'] as double;
      } catch (e) {
        logger.warning('Error parsing distance to delivery: $e');
      }
    }

    if (estimatedDeliveryTime == null && distanceToDelivery == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Delivery Tracking',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Estimated delivery time
              if (estimatedDeliveryTime != null) ...[
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withAlpha(30),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.access_time,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Estimated Delivery'),
                          const SizedBox(height: 4),
                          Text(
                            _formatEstimatedTime(estimatedDeliveryTime),
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            DateFormat('EEEE, MMMM d • h:mm a').format(estimatedDeliveryTime),
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],

              // Distance to delivery
              if (distanceToDelivery != null) ...[
                if (estimatedDeliveryTime != null) const SizedBox(height: 16),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.secondary.withAlpha(30),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.directions,
                        color: theme.colorScheme.secondary,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Distance'),
                          const SizedBox(height: 4),
                          Text(
                            '${distanceToDelivery.toStringAsFixed(1)} km away',
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Approximately ${(distanceToDelivery / 30 * 60).round()} min drive',
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 16),

              // Delivery status message
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withAlpha(20),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _getStatusMessage(order.status),
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatEstimatedTime(DateTime estimatedTime) {
    final now = DateTime.now();
    final difference = estimatedTime.difference(now);

    if (difference.isNegative) {
      return 'Arriving soon';
    } else if (difference.inMinutes < 60) {
      return 'In ${difference.inMinutes} minutes';
    } else {
      return 'In ${(difference.inMinutes / 60).floor()} hr ${difference.inMinutes % 60} min';
    }
  }

  String _getStatusMessage(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Your order has been received and is being processed.';
      case OrderStatus.confirmed:
        return 'Your order has been confirmed and is being prepared.';
      case OrderStatus.processing:
        return 'Your order is being processed.';
      case OrderStatus.readyForPickup:
        return 'Your order is ready and waiting for pickup.';
      case OrderStatus.outForDelivery:
        return 'Your order is on the way! Our delivery partner is heading to your location.';
      case OrderStatus.delivered:
        return 'Your order has been delivered. Enjoy!';
      case OrderStatus.cancelled:
        return 'Your order has been cancelled.';
      case OrderStatus.refunded:
        return 'Your order has been refunded.';
      default:
        return 'Your order is being processed.';
    }
  }

  Widget _buildOrderDetails(OrderModel order) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order #${order.id}',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${order.items.length} items • Total: ₹${order.total.toStringAsFixed(2)}',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          const Text(
            'Delivery Address:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${order.deliveryAddress.street}\n'
            '${order.deliveryAddress.city}, ${order.deliveryAddress.state} ${order.deliveryAddress.postalCode}\n'
            '${order.deliveryAddress.country}',
            style: const TextStyle(fontSize: 14),
          ),

          // Show delivery information if available
          if (order.shippingDetails != null) ...[
            const SizedBox(height: 16),
            const Text(
              'Delivery Information:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Provider: ${order.shippingDetails!.provider}',
              style: const TextStyle(fontSize: 14),
            ),
            if (order.shippingDetails!.trackingNumber.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                'Tracking Number: ${order.shippingDetails!.trackingNumber}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
            if (order.shippingDetails!.estimatedDeliveryDate != null) ...[
              const SizedBox(height: 4),
              Text(
                'Estimated Delivery: ${_formatDate(order.shippingDetails!.estimatedDeliveryDate!)}',
                style: const TextStyle(fontSize: 14),
              ),
            ],

          ],
        ],
      ),
    );
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildMapWithTracking(OrderModel order, Map<String, dynamic>? trackingInfo) {
    if (order.deliveryAddress.latitude == null ||
        order.deliveryAddress.longitude == null) {
      return const SizedBox.shrink();
    }

    final lat = order.deliveryAddress.latitude!;
    final lng = order.deliveryAddress.longitude!;

    // Convert to flutter_map LatLng
    final deliveryLocation = latlong2.LatLng(lat, lng);

    // Create store location if available
    latlong2.LatLng? storeLocation;
    if (trackingInfo != null &&
        trackingInfo['storeLocation'] != null &&
        trackingInfo['storeLocation'] is Map<String, dynamic>) {
      try {
        final storeLoc = trackingInfo['storeLocation'] as Map<String, dynamic>;
        if (storeLoc.containsKey('latitude') && storeLoc.containsKey('longitude')) {
          storeLocation = latlong2.LatLng(
            storeLoc['latitude'] as double,
            storeLoc['longitude'] as double,
          );
        }
      } catch (e) {
        logger.warning('Error processing store location: $e');
      }
    }

    // Create delivery person location if available
    latlong2.LatLng? deliveryPersonLocation;
    if (trackingInfo != null && trackingInfo['currentLocation'] != null) {
      try {
        final location = trackingInfo['currentLocation'] as Map<String, dynamic>;
        deliveryPersonLocation = latlong2.LatLng(
          location['latitude'] as double,
          location['longitude'] as double,
        );
      } catch (e) {
        logger.warning('Error processing delivery person location: $e');
      }
    }

    return SizedBox(
      height: 250,
      child: Stack(
        children: [
          FlutterMapView(
            // If store location is available, use it as pickup location
            pickupLocation: storeLocation,
            // Delivery address is the drop location
            dropLocation: deliveryLocation,
            // Current location of delivery person
            currentLocation: deliveryPersonLocation,
            showRoute: true,
            fitToMarkers: true,
            initialZoom: 15,
            showCurrentLocation: false,
          ),

          // Custom map controls
          Positioned(
            right: 16,
            bottom: 16,
            child: Column(
              children: [
                FloatingActionButton.small(
                  heroTag: 'zoom_in',
                  onPressed: () {
                    // Zoom controls are handled by FlutterMapView
                  },
                  child: const Icon(Icons.add),
                ),
                const SizedBox(height: 8),
                FloatingActionButton.small(
                  heroTag: 'zoom_out',
                  onPressed: () {
                    // Zoom controls are handled by FlutterMapView
                  },
                  child: const Icon(Icons.remove),
                ),
                const SizedBox(height: 8),
                FloatingActionButton.small(
                  heroTag: 'my_location',
                  onPressed: () {
                    // Center on delivery location
                  },
                  child: const Icon(Icons.my_location),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildTimeline(OrderModel order) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Order Timeline',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildTimelineItem(
            'Order Placed',
            order.createdAt,
            order.status == OrderStatus.pending,
          ),
          _buildTimelineItem(
            'Order Confirmed',
            order.updatedAt,
            order.status == OrderStatus.confirmed,
          ),
          _buildTimelineItem(
            'Processing',
            order.updatedAt,
            order.status == OrderStatus.processing,
          ),
          if (order.status == OrderStatus.readyForPickup)
            _buildTimelineItem(
              'Ready for Pickup',
              order.updatedAt,
              true,
            ),
          if (order.status == OrderStatus.outForDelivery)
            _buildTimelineItem(
              'Out for Delivery',
              order.updatedAt,
              true,
            ),
          if (order.status == OrderStatus.delivered)
            _buildTimelineItem(
              'Delivered',
              order.updatedAt,
              true,
            ),
          if (order.status == OrderStatus.cancelled)
            _buildTimelineItem(
              'Cancelled',
              order.updatedAt,
              true,
            ),
          if (order.status == OrderStatus.refunded)
            _buildTimelineItem(
              'Refunded',
              order.updatedAt,
              true,
            ),
        ],
      ),
    );
  }

  Widget _buildTimelineItem(String title, DateTime date, bool isCompleted) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isCompleted ? Colors.green : Colors.grey,
            ),
            child: isCompleted
                ? const Icon(Icons.check, color: Colors.white, size: 16)
                : null,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight:
                        isCompleted ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                Text(
                  '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showContactSupport(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const ContactSupportDialog(),
    );
  }
}
