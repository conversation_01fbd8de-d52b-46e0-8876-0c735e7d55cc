import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/auth/auth_bloc.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';

class ExecutorRequestsScreen extends StatefulWidget {
  const ExecutorRequestsScreen({super.key});

  @override
  State<ExecutorRequestsScreen> createState() => _ExecutorRequestsScreenState();
}

class _ExecutorRequestsScreenState extends State<ExecutorRequestsScreen> {
  final _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
  final _logger = getLogger('ExecutorRequestsScreen');
  bool _isLoading = false;
  List<Map<String, dynamic>> _executorRequests = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadExecutorRequests();
  }

  Future<void> _loadExecutorRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      _logger.info('Loading pending executor requests');
      final requests = await _databaseService.getAll(
        'executor_requests',
        where: 'status = ?',
        whereParams: ['pending'],
        orderBy: 'created_at DESC',
      );

      setState(() {
        _executorRequests = requests;
        _isLoading = false;
      });

      _logger.info('Successfully loaded ${requests.length} pending executor requests');
    } catch (e) {
      _logger.severe('Error loading executor requests: $e');
      setState(() {
        _errorMessage = 'Error loading executor requests: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _approveExecutor(String requestId) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      _logger.info('Approving executor request: $requestId');

      // Get request document
      final requestData = await _databaseService.find('executor_requests', requestId);
      if (requestData == null) {
        throw Exception('Executor request not found');
      }

      final userId = requestData['uid'] as String;
      final now = DateTime.now().toIso8601String();

      // Update user document to grant executor access
      await _databaseService.update('users', userId, {
        'is_approved': true,
        'updated_at': now,
      });

      // Update request status
      await _databaseService.update('executor_requests', requestId, {
        'status': 'approved',
        'approved_at': now,
        'approved_by': context.read<AuthBloc>().state is AuthAuthenticatedState
            ? (context.read<AuthBloc>().state as AuthAuthenticatedState).user.id
            : 'admin',
        'updated_at': now,
      });

      _logger.info('Successfully approved executor request: $requestId');

      // Reload the list
      await _loadExecutorRequests();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Executor approved successfully')),
      );
    } catch (e) {
      _logger.severe('Error approving executor: $e');
      setState(() {
        _errorMessage = 'Error approving executor: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _rejectExecutor(String requestId) async {
    final reasonController = TextEditingController();
    
    final reason = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Executor'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejection:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(reasonController.text),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (reason == null || reason.isEmpty) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      _logger.info('Rejecting executor request: $requestId with reason: $reason');

      // Update request status
      final now = DateTime.now().toIso8601String();
      await _databaseService.update('executor_requests', requestId, {
        'status': 'rejected',
        'rejection_reason': reason,
        'rejected_at': now,
        'rejected_by': context.read<AuthBloc>().state is AuthAuthenticatedState
            ? (context.read<AuthBloc>().state as AuthAuthenticatedState).user.id
            : 'admin',
        'updated_at': now,
      });

      _logger.info('Successfully rejected executor request: $requestId');

      // Reload the list
      await _loadExecutorRequests();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Executor rejected successfully')),
      );
    } catch (e) {
      _logger.severe('Error rejecting executor: $e');
      setState(() {
        _errorMessage = 'Error rejecting executor: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Executor Requests'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadExecutorRequests,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadExecutorRequests,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _executorRequests.isEmpty
                  ? const Center(
                      child: Text('No pending executor requests'),
                    )
                  : ListView.builder(
                      itemCount: _executorRequests.length,
                      itemBuilder: (context, index) {
                        final request = _executorRequests[index];
                        return Card(
                          margin: const EdgeInsets.all(8),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  request['displayName'] ?? 'Unknown',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text('Email: ${request['email'] ?? 'N/A'}'),
                                if (request['phoneNumber'] != null)
                                  Text('Phone: ${request['phoneNumber']}'),
                                const SizedBox(height: 8),
                                Text(
                                  'Requested: ${_formatTimestamp(request['requestedAt'])}',
                                  style: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton(
                                      onPressed: () => _rejectExecutor(request['id']),
                                      child: const Text('Reject'),
                                    ),
                                    const SizedBox(width: 8),
                                    ElevatedButton(
                                      onPressed: () => _approveExecutor(request['id']),
                                      child: const Text('Approve'),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
    );
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'N/A';
    
    if (timestamp is String) {
      try {
        final date = DateTime.parse(timestamp);
        return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
      } catch (e) {
        return 'Invalid date';
      }
    }

    if (timestamp is Map<String, dynamic>) {
      // Handle potential timestamp objects from database
      if (timestamp.containsKey('_seconds')) {
        final date = DateTime.fromMillisecondsSinceEpoch(
          (timestamp['_seconds'] as int) * 1000,
        );
        return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
      }
    }
    
    return 'N/A';
  }
}
