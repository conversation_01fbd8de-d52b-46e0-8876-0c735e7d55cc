import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';
import 'package:crypto/crypto.dart';
import 'dart:math';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final recoveryCodesProvider =
    AsyncNotifierProvider<RecoveryCodesNotifier, List<String>>(() {
  return RecoveryCodesNotifier();
});

class RecoveryCodesNotifier extends AsyncNotifier<List<String>> {
  late final DatabaseService _databaseService;

  @override
  Future<List<String>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadRecoveryCodes();
  }

  Future<List<String>> _loadRecoveryCodes() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('users', userId);
      return List<String>.from(userData?['recovery_codes'] ?? []);
    } catch (e) {
      debugPrint('Failed to load recovery codes: $e');
      throw Exception('Failed to load recovery codes: $e');
    }
  }

  Future<void> generateRecoveryCodes() async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Generate 10 random 8-character codes using secure random
      final random = Random.secure();
      final codes = List.generate(10, (index) {
        final codeBytes = List.generate(4, (_) => random.nextInt(256));
        final hash = sha256.convert(codeBytes);
        return hash.toString().substring(0, 8).toUpperCase();
      });

      // Store codes in database
      await _databaseService.update('users', userId, {
        'recovery_codes': codes,
        'recovery_codes_generated_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData(codes);
    } catch (e) {
      debugPrint('Failed to generate recovery codes: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  Future<bool> useRecoveryCode(String code) async {
    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      final userData = await _databaseService.find('users', userId);
      final codes = List<String>.from(userData?['recovery_codes'] ?? []);

      if (!codes.contains(code.toUpperCase())) {
        return false;
      }

      // Remove the used code
      codes.remove(code.toUpperCase());

      await _databaseService.update('users', userId, {
        'recovery_codes': codes,
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Update state
      state = AsyncData(codes);
      return true;
    } catch (e) {
      debugPrint('Failed to use recovery code: $e');
      return false;
    }
  }

  Future<void> clearRecoveryCodes() async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      await _databaseService.update('users', userId, {
        'recovery_codes': [],
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = const AsyncData([]);
    } catch (e) {
      debugPrint('Failed to clear recovery codes: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}
