import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/services/device/device_usage_service.dart';

/// A widget that tracks device usage and updates the last usage time
/// This should be placed at the root of the app to track all user interactions
class DeviceUsageTracker extends ConsumerStatefulWidget {
  final Widget child;

  const DeviceUsageTracker({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<DeviceUsageTracker> createState() => _DeviceUsageTrackerState();
}

class _DeviceUsageTrackerState extends ConsumerState<DeviceUsageTracker> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    // Record usage when app starts
    _recordUsage();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Record usage when app is resumed from background
      _recordUsage();
    } else if (state == AppLifecycleState.paused) {
      // Record usage when app goes to background
      _recordUsage();
    }
  }

  void _recordUsage() {
    final deviceUsageService = ref.read(deviceUsageServiceProvider);
    deviceUsageService.recordDeviceUsage();
  }

  @override
  Widget build(BuildContext context) {
    // Use a GestureDetector to track user interactions
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: _recordUsage,
      onPanDown: (_) => _recordUsage(),
      onScaleStart: (_) => _recordUsage(),
      child: widget.child,
    );
  }
}
