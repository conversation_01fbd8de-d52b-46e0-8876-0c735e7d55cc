// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'appointment_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppointmentModel {

 String get id; String get appointmentNumber; String get patientId; String get patientName; String get patientPhone; String? get patientEmail; String get hospitalId; String get hospitalName; String get doctorId; String get doctorName; String get departmentId; String get departmentName; DateTime get appointmentDate; String get timeSlotId; String get startTime; String get endTime; AppointmentType get type; AppointmentStatus get status; String get symptoms; String? get diagnosis; String? get prescription; String? get notes; double get consultationFee; bool get isPaid; String? get paymentId; DateTime get createdAt; DateTime get updatedAt; bool get isDeleted; DateTime? get deletedAt;
/// Create a copy of AppointmentModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppointmentModelCopyWith<AppointmentModel> get copyWith => _$AppointmentModelCopyWithImpl<AppointmentModel>(this as AppointmentModel, _$identity);

  /// Serializes this AppointmentModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppointmentModel&&(identical(other.id, id) || other.id == id)&&(identical(other.appointmentNumber, appointmentNumber) || other.appointmentNumber == appointmentNumber)&&(identical(other.patientId, patientId) || other.patientId == patientId)&&(identical(other.patientName, patientName) || other.patientName == patientName)&&(identical(other.patientPhone, patientPhone) || other.patientPhone == patientPhone)&&(identical(other.patientEmail, patientEmail) || other.patientEmail == patientEmail)&&(identical(other.hospitalId, hospitalId) || other.hospitalId == hospitalId)&&(identical(other.hospitalName, hospitalName) || other.hospitalName == hospitalName)&&(identical(other.doctorId, doctorId) || other.doctorId == doctorId)&&(identical(other.doctorName, doctorName) || other.doctorName == doctorName)&&(identical(other.departmentId, departmentId) || other.departmentId == departmentId)&&(identical(other.departmentName, departmentName) || other.departmentName == departmentName)&&(identical(other.appointmentDate, appointmentDate) || other.appointmentDate == appointmentDate)&&(identical(other.timeSlotId, timeSlotId) || other.timeSlotId == timeSlotId)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.symptoms, symptoms) || other.symptoms == symptoms)&&(identical(other.diagnosis, diagnosis) || other.diagnosis == diagnosis)&&(identical(other.prescription, prescription) || other.prescription == prescription)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.consultationFee, consultationFee) || other.consultationFee == consultationFee)&&(identical(other.isPaid, isPaid) || other.isPaid == isPaid)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,appointmentNumber,patientId,patientName,patientPhone,patientEmail,hospitalId,hospitalName,doctorId,doctorName,departmentId,departmentName,appointmentDate,timeSlotId,startTime,endTime,type,status,symptoms,diagnosis,prescription,notes,consultationFee,isPaid,paymentId,createdAt,updatedAt,isDeleted,deletedAt]);

@override
String toString() {
  return 'AppointmentModel(id: $id, appointmentNumber: $appointmentNumber, patientId: $patientId, patientName: $patientName, patientPhone: $patientPhone, patientEmail: $patientEmail, hospitalId: $hospitalId, hospitalName: $hospitalName, doctorId: $doctorId, doctorName: $doctorName, departmentId: $departmentId, departmentName: $departmentName, appointmentDate: $appointmentDate, timeSlotId: $timeSlotId, startTime: $startTime, endTime: $endTime, type: $type, status: $status, symptoms: $symptoms, diagnosis: $diagnosis, prescription: $prescription, notes: $notes, consultationFee: $consultationFee, isPaid: $isPaid, paymentId: $paymentId, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, deletedAt: $deletedAt)';
}


}

/// @nodoc
abstract mixin class $AppointmentModelCopyWith<$Res>  {
  factory $AppointmentModelCopyWith(AppointmentModel value, $Res Function(AppointmentModel) _then) = _$AppointmentModelCopyWithImpl;
@useResult
$Res call({
 String id, String appointmentNumber, String patientId, String patientName, String patientPhone, String? patientEmail, String hospitalId, String hospitalName, String doctorId, String doctorName, String departmentId, String departmentName, DateTime appointmentDate, String timeSlotId, String startTime, String endTime, AppointmentType type, AppointmentStatus status, String symptoms, String? diagnosis, String? prescription, String? notes, double consultationFee, bool isPaid, String? paymentId, DateTime createdAt, DateTime updatedAt, bool isDeleted, DateTime? deletedAt
});




}
/// @nodoc
class _$AppointmentModelCopyWithImpl<$Res>
    implements $AppointmentModelCopyWith<$Res> {
  _$AppointmentModelCopyWithImpl(this._self, this._then);

  final AppointmentModel _self;
  final $Res Function(AppointmentModel) _then;

/// Create a copy of AppointmentModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? appointmentNumber = null,Object? patientId = null,Object? patientName = null,Object? patientPhone = null,Object? patientEmail = freezed,Object? hospitalId = null,Object? hospitalName = null,Object? doctorId = null,Object? doctorName = null,Object? departmentId = null,Object? departmentName = null,Object? appointmentDate = null,Object? timeSlotId = null,Object? startTime = null,Object? endTime = null,Object? type = null,Object? status = null,Object? symptoms = null,Object? diagnosis = freezed,Object? prescription = freezed,Object? notes = freezed,Object? consultationFee = null,Object? isPaid = null,Object? paymentId = freezed,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,Object? deletedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,appointmentNumber: null == appointmentNumber ? _self.appointmentNumber : appointmentNumber // ignore: cast_nullable_to_non_nullable
as String,patientId: null == patientId ? _self.patientId : patientId // ignore: cast_nullable_to_non_nullable
as String,patientName: null == patientName ? _self.patientName : patientName // ignore: cast_nullable_to_non_nullable
as String,patientPhone: null == patientPhone ? _self.patientPhone : patientPhone // ignore: cast_nullable_to_non_nullable
as String,patientEmail: freezed == patientEmail ? _self.patientEmail : patientEmail // ignore: cast_nullable_to_non_nullable
as String?,hospitalId: null == hospitalId ? _self.hospitalId : hospitalId // ignore: cast_nullable_to_non_nullable
as String,hospitalName: null == hospitalName ? _self.hospitalName : hospitalName // ignore: cast_nullable_to_non_nullable
as String,doctorId: null == doctorId ? _self.doctorId : doctorId // ignore: cast_nullable_to_non_nullable
as String,doctorName: null == doctorName ? _self.doctorName : doctorName // ignore: cast_nullable_to_non_nullable
as String,departmentId: null == departmentId ? _self.departmentId : departmentId // ignore: cast_nullable_to_non_nullable
as String,departmentName: null == departmentName ? _self.departmentName : departmentName // ignore: cast_nullable_to_non_nullable
as String,appointmentDate: null == appointmentDate ? _self.appointmentDate : appointmentDate // ignore: cast_nullable_to_non_nullable
as DateTime,timeSlotId: null == timeSlotId ? _self.timeSlotId : timeSlotId // ignore: cast_nullable_to_non_nullable
as String,startTime: null == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as String,endTime: null == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as AppointmentType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as AppointmentStatus,symptoms: null == symptoms ? _self.symptoms : symptoms // ignore: cast_nullable_to_non_nullable
as String,diagnosis: freezed == diagnosis ? _self.diagnosis : diagnosis // ignore: cast_nullable_to_non_nullable
as String?,prescription: freezed == prescription ? _self.prescription : prescription // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,consultationFee: null == consultationFee ? _self.consultationFee : consultationFee // ignore: cast_nullable_to_non_nullable
as double,isPaid: null == isPaid ? _self.isPaid : isPaid // ignore: cast_nullable_to_non_nullable
as bool,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [AppointmentModel].
extension AppointmentModelPatterns on AppointmentModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AppointmentModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AppointmentModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AppointmentModel value)  $default,){
final _that = this;
switch (_that) {
case _AppointmentModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AppointmentModel value)?  $default,){
final _that = this;
switch (_that) {
case _AppointmentModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String appointmentNumber,  String patientId,  String patientName,  String patientPhone,  String? patientEmail,  String hospitalId,  String hospitalName,  String doctorId,  String doctorName,  String departmentId,  String departmentName,  DateTime appointmentDate,  String timeSlotId,  String startTime,  String endTime,  AppointmentType type,  AppointmentStatus status,  String symptoms,  String? diagnosis,  String? prescription,  String? notes,  double consultationFee,  bool isPaid,  String? paymentId,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  DateTime? deletedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AppointmentModel() when $default != null:
return $default(_that.id,_that.appointmentNumber,_that.patientId,_that.patientName,_that.patientPhone,_that.patientEmail,_that.hospitalId,_that.hospitalName,_that.doctorId,_that.doctorName,_that.departmentId,_that.departmentName,_that.appointmentDate,_that.timeSlotId,_that.startTime,_that.endTime,_that.type,_that.status,_that.symptoms,_that.diagnosis,_that.prescription,_that.notes,_that.consultationFee,_that.isPaid,_that.paymentId,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.deletedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String appointmentNumber,  String patientId,  String patientName,  String patientPhone,  String? patientEmail,  String hospitalId,  String hospitalName,  String doctorId,  String doctorName,  String departmentId,  String departmentName,  DateTime appointmentDate,  String timeSlotId,  String startTime,  String endTime,  AppointmentType type,  AppointmentStatus status,  String symptoms,  String? diagnosis,  String? prescription,  String? notes,  double consultationFee,  bool isPaid,  String? paymentId,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  DateTime? deletedAt)  $default,) {final _that = this;
switch (_that) {
case _AppointmentModel():
return $default(_that.id,_that.appointmentNumber,_that.patientId,_that.patientName,_that.patientPhone,_that.patientEmail,_that.hospitalId,_that.hospitalName,_that.doctorId,_that.doctorName,_that.departmentId,_that.departmentName,_that.appointmentDate,_that.timeSlotId,_that.startTime,_that.endTime,_that.type,_that.status,_that.symptoms,_that.diagnosis,_that.prescription,_that.notes,_that.consultationFee,_that.isPaid,_that.paymentId,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.deletedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String appointmentNumber,  String patientId,  String patientName,  String patientPhone,  String? patientEmail,  String hospitalId,  String hospitalName,  String doctorId,  String doctorName,  String departmentId,  String departmentName,  DateTime appointmentDate,  String timeSlotId,  String startTime,  String endTime,  AppointmentType type,  AppointmentStatus status,  String symptoms,  String? diagnosis,  String? prescription,  String? notes,  double consultationFee,  bool isPaid,  String? paymentId,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  DateTime? deletedAt)?  $default,) {final _that = this;
switch (_that) {
case _AppointmentModel() when $default != null:
return $default(_that.id,_that.appointmentNumber,_that.patientId,_that.patientName,_that.patientPhone,_that.patientEmail,_that.hospitalId,_that.hospitalName,_that.doctorId,_that.doctorName,_that.departmentId,_that.departmentName,_that.appointmentDate,_that.timeSlotId,_that.startTime,_that.endTime,_that.type,_that.status,_that.symptoms,_that.diagnosis,_that.prescription,_that.notes,_that.consultationFee,_that.isPaid,_that.paymentId,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.deletedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AppointmentModel implements AppointmentModel {
  const _AppointmentModel({required this.id, required this.appointmentNumber, required this.patientId, required this.patientName, required this.patientPhone, this.patientEmail, required this.hospitalId, required this.hospitalName, required this.doctorId, required this.doctorName, required this.departmentId, required this.departmentName, required this.appointmentDate, required this.timeSlotId, required this.startTime, required this.endTime, required this.type, required this.status, required this.symptoms, this.diagnosis, this.prescription, this.notes, required this.consultationFee, required this.isPaid, this.paymentId, required this.createdAt, required this.updatedAt, this.isDeleted = false, this.deletedAt});
  factory _AppointmentModel.fromJson(Map<String, dynamic> json) => _$AppointmentModelFromJson(json);

@override final  String id;
@override final  String appointmentNumber;
@override final  String patientId;
@override final  String patientName;
@override final  String patientPhone;
@override final  String? patientEmail;
@override final  String hospitalId;
@override final  String hospitalName;
@override final  String doctorId;
@override final  String doctorName;
@override final  String departmentId;
@override final  String departmentName;
@override final  DateTime appointmentDate;
@override final  String timeSlotId;
@override final  String startTime;
@override final  String endTime;
@override final  AppointmentType type;
@override final  AppointmentStatus status;
@override final  String symptoms;
@override final  String? diagnosis;
@override final  String? prescription;
@override final  String? notes;
@override final  double consultationFee;
@override final  bool isPaid;
@override final  String? paymentId;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool isDeleted;
@override final  DateTime? deletedAt;

/// Create a copy of AppointmentModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AppointmentModelCopyWith<_AppointmentModel> get copyWith => __$AppointmentModelCopyWithImpl<_AppointmentModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AppointmentModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AppointmentModel&&(identical(other.id, id) || other.id == id)&&(identical(other.appointmentNumber, appointmentNumber) || other.appointmentNumber == appointmentNumber)&&(identical(other.patientId, patientId) || other.patientId == patientId)&&(identical(other.patientName, patientName) || other.patientName == patientName)&&(identical(other.patientPhone, patientPhone) || other.patientPhone == patientPhone)&&(identical(other.patientEmail, patientEmail) || other.patientEmail == patientEmail)&&(identical(other.hospitalId, hospitalId) || other.hospitalId == hospitalId)&&(identical(other.hospitalName, hospitalName) || other.hospitalName == hospitalName)&&(identical(other.doctorId, doctorId) || other.doctorId == doctorId)&&(identical(other.doctorName, doctorName) || other.doctorName == doctorName)&&(identical(other.departmentId, departmentId) || other.departmentId == departmentId)&&(identical(other.departmentName, departmentName) || other.departmentName == departmentName)&&(identical(other.appointmentDate, appointmentDate) || other.appointmentDate == appointmentDate)&&(identical(other.timeSlotId, timeSlotId) || other.timeSlotId == timeSlotId)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.symptoms, symptoms) || other.symptoms == symptoms)&&(identical(other.diagnosis, diagnosis) || other.diagnosis == diagnosis)&&(identical(other.prescription, prescription) || other.prescription == prescription)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.consultationFee, consultationFee) || other.consultationFee == consultationFee)&&(identical(other.isPaid, isPaid) || other.isPaid == isPaid)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,appointmentNumber,patientId,patientName,patientPhone,patientEmail,hospitalId,hospitalName,doctorId,doctorName,departmentId,departmentName,appointmentDate,timeSlotId,startTime,endTime,type,status,symptoms,diagnosis,prescription,notes,consultationFee,isPaid,paymentId,createdAt,updatedAt,isDeleted,deletedAt]);

@override
String toString() {
  return 'AppointmentModel(id: $id, appointmentNumber: $appointmentNumber, patientId: $patientId, patientName: $patientName, patientPhone: $patientPhone, patientEmail: $patientEmail, hospitalId: $hospitalId, hospitalName: $hospitalName, doctorId: $doctorId, doctorName: $doctorName, departmentId: $departmentId, departmentName: $departmentName, appointmentDate: $appointmentDate, timeSlotId: $timeSlotId, startTime: $startTime, endTime: $endTime, type: $type, status: $status, symptoms: $symptoms, diagnosis: $diagnosis, prescription: $prescription, notes: $notes, consultationFee: $consultationFee, isPaid: $isPaid, paymentId: $paymentId, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, deletedAt: $deletedAt)';
}


}

/// @nodoc
abstract mixin class _$AppointmentModelCopyWith<$Res> implements $AppointmentModelCopyWith<$Res> {
  factory _$AppointmentModelCopyWith(_AppointmentModel value, $Res Function(_AppointmentModel) _then) = __$AppointmentModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String appointmentNumber, String patientId, String patientName, String patientPhone, String? patientEmail, String hospitalId, String hospitalName, String doctorId, String doctorName, String departmentId, String departmentName, DateTime appointmentDate, String timeSlotId, String startTime, String endTime, AppointmentType type, AppointmentStatus status, String symptoms, String? diagnosis, String? prescription, String? notes, double consultationFee, bool isPaid, String? paymentId, DateTime createdAt, DateTime updatedAt, bool isDeleted, DateTime? deletedAt
});




}
/// @nodoc
class __$AppointmentModelCopyWithImpl<$Res>
    implements _$AppointmentModelCopyWith<$Res> {
  __$AppointmentModelCopyWithImpl(this._self, this._then);

  final _AppointmentModel _self;
  final $Res Function(_AppointmentModel) _then;

/// Create a copy of AppointmentModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? appointmentNumber = null,Object? patientId = null,Object? patientName = null,Object? patientPhone = null,Object? patientEmail = freezed,Object? hospitalId = null,Object? hospitalName = null,Object? doctorId = null,Object? doctorName = null,Object? departmentId = null,Object? departmentName = null,Object? appointmentDate = null,Object? timeSlotId = null,Object? startTime = null,Object? endTime = null,Object? type = null,Object? status = null,Object? symptoms = null,Object? diagnosis = freezed,Object? prescription = freezed,Object? notes = freezed,Object? consultationFee = null,Object? isPaid = null,Object? paymentId = freezed,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,Object? deletedAt = freezed,}) {
  return _then(_AppointmentModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,appointmentNumber: null == appointmentNumber ? _self.appointmentNumber : appointmentNumber // ignore: cast_nullable_to_non_nullable
as String,patientId: null == patientId ? _self.patientId : patientId // ignore: cast_nullable_to_non_nullable
as String,patientName: null == patientName ? _self.patientName : patientName // ignore: cast_nullable_to_non_nullable
as String,patientPhone: null == patientPhone ? _self.patientPhone : patientPhone // ignore: cast_nullable_to_non_nullable
as String,patientEmail: freezed == patientEmail ? _self.patientEmail : patientEmail // ignore: cast_nullable_to_non_nullable
as String?,hospitalId: null == hospitalId ? _self.hospitalId : hospitalId // ignore: cast_nullable_to_non_nullable
as String,hospitalName: null == hospitalName ? _self.hospitalName : hospitalName // ignore: cast_nullable_to_non_nullable
as String,doctorId: null == doctorId ? _self.doctorId : doctorId // ignore: cast_nullable_to_non_nullable
as String,doctorName: null == doctorName ? _self.doctorName : doctorName // ignore: cast_nullable_to_non_nullable
as String,departmentId: null == departmentId ? _self.departmentId : departmentId // ignore: cast_nullable_to_non_nullable
as String,departmentName: null == departmentName ? _self.departmentName : departmentName // ignore: cast_nullable_to_non_nullable
as String,appointmentDate: null == appointmentDate ? _self.appointmentDate : appointmentDate // ignore: cast_nullable_to_non_nullable
as DateTime,timeSlotId: null == timeSlotId ? _self.timeSlotId : timeSlotId // ignore: cast_nullable_to_non_nullable
as String,startTime: null == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as String,endTime: null == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as AppointmentType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as AppointmentStatus,symptoms: null == symptoms ? _self.symptoms : symptoms // ignore: cast_nullable_to_non_nullable
as String,diagnosis: freezed == diagnosis ? _self.diagnosis : diagnosis // ignore: cast_nullable_to_non_nullable
as String?,prescription: freezed == prescription ? _self.prescription : prescription // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,consultationFee: null == consultationFee ? _self.consultationFee : consultationFee // ignore: cast_nullable_to_non_nullable
as double,isPaid: null == isPaid ? _self.isPaid : isPaid // ignore: cast_nullable_to_non_nullable
as bool,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
