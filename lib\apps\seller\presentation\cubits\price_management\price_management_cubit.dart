import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/seller/data/repositories/price_management_repository.dart';
import 'package:shivish/shared/models/product/product_model.dart';

part 'price_management_cubit.freezed.dart';
part 'price_management_cubit.g.dart';

enum PriceHistoryFilter {
  all,
  today,
  thisWeek,
  thisMonth,
  thisYear,
}

enum BulkUpdateType {
  percentageIncrease,
  percentageDecrease,
  fixedAmount,
  setPrice,
}

@freezed
sealed class PriceHistoryEntry with _$PriceHistoryEntry {
  const factory PriceHistoryEntry({
    required String productId,
    required String productName,
    required double oldPrice,
    required double newPrice,
    required String reason,
    required String updatedBy,
    required DateTime timestamp,
  }) = _PriceHistoryEntry;

  factory PriceHistoryEntry.fromJson(Map<String, dynamic> json) =>
      _$PriceHistoryEntryFromJson(json);
}

@freezed
sealed class PriceManagementState with _$PriceManagementState {
  const factory PriceManagementState({
    @Default([]) List<ProductModel> products,
    @Default([]) List<PriceHistoryEntry> priceHistory,
    @Default(false) bool isLoading,
    String? error,
  }) = _PriceManagementState;
}

@freezed
sealed class PriceManagementEvent with _$PriceManagementEvent {
  const factory PriceManagementEvent.loadPrices() = _LoadPrices;
  const factory PriceManagementEvent.updatePrice(
    String productId,
    double newPrice,
  ) = _UpdatePrice;
  const factory PriceManagementEvent.updateDiscount(
    String productId,
    double discount,
  ) = _UpdateDiscount;
  const factory PriceManagementEvent.bulkUpdatePrices(
    BulkUpdateType updateType,
    double value,
  ) = _BulkUpdatePrices;
  const factory PriceManagementEvent.filterHistory(
    PriceHistoryFilter filter,
  ) = _FilterHistory;
}

@injectable
class PriceManagementCubit extends Cubit<PriceManagementState> {
  final PriceManagementRepository _repository;

  PriceManagementCubit(this._repository) : super(const PriceManagementState());

  Future<void> loadProducts() async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      final products = await _repository.getProducts();
      emit(state.copyWith(products: products, isLoading: false));
    } catch (e) {
      emit(state.copyWith(
        error: e.toString(),
        isLoading: false,
      ));
    }
  }

  Future<void> updatePrice(String productId, double newPrice) async {
    try {
      await _repository.updatePrice(productId, newPrice);
      await loadProducts();
    } catch (e) {
      emit(state.copyWith(
        error: e.toString(),
      ));
    }
  }

  Future<void> updateDiscount(String productId, double discount) async {
    try {
      await _repository.updateDiscount(productId, discount);
      await loadProducts();
    } catch (e) {
      emit(state.copyWith(
        error: e.toString(),
      ));
    }
  }

  Future<void> bulkUpdatePrices(
      BulkUpdateType updateType, double value, List<String> productIds) async {
    try {
      await _repository.bulkUpdatePrices(
          updateType.toString(), value, productIds);
      await loadProducts();
    } catch (e) {
      emit(state.copyWith(
        error: e.toString(),
      ));
    }
  }

  Future<void> loadPriceHistory(PriceHistoryFilter filter) async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      final historyData =
          await _repository.getPriceHistory('', filter.name);
      final history = historyData.map((data) {
        // Handle timestamp - it could be a string or DateTime
        DateTime timestamp;
        final timestampData = data['timestamp'];
        if (timestampData is String) {
          timestamp = DateTime.parse(timestampData);
        } else if (timestampData is DateTime) {
          timestamp = timestampData;
        } else {
          timestamp = DateTime.now(); // fallback
        }

        return PriceHistoryEntry(
          productId: data['product_id'] as String? ?? '',
          productName: data['product_name'] as String? ?? '',
          oldPrice: (data['old_price'] as num?)?.toDouble() ?? 0.0,
          newPrice: (data['new_price'] as num?)?.toDouble() ?? 0.0,
          reason: data['reason'] as String? ?? '',
          updatedBy: data['updated_by'] as String? ?? '',
          timestamp: timestamp,
        );
      }).toList();
      emit(state.copyWith(priceHistory: history, isLoading: false));
    } catch (e) {
      emit(state.copyWith(
        error: e.toString(),
        isLoading: false,
      ));
    }
  }
}
