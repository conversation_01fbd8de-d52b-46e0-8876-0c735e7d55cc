import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// Auth Screens
import 'screens/auth/temple_login_screen.dart';
import 'screens/auth/temple_register_screen.dart';
import 'screens/auth/temple_pending_approval_screen.dart';

// Main Screens
import 'screens/home/<USER>';
import 'screens/services/temple_services_screen.dart';
import 'screens/bookings/temple_bookings_screen.dart';
import 'screens/analytics/temple_analytics_screen.dart';
import 'screens/profile/temple_profile_screen.dart';
import 'screens/settings/temple_settings_screen.dart';

// Main Screens will be implemented

// Shell
import 'widgets/temples_shell.dart';

class TemplesRoutes {
  // Auth Routes
  static const String login = '/temples/login';
  static const String register = '/temples/register';
  static const String forgotPassword = '/temples/forgot-password';
  static const String pendingApproval = '/temples/pending-approval';
  static const String agreement = '/temples/agreement';

  // Main Routes
  static const String home = '/temples/home';
  static const String profile = '/temples/profile';
  static const String services = '/temples/services';
  static const String bookings = '/temples/bookings';
  static const String analytics = '/temples/analytics';
  static const String settings = '/temples/settings';

  // Temple Management Routes
  static const String templeDetails = '/temples/temple';
  static const String editTemple = '/temples/temple/edit';
  static const String darshanManagement = '/temples/darshan';
  static const String sevaManagement = '/temples/seva';
  static const String timingsManagement = '/temples/timings';

  // Booking Routes
  static const String bookingDetails = '/temples/bookings/:id';

  // Settings Routes
  static const String languageSettings = '/temples/settings/language';
  static const String notificationSettings = '/temples/settings/notifications';
  static const String securitySettings = '/temples/settings/security';
  static const String about = '/temples/settings/about';

  static const String initialLocation = login;

  // Navigation helpers
  static void navigateToHome(BuildContext context) => context.go(home);
  static void navigateToProfile(BuildContext context) => context.go(profile);
  static void navigateToServices(BuildContext context) => context.go(services);
  static void navigateToBookings(BuildContext context) => context.go(bookings);
  static void navigateToAnalytics(BuildContext context) =>
      context.go(analytics);
  static void navigateToSettings(BuildContext context) => context.go(settings);

  static final router = GoRouter(
    initialLocation: initialLocation,
    routes: [
      // Auth Routes (outside shell)
      GoRoute(
        path: login,
        builder: (context, state) => const TempleLoginScreen(),
      ),
      GoRoute(
        path: register,
        builder: (context, state) => const TempleRegisterScreen(),
      ),
      GoRoute(
        path: forgotPassword,
        builder: (context, state) => const TempleForgotPasswordScreen(),
      ),
      GoRoute(
        path: pendingApproval,
        builder: (context, state) => const TemplePendingApprovalScreen(),
      ),
      GoRoute(
        path: agreement,
        builder: (context, state) => const TempleAgreementScreen(),
      ),

      // Main app routes (with shell)
      ShellRoute(
        builder: (context, state, child) {
          return TemplesShell(child: child);
        },
        routes: [
          // Main tab routes
          GoRoute(
            path: home,
            builder: (context, state) => const TempleHomeScreen(),
          ),
          GoRoute(
            path: profile,
            builder: (context, state) => const TempleProfileScreen(),
          ),
          GoRoute(
            path: services,
            builder: (context, state) => const TempleServicesScreen(),
          ),
          GoRoute(
            path: bookings,
            builder: (context, state) => const TempleBookingsScreen(),
          ),
          GoRoute(
            path: analytics,
            builder: (context, state) => const TempleAnalyticsScreen(),
          ),
          GoRoute(
            path: settings,
            builder: (context, state) => const TempleSettingsScreen(),
          ),

          // Temple Management Routes
          GoRoute(
            path: templeDetails,
            builder: (context, state) => const TempleDetailsScreen(),
          ),
          GoRoute(
            path: editTemple,
            builder: (context, state) => const EditTempleScreen(),
          ),
          GoRoute(
            path: darshanManagement,
            builder: (context, state) => const DarshanManagementScreen(),
          ),
          GoRoute(
            path: sevaManagement,
            builder: (context, state) => const SevaManagementScreen(),
          ),
          GoRoute(
            path: timingsManagement,
            builder: (context, state) => const TimingsManagementScreen(),
          ),

          // Booking Details
          GoRoute(
            path: bookingDetails,
            builder: (context, state) {
              final bookingId = state.pathParameters['id']!;
              return TempleBookingDetailsScreen(bookingId: bookingId);
            },
          ),

          // Settings Routes
          GoRoute(
            path: languageSettings,
            builder: (context, state) => const LanguageSettingsScreen(),
          ),
          GoRoute(
            path: notificationSettings,
            builder: (context, state) => const NotificationSettingsScreen(),
          ),
          GoRoute(
            path: securitySettings,
            builder: (context, state) => const SecuritySettingsScreen(),
          ),
          GoRoute(
            path: about,
            builder: (context, state) => const AboutScreen(),
          ),
        ],
      ),
    ],
  );
}

// Placeholder screens - these will be implemented
class TempleForgotPasswordScreen extends StatelessWidget {
  const TempleForgotPasswordScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: Text('Temple Forgot Password')));
}

class TempleAgreementScreen extends StatelessWidget {
  const TempleAgreementScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: Text('Temple Agreement')));
}

// Remaining placeholder screens
class TempleDetailsScreen extends StatelessWidget {
  const TempleDetailsScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: Text('Temple Details')));
}

class EditTempleScreen extends StatelessWidget {
  const EditTempleScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: Text('Edit Temple')));
}

// Additional placeholder screens - to be implemented
class DarshanManagementScreen extends StatelessWidget {
  const DarshanManagementScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: Text('Darshan Management')));
}

class SevaManagementScreen extends StatelessWidget {
  const SevaManagementScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: Text('Seva Management')));
}

class TimingsManagementScreen extends StatelessWidget {
  const TimingsManagementScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: Text('Timings Management')));
}

class TempleBookingDetailsScreen extends StatelessWidget {
  final String bookingId;
  const TempleBookingDetailsScreen({super.key, required this.bookingId});
  @override
  Widget build(BuildContext context) =>
      Scaffold(body: Center(child: Text('Booking Details: $bookingId')));
}

class LanguageSettingsScreen extends StatelessWidget {
  const LanguageSettingsScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: Text('Language Settings')));
}

class NotificationSettingsScreen extends StatelessWidget {
  const NotificationSettingsScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: Text('Notification Settings')));
}

class SecuritySettingsScreen extends StatelessWidget {
  const SecuritySettingsScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: Text('Security Settings')));
}

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: Text('About')));
}
