import 'package:flutter/material.dart';
import '../../../../shared/models/executor.dart';

class ExecutorPerformanceDialog extends StatefulWidget {
  final Executor executor;

  const ExecutorPerformanceDialog({
    super.key,
    required this.executor,
  });

  @override
  State<ExecutorPerformanceDialog> createState() =>
      _ExecutorPerformanceDialogState();
}

class _ExecutorPerformanceDialogState extends State<ExecutorPerformanceDialog> {
  late final Map<String, dynamic> _metrics;

  @override
  void initState() {
    super.initState();
    _metrics = Map.from(widget.executor.performanceMetrics ?? {});
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Executor Performance'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Name: ${widget.executor.name}'),
            const SizedBox(height: 16),
            _buildMetricField(
              'Verification Rate',
              'verificationRate',
              'Percentage of successful verifications',
            ),
            _buildMetricField(
              'Response Time',
              'responseTime',
              'Average response time in minutes',
            ),
            _buildMetricField(
              'Task Completion Rate',
              'taskCompletionRate',
              'Percentage of completed tasks',
            ),
            _buildMetricField(
              'Customer Satisfaction',
              'customerSatisfaction',
              'Rating out of 5',
            ),
            _buildMetricField(
              'Error Rate',
              'errorRate',
              'Percentage of errors in verifications',
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: () => Navigator.pop(context, _metrics),
          child: const Text('Save'),
        ),
      ],
    );
  }

  Widget _buildMetricField(
    String label,
    String key,
    String hint,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        initialValue: _metrics[key]?.toString() ?? '',
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          border: const OutlineInputBorder(),
        ),
        keyboardType: TextInputType.number,
        onChanged: (value) {
          if (value.isNotEmpty) {
            _metrics[key] = double.tryParse(value) ?? 0;
          } else {
            _metrics.remove(key);
          }
        },
      ),
    );
  }
}
