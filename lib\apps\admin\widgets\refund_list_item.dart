import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/refund/refund_bloc.dart';
import 'package:shivish/apps/admin/bloc/refund/refund_event.dart';
import 'package:shivish/shared/models/refund.dart';
import 'package:shivish/shared/models/user/user_model.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/shared/services/user/user_service.dart';
import 'package:shivish/shared/ui_components/messages/success_message.dart';
import 'package:shivish/shared/ui_components/badges/status_badge.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';

class RefundListItem extends StatelessWidget {
  final Refund refund;
  final AuthService authService;
  final UserService userService;

  const RefundListItem({
    super.key,
    required this.refund,
    required this.authService,
    required this.userService,
  });

  Future<String?> _getCurrentAdminId() async {
    final user = await userService.getCurrentUser();
    if (user?.role == UserRole.admin) {
      return user?.id;
    }
    return null;
  }

  Future<void> _showStatusDialog(BuildContext context) async {
    final adminId = await _getCurrentAdminId();
    if (adminId == null) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Unauthorized action')),
        );
      }
      return;
    }

    if (!context.mounted) return;

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Status'),
        content: const Text('Choose an action for this refund:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, 'approve'),
            child: const Text('Approve'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 'reject'),
            child: const Text('Reject'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 'process'),
            child: const Text('Process'),
          ),
        ],
      ),
    );

    if (!context.mounted) return;

    switch (result) {
      case 'approve':
        context.read<RefundBloc>().add(
              RefundEvent.approveRefund(refund.id, adminId),
            );
        ScaffoldMessenger.of(context).showSnackBar(
          SuccessMessage(message: 'Refund approved successfully'),
        );
        break;
      case 'reject':
        _showRejectDialog(context, adminId);
        break;
      case 'process':
        _showProcessDialog(context, adminId);
        break;
    }
  }

  Future<void> _showRejectDialog(BuildContext context, String adminId) async {
    final reasonController = TextEditingController();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Refund'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppTextField(
              controller: reasonController,
              label: 'Rejection Reason',
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (reasonController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter a reason')),
                );
                return;
              }
              Navigator.pop(context, true);
            },
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (result == true && context.mounted) {
      context.read<RefundBloc>().add(
            RefundEvent.rejectRefund(
              refund.id,
              adminId,
              reasonController.text.trim(),
            ),
          );
      ScaffoldMessenger.of(context).showSnackBar(
        SuccessMessage(message: 'Refund rejected successfully'),
      );
    }
  }

  Future<void> _showProcessDialog(BuildContext context, String adminId) async {
    final transactionIdController = TextEditingController();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Process Refund'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppTextField(
              controller: transactionIdController,
              label: 'Transaction ID',
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (transactionIdController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please enter transaction ID')),
                );
                return;
              }
              Navigator.pop(context, true);
            },
            child: const Text('Process'),
          ),
        ],
      ),
    );

    if (result == true && context.mounted) {
      context.read<RefundBloc>().add(
            RefundEvent.processRefund(
              refund.id,
              adminId,
              transactionIdController.text.trim(),
            ),
          );
      ScaffoldMessenger.of(context).showSnackBar(
        SuccessMessage(message: 'Refund processed successfully'),
      );
    }
  }

  Future<void> _showEditDialog(BuildContext context) async {
    final amountController =
        TextEditingController(text: refund.amount.toString());
    final reasonController = TextEditingController(text: refund.reason);

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Refund'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppTextField(
              controller: amountController,
              label: 'Amount',
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            AppTextField(
              controller: reasonController,
              label: 'Reason',
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (amountController.text.trim().isEmpty ||
                  reasonController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please fill all fields')),
                );
                return;
              }
              Navigator.pop(context, true);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result == true && context.mounted) {
      final updatedRefund = refund.copyWith(
        amount: double.parse(amountController.text.trim()),
        reason: reasonController.text.trim(),
      );
      context.read<RefundBloc>().add(RefundEvent.updateRefund(updatedRefund));
      ScaffoldMessenger.of(context).showSnackBar(
        SuccessMessage(message: 'Refund updated successfully'),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text('Order #${refund.orderId}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('Amount: ₹${refund.amount}'),
            Text('Reason: ${refund.reason}'),
            const SizedBox(height: 4),
            StatusBadge(
              isActive: refund.status == 'approved',
              onToggle: () => _showStatusDialog(context),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _showEditDialog(context),
            ),
            IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () => _showStatusDialog(context),
            ),
          ],
        ),
      ),
    );
  }
}
