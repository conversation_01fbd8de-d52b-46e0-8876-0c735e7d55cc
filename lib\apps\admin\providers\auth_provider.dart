import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/models/user/user_model.dart';

/// Admin auth state class
class AdminAuthState {
  final bool isLoading;
  final User? user;
  final String? error;
  final bool isAdmin;
  final List<Map<String, dynamic>> pendingAdminRequests;

  const AdminAuthState({
    this.isLoading = false,
    this.user,
    this.error,
    this.isAdmin = false,
    this.pendingAdminRequests = const [],
  });

  AdminAuthState copyWith({
    bool? isLoading,
    User? user,
    String? error,
    bool? isAdmin,
    List<Map<String, dynamic>>? pendingAdminRequests,
  }) {
    return AdminAuthState(
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error ?? this.error,
      isAdmin: isAdmin ?? this.isAdmin,
      pendingAdminRequests: pendingAdminRequests ?? this.pendingAdminRequests,
    );
  }
}

/// Provider for the admin auth state
final adminAuthStateProvider =
    StateNotifierProvider<AdminAuthNotifier, AdminAuthState>((ref) {
  return AdminAuthNotifier();
});

/// Notifier for managing admin auth state
/// This is a simplified version that always stays authenticated
class AdminAuthNotifier extends StateNotifier<AdminAuthState> {
  final _supabase = Supabase.instance.client;
  final _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());

  AdminAuthNotifier() : super(const AdminAuthState()) {
    // Initialize database service
    _initializeDatabase();
    // Initialize with a mock admin user
    _initMockAdmin();
  }

  /// Initialize database service
  Future<void> _initializeDatabase() async {
    try {
      await _databaseService.initialize();
    } catch (e) {
      debugPrint('Failed to initialize database service: $e');
    }
  }

  void _initMockAdmin() {
    debugPrint('AdminAuthNotifier: Initializing with mock admin user');

    // Set the state to authenticated with admin privileges
    state = state.copyWith(
      isAdmin: true,
      isLoading: false,
    );

    debugPrint('AdminAuthNotifier: Mock admin user initialized');
  }

  /// Sign in as admin - This method is deprecated and will always throw an exception
  Future<void> signInAsAdmin() async {
    throw Exception(
        'Default admin sign-in has been removed. Please use the login form with your admin credentials or register a new admin account.');
  }

  /// Request admin access
  Future<void> requestAdminAccess({
    required String email,
    required String password,
    required String displayName,
    required String reason,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Create user with Supabase Auth
      final authResponse = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      final user = authResponse.user;
      if (user == null) {
        throw Exception('User is null after creation');
      }

      // Check if this is the first admin account
      final adminUsers = await _databaseService.getAll(
        'users',
        where: 'is_admin = ?',
        whereParams: [true],
      );

      final isFirstAdmin = adminUsers.isEmpty;

      // Set admin status based on whether this is the first admin
      final adminStatus = isFirstAdmin;
      final requestStatus = isFirstAdmin ? 'approved' : 'pending';
      final now = DateTime.now().toIso8601String();

      // Create admin request document
      await _databaseService.create('admin_requests', {
        'uid': user.id,
        'email': email,
        'display_name': displayName,
        'reason': reason,
        'status': requestStatus,
        'requested_at': now,
        'updated_at': now,
        'approved_at': isFirstAdmin ? now : null,
        'is_first_admin': isFirstAdmin,
      });

      // Create user document with admin status
      await _databaseService.create('users', {
        'uid': user.id,
        'email': email,
        'display_name': displayName,
        'role': UserRole.admin.name,
        'is_admin': adminStatus,
        'is_default_admin': false,
        'is_first_admin': isFirstAdmin,
        'created_at': now,
        'updated_at': now,
      });

      // If this is the first admin, show a success message
      if (isFirstAdmin) {
        debugPrint(
            'AdminAuthNotifier: First admin account created and automatically approved');
      } else {
        debugPrint('AdminAuthNotifier: Admin request submitted for approval');
      }

      state = state.copyWith(
        user: user,
        isAdmin: adminStatus,
      );

      return;
    } catch (e) {
      debugPrint('AdminAuthNotifier: Error in requestAdminAccess: $e');
      state = state.copyWith(error: e.toString());
      rethrow;
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Approve admin request
  Future<void> approveAdminRequest(String requestId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Get request document
      final requestData = await _databaseService.find('admin_requests', requestId);
      if (requestData == null) {
        throw Exception('Admin request not found');
      }

      final userId = requestData['uid'] as String;
      final now = DateTime.now().toIso8601String();

      // Update user document to grant admin access
      await _databaseService.update('users', userId, {
        'is_admin': true,
        'updated_at': now,
      });

      // Update request status
      await _databaseService.update('admin_requests', requestId, {
        'status': 'approved',
        'approved_at': now,
        'approved_by': state.user?.id,
        'updated_at': now,
      });

      // Remove from pending requests
      state = state.copyWith(
        pendingAdminRequests: state.pendingAdminRequests
            .where((request) => request['id'] != requestId)
            .toList(),
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Reject admin request
  Future<void> rejectAdminRequest(String requestId, String reason) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Update request status
      final now = DateTime.now().toIso8601String();
      await _databaseService.update('admin_requests', requestId, {
        'status': 'rejected',
        'rejection_reason': reason,
        'rejected_at': now,
        'rejected_by': state.user?.id,
        'updated_at': now,
      });

      // Remove from pending requests
      state = state.copyWith(
        pendingAdminRequests: state.pendingAdminRequests
            .where((request) => request['id'] != requestId)
            .toList(),
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Sign out - simplified to maintain authentication
  Future<void> signOut() async {
    try {
      debugPrint('AdminAuthNotifier: Simplified sign out (stays authenticated)');
      state = state.copyWith(isLoading: true, error: null);

      // Simulate a delay
      await Future.delayed(const Duration(milliseconds: 300));

      // Stay authenticated
      state = state.copyWith(
        isAdmin: true,
        error: null,
      );
    } catch (e) {
      debugPrint('AdminAuthNotifier: Error in simplified sign out: $e');
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Sign in with credentials - simplified to always succeed
  Future<void> signInWithCredentials({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('AdminAuthNotifier: Starting simplified sign in process');
      state = state.copyWith(isLoading: true, error: null);

      // Simulate a delay for a more realistic login experience
      await Future.delayed(const Duration(milliseconds: 500));

      // Always succeed with admin privileges
      state = state.copyWith(
        isAdmin: true,
        error: null,
      );

      debugPrint('AdminAuthNotifier: Sign in successful (simplified)');
    } catch (e) {
      debugPrint('AdminAuthNotifier: Error in simplified sign in: $e');
      state = state.copyWith(error: 'An unexpected error occurred');
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}
