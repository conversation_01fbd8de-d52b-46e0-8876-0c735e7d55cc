// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'price_management_cubit.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PriceHistoryEntry _$PriceHistoryEntryFromJson(Map<String, dynamic> json) =>
    _PriceHistoryEntry(
      productId: json['productId'] as String,
      productName: json['productName'] as String,
      oldPrice: (json['oldPrice'] as num).toDouble(),
      newPrice: (json['newPrice'] as num).toDouble(),
      reason: json['reason'] as String,
      updatedBy: json['updatedBy'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$PriceHistoryEntryToJson(_PriceHistoryEntry instance) =>
    <String, dynamic>{
      'productId': instance.productId,
      'productName': instance.productName,
      'oldPrice': instance.oldPrice,
      'newPrice': instance.newPrice,
      'reason': instance.reason,
      'updatedBy': instance.updatedBy,
      'timestamp': instance.timestamp.toIso8601String(),
    };
