import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final paymentAlertSettingsProvider =
    AsyncNotifierProvider<PaymentAlertSettingsNotifier, Map<String, dynamic>>(
        () {
  return PaymentAlertSettingsNotifier();
});

class PaymentAlertSettingsNotifier extends AsyncNotifier<Map<String, dynamic>> {
  late final DatabaseService _databaseService;

  @override
  Future<Map<String, dynamic>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadPaymentAlertSettings();
  }

  Future<Map<String, dynamic>> _loadPaymentAlertSettings() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('users', userId);
      final settings = userData?['payment_alert_settings'] as Map<String, dynamic>?;

      return {
        'enable_payment_alerts': settings?['enable_payment_alerts'] ?? true,
        'enable_email_alerts': settings?['enable_email_alerts'] ?? true,
        'enable_push_alerts': settings?['enable_push_alerts'] ?? true,
        'enable_sms_alerts': settings?['enable_sms_alerts'] ?? false,
        'enable_low_balance_alerts': settings?['enable_low_balance_alerts'] ?? true,
        'low_balance_threshold': settings?['low_balance_threshold'] ?? 1000.0,
        'enable_payment_success_alerts': settings?['enable_payment_success_alerts'] ?? true,
        'enable_payment_failure_alerts': settings?['enable_payment_failure_alerts'] ?? true,
        'enable_refund_alerts': settings?['enable_refund_alerts'] ?? true,
        'enable_commission_alerts': settings?['enable_commission_alerts'] ?? true,
        'enable_withdrawal_alerts': settings?['enable_withdrawal_alerts'] ?? true,
        'enable_deposit_alerts': settings?['enable_deposit_alerts'] ?? true,
      };
    } catch (e) {
      debugPrint('Failed to load payment alert settings: $e');
      throw Exception('Failed to load payment alert settings: $e');
    }
  }

  Future<void> savePaymentAlertSettings(Map<String, dynamic> settings) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Validate threshold
      final threshold = settings['low_balance_threshold'] as double? ?? 1000.0;
      if (threshold < 0) {
        throw Exception('Low balance threshold cannot be negative');
      }

      await _databaseService.update('users', userId, {
        'payment_alert_settings': {
          ...settings,
          'updated_at': DateTime.now().toIso8601String(),
        },
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData(settings);
    } catch (e) {
      debugPrint('Failed to save payment alert settings: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}
