import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/buyer/models/list_submission_order.dart';
import 'package:shivish/shared/utils/logger.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';

final listSubmissionOrderRepositoryProvider =
    Provider<ListSubmissionOrderRepository>(
  (ref) => ListSubmissionOrderRepository(),
);

class ListSubmissionOrderRepository {
  final DatabaseService _databaseService;
  final _logger = getLogger('ListSubmissionOrderRepository');

  ListSubmissionOrderRepository({
    DatabaseService? databaseService,
  }) : _databaseService = databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment());

  Future<ListSubmissionOrder> createOrder({
    required String submissionId,
    required String buyerId,
    required String sellerId,
    required double totalAmount,
    required List<ListSubmissionOrderItem> items,
  }) async {
    try {
      final orderId = 'list_order_${DateTime.now().millisecondsSinceEpoch}_$buyerId';

      final order = ListSubmissionOrder(
        id: orderId,
        submissionId: submissionId,
        buyerId: buyerId,
        sellerId: sellerId,
        totalAmount: totalAmount,
        items: items,
        createdAt: DateTime.now(),
      );

      final orderData = {
        'id': orderId,
        'submissionId': submissionId,
        'buyerId': buyerId,
        'sellerId': sellerId,
        'totalAmount': totalAmount,
        'items': [], // Simplified for now - items would need proper serialization
        'createdAt': DateTime.now().toIso8601String(),
        'status': 'pending',
      };

      await _databaseService.create('list_submission_orders', orderData);

      // Update the submission status to 'converted'
      final submissionData = await _databaseService.find('list_submissions', submissionId);
      if (submissionData != null) {
        await _databaseService.update('list_submissions', submissionId, {
          ...submissionData,
          'status': 'converted',
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }

      return order;
    } catch (e, st) {
      _logger.severe('Error creating list submission order', e, st);
      throw Exception('Failed to create order from list submission');
    }
  }

  Future<void> updateOrderStatus({
    required String orderId,
    required String status,
  }) async {
    try {
      final existingOrder = await _databaseService.find('list_submission_orders', orderId);
      if (existingOrder != null) {
        await _databaseService.update('list_submission_orders', orderId, {
          ...existingOrder,
          'status': status,
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }
    } catch (e, st) {
      _logger.severe('Error updating order status', e, st);
      throw Exception('Failed to update order status');
    }
  }

  Future<void> updatePaymentStatus({
    required String orderId,
    required String paymentId,
    required bool isPaid,
  }) async {
    try {
      final existingOrder = await _databaseService.find('list_submission_orders', orderId);
      if (existingOrder != null) {
        await _databaseService.update('list_submission_orders', orderId, {
          ...existingOrder,
          'paymentId': paymentId,
          'isPaid': isPaid,
          'status': isPaid ? 'paid' : 'payment_failed',
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }
    } catch (e, st) {
      _logger.severe('Error updating payment status', e, st);
      throw Exception('Failed to update payment status');
    }
  }

  Future<void> updateDeliveryId({
    required String orderId,
    required String deliveryId,
  }) async {
    try {
      final existingOrder = await _databaseService.find('list_submission_orders', orderId);
      if (existingOrder != null) {
        await _databaseService.update('list_submission_orders', orderId, {
          ...existingOrder,
          'deliveryId': deliveryId,
          'status': 'delivery_assigned',
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }
    } catch (e, st) {
      _logger.severe('Error updating delivery ID', e, st);
      throw Exception('Failed to update delivery ID');
    }
  }

  Stream<ListSubmissionOrder> watchOrder(String orderId) {
    return _databaseService.watchCollection(
      'list_submission_orders',
      where: 'id = ?',
      whereParams: [orderId],
    ).map((ordersData) {
      if (ordersData.isNotEmpty) {
        return ListSubmissionOrder.fromJson(ordersData.first);
      } else {
        throw Exception('Order not found');
      }
    });
  }

  Stream<List<ListSubmissionOrder>> watchBuyerOrders(String buyerId) {
    return _databaseService.watchCollection(
      'list_submission_orders',
      where: 'buyerId = ?',
      whereParams: [buyerId],
      orderBy: 'createdAt DESC',
    ).map((ordersData) => ordersData
        .map((orderData) => ListSubmissionOrder.fromJson(orderData))
        .toList());
  }
}
