import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/providers/technician_provider.dart';
import '../../../../shared/models/technician/technician.dart';
import 'technician_details_screen.dart';

class TechnicianListingScreen extends ConsumerStatefulWidget {
  const TechnicianListingScreen({super.key});

  @override
  ConsumerState<TechnicianListingScreen> createState() =>
      _TechnicianListingScreenState();
}

class _TechnicianListingScreenState
    extends ConsumerState<TechnicianListingScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedSpecialization = 'All';
  String _selectedSkill = 'All';
  double _minRating = 0.0;
  bool _showFilters = false;

  final List<String> _specializations = [
    'All',
    'Plumbing',
    'Electrical',
    'Carpentry',
    'Painting',
    'Appliance Repair',
    'AC Service',
    'Other'
  ];

  final List<String> _skills = [
    'All',
    'Emergency Service',
    '24/7 Available',
    'Expert',
    'Certified',
    'Experience > 5 years',
    'Experience > 10 years',
    'Other'
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<Technician> _getFilteredTechnicians(
      List<Technician> technicians) {
    return technicians.where((technician) {
      final matchesSearch =
          technician.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              (technician.specializations.isNotEmpty ?
                technician.specializations.first.toLowerCase().contains(_searchQuery.toLowerCase()) : false);

      final matchesSpecialization = _selectedSpecialization == 'All' ||
          (technician.specializations.isNotEmpty &&
           technician.specializations.contains(_selectedSpecialization));

      final matchesSkill =
          _selectedSkill == 'All' ||
          (technician.specializations.contains(_selectedSkill));

      final matchesRating = technician.rating >= _minRating;

      return matchesSearch &&
          matchesSpecialization &&
          matchesSkill &&
          matchesRating;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final techniciansAsync = ref.watch(technicianListProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Available Technicians'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              showSearch(
                context: context,
                delegate: TechnicianSearchDelegate(
                  searchController: _searchController,
                  onSearch: (query) {
                    setState(() => _searchQuery = query);
                  },
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              setState(() => _showFilters = !_showFilters);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          if (_showFilters)
            Container(
              padding: const EdgeInsets.all(16),
              color: theme.colorScheme.surfaceContainerHighest,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Specialization',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _specializations.map((spec) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: Text(spec),
                            selected: _selectedSpecialization == spec,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() => _selectedSpecialization = spec);
                              }
                            },
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Skills',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _skills.map((skill) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: Text(skill),
                            selected: _selectedSkill == skill,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() => _selectedSkill = skill);
                              }
                            },
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Minimum Rating',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  Slider(
                    value: _minRating,
                    min: 0,
                    max: 5,
                    divisions: 10,
                    label: _minRating.toStringAsFixed(1),
                    onChanged: (value) {
                      setState(() => _minRating = value);
                    },
                  ),
                ],
              ),
            ),
          Expanded(
            child: techniciansAsync.when(
              data: (technicians) {
                final filteredTechnicians =
                    _getFilteredTechnicians(technicians);
                if (filteredTechnicians.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: theme.colorScheme.primary.withAlpha(128),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No Technicians Found',
                          style: theme.textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Try different search terms or filters',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.75,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: filteredTechnicians.length,
                  itemBuilder: (context, index) {
                    final technician = filteredTechnicians[index];
                    return Card(
                      clipBehavior: Clip.antiAlias,
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => TechnicianDetailsScreen(
                                technicianId: technician.id,
                              ),
                            ),
                          );
                        },
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AspectRatio(
                              aspectRatio: 1,
                              child: technician.profileImage != null
                                ? Image.network(
                                    technician.profileImage!,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        color: Colors.grey[300],
                                        child: const Icon(Icons.build, size: 64),
                                      );
                                    },
                                  )
                                : Container(
                                    color: Colors.grey[300],
                                    child: const Icon(Icons.build, size: 64),
                                  ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    technician.name,
                                    style: theme.textTheme.titleMedium,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    technician.specializations.isNotEmpty
                                        ? technician.specializations.first
                                        : 'Technician',
                                    style: theme.textTheme.bodyMedium,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      const Icon(Icons.star,
                                          color: Colors.amber),
                                      const SizedBox(width: 4),
                                      Text(
                                        technician.rating.toStringAsFixed(1),
                                        style: theme.textTheme.bodySmall,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        '(${technician.totalReviews})',
                                        style: theme.textTheme.bodySmall,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Text('Error: $error'),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TechnicianSearchDelegate extends SearchDelegate<String> {
  final TextEditingController searchController;
  final Function(String) onSearch;

  TechnicianSearchDelegate({
    required this.searchController,
    required this.onSearch,
  });

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
          searchController.clear();
          onSearch('');
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    onSearch(query);
    return const SizedBox.shrink();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    onSearch(query);
    return const SizedBox.shrink();
  }
}
