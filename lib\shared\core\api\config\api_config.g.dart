// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ApiConfigImpl _$$ApiConfigImplFromJson(Map<String, dynamic> json) =>
    _$ApiConfigImpl(
      baseUrl: json['baseUrl'] as String? ?? 'https://api.example.com',
      connectTimeout: (json['connectTimeout'] as num?)?.toInt() ?? 30000,
      receiveTimeout: (json['receiveTimeout'] as num?)?.toInt() ?? 30000,
      sendTimeout: (json['sendTimeout'] as num?)?.toInt() ?? 30000,
      maxRetries: (json['maxRetries'] as num?)?.toInt() ?? 3,
      enableLogging: json['enableLogging'] as bool? ?? true,
      enableCaching: json['enableCaching'] as bool? ?? true,
      cacheDuration: json['cacheDuration'] == null
          ? const Duration(hours: 1)
          : Duration(microseconds: (json['cacheDuration'] as num).toInt()),
      maxCacheSize: (json['maxCacheSize'] as num?)?.toInt() ?? 10 * 1024 * 1024,
      defaultHeaders:
          (json['defaultHeaders'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
      environment:
          $enumDecodeNullable(_$ApiEnvironmentEnumMap, json['environment']) ??
          ApiEnvironment.development,
    );

Map<String, dynamic> _$$ApiConfigImplToJson(_$ApiConfigImpl instance) =>
    <String, dynamic>{
      'baseUrl': instance.baseUrl,
      'connectTimeout': instance.connectTimeout,
      'receiveTimeout': instance.receiveTimeout,
      'sendTimeout': instance.sendTimeout,
      'maxRetries': instance.maxRetries,
      'enableLogging': instance.enableLogging,
      'enableCaching': instance.enableCaching,
      'cacheDuration': instance.cacheDuration.inMicroseconds,
      'maxCacheSize': instance.maxCacheSize,
      'defaultHeaders': instance.defaultHeaders,
      'environment': _$ApiEnvironmentEnumMap[instance.environment]!,
    };

const _$ApiEnvironmentEnumMap = {
  ApiEnvironment.development: 'development',
  ApiEnvironment.staging: 'staging',
  ApiEnvironment.production: 'production',
};
