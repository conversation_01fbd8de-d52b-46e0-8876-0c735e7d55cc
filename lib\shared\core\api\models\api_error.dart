import 'package:freezed_annotation/freezed_annotation.dart';

part 'api_error.g.dart';
part 'api_error.freezed.dart';

@freezed
sealed class ApiError with _$ApiError {
  const factory ApiError({
    @Default('') String code,
    @Default('') String message,
    Map<String, dynamic>? details,
    String? stackTrace,
    @Default('') String timestamp,
    Map<String, dynamic>? context,
  }) = _ApiError;

  factory ApiError.fromJson(Map<String, dynamic> json) =>
      _$ApiErrorFromJson(json);

  /// Creates an error instance for network-related errors
  factory ApiError.network({
    String message = 'Network error occurred',
    Map<String, dynamic>? details,
  }) =>
      ApiError(
        code: 'NETWORK_ERROR',
        message: message,
        details: details,
        timestamp: DateTime.now().toIso8601String(),
      );

  /// Creates an error instance for timeout errors
  factory ApiError.timeout({
    String message = 'Request timed out',
    Map<String, dynamic>? details,
  }) =>
      ApiError(
        code: 'TIMEOUT_ERROR',
        message: message,
        details: details,
        timestamp: DateTime.now().toIso8601String(),
      );

  /// Creates an error instance for server errors
  factory ApiError.server({
    String message = 'Server error occurred',
    Map<String, dynamic>? details,
  }) =>
      ApiError(
        code: 'SERVER_ERROR',
        message: message,
        details: details,
        timestamp: DateTime.now().toIso8601String(),
      );

  /// Creates an error instance for unauthorized access
  factory ApiError.unauthorized({
    String message = 'Unauthorized access',
    Map<String, dynamic>? details,
  }) =>
      ApiError(
        code: 'UNAUTHORIZED',
        message: message,
        details: details,
        timestamp: DateTime.now().toIso8601String(),
      );

  /// Creates an error instance for validation errors
  factory ApiError.validation({
    String message = 'Validation error',
    Map<String, dynamic>? details,
  }) =>
      ApiError(
        code: 'VALIDATION_ERROR',
        message: message,
        details: details,
        timestamp: DateTime.now().toIso8601String(),
      );

  /// Creates an error instance for unknown errors
  factory ApiError.unknown({
    String message = 'An unknown error occurred',
    Map<String, dynamic>? details,
  }) =>
      ApiError(
        code: 'UNKNOWN_ERROR',
        message: message,
        details: details,
        timestamp: DateTime.now().toIso8601String(),
      );
}
