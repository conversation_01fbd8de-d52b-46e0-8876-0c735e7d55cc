// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voice_command_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VoiceCommandConfigModel _$VoiceCommandConfigModelFromJson(
  Map<String, dynamic> json,
) => _VoiceCommandConfigModel(
  enabled: json['enabled'] as bool,
  wakeWord: json['wakeWord'] as String,
  language: json['language'] as String,
  sensitivity: (json['sensitivity'] as num).toDouble(),
  supportedCommands: (json['supportedCommands'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  commandMappings: Map<String, String>.from(json['commandMappings'] as Map),
);

Map<String, dynamic> _$VoiceCommandConfigModelToJson(
  _VoiceCommandConfigModel instance,
) => <String, dynamic>{
  'enabled': instance.enabled,
  'wakeWord': instance.wakeWord,
  'language': instance.language,
  'sensitivity': instance.sensitivity,
  'supportedCommands': instance.supportedCommands,
  'commandMappings': instance.commandMappings,
};
