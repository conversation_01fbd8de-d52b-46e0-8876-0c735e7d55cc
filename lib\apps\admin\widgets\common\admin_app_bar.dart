import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// A production-ready app bar for admin screens
class AdminAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// Creates an admin app bar
  const AdminAppBar({
    super.key,
    required this.title,
    this.actions,
    this.showBackButton = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.centerTitle = true,
    this.onBackPressed,
  });

  /// The title to display in the app bar
  final String title;

  /// Actions to display in the app bar
  final List<Widget>? actions;

  /// Whether to show the back button
  final bool showBackButton;

  /// Background color of the app bar
  final Color? backgroundColor;

  /// Foreground color of the app bar
  final Color? foregroundColor;

  /// Elevation of the app bar
  final double? elevation;

  /// Whether to center the title
  final bool centerTitle;

  /// Custom callback for back button press
  final VoidCallback? onBackPressed;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AppBar(
      title: Text(
        title,
        style: theme.textTheme.titleLarge?.copyWith(
          color: foregroundColor ?? Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: backgroundColor ?? theme.primaryColor,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: elevation ?? 2.0,
      centerTitle: centerTitle,
      leading: showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: onBackPressed ?? () => context.pop(),
              tooltip: 'Back',
            )
          : null,
      actions: actions,
      systemOverlayStyle: theme.appBarTheme.systemOverlayStyle,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// A specialized admin app bar with admin-specific styling
class AdminDashboardAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// Creates an admin dashboard app bar
  const AdminDashboardAppBar({
    super.key,
    required this.title,
    this.actions,
    this.showNotifications = true,
    this.notificationCount = 0,
    this.onNotificationPressed,
  });

  /// The title to display in the app bar
  final String title;

  /// Actions to display in the app bar
  final List<Widget>? actions;

  /// Whether to show the notification icon
  final bool showNotifications;

  /// Number of unread notifications
  final int notificationCount;

  /// Callback for notification icon press
  final VoidCallback? onNotificationPressed;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    final List<Widget> appBarActions = [
      if (showNotifications)
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications),
              onPressed: onNotificationPressed ?? () {
                // Default notification action
                context.push('/admin/notifications');
              },
              tooltip: 'Notifications',
            ),
            if (notificationCount > 0)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    notificationCount > 99 ? '99+' : notificationCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      if (actions != null) ...actions!,
    ];

    return AppBar(
      title: Row(
        children: [
          Icon(
            Icons.admin_panel_settings,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
      backgroundColor: const Color(0xFF1565C0), // Admin blue color
      foregroundColor: Colors.white,
      elevation: 4.0,
      centerTitle: false,
      actions: appBarActions.isNotEmpty ? appBarActions : null,
      systemOverlayStyle: theme.appBarTheme.systemOverlayStyle,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// A simple back app bar for executor screens
class BackAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// Creates a back app bar
  const BackAppBar({
    super.key,
    required this.title,
    this.onBackPressed,
    this.backgroundColor,
    this.foregroundColor,
  });

  /// The title to display in the app bar
  final String title;

  /// Custom callback for back button press
  final VoidCallback? onBackPressed;

  /// Background color of the app bar
  final Color? backgroundColor;

  /// Foreground color of the app bar
  final Color? foregroundColor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AppBar(
      title: Text(
        title,
        style: theme.textTheme.titleLarge?.copyWith(
          color: foregroundColor ?? Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: backgroundColor ?? theme.primaryColor,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: 2.0,
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: onBackPressed ?? () => context.pop(),
        tooltip: 'Back',
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
