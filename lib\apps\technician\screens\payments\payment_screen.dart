import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/technician/providers/technician_provider.dart';
import 'package:shivish/apps/technician/providers/payment_provider.dart';
import 'package:shivish/apps/technician/widgets/technician_app_toolbar.dart';
import 'package:shivish/apps/technician/technician_routes.dart';
import 'package:fl_chart/fl_chart.dart';

class PaymentScreen extends ConsumerWidget {
  const PaymentScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final technicianState = ref.watch(technicianProvider);

    return Scaffold(
      appBar: TechnicianAppToolbar.simple(
        title: 'Payments',
        fallbackRoute: TechnicianRoutes.home,
      ),
      body: technicianState.when(
        data: (technician) {
          if (technician == null) {
            return const Center(
              child: Text('No technician data available'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPaymentSummary(context, ref, technician.id),
                const SizedBox(height: 24),
                _buildEarningsChart(context, ref, technician.id),
                const SizedBox(height: 24),
                _buildRecentPayments(context, ref, technician.id),
                const SizedBox(height: 24),
                _buildSettlements(context, ref, technician.id),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildPaymentSummary(
      BuildContext context, WidgetRef ref, String technicianId) {
    final summaryState =
        ref.watch(technicianPaymentSummaryProvider(technicianId));

    return summaryState.when(
      data: (summary) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Payment Summary',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                _buildSummaryItem(
                  context,
                  'Total Earnings',
                  '₹${summary['totalEarnings'].toStringAsFixed(2)}',
                  Colors.green,
                ),
                _buildSummaryItem(
                  context,
                  'Commission',
                  '₹${summary['total_commission'].toStringAsFixed(2)}',
                  Colors.orange,
                ),
                _buildSummaryItem(
                  context,
                  'Total Settled',
                  '₹${summary['total_settled'].toStringAsFixed(2)}',
                  Colors.blue,
                ),
                _buildSummaryItem(
                  context,
                  'Pending Settlement',
                  '₹${summary['pending_settlement'].toStringAsFixed(2)}',
                  Colors.purple,
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stackTrace) => Center(
        child: Text('Error: $error'),
      ),
    );
  }

  Widget _buildEarningsChart(
      BuildContext context, WidgetRef ref, String technicianId) {
    final paymentsState = ref.watch(technicianPaymentsProvider(technicianId));

    return paymentsState.when(
      data: (payments) {
        if (payments.isEmpty) {
          return const Center(
            child: Text('No payment data available'),
          );
        }

        // Group payments by month
        final monthlyData = <String, double>{};
        for (final payment in payments) {
          final date =
              DateTime.parse(payment['createdAt'].toDate().toString());
          final monthKey =
              '${date.year}-${date.month.toString().padLeft(2, '0')}';
          monthlyData[monthKey] =
              (monthlyData[monthKey] ?? 0) + (payment['amount'] ?? 0);
        }

        final spots = monthlyData.entries.map((entry) {
          final date = DateTime.parse('${entry.key}-01');
          return FlSpot(date.millisecondsSinceEpoch.toDouble(), entry.value);
        }).toList();

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Earnings Trend',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 200,
                  child: LineChart(
                    LineChartData(
                      gridData: const FlGridData(show: true),
                      titlesData: FlTitlesData(
                        leftTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: true),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              final date = DateTime.fromMillisecondsSinceEpoch(
                                  value.toInt());
                              return Text('${date.month}/${date.year}');
                            },
                          ),
                        ),
                      ),
                      borderData: FlBorderData(show: true),
                      lineBarsData: [
                        LineChartBarData(
                          spots: spots,
                          isCurved: true,
                          color: Colors.blue,
                          barWidth: 3,
                          dotData: const FlDotData(show: true),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stackTrace) => Center(
        child: Text('Error: $error'),
      ),
    );
  }

  Widget _buildRecentPayments(
      BuildContext context, WidgetRef ref, String technicianId) {
    final paymentsState = ref.watch(technicianPaymentsProvider(technicianId));

    return paymentsState.when(
      data: (payments) {
        if (payments.isEmpty) {
          return const Center(
            child: Text('No recent payments'),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Recent Payments',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: payments.length,
                  itemBuilder: (context, index) {
                    final payment = payments[index];
                    final date = payment['createdAt'].toDate();
                    return ListTile(
                      title: Text('Payment #${payment['id']}'),
                      subtitle: Text(
                        '${date.day}/${date.month}/${date.year}',
                      ),
                      trailing: Text(
                        '₹${payment['amount'].toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stackTrace) => Center(
        child: Text('Error: $error'),
      ),
    );
  }

  Widget _buildSettlements(
      BuildContext context, WidgetRef ref, String technicianId) {
    final settlementsState =
        ref.watch(technicianSettlementsProvider(technicianId));

    return settlementsState.when(
      data: (settlements) {
        if (settlements.isEmpty) {
          return const Center(
            child: Text('No settlements found'),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Settlements',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: settlements.length,
                  itemBuilder: (context, index) {
                    final settlement = settlements[index];
                    final date = settlement['createdAt'].toDate();
                    return ListTile(
                      title: Text('Settlement #${settlement['id']}'),
                      subtitle: Text(
                        '${date.day}/${date.month}/${date.year}',
                      ),
                      trailing: Text(
                        '₹${settlement['amount'].toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stackTrace) => Center(
        child: Text('Error: $error'),
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String title,
    String value,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ],
      ),
    );
  }
}
