import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/models/hospital/doctor_model.dart';
import 'package:shivish/shared/models/hospital/hospital_model.dart';
import 'package:shivish/shared/services/hospital/hospital_service.dart';
import 'package:shivish/shared/repositories/hospital/hospital_repository.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

// Provider for DatabaseService
final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

// Provider for HospitalRepository
final hospitalRepositoryProvider = Provider<HospitalRepository>((ref) {
  // Note: HospitalRepository should be updated to use DatabaseService
  // For now, creating with default constructor
  return HospitalRepository();
});

// Provider for HospitalService
final hospitalServiceProvider = Provider<HospitalService>((ref) {
  final repository = ref.watch(hospitalRepositoryProvider);
  return HospitalService(repository);
});

// Provider for top doctors
final topDoctorsProvider = FutureProvider<List<DoctorModel>>((ref) async {
  final hospitalService = ref.watch(hospitalServiceProvider);
  return await hospitalService.getTopDoctors(limit: 10);
});

// Provider for doctors by specialty
final doctorsBySpecialtyProvider = FutureProvider.family<List<DoctorModel>, String>((ref, specialty) async {
  final hospitalService = ref.watch(hospitalServiceProvider);
  return await hospitalService.getDoctorsBySpecialty(specialty);
});

// Provider for nearby hospitals
final nearbyHospitalsProvider = FutureProvider<List<HospitalModel>>((ref) async {
  final hospitalService = ref.watch(hospitalServiceProvider);
  return await hospitalService.getNearbyHospitals();
});

// Provider for health packages
final healthPackagesProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final hospitalService = ref.watch(hospitalServiceProvider);
  return await hospitalService.getHealthPackages();
});

// Provider for a specific doctor
final doctorProvider = FutureProvider.family<DoctorModel?, String>((ref, doctorId) async {
  final hospitalService = ref.watch(hospitalServiceProvider);
  return await hospitalService.getDoctor(doctorId);
});

// Provider for a specific hospital
final hospitalProvider = FutureProvider.family<HospitalModel?, String>((ref, hospitalId) async {
  final hospitalService = ref.watch(hospitalServiceProvider);
  return await hospitalService.getHospital(hospitalId);
});

// Provider for a specific health package
final healthPackageProvider = FutureProvider.family<Map<String, dynamic>?, String>((ref, packageId) async {
  final hospitalService = ref.watch(hospitalServiceProvider);
  return await hospitalService.getHealthPackage(packageId);
});
