import 'package:flutter/material.dart';
import 'package:shivish/apps/seller/domain/models/payment_model.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';
import 'package:shivish/shared/utils/date_formatter.dart';

class SettlementListItem extends StatelessWidget {
  final SettlementModel settlement;

  const SettlementListItem({
    super.key,
    required this.settlement,
  });

  Color _getStatusColor(BuildContext context, String status) {
    final theme = Theme.of(context);
    switch (status.toLowerCase()) {
      case 'pending':
        return theme.colorScheme.tertiary;
      case 'processing':
        return theme.colorScheme.primary;
      case 'completed':
        return theme.colorScheme.secondary;
      case 'failed':
        return theme.colorScheme.error;
      default:
        return theme.colorScheme.onSurface;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.pending_outlined;
      case 'processing':
        return Icons.sync;
      case 'completed':
        return Icons.check_circle_outline;
      case 'failed':
        return Icons.error_outline;
      default:
        return Icons.help_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final statusColor = _getStatusColor(context, settlement.status);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Settlement #${settlement.id.substring(0, 8)}',
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        DateFormatter.format(settlement.date),
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                Text(
                  CurrencyFormatter.format(settlement.amount),
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  _getStatusIcon(settlement.status),
                  color: statusColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  settlement.status.toUpperCase(),
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: statusColor,
                  ),
                ),
              ],
            ),
            if (settlement.failureReason != null) ...[
              const SizedBox(height: 8),
              Text(
                settlement.failureReason!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
