import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../models/ticket_booking_models.dart';
import '../../providers/ticket_booking_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import 'flight_booking_screen.dart';

class FlightSearchScreen extends ConsumerStatefulWidget {
  final FlightSearchRequest searchRequest;

  const FlightSearchScreen({super.key, required this.searchRequest});

  @override
  ConsumerState<FlightSearchScreen> createState() => _FlightSearchScreenState();
}

class _FlightSearchScreenState extends ConsumerState<FlightSearchScreen> {
  @override
  void initState() {
    super.initState();
    // Trigger search when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(flightSearchProvider.notifier)
          .searchFlights(widget.searchRequest);
    });
  }

  @override
  Widget build(BuildContext context) {
    final searchState = ref.watch(flightSearchProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '${widget.searchRequest.origin} → ${widget.searchRequest.destination}',
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search summary card
          _buildSearchSummary(),

          // Search results
          Expanded(
            child: searchState.when(
              data: (response) {
                if (response == null) {
                  return const Center(child: Text('No search performed yet'));
                }

                if (response.flights.isEmpty) {
                  return _buildNoResultsView();
                }

                return _buildFlightsList(response.flights);
              },
              loading: () => const LoadingIndicator(),
              error: (error, stack) => ErrorView(
                message: 'Failed to search flights: $error',
                onRetry: () => ref
                    .read(flightSearchProvider.notifier)
                    .searchFlights(widget.searchRequest),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSummary() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.flight_takeoff, color: Colors.blue.shade700),
              const SizedBox(width: 8),
              Text(
                '${widget.searchRequest.origin} → ${widget.searchRequest.destination}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.calendar_today, size: 16, color: Colors.blue.shade600),
              const SizedBox(width: 4),
              Text(
                _formatDate(widget.searchRequest.departureDate),
                style: TextStyle(color: Colors.blue.shade600),
              ),
              if (widget.searchRequest.isRoundTrip &&
                  widget.searchRequest.returnDate != null) ...[
                const SizedBox(width: 16),
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: Colors.blue.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDate(widget.searchRequest.returnDate!),
                  style: TextStyle(color: Colors.blue.shade600),
                ),
              ],
              const Spacer(),
              Text(
                '${widget.searchRequest.adults} Adult${widget.searchRequest.adults > 1 ? 's' : ''}',
                style: TextStyle(color: Colors.blue.shade600),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFlightsList(List<Flight> flights) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: flights.length,
      itemBuilder: (context, index) {
        final flight = flights[index];
        return _buildFlightCard(flight);
      },
    );
  }

  Widget _buildFlightCard(Flight flight) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _selectFlight(flight),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Airline and flight number
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      flight.airline,
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    flight.flightNumber,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.grey,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${flight.currency} ${flight.price.toStringAsFixed(0)}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Flight times and route
              Row(
                children: [
                  // Departure
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _formatTime(flight.departureTime),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        flight.origin,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(width: 16),

                  // Flight path
                  Expanded(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                              ),
                            ),
                            Expanded(
                              child: Container(height: 2, color: Colors.blue),
                            ),
                            const Icon(
                              Icons.flight,
                              color: Colors.blue,
                              size: 16,
                            ),
                            Expanded(
                              child: Container(height: 2, color: Colors.blue),
                            ),
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          flight.duration,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Arrival
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        _formatTime(flight.arrivalTime),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        flight.destination,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Additional info
              Row(
                children: [
                  Icon(
                    Icons.airline_seat_recline_normal,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${flight.availableSeats} seats left',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.airplanemode_active,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    flight.aircraftType,
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNoResultsView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.flight_takeoff, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'No flights found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search criteria',
            style: TextStyle(color: Colors.grey.shade500),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Modify Search'),
          ),
        ],
      ),
    );
  }

  void _selectFlight(Flight flight) {
    // Navigate to flight details or booking screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Flight Selected'),
        content: Text(
          'Selected flight ${flight.flightNumber} for ${flight.currency} ${flight.price}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to flight booking screen
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => FlightBookingScreen(
                    flight: flight,
                    searchRequest: widget.searchRequest,
                  ),
                ),
              );
            },
            child: const Text('Book Now'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Flights'),
        content: const Text('Filter options coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
