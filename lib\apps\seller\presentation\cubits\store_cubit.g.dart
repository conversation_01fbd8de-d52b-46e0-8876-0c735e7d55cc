// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'store_cubit.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_StoreState _$StoreStateFromJson(Map<String, dynamic> json) => _StoreState(
  store: json['store'] == null
      ? null
      : StoreModel.fromJson(json['store'] as Map<String, dynamic>),
  isLoading: json['isLoading'] as bool? ?? false,
  isEditing: json['isEditing'] as bool? ?? false,
  error: json['error'] as String?,
);

Map<String, dynamic> _$StoreStateToJson(_StoreState instance) =>
    <String, dynamic>{
      'store': instance.store?.toJson(),
      'isLoading': instance.isLoading,
      'isEditing': instance.isEditing,
      'error': instance.error,
    };
