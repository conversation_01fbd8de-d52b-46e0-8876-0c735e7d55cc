import 'package:shivish/shared/models/order/order_model.dart';
import 'package:shivish/shared/models/product/product_model.dart';
import 'package:shivish/shared/services/order/order_service.dart';
import 'package:shivish/shared/services/product/product_service.dart';

/// Mock implementation of OrderService for development
class MockOrderService implements OrderService {
  @override
  Future<List<OrderModel>> getSellerOrders() async {
    // Return mock data
    await Future.delayed(const Duration(milliseconds: 300));
    return List.generate(
      10,
      (index) => OrderModel(
        id: 'order-$index',
        buyerId: 'customer-$index',
        sellerId: 'seller-1',
        customerName: 'Customer ${index + 1}',
        customerPhone: '+1234567890',
        items: List.generate(
          2,
          (itemIndex) => OrderItem(
            id: 'item-$itemIndex',
            productId: 'product-$itemIndex',
            name: 'Product ${itemIndex + 1}',
            imageUrl: 'https://via.placeholder.com/150',
            quantity: itemIndex + 1,
            price: (index + 1) * 100.0,
          ),
        ),
        total: (index + 1) * 150.0,
        status: OrderStatus.pending,
        createdAt: DateTime.now().subtract(Duration(days: index)),
        updatedAt: DateTime.now().subtract(Duration(days: index)),
        deliveryAddress: OrderAddress(
          street: '123 Mock Street',
          city: 'Mock City',
          state: 'Mock State',
          country: 'India',
          postalCode: '123456',
          contactName: 'Customer ${index + 1}',
          contactPhone: '+1234567890',
        ),
        paymentMethod: PaymentMethod.cod,
        paymentDetails: PaymentDetails(
          method: 'cod',
          status: PaymentStatus.pending,
          amount: (index + 1) * 150.0,
        ),
      ),
    );
  }

  // Implement other methods as needed
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

/// Mock implementation of ProductService for development
class MockProductService implements ProductService {
  @override
  Future<List<ProductModel>> getSellerProducts() async {
    // Return mock data
    await Future.delayed(const Duration(milliseconds: 300));
    return List.generate(
      15,
      (index) => ProductModel(
        id: 'product-$index',
        name: 'Product ${index + 1}',
        images: const ['https://via.placeholder.com/150'],
        price: (index + 1) * 100.0,
        quantity: (index + 1) * 10,
        createdAt: DateTime.now().subtract(Duration(days: index)),
        updatedAt: DateTime.now().subtract(Duration(days: index)),
        categoryId: 'category-${(index % 3) + 1}',
        description: 'This is product ${index + 1}',
        tags: const ['tag1', 'tag2'],
        rating: 4.5,
        reviewCount: (index + 1) * 5,
        sellerId: 'seller-1',
        productStatus: ProductStatus.approved,
        productType: ProductType.physical,
      ),
    );
  }

  // Implement other methods as needed
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}
