import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/product/product_model.dart';

part 'product_event.freezed.dart';

@freezed
sealed class ProductEvent with _$ProductEvent {
  const factory ProductEvent.loadProducts() = _LoadProducts;
  const factory ProductEvent.loadMoreProducts() = _LoadMoreProducts;
  const factory ProductEvent.updateProductStatus(
    ProductModel product,
    String status,
    String? notes,
    bool isActive,
  ) = _UpdateProductStatus;
  const factory ProductEvent.deleteProduct(String id) = _DeleteProduct;
  const factory ProductEvent.applyFilters(Map<String, dynamic> filters) =
      _ApplyFilters;
}
