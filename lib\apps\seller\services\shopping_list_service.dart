import 'package:flutter/foundation.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';

/// Service for managing shopping lists for sellers
class SellerShoppingListService {
  final DatabaseService _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());

  /// Get a shopping list by ID
  Stream<ShoppingListModel?> getShoppingList(String listId) {
    return _databaseService.watchRecord('shopping_lists', listId).map(
        (data) => data != null
            ? ShoppingListModel.fromJson(data)
            : null);
  }

  /// Get all shopping lists requested from this seller
  Stream<List<ShoppingListModel>> getRequestedShoppingLists(String sellerId) {
    return _databaseService.watchCollection(
      'shopping_lists',
      where: 'selected_seller_id = @param0 OR requested_sellers LIKE @param1',
      whereParams: [sellerId, '%$sellerId%'],
    ).map((dataList) {
      final lists = <ShoppingListModel>[];

      for (final data in dataList) {
        try {
          // Check if this list is for this seller
          final selectedSellerId = data['selected_seller_id'];
          final requestedSellers = data['requested_sellers'];

          bool isForThisSeller = false;
          if (selectedSellerId == sellerId) {
            isForThisSeller = true;
          } else if (requestedSellers is List && requestedSellers.contains(sellerId)) {
            isForThisSeller = true;
          } else if (requestedSellers is String && requestedSellers.contains(sellerId)) {
            isForThisSeller = true;
          }

          if (isForThisSeller) {
            // Ensure required fields have default values if they're null
            final listData = Map<String, dynamic>.from(data);
            listData['name'] = listData['name'] ?? 'Untitled List';
            listData['description'] = listData['description'] ?? '';
            listData['item_count'] = listData['item_count'] ?? 0;
            listData['total_price'] = listData['total_price'] ?? 0.0;
            listData['is_shared'] = listData['is_shared'] ?? false;
            listData['created_by'] = listData['created_by'] ?? 'Unknown User';

            // Ensure createdAt is a valid DateTime string
            if (listData['created_at'] == null) {
              listData['created_at'] = DateTime.now().toIso8601String();
            } else if (listData['created_at'] is DateTime) {
              listData['created_at'] = (listData['created_at'] as DateTime).toIso8601String();
            }

            // Ensure updatedAt is a valid DateTime string
            if (listData['updated_at'] == null) {
              listData['updated_at'] = DateTime.now().toIso8601String();
            } else if (listData['updated_at'] is DateTime) {
              listData['updated_at'] = (listData['updated_at'] as DateTime).toIso8601String();
            }

            // Ensure items is a valid list
            if (listData['items'] == null) {
              listData['items'] = [];
            }

            // Ensure status is valid
            listData['status'] = listData['status'] ?? 'requested';

            lists.add(ShoppingListModel.fromJson(listData));
          }
        } catch (e) {
          debugPrint('Error parsing shopping list document: $e');
          // Skip this document
        }
      }

      return lists;
    });
  }

  /// Update a shopping list with pricing information
  Future<void> updateShoppingList(ShoppingListModel list) async {
    try {
      final updatedData = list.copyWith(updatedAt: DateTime.now()).toJson();
      updatedData['updated_at'] = DateTime.now().toIso8601String();
      updatedData.remove('id'); // Remove ID from update data

      await _databaseService.update('shopping_lists', list.id, updatedData);
    } catch (e) {
      debugPrint('Error updating shopping list: $e');
      rethrow;
    }
  }
}
