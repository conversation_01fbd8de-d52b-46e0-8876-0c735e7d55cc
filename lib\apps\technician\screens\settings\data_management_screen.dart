import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/apps/technician/providers/data_management_provider.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

class DataManagementScreen extends ConsumerWidget {
  const DataManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dataState = ref.watch(dataManagementProvider);

    ref.listen(dataManagementProvider, (previous, next) {
      next.whenOrNull(
        error: (error, stackTrace) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(error.toString())),
          );
        },
      );
    });

    return Stack(
      children: [
        Scaffold(
          appBar: AppToolbar.simple(
            title: 'Data Management',
          ),
          body: ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              _buildSection(
                title: 'Export Data',
                description: 'Export your data to CSV format',
                onPressed: () => _showExportDialog(context, ref),
              ),
              const SizedBox(height: 16),
              _buildSection(
                title: 'Clear Data',
                description: 'Delete all your data',
                onPressed: () => _showClearDataDialog(context, ref),
              ),
              const SizedBox(height: 16),
              _buildSection(
                title: 'Backup Data',
                description: 'Create a backup of your data',
                onPressed: () => _showBackupDialog(context, ref),
              ),
              const SizedBox(height: 16),
              _buildSection(
                title: 'Restore Data',
                description: 'Restore data from backup',
                onPressed: () => _showRestoreDialog(context, ref),
              ),
            ],
          ),
        ),
        if (dataState.isLoading) const LoadingIndicator(),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required String description,
    required VoidCallback onPressed,
  }) {
    return Card(
      child: ListTile(
        title: Text(title),
        subtitle: Text(description),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onPressed,
      ),
    );
  }

  Future<void> _showExportDialog(BuildContext context, WidgetRef ref) async {
    final collections = ['orders', 'services', 'bookings', 'reviews'];
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Select data to export:'),
            const SizedBox(height: 16),
            ...collections.map(
              (collection) => ListTile(
                title: Text(collection),
                onTap: () {
                  Navigator.pop(context);
                  ref
                      .read(dataManagementProvider.notifier)
                      .exportData(collection);
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _showClearDataDialog(BuildContext context, WidgetRef ref) async {
    final collections = ['orders', 'services', 'bookings', 'reviews'];
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Data'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Select data to clear:'),
            const SizedBox(height: 16),
            ...collections.map(
              (collection) => ListTile(
                title: Text(collection),
                onTap: () {
                  Navigator.pop(context);
                  _showConfirmationDialog(
                    context,
                    'Are you sure you want to clear all $collection data?',
                    () => ref
                        .read(dataManagementProvider.notifier)
                        .clearData(collection),
                  );
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _showBackupDialog(BuildContext context, WidgetRef ref) async {
    final collections = ['orders', 'services', 'bookings', 'reviews'];
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Backup Data'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Select data to backup:'),
            const SizedBox(height: 16),
            ...collections.map(
              (collection) => ListTile(
                title: Text(collection),
                onTap: () {
                  Navigator.pop(context);
                  ref
                      .read(dataManagementProvider.notifier)
                      .backupData(collection);
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _showRestoreDialog(BuildContext context, WidgetRef ref) async {
    final collections = ['orders', 'services', 'bookings', 'reviews'];
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Restore Data'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Select data to restore:'),
            const SizedBox(height: 16),
            ...collections.map(
              (collection) => ListTile(
                title: Text(collection),
                onTap: () {
                  Navigator.pop(context);
                  _showConfirmationDialog(
                    context,
                    'Are you sure you want to restore $collection data? This will replace all current data.',
                    () => ref
                        .read(dataManagementProvider.notifier)
                        .restoreData(collection),
                  );
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _showConfirmationDialog(
    BuildContext context,
    String message,
    VoidCallback onConfirm,
  ) {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onConfirm();
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }
}
