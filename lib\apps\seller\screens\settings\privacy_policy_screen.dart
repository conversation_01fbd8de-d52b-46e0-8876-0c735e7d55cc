import 'package:flutter/material.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppToolbar(
        title: 'Privacy Policy',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Privacy Policy',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            Text(
              'Last Updated: ${DateTime.now().toString().substring(0, 10)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey,
                  ),
            ),
            const SizedBox(height: 24),
            const Text(
              'This Privacy Policy describes how SHIVISH ("we", "our", or "us") collects, uses, and shares your personal information when you use our mobile application ("App").',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            _buildSection(
              context,
              title: 'Information We Collect',
              content:
                  'We collect information that you provide directly to us, such as when you create an account, update your profile, use the features in the App, or communicate with us. This information may include your name, email address, phone number, address, business information, and payment information.\n\nWe also automatically collect certain information about your device and how you interact with the App, including your IP address, device type, operating system, app version, and usage data.',
            ),
            _buildSection(
              context,
              title: 'How We Use Your Information',
              content:
                  'We use the information we collect to:\n\n• Provide, maintain, and improve the App\n• Process transactions and send related information\n• Send you technical notices, updates, security alerts, and support messages\n• Respond to your comments, questions, and customer service requests\n• Communicate with you about products, services, offers, and events\n• Monitor and analyze trends, usage, and activities in connection with the App\n• Detect, investigate, and prevent fraudulent transactions and other illegal activities\n• Personalize your experience in the App',
            ),
            _buildSection(
              context,
              title: 'Sharing Your Information',
              content:
                  'We may share the information we collect as follows:\n\n• With vendors, service providers, and consultants that perform services for us\n• In response to a request for information if we believe disclosure is in accordance with any applicable law, regulation, or legal process\n• If we believe your actions are inconsistent with our user agreements or policies, or to protect the rights, property, and safety of us or others\n• In connection with, or during negotiations of, any merger, sale of company assets, financing, or acquisition of all or a portion of our business by another company\n• With your consent or at your direction',
            ),
            _buildSection(
              context,
              title: 'Your Choices',
              content:
                  'You can update your account information and preferences at any time in the App settings. You may also opt out of receiving promotional communications from us by following the instructions in those communications.',
            ),
            _buildSection(
              context,
              title: 'Data Security',
              content:
                  'We take reasonable measures to help protect information about you from loss, theft, misuse, unauthorized access, disclosure, alteration, and destruction.',
            ),
            _buildSection(
              context,
              title: 'Changes to This Privacy Policy',
              content:
                  'We may change this Privacy Policy from time to time. If we make changes, we will notify you by revising the date at the top of the policy and, in some cases, we may provide you with additional notice.',
            ),
            _buildSection(
              context,
              title: 'Contact Us',
              content:
                  'If you have any questions about this Privacy Policy, please contact us at:\n\nEmail: <EMAIL>\nPhone: +91 1234567890\nAddress: 123 Main Street, City, State, PIN Code, India',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required String content,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(fontSize: 16, height: 1.5),
          ),
        ],
      ),
    );
  }
}
