import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/models/technician/break_time_settings_model.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final breakTimeSettingsProvider =
    AsyncNotifierProvider<BreakTimeSettingsNotifier, BreakTimeSettingsModel?>(
        () {
  return BreakTimeSettingsNotifier();
});

class BreakTimeSettingsNotifier extends AsyncNotifier<BreakTimeSettingsModel?> {
  static const String _collection = 'technician_break_time_settings';

  @override
  Future<BreakTimeSettingsModel?> build() async {
    try {
      final authService = ref.read(authServiceProvider);
      final userId = authService.currentUser?.id;
      if (userId == null) return null;

      final databaseService = ref.read(databaseServiceProvider);
      final data = await databaseService.find(_collection, userId);

      if (data == null) {
        // Return default settings if none exist
        return const BreakTimeSettingsModel(
          lunchStartTime: '12:00 PM',
          lunchEndTime: '1:00 PM',
          teaStartTime: '4:00 PM',
          teaEndTime: '4:30 PM',
        );
      }

      return BreakTimeSettingsModel.fromJson(data);
    } catch (e) {
      debugPrint('Error loading break time settings: $e');
      return null;
    }
  }

  Future<void> updateSettings(BreakTimeSettingsModel settings) async {
    state = const AsyncValue.loading();
    try {
      final authService = ref.read(authServiceProvider);
      final userId = authService.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final databaseService = ref.read(databaseServiceProvider);
      final settingsData = settings.toJson();
      settingsData['updated_at'] = DateTime.now().toIso8601String();

      // Try to update first, if not found, create new record
      final existingData = await databaseService.find(_collection, userId);
      if (existingData != null) {
        await databaseService.update(_collection, userId, settingsData);
      } else {
        settingsData['id'] = userId;
        settingsData['created_at'] = DateTime.now().toIso8601String();
        await databaseService.create(_collection, settingsData);
      }

      state = AsyncValue.data(settings);
    } catch (e, st) {
      debugPrint('Error updating break time settings: $e');
      state = AsyncValue.error(e, st);
    }
  }
}
