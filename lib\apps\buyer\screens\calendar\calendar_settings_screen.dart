import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:latlong2/latlong.dart' as latlong2;
import '../../../../shared/providers/panchangam_notification_settings_provider.dart';
import '../../../../shared/services/location/location_service.dart';
import '../../../../shared/core/service_locator.dart';

/// Screen for calendar settings
class CalendarSettingsScreen extends ConsumerStatefulWidget {
  /// Creates a [CalendarSettingsScreen]
  const CalendarSettingsScreen({super.key});

  @override
  ConsumerState<CalendarSettingsScreen> createState() =>
      _CalendarSettingsScreenState();
}

class _CalendarSettingsScreenState
    extends ConsumerState<CalendarSettingsScreen> {
  final LocationService _locationService = serviceLocator<LocationService>();
  bool _isLoadingLocation = false;
  String _currentLocation = 'Loading...';

  @override
  void initState() {
    super.initState();
    _loadCurrentLocation();
  }

  Future<void> _loadCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      final position = await _locationService.getCurrentLocation();
      if (position != null) {
        final address = await _locationService.getAddressFromCoordinates(
          latlong2.LatLng(position.latitude, position.longitude),
        );

        if (address != null && address.isNotEmpty) {
          // Extract city from the address string
          final addressParts = address.split(',');
          String city = 'Unknown';

          if (addressParts.length > 1) {
            // Try to get the city part (usually the second part after street)
            city = addressParts[1].trim();
          }

          setState(() {
            _currentLocation = city;
          });

          // Update the panchangam settings with the detected location
          await ref
              .read(panchangamNotificationSettingsProvider.notifier)
              .updateLocation(city);
        }
      }
    } catch (e) {
      debugPrint('Error getting location: $e');
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(panchangamNotificationSettingsProvider);
    final theme = Theme.of(context);

    return PopScope(
        canPop: true,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop && context.mounted) {
            Navigator.of(context).pop();
          }
        },
        child: Scaffold(
          appBar: AppBar(
            title: const Text('Calendar Settings'),
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: Colors.white,
          ),
          body: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Location Settings',
                        style: theme.textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      ListTile(
                        title: const Text('Current Location'),
                        subtitle: Text(_isLoadingLocation
                            ? 'Detecting location...'
                            : _currentLocation),
                        leading: const Icon(Icons.location_on),
                        trailing: _isLoadingLocation
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2))
                            : IconButton(
                                icon: const Icon(Icons.refresh),
                                onPressed: _loadCurrentLocation,
                              ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Your location is used to provide accurate panchangam information and local events.',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notification Settings',
                        style: theme.textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      SwitchListTile(
                        title: const Text('Daily Panchangam Notifications'),
                        subtitle: const Text(
                            'Receive daily updates about tithi, nakshatra, and festivals'),
                        value: settings.isEnabled,
                        onChanged: (value) async {
                          await ref
                              .read(panchangamNotificationSettingsProvider
                                  .notifier)
                              .toggleEnabled();
                        },
                      ),
                      const Divider(),
                      ListTile(
                        title: const Text('Notification Time'),
                        subtitle: Text(
                          TimeOfDay(
                            hour: settings.notificationHour,
                            minute: settings.notificationMinute,
                          ).format(context),
                        ),
                        leading: const Icon(Icons.access_time),
                        onTap: () async {
                          final TimeOfDay? picked = await showTimePicker(
                            context: context,
                            initialTime: TimeOfDay(
                              hour: settings.notificationHour,
                              minute: settings.notificationMinute,
                            ),
                          );
                          if (picked != null) {
                            await ref
                                .read(panchangamNotificationSettingsProvider
                                    .notifier)
                                .updateNotificationTime(
                                    picked.hour, picked.minute);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Display Settings',
                        style: theme.textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      SwitchListTile(
                        title: const Text('Show Panchangam Information'),
                        subtitle: const Text(
                            'Display tithi, nakshatra, and yoga on calendar'),
                        value:
                            true, // This would be connected to a provider in a real implementation
                        onChanged: (value) {
                          // Implement this functionality
                        },
                      ),
                      const Divider(),
                      SwitchListTile(
                        title: const Text('Show Week Numbers'),
                        subtitle:
                            const Text('Display week numbers in calendar view'),
                        value:
                            false, // This would be connected to a provider in a real implementation
                        onChanged: (value) {
                          // Implement this functionality
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}
