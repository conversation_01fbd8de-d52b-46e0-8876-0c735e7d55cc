import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/config/security_config_model.dart';
import '../../../shared/providers/system_config_provider.dart';

class SecurityConfigSection extends ConsumerWidget {
  final SecurityConfigModel config;

  const SecurityConfigSection({
    super.key,
    required this.config,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Security Configuration',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              context,
              ref,
              'Session Timeout (minutes)',
              config.sessionTimeoutMinutes,
              (value) => _updateConfig(
                ref,
                config.copyWith(sessionTimeoutMinutes: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              context,
              ref,
              'Max Login Attempts',
              config.maxLoginAttempts,
              (value) => _updateConfig(
                ref,
                config.copyWith(maxLoginAttempts: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              context,
              ref,
              'Password Min Length',
              config.passwordMinLength,
              (value) => _updateConfig(
                ref,
                config.copyWith(passwordMinLength: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              context,
              ref,
              'Password Expiry (days)',
              config.passwordExpiryDays,
              (value) => _updateConfig(
                ref,
                config.copyWith(passwordExpiryDays: value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNumberField(
    BuildContext context,
    WidgetRef ref,
    String label,
    int value,
    Function(int) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        SizedBox(
          width: 100,
          child: TextFormField(
            initialValue: value.toString(),
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              final number = int.tryParse(value);
              if (number != null) {
                onChanged(number);
              }
            },
          ),
        ),
      ],
    );
  }

  Future<void> _updateConfig(
    WidgetRef ref,
    SecurityConfigModel newConfig,
  ) async {
    await ref
        .read(systemConfigStateProvider.notifier)
        .updateSecurityConfig(newConfig);
  }
}
