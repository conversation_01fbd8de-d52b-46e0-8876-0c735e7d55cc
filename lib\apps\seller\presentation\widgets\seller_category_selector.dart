import 'package:flutter/material.dart';
import 'package:shivish/apps/seller/domain/models/seller_category.dart';

class SellerCategorySelector extends StatelessWidget {
  final SellerCategory selectedCategory;
  final ValueChanged<SellerCategory> onCategorySelected;

  const SellerCategorySelector({
    super.key,
    required this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Business Category',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<SellerCategory>(
          value: selectedCategory,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          items: SellerCategory.values.map((category) {
            return DropdownMenuItem(
              value: category,
              child: Text(category.displayName),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              onCategorySelected(value);
            }
          },
        ),
      ],
    );
  }
}
