import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';
import '../../providers/shopping/shopping_list_provider.dart';
import '../../../../shared/providers/auth_provider.dart';
import 'price_list_response_dialog.dart';

/// Screen for sellers to view and respond to shopping list requests
class RequestedListsScreen extends ConsumerWidget {
  /// Creates a [RequestedListsScreen]
  const RequestedListsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final user = ref.watch(currentUserProvider).value;
    final requestedListsAsync = ref.watch(sellerRequestedListsProvider);

    if (user == null) {
      return const Scaffold(
        body: Center(
          child: Text('Please sign in to view requested lists'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Requested Shopping Lists'),
      ),
      body: requestedListsAsync.when(
        data: (lists) {
          if (lists.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_cart,
                    size: 64,
                    color: theme.colorScheme.primary.withValues(alpha: 128),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No Requested Lists',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You have no pending shopping list requests',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: lists.length,
            itemBuilder: (context, index) {
              final list = lists[index];
              return _buildListItem(context, list, ref);
            },
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Text('Error loading requested lists: $error'),
        ),
      ),
    );
  }

  Widget _buildListItem(
    BuildContext context,
    ShoppingListModel list,
    WidgetRef ref,
  ) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    list.name,
                    style: theme.textTheme.titleLarge,
                  ),
                ),
                _buildStatusChip(context, list.status),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              list.description,
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${list.itemCount} items',
                  style: theme.textTheme.bodyMedium,
                ),
                Text(
                  'Requested: ${_formatDate(list.updatedAt)}',
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton(
                  onPressed: () {
                    _showListDetails(context, list);
                  },
                  child: const Text('View Details'),
                ),
                const SizedBox(width: 8),
                FilledButton(
                  onPressed: () {
                    _showPriceResponseDialog(context, list, ref);
                  },
                  child: const Text('Respond with Pricing'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, String status) {
    final theme = Theme.of(context);
    
    Color color;
    String label;
    
    switch (status) {
      case 'requested':
        color = Colors.orange;
        label = 'Requested';
        break;
      case 'priced':
        color = Colors.green;
        label = 'Priced';
        break;
      default:
        color = Colors.grey;
        label = 'Pending';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 77)),
      ),
      child: Text(
        label,
        style: theme.textTheme.bodySmall?.copyWith(color: color),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showListDetails(BuildContext context, ShoppingListModel list) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(list.name),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: list.items.length,
            itemBuilder: (context, index) {
              final item = list.items[index];
              return ListTile(
                title: Text(item.name),
                subtitle: Text('Quantity: ${item.quantity}'),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showPriceResponseDialog(
    BuildContext context,
    ShoppingListModel list,
    WidgetRef ref,
  ) {
    showDialog<bool>(
      context: context,
      builder: (context) => PriceListResponseDialog(list: list),
    ).then((result) {
      if (result == true) {
        // Refresh the list
        ref.invalidate(sellerRequestedListsProvider);
      }
    });
  }
}
