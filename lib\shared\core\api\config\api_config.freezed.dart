// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'api_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ApiConfig _$ApiConfigFromJson(Map<String, dynamic> json) {
  return _ApiConfig.fromJson(json);
}

/// @nodoc
mixin _$ApiConfig {
  String get baseUrl => throw _privateConstructorUsedError;
  int get connectTimeout => throw _privateConstructorUsedError;
  int get receiveTimeout => throw _privateConstructorUsedError;
  int get sendTimeout => throw _privateConstructorUsedError;
  int get maxRetries => throw _privateConstructorUsedError;
  bool get enableLogging => throw _privateConstructorUsedError;
  bool get enableCaching => throw _privateConstructorUsedError;
  Duration get cacheDuration => throw _privateConstructorUsedError;
  int get maxCacheSize => throw _privateConstructorUsedError; // 10MB
  Map<String, String> get defaultHeaders => throw _privateConstructorUsedError;
  ApiEnvironment get environment => throw _privateConstructorUsedError;

  /// Serializes this ApiConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ApiConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ApiConfigCopyWith<ApiConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApiConfigCopyWith<$Res> {
  factory $ApiConfigCopyWith(ApiConfig value, $Res Function(ApiConfig) then) =
      _$ApiConfigCopyWithImpl<$Res, ApiConfig>;
  @useResult
  $Res call(
      {String baseUrl,
      int connectTimeout,
      int receiveTimeout,
      int sendTimeout,
      int maxRetries,
      bool enableLogging,
      bool enableCaching,
      Duration cacheDuration,
      int maxCacheSize,
      Map<String, String> defaultHeaders,
      ApiEnvironment environment});
}

/// @nodoc
class _$ApiConfigCopyWithImpl<$Res, $Val extends ApiConfig>
    implements $ApiConfigCopyWith<$Res> {
  _$ApiConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ApiConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseUrl = null,
    Object? connectTimeout = null,
    Object? receiveTimeout = null,
    Object? sendTimeout = null,
    Object? maxRetries = null,
    Object? enableLogging = null,
    Object? enableCaching = null,
    Object? cacheDuration = null,
    Object? maxCacheSize = null,
    Object? defaultHeaders = null,
    Object? environment = null,
  }) {
    return _then(_value.copyWith(
      baseUrl: null == baseUrl
          ? _value.baseUrl
          : baseUrl // ignore: cast_nullable_to_non_nullable
              as String,
      connectTimeout: null == connectTimeout
          ? _value.connectTimeout
          : connectTimeout // ignore: cast_nullable_to_non_nullable
              as int,
      receiveTimeout: null == receiveTimeout
          ? _value.receiveTimeout
          : receiveTimeout // ignore: cast_nullable_to_non_nullable
              as int,
      sendTimeout: null == sendTimeout
          ? _value.sendTimeout
          : sendTimeout // ignore: cast_nullable_to_non_nullable
              as int,
      maxRetries: null == maxRetries
          ? _value.maxRetries
          : maxRetries // ignore: cast_nullable_to_non_nullable
              as int,
      enableLogging: null == enableLogging
          ? _value.enableLogging
          : enableLogging // ignore: cast_nullable_to_non_nullable
              as bool,
      enableCaching: null == enableCaching
          ? _value.enableCaching
          : enableCaching // ignore: cast_nullable_to_non_nullable
              as bool,
      cacheDuration: null == cacheDuration
          ? _value.cacheDuration
          : cacheDuration // ignore: cast_nullable_to_non_nullable
              as Duration,
      maxCacheSize: null == maxCacheSize
          ? _value.maxCacheSize
          : maxCacheSize // ignore: cast_nullable_to_non_nullable
              as int,
      defaultHeaders: null == defaultHeaders
          ? _value.defaultHeaders
          : defaultHeaders // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      environment: null == environment
          ? _value.environment
          : environment // ignore: cast_nullable_to_non_nullable
              as ApiEnvironment,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ApiConfigImplCopyWith<$Res>
    implements $ApiConfigCopyWith<$Res> {
  factory _$$ApiConfigImplCopyWith(
          _$ApiConfigImpl value, $Res Function(_$ApiConfigImpl) then) =
      __$$ApiConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String baseUrl,
      int connectTimeout,
      int receiveTimeout,
      int sendTimeout,
      int maxRetries,
      bool enableLogging,
      bool enableCaching,
      Duration cacheDuration,
      int maxCacheSize,
      Map<String, String> defaultHeaders,
      ApiEnvironment environment});
}

/// @nodoc
class __$$ApiConfigImplCopyWithImpl<$Res>
    extends _$ApiConfigCopyWithImpl<$Res, _$ApiConfigImpl>
    implements _$$ApiConfigImplCopyWith<$Res> {
  __$$ApiConfigImplCopyWithImpl(
      _$ApiConfigImpl _value, $Res Function(_$ApiConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseUrl = null,
    Object? connectTimeout = null,
    Object? receiveTimeout = null,
    Object? sendTimeout = null,
    Object? maxRetries = null,
    Object? enableLogging = null,
    Object? enableCaching = null,
    Object? cacheDuration = null,
    Object? maxCacheSize = null,
    Object? defaultHeaders = null,
    Object? environment = null,
  }) {
    return _then(_$ApiConfigImpl(
      baseUrl: null == baseUrl
          ? _value.baseUrl
          : baseUrl // ignore: cast_nullable_to_non_nullable
              as String,
      connectTimeout: null == connectTimeout
          ? _value.connectTimeout
          : connectTimeout // ignore: cast_nullable_to_non_nullable
              as int,
      receiveTimeout: null == receiveTimeout
          ? _value.receiveTimeout
          : receiveTimeout // ignore: cast_nullable_to_non_nullable
              as int,
      sendTimeout: null == sendTimeout
          ? _value.sendTimeout
          : sendTimeout // ignore: cast_nullable_to_non_nullable
              as int,
      maxRetries: null == maxRetries
          ? _value.maxRetries
          : maxRetries // ignore: cast_nullable_to_non_nullable
              as int,
      enableLogging: null == enableLogging
          ? _value.enableLogging
          : enableLogging // ignore: cast_nullable_to_non_nullable
              as bool,
      enableCaching: null == enableCaching
          ? _value.enableCaching
          : enableCaching // ignore: cast_nullable_to_non_nullable
              as bool,
      cacheDuration: null == cacheDuration
          ? _value.cacheDuration
          : cacheDuration // ignore: cast_nullable_to_non_nullable
              as Duration,
      maxCacheSize: null == maxCacheSize
          ? _value.maxCacheSize
          : maxCacheSize // ignore: cast_nullable_to_non_nullable
              as int,
      defaultHeaders: null == defaultHeaders
          ? _value._defaultHeaders
          : defaultHeaders // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      environment: null == environment
          ? _value.environment
          : environment // ignore: cast_nullable_to_non_nullable
              as ApiEnvironment,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ApiConfigImpl implements _ApiConfig {
  const _$ApiConfigImpl(
      {this.baseUrl = 'https://api.example.com',
      this.connectTimeout = 30000,
      this.receiveTimeout = 30000,
      this.sendTimeout = 30000,
      this.maxRetries = 3,
      this.enableLogging = true,
      this.enableCaching = true,
      this.cacheDuration = const Duration(hours: 1),
      this.maxCacheSize = 10 * 1024 * 1024,
      final Map<String, String> defaultHeaders = const {},
      this.environment = ApiEnvironment.development})
      : _defaultHeaders = defaultHeaders;

  factory _$ApiConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$ApiConfigImplFromJson(json);

  @override
  @JsonKey()
  final String baseUrl;
  @override
  @JsonKey()
  final int connectTimeout;
  @override
  @JsonKey()
  final int receiveTimeout;
  @override
  @JsonKey()
  final int sendTimeout;
  @override
  @JsonKey()
  final int maxRetries;
  @override
  @JsonKey()
  final bool enableLogging;
  @override
  @JsonKey()
  final bool enableCaching;
  @override
  @JsonKey()
  final Duration cacheDuration;
  @override
  @JsonKey()
  final int maxCacheSize;
// 10MB
  final Map<String, String> _defaultHeaders;
// 10MB
  @override
  @JsonKey()
  Map<String, String> get defaultHeaders {
    if (_defaultHeaders is EqualUnmodifiableMapView) return _defaultHeaders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_defaultHeaders);
  }

  @override
  @JsonKey()
  final ApiEnvironment environment;

  @override
  String toString() {
    return 'ApiConfig(baseUrl: $baseUrl, connectTimeout: $connectTimeout, receiveTimeout: $receiveTimeout, sendTimeout: $sendTimeout, maxRetries: $maxRetries, enableLogging: $enableLogging, enableCaching: $enableCaching, cacheDuration: $cacheDuration, maxCacheSize: $maxCacheSize, defaultHeaders: $defaultHeaders, environment: $environment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApiConfigImpl &&
            (identical(other.baseUrl, baseUrl) || other.baseUrl == baseUrl) &&
            (identical(other.connectTimeout, connectTimeout) ||
                other.connectTimeout == connectTimeout) &&
            (identical(other.receiveTimeout, receiveTimeout) ||
                other.receiveTimeout == receiveTimeout) &&
            (identical(other.sendTimeout, sendTimeout) ||
                other.sendTimeout == sendTimeout) &&
            (identical(other.maxRetries, maxRetries) ||
                other.maxRetries == maxRetries) &&
            (identical(other.enableLogging, enableLogging) ||
                other.enableLogging == enableLogging) &&
            (identical(other.enableCaching, enableCaching) ||
                other.enableCaching == enableCaching) &&
            (identical(other.cacheDuration, cacheDuration) ||
                other.cacheDuration == cacheDuration) &&
            (identical(other.maxCacheSize, maxCacheSize) ||
                other.maxCacheSize == maxCacheSize) &&
            const DeepCollectionEquality()
                .equals(other._defaultHeaders, _defaultHeaders) &&
            (identical(other.environment, environment) ||
                other.environment == environment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      baseUrl,
      connectTimeout,
      receiveTimeout,
      sendTimeout,
      maxRetries,
      enableLogging,
      enableCaching,
      cacheDuration,
      maxCacheSize,
      const DeepCollectionEquality().hash(_defaultHeaders),
      environment);

  /// Create a copy of ApiConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApiConfigImplCopyWith<_$ApiConfigImpl> get copyWith =>
      __$$ApiConfigImplCopyWithImpl<_$ApiConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ApiConfigImplToJson(
      this,
    );
  }
}

abstract class _ApiConfig implements ApiConfig {
  const factory _ApiConfig(
      {final String baseUrl,
      final int connectTimeout,
      final int receiveTimeout,
      final int sendTimeout,
      final int maxRetries,
      final bool enableLogging,
      final bool enableCaching,
      final Duration cacheDuration,
      final int maxCacheSize,
      final Map<String, String> defaultHeaders,
      final ApiEnvironment environment}) = _$ApiConfigImpl;

  factory _ApiConfig.fromJson(Map<String, dynamic> json) =
      _$ApiConfigImpl.fromJson;

  @override
  String get baseUrl;
  @override
  int get connectTimeout;
  @override
  int get receiveTimeout;
  @override
  int get sendTimeout;
  @override
  int get maxRetries;
  @override
  bool get enableLogging;
  @override
  bool get enableCaching;
  @override
  Duration get cacheDuration;
  @override
  int get maxCacheSize; // 10MB
  @override
  Map<String, String> get defaultHeaders;
  @override
  ApiEnvironment get environment;

  /// Create a copy of ApiConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApiConfigImplCopyWith<_$ApiConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
