// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shopping_list_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ShoppingListModel {

 String get id; String get name; String? get description; int get itemCount; double get totalPrice; bool get isShared; DateTime? get createdAt; DateTime? get updatedAt; String get createdBy; List<String> get sharedWith; List<ShoppingListItemModel> get items;
/// Create a copy of ShoppingListModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ShoppingListModelCopyWith<ShoppingListModel> get copyWith => _$ShoppingListModelCopyWithImpl<ShoppingListModel>(this as ShoppingListModel, _$identity);

  /// Serializes this ShoppingListModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ShoppingListModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.itemCount, itemCount) || other.itemCount == itemCount)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.isShared, isShared) || other.isShared == isShared)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.createdBy, createdBy) || other.createdBy == createdBy)&&const DeepCollectionEquality().equals(other.sharedWith, sharedWith)&&const DeepCollectionEquality().equals(other.items, items));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,itemCount,totalPrice,isShared,createdAt,updatedAt,createdBy,const DeepCollectionEquality().hash(sharedWith),const DeepCollectionEquality().hash(items));

@override
String toString() {
  return 'ShoppingListModel(id: $id, name: $name, description: $description, itemCount: $itemCount, totalPrice: $totalPrice, isShared: $isShared, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, sharedWith: $sharedWith, items: $items)';
}


}

/// @nodoc
abstract mixin class $ShoppingListModelCopyWith<$Res>  {
  factory $ShoppingListModelCopyWith(ShoppingListModel value, $Res Function(ShoppingListModel) _then) = _$ShoppingListModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String? description, int itemCount, double totalPrice, bool isShared, DateTime? createdAt, DateTime? updatedAt, String createdBy, List<String> sharedWith, List<ShoppingListItemModel> items
});




}
/// @nodoc
class _$ShoppingListModelCopyWithImpl<$Res>
    implements $ShoppingListModelCopyWith<$Res> {
  _$ShoppingListModelCopyWithImpl(this._self, this._then);

  final ShoppingListModel _self;
  final $Res Function(ShoppingListModel) _then;

/// Create a copy of ShoppingListModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? description = freezed,Object? itemCount = null,Object? totalPrice = null,Object? isShared = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? createdBy = null,Object? sharedWith = null,Object? items = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,itemCount: null == itemCount ? _self.itemCount : itemCount // ignore: cast_nullable_to_non_nullable
as int,totalPrice: null == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double,isShared: null == isShared ? _self.isShared : isShared // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdBy: null == createdBy ? _self.createdBy : createdBy // ignore: cast_nullable_to_non_nullable
as String,sharedWith: null == sharedWith ? _self.sharedWith : sharedWith // ignore: cast_nullable_to_non_nullable
as List<String>,items: null == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<ShoppingListItemModel>,
  ));
}

}


/// Adds pattern-matching-related methods to [ShoppingListModel].
extension ShoppingListModelPatterns on ShoppingListModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ShoppingListModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ShoppingListModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ShoppingListModel value)  $default,){
final _that = this;
switch (_that) {
case _ShoppingListModel():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ShoppingListModel value)?  $default,){
final _that = this;
switch (_that) {
case _ShoppingListModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String? description,  int itemCount,  double totalPrice,  bool isShared,  DateTime? createdAt,  DateTime? updatedAt,  String createdBy,  List<String> sharedWith,  List<ShoppingListItemModel> items)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ShoppingListModel() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.itemCount,_that.totalPrice,_that.isShared,_that.createdAt,_that.updatedAt,_that.createdBy,_that.sharedWith,_that.items);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String? description,  int itemCount,  double totalPrice,  bool isShared,  DateTime? createdAt,  DateTime? updatedAt,  String createdBy,  List<String> sharedWith,  List<ShoppingListItemModel> items)  $default,) {final _that = this;
switch (_that) {
case _ShoppingListModel():
return $default(_that.id,_that.name,_that.description,_that.itemCount,_that.totalPrice,_that.isShared,_that.createdAt,_that.updatedAt,_that.createdBy,_that.sharedWith,_that.items);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String? description,  int itemCount,  double totalPrice,  bool isShared,  DateTime? createdAt,  DateTime? updatedAt,  String createdBy,  List<String> sharedWith,  List<ShoppingListItemModel> items)?  $default,) {final _that = this;
switch (_that) {
case _ShoppingListModel() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.itemCount,_that.totalPrice,_that.isShared,_that.createdAt,_that.updatedAt,_that.createdBy,_that.sharedWith,_that.items);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ShoppingListModel implements ShoppingListModel {
  const _ShoppingListModel({required this.id, required this.name, this.description, this.itemCount = 0, this.totalPrice = 0.0, this.isShared = false, this.createdAt, this.updatedAt, required this.createdBy, final  List<String> sharedWith = const [], final  List<ShoppingListItemModel> items = const []}): _sharedWith = sharedWith,_items = items;
  factory _ShoppingListModel.fromJson(Map<String, dynamic> json) => _$ShoppingListModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String? description;
@override@JsonKey() final  int itemCount;
@override@JsonKey() final  double totalPrice;
@override@JsonKey() final  bool isShared;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
@override final  String createdBy;
 final  List<String> _sharedWith;
@override@JsonKey() List<String> get sharedWith {
  if (_sharedWith is EqualUnmodifiableListView) return _sharedWith;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_sharedWith);
}

 final  List<ShoppingListItemModel> _items;
@override@JsonKey() List<ShoppingListItemModel> get items {
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_items);
}


/// Create a copy of ShoppingListModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ShoppingListModelCopyWith<_ShoppingListModel> get copyWith => __$ShoppingListModelCopyWithImpl<_ShoppingListModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ShoppingListModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ShoppingListModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.itemCount, itemCount) || other.itemCount == itemCount)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.isShared, isShared) || other.isShared == isShared)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.createdBy, createdBy) || other.createdBy == createdBy)&&const DeepCollectionEquality().equals(other._sharedWith, _sharedWith)&&const DeepCollectionEquality().equals(other._items, _items));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,itemCount,totalPrice,isShared,createdAt,updatedAt,createdBy,const DeepCollectionEquality().hash(_sharedWith),const DeepCollectionEquality().hash(_items));

@override
String toString() {
  return 'ShoppingListModel(id: $id, name: $name, description: $description, itemCount: $itemCount, totalPrice: $totalPrice, isShared: $isShared, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, sharedWith: $sharedWith, items: $items)';
}


}

/// @nodoc
abstract mixin class _$ShoppingListModelCopyWith<$Res> implements $ShoppingListModelCopyWith<$Res> {
  factory _$ShoppingListModelCopyWith(_ShoppingListModel value, $Res Function(_ShoppingListModel) _then) = __$ShoppingListModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String? description, int itemCount, double totalPrice, bool isShared, DateTime? createdAt, DateTime? updatedAt, String createdBy, List<String> sharedWith, List<ShoppingListItemModel> items
});




}
/// @nodoc
class __$ShoppingListModelCopyWithImpl<$Res>
    implements _$ShoppingListModelCopyWith<$Res> {
  __$ShoppingListModelCopyWithImpl(this._self, this._then);

  final _ShoppingListModel _self;
  final $Res Function(_ShoppingListModel) _then;

/// Create a copy of ShoppingListModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? description = freezed,Object? itemCount = null,Object? totalPrice = null,Object? isShared = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? createdBy = null,Object? sharedWith = null,Object? items = null,}) {
  return _then(_ShoppingListModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,itemCount: null == itemCount ? _self.itemCount : itemCount // ignore: cast_nullable_to_non_nullable
as int,totalPrice: null == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double,isShared: null == isShared ? _self.isShared : isShared // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdBy: null == createdBy ? _self.createdBy : createdBy // ignore: cast_nullable_to_non_nullable
as String,sharedWith: null == sharedWith ? _self._sharedWith : sharedWith // ignore: cast_nullable_to_non_nullable
as List<String>,items: null == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<ShoppingListItemModel>,
  ));
}


}


/// @nodoc
mixin _$ShoppingListItemModel {

 String get id; String get name; String? get description; int get quantity; double get price; bool get isChecked; DateTime? get createdAt; DateTime? get updatedAt;
/// Create a copy of ShoppingListItemModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ShoppingListItemModelCopyWith<ShoppingListItemModel> get copyWith => _$ShoppingListItemModelCopyWithImpl<ShoppingListItemModel>(this as ShoppingListItemModel, _$identity);

  /// Serializes this ShoppingListItemModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ShoppingListItemModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.price, price) || other.price == price)&&(identical(other.isChecked, isChecked) || other.isChecked == isChecked)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,quantity,price,isChecked,createdAt,updatedAt);

@override
String toString() {
  return 'ShoppingListItemModel(id: $id, name: $name, description: $description, quantity: $quantity, price: $price, isChecked: $isChecked, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $ShoppingListItemModelCopyWith<$Res>  {
  factory $ShoppingListItemModelCopyWith(ShoppingListItemModel value, $Res Function(ShoppingListItemModel) _then) = _$ShoppingListItemModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String? description, int quantity, double price, bool isChecked, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$ShoppingListItemModelCopyWithImpl<$Res>
    implements $ShoppingListItemModelCopyWith<$Res> {
  _$ShoppingListItemModelCopyWithImpl(this._self, this._then);

  final ShoppingListItemModel _self;
  final $Res Function(ShoppingListItemModel) _then;

/// Create a copy of ShoppingListItemModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? description = freezed,Object? quantity = null,Object? price = null,Object? isChecked = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,isChecked: null == isChecked ? _self.isChecked : isChecked // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [ShoppingListItemModel].
extension ShoppingListItemModelPatterns on ShoppingListItemModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ShoppingListItemModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ShoppingListItemModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ShoppingListItemModel value)  $default,){
final _that = this;
switch (_that) {
case _ShoppingListItemModel():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ShoppingListItemModel value)?  $default,){
final _that = this;
switch (_that) {
case _ShoppingListItemModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String? description,  int quantity,  double price,  bool isChecked,  DateTime? createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ShoppingListItemModel() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.quantity,_that.price,_that.isChecked,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String? description,  int quantity,  double price,  bool isChecked,  DateTime? createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _ShoppingListItemModel():
return $default(_that.id,_that.name,_that.description,_that.quantity,_that.price,_that.isChecked,_that.createdAt,_that.updatedAt);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String? description,  int quantity,  double price,  bool isChecked,  DateTime? createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _ShoppingListItemModel() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.quantity,_that.price,_that.isChecked,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ShoppingListItemModel implements ShoppingListItemModel {
  const _ShoppingListItemModel({required this.id, required this.name, this.description, this.quantity = 1, this.price = 0.0, this.isChecked = false, this.createdAt, this.updatedAt});
  factory _ShoppingListItemModel.fromJson(Map<String, dynamic> json) => _$ShoppingListItemModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String? description;
@override@JsonKey() final  int quantity;
@override@JsonKey() final  double price;
@override@JsonKey() final  bool isChecked;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of ShoppingListItemModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ShoppingListItemModelCopyWith<_ShoppingListItemModel> get copyWith => __$ShoppingListItemModelCopyWithImpl<_ShoppingListItemModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ShoppingListItemModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ShoppingListItemModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.price, price) || other.price == price)&&(identical(other.isChecked, isChecked) || other.isChecked == isChecked)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,quantity,price,isChecked,createdAt,updatedAt);

@override
String toString() {
  return 'ShoppingListItemModel(id: $id, name: $name, description: $description, quantity: $quantity, price: $price, isChecked: $isChecked, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$ShoppingListItemModelCopyWith<$Res> implements $ShoppingListItemModelCopyWith<$Res> {
  factory _$ShoppingListItemModelCopyWith(_ShoppingListItemModel value, $Res Function(_ShoppingListItemModel) _then) = __$ShoppingListItemModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String? description, int quantity, double price, bool isChecked, DateTime? createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$ShoppingListItemModelCopyWithImpl<$Res>
    implements _$ShoppingListItemModelCopyWith<$Res> {
  __$ShoppingListItemModelCopyWithImpl(this._self, this._then);

  final _ShoppingListItemModel _self;
  final $Res Function(_ShoppingListItemModel) _then;

/// Create a copy of ShoppingListItemModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? description = freezed,Object? quantity = null,Object? price = null,Object? isChecked = null,Object? createdAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_ShoppingListItemModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,isChecked: null == isChecked ? _self.isChecked : isChecked // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
