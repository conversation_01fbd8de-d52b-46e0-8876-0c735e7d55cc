import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:latlong2/latlong.dart' as latlong2;
import '../../../../shared/models/calendar/calendar_event_model.dart';
import '../../../../shared/models/calendar/calendar_event_type.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/location_provider.dart';
import '../../../../shared/ui_components/navigation/bottom_nav_back_handler.dart';
import '../../../../shared/services/location/location_service.dart';
import '../../providers/calendar/calendar_event_provider.dart';
import '../../providers/calendar/admin_product_list_provider.dart';
import '../../widgets/calendar/calendar_event_list.dart';
import '../../widgets/calendar/calendar_event_card.dart';
import '../../widgets/calendar/create_event_dialog.dart';
import '../../widgets/calendar/create_product_list_dialog.dart';
import '../../widgets/calendar/edit_event_dialog.dart';

import '../../buyer_routes.dart';
import '../../../../../shared/ui_components/calendar/panchangam_widget.dart';
import 'panchangam_notification_settings_screen.dart';
import 'calendar_settings_screen.dart';
import 'filter_events_dialog.dart';

/// Screen for displaying and managing calendar events
class CalendarScreen extends ConsumerStatefulWidget {
  /// Creates a [CalendarScreen]
  const CalendarScreen({super.key});

  @override
  ConsumerState<CalendarScreen> createState() => _CalendarScreenState();
}

class _CalendarScreenState extends ConsumerState<CalendarScreen> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _selectedDate = DateTime.now();
  final String _selectedLanguage = 'en';
  final String _selectedLocation = 'New Delhi';

  // View mode state
  bool _isListView = false; // Default to calendar view
  CalendarEventType? _selectedEventType; // For filtering events in list view

  final LocationService _locationService = LocationService();

  @override
  void initState() {
    super.initState();
    _selectedDay = _focusedDay;
    _detectCurrentLocation();
  }

  /// Automatically detect current location
  Future<void> _detectCurrentLocation() async {
    try {
      final position = await _locationService.getCurrentLocation();
      if (position != null) {
        // Get city name from coordinates
        final latLng = latlong2.LatLng(position.latitude, position.longitude);
        final address =
            await _locationService.getAddressFromCoordinates(latLng);

        if (address != null && address.isNotEmpty) {
          // Extract city from address string
          final addressParts = address.split(',');
          String city = 'Unknown';
          if (addressParts.length > 1) {
            city = addressParts[1].trim();
          }

          // Update the location provider
          if (mounted) {
            ref.read(userLocationProvider.notifier).state = UserLocation(
              city: city,
              area: 'All Areas',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error detecting location: $e');
    }
  }

  /// Builds a Panchangam info item with title, value and icon
  Widget _buildPanchangamInfoItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 14, color: theme.colorScheme.primary),
            const SizedBox(width: 4),
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Future<void> _showCreateEventDialog() async {
    final user = ref.read(currentUserProvider).value;
    if (user == null) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const CreateEventDialog(),
    );

    if (result == true) {
      debugPrint(
          'Calendar Screen: Event created successfully, refreshing providers');

      // Refresh all events providers to ensure UI updates
      ref.invalidate(userCalendarEventsProvider(user.id));

      // Refresh events for the selected day
      if (_selectedDay != null) {
        final startOfDay = DateTime(
            _selectedDay!.year, _selectedDay!.month, _selectedDay!.day);
        final endOfDay = DateTime(_selectedDay!.year, _selectedDay!.month,
            _selectedDay!.day, 23, 59, 59);

        debugPrint(
            'Calendar Screen: Refreshing events for date range: $startOfDay to $endOfDay');
        debugPrint('Calendar Screen: User ID: ${user.id}');

        // First invalidate the provider
        ref.invalidate(userCalendarEventsByDateRangeProvider(
            (user.id, startOfDay, endOfDay)));

        // Then force a read to trigger the query
        ref.read(userCalendarEventsByDateRangeProvider(
            (user.id, startOfDay, endOfDay)).future);
      }

      // Refresh all event types
      for (final type in CalendarEventType.values) {
        debugPrint(
            'Calendar Screen: Refreshing events for type: ${type.toString()}');
        ref.invalidate(userCalendarEventsByTypeProvider((user.id, type)));
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Event created successfully'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      // Force UI update with a small delay to ensure hybrid database has time to update
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          debugPrint('Calendar Screen: Forcing UI update after event creation');
          setState(() {});
        }
      });
    }
  }

  Future<void> _showCreateProductListDialog() async {
    final user = ref.read(currentUserProvider).value;
    if (user == null) return;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => CreateProductListDialog(selectedDate: _selectedDay),
    );

    if (result == true) {
      debugPrint(
          'Calendar Screen: Product list created successfully, refreshing providers');

      // Refresh all events providers to ensure UI updates
      ref.invalidate(userCalendarEventsProvider(user.id));

      // Refresh events for the selected day
      if (_selectedDay != null) {
        final startOfDay = DateTime(
            _selectedDay!.year, _selectedDay!.month, _selectedDay!.day);
        final endOfDay = DateTime(_selectedDay!.year, _selectedDay!.month,
            _selectedDay!.day, 23, 59, 59);

        debugPrint(
            'Calendar Screen: Refreshing events for date range: $startOfDay to $endOfDay');
        debugPrint('Calendar Screen: User ID: ${user.id}');

        // First invalidate the provider
        ref.invalidate(userCalendarEventsByDateRangeProvider(
            (user.id, startOfDay, endOfDay)));

        // Then force a read to trigger the query
        ref.read(userCalendarEventsByDateRangeProvider(
            (user.id, startOfDay, endOfDay)).future);
      }

      // Refresh product list type specifically
      debugPrint(
          'Calendar Screen: Refreshing events for type: ${CalendarEventType.productList.toString()}');
      ref.invalidate(userCalendarEventsByTypeProvider(
          (user.id, CalendarEventType.productList)));

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Product list created successfully'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      // Force UI update with a small delay to ensure hybrid database has time to update
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          debugPrint(
              'Calendar Screen: Forcing UI update after product list creation');
          setState(() {});
        }
      });
    }
  }

  /// Builds a list view of all events
  Widget _buildListView(dynamic user) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Filter options
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Card(
            elevation: 2,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              child: Row(
                children: [
                  Icon(Icons.filter_list, color: theme.colorScheme.primary),
                  const SizedBox(width: 8),
                  Text(
                    'Filter Events',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  ElevatedButton.icon(
                    onPressed: () async {
                      final result = await showDialog<CalendarEventType?>(
                        context: context,
                        builder: (context) => FilterEventsDialog(
                          selectedEventType: _selectedEventType,
                          onFilterSelected: (type) {},
                        ),
                      );

                      debugPrint('Filter dialog result: $result');

                      if (mounted) {
                        // Update the filter (null is valid for 'All Events')
                        setState(() {
                          _selectedEventType = result;
                        });

                        // Refresh the events list with the new filter
                        final user = ref.read(currentUserProvider).value;
                        if (user != null) {
                          debugPrint(
                              'User ID: ${user.id}, Selected type: $_selectedEventType');
                          if (_selectedEventType != null) {
                            ref.invalidate(userCalendarEventsByTypeProvider(
                                (user.id, _selectedEventType!)));
                          } else {
                            ref.invalidate(
                                userCalendarEventsProvider(user.id));
                          }
                        }
                      }
                    },
                    icon: const Icon(Icons.filter_list),
                    label: Text(_selectedEventType == null
                        ? 'All Events'
                        : _selectedEventType!.displayName),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondary,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Events list
        Expanded(
          child: Consumer(
            builder: (context, ref, child) {
              debugPrint(
                  'Consumer rebuilding, selected type: $_selectedEventType');
              // Use filtered events if a type is selected
              final eventsAsync = _selectedEventType != null
                  ? ref.watch(userCalendarEventsByTypeProvider(
                      (user.id, _selectedEventType!)))
                  : ref.watch(userCalendarEventsProvider(user.id));

              debugPrint('Events async state: ${eventsAsync.toString()}');

              return eventsAsync.when(
                data: (events) {
                  if (events.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.event_busy,
                            size: 64,
                            color: theme.colorScheme.primary
                                .withValues(alpha: 128),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No events found',
                            style: theme.textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton.icon(
                            onPressed: _showCreateEventDialog,
                            icon: const Icon(Icons.add),
                            label: const Text('Create Event'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: theme.colorScheme.primary,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  // Group events by date
                  final groupedEvents = <DateTime, List<CalendarEventModel>>{};
                  for (final event in events) {
                    final date = DateTime(
                      event.startDate.year,
                      event.startDate.month,
                      event.startDate.day,
                    );

                    if (!groupedEvents.containsKey(date)) {
                      groupedEvents[date] = [];
                    }

                    groupedEvents[date]!.add(event);
                  }

                  // Sort dates in descending order (newest first)
                  final sortedDates = groupedEvents.keys.toList()
                    ..sort((a, b) => b.compareTo(a));

                  return ListView.builder(
                    itemCount: sortedDates.length,
                    itemBuilder: (context, index) {
                      final date = sortedDates[index];
                      final dateEvents = groupedEvents[date]!;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date header
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16.0, vertical: 8.0),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.primary,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    '${date.day}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _getMonthName(date.month),
                                      style: TextStyle(
                                        color: theme.colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      '${date.year}',
                                      style: theme.textTheme.bodySmall,
                                    ),
                                  ],
                                ),
                                const Spacer(),
                                Text(
                                  '${dateEvents.length} ${dateEvents.length == 1 ? 'event' : 'events'}',
                                  style: theme.textTheme.bodySmall,
                                ),
                              ],
                            ),
                          ),

                          // Events for this date
                          ...dateEvents.map((event) => Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8.0, vertical: 4.0),
                                child: CalendarEventCard(
                                  event: event,
                                  onTap: () {
                                    // Use GoRouter to navigate to event details
                                    context.go(BuyerRoutes.calendarEventDetails
                                        .replaceFirst(':id', event.id));
                                  },
                                  onEdit: () =>
                                      _handleEdit(context, ref, event),
                                  onDelete: () =>
                                      _handleDelete(context, ref, event),
                                ),
                              )),

                          const Divider(),
                        ],
                      );
                    },
                  );
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stackTrace) => Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.error_outline,
                          color: theme.colorScheme.error, size: 48),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading events',
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        error.toString(),
                        style: theme.textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () {
                          ref.invalidate(userCalendarEventsProvider(user.id));
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('Retry'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Helper method to get month name from month number
  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  /// Handle editing an event
  Future<void> _handleEdit(
      BuildContext context, WidgetRef ref, CalendarEventModel event) async {
    if (event.type == CalendarEventType.productList) {
      final result = await showDialog<bool>(
        context: context,
        builder: (context) =>
            CreateProductListDialog(selectedDate: _selectedDate),
      );

      if (result == true) {
        // Refresh the events list
        final user = ref.read(currentUserProvider).value;
        if (user != null) {
          ref.invalidate(userCalendarEventsProvider(user.id));
        }
      }
    } else {
      final result = await showDialog<bool>(
        context: context,
        builder: (context) => EditEventDialog(event: event),
      );

      if (result == true) {
        // Refresh the events list
        final user = ref.read(currentUserProvider).value;
        if (user != null) {
          ref.invalidate(userCalendarEventsProvider(user.id));
        }
      }
    }
  }

  /// Handle deleting an event
  Future<void> _handleDelete(
      BuildContext context, WidgetRef ref, CalendarEventModel event) async {
    final theme = Theme.of(context);

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Event', style: theme.textTheme.titleLarge),
        content: Text(
          'Are you sure you want to delete this event?',
          style: theme.textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Cancel',
                style: TextStyle(color: theme.colorScheme.primary)),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: theme.colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(calendarEventServiceProvider).deleteEvent(event.id);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Event deleted successfully'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
              margin: const EdgeInsets.all(8),
            ),
          );

          // Refresh the events list
          final user = ref.read(currentUserProvider).value;
          if (user != null) {
            ref.invalidate(userCalendarEventsProvider(user.id));
          }
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to delete event: $e'),
              backgroundColor: theme.colorScheme.error,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
              margin: const EdgeInsets.all(8),
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ref.watch(currentUserProvider).when(
          data: (user) {
            if (user == null) {
              return Scaffold(
                appBar: AppBar(
                  title: const Text('Calendar'),
                ),
                body: const Center(
                  child: Text('Please sign in to view your calendar'),
                ),
              );
            }

            return BottomNavBackHandler(
              fallbackRoute: BuyerRoutes.calendar,
              child: Scaffold(
                  appBar: AppBar(
                    title: const Text('Calendar',
                        style: TextStyle(color: Colors.white)),
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    iconTheme: const IconThemeData(color: Colors.white),
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () {
                        if (Navigator.of(context).canPop()) {
                          Navigator.of(context).pop();
                        } else {
                          context.go(BuyerRoutes.home);
                        }
                      },
                    ),
                    actions: [
                      // Toggle between calendar and list view
                      IconButton(
                        icon: Icon(
                          _isListView ? Icons.calendar_month : Icons.view_list,
                          color: Colors.white,
                        ),
                        tooltip: _isListView
                            ? 'Switch to Calendar View'
                            : 'Switch to List View',
                        onPressed: () {
                          setState(() {
                            _isListView = !_isListView;
                          });
                        },
                      ),
                      IconButton(
                        icon: const Icon(Icons.notifications,
                            color: Colors.white),
                        onPressed: () async {
                          await Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) =>
                                  const PanchangamNotificationSettingsScreen(),
                            ),
                          );
                          // Refresh the UI after returning
                          if (mounted) {
                            setState(() {});
                          }
                        },
                      ),
                      IconButton(
                        icon: const Icon(Icons.settings, color: Colors.white),
                        onPressed: () async {
                          await Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) =>
                                  const CalendarSettingsScreen(),
                            ),
                          );
                          // Refresh the UI after returning
                          if (mounted) {
                            setState(() {});
                          }
                        },
                      ),
                    ],
                  ),
                  body: _isListView
                      ? _buildListView(user)
                      : Column(
                          children: [
                            // Single calendar view
                            Card(
                              elevation: 4,
                              margin: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: TableCalendar<CalendarEventModel>(
                                  firstDay: DateTime.now()
                                      .subtract(const Duration(days: 365)),
                                  lastDay: DateTime.now()
                                      .add(const Duration(days: 365)),
                                  focusedDay: _focusedDay,
                                  selectedDayPredicate: (day) =>
                                      isSameDay(_selectedDay, day),
                                  calendarFormat: _calendarFormat,
                                  onFormatChanged: (format) {
                                    setState(() {
                                      _calendarFormat = format;
                                    });
                                  },
                                  onDaySelected: (selectedDay, focusedDay) {
                                    setState(() {
                                      _selectedDay = selectedDay;
                                      _focusedDay = focusedDay;
                                      _selectedDate =
                                          selectedDay; // Update selected date for Panchangam
                                    });
                                  },
                                  // Add gesture detector for double tap and long press
                                  calendarBuilders: CalendarBuilders(
                                    defaultBuilder: (context, day, focusedDay) {
                                      return GestureDetector(
                                        onDoubleTap: () {
                                          setState(() {
                                            _selectedDay = day;
                                            _focusedDay = day;
                                            _selectedDate = day;
                                          });
                                          _showCreateEventDialog();
                                        },
                                        onLongPress: () {
                                          setState(() {
                                            _selectedDay = day;
                                            _focusedDay = day;
                                            _selectedDate = day;
                                          });
                                          _showCreateEventDialog();
                                        },
                                        child: Center(
                                          child: Text(
                                            '${day.day}',
                                            style:
                                                const TextStyle(fontSize: 16),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                  headerStyle: HeaderStyle(
                                    titleCentered: true,
                                    formatButtonDecoration: BoxDecoration(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .primary
                                          .withAlpha(50),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    formatButtonTextStyle: TextStyle(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    titleTextStyle: TextStyle(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    leftChevronIcon: Icon(
                                      Icons.chevron_left,
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                    rightChevronIcon: Icon(
                                      Icons.chevron_right,
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                  calendarStyle: CalendarStyle(
                                    todayDecoration: BoxDecoration(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary
                                          .withAlpha(150),
                                      shape: BoxShape.circle,
                                    ),
                                    selectedDecoration: BoxDecoration(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      shape: BoxShape.circle,
                                    ),
                                    markerDecoration: BoxDecoration(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .tertiary,
                                      shape: BoxShape.circle,
                                    ),
                                    markersMaxCount: 3,
                                    cellMargin: const EdgeInsets.all(4),
                                    weekendTextStyle:
                                        const TextStyle(color: Colors.red),
                                  ),
                                  eventLoader: (day) {
                                    try {
                                      // Create proper date range for the day (full day)
                                      final startOfDay = DateTime(
                                          day.year, day.month, day.day);
                                      final endOfDay = DateTime(day.year,
                                          day.month, day.day, 23, 59, 59);

                                      debugPrint(
                                          'Calendar: Loading events for day: $day');
                                      debugPrint(
                                          'Calendar: Using date range: $startOfDay to $endOfDay');

                                      return ref
                                          .watch(
                                              userCalendarEventsByDateRangeProvider(
                                            (user.id, startOfDay, endOfDay),
                                          ))
                                          .when(
                                            data: (events) {
                                              debugPrint(
                                                  'Calendar: Found ${events.length} events for day $day');
                                              return events;
                                            },
                                            loading: () => [],
                                            error: (_, __) => [],
                                          );
                                    } catch (e) {
                                      debugPrint(
                                          'Calendar: Error loading events for day $day: $e');
                                      return [];
                                    }
                                  },
                                ),
                              ),
                            ),

                            // Panchangam widget in a card with limited height
                            Card(
                              elevation: 4,
                              margin: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              child: Container(
                                height: 150, // Fixed height
                                padding: const EdgeInsets.all(12.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Header with title and icon
                                    Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary
                                                .withAlpha(30),
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(
                                            Icons.calendar_today,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                            size: 20,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Panchangam',
                                          style: TextStyle(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                        const Spacer(),
                                        IconButton(
                                          icon: Icon(
                                            Icons.info_outline,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                          ),
                                          onPressed: () {
                                            // Show full Panchangam details
                                            showModalBottomSheet(
                                              context: context,
                                              isScrollControlled: true,
                                              backgroundColor: Colors.white,
                                              shape:
                                                  const RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.vertical(
                                                        top: Radius.circular(
                                                            20)),
                                              ),
                                              builder: (context) =>
                                                  DraggableScrollableSheet(
                                                expand: false,
                                                initialChildSize: 0.6,
                                                maxChildSize: 0.9,
                                                minChildSize: 0.4,
                                                builder: (context,
                                                        scrollController) =>
                                                    SingleChildScrollView(
                                                  controller: scrollController,
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            16.0),
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Center(
                                                          child: Container(
                                                            width: 40,
                                                            height: 5,
                                                            decoration:
                                                                BoxDecoration(
                                                              color: Colors
                                                                  .grey[300],
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          10),
                                                            ),
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            height: 16),
                                                        Text(
                                                          'Panchangam Details',
                                                          style:
                                                              Theme.of(context)
                                                                  .textTheme
                                                                  .headlineSmall
                                                                  ?.copyWith(
                                                                    color: Theme.of(
                                                                            context)
                                                                        .colorScheme
                                                                        .primary,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  ),
                                                        ),
                                                        const SizedBox(
                                                            height: 16),
                                                        PanchangamWidget(
                                                          date: _selectedDate,
                                                          language:
                                                              _selectedLanguage,
                                                          location:
                                                              _selectedLocation,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                    const Divider(),
                                    // Basic info
                                    Expanded(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          _buildPanchangamInfoItem(
                                            context,
                                            'Tithi',
                                            'Shukla Paksha Pratipada',
                                            Icons.brightness_4,
                                          ),
                                          _buildPanchangamInfoItem(
                                            context,
                                            'Nakshatra',
                                            'Ashwini',
                                            Icons.star,
                                          ),
                                          _buildPanchangamInfoItem(
                                            context,
                                            'Yoga',
                                            'Shubha',
                                            Icons.auto_awesome,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            // Events list with header
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12.0, vertical: 8.0),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.event_note,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Events for ${_selectedDay?.day ?? _focusedDay.day}/${_selectedDay?.month ?? _focusedDay.month}/${_selectedDay?.year ?? _focusedDay.year}',
                                    style: TextStyle(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Events list
                            Expanded(
                              child: CalendarEventList(
                                selectedDate: _selectedDay ?? _focusedDay,
                              ),
                            ),
                          ],
                        ),
                  // Always show FAB, even if no date is selected
                  floatingActionButton: Consumer(
                    builder: (context, ref, child) {
                      // Use selected day if available, otherwise use focused day
                      final dateForProductLists = _selectedDay ?? _focusedDay;

                      // Check if there are admin product lists for this date
                      final hasAdminProductLists = ref.watch(
                        adminProductListsForDateProvider(dateForProductLists),
                      );

                      return hasAdminProductLists.when(
                        data: (hasLists) {
                          // If admin has set product lists for this date, show both options
                          if (hasLists) {
                            return Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // Personal event button
                                FloatingActionButton(
                                  heroTag: 'create_event',
                                  onPressed: _showCreateEventDialog,
                                  backgroundColor:
                                      Theme.of(context).colorScheme.primary,
                                  mini: true,
                                  elevation: 4,
                                  tooltip: 'Add Personal Event',
                                  child: const Icon(Icons.event,
                                      color: Colors.white),
                                ),
                                const SizedBox(height: 8),
                                // Product list button
                                FloatingActionButton(
                                  heroTag: 'create_product_list',
                                  onPressed: _showCreateProductListDialog,
                                  backgroundColor:
                                      Theme.of(context).colorScheme.secondary,
                                  elevation: 4,
                                  tooltip: 'Create Shopping List',
                                  child: const Icon(Icons.shopping_cart,
                                      color: Colors.white),
                                ),
                              ],
                            );
                          } else {
                            // If no admin product lists, only show personal event button
                            return FloatingActionButton.extended(
                              heroTag: 'create_event',
                              onPressed: _showCreateEventDialog,
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              elevation: 4,
                              icon: const Icon(Icons.add, color: Colors.white),
                              label: const Text('Add Event',
                                  style: TextStyle(color: Colors.white)),
                            );
                          }
                        },
                        loading: () => FloatingActionButton(
                          heroTag: 'create_event',
                          onPressed: _showCreateEventDialog,
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          child: const CircularProgressIndicator(
                              color: Colors.white, strokeWidth: 2),
                        ),
                        error: (_, __) => FloatingActionButton(
                          heroTag: 'create_event',
                          onPressed: _showCreateEventDialog,
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          child: const Icon(Icons.add, color: Colors.white),
                        ),
                      );
                    },
                  )
                  // Bottom navigation bar is provided by the BuyerShell
                  ),
            );
          },
          loading: () => const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          ),
          error: (error, stackTrace) => Scaffold(
            appBar: AppBar(
              title: const Text('Calendar'),
            ),
            body: Center(
              child: Text('Error: $error'),
            ),
          ),
        );
  }
}
