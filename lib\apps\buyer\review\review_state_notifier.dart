import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/buyer/providers/review_providers.dart';
import 'package:shivish/apps/buyer/repositories/review_repository.dart';
import 'package:shivish/shared/models/review.dart';

final reviewStateNotifierProvider =
    StateNotifierProvider<ReviewStateNotifier, AsyncValue<List<Review>>>(
  (ref) => ReviewStateNotifier(ref.watch(reviewRepositoryProvider)),
);

class ReviewStateNotifier extends StateNotifier<AsyncValue<List<Review>>> {
  final ReviewRepository _repository;

  ReviewStateNotifier(this._repository) : super(const AsyncValue.loading());

  Future<void> loadProductReviews(String productId) async {
    try {
      state = const AsyncValue.loading();
      final reviews = await _repository.getProductReviews(productId);
      state = AsyncValue.data(reviews);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> loadUserReviews(String userId) async {
    try {
      state = const AsyncValue.loading();
      final reviews = await _repository.getUserReviews(userId);
      state = AsyncValue.data(reviews);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> createReview(Review review) async {
    try {
      await _repository.createReview(review);
      if (state.hasValue) {
        state = AsyncValue.data([...state.value!, review]);
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateReview(Review review) async {
    try {
      await _repository.updateReview(review);
      if (state.hasValue) {
        state = AsyncValue.data(
          state.value!.map((r) => r.id == review.id ? review : r).toList(),
        );
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> deleteReview(String reviewId) async {
    try {
      await _repository.deleteReview(reviewId);
      if (state.hasValue) {
        state = AsyncValue.data(
          state.value!.where((r) => r.id != reviewId).toList(),
        );
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> voteReview(String reviewId, bool isHelpful) async {
    try {
      await _repository.voteReview(reviewId, isHelpful);
      if (state.hasValue) {
        state = AsyncValue.data(
          state.value!.map((review) {
            if (review.id == reviewId) {
              return review.copyWith(
                helpfulVotes:
                    isHelpful ? review.helpfulVotes + 1 : review.helpfulVotes,
                notHelpfulVotes: !isHelpful
                    ? review.notHelpfulVotes + 1
                    : review.notHelpfulVotes,
              );
            }
            return review;
          }).toList(),
        );
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}
