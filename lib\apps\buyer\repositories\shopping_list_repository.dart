import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';
import '../services/shopping_list_service.dart';

final shoppingListRepositoryProvider = Provider<ShoppingListRepository>(
  (ref) => ShoppingListRepository(ref.watch(shoppingListServiceProvider)),
);

class ShoppingListRepository {
  final ShoppingListService _service;

  ShoppingListRepository(this._service);

  Stream<ShoppingListModel?> getShoppingList(String listId) {
    return _service.getShoppingList(listId);
  }

  Stream<List<ShoppingListModel>> getUserShoppingLists(String userId) {
    return _service.getUserShoppingLists(userId);
  }

  Future<void> shareShoppingList(String listId, String userId) async {
    await _service.shareShoppingList(listId, userId);
  }

  Future<void> unshareShoppingList(String listId, String userId) async {
    await _service.unshareShoppingList(listId, userId);
  }

  Future<void> updateUserAccessLevel(
    String listId,
    String userId,
    String accessLevel,
  ) async {
    await _service.updateUserAccessLevel(listId, userId, accessLevel);
  }
}
