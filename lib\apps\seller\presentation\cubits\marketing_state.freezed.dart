// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'marketing_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MarketingState {

 List<PromotionModel> get promotions; List<CampaignModel> get campaigns; bool get isLoading; String? get error;
/// Create a copy of MarketingState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MarketingStateCopyWith<MarketingState> get copyWith => _$MarketingStateCopyWithImpl<MarketingState>(this as MarketingState, _$identity);

  /// Serializes this MarketingState to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketingState&&const DeepCollectionEquality().equals(other.promotions, promotions)&&const DeepCollectionEquality().equals(other.campaigns, campaigns)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(promotions),const DeepCollectionEquality().hash(campaigns),isLoading,error);

@override
String toString() {
  return 'MarketingState(promotions: $promotions, campaigns: $campaigns, isLoading: $isLoading, error: $error)';
}


}

/// @nodoc
abstract mixin class $MarketingStateCopyWith<$Res>  {
  factory $MarketingStateCopyWith(MarketingState value, $Res Function(MarketingState) _then) = _$MarketingStateCopyWithImpl;
@useResult
$Res call({
 List<PromotionModel> promotions, List<CampaignModel> campaigns, bool isLoading, String? error
});




}
/// @nodoc
class _$MarketingStateCopyWithImpl<$Res>
    implements $MarketingStateCopyWith<$Res> {
  _$MarketingStateCopyWithImpl(this._self, this._then);

  final MarketingState _self;
  final $Res Function(MarketingState) _then;

/// Create a copy of MarketingState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? promotions = null,Object? campaigns = null,Object? isLoading = null,Object? error = freezed,}) {
  return _then(_self.copyWith(
promotions: null == promotions ? _self.promotions : promotions // ignore: cast_nullable_to_non_nullable
as List<PromotionModel>,campaigns: null == campaigns ? _self.campaigns : campaigns // ignore: cast_nullable_to_non_nullable
as List<CampaignModel>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [MarketingState].
extension MarketingStatePatterns on MarketingState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MarketingState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MarketingState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MarketingState value)  $default,){
final _that = this;
switch (_that) {
case _MarketingState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MarketingState value)?  $default,){
final _that = this;
switch (_that) {
case _MarketingState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<PromotionModel> promotions,  List<CampaignModel> campaigns,  bool isLoading,  String? error)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MarketingState() when $default != null:
return $default(_that.promotions,_that.campaigns,_that.isLoading,_that.error);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<PromotionModel> promotions,  List<CampaignModel> campaigns,  bool isLoading,  String? error)  $default,) {final _that = this;
switch (_that) {
case _MarketingState():
return $default(_that.promotions,_that.campaigns,_that.isLoading,_that.error);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<PromotionModel> promotions,  List<CampaignModel> campaigns,  bool isLoading,  String? error)?  $default,) {final _that = this;
switch (_that) {
case _MarketingState() when $default != null:
return $default(_that.promotions,_that.campaigns,_that.isLoading,_that.error);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MarketingState extends MarketingState {
  const _MarketingState({final  List<PromotionModel> promotions = const [], final  List<CampaignModel> campaigns = const [], this.isLoading = false, this.error}): _promotions = promotions,_campaigns = campaigns,super._();
  factory _MarketingState.fromJson(Map<String, dynamic> json) => _$MarketingStateFromJson(json);

 final  List<PromotionModel> _promotions;
@override@JsonKey() List<PromotionModel> get promotions {
  if (_promotions is EqualUnmodifiableListView) return _promotions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_promotions);
}

 final  List<CampaignModel> _campaigns;
@override@JsonKey() List<CampaignModel> get campaigns {
  if (_campaigns is EqualUnmodifiableListView) return _campaigns;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_campaigns);
}

@override@JsonKey() final  bool isLoading;
@override final  String? error;

/// Create a copy of MarketingState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MarketingStateCopyWith<_MarketingState> get copyWith => __$MarketingStateCopyWithImpl<_MarketingState>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MarketingStateToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MarketingState&&const DeepCollectionEquality().equals(other._promotions, _promotions)&&const DeepCollectionEquality().equals(other._campaigns, _campaigns)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_promotions),const DeepCollectionEquality().hash(_campaigns),isLoading,error);

@override
String toString() {
  return 'MarketingState(promotions: $promotions, campaigns: $campaigns, isLoading: $isLoading, error: $error)';
}


}

/// @nodoc
abstract mixin class _$MarketingStateCopyWith<$Res> implements $MarketingStateCopyWith<$Res> {
  factory _$MarketingStateCopyWith(_MarketingState value, $Res Function(_MarketingState) _then) = __$MarketingStateCopyWithImpl;
@override @useResult
$Res call({
 List<PromotionModel> promotions, List<CampaignModel> campaigns, bool isLoading, String? error
});




}
/// @nodoc
class __$MarketingStateCopyWithImpl<$Res>
    implements _$MarketingStateCopyWith<$Res> {
  __$MarketingStateCopyWithImpl(this._self, this._then);

  final _MarketingState _self;
  final $Res Function(_MarketingState) _then;

/// Create a copy of MarketingState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? promotions = null,Object? campaigns = null,Object? isLoading = null,Object? error = freezed,}) {
  return _then(_MarketingState(
promotions: null == promotions ? _self._promotions : promotions // ignore: cast_nullable_to_non_nullable
as List<PromotionModel>,campaigns: null == campaigns ? _self._campaigns : campaigns // ignore: cast_nullable_to_non_nullable
as List<CampaignModel>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
