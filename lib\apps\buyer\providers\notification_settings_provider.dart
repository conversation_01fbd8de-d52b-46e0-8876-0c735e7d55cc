import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../shared/models/notification/notification_settings_model.dart';
import '../../../shared/models/notification/notification_type.dart';
import '../../../shared/services/notification/notification_service.dart';
import '../../../shared/providers/auth_provider.dart';

/// Provider for notification settings
final notificationSettingsProvider = StateNotifierProvider<
    NotificationSettingsNotifier, AsyncValue<NotificationSettingsModel>>(
  (ref) => NotificationSettingsNotifier(
    ref.watch(notificationServiceProvider),
    ref.watch(userIdProvider),
  ),
);

/// Notifier class for managing notification settings state
class NotificationSettingsNotifier
    extends StateNotifier<AsyncValue<NotificationSettingsModel>> {
  final NotificationService _notificationService;
  final String _userId;

  NotificationSettingsNotifier(this._notificationService, this._userId)
      : super(const AsyncValue.loading()) {
    _loadSettings();
  }

  /// Load notification settings for the current user
  Future<void> _loadSettings() async {
    try {
      state = const AsyncValue.loading();
      final settings =
          await _notificationService.getNotificationSettings(_userId);
      state = AsyncValue.data(settings);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Update push notification settings
  Future<void> updatePushEnabled(bool enabled) async {
    try {
      state.whenData((settings) async {
        final updatedSettings = settings.copyWith(pushEnabled: enabled);
        await _notificationService.updateNotificationSettings(updatedSettings);
        state = AsyncValue.data(updatedSettings);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Update email notification settings
  Future<void> updateEmailEnabled(bool enabled) async {
    try {
      state.whenData((settings) async {
        final updatedSettings = settings.copyWith(emailEnabled: enabled);
        await _notificationService.updateNotificationSettings(updatedSettings);
        state = AsyncValue.data(updatedSettings);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Update SMS notification settings
  Future<void> updateSmsEnabled(bool enabled) async {
    try {
      state.whenData((settings) async {
        final updatedSettings = settings.copyWith(smsEnabled: enabled);
        await _notificationService.updateNotificationSettings(updatedSettings);
        state = AsyncValue.data(updatedSettings);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Update in-app notification settings
  Future<void> updateInAppEnabled(bool enabled) async {
    try {
      state.whenData((settings) async {
        final updatedSettings = settings.copyWith(inAppEnabled: enabled);
        await _notificationService.updateNotificationSettings(updatedSettings);
        state = AsyncValue.data(updatedSettings);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Update notification type settings
  Future<void> updateTypeSettings(
      Map<NotificationType, bool> typeSettings) async {
    try {
      state.whenData((settings) async {
        final updatedSettings = settings.copyWith(typeSettings: typeSettings);
        await _notificationService.updateNotificationSettings(updatedSettings);
        state = AsyncValue.data(updatedSettings);
      });
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Reset notification settings to default values
  Future<void> resetSettings() async {
    try {
      final defaultSettings = NotificationSettingsModel.empty().copyWith(
        id: state.value?.id ?? '',
        userId: _userId,
      );
      await _notificationService.updateNotificationSettings(defaultSettings);
      state = AsyncValue.data(defaultSettings);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
