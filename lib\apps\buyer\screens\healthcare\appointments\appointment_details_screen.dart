import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:shivish/apps/buyer/buyer_routes.dart';

import 'package:shivish/shared/models/hospital/appointment_model.dart';
import 'package:shivish/shared/providers/hospital/appointment_provider.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';


class DoctorAppointmentScreen extends ConsumerWidget {
  final String appointmentId;

  const DoctorAppointmentScreen({
    super.key,
    required this.appointmentId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appointmentAsync = ref.watch(appointmentProvider(appointmentId));

    return Scaffold(
      appBar: AppBar(
          title: const Text('Appointment Details'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go(BuyerRoutes.healthcareAppointments),
          ),
        ),
        body: appointmentAsync.when(
          data: (appointment) {
            if (appointment == null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Appointment not found',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'The appointment you are looking for does not exist or has been deleted.',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () => context.go(BuyerRoutes.healthcareAppointments),
                      child: const Text('Go to My Appointments'),
                    ),
                  ],
                ),
              );
            }

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildAppointmentStatusCard(context, appointment),
                  const SizedBox(height: 24),
                  _buildDoctorInfoCard(context, appointment),
                  const SizedBox(height: 24),
                  _buildAppointmentDetailsCard(context, appointment),
                  const SizedBox(height: 24),
                  _buildSymptomsCard(context, appointment),
                  const SizedBox(height: 24),
                  _buildActionButtons(context, ref, appointment),
                ],
              ),
            );
          },
          loading: () => const Center(child: LoadingIndicator()),
          error: (error, stackTrace) => ErrorMessage(
            message: 'Failed to load appointment details: $error',
            onRetry: () => ref.refresh(appointmentProvider(appointmentId)),
          ),
        ),
      );
  }

  Widget _buildAppointmentStatusCard(BuildContext context, AppointmentModel appointment) {
    final statusColor = _getStatusColor(appointment.status);
    final statusText = _getStatusText(appointment.status);

    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: statusColor.withAlpha(25), // 0.1 opacity
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getStatusIcon(appointment.status),
                color: statusColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statusText,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getStatusDescription(appointment.status),
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDoctorInfoCard(BuildContext context, AppointmentModel appointment) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Doctor Information',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.blue.shade100,
                  child: const Icon(
                    Icons.person,
                    color: Colors.blue,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Dr. ${appointment.doctorName}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        appointment.departmentName,
                        style: TextStyle(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        appointment.hospitalName,
                        style: TextStyle(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentDetailsCard(BuildContext context, AppointmentModel appointment) {
    final formattedDate = DateFormat('EEEE, MMMM d, yyyy').format(appointment.appointmentDate);

    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Appointment Details',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
              Icons.confirmation_number,
              'Appointment ID',
              '#${appointment.appointmentNumber}',
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              Icons.calendar_today,
              'Date',
              formattedDate,
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              Icons.access_time,
              'Time',
              '${appointment.startTime} - ${appointment.endTime}',
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              Icons.medical_services,
              'Type',
              _getAppointmentTypeText(appointment.type),
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              Icons.currency_rupee,
              'Consultation Fee',
              '₹${appointment.consultationFee.toStringAsFixed(2)}',
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              Icons.payment,
              'Payment Status',
              appointment.isPaid ? 'Paid' : 'Pending',
              valueColor: appointment.isPaid ? Colors.green : Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    IconData icon,
    String label,
    String value, {
    Color? valueColor,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.blue,
        ),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: const TextStyle(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: valueColor,
              fontWeight: valueColor != null ? FontWeight.bold : null,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Widget _buildSymptomsCard(BuildContext context, AppointmentModel appointment) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Symptoms',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              appointment.symptoms.isNotEmpty
                  ? appointment.symptoms
                  : 'No symptoms provided',
              style: TextStyle(
                color: appointment.symptoms.isNotEmpty
                    ? Colors.black87
                    : Colors.grey,
                fontStyle: appointment.symptoms.isNotEmpty
                    ? FontStyle.normal
                    : FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    WidgetRef ref,
    AppointmentModel appointment,
  ) {
    return Column(
      children: [
        if (appointment.status == AppointmentStatus.pending ||
            appointment.status == AppointmentStatus.confirmed) ...[
          ElevatedButton.icon(
            onPressed: () => _showCancelDialog(context, ref, appointment),
            icon: const Icon(Icons.cancel),
            label: const Text('Cancel Appointment'),
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.red,
              side: const BorderSide(color: Colors.red),
              backgroundColor: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
        ],
        if (!appointment.isPaid) ...[
          ElevatedButton.icon(
            onPressed: () => _navigateToPayment(context, appointment),
            icon: const Icon(Icons.payment),
            label: const Text('Pay Now'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
        ],
        if (appointment.status == AppointmentStatus.completed) ...[
          ElevatedButton.icon(
            onPressed: () => _navigateToReview(context, appointment),
            icon: const Icon(Icons.rate_review),
            label: const Text('Write a Review'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
        ],
        OutlinedButton.icon(
          onPressed: () => context.go(BuyerRoutes.healthcareDoctors),
          icon: const Icon(Icons.add),
          label: const Text('Book Another Appointment'),
        ),
      ],
    );
  }

  void _showCancelDialog(
    BuildContext context,
    WidgetRef ref,
    AppointmentModel appointment,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Appointment'),
        content: const Text(
          'Are you sure you want to cancel this appointment? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No, Keep It'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _cancelAppointment(context, ref, appointment);
            },
            child: const Text(
              'Yes, Cancel',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelAppointment(
    BuildContext context,
    WidgetRef ref,
    AppointmentModel appointment,
  ) async {
    try {
      // Update appointment status to cancelled
      final updatedAppointment = appointment.copyWith(
        status: AppointmentStatus.cancelled,
      );

      // Save to hybrid database
      await ref.read(appointmentRepositoryProvider).updateAppointment(
            updatedAppointment,
          );

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment cancelled successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh appointment data
        final _ = ref.refresh(appointmentProvider(appointmentId));
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to cancel appointment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _navigateToPayment(BuildContext context, AppointmentModel appointment) {
    // Navigate to payment screen
    context.push('/buyer/healthcare/payment/${appointment.id}');
  }

  void _navigateToReview(BuildContext context, AppointmentModel appointment) {
    // Navigate to review screen
    context.push('/buyer/healthcare/review/${appointment.doctorId}?appointmentId=${appointment.id}');
  }

  Color _getStatusColor(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return Colors.orange;
      case AppointmentStatus.confirmed:
        return Colors.green;
      case AppointmentStatus.completed:
        return Colors.blue;
      case AppointmentStatus.cancelled:
        return Colors.red;
      case AppointmentStatus.noShow:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return 'Pending';
      case AppointmentStatus.confirmed:
        return 'Confirmed';
      case AppointmentStatus.completed:
        return 'Completed';
      case AppointmentStatus.cancelled:
        return 'Cancelled';
      case AppointmentStatus.noShow:
        return 'No Show';
      default:
        return 'Unknown';
    }
  }

  IconData _getStatusIcon(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return Icons.pending_actions;
      case AppointmentStatus.confirmed:
        return Icons.check_circle;
      case AppointmentStatus.completed:
        return Icons.task_alt;
      case AppointmentStatus.cancelled:
        return Icons.cancel;
      case AppointmentStatus.noShow:
        return Icons.person_off;
      default:
        return Icons.help;
    }
  }

  String _getStatusDescription(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return 'Your appointment is waiting for confirmation';
      case AppointmentStatus.confirmed:
        return 'Your appointment has been confirmed';
      case AppointmentStatus.completed:
        return 'Your appointment has been completed';
      case AppointmentStatus.cancelled:
        return 'Your appointment has been cancelled';
      case AppointmentStatus.noShow:
        return 'You did not attend this appointment';
      default:
        return '';
    }
  }

  String _getAppointmentTypeText(AppointmentType type) {
    switch (type) {
      case AppointmentType.consultation:
        return 'Consultation';
      case AppointmentType.followUp:
        return 'Follow-up';
      case AppointmentType.routineCheckup:
        return 'Routine Checkup';
      default:
        return 'Consultation';
    }
  }
}
