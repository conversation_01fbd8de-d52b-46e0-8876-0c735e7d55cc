// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$SellerEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SellerEvent()';
}


}

/// @nodoc
class $SellerEventCopyWith<$Res>  {
$SellerEventCopyWith(SellerEvent _, $Res Function(SellerEvent) __);
}


/// Adds pattern-matching-related methods to [SellerEvent].
extension SellerEventPatterns on SellerEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( LoadSellers value)?  loadSellers,TResult Function( LoadMoreSellers value)?  loadMoreSellers,TResult Function( UpdateSellerStatus value)?  updateSellerStatus,TResult Function( UpdateSellerPerformance value)?  updateSellerPerformance,TResult Function( DeleteSeller value)?  deleteSeller,TResult Function( CreateSeller value)?  createSeller,TResult Function( StartRealtimeUpdates value)?  startRealtimeUpdates,TResult Function( StopRealtimeUpdates value)?  stopRealtimeUpdates,required TResult orElse(),}){
final _that = this;
switch (_that) {
case LoadSellers() when loadSellers != null:
return loadSellers(_that);case LoadMoreSellers() when loadMoreSellers != null:
return loadMoreSellers(_that);case UpdateSellerStatus() when updateSellerStatus != null:
return updateSellerStatus(_that);case UpdateSellerPerformance() when updateSellerPerformance != null:
return updateSellerPerformance(_that);case DeleteSeller() when deleteSeller != null:
return deleteSeller(_that);case CreateSeller() when createSeller != null:
return createSeller(_that);case StartRealtimeUpdates() when startRealtimeUpdates != null:
return startRealtimeUpdates(_that);case StopRealtimeUpdates() when stopRealtimeUpdates != null:
return stopRealtimeUpdates(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( LoadSellers value)  loadSellers,required TResult Function( LoadMoreSellers value)  loadMoreSellers,required TResult Function( UpdateSellerStatus value)  updateSellerStatus,required TResult Function( UpdateSellerPerformance value)  updateSellerPerformance,required TResult Function( DeleteSeller value)  deleteSeller,required TResult Function( CreateSeller value)  createSeller,required TResult Function( StartRealtimeUpdates value)  startRealtimeUpdates,required TResult Function( StopRealtimeUpdates value)  stopRealtimeUpdates,}){
final _that = this;
switch (_that) {
case LoadSellers():
return loadSellers(_that);case LoadMoreSellers():
return loadMoreSellers(_that);case UpdateSellerStatus():
return updateSellerStatus(_that);case UpdateSellerPerformance():
return updateSellerPerformance(_that);case DeleteSeller():
return deleteSeller(_that);case CreateSeller():
return createSeller(_that);case StartRealtimeUpdates():
return startRealtimeUpdates(_that);case StopRealtimeUpdates():
return stopRealtimeUpdates(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( LoadSellers value)?  loadSellers,TResult? Function( LoadMoreSellers value)?  loadMoreSellers,TResult? Function( UpdateSellerStatus value)?  updateSellerStatus,TResult? Function( UpdateSellerPerformance value)?  updateSellerPerformance,TResult? Function( DeleteSeller value)?  deleteSeller,TResult? Function( CreateSeller value)?  createSeller,TResult? Function( StartRealtimeUpdates value)?  startRealtimeUpdates,TResult? Function( StopRealtimeUpdates value)?  stopRealtimeUpdates,}){
final _that = this;
switch (_that) {
case LoadSellers() when loadSellers != null:
return loadSellers(_that);case LoadMoreSellers() when loadMoreSellers != null:
return loadMoreSellers(_that);case UpdateSellerStatus() when updateSellerStatus != null:
return updateSellerStatus(_that);case UpdateSellerPerformance() when updateSellerPerformance != null:
return updateSellerPerformance(_that);case DeleteSeller() when deleteSeller != null:
return deleteSeller(_that);case CreateSeller() when createSeller != null:
return createSeller(_that);case StartRealtimeUpdates() when startRealtimeUpdates != null:
return startRealtimeUpdates(_that);case StopRealtimeUpdates() when stopRealtimeUpdates != null:
return stopRealtimeUpdates(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  loadSellers,TResult Function()?  loadMoreSellers,TResult Function( Seller seller,  bool isActive,  bool isSuspended)?  updateSellerStatus,TResult Function( Seller seller,  double rating,  int totalReviews,  int totalOrders,  int totalProducts,  double totalRevenue)?  updateSellerPerformance,TResult Function( Seller seller)?  deleteSeller,TResult Function( Seller seller)?  createSeller,TResult Function()?  startRealtimeUpdates,TResult Function()?  stopRealtimeUpdates,required TResult orElse(),}) {final _that = this;
switch (_that) {
case LoadSellers() when loadSellers != null:
return loadSellers();case LoadMoreSellers() when loadMoreSellers != null:
return loadMoreSellers();case UpdateSellerStatus() when updateSellerStatus != null:
return updateSellerStatus(_that.seller,_that.isActive,_that.isSuspended);case UpdateSellerPerformance() when updateSellerPerformance != null:
return updateSellerPerformance(_that.seller,_that.rating,_that.totalReviews,_that.totalOrders,_that.totalProducts,_that.totalRevenue);case DeleteSeller() when deleteSeller != null:
return deleteSeller(_that.seller);case CreateSeller() when createSeller != null:
return createSeller(_that.seller);case StartRealtimeUpdates() when startRealtimeUpdates != null:
return startRealtimeUpdates();case StopRealtimeUpdates() when stopRealtimeUpdates != null:
return stopRealtimeUpdates();case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  loadSellers,required TResult Function()  loadMoreSellers,required TResult Function( Seller seller,  bool isActive,  bool isSuspended)  updateSellerStatus,required TResult Function( Seller seller,  double rating,  int totalReviews,  int totalOrders,  int totalProducts,  double totalRevenue)  updateSellerPerformance,required TResult Function( Seller seller)  deleteSeller,required TResult Function( Seller seller)  createSeller,required TResult Function()  startRealtimeUpdates,required TResult Function()  stopRealtimeUpdates,}) {final _that = this;
switch (_that) {
case LoadSellers():
return loadSellers();case LoadMoreSellers():
return loadMoreSellers();case UpdateSellerStatus():
return updateSellerStatus(_that.seller,_that.isActive,_that.isSuspended);case UpdateSellerPerformance():
return updateSellerPerformance(_that.seller,_that.rating,_that.totalReviews,_that.totalOrders,_that.totalProducts,_that.totalRevenue);case DeleteSeller():
return deleteSeller(_that.seller);case CreateSeller():
return createSeller(_that.seller);case StartRealtimeUpdates():
return startRealtimeUpdates();case StopRealtimeUpdates():
return stopRealtimeUpdates();}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  loadSellers,TResult? Function()?  loadMoreSellers,TResult? Function( Seller seller,  bool isActive,  bool isSuspended)?  updateSellerStatus,TResult? Function( Seller seller,  double rating,  int totalReviews,  int totalOrders,  int totalProducts,  double totalRevenue)?  updateSellerPerformance,TResult? Function( Seller seller)?  deleteSeller,TResult? Function( Seller seller)?  createSeller,TResult? Function()?  startRealtimeUpdates,TResult? Function()?  stopRealtimeUpdates,}) {final _that = this;
switch (_that) {
case LoadSellers() when loadSellers != null:
return loadSellers();case LoadMoreSellers() when loadMoreSellers != null:
return loadMoreSellers();case UpdateSellerStatus() when updateSellerStatus != null:
return updateSellerStatus(_that.seller,_that.isActive,_that.isSuspended);case UpdateSellerPerformance() when updateSellerPerformance != null:
return updateSellerPerformance(_that.seller,_that.rating,_that.totalReviews,_that.totalOrders,_that.totalProducts,_that.totalRevenue);case DeleteSeller() when deleteSeller != null:
return deleteSeller(_that.seller);case CreateSeller() when createSeller != null:
return createSeller(_that.seller);case StartRealtimeUpdates() when startRealtimeUpdates != null:
return startRealtimeUpdates();case StopRealtimeUpdates() when stopRealtimeUpdates != null:
return stopRealtimeUpdates();case _:
  return null;

}
}

}

/// @nodoc


class LoadSellers implements SellerEvent {
  const LoadSellers();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadSellers);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SellerEvent.loadSellers()';
}


}




/// @nodoc


class LoadMoreSellers implements SellerEvent {
  const LoadMoreSellers();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadMoreSellers);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SellerEvent.loadMoreSellers()';
}


}




/// @nodoc


class UpdateSellerStatus implements SellerEvent {
  const UpdateSellerStatus({required this.seller, required this.isActive, required this.isSuspended});
  

 final  Seller seller;
 final  bool isActive;
 final  bool isSuspended;

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateSellerStatusCopyWith<UpdateSellerStatus> get copyWith => _$UpdateSellerStatusCopyWithImpl<UpdateSellerStatus>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateSellerStatus&&(identical(other.seller, seller) || other.seller == seller)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.isSuspended, isSuspended) || other.isSuspended == isSuspended));
}


@override
int get hashCode => Object.hash(runtimeType,seller,isActive,isSuspended);

@override
String toString() {
  return 'SellerEvent.updateSellerStatus(seller: $seller, isActive: $isActive, isSuspended: $isSuspended)';
}


}

/// @nodoc
abstract mixin class $UpdateSellerStatusCopyWith<$Res> implements $SellerEventCopyWith<$Res> {
  factory $UpdateSellerStatusCopyWith(UpdateSellerStatus value, $Res Function(UpdateSellerStatus) _then) = _$UpdateSellerStatusCopyWithImpl;
@useResult
$Res call({
 Seller seller, bool isActive, bool isSuspended
});


$SellerCopyWith<$Res> get seller;

}
/// @nodoc
class _$UpdateSellerStatusCopyWithImpl<$Res>
    implements $UpdateSellerStatusCopyWith<$Res> {
  _$UpdateSellerStatusCopyWithImpl(this._self, this._then);

  final UpdateSellerStatus _self;
  final $Res Function(UpdateSellerStatus) _then;

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? seller = null,Object? isActive = null,Object? isSuspended = null,}) {
  return _then(UpdateSellerStatus(
seller: null == seller ? _self.seller : seller // ignore: cast_nullable_to_non_nullable
as Seller,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,isSuspended: null == isSuspended ? _self.isSuspended : isSuspended // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SellerCopyWith<$Res> get seller {
  
  return $SellerCopyWith<$Res>(_self.seller, (value) {
    return _then(_self.copyWith(seller: value));
  });
}
}

/// @nodoc


class UpdateSellerPerformance implements SellerEvent {
  const UpdateSellerPerformance({required this.seller, required this.rating, required this.totalReviews, required this.totalOrders, required this.totalProducts, required this.totalRevenue});
  

 final  Seller seller;
 final  double rating;
 final  int totalReviews;
 final  int totalOrders;
 final  int totalProducts;
 final  double totalRevenue;

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateSellerPerformanceCopyWith<UpdateSellerPerformance> get copyWith => _$UpdateSellerPerformanceCopyWithImpl<UpdateSellerPerformance>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateSellerPerformance&&(identical(other.seller, seller) || other.seller == seller)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.totalProducts, totalProducts) || other.totalProducts == totalProducts)&&(identical(other.totalRevenue, totalRevenue) || other.totalRevenue == totalRevenue));
}


@override
int get hashCode => Object.hash(runtimeType,seller,rating,totalReviews,totalOrders,totalProducts,totalRevenue);

@override
String toString() {
  return 'SellerEvent.updateSellerPerformance(seller: $seller, rating: $rating, totalReviews: $totalReviews, totalOrders: $totalOrders, totalProducts: $totalProducts, totalRevenue: $totalRevenue)';
}


}

/// @nodoc
abstract mixin class $UpdateSellerPerformanceCopyWith<$Res> implements $SellerEventCopyWith<$Res> {
  factory $UpdateSellerPerformanceCopyWith(UpdateSellerPerformance value, $Res Function(UpdateSellerPerformance) _then) = _$UpdateSellerPerformanceCopyWithImpl;
@useResult
$Res call({
 Seller seller, double rating, int totalReviews, int totalOrders, int totalProducts, double totalRevenue
});


$SellerCopyWith<$Res> get seller;

}
/// @nodoc
class _$UpdateSellerPerformanceCopyWithImpl<$Res>
    implements $UpdateSellerPerformanceCopyWith<$Res> {
  _$UpdateSellerPerformanceCopyWithImpl(this._self, this._then);

  final UpdateSellerPerformance _self;
  final $Res Function(UpdateSellerPerformance) _then;

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? seller = null,Object? rating = null,Object? totalReviews = null,Object? totalOrders = null,Object? totalProducts = null,Object? totalRevenue = null,}) {
  return _then(UpdateSellerPerformance(
seller: null == seller ? _self.seller : seller // ignore: cast_nullable_to_non_nullable
as Seller,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,totalProducts: null == totalProducts ? _self.totalProducts : totalProducts // ignore: cast_nullable_to_non_nullable
as int,totalRevenue: null == totalRevenue ? _self.totalRevenue : totalRevenue // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SellerCopyWith<$Res> get seller {
  
  return $SellerCopyWith<$Res>(_self.seller, (value) {
    return _then(_self.copyWith(seller: value));
  });
}
}

/// @nodoc


class DeleteSeller implements SellerEvent {
  const DeleteSeller({required this.seller});
  

 final  Seller seller;

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DeleteSellerCopyWith<DeleteSeller> get copyWith => _$DeleteSellerCopyWithImpl<DeleteSeller>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DeleteSeller&&(identical(other.seller, seller) || other.seller == seller));
}


@override
int get hashCode => Object.hash(runtimeType,seller);

@override
String toString() {
  return 'SellerEvent.deleteSeller(seller: $seller)';
}


}

/// @nodoc
abstract mixin class $DeleteSellerCopyWith<$Res> implements $SellerEventCopyWith<$Res> {
  factory $DeleteSellerCopyWith(DeleteSeller value, $Res Function(DeleteSeller) _then) = _$DeleteSellerCopyWithImpl;
@useResult
$Res call({
 Seller seller
});


$SellerCopyWith<$Res> get seller;

}
/// @nodoc
class _$DeleteSellerCopyWithImpl<$Res>
    implements $DeleteSellerCopyWith<$Res> {
  _$DeleteSellerCopyWithImpl(this._self, this._then);

  final DeleteSeller _self;
  final $Res Function(DeleteSeller) _then;

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? seller = null,}) {
  return _then(DeleteSeller(
seller: null == seller ? _self.seller : seller // ignore: cast_nullable_to_non_nullable
as Seller,
  ));
}

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SellerCopyWith<$Res> get seller {
  
  return $SellerCopyWith<$Res>(_self.seller, (value) {
    return _then(_self.copyWith(seller: value));
  });
}
}

/// @nodoc


class CreateSeller implements SellerEvent {
  const CreateSeller({required this.seller});
  

 final  Seller seller;

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateSellerCopyWith<CreateSeller> get copyWith => _$CreateSellerCopyWithImpl<CreateSeller>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateSeller&&(identical(other.seller, seller) || other.seller == seller));
}


@override
int get hashCode => Object.hash(runtimeType,seller);

@override
String toString() {
  return 'SellerEvent.createSeller(seller: $seller)';
}


}

/// @nodoc
abstract mixin class $CreateSellerCopyWith<$Res> implements $SellerEventCopyWith<$Res> {
  factory $CreateSellerCopyWith(CreateSeller value, $Res Function(CreateSeller) _then) = _$CreateSellerCopyWithImpl;
@useResult
$Res call({
 Seller seller
});


$SellerCopyWith<$Res> get seller;

}
/// @nodoc
class _$CreateSellerCopyWithImpl<$Res>
    implements $CreateSellerCopyWith<$Res> {
  _$CreateSellerCopyWithImpl(this._self, this._then);

  final CreateSeller _self;
  final $Res Function(CreateSeller) _then;

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? seller = null,}) {
  return _then(CreateSeller(
seller: null == seller ? _self.seller : seller // ignore: cast_nullable_to_non_nullable
as Seller,
  ));
}

/// Create a copy of SellerEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SellerCopyWith<$Res> get seller {
  
  return $SellerCopyWith<$Res>(_self.seller, (value) {
    return _then(_self.copyWith(seller: value));
  });
}
}

/// @nodoc


class StartRealtimeUpdates implements SellerEvent {
  const StartRealtimeUpdates();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StartRealtimeUpdates);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SellerEvent.startRealtimeUpdates()';
}


}




/// @nodoc


class StopRealtimeUpdates implements SellerEvent {
  const StopRealtimeUpdates();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StopRealtimeUpdates);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'SellerEvent.stopRealtimeUpdates()';
}


}




/// @nodoc
mixin _$SellerState {

 List<Seller> get sellers; bool get isLoading; bool get hasMore; String? get error;
/// Create a copy of SellerState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerStateCopyWith<SellerState> get copyWith => _$SellerStateCopyWithImpl<SellerState>(this as SellerState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerState&&const DeepCollectionEquality().equals(other.sellers, sellers)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore)&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(sellers),isLoading,hasMore,error);

@override
String toString() {
  return 'SellerState(sellers: $sellers, isLoading: $isLoading, hasMore: $hasMore, error: $error)';
}


}

/// @nodoc
abstract mixin class $SellerStateCopyWith<$Res>  {
  factory $SellerStateCopyWith(SellerState value, $Res Function(SellerState) _then) = _$SellerStateCopyWithImpl;
@useResult
$Res call({
 List<Seller> sellers, bool isLoading, bool hasMore, String? error
});




}
/// @nodoc
class _$SellerStateCopyWithImpl<$Res>
    implements $SellerStateCopyWith<$Res> {
  _$SellerStateCopyWithImpl(this._self, this._then);

  final SellerState _self;
  final $Res Function(SellerState) _then;

/// Create a copy of SellerState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sellers = null,Object? isLoading = null,Object? hasMore = null,Object? error = freezed,}) {
  return _then(_self.copyWith(
sellers: null == sellers ? _self.sellers : sellers // ignore: cast_nullable_to_non_nullable
as List<Seller>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerState].
extension SellerStatePatterns on SellerState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( Loaded value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case Loaded() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( Loaded value)  $default,){
final _that = this;
switch (_that) {
case Loaded():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( Loaded value)?  $default,){
final _that = this;
switch (_that) {
case Loaded() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<Seller> sellers,  bool isLoading,  bool hasMore,  String? error)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case Loaded() when $default != null:
return $default(_that.sellers,_that.isLoading,_that.hasMore,_that.error);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<Seller> sellers,  bool isLoading,  bool hasMore,  String? error)  $default,) {final _that = this;
switch (_that) {
case Loaded():
return $default(_that.sellers,_that.isLoading,_that.hasMore,_that.error);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<Seller> sellers,  bool isLoading,  bool hasMore,  String? error)?  $default,) {final _that = this;
switch (_that) {
case Loaded() when $default != null:
return $default(_that.sellers,_that.isLoading,_that.hasMore,_that.error);case _:
  return null;

}
}

}

/// @nodoc


class Loaded implements SellerState {
  const Loaded({final  List<Seller> sellers = const [], this.isLoading = false, this.hasMore = false, this.error}): _sellers = sellers;
  

 final  List<Seller> _sellers;
@override@JsonKey() List<Seller> get sellers {
  if (_sellers is EqualUnmodifiableListView) return _sellers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_sellers);
}

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool hasMore;
@override final  String? error;

/// Create a copy of SellerState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LoadedCopyWith<Loaded> get copyWith => _$LoadedCopyWithImpl<Loaded>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Loaded&&const DeepCollectionEquality().equals(other._sellers, _sellers)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasMore, hasMore) || other.hasMore == hasMore)&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_sellers),isLoading,hasMore,error);

@override
String toString() {
  return 'SellerState(sellers: $sellers, isLoading: $isLoading, hasMore: $hasMore, error: $error)';
}


}

/// @nodoc
abstract mixin class $LoadedCopyWith<$Res> implements $SellerStateCopyWith<$Res> {
  factory $LoadedCopyWith(Loaded value, $Res Function(Loaded) _then) = _$LoadedCopyWithImpl;
@override @useResult
$Res call({
 List<Seller> sellers, bool isLoading, bool hasMore, String? error
});




}
/// @nodoc
class _$LoadedCopyWithImpl<$Res>
    implements $LoadedCopyWith<$Res> {
  _$LoadedCopyWithImpl(this._self, this._then);

  final Loaded _self;
  final $Res Function(Loaded) _then;

/// Create a copy of SellerState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sellers = null,Object? isLoading = null,Object? hasMore = null,Object? error = freezed,}) {
  return _then(Loaded(
sellers: null == sellers ? _self._sellers : sellers // ignore: cast_nullable_to_non_nullable
as List<Seller>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasMore: null == hasMore ? _self.hasMore : hasMore // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
