// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get inventory => 'सूची';

  @override
  String get unitPrice => 'इकाई मूल्य';

  @override
  String get minStock => 'न्यूनतम स्टॉक';

  @override
  String get addStock => 'स्टॉक जोड़ें';

  @override
  String get removeStock => 'स्टॉक हटाएं';

  @override
  String get setLowStockAlert => 'कम स्टॉक अलर्ट सेट करें';

  @override
  String get lowStockAlert => 'कम स्टॉक अलर्ट';

  @override
  String get retry => 'पुनः प्रयास करें';

  @override
  String get error => 'त्रुटि';

  @override
  String get loading => 'लोड हो रहा है...';

  @override
  String get noItems => 'कोई आइटम नहीं मिला';

  @override
  String get stock => 'स्टॉक';

  @override
  String get currentStock => 'वर्तमान स्टॉक';

  @override
  String get maximumStock => 'अधिकतम स्टॉक';

  @override
  String get minimumStock => 'न्यूनतम स्टॉक';

  @override
  String get unit => 'इकाई';

  @override
  String get productId => 'उत्पाद आईडी';

  @override
  String itemsRunningLow(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count आइटम कम हैं',
      one: '1 आइटम कम है',
      zero: 'कोई आइटम कम नहीं है',
    );
    return '$_temp0';
  }

  @override
  String get defaultAlarm => 'डिफ़ॉल्ट अलार्म';

  @override
  String get time => 'समय';

  @override
  String get tone => 'टोन';

  @override
  String get volume => 'वॉल्यूम';

  @override
  String get vibration => 'वाइब्रेशन';

  @override
  String get snoozeSettings => 'स्नूज़ सेटिंग्स';

  @override
  String get snoozeDuration => 'स्नूज़ अवधि';

  @override
  String get maxSnoozeCount => 'अधिकतम स्नूज़ संख्या';

  @override
  String get minutes => 'मिनट';

  @override
  String get save => 'सहेजें';

  @override
  String get alarmSaved => 'अलार्म सफलतापूर्वक सहेजा गया';

  @override
  String get errorSavingAlarm => 'अलार्म सहेजने में त्रुटि';
}
