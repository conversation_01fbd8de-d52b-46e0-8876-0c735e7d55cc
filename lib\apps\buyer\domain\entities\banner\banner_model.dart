import 'package:freezed_annotation/freezed_annotation.dart';

part 'banner_model.freezed.dart';
part 'banner_model.g.dart';

/// Model for banner carousel items
@freezed
sealed class BannerModel with _$BannerModel {
  /// Creates a [BannerModel]
  const factory BannerModel({
    /// Unique identifier for the banner
    required String id,

    /// Image URL for the banner
    required String imageUrl,

    /// Title of the banner
    required String title,

    /// Description of the banner
    String? description,

    /// Action type for the banner (e.g., 'product', 'category', 'url')
    required String actionType,

    /// Action data for the banner (e.g., product ID, category ID, URL)
    String? actionData,

    /// Priority order for the banner
    required int priority,

    /// Whether the banner is active
    required bool isActive,

    /// Start date for the banner
    required DateTime startDate,

    /// End date for the banner
    required DateTime endDate,

    /// Created at timestamp
    DateTime? createdAt,

    /// Updated at timestamp
    DateTime? updatedAt,
  }) = _BannerModel;

  /// Creates a [BannerModel] from JSON
  factory BannerModel.fromJson(Map<String, dynamic> json) =>
      _$BannerModelFromJson(json);
}
