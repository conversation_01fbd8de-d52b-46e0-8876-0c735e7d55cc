import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';
import '../../repositories/shopping_list_repository.dart';
import '../../services/shopping_list_service.dart';
import '../../../../shared/providers/auth_provider.dart';

/// Provider for the seller shopping list service
final sellerShoppingListServiceProvider = Provider<SellerShoppingListService>((ref) {
  return SellerShoppingListService();
});

/// Provider for the seller shopping list repository
final sellerShoppingListRepositoryProvider = Provider<SellerShoppingListRepository>((ref) {
  return SellerShoppingListRepository(ref.watch(sellerShoppingListServiceProvider));
});

/// Provider for the shopping lists requested from this seller
final sellerRequestedListsProvider =
    StreamProvider<List<ShoppingListModel>>((ref) {
  final user = ref.watch(currentUserProvider).value;
  if (user == null) return Stream.value([]);
  return ref
      .watch(sellerShoppingListRepositoryProvider)
      .getRequestedShoppingLists(user.id);
});

/// Provider for a specific shopping list
final sellerShoppingListProvider = StreamProvider.family<ShoppingListModel?, String>(
  (ref, listId) =>
      ref.watch(sellerShoppingListRepositoryProvider).getShoppingList(listId),
);

/// Provider for shopping list operations
final sellerShoppingListNotifierProvider =
    StateNotifierProvider<SellerShoppingListNotifier, AsyncValue<void>>(
  (ref) => SellerShoppingListNotifier(ref.watch(sellerShoppingListRepositoryProvider)),
);

class SellerShoppingListNotifier extends StateNotifier<AsyncValue<void>> {
  final SellerShoppingListRepository _repository;

  SellerShoppingListNotifier(this._repository) : super(const AsyncValue.data(null));

  Future<void> updatePriceList(ShoppingListModel list) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateShoppingList(list);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
