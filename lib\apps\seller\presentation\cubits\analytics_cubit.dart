import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/apps/seller/domain/models/analytics_model.dart';
import 'package:shivish/apps/seller/domain/repositories/analytics_repository.dart';

part 'analytics_cubit.freezed.dart';

@freezed
sealed class AnalyticsState with _$AnalyticsState {
  const factory AnalyticsState({
    @Default(false) bool isLoading,
    String? error,
    SalesAnalytics? salesAnalytics,
    ProductAnalytics? productAnalytics,
    CustomerAnalytics? customerAnalytics,
    PerformanceMetrics? performanceMetrics,
  }) = _AnalyticsState;

  const AnalyticsState._();

  bool get hasError => error != null;
  String? get errorMessage => error;
}

class AnalyticsCubit extends Cubit<AnalyticsState> {
  final AnalyticsRepository _repository;

  AnalyticsCubit(this._repository) : super(const AnalyticsState());

  Future<void> loadSalesAnalytics() async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      final analytics = await _repository.getSalesAnalytics();
      emit(state.copyWith(
        isLoading: false,
        salesAnalytics: analytics,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> loadProductAnalytics() async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      final analytics = await _repository.getProductAnalytics();
      emit(state.copyWith(
        isLoading: false,
        productAnalytics: analytics,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> loadCustomerAnalytics() async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      final analytics = await _repository.getCustomerAnalytics();
      emit(state.copyWith(
        isLoading: false,
        customerAnalytics: analytics,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> loadPerformanceMetrics() async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      final metrics = await _repository.getPerformanceMetrics();
      emit(state.copyWith(
        isLoading: false,
        performanceMetrics: metrics,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }
}
