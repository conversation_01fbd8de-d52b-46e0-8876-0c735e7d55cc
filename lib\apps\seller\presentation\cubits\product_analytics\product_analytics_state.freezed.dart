// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_analytics_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ProductAnalyticsState {

 double get totalSales; int get totalOrders; double get averageOrderValue; double get conversionRate; double get salesTrend; double get ordersTrend; double get aovTrend; double get conversionTrend; List<FlSpot> get salesData; List<BarChartGroupData> get topProductsData; List<InventoryStatus> get inventoryStatus;
/// Create a copy of ProductAnalyticsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductAnalyticsStateCopyWith<ProductAnalyticsState> get copyWith => _$ProductAnalyticsStateCopyWithImpl<ProductAnalyticsState>(this as ProductAnalyticsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductAnalyticsState&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.averageOrderValue, averageOrderValue) || other.averageOrderValue == averageOrderValue)&&(identical(other.conversionRate, conversionRate) || other.conversionRate == conversionRate)&&(identical(other.salesTrend, salesTrend) || other.salesTrend == salesTrend)&&(identical(other.ordersTrend, ordersTrend) || other.ordersTrend == ordersTrend)&&(identical(other.aovTrend, aovTrend) || other.aovTrend == aovTrend)&&(identical(other.conversionTrend, conversionTrend) || other.conversionTrend == conversionTrend)&&const DeepCollectionEquality().equals(other.salesData, salesData)&&const DeepCollectionEquality().equals(other.topProductsData, topProductsData)&&const DeepCollectionEquality().equals(other.inventoryStatus, inventoryStatus));
}


@override
int get hashCode => Object.hash(runtimeType,totalSales,totalOrders,averageOrderValue,conversionRate,salesTrend,ordersTrend,aovTrend,conversionTrend,const DeepCollectionEquality().hash(salesData),const DeepCollectionEquality().hash(topProductsData),const DeepCollectionEquality().hash(inventoryStatus));

@override
String toString() {
  return 'ProductAnalyticsState(totalSales: $totalSales, totalOrders: $totalOrders, averageOrderValue: $averageOrderValue, conversionRate: $conversionRate, salesTrend: $salesTrend, ordersTrend: $ordersTrend, aovTrend: $aovTrend, conversionTrend: $conversionTrend, salesData: $salesData, topProductsData: $topProductsData, inventoryStatus: $inventoryStatus)';
}


}

/// @nodoc
abstract mixin class $ProductAnalyticsStateCopyWith<$Res>  {
  factory $ProductAnalyticsStateCopyWith(ProductAnalyticsState value, $Res Function(ProductAnalyticsState) _then) = _$ProductAnalyticsStateCopyWithImpl;
@useResult
$Res call({
 double totalSales, int totalOrders, double averageOrderValue, double conversionRate, double salesTrend, double ordersTrend, double aovTrend, double conversionTrend, List<FlSpot> salesData, List<BarChartGroupData> topProductsData, List<InventoryStatus> inventoryStatus
});




}
/// @nodoc
class _$ProductAnalyticsStateCopyWithImpl<$Res>
    implements $ProductAnalyticsStateCopyWith<$Res> {
  _$ProductAnalyticsStateCopyWithImpl(this._self, this._then);

  final ProductAnalyticsState _self;
  final $Res Function(ProductAnalyticsState) _then;

/// Create a copy of ProductAnalyticsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalSales = null,Object? totalOrders = null,Object? averageOrderValue = null,Object? conversionRate = null,Object? salesTrend = null,Object? ordersTrend = null,Object? aovTrend = null,Object? conversionTrend = null,Object? salesData = null,Object? topProductsData = null,Object? inventoryStatus = null,}) {
  return _then(_self.copyWith(
totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,averageOrderValue: null == averageOrderValue ? _self.averageOrderValue : averageOrderValue // ignore: cast_nullable_to_non_nullable
as double,conversionRate: null == conversionRate ? _self.conversionRate : conversionRate // ignore: cast_nullable_to_non_nullable
as double,salesTrend: null == salesTrend ? _self.salesTrend : salesTrend // ignore: cast_nullable_to_non_nullable
as double,ordersTrend: null == ordersTrend ? _self.ordersTrend : ordersTrend // ignore: cast_nullable_to_non_nullable
as double,aovTrend: null == aovTrend ? _self.aovTrend : aovTrend // ignore: cast_nullable_to_non_nullable
as double,conversionTrend: null == conversionTrend ? _self.conversionTrend : conversionTrend // ignore: cast_nullable_to_non_nullable
as double,salesData: null == salesData ? _self.salesData : salesData // ignore: cast_nullable_to_non_nullable
as List<FlSpot>,topProductsData: null == topProductsData ? _self.topProductsData : topProductsData // ignore: cast_nullable_to_non_nullable
as List<BarChartGroupData>,inventoryStatus: null == inventoryStatus ? _self.inventoryStatus : inventoryStatus // ignore: cast_nullable_to_non_nullable
as List<InventoryStatus>,
  ));
}

}


/// Adds pattern-matching-related methods to [ProductAnalyticsState].
extension ProductAnalyticsStatePatterns on ProductAnalyticsState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ProductAnalyticsState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ProductAnalyticsState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ProductAnalyticsState value)  $default,){
final _that = this;
switch (_that) {
case _ProductAnalyticsState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ProductAnalyticsState value)?  $default,){
final _that = this;
switch (_that) {
case _ProductAnalyticsState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double totalSales,  int totalOrders,  double averageOrderValue,  double conversionRate,  double salesTrend,  double ordersTrend,  double aovTrend,  double conversionTrend,  List<FlSpot> salesData,  List<BarChartGroupData> topProductsData,  List<InventoryStatus> inventoryStatus)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ProductAnalyticsState() when $default != null:
return $default(_that.totalSales,_that.totalOrders,_that.averageOrderValue,_that.conversionRate,_that.salesTrend,_that.ordersTrend,_that.aovTrend,_that.conversionTrend,_that.salesData,_that.topProductsData,_that.inventoryStatus);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double totalSales,  int totalOrders,  double averageOrderValue,  double conversionRate,  double salesTrend,  double ordersTrend,  double aovTrend,  double conversionTrend,  List<FlSpot> salesData,  List<BarChartGroupData> topProductsData,  List<InventoryStatus> inventoryStatus)  $default,) {final _that = this;
switch (_that) {
case _ProductAnalyticsState():
return $default(_that.totalSales,_that.totalOrders,_that.averageOrderValue,_that.conversionRate,_that.salesTrend,_that.ordersTrend,_that.aovTrend,_that.conversionTrend,_that.salesData,_that.topProductsData,_that.inventoryStatus);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double totalSales,  int totalOrders,  double averageOrderValue,  double conversionRate,  double salesTrend,  double ordersTrend,  double aovTrend,  double conversionTrend,  List<FlSpot> salesData,  List<BarChartGroupData> topProductsData,  List<InventoryStatus> inventoryStatus)?  $default,) {final _that = this;
switch (_that) {
case _ProductAnalyticsState() when $default != null:
return $default(_that.totalSales,_that.totalOrders,_that.averageOrderValue,_that.conversionRate,_that.salesTrend,_that.ordersTrend,_that.aovTrend,_that.conversionTrend,_that.salesData,_that.topProductsData,_that.inventoryStatus);case _:
  return null;

}
}

}

/// @nodoc


class _ProductAnalyticsState implements ProductAnalyticsState {
  const _ProductAnalyticsState({required this.totalSales, required this.totalOrders, required this.averageOrderValue, required this.conversionRate, required this.salesTrend, required this.ordersTrend, required this.aovTrend, required this.conversionTrend, required final  List<FlSpot> salesData, required final  List<BarChartGroupData> topProductsData, required final  List<InventoryStatus> inventoryStatus}): _salesData = salesData,_topProductsData = topProductsData,_inventoryStatus = inventoryStatus;
  

@override final  double totalSales;
@override final  int totalOrders;
@override final  double averageOrderValue;
@override final  double conversionRate;
@override final  double salesTrend;
@override final  double ordersTrend;
@override final  double aovTrend;
@override final  double conversionTrend;
 final  List<FlSpot> _salesData;
@override List<FlSpot> get salesData {
  if (_salesData is EqualUnmodifiableListView) return _salesData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_salesData);
}

 final  List<BarChartGroupData> _topProductsData;
@override List<BarChartGroupData> get topProductsData {
  if (_topProductsData is EqualUnmodifiableListView) return _topProductsData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topProductsData);
}

 final  List<InventoryStatus> _inventoryStatus;
@override List<InventoryStatus> get inventoryStatus {
  if (_inventoryStatus is EqualUnmodifiableListView) return _inventoryStatus;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_inventoryStatus);
}


/// Create a copy of ProductAnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductAnalyticsStateCopyWith<_ProductAnalyticsState> get copyWith => __$ProductAnalyticsStateCopyWithImpl<_ProductAnalyticsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductAnalyticsState&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.averageOrderValue, averageOrderValue) || other.averageOrderValue == averageOrderValue)&&(identical(other.conversionRate, conversionRate) || other.conversionRate == conversionRate)&&(identical(other.salesTrend, salesTrend) || other.salesTrend == salesTrend)&&(identical(other.ordersTrend, ordersTrend) || other.ordersTrend == ordersTrend)&&(identical(other.aovTrend, aovTrend) || other.aovTrend == aovTrend)&&(identical(other.conversionTrend, conversionTrend) || other.conversionTrend == conversionTrend)&&const DeepCollectionEquality().equals(other._salesData, _salesData)&&const DeepCollectionEquality().equals(other._topProductsData, _topProductsData)&&const DeepCollectionEquality().equals(other._inventoryStatus, _inventoryStatus));
}


@override
int get hashCode => Object.hash(runtimeType,totalSales,totalOrders,averageOrderValue,conversionRate,salesTrend,ordersTrend,aovTrend,conversionTrend,const DeepCollectionEquality().hash(_salesData),const DeepCollectionEquality().hash(_topProductsData),const DeepCollectionEquality().hash(_inventoryStatus));

@override
String toString() {
  return 'ProductAnalyticsState(totalSales: $totalSales, totalOrders: $totalOrders, averageOrderValue: $averageOrderValue, conversionRate: $conversionRate, salesTrend: $salesTrend, ordersTrend: $ordersTrend, aovTrend: $aovTrend, conversionTrend: $conversionTrend, salesData: $salesData, topProductsData: $topProductsData, inventoryStatus: $inventoryStatus)';
}


}

/// @nodoc
abstract mixin class _$ProductAnalyticsStateCopyWith<$Res> implements $ProductAnalyticsStateCopyWith<$Res> {
  factory _$ProductAnalyticsStateCopyWith(_ProductAnalyticsState value, $Res Function(_ProductAnalyticsState) _then) = __$ProductAnalyticsStateCopyWithImpl;
@override @useResult
$Res call({
 double totalSales, int totalOrders, double averageOrderValue, double conversionRate, double salesTrend, double ordersTrend, double aovTrend, double conversionTrend, List<FlSpot> salesData, List<BarChartGroupData> topProductsData, List<InventoryStatus> inventoryStatus
});




}
/// @nodoc
class __$ProductAnalyticsStateCopyWithImpl<$Res>
    implements _$ProductAnalyticsStateCopyWith<$Res> {
  __$ProductAnalyticsStateCopyWithImpl(this._self, this._then);

  final _ProductAnalyticsState _self;
  final $Res Function(_ProductAnalyticsState) _then;

/// Create a copy of ProductAnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalSales = null,Object? totalOrders = null,Object? averageOrderValue = null,Object? conversionRate = null,Object? salesTrend = null,Object? ordersTrend = null,Object? aovTrend = null,Object? conversionTrend = null,Object? salesData = null,Object? topProductsData = null,Object? inventoryStatus = null,}) {
  return _then(_ProductAnalyticsState(
totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,averageOrderValue: null == averageOrderValue ? _self.averageOrderValue : averageOrderValue // ignore: cast_nullable_to_non_nullable
as double,conversionRate: null == conversionRate ? _self.conversionRate : conversionRate // ignore: cast_nullable_to_non_nullable
as double,salesTrend: null == salesTrend ? _self.salesTrend : salesTrend // ignore: cast_nullable_to_non_nullable
as double,ordersTrend: null == ordersTrend ? _self.ordersTrend : ordersTrend // ignore: cast_nullable_to_non_nullable
as double,aovTrend: null == aovTrend ? _self.aovTrend : aovTrend // ignore: cast_nullable_to_non_nullable
as double,conversionTrend: null == conversionTrend ? _self.conversionTrend : conversionTrend // ignore: cast_nullable_to_non_nullable
as double,salesData: null == salesData ? _self._salesData : salesData // ignore: cast_nullable_to_non_nullable
as List<FlSpot>,topProductsData: null == topProductsData ? _self._topProductsData : topProductsData // ignore: cast_nullable_to_non_nullable
as List<BarChartGroupData>,inventoryStatus: null == inventoryStatus ? _self._inventoryStatus : inventoryStatus // ignore: cast_nullable_to_non_nullable
as List<InventoryStatus>,
  ));
}


}

// dart format on
