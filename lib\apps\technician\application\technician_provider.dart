import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/models/technician/technician.dart';
import 'package:shivish/apps/technician/domain/repositories/technician_repository.dart';
import 'package:shivish/apps/technician/data/repositories/technician_repository_impl.dart';
import 'package:shivish/shared/core/auth/models/register_request.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

/// Provider for DatabaseService
final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

/// Provider for AuthService
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

/// Provider for TechnicianRepository
final technicianRepositoryProvider = Provider<TechnicianRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return TechnicianRepositoryImpl(databaseService);
});

/// Provider for technician data
final technicianProvider =
    StateNotifierProvider<TechnicianNotifier, AsyncValue<Technician?>>(
  (ref) => TechnicianNotifier(ref.watch(technicianRepositoryProvider)),
);

/// Notifier for technician data
class TechnicianNotifier extends StateNotifier<AsyncValue<Technician?>> {
  final TechnicianRepository _repository;
  final AuthService _authService = AuthService();

  TechnicianNotifier(this._repository) : super(const AsyncValue.data(null));

  /// Get technician by ID
  Future<Technician?> getTechnician(String id) async {
    try {
      // Check if we're already loading to avoid duplicate state changes
      if (!state.isLoading) {
        state = const AsyncValue.loading();
      }

      debugPrint('TechnicianNotifier: Getting technician with ID: $id');
      final technician = await _repository.getTechnician(id);

      // If technician is null, create a default technician profile
      if (technician == null) {
        debugPrint('TechnicianNotifier: Technician not found, creating default technician');
        return await _createDefaultTechnician(id);
      }

      debugPrint('TechnicianNotifier: Technician found: ${technician.name}');

      // Only update state if we're still mounted and the state hasn't changed
      if (!state.hasValue || state.value?.id != technician.id) {
        state = AsyncValue.data(technician);
      }

      return technician;
    } catch (e, stackTrace) {
      debugPrint('TechnicianNotifier: Error getting technician: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Create a default technician profile
  Future<Technician> _createDefaultTechnician(String id) async {
    try {
      debugPrint('TechnicianNotifier: Creating default technician profile for ID: $id');

      // Get user data from Firebase Auth
      final user = await _authService.getCurrentUser();
      if (user == null) {
        throw Exception('User not found');
      }

      // Create a default technician profile with all required fields
      final now = DateTime.now();
      final technician = Technician(
        id: id,
        name: user.displayName,
        email: user.email,
        phone: user.phoneNumber ?? '',
        createdAt: now,
        updatedAt: now,
        experienceYears: 0,
        specializations: [],
        serviceAreas: [],
        availabilitySchedule: {},
        certifications: [],
        partsInventory: {},
        serviceRates: {},
        toolsEquipment: [],
        insuranceInfo: {},
        emergencyContact: {},
        businessHours: {},
        rating: 0.0,
        totalReviews: 0,
        isVerified: false,
        isActive: true,
        verificationStatus: 'pending',
        profileImage: null, // Will be updated later if available
      );

      // Save the technician to the repository
      await _repository.createTechnician(technician);

      // Update state
      state = AsyncValue.data(technician);

      return technician;
    } catch (e, stackTrace) {
      debugPrint('TechnicianNotifier: Error creating default technician: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Update technician profile
  Future<void> updateTechnician(Technician technician) async {
    try {
      // Check if we're already loading to avoid duplicate state changes
      if (!state.isLoading) {
        state = const AsyncValue.loading();
      }

      debugPrint('TechnicianNotifier: Updating technician: ${technician.name}');
      await _repository.updateTechnician(technician);

      // Update state
      state = AsyncValue.data(technician);
    } catch (e, stackTrace) {
      debugPrint('TechnicianNotifier: Error updating technician: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Registers a new technician
  Future<void> register(RegisterRequest request) async {
    try {
      // Check if we're already loading to avoid duplicate state changes
      if (!state.isLoading) {
        state = const AsyncValue.loading();
      }

      // Register the user with Firebase Auth
      final userCredential = await _authService.signUpWithEmail(
        email: request.email,
        password: request.password,
        role: request.role,
        userData: request.metadata,
      );

      // Create technician profile
      if (userCredential.user != null) {
        final now = DateTime.now();
        final technician = Technician(
          id: userCredential.user!.id,
          name: request.displayName,
          email: request.email,
          phone: request.phoneNumber ?? '',
          createdAt: now,
          updatedAt: now,
          specializations: request.metadata?['specializations'] != null
              ? List<String>.from(request.metadata!['specializations'])
              : [],
          serviceAreas: request.metadata?['serviceAreas'] != null
              ? List<String>.from(request.metadata!['serviceAreas'])
              : [],
          experienceYears: request.metadata?['experienceYears'] != null
              ? int.parse(request.metadata!['experienceYears'].toString())
              : 0,
          rating: 0.0,
          totalReviews: 0,
          isVerified: false,
          isActive: true,
          verificationStatus: 'pending',
          profileImage: request.metadata?['profileImage'],
          // Required fields with default values
          availabilitySchedule: {},
          certifications: [],
          partsInventory: {},
          serviceRates: {},
          toolsEquipment: [],
          insuranceInfo: {},
          emergencyContact: {},
          businessHours: {},
        );

        debugPrint('Creating technician with pending approval status');

        await _repository.createTechnician(technician);

        // Only update state if we're still in loading state
        if (state.isLoading) {
          state = AsyncValue.data(technician);
        }
      }
    } catch (e, stackTrace) {
      debugPrint('TechnicianNotifier: Error registering technician: $e');
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  /// Clear any errors
  void clearError() {
    if (state.hasError) {
      state = const AsyncValue.data(null);
    }
  }
}
