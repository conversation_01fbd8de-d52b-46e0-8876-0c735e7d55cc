// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RegisterRequestImpl _$$RegisterRequestImplFromJson(
  Map<String, dynamic> json,
) => _$RegisterRequestImpl(
  email: json['email'] as String,
  password: json['password'] as String,
  displayName: json['displayName'] as String,
  phoneNumber: json['phoneNumber'] as String?,
  role: $enumDecode(_$UserRoleEnumMap, json['role']),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$RegisterRequestImplToJson(
  _$RegisterRequestImpl instance,
) => <String, dynamic>{
  'email': instance.email,
  'password': instance.password,
  'displayName': instance.displayName,
  'phoneNumber': instance.phoneNumber,
  'role': _$UserRoleEnumMap[instance.role]!,
  'metadata': instance.metadata,
};

const _$UserRoleEnumMap = {
  UserRole.buyer: 'buyer',
  UserRole.seller: 'seller',
  UserRole.priest: 'priest',
  UserRole.technician: 'technician',
  UserRole.executor: 'executor',
  UserRole.admin: 'admin',
  UserRole.saviour: 'saviour',
  UserRole.hospital: 'hospital',
};
