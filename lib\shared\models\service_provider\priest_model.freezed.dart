// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'priest_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PriestModel {

 String get id; String get name; String get specialization; String get description; double get rating; int get totalReviews; List<String> get services; List<String> get languages; List<String> get certifications; List<String> get experience; String get profileImage; String get coverImage; Map<String, dynamic> get contactInfo; Map<String, dynamic> get availability; Map<String, dynamic> get pricing; Map<String, dynamic> get location; bool get isAvailable; bool get isVerified; DateTime get createdAt; DateTime get updatedAt; bool get isDeleted;
/// Create a copy of PriestModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PriestModelCopyWith<PriestModel> get copyWith => _$PriestModelCopyWithImpl<PriestModel>(this as PriestModel, _$identity);

  /// Serializes this PriestModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PriestModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.specialization, specialization) || other.specialization == specialization)&&(identical(other.description, description) || other.description == description)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&const DeepCollectionEquality().equals(other.services, services)&&const DeepCollectionEquality().equals(other.languages, languages)&&const DeepCollectionEquality().equals(other.certifications, certifications)&&const DeepCollectionEquality().equals(other.experience, experience)&&(identical(other.profileImage, profileImage) || other.profileImage == profileImage)&&(identical(other.coverImage, coverImage) || other.coverImage == coverImage)&&const DeepCollectionEquality().equals(other.contactInfo, contactInfo)&&const DeepCollectionEquality().equals(other.availability, availability)&&const DeepCollectionEquality().equals(other.pricing, pricing)&&const DeepCollectionEquality().equals(other.location, location)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,specialization,description,rating,totalReviews,const DeepCollectionEquality().hash(services),const DeepCollectionEquality().hash(languages),const DeepCollectionEquality().hash(certifications),const DeepCollectionEquality().hash(experience),profileImage,coverImage,const DeepCollectionEquality().hash(contactInfo),const DeepCollectionEquality().hash(availability),const DeepCollectionEquality().hash(pricing),const DeepCollectionEquality().hash(location),isAvailable,isVerified,createdAt,updatedAt,isDeleted]);

@override
String toString() {
  return 'PriestModel(id: $id, name: $name, specialization: $specialization, description: $description, rating: $rating, totalReviews: $totalReviews, services: $services, languages: $languages, certifications: $certifications, experience: $experience, profileImage: $profileImage, coverImage: $coverImage, contactInfo: $contactInfo, availability: $availability, pricing: $pricing, location: $location, isAvailable: $isAvailable, isVerified: $isVerified, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $PriestModelCopyWith<$Res>  {
  factory $PriestModelCopyWith(PriestModel value, $Res Function(PriestModel) _then) = _$PriestModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String specialization, String description, double rating, int totalReviews, List<String> services, List<String> languages, List<String> certifications, List<String> experience, String profileImage, String coverImage, Map<String, dynamic> contactInfo, Map<String, dynamic> availability, Map<String, dynamic> pricing, Map<String, dynamic> location, bool isAvailable, bool isVerified, DateTime createdAt, DateTime updatedAt, bool isDeleted
});




}
/// @nodoc
class _$PriestModelCopyWithImpl<$Res>
    implements $PriestModelCopyWith<$Res> {
  _$PriestModelCopyWithImpl(this._self, this._then);

  final PriestModel _self;
  final $Res Function(PriestModel) _then;

/// Create a copy of PriestModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? specialization = null,Object? description = null,Object? rating = null,Object? totalReviews = null,Object? services = null,Object? languages = null,Object? certifications = null,Object? experience = null,Object? profileImage = null,Object? coverImage = null,Object? contactInfo = null,Object? availability = null,Object? pricing = null,Object? location = null,Object? isAvailable = null,Object? isVerified = null,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,specialization: null == specialization ? _self.specialization : specialization // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,services: null == services ? _self.services : services // ignore: cast_nullable_to_non_nullable
as List<String>,languages: null == languages ? _self.languages : languages // ignore: cast_nullable_to_non_nullable
as List<String>,certifications: null == certifications ? _self.certifications : certifications // ignore: cast_nullable_to_non_nullable
as List<String>,experience: null == experience ? _self.experience : experience // ignore: cast_nullable_to_non_nullable
as List<String>,profileImage: null == profileImage ? _self.profileImage : profileImage // ignore: cast_nullable_to_non_nullable
as String,coverImage: null == coverImage ? _self.coverImage : coverImage // ignore: cast_nullable_to_non_nullable
as String,contactInfo: null == contactInfo ? _self.contactInfo : contactInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,availability: null == availability ? _self.availability : availability // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,pricing: null == pricing ? _self.pricing : pricing // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [PriestModel].
extension PriestModelPatterns on PriestModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PriestModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PriestModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PriestModel value)  $default,){
final _that = this;
switch (_that) {
case _PriestModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PriestModel value)?  $default,){
final _that = this;
switch (_that) {
case _PriestModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String specialization,  String description,  double rating,  int totalReviews,  List<String> services,  List<String> languages,  List<String> certifications,  List<String> experience,  String profileImage,  String coverImage,  Map<String, dynamic> contactInfo,  Map<String, dynamic> availability,  Map<String, dynamic> pricing,  Map<String, dynamic> location,  bool isAvailable,  bool isVerified,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PriestModel() when $default != null:
return $default(_that.id,_that.name,_that.specialization,_that.description,_that.rating,_that.totalReviews,_that.services,_that.languages,_that.certifications,_that.experience,_that.profileImage,_that.coverImage,_that.contactInfo,_that.availability,_that.pricing,_that.location,_that.isAvailable,_that.isVerified,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String specialization,  String description,  double rating,  int totalReviews,  List<String> services,  List<String> languages,  List<String> certifications,  List<String> experience,  String profileImage,  String coverImage,  Map<String, dynamic> contactInfo,  Map<String, dynamic> availability,  Map<String, dynamic> pricing,  Map<String, dynamic> location,  bool isAvailable,  bool isVerified,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _PriestModel():
return $default(_that.id,_that.name,_that.specialization,_that.description,_that.rating,_that.totalReviews,_that.services,_that.languages,_that.certifications,_that.experience,_that.profileImage,_that.coverImage,_that.contactInfo,_that.availability,_that.pricing,_that.location,_that.isAvailable,_that.isVerified,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String specialization,  String description,  double rating,  int totalReviews,  List<String> services,  List<String> languages,  List<String> certifications,  List<String> experience,  String profileImage,  String coverImage,  Map<String, dynamic> contactInfo,  Map<String, dynamic> availability,  Map<String, dynamic> pricing,  Map<String, dynamic> location,  bool isAvailable,  bool isVerified,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _PriestModel() when $default != null:
return $default(_that.id,_that.name,_that.specialization,_that.description,_that.rating,_that.totalReviews,_that.services,_that.languages,_that.certifications,_that.experience,_that.profileImage,_that.coverImage,_that.contactInfo,_that.availability,_that.pricing,_that.location,_that.isAvailable,_that.isVerified,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PriestModel implements PriestModel {
  const _PriestModel({required this.id, required this.name, required this.specialization, required this.description, required this.rating, required this.totalReviews, required final  List<String> services, required final  List<String> languages, required final  List<String> certifications, required final  List<String> experience, required this.profileImage, required this.coverImage, required final  Map<String, dynamic> contactInfo, required final  Map<String, dynamic> availability, required final  Map<String, dynamic> pricing, required final  Map<String, dynamic> location, required this.isAvailable, required this.isVerified, required this.createdAt, required this.updatedAt, this.isDeleted = false}): _services = services,_languages = languages,_certifications = certifications,_experience = experience,_contactInfo = contactInfo,_availability = availability,_pricing = pricing,_location = location;
  factory _PriestModel.fromJson(Map<String, dynamic> json) => _$PriestModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String specialization;
@override final  String description;
@override final  double rating;
@override final  int totalReviews;
 final  List<String> _services;
@override List<String> get services {
  if (_services is EqualUnmodifiableListView) return _services;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_services);
}

 final  List<String> _languages;
@override List<String> get languages {
  if (_languages is EqualUnmodifiableListView) return _languages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_languages);
}

 final  List<String> _certifications;
@override List<String> get certifications {
  if (_certifications is EqualUnmodifiableListView) return _certifications;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_certifications);
}

 final  List<String> _experience;
@override List<String> get experience {
  if (_experience is EqualUnmodifiableListView) return _experience;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_experience);
}

@override final  String profileImage;
@override final  String coverImage;
 final  Map<String, dynamic> _contactInfo;
@override Map<String, dynamic> get contactInfo {
  if (_contactInfo is EqualUnmodifiableMapView) return _contactInfo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_contactInfo);
}

 final  Map<String, dynamic> _availability;
@override Map<String, dynamic> get availability {
  if (_availability is EqualUnmodifiableMapView) return _availability;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_availability);
}

 final  Map<String, dynamic> _pricing;
@override Map<String, dynamic> get pricing {
  if (_pricing is EqualUnmodifiableMapView) return _pricing;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_pricing);
}

 final  Map<String, dynamic> _location;
@override Map<String, dynamic> get location {
  if (_location is EqualUnmodifiableMapView) return _location;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_location);
}

@override final  bool isAvailable;
@override final  bool isVerified;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool isDeleted;

/// Create a copy of PriestModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PriestModelCopyWith<_PriestModel> get copyWith => __$PriestModelCopyWithImpl<_PriestModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PriestModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PriestModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.specialization, specialization) || other.specialization == specialization)&&(identical(other.description, description) || other.description == description)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&const DeepCollectionEquality().equals(other._services, _services)&&const DeepCollectionEquality().equals(other._languages, _languages)&&const DeepCollectionEquality().equals(other._certifications, _certifications)&&const DeepCollectionEquality().equals(other._experience, _experience)&&(identical(other.profileImage, profileImage) || other.profileImage == profileImage)&&(identical(other.coverImage, coverImage) || other.coverImage == coverImage)&&const DeepCollectionEquality().equals(other._contactInfo, _contactInfo)&&const DeepCollectionEquality().equals(other._availability, _availability)&&const DeepCollectionEquality().equals(other._pricing, _pricing)&&const DeepCollectionEquality().equals(other._location, _location)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,specialization,description,rating,totalReviews,const DeepCollectionEquality().hash(_services),const DeepCollectionEquality().hash(_languages),const DeepCollectionEquality().hash(_certifications),const DeepCollectionEquality().hash(_experience),profileImage,coverImage,const DeepCollectionEquality().hash(_contactInfo),const DeepCollectionEquality().hash(_availability),const DeepCollectionEquality().hash(_pricing),const DeepCollectionEquality().hash(_location),isAvailable,isVerified,createdAt,updatedAt,isDeleted]);

@override
String toString() {
  return 'PriestModel(id: $id, name: $name, specialization: $specialization, description: $description, rating: $rating, totalReviews: $totalReviews, services: $services, languages: $languages, certifications: $certifications, experience: $experience, profileImage: $profileImage, coverImage: $coverImage, contactInfo: $contactInfo, availability: $availability, pricing: $pricing, location: $location, isAvailable: $isAvailable, isVerified: $isVerified, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$PriestModelCopyWith<$Res> implements $PriestModelCopyWith<$Res> {
  factory _$PriestModelCopyWith(_PriestModel value, $Res Function(_PriestModel) _then) = __$PriestModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String specialization, String description, double rating, int totalReviews, List<String> services, List<String> languages, List<String> certifications, List<String> experience, String profileImage, String coverImage, Map<String, dynamic> contactInfo, Map<String, dynamic> availability, Map<String, dynamic> pricing, Map<String, dynamic> location, bool isAvailable, bool isVerified, DateTime createdAt, DateTime updatedAt, bool isDeleted
});




}
/// @nodoc
class __$PriestModelCopyWithImpl<$Res>
    implements _$PriestModelCopyWith<$Res> {
  __$PriestModelCopyWithImpl(this._self, this._then);

  final _PriestModel _self;
  final $Res Function(_PriestModel) _then;

/// Create a copy of PriestModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? specialization = null,Object? description = null,Object? rating = null,Object? totalReviews = null,Object? services = null,Object? languages = null,Object? certifications = null,Object? experience = null,Object? profileImage = null,Object? coverImage = null,Object? contactInfo = null,Object? availability = null,Object? pricing = null,Object? location = null,Object? isAvailable = null,Object? isVerified = null,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,}) {
  return _then(_PriestModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,specialization: null == specialization ? _self.specialization : specialization // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,services: null == services ? _self._services : services // ignore: cast_nullable_to_non_nullable
as List<String>,languages: null == languages ? _self._languages : languages // ignore: cast_nullable_to_non_nullable
as List<String>,certifications: null == certifications ? _self._certifications : certifications // ignore: cast_nullable_to_non_nullable
as List<String>,experience: null == experience ? _self._experience : experience // ignore: cast_nullable_to_non_nullable
as List<String>,profileImage: null == profileImage ? _self.profileImage : profileImage // ignore: cast_nullable_to_non_nullable
as String,coverImage: null == coverImage ? _self.coverImage : coverImage // ignore: cast_nullable_to_non_nullable
as String,contactInfo: null == contactInfo ? _self._contactInfo : contactInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,availability: null == availability ? _self._availability : availability // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,pricing: null == pricing ? _self._pricing : pricing // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,location: null == location ? _self._location : location // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
