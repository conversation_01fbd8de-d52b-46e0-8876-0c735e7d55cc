import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/buyer/providers/help_provider.dart';
import 'package:shivish/apps/buyer/services/help_service.dart';
import 'package:shivish/shared/models/help/help_article.dart';
import 'package:shivish/shared/models/help/help_suggestion.dart';

final searchQueryProvider = StateProvider<String>((ref) => '');

class HelpCenterScreen extends ConsumerWidget {
  const HelpCenterScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Help Center'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              showSearch(
                context: context,
                delegate: HelpSearchDelegate(ref),
              );
            },
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSearchBar(ref),
          const SizedBox(height: 16),
          _buildQuickActions(context),
          const SizedBox(height: 16),
          _buildFAQSection(context),
          const SizedBox(height: 16),
          _buildKnowledgeBaseSection(context),
          const SizedBox(height: 16),
          _buildContactSupportSection(context, ref),
          const SizedBox(height: 16),
          _buildTicketSystemSection(context),
        ],
      ),
    );
  }

  Widget _buildSearchBar(WidgetRef ref) {
    return TextField(
      decoration: const InputDecoration(
        hintText: 'Search for help...',
        prefixIcon: Icon(Icons.search),
        border: OutlineInputBorder(),
      ),
      onTap: () {
        showSearch(
          context: ref.context,
          delegate: HelpSearchDelegate(ref),
        );
      },
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
          children: [
            _buildQuickActionCard(
              context,
              'Orders',
              Icons.shopping_bag,
              () => _navigateToOrders(context),
            ),
            _buildQuickActionCard(
              context,
              'Payments',
              Icons.payment,
              () => _navigateToPayments(context),
            ),
            _buildQuickActionCard(
              context,
              'Shipping',
              Icons.local_shipping,
              () => _navigateToShipping(context),
            ),
            _buildQuickActionCard(
              context,
              'Returns',
              Icons.assignment_return,
              () => _navigateToReturns(context),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32),
            const SizedBox(height: 8),
            Text(title),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Frequently Asked Questions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 5,
          itemBuilder: (context, index) {
            return ListTile(
              title: Text('FAQ ${index + 1}'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                _navigateToFAQDetails(context, index);
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildKnowledgeBaseSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Knowledge Base',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 5,
          itemBuilder: (context, index) {
            return ListTile(
              title: Text('Article ${index + 1}'),
              subtitle: Text('Category ${index + 1}'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                _navigateToArticleDetails(context, index);
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildContactSupportSection(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Contact Support',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Card(
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.chat),
                title: const Text('Live Chat'),
                subtitle: const Text('Chat with our support team'),
                onTap: () => _startLiveChat(ref),
              ),
              ListTile(
                leading: const Icon(Icons.email),
                title: const Text('Email Support'),
                subtitle: const Text('<EMAIL>'),
                onTap: () => _startEmailSupport(ref),
              ),
              ListTile(
                leading: const Icon(Icons.phone),
                title: const Text('Phone Support'),
                subtitle: const Text('****** 567 8900'),
                onTap: () => _startPhoneSupport(ref),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTicketSystemSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Support Tickets',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ElevatedButton.icon(
          onPressed: () {
            _navigateToCreateTicket(context);
          },
          icon: const Icon(Icons.add),
          label: const Text('Create New Ticket'),
        ),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3,
          itemBuilder: (context, index) {
            return ListTile(
              title: Text('Ticket #${index + 1}'),
              subtitle: Text('Status: ${index % 2 == 0 ? 'Open' : 'Closed'}'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                _navigateToTicketDetails(context, index);
              },
            );
          },
        ),
      ],
    );
  }

  void _navigateToOrders(BuildContext context) {
    context.pushNamed('orders');
  }

  void _navigateToPayments(BuildContext context) {
    context.pushNamed('payments');
  }

  void _navigateToShipping(BuildContext context) {
    context.pushNamed('shipping');
  }

  void _navigateToReturns(BuildContext context) {
    context.pushNamed('returns');
  }

  void _navigateToFAQDetails(BuildContext context, int index) {
    context.pushNamed('faq-details', extra: index);
  }

  void _navigateToArticleDetails(BuildContext context, int index) {
    context.pushNamed('article-details', extra: index);
  }

  void _navigateToCreateTicket(BuildContext context) {
    context.pushNamed('create-ticket');
  }

  void _navigateToTicketDetails(BuildContext context, int index) {
    context.pushNamed('ticket-details', extra: index);
  }

  Future<void> _startLiveChat(WidgetRef ref) async {
    final helpService = ref.read(helpServiceProvider);
    await helpService.startLiveChat();
  }

  Future<void> _startEmailSupport(WidgetRef ref) async {
    final helpService = ref.read(helpServiceProvider);
    await helpService.sendEmail(
      'Support Request',
      'I need help with...',
    );
  }

  Future<void> _startPhoneSupport(WidgetRef ref) async {
    final helpService = ref.read(helpServiceProvider);
    await helpService.initiatePhoneCall('+12345678900');
  }
}

class HelpSearchDelegate extends SearchDelegate<String> {
  final WidgetRef ref;

  HelpSearchDelegate(this.ref);

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final helpNotifier = ref.read(helpProvider.notifier);
        helpNotifier.searchArticles(query);

        return ref.watch(helpProvider).when(
              data: (articles) => _buildSearchResults(articles),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Text('Error: $error'),
              ),
            );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final suggestionsNotifier = ref.read(suggestionsProvider.notifier);
        suggestionsNotifier.getSuggestions(query);

        return ref.watch(suggestionsProvider).when(
              data: (suggestions) => _buildSearchSuggestions(suggestions),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Text('Error: $error'),
              ),
            );
      },
    );
  }

  Widget _buildSearchResults(List<HelpArticle> articles) {
    if (articles.isEmpty) {
      return const Center(
        child: Text('No results found'),
      );
    }
    return ListView.builder(
      itemCount: articles.length,
      itemBuilder: (context, index) {
        final article = articles[index];
        return ListTile(
          title: Text(article.title),
          subtitle: Text(article.question),
          onTap: () {
            context.pushNamed('article-details', extra: article);
          },
        );
      },
    );
  }

  Widget _buildSearchSuggestions(List<HelpSuggestion> suggestions) {
    if (suggestions.isEmpty) {
      return const Center(
        child: Text('No suggestions found'),
      );
    }
    return ListView.builder(
      itemCount: suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = suggestions[index];
        return ListTile(
          title: Text(suggestion.title),
          subtitle: Text(suggestion.description),
          onTap: () {
            query = suggestion.title;
          },
        );
      },
    );
  }
}
