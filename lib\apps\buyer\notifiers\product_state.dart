import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../shared/models/product/product_model.dart';

part 'product_state.freezed.dart';

/// Product state
@freezed
sealed class ProductState with _$ProductState {
  /// Initial state
  const factory ProductState.initial() = _Initial;

  /// Loading state
  const factory ProductState.loading() = _Loading;

  /// Loaded state
  const factory ProductState.loaded(List<ProductModel> products) = _Loaded;

  /// Error state
  const factory ProductState.error(String message) = _Error;
}
