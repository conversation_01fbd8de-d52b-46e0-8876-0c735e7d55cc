import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/help_provider.dart';
import '../../../../shared/models/help/help_article.dart';
import 'support_ticket_screen.dart';
import 'ticket_details_screen.dart';

class HelpScreen extends ConsumerWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final helpState = ref.watch(helpProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
      ),
      body: helpState.when(
        data: (data) => ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            _buildSearchBar(ref),
            const SizedBox(height: 24),
            _buildFAQSection(data, theme),
            const SizedBox(height: 24),
            _buildContactSupport(ref, theme),
            const SizedBox(height: 24),
            _buildTicketHistory(context, data, theme),
          ],
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text(
            'Error: ${error.toString()}',
            style: TextStyle(color: theme.colorScheme.error),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const SupportTicketScreen(),
          ),
        ),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchBar(WidgetRef ref) {
    return TextField(
      onChanged: (value) =>
          ref.read(helpProvider.notifier).updateSearchQuery(value),
      decoration: InputDecoration(
        hintText: 'Search for help',
        prefixIcon: const Icon(Icons.search),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Widget _buildFAQSection(List<HelpArticle> faqItems, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Frequently Asked Questions',
          style: theme.textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: faqItems.length,
          itemBuilder: (context, index) {
            final faq = faqItems[index];
            return Card(
              child: ExpansionTile(
                title: Text(faq.question),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(faq.answer),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildContactSupport(WidgetRef ref, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Contact Support',
          style: theme.textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        Card(
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.email),
                title: const Text('Email Support'),
                onTap: () => ref.read(helpProvider.notifier).openEmailSupport(),
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.phone),
                title: const Text('Phone Support'),
                onTap: () => ref.read(helpProvider.notifier).openPhoneSupport(),
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.chat),
                title: const Text('Live Chat'),
                onTap: () => ref.read(helpProvider.notifier).openLiveChat(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTicketHistory(
      BuildContext context, List<HelpArticle> tickets, ThemeData theme) {
    if (tickets.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Support Tickets',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          const Center(
            child: Text('No support tickets yet'),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Support Tickets',
          style: theme.textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: tickets.length,
          itemBuilder: (context, index) {
            final ticket = tickets[index];
            return Card(
              child: ListTile(
                title: Text(ticket.title),
                subtitle: Text('Status: ${ticket.status} • ${ticket.date}'),
                trailing: const Icon(Icons.chevron_right),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TicketDetailsScreen(ticket: ticket),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }
}
