import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shivish/shared/models/product/product_model.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/services/product/product_service.dart';
import 'package:shivish/shared/services/storage/storage_service.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/apps/seller/presentation/providers/product_provider.dart';
import 'package:shivish/apps/seller/application/seller_provider.dart';
import 'package:shivish/shared/constants/product_constants.dart';

class AddProductScreen extends ConsumerStatefulWidget {
  const AddProductScreen({super.key});

  @override
  ConsumerState<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends ConsumerState<AddProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _originalPriceController = TextEditingController();
  final _stockController = TextEditingController();
  final _brandController = TextEditingController();
  final _unitController = TextEditingController();
  final _weightController = TextEditingController();
  final _highlightsController = TextEditingController();
  final _warrantyController = TextEditingController();
  final _manufacturerController = TextEditingController();
  final _countryOfOriginController = TextEditingController();
  final _tagsController = TextEditingController();

  // Food type (veg/non-veg)
  String _foodType = 'veg'; // Default to vegetarian

  // Specifications
  final List<Map<String, TextEditingController>> _specifications = [
    {'key': TextEditingController(), 'value': TextEditingController()},
  ];

  // Day-wise availability for food products
  final Map<String, Map<String, TextEditingController>> _dayWiseAvailability = {
    'Monday': {'from': TextEditingController(), 'to': TextEditingController()},
    'Tuesday': {'from': TextEditingController(), 'to': TextEditingController()},
    'Wednesday': {
      'from': TextEditingController(),
      'to': TextEditingController(),
    },
    'Thursday': {
      'from': TextEditingController(),
      'to': TextEditingController(),
    },
    'Friday': {'from': TextEditingController(), 'to': TextEditingController()},
    'Saturday': {
      'from': TextEditingController(),
      'to': TextEditingController(),
    },
    'Sunday': {'from': TextEditingController(), 'to': TextEditingController()},
  };

  String? _selectedCategory;
  final List<String> _selectedImageUrls = [];
  bool _isLoading = false;

  /// Check if current seller is an event service provider
  bool get _isEventService {
    final sellerAsync = ref.read(sellerProvider);
    return sellerAsync.value?.category.isEventService ?? false;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _originalPriceController.dispose();
    _stockController.dispose();
    _brandController.dispose();
    _unitController.dispose();
    _weightController.dispose();
    _highlightsController.dispose();
    _warrantyController.dispose();
    _manufacturerController.dispose();
    _countryOfOriginController.dispose();
    _tagsController.dispose();

    // Dispose specification controllers
    for (final spec in _specifications) {
      spec['key']?.dispose();
      spec['value']?.dispose();
    }

    // Dispose day-wise availability controllers
    for (final day in _dayWiseAvailability.values) {
      day['from']?.dispose();
      day['to']?.dispose();
    }

    super.dispose();
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final storageService = ref.read(storageServiceProvider);
        final imageUrl = await storageService.uploadProductImage(
          pickedFile.path,
          'products/${DateTime.now().millisecondsSinceEpoch}',
        );
        setState(() {
          _selectedImageUrls.add(imageUrl);
        });
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Failed to upload image: $e')));
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImageUrls.removeAt(index);
    });
  }

  void _addSpecification() {
    setState(() {
      _specifications.add({
        'key': TextEditingController(),
        'value': TextEditingController(),
      });
    });
  }

  void _removeSpecification(int index) {
    setState(() {
      final spec = _specifications.removeAt(index);
      spec['key']?.dispose();
      spec['value']?.dispose();
    });
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedCategory == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please select a category')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Get the current user ID
      final sellerId = ref.read(authServiceProvider).currentUser?.id;
      if (sellerId == null) {
        throw Exception('User not authenticated');
      }

      final now = DateTime.now();
      final productId = 'product_${now.millisecondsSinceEpoch}';

      // Parse tags from comma-separated string
      final tags = _tagsController.text.isEmpty
          ? <String>[]
          : _tagsController.text.split(',').map((tag) => tag.trim()).toList();

      // Build specifications map (only for non-food products)
      final specifications = <String, dynamic>{};
      if (_selectedCategory != 'food') {
        for (final spec in _specifications) {
          final key = spec['key']?.text;
          final value = spec['value']?.text;
          if (key != null &&
              key.isNotEmpty &&
              value != null &&
              value.isNotEmpty) {
            specifications[key] = value;
          }
        }
      }

      // Build day-wise availability map (only for food products)
      final dayWiseAvailability = <String, Map<String, String>>{};
      if (_selectedCategory == 'food') {
        for (final entry in _dayWiseAvailability.entries) {
          final day = entry.key;
          final times = entry.value;
          final fromTime = times['from']?.text;
          final toTime = times['to']?.text;

          if (fromTime != null &&
              fromTime.isNotEmpty &&
              toTime != null &&
              toTime.isNotEmpty) {
            dayWiseAvailability[day] = {'from': fromTime, 'to': toTime};
          }
        }
      }

      // Parse numeric values safely
      double? originalPrice;
      if (_originalPriceController.text.isNotEmpty) {
        originalPrice = double.tryParse(_originalPriceController.text);
      }

      double? weight;
      if (_weightController.text.isNotEmpty) {
        weight = double.tryParse(_weightController.text);
      }

      // Calculate discount percentage if original price is provided
      double? discountPercentage;
      if (originalPrice != null) {
        final price = double.parse(_priceController.text);
        if (originalPrice > price) {
          discountPercentage = ((originalPrice - price) / originalPrice) * 100;
        }
      }

      debugPrint(
        'Creating product with ID: $productId and seller ID: $sellerId',
      );

      // Create the base product model
      final productJson = {
        'id': productId,
        'name': _nameController.text,
        'description': _descriptionController.text,
        'price': double.parse(_priceController.text),
        'quantity': int.parse(_stockController.text),
        'categoryId': _selectedCategory!,
        'sellerId': sellerId,
        'images': _selectedImageUrls,
        'isApproved': false, // Products need admin/executor approval
        'isDeleted': false, // Make sure it's not deleted
        'tags': tags,
        'productType': ProductType.physical.index,
        'productStatus': ProductStatus.pending.index, // Set status to pending
        'createdAt': now,
        'updatedAt': now,
      };

      // Add optional fields based on category
      if (originalPrice != null) {
        productJson['originalPrice'] = originalPrice;
        if (discountPercentage != null) {
          productJson['discountPercentage'] = discountPercentage;
        }
      }

      if (_brandController.text.isNotEmpty) {
        productJson['brand'] = _brandController.text;
      }

      if (_unitController.text.isNotEmpty) {
        productJson['unit'] = _unitController.text;
      }

      if (weight != null) {
        productJson['weight'] = weight;
      }

      // Add specifications for non-food products
      if (_selectedCategory != 'food' && specifications.isNotEmpty) {
        productJson['specifications'] = specifications;
      }

      // Add highlights for non-food products
      if (_selectedCategory != 'food' &&
          _highlightsController.text.isNotEmpty) {
        // Split the text by newlines and filter out empty lines
        final highlights = _highlightsController.text
            .split('\n')
            .where((line) => line.trim().isNotEmpty)
            .toList();
        if (highlights.isNotEmpty) {
          productJson['highlights'] = highlights;
        }
      }

      // Add day-wise availability and food type for food products
      if (_selectedCategory == 'food') {
        if (dayWiseAvailability.isNotEmpty) {
          productJson['dayWiseAvailability'] = dayWiseAvailability;
        }
        // Add food type (veg/non-veg)
        productJson['foodType'] = _foodType;
      }

      final product = ProductModel.fromJson(productJson);

      debugPrint('Product JSON: ${product.toJson()}');

      await ref.read(productServiceProvider).createOrUpdateProduct(product);
      debugPrint('Product saved to hybrid database');

      // Refresh the products list
      await ref.read(productsProvider.notifier).refreshProducts();
      debugPrint('Products list refreshed');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Product added and sent for approval')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to add product: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Add Product')),
      body: _isLoading
          ? const LoadingIndicator()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Image Gallery
                    if (_selectedImageUrls.isNotEmpty)
                      SizedBox(
                        height: 200,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: _selectedImageUrls.length,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: Stack(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(12),
                                    child: Image.network(
                                      _selectedImageUrls[index],
                                      width: 200,
                                      height: 200,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                            return Container(
                                              width: 200,
                                              height: 200,
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .surfaceContainerHighest,
                                              child: Icon(
                                                Icons.error_outline,
                                                size: 48,
                                                color: Theme.of(
                                                  context,
                                                ).colorScheme.error,
                                              ),
                                            );
                                          },
                                    ),
                                  ),
                                  Positioned(
                                    top: 8,
                                    right: 8,
                                    child: IconButton(
                                      icon: const Icon(Icons.close),
                                      onPressed: () => _removeImage(index),
                                      style: IconButton.styleFrom(
                                        backgroundColor: Theme.of(
                                          context,
                                        ).colorScheme.surface,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      )
                    else
                      GestureDetector(
                        onTap: _pickImage,
                        child: Container(
                          height: 200,
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add_photo_alternate,
                                size: 48,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Tap to add product image',
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                            ],
                          ),
                        ),
                      ),
                    const SizedBox(height: 16),
                    FilledButton.icon(
                      onPressed: _pickImage,
                      icon: const Icon(Icons.add_photo_alternate),
                      label: const Text('Add More Images'),
                    ),

                    // Basic Information Section
                    const SizedBox(height: 24),
                    Text(
                      _isEventService
                          ? 'Service Package Information'
                          : 'Basic Information',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    _ProductNameField(controller: _nameController),
                    const SizedBox(height: 16),
                    _ProductDescriptionField(
                      controller: _descriptionController,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _priceController,
                            decoration: InputDecoration(
                              labelText: _isEventService
                                  ? 'Service Price'
                                  : 'Selling Price',
                              border: const OutlineInputBorder(),
                              prefixText: '₹',
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter product price';
                              }
                              if (double.tryParse(value) == null) {
                                return 'Please enter a valid price';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _originalPriceController,
                            decoration: const InputDecoration(
                              labelText: 'MRP/Original Price',
                              border: OutlineInputBorder(),
                              prefixText: '₹',
                              helperText: 'Leave empty if no discount',
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                final originalPrice = double.tryParse(value);
                                if (originalPrice == null) {
                                  return 'Please enter a valid price';
                                }

                                final sellingPrice = double.tryParse(
                                  _priceController.text,
                                );
                                if (sellingPrice != null &&
                                    originalPrice < sellingPrice) {
                                  return 'MRP should be >= selling price';
                                }
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _stockController,
                            decoration: InputDecoration(
                              labelText: _isEventService
                                  ? 'Availability Count'
                                  : 'Stock Quantity',
                              border: const OutlineInputBorder(),
                              helperText: _isEventService
                                  ? 'How many bookings can you handle simultaneously?'
                                  : null,
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter stock quantity';
                              }
                              if (int.tryParse(value) == null) {
                                return 'Please enter a valid number';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _CategoryDropdown(
                            value: _selectedCategory,
                            onChanged: (value) {
                              final oldCategory = _selectedCategory;
                              setState(() {
                                _selectedCategory = value;
                              });

                              // Show a snackbar when changing to or from food category
                              if (value == 'food' && oldCategory != 'food') {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Food products only require availability timings. Product details section has been hidden.',
                                    ),
                                    duration: Duration(seconds: 4),
                                  ),
                                );
                              } else if (value != 'food' &&
                                  oldCategory == 'food') {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Product details section is now visible for non-food products.',
                                    ),
                                    duration: Duration(seconds: 4),
                                  ),
                                );
                              }
                            },
                          ),
                        ),
                      ],
                    ),

                    // Product Details Section (only for non-food products)
                    if (_selectedCategory != 'food') ...[
                      const SizedBox(height: 24),
                      Text(
                        'Product Details',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _brandController,
                              decoration: const InputDecoration(
                                labelText: 'Brand',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              controller: _manufacturerController,
                              decoration: const InputDecoration(
                                labelText: 'Manufacturer',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _unitController.text.isEmpty
                                  ? null
                                  : _unitController.text,
                              decoration: const InputDecoration(
                                labelText: 'Unit',
                                border: OutlineInputBorder(),
                              ),
                              items: ProductConstants.units
                                  .map(
                                    (unit) => DropdownMenuItem<String>(
                                      value: unit,
                                      child: Text(unit),
                                    ),
                                  )
                                  .toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _unitController.text = value;
                                  });
                                }
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              controller: _weightController,
                              decoration: const InputDecoration(
                                labelText: 'Weight',
                                border: OutlineInputBorder(),
                                helperText: 'In kg',
                              ),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<String>(
                        value: _countryOfOriginController.text.isEmpty
                            ? null
                            : _countryOfOriginController.text,
                        decoration: const InputDecoration(
                          labelText: 'Country of Origin',
                          border: OutlineInputBorder(),
                        ),
                        items: ProductConstants.countries
                            .map(
                              (country) => DropdownMenuItem<String>(
                                value: country,
                                child: Text(country),
                              ),
                            )
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _countryOfOriginController.text = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _warrantyController,
                        decoration: const InputDecoration(
                          labelText: 'Warranty Information',
                          border: OutlineInputBorder(),
                          hintText: 'E.g., 1 Year Manufacturer Warranty',
                        ),
                      ),
                      const SizedBox(height: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Product Highlights',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Add key features of your product (one per line)',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _highlightsController,
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              hintText:
                                  'e.g.\nWaterproof\nDurable\nEasy to clean',
                            ),
                            maxLines: 5,
                            keyboardType: TextInputType.multiline,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Each line will be displayed as a separate bullet point',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: Colors.grey[600],
                                  fontStyle: FontStyle.italic,
                                ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _tagsController,
                        decoration: const InputDecoration(
                          labelText: 'Tags (comma separated)',
                          border: OutlineInputBorder(),
                          hintText: 'E.g., organic, fresh, premium',
                        ),
                      ),

                      // Specifications Section
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Specifications',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          IconButton(
                            onPressed: _addSpecification,
                            icon: const Icon(Icons.add_circle),
                            tooltip: 'Add Specification',
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      ...List.generate(
                        _specifications.length,
                        (index) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _specifications[index]['key'],
                                  decoration: const InputDecoration(
                                    labelText: 'Specification',
                                    border: OutlineInputBorder(),
                                    hintText: 'E.g., Material',
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextFormField(
                                  controller: _specifications[index]['value'],
                                  decoration: const InputDecoration(
                                    labelText: 'Value',
                                    border: OutlineInputBorder(),
                                    hintText: 'E.g., Cotton',
                                  ),
                                ),
                              ),
                              IconButton(
                                onPressed:
                                    index == 0 && _specifications.length == 1
                                    ? null
                                    : () => _removeSpecification(index),
                                icon: const Icon(Icons.remove_circle),
                                tooltip: 'Remove Specification',
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],

                    // Day-wise Availability Section (only for food products)
                    if (_selectedCategory == 'food') ...[
                      const SizedBox(height: 24),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              'Day-wise Availability Timings',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                          ),
                          Icon(
                            Icons.info_outline,
                            color: Theme.of(context).colorScheme.primary,
                            size: 20,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(
                            context,
                          ).colorScheme.surfaceContainerLowest,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Theme.of(
                              context,
                            ).colorScheme.outline.withAlpha(40),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'For food products, only availability timings are required. Product details are not needed.',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Vegetarian/Non-vegetarian selector
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              'Food Type',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _foodType,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                              ),
                              items: const [
                                DropdownMenuItem(
                                  value: 'veg',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.circle,
                                        color: Colors.green,
                                        size: 16,
                                      ),
                                      SizedBox(width: 2),
                                      Text('Vegetarian'),
                                    ],
                                  ),
                                ),
                                DropdownMenuItem(
                                  value: 'non-veg',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.circle,
                                        color: Colors.red,
                                        size: 16,
                                      ),
                                      SizedBox(width: 2),
                                      Text('Non-Vegetarian'),
                                    ],
                                  ),
                                ),
                              ],
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _foodType = value;
                                  });
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      ..._dayWiseAvailability.entries.map((entry) {
                        final day = entry.key;
                        final times = entry.value;
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                day,
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: TextFormField(
                                      controller: times['from'],
                                      decoration: const InputDecoration(
                                        labelText: 'From',
                                        border: OutlineInputBorder(),
                                        hintText: 'E.g., 9:00 AM',
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: TextFormField(
                                      controller: times['to'],
                                      decoration: const InputDecoration(
                                        labelText: 'To',
                                        border: OutlineInputBorder(),
                                        hintText: 'E.g., 5:00 PM',
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      }),
                    ],

                    // Approval Note
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.outline.withAlpha(40),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'All new products require approval before they become visible to buyers.',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Submit Button
                    const SizedBox(height: 32),
                    FilledButton.icon(
                      onPressed: _submitForm,
                      icon: Icon(_isEventService ? Icons.event : Icons.add),
                      label: Text(
                        _isEventService ? 'Add Service Package' : 'Add Product',
                      ),
                      style: FilledButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}

class _ProductNameField extends ConsumerWidget {
  final TextEditingController controller;

  const _ProductNameField({required this.controller});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sellerAsync = ref.read(sellerProvider);
    final isEventService = sellerAsync.value?.category.isEventService ?? false;

    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: isEventService ? 'Service Package Name' : 'Product Name',
        border: const OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return isEventService
              ? 'Please enter service package name'
              : 'Please enter product name';
        }
        return null;
      },
    );
  }
}

class _ProductDescriptionField extends ConsumerWidget {
  final TextEditingController controller;

  const _ProductDescriptionField({required this.controller});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sellerAsync = ref.read(sellerProvider);
    final isEventService = sellerAsync.value?.category.isEventService ?? false;

    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: isEventService ? 'Service Description' : 'Description',
        border: const OutlineInputBorder(),
        helperText: isEventService
            ? 'Describe your service package, what\'s included, and any special features'
            : null,
      ),
      maxLines: 3,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return isEventService
              ? 'Please enter service description'
              : 'Please enter product description';
        }
        return null;
      },
    );
  }
}

class _CategoryDropdown extends StatelessWidget {
  final String? value;
  final void Function(String?) onChanged;

  const _CategoryDropdown({required this.value, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: const InputDecoration(
        labelText: 'Category',
        border: OutlineInputBorder(),
      ),
      items: const [
        // Basic categories
        DropdownMenuItem(value: 'electronics', child: Text('Electronics')),
        DropdownMenuItem(value: 'clothing', child: Text('Clothing')),
        DropdownMenuItem(value: 'food', child: Text('Food')),
        DropdownMenuItem(value: 'health', child: Text('Health')),
        DropdownMenuItem(value: 'beauty', child: Text('Beauty')),
        DropdownMenuItem(value: 'home', child: Text('Home')),
        DropdownMenuItem(value: 'books', child: Text('Books')),
        DropdownMenuItem(value: 'toys', child: Text('Toys')),

        // Food subcategories
        DropdownMenuItem(value: 'vegetables', child: Text('Vegetables')),
        DropdownMenuItem(value: 'fruits', child: Text('Fruits')),
        DropdownMenuItem(value: 'grocery', child: Text('Grocery')),
        DropdownMenuItem(value: 'bakery', child: Text('Bakery')),
        DropdownMenuItem(value: 'dairy', child: Text('Dairy')),
        DropdownMenuItem(value: 'meat', child: Text('Meat')),

        // Additional categories
        DropdownMenuItem(value: 'sports', child: Text('Sports')),
        DropdownMenuItem(value: 'furniture', child: Text('Furniture')),
        DropdownMenuItem(value: 'garden', child: Text('Garden')),
        DropdownMenuItem(value: 'pets', child: Text('Pets')),
        DropdownMenuItem(value: 'auto', child: Text('Auto')),
        DropdownMenuItem(value: 'education', child: Text('Education')),
        DropdownMenuItem(value: 'gaming', child: Text('Gaming')),
        DropdownMenuItem(value: 'other', child: Text('Other')),
      ],
      onChanged: onChanged,
      validator: (value) {
        if (value == null) {
          return 'Please select a category';
        }
        return null;
      },
    );
  }
}
