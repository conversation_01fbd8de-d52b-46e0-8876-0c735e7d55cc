import 'package:flutter/foundation.dart';
import 'package:shivish/apps/seller/domain/models/promotion_model.dart';
import 'package:shivish/apps/seller/domain/models/campaign_model.dart';
import 'package:shivish/apps/seller/domain/repositories/marketing_repository.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';

class MarketingRepositoryImpl implements MarketingRepository {
  final DatabaseService _databaseService;
  final AuthService _authService;

  MarketingRepositoryImpl(this._databaseService, this._authService);

  @override
  Future<List<PromotionModel>> getPromotions() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final promotions = await _databaseService.getAll(
        'promotions',
        where: 'seller_id = @param0 AND is_deleted = @param1',
        whereParams: [userId, false],
        orderBy: 'created_at DESC',
      );

      return promotions
          .map((data) => PromotionModel.fromJson(data))
          .toList();
    } catch (e) {
      debugPrint('Error getting promotions: $e');
      rethrow;
    }
  }

  @override
  Future<void> createPromotion(PromotionModel promotion) async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final data = promotion.toJson();
      data['seller_id'] = userId;
      data['created_at'] = DateTime.now().toIso8601String();
      data['updated_at'] = DateTime.now().toIso8601String();

      await _databaseService.create('promotions', data);
    } catch (e) {
      debugPrint('Error creating promotion: $e');
      rethrow;
    }
  }

  @override
  Future<void> updatePromotion(PromotionModel promotion) async {
    try {
      final data = promotion.toJson();
      data['updated_at'] = DateTime.now().toIso8601String();
      data.remove('id'); // Remove ID from update data

      await _databaseService.update('promotions', promotion.id, data);
    } catch (e) {
      debugPrint('Error updating promotion: $e');
      rethrow;
    }
  }

  @override
  Future<void> deletePromotion(String id) async {
    try {
      await _databaseService.update('promotions', id, {
        'is_deleted': true,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error deleting promotion: $e');
      rethrow;
    }
  }

  @override
  Future<List<CampaignModel>> getCampaigns() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final campaigns = await _databaseService.getAll(
        'campaigns',
        where: 'seller_id = @param0 AND is_deleted = @param1',
        whereParams: [userId, false],
        orderBy: 'created_at DESC',
      );

      return campaigns
          .map((data) => CampaignModel.fromJson(data))
          .toList();
    } catch (e) {
      debugPrint('Error getting campaigns: $e');
      rethrow;
    }
  }

  @override
  Future<void> createCampaign(CampaignModel campaign) async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final data = campaign.toJson();
      data['seller_id'] = userId;
      data['created_at'] = DateTime.now().toIso8601String();
      data['updated_at'] = DateTime.now().toIso8601String();

      await _databaseService.create('campaigns', data);
    } catch (e) {
      debugPrint('Error creating campaign: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateCampaign(CampaignModel campaign) async {
    try {
      final data = campaign.toJson();
      data['updated_at'] = DateTime.now().toIso8601String();
      data.remove('id'); // Remove ID from update data

      await _databaseService.update('campaigns', campaign.id, data);
    } catch (e) {
      debugPrint('Error updating campaign: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteCampaign(String id) async {
    try {
      await _databaseService.update('campaigns', id, {
        'is_deleted': true,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error deleting campaign: $e');
      rethrow;
    }
  }

  Future<void> togglePromotionStatus(String id, bool isActive) async {
    try {
      await _databaseService.update('promotions', id, {
        'is_active': isActive,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error toggling promotion status: $e');
      rethrow;
    }
  }

  Future<void> updateCampaignStatus(String id, CampaignStatus status) async {
    try {
      await _databaseService.update('campaigns', id, {
        'status': status.name,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error updating campaign status: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateCampaignMetrics(
    String id, {
    int? impressions,
    int? clicks,
    int? conversions,
    double? spent,
  }) async {
    try {
      // Get current campaign data to calculate increments
      final currentData = await _databaseService.find('campaigns', id);
      if (currentData == null) {
        throw Exception('Campaign not found');
      }

      final updates = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (impressions != null) {
        final currentImpressions = currentData['impressions'] as int? ?? 0;
        updates['impressions'] = currentImpressions + impressions;
      }
      if (clicks != null) {
        final currentClicks = currentData['clicks'] as int? ?? 0;
        updates['clicks'] = currentClicks + clicks;
      }
      if (conversions != null) {
        final currentConversions = currentData['conversions'] as int? ?? 0;
        updates['conversions'] = currentConversions + conversions;
      }
      if (spent != null) {
        final currentSpent = (currentData['spent'] as num?)?.toDouble() ?? 0.0;
        updates['spent'] = currentSpent + spent;
      }

      await _databaseService.update('campaigns', id, updates);
    } catch (e) {
      debugPrint('Error updating campaign metrics: $e');
      rethrow;
    }
  }
}
