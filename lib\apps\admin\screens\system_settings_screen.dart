import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../admin_routes.dart';

class SystemSettingsScreen extends StatelessWidget {
  const SystemSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('System Settings'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              // Check if we can pop the current route
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              } else {
                // If we can't pop, use GoRouter to navigate back to home
                context.go(AdminRoutes.home);
              }
            },
          ),
        ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSection(
            context,
            'Content Management',
            [
              _buildSettingTile(
                context,
                'Product Lists',
                'Manage location-based product lists for buyers',
                Icons.shopping_cart,
                () => context.go(AdminRoutes.productLists),
              ),
              _buildSettingTile(
                context,
                'Products',
                'Manage products in the system',
                Icons.inventory,
                () => context.go(AdminRoutes.products),
              ),
              _buildSettingTile(
                context,
                'Events',
                'Manage events in the system',
                Icons.event,
                () => context.go(AdminRoutes.events),
              ),
              _buildSettingTile(
                context,
                'Media',
                'Manage media files',
                Icons.perm_media,
                () => context.go(AdminRoutes.media),
              ),
              _buildSettingTile(
                context,
                'Banners',
                'Manage promotional banners',
                Icons.view_carousel,
                () => context.go(AdminRoutes.banners),
              ),
              _buildSettingTile(
                context,
                'Bank Offers',
                'Manage bank offers and promotions',
                Icons.local_offer,
                () => context.go(AdminRoutes.bankOffers),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            context,
            'System Configuration',
            [
              _buildSettingTile(
                context,
                'Security',
                'Manage security settings',
                Icons.security,
                () => context.go(AdminRoutes.securitySettings),
              ),
              _buildSettingTile(
                context,
                'Analytics',
                'Configure analytics settings',
                Icons.analytics,
                () => context.go(AdminRoutes.analyticsSettings),
              ),
              _buildSettingTile(
                context,
                'PhonePe Gateway',
                'Configure PhonePe payment gateway settings',
                Icons.payment,
                () => context.go(AdminRoutes.phonePeSettings),
              ),
              _buildSettingTile(
                context,
                'App Configuration',
                'Configure app settings',
                Icons.settings_applications,
                () => context.go(AdminRoutes.appConfig),
              ),
              _buildSettingTile(
                context,
                'System Configuration',
                'Configure system settings',
                Icons.settings_system_daydream,
                () => context.go(AdminRoutes.systemConfig),
              ),
              _buildSettingTile(
                context,
                'Registration Settings',
                'Configure user registration requirements',
                Icons.app_registration,
                () => context.go(AdminRoutes.registrationSettings),
              ),
              _buildSettingTile(
                context,
                'Delivery Settings',
                'Configure delivery providers and options',
                Icons.local_shipping,
                () => context.go(AdminRoutes.deliverySettings),
              ),
              _buildSettingTile(
                context,
                'Delivery Charges',
                'Configure delivery charges for different areas',
                Icons.attach_money,
                () => context.go(AdminRoutes.deliveryCharges),
              ),
              _buildSettingTile(
                context,
                'Webhook Endpoints',
                'Configure delivery tracking webhooks',
                Icons.webhook,
                () => context.go(AdminRoutes.webhookEndpoints),
              ),
              _buildSettingTile(
                context,
                'Tax Rates',
                'Manage GST rates for different product categories',
                Icons.receipt_long,
                () => context.go(AdminRoutes.taxRates),
              ),
              _buildSettingTile(
                context,
                'Storage Settings',
                'Configure storage providers for documents, images, and media',
                Icons.storage,
                () => context.go(AdminRoutes.storageSettings),
              ),
              _buildSettingTile(
                context,
                'Vehicle Fare Management',
                'Configure fare rates for different vehicle types',
                Icons.directions_car,
                () => context.go(AdminRoutes.vehicleFareManagement),
                isNew: true,
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            context,
            'Appearance',
            [
              _buildSettingTile(
                context,
                'Icon Management',
                'Manage app icons',
                Icons.image,
                () => context.go(AdminRoutes.iconManagement),
              ),
              _buildSettingTile(
                context,
                'Banner Pricing',
                'Configure banner pricing',
                Icons.monetization_on,
                () => context.go(AdminRoutes.bannerPricing),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSection(
            context,
            'AI Settings',
            [
              _buildSettingTile(
                context,
                'AI Assistant',
                'Configure AI assistant settings',
                Icons.smart_toy,
                () => context.go(AdminRoutes.aiAssistantSettings),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
      BuildContext context, String title, List<Widget> children) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Card(
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    bool isNew = false,
  }) {
    return Stack(
      children: [
        ListTile(
          leading: Icon(icon),
          title: Text(title),
          subtitle: Text(subtitle),
          trailing: const Icon(Icons.chevron_right),
          onTap: onTap,
        ),
        if (isNew)
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(4),
                  bottomLeft: Radius.circular(8),
                ),
              ),
              child: Text(
                'New',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
