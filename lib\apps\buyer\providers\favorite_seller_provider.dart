import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/favorite_seller.dart';
import '../services/favorite_seller_service.dart';

/// Provider for the favorite seller service
final favoriteSellerServiceProvider = Provider<FavoriteSellerService>((ref) {
  return FavoriteSellerService();
});

/// Provider for the list of favorite sellers
final favoriteSellersProvider = FutureProvider<List<FavoriteSeller>>((ref) async {
  final service = ref.read(favoriteSellerServiceProvider);
  return service.getFavoriteSellers();
});

/// Provider to check if a seller is in favorites
final isSellerFavoriteProvider = FutureProvider.family<bool, String>((ref, sellerId) async {
  final service = ref.read(favoriteSellerServiceProvider);
  return service.isFavoriteSeller(sellerId);
});

/// Notifier for managing favorite sellers
class FavoriteSellerNotifier extends StateNotifier<AsyncValue<List<FavoriteSeller>>> {
  final FavoriteSellerService _service;
  
  FavoriteSellerNotifier(this._service) : super(const AsyncValue.loading()) {
    _loadFavoriteSellers();
  }
  
  Future<void> _loadFavoriteSellers() async {
    try {
      final sellers = await _service.getFavoriteSellers();
      state = AsyncValue.data(sellers);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  Future<bool> addFavoriteSeller(FavoriteSeller seller) async {
    try {
      final success = await _service.addFavoriteSeller(seller);
      if (success) {
        _loadFavoriteSellers();
      }
      return success;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return false;
    }
  }
  
  Future<bool> removeFavoriteSeller(String sellerId) async {
    try {
      final success = await _service.removeFavoriteSeller(sellerId);
      if (success) {
        _loadFavoriteSellers();
      }
      return success;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return false;
    }
  }
  
  Future<bool> toggleFavoriteSeller(FavoriteSeller seller) async {
    try {
      final isFavorite = await _service.isFavoriteSeller(seller.id);
      if (isFavorite) {
        return await removeFavoriteSeller(seller.id);
      } else {
        return await addFavoriteSeller(seller);
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return false;
    }
  }
}

/// Provider for the favorite seller notifier
final favoriteSellerNotifierProvider = StateNotifierProvider<FavoriteSellerNotifier, AsyncValue<List<FavoriteSeller>>>((ref) {
  final service = ref.read(favoriteSellerServiceProvider);
  return FavoriteSellerNotifier(service);
});
