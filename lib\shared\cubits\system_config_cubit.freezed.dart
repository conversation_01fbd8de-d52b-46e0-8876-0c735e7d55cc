// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'system_config_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SystemConfigState {
  bool get isLoading => throw _privateConstructorUsedError;
  SystemConfigModel? get config => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of SystemConfigState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SystemConfigStateCopyWith<SystemConfigState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SystemConfigStateCopyWith<$Res> {
  factory $SystemConfigStateCopyWith(
          SystemConfigState value, $Res Function(SystemConfigState) then) =
      _$SystemConfigStateCopyWithImpl<$Res, SystemConfigState>;
  @useResult
  $Res call({bool isLoading, SystemConfigModel? config, String? error});

  $SystemConfigModelCopyWith<$Res>? get config;
}

/// @nodoc
class _$SystemConfigStateCopyWithImpl<$Res, $Val extends SystemConfigState>
    implements $SystemConfigStateCopyWith<$Res> {
  _$SystemConfigStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SystemConfigState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? config = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      config: freezed == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as SystemConfigModel?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of SystemConfigState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SystemConfigModelCopyWith<$Res>? get config {
    if (_value.config == null) {
      return null;
    }

    return $SystemConfigModelCopyWith<$Res>(_value.config!, (value) {
      return _then(_value.copyWith(config: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SystemConfigStateImplCopyWith<$Res>
    implements $SystemConfigStateCopyWith<$Res> {
  factory _$$SystemConfigStateImplCopyWith(_$SystemConfigStateImpl value,
          $Res Function(_$SystemConfigStateImpl) then) =
      __$$SystemConfigStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isLoading, SystemConfigModel? config, String? error});

  @override
  $SystemConfigModelCopyWith<$Res>? get config;
}

/// @nodoc
class __$$SystemConfigStateImplCopyWithImpl<$Res>
    extends _$SystemConfigStateCopyWithImpl<$Res, _$SystemConfigStateImpl>
    implements _$$SystemConfigStateImplCopyWith<$Res> {
  __$$SystemConfigStateImplCopyWithImpl(_$SystemConfigStateImpl _value,
      $Res Function(_$SystemConfigStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SystemConfigState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? config = freezed,
    Object? error = freezed,
  }) {
    return _then(_$SystemConfigStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      config: freezed == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as SystemConfigModel?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$SystemConfigStateImpl implements _SystemConfigState {
  const _$SystemConfigStateImpl(
      {this.isLoading = false, this.config = null, this.error = null});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final SystemConfigModel? config;
  @override
  @JsonKey()
  final String? error;

  @override
  String toString() {
    return 'SystemConfigState(isLoading: $isLoading, config: $config, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SystemConfigStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.config, config) || other.config == config) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading, config, error);

  /// Create a copy of SystemConfigState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SystemConfigStateImplCopyWith<_$SystemConfigStateImpl> get copyWith =>
      __$$SystemConfigStateImplCopyWithImpl<_$SystemConfigStateImpl>(
          this, _$identity);
}

abstract class _SystemConfigState implements SystemConfigState {
  const factory _SystemConfigState(
      {final bool isLoading,
      final SystemConfigModel? config,
      final String? error}) = _$SystemConfigStateImpl;

  @override
  bool get isLoading;
  @override
  SystemConfigModel? get config;
  @override
  String? get error;

  /// Create a copy of SystemConfigState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SystemConfigStateImplCopyWith<_$SystemConfigStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
