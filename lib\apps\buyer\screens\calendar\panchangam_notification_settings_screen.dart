import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/providers/panchangam_notification_provider.dart';
import '../../../../shared/providers/panchangam_notification_settings_provider.dart';

class PanchangamNotificationSettingsScreen extends ConsumerStatefulWidget {
  const PanchangamNotificationSettingsScreen({super.key});

  @override
  ConsumerState<PanchangamNotificationSettingsScreen> createState() =>
      _PanchangamNotificationSettingsScreenState();
}

class _PanchangamNotificationSettingsScreenState
    extends ConsumerState<PanchangamNotificationSettingsScreen> {
  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final settings = ref.read(panchangamNotificationSettingsProvider);
    if (settings.isEnabled) {
      await ref
          .read(panchangamNotificationServiceProvider)
          .scheduleDailyPanchangamNotification(
            time: TimeOfDay(
              hour: settings.notificationHour,
              minute: settings.notificationMinute,
            ),
            language: settings.language,
            location: settings.location,
          );
    }
  }

  Future<void> _selectTime() async {
    final settings = ref.read(panchangamNotificationSettingsProvider);
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(
        hour: settings.notificationHour,
        minute: settings.notificationMinute,
      ),
    );
    if (picked != null) {
      await ref
          .read(panchangamNotificationSettingsProvider.notifier)
          .updateNotificationTime(picked.hour, picked.minute);
      if (settings.isEnabled) {
        await ref
            .read(panchangamNotificationServiceProvider)
            .scheduleDailyPanchangamNotification(
              time: picked,
              language: settings.language,
              location: settings.location,
            );
      }
    }
  }

  Future<void> _toggleEnabled(bool value) async {
    final settings = ref.read(panchangamNotificationSettingsProvider);
    await ref
        .read(panchangamNotificationSettingsProvider.notifier)
        .toggleEnabled();
    if (value) {
      await ref
          .read(panchangamNotificationServiceProvider)
          .scheduleDailyPanchangamNotification(
            time: TimeOfDay(
              hour: settings.notificationHour,
              minute: settings.notificationMinute,
            ),
            language: settings.language,
            location: settings.location,
          );
    } else {
      await ref
          .read(panchangamNotificationServiceProvider)
          .cancelAllNotifications();
    }
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(panchangamNotificationSettingsProvider);

    return PopScope(
        canPop: true,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop && context.mounted) {
            Navigator.of(context).pop();
          }
        },
        child: Scaffold(
          appBar: AppBar(
            title: const Text('Panchangam Notifications'),
          ),
          body: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              SwitchListTile(
                title: const Text('Enable Daily Panchangam'),
                subtitle: const Text('Receive daily Panchangam updates'),
                value: settings.isEnabled,
                onChanged: _toggleEnabled,
              ),
              if (settings.isEnabled) ...[
                const SizedBox(height: 16),
                ListTile(
                  title: const Text('Notification Time'),
                  subtitle: Text(
                    TimeOfDay(
                      hour: settings.notificationHour,
                      minute: settings.notificationMinute,
                    ).format(context),
                  ),
                  trailing: const Icon(Icons.access_time),
                  onTap: _selectTime,
                ),
                const SizedBox(height: 16),
                const Text(
                  'The notification will include:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                _buildNotificationContentItem(
                  'Tithi and Nakshatra',
                  'Current lunar day and star',
                ),
                _buildNotificationContentItem(
                  'Sunrise and Sunset',
                  'Daily timing information',
                ),
                _buildNotificationContentItem(
                  'Festivals and Vratas',
                  'Important religious events',
                ),
                _buildNotificationContentItem(
                  'Auspicious Activities',
                  'Recommended activities for the day',
                ),
              ],
            ],
          ),
        ));
  }

  Widget _buildNotificationContentItem(String title, String subtitle) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Colors.green),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
