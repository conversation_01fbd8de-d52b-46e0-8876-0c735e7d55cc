import 'package:freezed_annotation/freezed_annotation.dart';

part 'api_config.g.dart';
part 'api_config.freezed.dart';

@freezed
sealed class ApiConfig with _$ApiConfig {
  const factory ApiConfig({
    @Default('https://api.example.com') String baseUrl,
    @Default(30000) int connectTimeout,
    @Default(30000) int receiveTimeout,
    @Default(30000) int sendTimeout,
    @Default(3) int maxRetries,
    @Default(true) bool enableLogging,
    @Default(true) bool enableCaching,
    @Default(Duration(hours: 1)) Duration cacheDuration,
    @Default(10 * 1024 * 1024) int maxCacheSize, // 10MB
    @Default({}) Map<String, String> defaultHeaders,
    @Default(ApiEnvironment.development) ApiEnvironment environment,
  }) = _ApiConfig;

  factory ApiConfig.fromJson(Map<String, dynamic> json) =>
      _$ApiConfigFromJson(json);

  /// Production configuration
  factory ApiConfig.production() => const ApiConfig(
        baseUrl: 'https://api.production.example.com',
        environment: ApiEnvironment.production,
        enableLogging: false,
      );

  /// Development configuration
  factory ApiConfig.development() => const ApiConfig(
        baseUrl: 'https://api.dev.example.com',
        environment: ApiEnvironment.development,
        enableLogging: true,
      );

  /// Staging configuration
  factory ApiConfig.staging() => const ApiConfig(
        baseUrl: 'https://api.staging.example.com',
        environment: ApiEnvironment.staging,
        enableLogging: true,
      );
}

enum ApiEnvironment {
  @JsonValue('development')
  development,
  @JsonValue('staging')
  staging,
  @JsonValue('production')
  production,
}
