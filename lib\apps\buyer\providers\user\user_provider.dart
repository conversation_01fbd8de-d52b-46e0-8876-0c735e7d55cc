import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/user/user_model.dart';
import '../../repositories/user_repository.dart';

final userProvider =
    StateNotifierProvider<UserNotifier, AsyncValue<UserModel?>>(
  (ref) => UserNotifier(ref.watch(userRepositoryProvider)),
);

class UserNotifier extends StateNotifier<AsyncValue<UserModel?>> {
  final UserRepository _repository;

  UserNotifier(this._repository) : super(const AsyncValue.loading()) {
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    try {
      final user = await _repository.getCurrentUser();
      state = AsyncValue.data(user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Public method to refresh the current user
  Future<void> refreshUser() async {
    state = const AsyncValue.loading();
    await _loadCurrentUser();
  }

  Future<UserModel?> getUserById(String userId) async {
    try {
      final user = await _repository.getUserById(userId);
      return user;
    } catch (error) {
      return null;
    }
  }

  Future<UserModel?> getUserByEmail(String email) async {
    try {
      final user = await _repository.getUserByEmail(email);
      return user;
    } catch (error) {
      return null;
    }
  }

  Future<void> updateUser(UserModel user) async {
    try {
      await _repository.updateUser(user);
      state = AsyncValue.data(user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteUser() async {
    try {
      await _repository.deleteUser();
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
