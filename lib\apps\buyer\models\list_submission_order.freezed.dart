// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'list_submission_order.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ListSubmissionOrder implements DiagnosticableTreeMixin {

 String get id; String get submissionId; String get buyerId; String get sellerId; double get totalAmount; List<ListSubmissionOrderItem> get items; DateTime get createdAt; bool get isPaid; String? get paymentId; String? get deliveryId; String get status;
/// Create a copy of ListSubmissionOrder
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ListSubmissionOrderCopyWith<ListSubmissionOrder> get copyWith => _$ListSubmissionOrderCopyWithImpl<ListSubmissionOrder>(this as ListSubmissionOrder, _$identity);

  /// Serializes this ListSubmissionOrder to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ListSubmissionOrder'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('submissionId', submissionId))..add(DiagnosticsProperty('buyerId', buyerId))..add(DiagnosticsProperty('sellerId', sellerId))..add(DiagnosticsProperty('totalAmount', totalAmount))..add(DiagnosticsProperty('items', items))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('isPaid', isPaid))..add(DiagnosticsProperty('paymentId', paymentId))..add(DiagnosticsProperty('deliveryId', deliveryId))..add(DiagnosticsProperty('status', status));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ListSubmissionOrder&&(identical(other.id, id) || other.id == id)&&(identical(other.submissionId, submissionId) || other.submissionId == submissionId)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&const DeepCollectionEquality().equals(other.items, items)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.isPaid, isPaid) || other.isPaid == isPaid)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.deliveryId, deliveryId) || other.deliveryId == deliveryId)&&(identical(other.status, status) || other.status == status));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,submissionId,buyerId,sellerId,totalAmount,const DeepCollectionEquality().hash(items),createdAt,isPaid,paymentId,deliveryId,status);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ListSubmissionOrder(id: $id, submissionId: $submissionId, buyerId: $buyerId, sellerId: $sellerId, totalAmount: $totalAmount, items: $items, createdAt: $createdAt, isPaid: $isPaid, paymentId: $paymentId, deliveryId: $deliveryId, status: $status)';
}


}

/// @nodoc
abstract mixin class $ListSubmissionOrderCopyWith<$Res>  {
  factory $ListSubmissionOrderCopyWith(ListSubmissionOrder value, $Res Function(ListSubmissionOrder) _then) = _$ListSubmissionOrderCopyWithImpl;
@useResult
$Res call({
 String id, String submissionId, String buyerId, String sellerId, double totalAmount, List<ListSubmissionOrderItem> items, DateTime createdAt, bool isPaid, String? paymentId, String? deliveryId, String status
});




}
/// @nodoc
class _$ListSubmissionOrderCopyWithImpl<$Res>
    implements $ListSubmissionOrderCopyWith<$Res> {
  _$ListSubmissionOrderCopyWithImpl(this._self, this._then);

  final ListSubmissionOrder _self;
  final $Res Function(ListSubmissionOrder) _then;

/// Create a copy of ListSubmissionOrder
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? submissionId = null,Object? buyerId = null,Object? sellerId = null,Object? totalAmount = null,Object? items = null,Object? createdAt = null,Object? isPaid = null,Object? paymentId = freezed,Object? deliveryId = freezed,Object? status = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,submissionId: null == submissionId ? _self.submissionId : submissionId // ignore: cast_nullable_to_non_nullable
as String,buyerId: null == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,items: null == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<ListSubmissionOrderItem>,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,isPaid: null == isPaid ? _self.isPaid : isPaid // ignore: cast_nullable_to_non_nullable
as bool,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,deliveryId: freezed == deliveryId ? _self.deliveryId : deliveryId // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [ListSubmissionOrder].
extension ListSubmissionOrderPatterns on ListSubmissionOrder {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ListSubmissionOrder value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ListSubmissionOrder() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ListSubmissionOrder value)  $default,){
final _that = this;
switch (_that) {
case _ListSubmissionOrder():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ListSubmissionOrder value)?  $default,){
final _that = this;
switch (_that) {
case _ListSubmissionOrder() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String submissionId,  String buyerId,  String sellerId,  double totalAmount,  List<ListSubmissionOrderItem> items,  DateTime createdAt,  bool isPaid,  String? paymentId,  String? deliveryId,  String status)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ListSubmissionOrder() when $default != null:
return $default(_that.id,_that.submissionId,_that.buyerId,_that.sellerId,_that.totalAmount,_that.items,_that.createdAt,_that.isPaid,_that.paymentId,_that.deliveryId,_that.status);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String submissionId,  String buyerId,  String sellerId,  double totalAmount,  List<ListSubmissionOrderItem> items,  DateTime createdAt,  bool isPaid,  String? paymentId,  String? deliveryId,  String status)  $default,) {final _that = this;
switch (_that) {
case _ListSubmissionOrder():
return $default(_that.id,_that.submissionId,_that.buyerId,_that.sellerId,_that.totalAmount,_that.items,_that.createdAt,_that.isPaid,_that.paymentId,_that.deliveryId,_that.status);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String submissionId,  String buyerId,  String sellerId,  double totalAmount,  List<ListSubmissionOrderItem> items,  DateTime createdAt,  bool isPaid,  String? paymentId,  String? deliveryId,  String status)?  $default,) {final _that = this;
switch (_that) {
case _ListSubmissionOrder() when $default != null:
return $default(_that.id,_that.submissionId,_that.buyerId,_that.sellerId,_that.totalAmount,_that.items,_that.createdAt,_that.isPaid,_that.paymentId,_that.deliveryId,_that.status);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ListSubmissionOrder with DiagnosticableTreeMixin implements ListSubmissionOrder {
  const _ListSubmissionOrder({required this.id, required this.submissionId, required this.buyerId, required this.sellerId, required this.totalAmount, required final  List<ListSubmissionOrderItem> items, required this.createdAt, this.isPaid = false, this.paymentId, this.deliveryId, this.status = 'pending'}): _items = items;
  factory _ListSubmissionOrder.fromJson(Map<String, dynamic> json) => _$ListSubmissionOrderFromJson(json);

@override final  String id;
@override final  String submissionId;
@override final  String buyerId;
@override final  String sellerId;
@override final  double totalAmount;
 final  List<ListSubmissionOrderItem> _items;
@override List<ListSubmissionOrderItem> get items {
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_items);
}

@override final  DateTime createdAt;
@override@JsonKey() final  bool isPaid;
@override final  String? paymentId;
@override final  String? deliveryId;
@override@JsonKey() final  String status;

/// Create a copy of ListSubmissionOrder
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ListSubmissionOrderCopyWith<_ListSubmissionOrder> get copyWith => __$ListSubmissionOrderCopyWithImpl<_ListSubmissionOrder>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ListSubmissionOrderToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ListSubmissionOrder'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('submissionId', submissionId))..add(DiagnosticsProperty('buyerId', buyerId))..add(DiagnosticsProperty('sellerId', sellerId))..add(DiagnosticsProperty('totalAmount', totalAmount))..add(DiagnosticsProperty('items', items))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('isPaid', isPaid))..add(DiagnosticsProperty('paymentId', paymentId))..add(DiagnosticsProperty('deliveryId', deliveryId))..add(DiagnosticsProperty('status', status));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ListSubmissionOrder&&(identical(other.id, id) || other.id == id)&&(identical(other.submissionId, submissionId) || other.submissionId == submissionId)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&const DeepCollectionEquality().equals(other._items, _items)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.isPaid, isPaid) || other.isPaid == isPaid)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.deliveryId, deliveryId) || other.deliveryId == deliveryId)&&(identical(other.status, status) || other.status == status));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,submissionId,buyerId,sellerId,totalAmount,const DeepCollectionEquality().hash(_items),createdAt,isPaid,paymentId,deliveryId,status);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ListSubmissionOrder(id: $id, submissionId: $submissionId, buyerId: $buyerId, sellerId: $sellerId, totalAmount: $totalAmount, items: $items, createdAt: $createdAt, isPaid: $isPaid, paymentId: $paymentId, deliveryId: $deliveryId, status: $status)';
}


}

/// @nodoc
abstract mixin class _$ListSubmissionOrderCopyWith<$Res> implements $ListSubmissionOrderCopyWith<$Res> {
  factory _$ListSubmissionOrderCopyWith(_ListSubmissionOrder value, $Res Function(_ListSubmissionOrder) _then) = __$ListSubmissionOrderCopyWithImpl;
@override @useResult
$Res call({
 String id, String submissionId, String buyerId, String sellerId, double totalAmount, List<ListSubmissionOrderItem> items, DateTime createdAt, bool isPaid, String? paymentId, String? deliveryId, String status
});




}
/// @nodoc
class __$ListSubmissionOrderCopyWithImpl<$Res>
    implements _$ListSubmissionOrderCopyWith<$Res> {
  __$ListSubmissionOrderCopyWithImpl(this._self, this._then);

  final _ListSubmissionOrder _self;
  final $Res Function(_ListSubmissionOrder) _then;

/// Create a copy of ListSubmissionOrder
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? submissionId = null,Object? buyerId = null,Object? sellerId = null,Object? totalAmount = null,Object? items = null,Object? createdAt = null,Object? isPaid = null,Object? paymentId = freezed,Object? deliveryId = freezed,Object? status = null,}) {
  return _then(_ListSubmissionOrder(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,submissionId: null == submissionId ? _self.submissionId : submissionId // ignore: cast_nullable_to_non_nullable
as String,buyerId: null == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,items: null == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<ListSubmissionOrderItem>,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,isPaid: null == isPaid ? _self.isPaid : isPaid // ignore: cast_nullable_to_non_nullable
as bool,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,deliveryId: freezed == deliveryId ? _self.deliveryId : deliveryId // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$ListSubmissionOrderItem implements DiagnosticableTreeMixin {

 String get id; String get name; int get quantity; double get price; String? get notes;
/// Create a copy of ListSubmissionOrderItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ListSubmissionOrderItemCopyWith<ListSubmissionOrderItem> get copyWith => _$ListSubmissionOrderItemCopyWithImpl<ListSubmissionOrderItem>(this as ListSubmissionOrderItem, _$identity);

  /// Serializes this ListSubmissionOrderItem to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ListSubmissionOrderItem'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('quantity', quantity))..add(DiagnosticsProperty('price', price))..add(DiagnosticsProperty('notes', notes));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ListSubmissionOrderItem&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.price, price) || other.price == price)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,quantity,price,notes);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ListSubmissionOrderItem(id: $id, name: $name, quantity: $quantity, price: $price, notes: $notes)';
}


}

/// @nodoc
abstract mixin class $ListSubmissionOrderItemCopyWith<$Res>  {
  factory $ListSubmissionOrderItemCopyWith(ListSubmissionOrderItem value, $Res Function(ListSubmissionOrderItem) _then) = _$ListSubmissionOrderItemCopyWithImpl;
@useResult
$Res call({
 String id, String name, int quantity, double price, String? notes
});




}
/// @nodoc
class _$ListSubmissionOrderItemCopyWithImpl<$Res>
    implements $ListSubmissionOrderItemCopyWith<$Res> {
  _$ListSubmissionOrderItemCopyWithImpl(this._self, this._then);

  final ListSubmissionOrderItem _self;
  final $Res Function(ListSubmissionOrderItem) _then;

/// Create a copy of ListSubmissionOrderItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? quantity = null,Object? price = null,Object? notes = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [ListSubmissionOrderItem].
extension ListSubmissionOrderItemPatterns on ListSubmissionOrderItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ListSubmissionOrderItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ListSubmissionOrderItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ListSubmissionOrderItem value)  $default,){
final _that = this;
switch (_that) {
case _ListSubmissionOrderItem():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ListSubmissionOrderItem value)?  $default,){
final _that = this;
switch (_that) {
case _ListSubmissionOrderItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  int quantity,  double price,  String? notes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ListSubmissionOrderItem() when $default != null:
return $default(_that.id,_that.name,_that.quantity,_that.price,_that.notes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  int quantity,  double price,  String? notes)  $default,) {final _that = this;
switch (_that) {
case _ListSubmissionOrderItem():
return $default(_that.id,_that.name,_that.quantity,_that.price,_that.notes);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  int quantity,  double price,  String? notes)?  $default,) {final _that = this;
switch (_that) {
case _ListSubmissionOrderItem() when $default != null:
return $default(_that.id,_that.name,_that.quantity,_that.price,_that.notes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ListSubmissionOrderItem with DiagnosticableTreeMixin implements ListSubmissionOrderItem {
  const _ListSubmissionOrderItem({required this.id, required this.name, required this.quantity, required this.price, this.notes});
  factory _ListSubmissionOrderItem.fromJson(Map<String, dynamic> json) => _$ListSubmissionOrderItemFromJson(json);

@override final  String id;
@override final  String name;
@override final  int quantity;
@override final  double price;
@override final  String? notes;

/// Create a copy of ListSubmissionOrderItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ListSubmissionOrderItemCopyWith<_ListSubmissionOrderItem> get copyWith => __$ListSubmissionOrderItemCopyWithImpl<_ListSubmissionOrderItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ListSubmissionOrderItemToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ListSubmissionOrderItem'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('quantity', quantity))..add(DiagnosticsProperty('price', price))..add(DiagnosticsProperty('notes', notes));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ListSubmissionOrderItem&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.price, price) || other.price == price)&&(identical(other.notes, notes) || other.notes == notes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,quantity,price,notes);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ListSubmissionOrderItem(id: $id, name: $name, quantity: $quantity, price: $price, notes: $notes)';
}


}

/// @nodoc
abstract mixin class _$ListSubmissionOrderItemCopyWith<$Res> implements $ListSubmissionOrderItemCopyWith<$Res> {
  factory _$ListSubmissionOrderItemCopyWith(_ListSubmissionOrderItem value, $Res Function(_ListSubmissionOrderItem) _then) = __$ListSubmissionOrderItemCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, int quantity, double price, String? notes
});




}
/// @nodoc
class __$ListSubmissionOrderItemCopyWithImpl<$Res>
    implements _$ListSubmissionOrderItemCopyWith<$Res> {
  __$ListSubmissionOrderItemCopyWithImpl(this._self, this._then);

  final _ListSubmissionOrderItem _self;
  final $Res Function(_ListSubmissionOrderItem) _then;

/// Create a copy of ListSubmissionOrderItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? quantity = null,Object? price = null,Object? notes = freezed,}) {
  return _then(_ListSubmissionOrderItem(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
