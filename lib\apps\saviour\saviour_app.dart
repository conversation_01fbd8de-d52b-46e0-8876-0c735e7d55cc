import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/utils/logger.dart';
import '../../shared/ui_components/navigation/global_back_handler.dart';
import 'saviour_routes.dart';
import 'providers/location_provider.dart';
import 'theme/saviour_theme.dart';
import 'services/saviour_notification_service.dart';
import 'services/notification_popup_service.dart';

final _logger = getLogger('SaviourApp');

/// The Saviour Delivery App for delivery personnel
class SaviourApp extends ConsumerStatefulWidget {
  const SaviourApp({super.key});

  @override
  ConsumerState<SaviourApp> createState() => _SaviourAppState();
}

class _SaviourAppState extends ConsumerState<SaviourApp> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Set preferred orientations
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

      // Set system UI overlay style
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      );

      // Initialize location services
      ref.read(saviourLocationProvider.notifier).initialize();

      // Initialize notification service
      final notificationService = ref.read(saviourNotificationServiceProvider);
      await notificationService.initialize();

      // Set notification tap callback
      notificationService.setOnNotificationTap((data) {
        _handleNotificationTap(data);
      });

      _logger.info('Saviour app initialized successfully');
    } catch (e) {
      _logger.severe('Error initializing Saviour app: $e');
    }
  }

  /// Handle notification tap
  void _handleNotificationTap(Map<String, dynamic> data) {
    try {
      final type = data['type'] as String? ?? 'general';

      switch (type) {
        case 'new_request':
          if (data['requestId'] != null) {
            ref.read(saviourRouterProvider).go(SaviourRoutes.deliveryRequests);
          }
          break;
        case 'delivery_update':
          if (data['requestId'] != null) {
            ref
                .read(saviourRouterProvider)
                .go(SaviourRoutes.getActiveDeliveryRoute(data['requestId']));
          }
          break;
        case 'earnings':
          // Navigate to earnings screen (to be implemented)
          break;
        default:
          // Just open the app
          break;
      }
    } catch (e) {
      _logger.severe('Error handling notification tap: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final router = ref.watch(saviourRouterProvider);

    return MaterialApp.router(
      title: 'Saviour Delivery',
      theme: SaviourTheme.lightTheme,
      darkTheme: SaviourTheme.darkTheme,
      themeMode: ThemeMode.system,
      debugShowCheckedModeBanner: false,
      routerConfig: router,
      builder: (context, child) {
        return GlobalBackHandler(
          fallbackRoute: SaviourRoutes.home,
          child: SaviourNotificationPopupInitializer(child: child!),
        );
      },
    );
  }
}
