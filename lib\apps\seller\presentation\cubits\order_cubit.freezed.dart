// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$OrderState {

 List<OrderModel> get orders; bool get isLoading; bool get hasError; String? get errorMessage; OrderStatus? get selectedStatus; DateTime? get startDate; DateTime? get endDate; String? get lastOrderId; bool get hasMoreOrders;
/// Create a copy of OrderState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderStateCopyWith<OrderState> get copyWith => _$OrderStateCopyWithImpl<OrderState>(this as OrderState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderState&&const DeepCollectionEquality().equals(other.orders, orders)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasError, hasError) || other.hasError == hasError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.selectedStatus, selectedStatus) || other.selectedStatus == selectedStatus)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.lastOrderId, lastOrderId) || other.lastOrderId == lastOrderId)&&(identical(other.hasMoreOrders, hasMoreOrders) || other.hasMoreOrders == hasMoreOrders));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(orders),isLoading,hasError,errorMessage,selectedStatus,startDate,endDate,lastOrderId,hasMoreOrders);

@override
String toString() {
  return 'OrderState(orders: $orders, isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, selectedStatus: $selectedStatus, startDate: $startDate, endDate: $endDate, lastOrderId: $lastOrderId, hasMoreOrders: $hasMoreOrders)';
}


}

/// @nodoc
abstract mixin class $OrderStateCopyWith<$Res>  {
  factory $OrderStateCopyWith(OrderState value, $Res Function(OrderState) _then) = _$OrderStateCopyWithImpl;
@useResult
$Res call({
 List<OrderModel> orders, bool isLoading, bool hasError, String? errorMessage, OrderStatus? selectedStatus, DateTime? startDate, DateTime? endDate, String? lastOrderId, bool hasMoreOrders
});




}
/// @nodoc
class _$OrderStateCopyWithImpl<$Res>
    implements $OrderStateCopyWith<$Res> {
  _$OrderStateCopyWithImpl(this._self, this._then);

  final OrderState _self;
  final $Res Function(OrderState) _then;

/// Create a copy of OrderState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? orders = null,Object? isLoading = null,Object? hasError = null,Object? errorMessage = freezed,Object? selectedStatus = freezed,Object? startDate = freezed,Object? endDate = freezed,Object? lastOrderId = freezed,Object? hasMoreOrders = null,}) {
  return _then(_self.copyWith(
orders: null == orders ? _self.orders : orders // ignore: cast_nullable_to_non_nullable
as List<OrderModel>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,selectedStatus: freezed == selectedStatus ? _self.selectedStatus : selectedStatus // ignore: cast_nullable_to_non_nullable
as OrderStatus?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,lastOrderId: freezed == lastOrderId ? _self.lastOrderId : lastOrderId // ignore: cast_nullable_to_non_nullable
as String?,hasMoreOrders: null == hasMoreOrders ? _self.hasMoreOrders : hasMoreOrders // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [OrderState].
extension OrderStatePatterns on OrderState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OrderState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OrderState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OrderState value)  $default,){
final _that = this;
switch (_that) {
case _OrderState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OrderState value)?  $default,){
final _that = this;
switch (_that) {
case _OrderState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<OrderModel> orders,  bool isLoading,  bool hasError,  String? errorMessage,  OrderStatus? selectedStatus,  DateTime? startDate,  DateTime? endDate,  String? lastOrderId,  bool hasMoreOrders)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OrderState() when $default != null:
return $default(_that.orders,_that.isLoading,_that.hasError,_that.errorMessage,_that.selectedStatus,_that.startDate,_that.endDate,_that.lastOrderId,_that.hasMoreOrders);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<OrderModel> orders,  bool isLoading,  bool hasError,  String? errorMessage,  OrderStatus? selectedStatus,  DateTime? startDate,  DateTime? endDate,  String? lastOrderId,  bool hasMoreOrders)  $default,) {final _that = this;
switch (_that) {
case _OrderState():
return $default(_that.orders,_that.isLoading,_that.hasError,_that.errorMessage,_that.selectedStatus,_that.startDate,_that.endDate,_that.lastOrderId,_that.hasMoreOrders);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<OrderModel> orders,  bool isLoading,  bool hasError,  String? errorMessage,  OrderStatus? selectedStatus,  DateTime? startDate,  DateTime? endDate,  String? lastOrderId,  bool hasMoreOrders)?  $default,) {final _that = this;
switch (_that) {
case _OrderState() when $default != null:
return $default(_that.orders,_that.isLoading,_that.hasError,_that.errorMessage,_that.selectedStatus,_that.startDate,_that.endDate,_that.lastOrderId,_that.hasMoreOrders);case _:
  return null;

}
}

}

/// @nodoc


class _OrderState implements OrderState {
  const _OrderState({final  List<OrderModel> orders = const [], this.isLoading = false, this.hasError = false, this.errorMessage, this.selectedStatus, this.startDate, this.endDate, this.lastOrderId, this.hasMoreOrders = true}): _orders = orders;
  

 final  List<OrderModel> _orders;
@override@JsonKey() List<OrderModel> get orders {
  if (_orders is EqualUnmodifiableListView) return _orders;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_orders);
}

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool hasError;
@override final  String? errorMessage;
@override final  OrderStatus? selectedStatus;
@override final  DateTime? startDate;
@override final  DateTime? endDate;
@override final  String? lastOrderId;
@override@JsonKey() final  bool hasMoreOrders;

/// Create a copy of OrderState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderStateCopyWith<_OrderState> get copyWith => __$OrderStateCopyWithImpl<_OrderState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderState&&const DeepCollectionEquality().equals(other._orders, _orders)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasError, hasError) || other.hasError == hasError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.selectedStatus, selectedStatus) || other.selectedStatus == selectedStatus)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.lastOrderId, lastOrderId) || other.lastOrderId == lastOrderId)&&(identical(other.hasMoreOrders, hasMoreOrders) || other.hasMoreOrders == hasMoreOrders));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_orders),isLoading,hasError,errorMessage,selectedStatus,startDate,endDate,lastOrderId,hasMoreOrders);

@override
String toString() {
  return 'OrderState(orders: $orders, isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, selectedStatus: $selectedStatus, startDate: $startDate, endDate: $endDate, lastOrderId: $lastOrderId, hasMoreOrders: $hasMoreOrders)';
}


}

/// @nodoc
abstract mixin class _$OrderStateCopyWith<$Res> implements $OrderStateCopyWith<$Res> {
  factory _$OrderStateCopyWith(_OrderState value, $Res Function(_OrderState) _then) = __$OrderStateCopyWithImpl;
@override @useResult
$Res call({
 List<OrderModel> orders, bool isLoading, bool hasError, String? errorMessage, OrderStatus? selectedStatus, DateTime? startDate, DateTime? endDate, String? lastOrderId, bool hasMoreOrders
});




}
/// @nodoc
class __$OrderStateCopyWithImpl<$Res>
    implements _$OrderStateCopyWith<$Res> {
  __$OrderStateCopyWithImpl(this._self, this._then);

  final _OrderState _self;
  final $Res Function(_OrderState) _then;

/// Create a copy of OrderState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? orders = null,Object? isLoading = null,Object? hasError = null,Object? errorMessage = freezed,Object? selectedStatus = freezed,Object? startDate = freezed,Object? endDate = freezed,Object? lastOrderId = freezed,Object? hasMoreOrders = null,}) {
  return _then(_OrderState(
orders: null == orders ? _self._orders : orders // ignore: cast_nullable_to_non_nullable
as List<OrderModel>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,selectedStatus: freezed == selectedStatus ? _self.selectedStatus : selectedStatus // ignore: cast_nullable_to_non_nullable
as OrderStatus?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,lastOrderId: freezed == lastOrderId ? _self.lastOrderId : lastOrderId // ignore: cast_nullable_to_non_nullable
as String?,hasMoreOrders: null == hasMoreOrders ? _self.hasMoreOrders : hasMoreOrders // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
