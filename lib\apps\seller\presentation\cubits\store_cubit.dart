import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shivish/apps/seller/domain/models/store_model.dart';
import 'package:shivish/apps/seller/domain/models/schedule_model.dart';
import 'package:shivish/apps/seller/domain/repositories/store_repository.dart';

part 'store_cubit.freezed.dart';
part 'store_cubit.g.dart';

@freezed
sealed class StoreState with _$StoreState {
  const factory StoreState({
    StoreModel? store,
    @Default(false) bool isLoading,
    @Default(false) bool isEditing,
    String? error,
  }) = _StoreState;

  const StoreState._();

  bool get hasError => error != null;
  String? get errorMessage => error;

  factory StoreState.fromJson(Map<String, dynamic> json) =>
      _$StoreStateFromJson(json);
}

class StoreCubit extends Cubit<StoreState> {
  final StoreRepository _repository;

  StoreCubit(this._repository) : super(const StoreState());

  Future<void> loadStore() async {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      final store = await _repository.getStore();
      emit(state.copyWith(store: store, isLoading: false));
    } catch (e) {
      emit(state.copyWith(error: e.toString(), isLoading: false));
    }
  }

  Future<void> updateStore({
    String? name,
    String? description,
    String? address,
    String? phone,
    String? email,
    String? website,
  }) async {
    if (state.store == null) return;

    try {
      emit(state.copyWith(isLoading: true, error: null));
      final updatedStore = state.store!.copyWith(
        name: name ?? state.store!.name,
        description: description ?? state.store!.description,
        address: address ?? state.store!.address,
        phone: phone ?? state.store!.phone,
        email: email ?? state.store!.email,
        website: website,
        updatedAt: DateTime.now(),
      );
      await _repository.updateStore(updatedStore);
      emit(state.copyWith(store: updatedStore, isLoading: false));
    } catch (e) {
      emit(state.copyWith(error: e.toString(), isLoading: false));
    }
  }

  Future<void> updateSchedule(ScheduleModel schedule) async {
    if (state.store == null) return;
    try {
      final updatedStore = state.store!.copyWith(schedule: schedule);
      await _repository.updateStore(updatedStore);
      emit(state.copyWith(store: updatedStore));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  void setEditing(bool isEditing) {
    emit(state.copyWith(isEditing: isEditing));
  }

  void clearError() {
    emit(state.copyWith(error: null));
  }

  Future<void> updateLogo(XFile file) async {
    if (state.store == null) return;

    emit(state.copyWith(isLoading: true, error: null));

    try {
      final logoUrl = await _repository.uploadLogo(file);
      final updatedStore = state.store!.copyWith(
        logoUrl: logoUrl,
        updatedAt: DateTime.now(),
      );

      await _repository.updateStore(updatedStore);
      emit(state.copyWith(store: updatedStore, isLoading: false));
    } catch (e) {
      emit(state.copyWith(error: e.toString(), isLoading: false));
    }
  }

  Future<void> addCategory(String category) async {
    if (state.store == null) return;

    final categories = List<String>.from(state.store!.categories);
    if (!categories.contains(category)) {
      categories.add(category);
      final updatedStore = state.store!.copyWith(
        categories: categories,
        updatedAt: DateTime.now(),
      );

      try {
        await _repository.updateStore(updatedStore);
        emit(state.copyWith(store: updatedStore));
      } catch (e) {
        emit(state.copyWith(error: e.toString()));
      }
    }
  }

  Future<void> removeCategory(String category) async {
    if (state.store == null) return;

    final categories = List<String>.from(state.store!.categories);
    if (categories.contains(category)) {
      categories.remove(category);
      final updatedStore = state.store!.copyWith(
        categories: categories,
        updatedAt: DateTime.now(),
      );

      try {
        await _repository.updateStore(updatedStore);
        emit(state.copyWith(store: updatedStore));
      } catch (e) {
        emit(state.copyWith(error: e.toString()));
      }
    }
  }
}
