import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import '../repositories/auth_repository.dart';
import '../models/login_request.dart';
import '../models/register_request.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository _authRepository;
  StreamSubscription<dynamic>? _authStateSubscription;

  AuthBloc({required AuthRepository authRepository})
      : _authRepository = authRepository,
        super(const AuthState.initial()) {
    on<AuthEvent>((event, emit) async {
      await event.when(
        checkAuthStatus: () => _checkAuthStatus(emit),
        signInWithEmailAndPassword: (request) =>
            _signInWithEmailAndPassword(request, emit),
        signInWithGoogle: () => _signInWithGoogle(emit),
        signInWithApple: () => _signInWithApple(emit),
        register: (request) => _register(request, emit),
        signOut: () => _signOut(emit),
        resetPassword: (email) => _resetPassword(email, emit),
        updateProfile: (displayName, photoUrl, phoneNumber) =>
            _updateProfile(displayName, photoUrl, phoneNumber, emit),
        deleteAccount: () => _deleteAccount(emit),
        verifyPhone: (phoneNumber) => _verifyPhone(phoneNumber, emit),
        verifyPhoneCode: (code) => _verifyPhoneCode(code, emit),
      );
    });

    _authStateSubscription = _authRepository.authStateChanges.listen(
      (user) => add(const AuthEvent.checkAuthStatus()),
    );
  }

  Future<void> _checkAuthStatus(Emitter<AuthState> emit) async {
    try {
      emit(const AuthState.loading());

      // First check if there's a current user in Firebase Auth
      final currentUser = _authRepository.currentUser;

      if (currentUser != null) {
        // User is already authenticated
        debugPrint('User is already authenticated: ${currentUser.email}');
        emit(AuthState.authenticated(currentUser));
        return;
      }

      // If no current user, try to get the user from Firebase Auth
      final user = await _authRepository.getCurrentUser();

      if (user != null) {
        // User found from getCurrentUser
        debugPrint('User found from getCurrentUser: ${user.email}');
        emit(AuthState.authenticated(user));
      } else {
        // No authenticated user
        debugPrint('No authenticated user found');
        emit(const AuthState.unauthenticated());
      }
    } catch (e) {
      debugPrint('Error checking auth status: $e');
      emit(AuthState.error(e.toString()));
    }
  }

  Future<void> _signInWithEmailAndPassword(
    LoginRequest request,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthState.loading());
      debugPrint('AuthBloc: Attempting to sign in with email: ${request.email}');

      final user = await _authRepository.signInWithEmailAndPassword(request);

      debugPrint('AuthBloc: Sign in successful for user: ${user.email}');
      emit(AuthState.authenticated(user));
    } catch (e) {
      debugPrint('AuthBloc: Sign in error: $e');

      // Create a more user-friendly error message
      String errorMessage = e.toString();
      if (errorMessage.contains('pending approval')) {
        errorMessage = 'Your executor account is pending approval by an administrator. Please wait for approval before logging in.';
      } else if (errorMessage.contains('user-not-found') || errorMessage.contains('wrong-password')) {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.';
      } else if (errorMessage.contains('network')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      }

      emit(AuthState.error(errorMessage));
    }
  }

  Future<void> _signInWithGoogle(Emitter<AuthState> emit) async {
    try {
      emit(const AuthState.loading());
      final user = await _authRepository.signInWithGoogle();
      emit(AuthState.authenticated(user));
    } catch (e) {
      emit(AuthState.error(e.toString()));
    }
  }

  Future<void> _signInWithApple(Emitter<AuthState> emit) async {
    try {
      emit(const AuthState.loading());
      final user = await _authRepository.signInWithApple();
      emit(AuthState.authenticated(user));
    } catch (e) {
      emit(AuthState.error(e.toString()));
    }
  }

  Future<void> _register(
    RegisterRequest request,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthState.loading());
      final user = await _authRepository.register(request);
      emit(AuthState.authenticated(user));
    } catch (e) {
      emit(AuthState.error(e.toString()));
    }
  }

  Future<void> _signOut(Emitter<AuthState> emit) async {
    try {
      emit(const AuthState.loading());
      await _authRepository.signOut();
      emit(const AuthState.unauthenticated());
    } catch (e) {
      emit(AuthState.error(e.toString()));
    }
  }

  Future<void> _resetPassword(
    String email,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthState.loading());
      await _authRepository.resetPassword(email);
      emit(const AuthState.unauthenticated());
    } catch (e) {
      emit(AuthState.error(e.toString()));
    }
  }

  Future<void> _updateProfile(
    String? displayName,
    String? photoUrl,
    String? phoneNumber,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthState.loading());
      final user = await _authRepository.updateProfile(
        displayName: displayName,
        photoUrl: photoUrl,
        phoneNumber: phoneNumber,
      );
      emit(AuthState.authenticated(user));
    } catch (e) {
      emit(AuthState.error(e.toString()));
    }
  }

  Future<void> _deleteAccount(Emitter<AuthState> emit) async {
    try {
      emit(const AuthState.loading());
      await _authRepository.deleteAccount();
      emit(const AuthState.unauthenticated());
    } catch (e) {
      emit(AuthState.error(e.toString()));
    }
  }

  Future<void> _verifyPhone(
    String phoneNumber,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthState.loading());
      await _authRepository.verifyPhone(phoneNumber);
      final user = await _authRepository.getCurrentUser();
      if (user != null) {
        emit(AuthState.authenticated(user));
      }
    } catch (e) {
      emit(AuthState.error(e.toString()));
    }
  }

  Future<void> _verifyPhoneCode(
    String code,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthState.loading());
      final user = await _authRepository.verifyPhoneCode(code);
      emit(AuthState.authenticated(user));
    } catch (e) {
      emit(AuthState.error(e.toString()));
    }
  }

  @override
  Future<void> close() {
    _authStateSubscription?.cancel();
    return super.close();
  }
}
