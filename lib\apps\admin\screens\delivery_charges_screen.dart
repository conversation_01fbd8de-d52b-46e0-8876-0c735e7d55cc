import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/delivery/delivery_charge_model.dart';
import '../../../shared/models/delivery/delivery_provider_model.dart';
import '../../../shared/services/delivery/delivery_service.dart';
import '../../../shared/ui_components/inputs/text_field.dart';
import '../../../shared/ui_components/cards/app_card.dart';
import '../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../shared/ui_components/errors/error_message.dart';

class DeliveryChargesScreen extends ConsumerStatefulWidget {
  const DeliveryChargesScreen({super.key});

  @override
  ConsumerState<DeliveryChargesScreen> createState() => _DeliveryChargesScreenState();
}

class _DeliveryChargesScreenState extends ConsumerState<DeliveryChargesScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  List<DeliveryChargeModel> _charges = [];

  // Ecom Express charge controllers
  final _ecomBaseChargeController = TextEditingController();
  final _ecomHeavyItemChargeController = TextEditingController();
  final _ecomFragileItemChargeController = TextEditingController();

  // Local Metro charge controllers
  final _localMetroBaseChargeController = TextEditingController();
  final _localMetroPerKmChargeController = TextEditingController();
  final _localMetroMaxDistanceController = TextEditingController();
  final _localMetroPeakHoursChargeController = TextEditingController();
  final _localMetroRainChargeController = TextEditingController();

  // Local Non-Metro charge controllers
  final _localNonMetroBaseChargeController = TextEditingController();
  final _localNonMetroPerKmChargeController = TextEditingController();
  final _localNonMetroMaxDistanceController = TextEditingController();
  final _localNonMetroPeakHoursChargeController = TextEditingController();
  final _localNonMetroRainChargeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCharges();
  }

  @override
  void dispose() {
    _ecomBaseChargeController.dispose();
    _ecomHeavyItemChargeController.dispose();
    _ecomFragileItemChargeController.dispose();

    _localMetroBaseChargeController.dispose();
    _localMetroPerKmChargeController.dispose();
    _localMetroMaxDistanceController.dispose();
    _localMetroPeakHoursChargeController.dispose();
    _localMetroRainChargeController.dispose();

    _localNonMetroBaseChargeController.dispose();
    _localNonMetroPerKmChargeController.dispose();
    _localNonMetroMaxDistanceController.dispose();
    _localNonMetroPeakHoursChargeController.dispose();
    _localNonMetroRainChargeController.dispose();

    super.dispose();
  }

  Future<void> _loadCharges() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final deliveryService = ref.read(deliveryServiceProvider);

      // Initialize default settings if needed
      await deliveryService.initializeDefaultDeliverySettings();

      // Load Ecom Express charges
      final ecomCharges = await deliveryService.getDeliveryCharges(
        providerType: DeliveryProviderType.ecomExpress,
      );

      // Load Local Metro charges
      final localMetroCharges = await deliveryService.getDeliveryCharges(
        providerType: DeliveryProviderType.localDelivery,
        areaType: AreaType.metro,
      );

      // Load Local Non-Metro charges
      final localNonMetroCharges = await deliveryService.getDeliveryCharges(
        providerType: DeliveryProviderType.localDelivery,
        areaType: AreaType.nonMetro,
      );

      setState(() {
        _charges = [...ecomCharges, ...localMetroCharges, ...localNonMetroCharges];

        // Set controller values for Ecom Express
        final ecomCharge = ecomCharges.isNotEmpty
            ? ecomCharges.first
            : DeliveryChargeModel.ecomExpress();

        _ecomBaseChargeController.text = ecomCharge.baseCharge.toString();
        _ecomHeavyItemChargeController.text =
            (ecomCharge.additionalCharges['heavy_item_charge'] ?? 30.0).toString();
        _ecomFragileItemChargeController.text =
            (ecomCharge.additionalCharges['fragile_item_charge'] ?? 25.0).toString();

        // Set controller values for Local Metro
        final localMetroCharge = localMetroCharges.isNotEmpty
            ? localMetroCharges.first
            : DeliveryChargeModel.localMetro();

        _localMetroBaseChargeController.text = localMetroCharge.baseCharge.toString();
        _localMetroPerKmChargeController.text = localMetroCharge.perKmCharge.toString();
        _localMetroMaxDistanceController.text = localMetroCharge.maxDistance.toString();
        _localMetroPeakHoursChargeController.text =
            (localMetroCharge.additionalCharges['peak_hours_charge'] ?? 15.0).toString();
        _localMetroRainChargeController.text =
            (localMetroCharge.additionalCharges['rain_charge'] ?? 20.0).toString();

        // Set controller values for Local Non-Metro
        final localNonMetroCharge = localNonMetroCharges.isNotEmpty
            ? localNonMetroCharges.first
            : DeliveryChargeModel.localNonMetro();

        _localNonMetroBaseChargeController.text = localNonMetroCharge.baseCharge.toString();
        _localNonMetroPerKmChargeController.text = localNonMetroCharge.perKmCharge.toString();
        _localNonMetroMaxDistanceController.text = localNonMetroCharge.maxDistance.toString();
        _localNonMetroPeakHoursChargeController.text =
            (localNonMetroCharge.additionalCharges['peak_hours_charge'] ?? 10.0).toString();
        _localNonMetroRainChargeController.text =
            (localNonMetroCharge.additionalCharges['rain_charge'] ?? 15.0).toString();
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading delivery charges: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveCharges() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final deliveryService = ref.read(deliveryServiceProvider);

      // Update Ecom Express charge
      final ecomCharge = _charges.firstWhere(
        (c) => c.providerType == DeliveryProviderType.ecomExpress,
        orElse: () => DeliveryChargeModel.ecomExpress(),
      );

      final updatedEcomCharge = ecomCharge.copyWith(
        baseCharge: double.tryParse(_ecomBaseChargeController.text) ?? 50.0,
        additionalCharges: {
          'heavy_item_charge': double.tryParse(_ecomHeavyItemChargeController.text) ?? 30.0,
          'fragile_item_charge': double.tryParse(_ecomFragileItemChargeController.text) ?? 25.0,
        },
      );

      // Update Local Metro charge
      final localMetroCharge = _charges.firstWhere(
        (c) => c.providerType == DeliveryProviderType.localDelivery && c.areaType == AreaType.metro,
        orElse: () => DeliveryChargeModel.localMetro(),
      );

      final updatedLocalMetroCharge = localMetroCharge.copyWith(
        baseCharge: double.tryParse(_localMetroBaseChargeController.text) ?? 30.0,
        perKmCharge: double.tryParse(_localMetroPerKmChargeController.text) ?? 10.0,
        maxDistance: double.tryParse(_localMetroMaxDistanceController.text) ?? 5.0,
        additionalCharges: {
          'peak_hours_charge': double.tryParse(_localMetroPeakHoursChargeController.text) ?? 15.0,
          'rain_charge': double.tryParse(_localMetroRainChargeController.text) ?? 20.0,
        },
      );

      // Update Local Non-Metro charge
      final localNonMetroCharge = _charges.firstWhere(
        (c) => c.providerType == DeliveryProviderType.localDelivery && c.areaType == AreaType.nonMetro,
        orElse: () => DeliveryChargeModel.localNonMetro(),
      );

      final updatedLocalNonMetroCharge = localNonMetroCharge.copyWith(
        baseCharge: double.tryParse(_localNonMetroBaseChargeController.text) ?? 25.0,
        perKmCharge: double.tryParse(_localNonMetroPerKmChargeController.text) ?? 8.0,
        maxDistance: double.tryParse(_localNonMetroMaxDistanceController.text) ?? 10.0,
        additionalCharges: {
          'peak_hours_charge': double.tryParse(_localNonMetroPeakHoursChargeController.text) ?? 10.0,
          'rain_charge': double.tryParse(_localNonMetroRainChargeController.text) ?? 15.0,
        },
      );

      // Save charges
      await deliveryService.saveDeliveryCharge(updatedEcomCharge);
      await deliveryService.saveDeliveryCharge(updatedLocalMetroCharge);
      await deliveryService.saveDeliveryCharge(updatedLocalNonMetroCharge);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Delivery charges saved successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error saving delivery charges: $e';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving charges: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Delivery Charges'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCharges,
            tooltip: 'Reload charges',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _errorMessage != null && _charges.isEmpty
              ? ErrorMessage(
                  message: _errorMessage!,
                  onRetry: _loadCharges,
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Ecom Express Charges
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Text(
                          'Ecom Express Charges',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ),
                      AppCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Configure charges for Ecom Express delivery service.',
                              style: TextStyle(fontSize: 14),
                            ),
                            const SizedBox(height: 16),
                            AppTextField(
                              controller: _ecomBaseChargeController,
                              label: 'Base Charge',
                              hint: '50.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _ecomHeavyItemChargeController,
                              label: 'Heavy Item Surcharge',
                              hint: '30.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _ecomFragileItemChargeController,
                              label: 'Fragile Item Surcharge',
                              hint: '25.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            SwitchListTile(
                              title: const Text('Active'),
                              value: _charges
                                  .firstWhere(
                                    (c) => c.providerType == DeliveryProviderType.ecomExpress,
                                    orElse: () => DeliveryChargeModel.ecomExpress(),
                                  )
                                  .isActive,
                              onChanged: (value) {
                                setState(() {
                                  final index = _charges.indexWhere(
                                    (c) => c.providerType == DeliveryProviderType.ecomExpress,
                                  );
                                  if (index >= 0) {
                                    _charges[index] = _charges[index].copyWith(isActive: value);
                                  }
                                });
                              },
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Local Metro Charges
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Text(
                          'Local Delivery - Metro Cities',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ),
                      AppCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Configure charges for local delivery in metro cities (within 5km).',
                              style: TextStyle(fontSize: 14),
                            ),
                            const SizedBox(height: 16),
                            AppTextField(
                              controller: _localMetroBaseChargeController,
                              label: 'Base Charge',
                              hint: '30.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _localMetroPerKmChargeController,
                              label: 'Per Kilometer Charge',
                              hint: '10.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _localMetroMaxDistanceController,
                              label: 'Maximum Distance (km)',
                              hint: '5.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _localMetroPeakHoursChargeController,
                              label: 'Peak Hours Surcharge',
                              hint: '15.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _localMetroRainChargeController,
                              label: 'Rain Surcharge',
                              hint: '20.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            SwitchListTile(
                              title: const Text('Active'),
                              value: _charges
                                  .firstWhere(
                                    (c) => c.providerType == DeliveryProviderType.localDelivery &&
                                        c.areaType == AreaType.metro,
                                    orElse: () => DeliveryChargeModel.localMetro(),
                                  )
                                  .isActive,
                              onChanged: (value) {
                                setState(() {
                                  final index = _charges.indexWhere(
                                    (c) => c.providerType == DeliveryProviderType.localDelivery &&
                                        c.areaType == AreaType.metro,
                                  );
                                  if (index >= 0) {
                                    _charges[index] = _charges[index].copyWith(isActive: value);
                                  }
                                });
                              },
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Local Non-Metro Charges
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Text(
                          'Local Delivery - Other Places',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ),
                      AppCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Configure charges for local delivery in non-metro areas (within 10km).',
                              style: TextStyle(fontSize: 14),
                            ),
                            const SizedBox(height: 16),
                            AppTextField(
                              controller: _localNonMetroBaseChargeController,
                              label: 'Base Charge',
                              hint: '25.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _localNonMetroPerKmChargeController,
                              label: 'Per Kilometer Charge',
                              hint: '8.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _localNonMetroMaxDistanceController,
                              label: 'Maximum Distance (km)',
                              hint: '10.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _localNonMetroPeakHoursChargeController,
                              label: 'Peak Hours Surcharge',
                              hint: '10.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            AppTextField(
                              controller: _localNonMetroRainChargeController,
                              label: 'Rain Surcharge',
                              hint: '15.0',
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 12),
                            SwitchListTile(
                              title: const Text('Active'),
                              value: _charges
                                  .firstWhere(
                                    (c) => c.providerType == DeliveryProviderType.localDelivery &&
                                        c.areaType == AreaType.nonMetro,
                                    orElse: () => DeliveryChargeModel.localNonMetro(),
                                  )
                                  .isActive,
                              onChanged: (value) {
                                setState(() {
                                  final index = _charges.indexWhere(
                                    (c) => c.providerType == DeliveryProviderType.localDelivery &&
                                        c.areaType == AreaType.nonMetro,
                                  );
                                  if (index >= 0) {
                                    _charges[index] = _charges[index].copyWith(isActive: value);
                                  }
                                });
                              },
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Save Button
                      Center(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _saveCharges,
                          child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(strokeWidth: 2)
                              )
                            : const Text('Save Charges'),
                        ),
                      ),
                    ],
                  ),
                ),
    );
  }
}
