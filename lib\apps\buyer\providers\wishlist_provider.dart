import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/product/product_model.dart';
import '../../../shared/services/product/product_service.dart';
import '../../../shared/providers/auth_provider.dart';

final productServiceProvider = Provider<ProductService>((ref) {
  return ProductService();
});

final wishlistProvider = StreamProvider.autoDispose<List<ProductModel>>((ref) {
  final productService = ref.watch(productServiceProvider);
  final userId = ref.watch(userIdProvider);
  return productService.getWishlistProducts(userId);
});

/// Provider to check if a product is in the wishlist
final isProductInWishlistProvider =
    FutureProvider.family<bool, String>((ref, productId) async {
  final wishlistAsync = await ref.watch(wishlistProvider.future);
  return wishlistAsync.any((product) => product.id == productId);
});

final wishlistNotifierProvider =
    StateNotifierProvider.autoDispose<WishlistNotifier, AsyncValue<void>>(
        (ref) {
  final productService = ref.watch(productServiceProvider);
  final userId = ref.watch(userIdProvider);
  return WishlistNotifier(productService, userId);
});

class WishlistNotifier extends StateNotifier<AsyncValue<void>> {
  final ProductService _productService;
  final String _userId;

  WishlistNotifier(this._productService, this._userId)
      : super(const AsyncValue.data(null));

  Future<void> addToWishlist(String productId) async {
    try {
      state = const AsyncValue.loading();
      await _productService.addToWishlist(_userId, productId);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> removeFromWishlist(String productId) async {
    try {
      state = const AsyncValue.loading();
      await _productService.removeFromWishlist(_userId, productId);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> clearWishlist() async {
    try {
      state = const AsyncValue.loading();
      await _productService.clearWishlist(_userId);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
