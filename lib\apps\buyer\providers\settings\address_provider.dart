import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/user/user_model.dart';
import '../../../../shared/services/user/user_service.dart';
import '../../../../shared/providers/auth_provider.dart';

/// Provider for the user service
final userServiceProvider = Provider<UserService>((ref) {
  return UserService();
});

/// Provider for user addresses
final userAddressesProvider = StreamProvider<List<UserAddress>>((ref) {
  final userId = ref.watch(userIdProvider);
  final userService = ref.watch(userServiceProvider);
  return userService.getUserById(userId).asStream().map((user) {
    return user?.addresses ?? [];
  });
});

/// Provider for managing address operations
final addressStateProvider =
    StateNotifierProvider<AddressStateNotifier, AsyncValue<void>>((ref) {
  final userService = ref.watch(userServiceProvider);
  final userId = ref.watch(userIdProvider);
  return AddressStateNotifier(userService, userId);
});

class AddressStateNotifier extends StateNotifier<AsyncValue<void>> {
  final UserService _userService;
  final String _userId;

  AddressStateNotifier(this._userService, this._userId)
      : super(const AsyncValue.data(null));

  Future<void> addAddress(UserAddress address) async {
    try {
      state = const AsyncValue.loading();
      final user = await _userService.getUserById(_userId);
      if (user == null) throw Exception('User not found');

      final addresses = List<UserAddress>.from(user.addresses);

      // If this is the first address or marked as default, update other addresses
      if (addresses.isEmpty || address.isDefault) {
        for (var addr in addresses) {
          if (addr.isDefault) {
            final updatedAddr = addr.copyWith(isDefault: false);
            final index = addresses.indexOf(addr);
            addresses[index] = updatedAddr;
          }
        }
      }

      addresses.add(address);

      final updatedUser = user.copyWith(addresses: addresses);
      await _userService.createOrUpdateUser(updatedUser);

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateAddress(UserAddress address) async {
    try {
      state = const AsyncValue.loading();
      final user = await _userService.getUserById(_userId);
      if (user == null) throw Exception('User not found');

      final addresses = List<UserAddress>.from(user.addresses);
      final index = addresses.indexWhere((addr) => addr.id == address.id);

      if (index == -1) throw Exception('Address not found');

      // If this address is being set as default, update other addresses
      if (address.isDefault) {
        for (var addr in addresses) {
          if (addr.isDefault && addr.id != address.id) {
            final updatedAddr = addr.copyWith(isDefault: false);
            final idx = addresses.indexOf(addr);
            addresses[idx] = updatedAddr;
          }
        }
      }

      addresses[index] = address;

      final updatedUser = user.copyWith(addresses: addresses);
      await _userService.createOrUpdateUser(updatedUser);

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteAddress(String addressId) async {
    try {
      state = const AsyncValue.loading();
      final user = await _userService.getUserById(_userId);
      if (user == null) throw Exception('User not found');

      final addresses = List<UserAddress>.from(user.addresses);
      final index = addresses.indexWhere((addr) => addr.id == addressId);

      if (index == -1) throw Exception('Address not found');

      // If deleting default address, make the first remaining address default
      final isDefault = addresses[index].isDefault;
      addresses.removeAt(index);

      if (isDefault && addresses.isNotEmpty) {
        addresses[0] = addresses[0].copyWith(isDefault: true);
      }

      final updatedUser = user.copyWith(addresses: addresses);
      await _userService.createOrUpdateUser(updatedUser);

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
