// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'server_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ServerConfig {

 String get environment; String get awsRegion; String get datacenterIp; String get databaseUrl; String get redisUrl; int get maxConnections; int get timeout; ServerState get awsStatus; ServerState get datacenterStatus; bool get autoScaling; bool get loadBalancing; bool get cachingEnabled; bool get sslEnabled; bool get rateLimitingEnabled; bool get ddosProtection; DateTime get lastUpdated; Map<String, dynamic> get customSettings; List<ServerInstance> get awsInstances; List<ServerInstance> get datacenterInstances;
/// Create a copy of ServerConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ServerConfigCopyWith<ServerConfig> get copyWith => _$ServerConfigCopyWithImpl<ServerConfig>(this as ServerConfig, _$identity);

  /// Serializes this ServerConfig to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ServerConfig&&(identical(other.environment, environment) || other.environment == environment)&&(identical(other.awsRegion, awsRegion) || other.awsRegion == awsRegion)&&(identical(other.datacenterIp, datacenterIp) || other.datacenterIp == datacenterIp)&&(identical(other.databaseUrl, databaseUrl) || other.databaseUrl == databaseUrl)&&(identical(other.redisUrl, redisUrl) || other.redisUrl == redisUrl)&&(identical(other.maxConnections, maxConnections) || other.maxConnections == maxConnections)&&(identical(other.timeout, timeout) || other.timeout == timeout)&&(identical(other.awsStatus, awsStatus) || other.awsStatus == awsStatus)&&(identical(other.datacenterStatus, datacenterStatus) || other.datacenterStatus == datacenterStatus)&&(identical(other.autoScaling, autoScaling) || other.autoScaling == autoScaling)&&(identical(other.loadBalancing, loadBalancing) || other.loadBalancing == loadBalancing)&&(identical(other.cachingEnabled, cachingEnabled) || other.cachingEnabled == cachingEnabled)&&(identical(other.sslEnabled, sslEnabled) || other.sslEnabled == sslEnabled)&&(identical(other.rateLimitingEnabled, rateLimitingEnabled) || other.rateLimitingEnabled == rateLimitingEnabled)&&(identical(other.ddosProtection, ddosProtection) || other.ddosProtection == ddosProtection)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&const DeepCollectionEquality().equals(other.customSettings, customSettings)&&const DeepCollectionEquality().equals(other.awsInstances, awsInstances)&&const DeepCollectionEquality().equals(other.datacenterInstances, datacenterInstances));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,environment,awsRegion,datacenterIp,databaseUrl,redisUrl,maxConnections,timeout,awsStatus,datacenterStatus,autoScaling,loadBalancing,cachingEnabled,sslEnabled,rateLimitingEnabled,ddosProtection,lastUpdated,const DeepCollectionEquality().hash(customSettings),const DeepCollectionEquality().hash(awsInstances),const DeepCollectionEquality().hash(datacenterInstances)]);

@override
String toString() {
  return 'ServerConfig(environment: $environment, awsRegion: $awsRegion, datacenterIp: $datacenterIp, databaseUrl: $databaseUrl, redisUrl: $redisUrl, maxConnections: $maxConnections, timeout: $timeout, awsStatus: $awsStatus, datacenterStatus: $datacenterStatus, autoScaling: $autoScaling, loadBalancing: $loadBalancing, cachingEnabled: $cachingEnabled, sslEnabled: $sslEnabled, rateLimitingEnabled: $rateLimitingEnabled, ddosProtection: $ddosProtection, lastUpdated: $lastUpdated, customSettings: $customSettings, awsInstances: $awsInstances, datacenterInstances: $datacenterInstances)';
}


}

/// @nodoc
abstract mixin class $ServerConfigCopyWith<$Res>  {
  factory $ServerConfigCopyWith(ServerConfig value, $Res Function(ServerConfig) _then) = _$ServerConfigCopyWithImpl;
@useResult
$Res call({
 String environment, String awsRegion, String datacenterIp, String databaseUrl, String redisUrl, int maxConnections, int timeout, ServerState awsStatus, ServerState datacenterStatus, bool autoScaling, bool loadBalancing, bool cachingEnabled, bool sslEnabled, bool rateLimitingEnabled, bool ddosProtection, DateTime lastUpdated, Map<String, dynamic> customSettings, List<ServerInstance> awsInstances, List<ServerInstance> datacenterInstances
});




}
/// @nodoc
class _$ServerConfigCopyWithImpl<$Res>
    implements $ServerConfigCopyWith<$Res> {
  _$ServerConfigCopyWithImpl(this._self, this._then);

  final ServerConfig _self;
  final $Res Function(ServerConfig) _then;

/// Create a copy of ServerConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? environment = null,Object? awsRegion = null,Object? datacenterIp = null,Object? databaseUrl = null,Object? redisUrl = null,Object? maxConnections = null,Object? timeout = null,Object? awsStatus = null,Object? datacenterStatus = null,Object? autoScaling = null,Object? loadBalancing = null,Object? cachingEnabled = null,Object? sslEnabled = null,Object? rateLimitingEnabled = null,Object? ddosProtection = null,Object? lastUpdated = null,Object? customSettings = null,Object? awsInstances = null,Object? datacenterInstances = null,}) {
  return _then(_self.copyWith(
environment: null == environment ? _self.environment : environment // ignore: cast_nullable_to_non_nullable
as String,awsRegion: null == awsRegion ? _self.awsRegion : awsRegion // ignore: cast_nullable_to_non_nullable
as String,datacenterIp: null == datacenterIp ? _self.datacenterIp : datacenterIp // ignore: cast_nullable_to_non_nullable
as String,databaseUrl: null == databaseUrl ? _self.databaseUrl : databaseUrl // ignore: cast_nullable_to_non_nullable
as String,redisUrl: null == redisUrl ? _self.redisUrl : redisUrl // ignore: cast_nullable_to_non_nullable
as String,maxConnections: null == maxConnections ? _self.maxConnections : maxConnections // ignore: cast_nullable_to_non_nullable
as int,timeout: null == timeout ? _self.timeout : timeout // ignore: cast_nullable_to_non_nullable
as int,awsStatus: null == awsStatus ? _self.awsStatus : awsStatus // ignore: cast_nullable_to_non_nullable
as ServerState,datacenterStatus: null == datacenterStatus ? _self.datacenterStatus : datacenterStatus // ignore: cast_nullable_to_non_nullable
as ServerState,autoScaling: null == autoScaling ? _self.autoScaling : autoScaling // ignore: cast_nullable_to_non_nullable
as bool,loadBalancing: null == loadBalancing ? _self.loadBalancing : loadBalancing // ignore: cast_nullable_to_non_nullable
as bool,cachingEnabled: null == cachingEnabled ? _self.cachingEnabled : cachingEnabled // ignore: cast_nullable_to_non_nullable
as bool,sslEnabled: null == sslEnabled ? _self.sslEnabled : sslEnabled // ignore: cast_nullable_to_non_nullable
as bool,rateLimitingEnabled: null == rateLimitingEnabled ? _self.rateLimitingEnabled : rateLimitingEnabled // ignore: cast_nullable_to_non_nullable
as bool,ddosProtection: null == ddosProtection ? _self.ddosProtection : ddosProtection // ignore: cast_nullable_to_non_nullable
as bool,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,customSettings: null == customSettings ? _self.customSettings : customSettings // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,awsInstances: null == awsInstances ? _self.awsInstances : awsInstances // ignore: cast_nullable_to_non_nullable
as List<ServerInstance>,datacenterInstances: null == datacenterInstances ? _self.datacenterInstances : datacenterInstances // ignore: cast_nullable_to_non_nullable
as List<ServerInstance>,
  ));
}

}


/// Adds pattern-matching-related methods to [ServerConfig].
extension ServerConfigPatterns on ServerConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ServerConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ServerConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ServerConfig value)  $default,){
final _that = this;
switch (_that) {
case _ServerConfig():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ServerConfig value)?  $default,){
final _that = this;
switch (_that) {
case _ServerConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String environment,  String awsRegion,  String datacenterIp,  String databaseUrl,  String redisUrl,  int maxConnections,  int timeout,  ServerState awsStatus,  ServerState datacenterStatus,  bool autoScaling,  bool loadBalancing,  bool cachingEnabled,  bool sslEnabled,  bool rateLimitingEnabled,  bool ddosProtection,  DateTime lastUpdated,  Map<String, dynamic> customSettings,  List<ServerInstance> awsInstances,  List<ServerInstance> datacenterInstances)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ServerConfig() when $default != null:
return $default(_that.environment,_that.awsRegion,_that.datacenterIp,_that.databaseUrl,_that.redisUrl,_that.maxConnections,_that.timeout,_that.awsStatus,_that.datacenterStatus,_that.autoScaling,_that.loadBalancing,_that.cachingEnabled,_that.sslEnabled,_that.rateLimitingEnabled,_that.ddosProtection,_that.lastUpdated,_that.customSettings,_that.awsInstances,_that.datacenterInstances);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String environment,  String awsRegion,  String datacenterIp,  String databaseUrl,  String redisUrl,  int maxConnections,  int timeout,  ServerState awsStatus,  ServerState datacenterStatus,  bool autoScaling,  bool loadBalancing,  bool cachingEnabled,  bool sslEnabled,  bool rateLimitingEnabled,  bool ddosProtection,  DateTime lastUpdated,  Map<String, dynamic> customSettings,  List<ServerInstance> awsInstances,  List<ServerInstance> datacenterInstances)  $default,) {final _that = this;
switch (_that) {
case _ServerConfig():
return $default(_that.environment,_that.awsRegion,_that.datacenterIp,_that.databaseUrl,_that.redisUrl,_that.maxConnections,_that.timeout,_that.awsStatus,_that.datacenterStatus,_that.autoScaling,_that.loadBalancing,_that.cachingEnabled,_that.sslEnabled,_that.rateLimitingEnabled,_that.ddosProtection,_that.lastUpdated,_that.customSettings,_that.awsInstances,_that.datacenterInstances);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String environment,  String awsRegion,  String datacenterIp,  String databaseUrl,  String redisUrl,  int maxConnections,  int timeout,  ServerState awsStatus,  ServerState datacenterStatus,  bool autoScaling,  bool loadBalancing,  bool cachingEnabled,  bool sslEnabled,  bool rateLimitingEnabled,  bool ddosProtection,  DateTime lastUpdated,  Map<String, dynamic> customSettings,  List<ServerInstance> awsInstances,  List<ServerInstance> datacenterInstances)?  $default,) {final _that = this;
switch (_that) {
case _ServerConfig() when $default != null:
return $default(_that.environment,_that.awsRegion,_that.datacenterIp,_that.databaseUrl,_that.redisUrl,_that.maxConnections,_that.timeout,_that.awsStatus,_that.datacenterStatus,_that.autoScaling,_that.loadBalancing,_that.cachingEnabled,_that.sslEnabled,_that.rateLimitingEnabled,_that.ddosProtection,_that.lastUpdated,_that.customSettings,_that.awsInstances,_that.datacenterInstances);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ServerConfig implements ServerConfig {
  const _ServerConfig({required this.environment, required this.awsRegion, required this.datacenterIp, required this.databaseUrl, required this.redisUrl, required this.maxConnections, required this.timeout, required this.awsStatus, required this.datacenterStatus, required this.autoScaling, required this.loadBalancing, required this.cachingEnabled, required this.sslEnabled, required this.rateLimitingEnabled, required this.ddosProtection, required this.lastUpdated, final  Map<String, dynamic> customSettings = const {}, final  List<ServerInstance> awsInstances = const [], final  List<ServerInstance> datacenterInstances = const []}): _customSettings = customSettings,_awsInstances = awsInstances,_datacenterInstances = datacenterInstances;
  factory _ServerConfig.fromJson(Map<String, dynamic> json) => _$ServerConfigFromJson(json);

@override final  String environment;
@override final  String awsRegion;
@override final  String datacenterIp;
@override final  String databaseUrl;
@override final  String redisUrl;
@override final  int maxConnections;
@override final  int timeout;
@override final  ServerState awsStatus;
@override final  ServerState datacenterStatus;
@override final  bool autoScaling;
@override final  bool loadBalancing;
@override final  bool cachingEnabled;
@override final  bool sslEnabled;
@override final  bool rateLimitingEnabled;
@override final  bool ddosProtection;
@override final  DateTime lastUpdated;
 final  Map<String, dynamic> _customSettings;
@override@JsonKey() Map<String, dynamic> get customSettings {
  if (_customSettings is EqualUnmodifiableMapView) return _customSettings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_customSettings);
}

 final  List<ServerInstance> _awsInstances;
@override@JsonKey() List<ServerInstance> get awsInstances {
  if (_awsInstances is EqualUnmodifiableListView) return _awsInstances;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_awsInstances);
}

 final  List<ServerInstance> _datacenterInstances;
@override@JsonKey() List<ServerInstance> get datacenterInstances {
  if (_datacenterInstances is EqualUnmodifiableListView) return _datacenterInstances;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_datacenterInstances);
}


/// Create a copy of ServerConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ServerConfigCopyWith<_ServerConfig> get copyWith => __$ServerConfigCopyWithImpl<_ServerConfig>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ServerConfigToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ServerConfig&&(identical(other.environment, environment) || other.environment == environment)&&(identical(other.awsRegion, awsRegion) || other.awsRegion == awsRegion)&&(identical(other.datacenterIp, datacenterIp) || other.datacenterIp == datacenterIp)&&(identical(other.databaseUrl, databaseUrl) || other.databaseUrl == databaseUrl)&&(identical(other.redisUrl, redisUrl) || other.redisUrl == redisUrl)&&(identical(other.maxConnections, maxConnections) || other.maxConnections == maxConnections)&&(identical(other.timeout, timeout) || other.timeout == timeout)&&(identical(other.awsStatus, awsStatus) || other.awsStatus == awsStatus)&&(identical(other.datacenterStatus, datacenterStatus) || other.datacenterStatus == datacenterStatus)&&(identical(other.autoScaling, autoScaling) || other.autoScaling == autoScaling)&&(identical(other.loadBalancing, loadBalancing) || other.loadBalancing == loadBalancing)&&(identical(other.cachingEnabled, cachingEnabled) || other.cachingEnabled == cachingEnabled)&&(identical(other.sslEnabled, sslEnabled) || other.sslEnabled == sslEnabled)&&(identical(other.rateLimitingEnabled, rateLimitingEnabled) || other.rateLimitingEnabled == rateLimitingEnabled)&&(identical(other.ddosProtection, ddosProtection) || other.ddosProtection == ddosProtection)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&const DeepCollectionEquality().equals(other._customSettings, _customSettings)&&const DeepCollectionEquality().equals(other._awsInstances, _awsInstances)&&const DeepCollectionEquality().equals(other._datacenterInstances, _datacenterInstances));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,environment,awsRegion,datacenterIp,databaseUrl,redisUrl,maxConnections,timeout,awsStatus,datacenterStatus,autoScaling,loadBalancing,cachingEnabled,sslEnabled,rateLimitingEnabled,ddosProtection,lastUpdated,const DeepCollectionEquality().hash(_customSettings),const DeepCollectionEquality().hash(_awsInstances),const DeepCollectionEquality().hash(_datacenterInstances)]);

@override
String toString() {
  return 'ServerConfig(environment: $environment, awsRegion: $awsRegion, datacenterIp: $datacenterIp, databaseUrl: $databaseUrl, redisUrl: $redisUrl, maxConnections: $maxConnections, timeout: $timeout, awsStatus: $awsStatus, datacenterStatus: $datacenterStatus, autoScaling: $autoScaling, loadBalancing: $loadBalancing, cachingEnabled: $cachingEnabled, sslEnabled: $sslEnabled, rateLimitingEnabled: $rateLimitingEnabled, ddosProtection: $ddosProtection, lastUpdated: $lastUpdated, customSettings: $customSettings, awsInstances: $awsInstances, datacenterInstances: $datacenterInstances)';
}


}

/// @nodoc
abstract mixin class _$ServerConfigCopyWith<$Res> implements $ServerConfigCopyWith<$Res> {
  factory _$ServerConfigCopyWith(_ServerConfig value, $Res Function(_ServerConfig) _then) = __$ServerConfigCopyWithImpl;
@override @useResult
$Res call({
 String environment, String awsRegion, String datacenterIp, String databaseUrl, String redisUrl, int maxConnections, int timeout, ServerState awsStatus, ServerState datacenterStatus, bool autoScaling, bool loadBalancing, bool cachingEnabled, bool sslEnabled, bool rateLimitingEnabled, bool ddosProtection, DateTime lastUpdated, Map<String, dynamic> customSettings, List<ServerInstance> awsInstances, List<ServerInstance> datacenterInstances
});




}
/// @nodoc
class __$ServerConfigCopyWithImpl<$Res>
    implements _$ServerConfigCopyWith<$Res> {
  __$ServerConfigCopyWithImpl(this._self, this._then);

  final _ServerConfig _self;
  final $Res Function(_ServerConfig) _then;

/// Create a copy of ServerConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? environment = null,Object? awsRegion = null,Object? datacenterIp = null,Object? databaseUrl = null,Object? redisUrl = null,Object? maxConnections = null,Object? timeout = null,Object? awsStatus = null,Object? datacenterStatus = null,Object? autoScaling = null,Object? loadBalancing = null,Object? cachingEnabled = null,Object? sslEnabled = null,Object? rateLimitingEnabled = null,Object? ddosProtection = null,Object? lastUpdated = null,Object? customSettings = null,Object? awsInstances = null,Object? datacenterInstances = null,}) {
  return _then(_ServerConfig(
environment: null == environment ? _self.environment : environment // ignore: cast_nullable_to_non_nullable
as String,awsRegion: null == awsRegion ? _self.awsRegion : awsRegion // ignore: cast_nullable_to_non_nullable
as String,datacenterIp: null == datacenterIp ? _self.datacenterIp : datacenterIp // ignore: cast_nullable_to_non_nullable
as String,databaseUrl: null == databaseUrl ? _self.databaseUrl : databaseUrl // ignore: cast_nullable_to_non_nullable
as String,redisUrl: null == redisUrl ? _self.redisUrl : redisUrl // ignore: cast_nullable_to_non_nullable
as String,maxConnections: null == maxConnections ? _self.maxConnections : maxConnections // ignore: cast_nullable_to_non_nullable
as int,timeout: null == timeout ? _self.timeout : timeout // ignore: cast_nullable_to_non_nullable
as int,awsStatus: null == awsStatus ? _self.awsStatus : awsStatus // ignore: cast_nullable_to_non_nullable
as ServerState,datacenterStatus: null == datacenterStatus ? _self.datacenterStatus : datacenterStatus // ignore: cast_nullable_to_non_nullable
as ServerState,autoScaling: null == autoScaling ? _self.autoScaling : autoScaling // ignore: cast_nullable_to_non_nullable
as bool,loadBalancing: null == loadBalancing ? _self.loadBalancing : loadBalancing // ignore: cast_nullable_to_non_nullable
as bool,cachingEnabled: null == cachingEnabled ? _self.cachingEnabled : cachingEnabled // ignore: cast_nullable_to_non_nullable
as bool,sslEnabled: null == sslEnabled ? _self.sslEnabled : sslEnabled // ignore: cast_nullable_to_non_nullable
as bool,rateLimitingEnabled: null == rateLimitingEnabled ? _self.rateLimitingEnabled : rateLimitingEnabled // ignore: cast_nullable_to_non_nullable
as bool,ddosProtection: null == ddosProtection ? _self.ddosProtection : ddosProtection // ignore: cast_nullable_to_non_nullable
as bool,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,customSettings: null == customSettings ? _self._customSettings : customSettings // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,awsInstances: null == awsInstances ? _self._awsInstances : awsInstances // ignore: cast_nullable_to_non_nullable
as List<ServerInstance>,datacenterInstances: null == datacenterInstances ? _self._datacenterInstances : datacenterInstances // ignore: cast_nullable_to_non_nullable
as List<ServerInstance>,
  ));
}


}


/// @nodoc
mixin _$ServerInstance {

 String get id; String get name; String get ipAddress; int get port; ServerInstanceType get type; ServerState get status; ServerMetrics get metrics; DateTime get lastHealthCheck; String get region; Map<String, String> get tags;
/// Create a copy of ServerInstance
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ServerInstanceCopyWith<ServerInstance> get copyWith => _$ServerInstanceCopyWithImpl<ServerInstance>(this as ServerInstance, _$identity);

  /// Serializes this ServerInstance to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ServerInstance&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.ipAddress, ipAddress) || other.ipAddress == ipAddress)&&(identical(other.port, port) || other.port == port)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.metrics, metrics) || other.metrics == metrics)&&(identical(other.lastHealthCheck, lastHealthCheck) || other.lastHealthCheck == lastHealthCheck)&&(identical(other.region, region) || other.region == region)&&const DeepCollectionEquality().equals(other.tags, tags));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,ipAddress,port,type,status,metrics,lastHealthCheck,region,const DeepCollectionEquality().hash(tags));

@override
String toString() {
  return 'ServerInstance(id: $id, name: $name, ipAddress: $ipAddress, port: $port, type: $type, status: $status, metrics: $metrics, lastHealthCheck: $lastHealthCheck, region: $region, tags: $tags)';
}


}

/// @nodoc
abstract mixin class $ServerInstanceCopyWith<$Res>  {
  factory $ServerInstanceCopyWith(ServerInstance value, $Res Function(ServerInstance) _then) = _$ServerInstanceCopyWithImpl;
@useResult
$Res call({
 String id, String name, String ipAddress, int port, ServerInstanceType type, ServerState status, ServerMetrics metrics, DateTime lastHealthCheck, String region, Map<String, String> tags
});


$ServerMetricsCopyWith<$Res> get metrics;

}
/// @nodoc
class _$ServerInstanceCopyWithImpl<$Res>
    implements $ServerInstanceCopyWith<$Res> {
  _$ServerInstanceCopyWithImpl(this._self, this._then);

  final ServerInstance _self;
  final $Res Function(ServerInstance) _then;

/// Create a copy of ServerInstance
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? ipAddress = null,Object? port = null,Object? type = null,Object? status = null,Object? metrics = null,Object? lastHealthCheck = null,Object? region = null,Object? tags = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,ipAddress: null == ipAddress ? _self.ipAddress : ipAddress // ignore: cast_nullable_to_non_nullable
as String,port: null == port ? _self.port : port // ignore: cast_nullable_to_non_nullable
as int,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ServerInstanceType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ServerState,metrics: null == metrics ? _self.metrics : metrics // ignore: cast_nullable_to_non_nullable
as ServerMetrics,lastHealthCheck: null == lastHealthCheck ? _self.lastHealthCheck : lastHealthCheck // ignore: cast_nullable_to_non_nullable
as DateTime,region: null == region ? _self.region : region // ignore: cast_nullable_to_non_nullable
as String,tags: null == tags ? _self.tags : tags // ignore: cast_nullable_to_non_nullable
as Map<String, String>,
  ));
}
/// Create a copy of ServerInstance
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ServerMetricsCopyWith<$Res> get metrics {
  
  return $ServerMetricsCopyWith<$Res>(_self.metrics, (value) {
    return _then(_self.copyWith(metrics: value));
  });
}
}


/// Adds pattern-matching-related methods to [ServerInstance].
extension ServerInstancePatterns on ServerInstance {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ServerInstance value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ServerInstance() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ServerInstance value)  $default,){
final _that = this;
switch (_that) {
case _ServerInstance():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ServerInstance value)?  $default,){
final _that = this;
switch (_that) {
case _ServerInstance() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String ipAddress,  int port,  ServerInstanceType type,  ServerState status,  ServerMetrics metrics,  DateTime lastHealthCheck,  String region,  Map<String, String> tags)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ServerInstance() when $default != null:
return $default(_that.id,_that.name,_that.ipAddress,_that.port,_that.type,_that.status,_that.metrics,_that.lastHealthCheck,_that.region,_that.tags);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String ipAddress,  int port,  ServerInstanceType type,  ServerState status,  ServerMetrics metrics,  DateTime lastHealthCheck,  String region,  Map<String, String> tags)  $default,) {final _that = this;
switch (_that) {
case _ServerInstance():
return $default(_that.id,_that.name,_that.ipAddress,_that.port,_that.type,_that.status,_that.metrics,_that.lastHealthCheck,_that.region,_that.tags);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String ipAddress,  int port,  ServerInstanceType type,  ServerState status,  ServerMetrics metrics,  DateTime lastHealthCheck,  String region,  Map<String, String> tags)?  $default,) {final _that = this;
switch (_that) {
case _ServerInstance() when $default != null:
return $default(_that.id,_that.name,_that.ipAddress,_that.port,_that.type,_that.status,_that.metrics,_that.lastHealthCheck,_that.region,_that.tags);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ServerInstance implements ServerInstance {
  const _ServerInstance({required this.id, required this.name, required this.ipAddress, required this.port, required this.type, required this.status, required this.metrics, required this.lastHealthCheck, this.region = 'Unknown', final  Map<String, String> tags = const {}}): _tags = tags;
  factory _ServerInstance.fromJson(Map<String, dynamic> json) => _$ServerInstanceFromJson(json);

@override final  String id;
@override final  String name;
@override final  String ipAddress;
@override final  int port;
@override final  ServerInstanceType type;
@override final  ServerState status;
@override final  ServerMetrics metrics;
@override final  DateTime lastHealthCheck;
@override@JsonKey() final  String region;
 final  Map<String, String> _tags;
@override@JsonKey() Map<String, String> get tags {
  if (_tags is EqualUnmodifiableMapView) return _tags;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_tags);
}


/// Create a copy of ServerInstance
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ServerInstanceCopyWith<_ServerInstance> get copyWith => __$ServerInstanceCopyWithImpl<_ServerInstance>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ServerInstanceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ServerInstance&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.ipAddress, ipAddress) || other.ipAddress == ipAddress)&&(identical(other.port, port) || other.port == port)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.metrics, metrics) || other.metrics == metrics)&&(identical(other.lastHealthCheck, lastHealthCheck) || other.lastHealthCheck == lastHealthCheck)&&(identical(other.region, region) || other.region == region)&&const DeepCollectionEquality().equals(other._tags, _tags));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,ipAddress,port,type,status,metrics,lastHealthCheck,region,const DeepCollectionEquality().hash(_tags));

@override
String toString() {
  return 'ServerInstance(id: $id, name: $name, ipAddress: $ipAddress, port: $port, type: $type, status: $status, metrics: $metrics, lastHealthCheck: $lastHealthCheck, region: $region, tags: $tags)';
}


}

/// @nodoc
abstract mixin class _$ServerInstanceCopyWith<$Res> implements $ServerInstanceCopyWith<$Res> {
  factory _$ServerInstanceCopyWith(_ServerInstance value, $Res Function(_ServerInstance) _then) = __$ServerInstanceCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String ipAddress, int port, ServerInstanceType type, ServerState status, ServerMetrics metrics, DateTime lastHealthCheck, String region, Map<String, String> tags
});


@override $ServerMetricsCopyWith<$Res> get metrics;

}
/// @nodoc
class __$ServerInstanceCopyWithImpl<$Res>
    implements _$ServerInstanceCopyWith<$Res> {
  __$ServerInstanceCopyWithImpl(this._self, this._then);

  final _ServerInstance _self;
  final $Res Function(_ServerInstance) _then;

/// Create a copy of ServerInstance
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? ipAddress = null,Object? port = null,Object? type = null,Object? status = null,Object? metrics = null,Object? lastHealthCheck = null,Object? region = null,Object? tags = null,}) {
  return _then(_ServerInstance(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,ipAddress: null == ipAddress ? _self.ipAddress : ipAddress // ignore: cast_nullable_to_non_nullable
as String,port: null == port ? _self.port : port // ignore: cast_nullable_to_non_nullable
as int,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ServerInstanceType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ServerState,metrics: null == metrics ? _self.metrics : metrics // ignore: cast_nullable_to_non_nullable
as ServerMetrics,lastHealthCheck: null == lastHealthCheck ? _self.lastHealthCheck : lastHealthCheck // ignore: cast_nullable_to_non_nullable
as DateTime,region: null == region ? _self.region : region // ignore: cast_nullable_to_non_nullable
as String,tags: null == tags ? _self._tags : tags // ignore: cast_nullable_to_non_nullable
as Map<String, String>,
  ));
}

/// Create a copy of ServerInstance
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ServerMetricsCopyWith<$Res> get metrics {
  
  return $ServerMetricsCopyWith<$Res>(_self.metrics, (value) {
    return _then(_self.copyWith(metrics: value));
  });
}
}


/// @nodoc
mixin _$ServerMetrics {

 double get cpuUsage; double get memoryUsage; double get diskUsage; double get networkIn; double get networkOut; int get activeConnections; int get requestsPerSecond; double get averageResponseTime; double get errorRate; DateTime get timestamp;
/// Create a copy of ServerMetrics
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ServerMetricsCopyWith<ServerMetrics> get copyWith => _$ServerMetricsCopyWithImpl<ServerMetrics>(this as ServerMetrics, _$identity);

  /// Serializes this ServerMetrics to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ServerMetrics&&(identical(other.cpuUsage, cpuUsage) || other.cpuUsage == cpuUsage)&&(identical(other.memoryUsage, memoryUsage) || other.memoryUsage == memoryUsage)&&(identical(other.diskUsage, diskUsage) || other.diskUsage == diskUsage)&&(identical(other.networkIn, networkIn) || other.networkIn == networkIn)&&(identical(other.networkOut, networkOut) || other.networkOut == networkOut)&&(identical(other.activeConnections, activeConnections) || other.activeConnections == activeConnections)&&(identical(other.requestsPerSecond, requestsPerSecond) || other.requestsPerSecond == requestsPerSecond)&&(identical(other.averageResponseTime, averageResponseTime) || other.averageResponseTime == averageResponseTime)&&(identical(other.errorRate, errorRate) || other.errorRate == errorRate)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,cpuUsage,memoryUsage,diskUsage,networkIn,networkOut,activeConnections,requestsPerSecond,averageResponseTime,errorRate,timestamp);

@override
String toString() {
  return 'ServerMetrics(cpuUsage: $cpuUsage, memoryUsage: $memoryUsage, diskUsage: $diskUsage, networkIn: $networkIn, networkOut: $networkOut, activeConnections: $activeConnections, requestsPerSecond: $requestsPerSecond, averageResponseTime: $averageResponseTime, errorRate: $errorRate, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class $ServerMetricsCopyWith<$Res>  {
  factory $ServerMetricsCopyWith(ServerMetrics value, $Res Function(ServerMetrics) _then) = _$ServerMetricsCopyWithImpl;
@useResult
$Res call({
 double cpuUsage, double memoryUsage, double diskUsage, double networkIn, double networkOut, int activeConnections, int requestsPerSecond, double averageResponseTime, double errorRate, DateTime timestamp
});




}
/// @nodoc
class _$ServerMetricsCopyWithImpl<$Res>
    implements $ServerMetricsCopyWith<$Res> {
  _$ServerMetricsCopyWithImpl(this._self, this._then);

  final ServerMetrics _self;
  final $Res Function(ServerMetrics) _then;

/// Create a copy of ServerMetrics
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? cpuUsage = null,Object? memoryUsage = null,Object? diskUsage = null,Object? networkIn = null,Object? networkOut = null,Object? activeConnections = null,Object? requestsPerSecond = null,Object? averageResponseTime = null,Object? errorRate = null,Object? timestamp = null,}) {
  return _then(_self.copyWith(
cpuUsage: null == cpuUsage ? _self.cpuUsage : cpuUsage // ignore: cast_nullable_to_non_nullable
as double,memoryUsage: null == memoryUsage ? _self.memoryUsage : memoryUsage // ignore: cast_nullable_to_non_nullable
as double,diskUsage: null == diskUsage ? _self.diskUsage : diskUsage // ignore: cast_nullable_to_non_nullable
as double,networkIn: null == networkIn ? _self.networkIn : networkIn // ignore: cast_nullable_to_non_nullable
as double,networkOut: null == networkOut ? _self.networkOut : networkOut // ignore: cast_nullable_to_non_nullable
as double,activeConnections: null == activeConnections ? _self.activeConnections : activeConnections // ignore: cast_nullable_to_non_nullable
as int,requestsPerSecond: null == requestsPerSecond ? _self.requestsPerSecond : requestsPerSecond // ignore: cast_nullable_to_non_nullable
as int,averageResponseTime: null == averageResponseTime ? _self.averageResponseTime : averageResponseTime // ignore: cast_nullable_to_non_nullable
as double,errorRate: null == errorRate ? _self.errorRate : errorRate // ignore: cast_nullable_to_non_nullable
as double,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [ServerMetrics].
extension ServerMetricsPatterns on ServerMetrics {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ServerMetrics value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ServerMetrics() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ServerMetrics value)  $default,){
final _that = this;
switch (_that) {
case _ServerMetrics():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ServerMetrics value)?  $default,){
final _that = this;
switch (_that) {
case _ServerMetrics() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double cpuUsage,  double memoryUsage,  double diskUsage,  double networkIn,  double networkOut,  int activeConnections,  int requestsPerSecond,  double averageResponseTime,  double errorRate,  DateTime timestamp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ServerMetrics() when $default != null:
return $default(_that.cpuUsage,_that.memoryUsage,_that.diskUsage,_that.networkIn,_that.networkOut,_that.activeConnections,_that.requestsPerSecond,_that.averageResponseTime,_that.errorRate,_that.timestamp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double cpuUsage,  double memoryUsage,  double diskUsage,  double networkIn,  double networkOut,  int activeConnections,  int requestsPerSecond,  double averageResponseTime,  double errorRate,  DateTime timestamp)  $default,) {final _that = this;
switch (_that) {
case _ServerMetrics():
return $default(_that.cpuUsage,_that.memoryUsage,_that.diskUsage,_that.networkIn,_that.networkOut,_that.activeConnections,_that.requestsPerSecond,_that.averageResponseTime,_that.errorRate,_that.timestamp);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double cpuUsage,  double memoryUsage,  double diskUsage,  double networkIn,  double networkOut,  int activeConnections,  int requestsPerSecond,  double averageResponseTime,  double errorRate,  DateTime timestamp)?  $default,) {final _that = this;
switch (_that) {
case _ServerMetrics() when $default != null:
return $default(_that.cpuUsage,_that.memoryUsage,_that.diskUsage,_that.networkIn,_that.networkOut,_that.activeConnections,_that.requestsPerSecond,_that.averageResponseTime,_that.errorRate,_that.timestamp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ServerMetrics implements ServerMetrics {
  const _ServerMetrics({required this.cpuUsage, required this.memoryUsage, required this.diskUsage, required this.networkIn, required this.networkOut, required this.activeConnections, required this.requestsPerSecond, required this.averageResponseTime, required this.errorRate, required this.timestamp});
  factory _ServerMetrics.fromJson(Map<String, dynamic> json) => _$ServerMetricsFromJson(json);

@override final  double cpuUsage;
@override final  double memoryUsage;
@override final  double diskUsage;
@override final  double networkIn;
@override final  double networkOut;
@override final  int activeConnections;
@override final  int requestsPerSecond;
@override final  double averageResponseTime;
@override final  double errorRate;
@override final  DateTime timestamp;

/// Create a copy of ServerMetrics
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ServerMetricsCopyWith<_ServerMetrics> get copyWith => __$ServerMetricsCopyWithImpl<_ServerMetrics>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ServerMetricsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ServerMetrics&&(identical(other.cpuUsage, cpuUsage) || other.cpuUsage == cpuUsage)&&(identical(other.memoryUsage, memoryUsage) || other.memoryUsage == memoryUsage)&&(identical(other.diskUsage, diskUsage) || other.diskUsage == diskUsage)&&(identical(other.networkIn, networkIn) || other.networkIn == networkIn)&&(identical(other.networkOut, networkOut) || other.networkOut == networkOut)&&(identical(other.activeConnections, activeConnections) || other.activeConnections == activeConnections)&&(identical(other.requestsPerSecond, requestsPerSecond) || other.requestsPerSecond == requestsPerSecond)&&(identical(other.averageResponseTime, averageResponseTime) || other.averageResponseTime == averageResponseTime)&&(identical(other.errorRate, errorRate) || other.errorRate == errorRate)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,cpuUsage,memoryUsage,diskUsage,networkIn,networkOut,activeConnections,requestsPerSecond,averageResponseTime,errorRate,timestamp);

@override
String toString() {
  return 'ServerMetrics(cpuUsage: $cpuUsage, memoryUsage: $memoryUsage, diskUsage: $diskUsage, networkIn: $networkIn, networkOut: $networkOut, activeConnections: $activeConnections, requestsPerSecond: $requestsPerSecond, averageResponseTime: $averageResponseTime, errorRate: $errorRate, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class _$ServerMetricsCopyWith<$Res> implements $ServerMetricsCopyWith<$Res> {
  factory _$ServerMetricsCopyWith(_ServerMetrics value, $Res Function(_ServerMetrics) _then) = __$ServerMetricsCopyWithImpl;
@override @useResult
$Res call({
 double cpuUsage, double memoryUsage, double diskUsage, double networkIn, double networkOut, int activeConnections, int requestsPerSecond, double averageResponseTime, double errorRate, DateTime timestamp
});




}
/// @nodoc
class __$ServerMetricsCopyWithImpl<$Res>
    implements _$ServerMetricsCopyWith<$Res> {
  __$ServerMetricsCopyWithImpl(this._self, this._then);

  final _ServerMetrics _self;
  final $Res Function(_ServerMetrics) _then;

/// Create a copy of ServerMetrics
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? cpuUsage = null,Object? memoryUsage = null,Object? diskUsage = null,Object? networkIn = null,Object? networkOut = null,Object? activeConnections = null,Object? requestsPerSecond = null,Object? averageResponseTime = null,Object? errorRate = null,Object? timestamp = null,}) {
  return _then(_ServerMetrics(
cpuUsage: null == cpuUsage ? _self.cpuUsage : cpuUsage // ignore: cast_nullable_to_non_nullable
as double,memoryUsage: null == memoryUsage ? _self.memoryUsage : memoryUsage // ignore: cast_nullable_to_non_nullable
as double,diskUsage: null == diskUsage ? _self.diskUsage : diskUsage // ignore: cast_nullable_to_non_nullable
as double,networkIn: null == networkIn ? _self.networkIn : networkIn // ignore: cast_nullable_to_non_nullable
as double,networkOut: null == networkOut ? _self.networkOut : networkOut // ignore: cast_nullable_to_non_nullable
as double,activeConnections: null == activeConnections ? _self.activeConnections : activeConnections // ignore: cast_nullable_to_non_nullable
as int,requestsPerSecond: null == requestsPerSecond ? _self.requestsPerSecond : requestsPerSecond // ignore: cast_nullable_to_non_nullable
as int,averageResponseTime: null == averageResponseTime ? _self.averageResponseTime : averageResponseTime // ignore: cast_nullable_to_non_nullable
as double,errorRate: null == errorRate ? _self.errorRate : errorRate // ignore: cast_nullable_to_non_nullable
as double,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$AutoScalingConfig {

 bool get enabled; int get minInstances; int get maxInstances; double get cpuThresholdUp; double get cpuThresholdDown; double get memoryThresholdUp; double get memoryThresholdDown; int get scaleUpCooldown; int get scaleDownCooldown; List<ScalingPolicy> get customPolicies;
/// Create a copy of AutoScalingConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AutoScalingConfigCopyWith<AutoScalingConfig> get copyWith => _$AutoScalingConfigCopyWithImpl<AutoScalingConfig>(this as AutoScalingConfig, _$identity);

  /// Serializes this AutoScalingConfig to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AutoScalingConfig&&(identical(other.enabled, enabled) || other.enabled == enabled)&&(identical(other.minInstances, minInstances) || other.minInstances == minInstances)&&(identical(other.maxInstances, maxInstances) || other.maxInstances == maxInstances)&&(identical(other.cpuThresholdUp, cpuThresholdUp) || other.cpuThresholdUp == cpuThresholdUp)&&(identical(other.cpuThresholdDown, cpuThresholdDown) || other.cpuThresholdDown == cpuThresholdDown)&&(identical(other.memoryThresholdUp, memoryThresholdUp) || other.memoryThresholdUp == memoryThresholdUp)&&(identical(other.memoryThresholdDown, memoryThresholdDown) || other.memoryThresholdDown == memoryThresholdDown)&&(identical(other.scaleUpCooldown, scaleUpCooldown) || other.scaleUpCooldown == scaleUpCooldown)&&(identical(other.scaleDownCooldown, scaleDownCooldown) || other.scaleDownCooldown == scaleDownCooldown)&&const DeepCollectionEquality().equals(other.customPolicies, customPolicies));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,enabled,minInstances,maxInstances,cpuThresholdUp,cpuThresholdDown,memoryThresholdUp,memoryThresholdDown,scaleUpCooldown,scaleDownCooldown,const DeepCollectionEquality().hash(customPolicies));

@override
String toString() {
  return 'AutoScalingConfig(enabled: $enabled, minInstances: $minInstances, maxInstances: $maxInstances, cpuThresholdUp: $cpuThresholdUp, cpuThresholdDown: $cpuThresholdDown, memoryThresholdUp: $memoryThresholdUp, memoryThresholdDown: $memoryThresholdDown, scaleUpCooldown: $scaleUpCooldown, scaleDownCooldown: $scaleDownCooldown, customPolicies: $customPolicies)';
}


}

/// @nodoc
abstract mixin class $AutoScalingConfigCopyWith<$Res>  {
  factory $AutoScalingConfigCopyWith(AutoScalingConfig value, $Res Function(AutoScalingConfig) _then) = _$AutoScalingConfigCopyWithImpl;
@useResult
$Res call({
 bool enabled, int minInstances, int maxInstances, double cpuThresholdUp, double cpuThresholdDown, double memoryThresholdUp, double memoryThresholdDown, int scaleUpCooldown, int scaleDownCooldown, List<ScalingPolicy> customPolicies
});




}
/// @nodoc
class _$AutoScalingConfigCopyWithImpl<$Res>
    implements $AutoScalingConfigCopyWith<$Res> {
  _$AutoScalingConfigCopyWithImpl(this._self, this._then);

  final AutoScalingConfig _self;
  final $Res Function(AutoScalingConfig) _then;

/// Create a copy of AutoScalingConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? enabled = null,Object? minInstances = null,Object? maxInstances = null,Object? cpuThresholdUp = null,Object? cpuThresholdDown = null,Object? memoryThresholdUp = null,Object? memoryThresholdDown = null,Object? scaleUpCooldown = null,Object? scaleDownCooldown = null,Object? customPolicies = null,}) {
  return _then(_self.copyWith(
enabled: null == enabled ? _self.enabled : enabled // ignore: cast_nullable_to_non_nullable
as bool,minInstances: null == minInstances ? _self.minInstances : minInstances // ignore: cast_nullable_to_non_nullable
as int,maxInstances: null == maxInstances ? _self.maxInstances : maxInstances // ignore: cast_nullable_to_non_nullable
as int,cpuThresholdUp: null == cpuThresholdUp ? _self.cpuThresholdUp : cpuThresholdUp // ignore: cast_nullable_to_non_nullable
as double,cpuThresholdDown: null == cpuThresholdDown ? _self.cpuThresholdDown : cpuThresholdDown // ignore: cast_nullable_to_non_nullable
as double,memoryThresholdUp: null == memoryThresholdUp ? _self.memoryThresholdUp : memoryThresholdUp // ignore: cast_nullable_to_non_nullable
as double,memoryThresholdDown: null == memoryThresholdDown ? _self.memoryThresholdDown : memoryThresholdDown // ignore: cast_nullable_to_non_nullable
as double,scaleUpCooldown: null == scaleUpCooldown ? _self.scaleUpCooldown : scaleUpCooldown // ignore: cast_nullable_to_non_nullable
as int,scaleDownCooldown: null == scaleDownCooldown ? _self.scaleDownCooldown : scaleDownCooldown // ignore: cast_nullable_to_non_nullable
as int,customPolicies: null == customPolicies ? _self.customPolicies : customPolicies // ignore: cast_nullable_to_non_nullable
as List<ScalingPolicy>,
  ));
}

}


/// Adds pattern-matching-related methods to [AutoScalingConfig].
extension AutoScalingConfigPatterns on AutoScalingConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AutoScalingConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AutoScalingConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AutoScalingConfig value)  $default,){
final _that = this;
switch (_that) {
case _AutoScalingConfig():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AutoScalingConfig value)?  $default,){
final _that = this;
switch (_that) {
case _AutoScalingConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool enabled,  int minInstances,  int maxInstances,  double cpuThresholdUp,  double cpuThresholdDown,  double memoryThresholdUp,  double memoryThresholdDown,  int scaleUpCooldown,  int scaleDownCooldown,  List<ScalingPolicy> customPolicies)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AutoScalingConfig() when $default != null:
return $default(_that.enabled,_that.minInstances,_that.maxInstances,_that.cpuThresholdUp,_that.cpuThresholdDown,_that.memoryThresholdUp,_that.memoryThresholdDown,_that.scaleUpCooldown,_that.scaleDownCooldown,_that.customPolicies);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool enabled,  int minInstances,  int maxInstances,  double cpuThresholdUp,  double cpuThresholdDown,  double memoryThresholdUp,  double memoryThresholdDown,  int scaleUpCooldown,  int scaleDownCooldown,  List<ScalingPolicy> customPolicies)  $default,) {final _that = this;
switch (_that) {
case _AutoScalingConfig():
return $default(_that.enabled,_that.minInstances,_that.maxInstances,_that.cpuThresholdUp,_that.cpuThresholdDown,_that.memoryThresholdUp,_that.memoryThresholdDown,_that.scaleUpCooldown,_that.scaleDownCooldown,_that.customPolicies);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool enabled,  int minInstances,  int maxInstances,  double cpuThresholdUp,  double cpuThresholdDown,  double memoryThresholdUp,  double memoryThresholdDown,  int scaleUpCooldown,  int scaleDownCooldown,  List<ScalingPolicy> customPolicies)?  $default,) {final _that = this;
switch (_that) {
case _AutoScalingConfig() when $default != null:
return $default(_that.enabled,_that.minInstances,_that.maxInstances,_that.cpuThresholdUp,_that.cpuThresholdDown,_that.memoryThresholdUp,_that.memoryThresholdDown,_that.scaleUpCooldown,_that.scaleDownCooldown,_that.customPolicies);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AutoScalingConfig implements AutoScalingConfig {
  const _AutoScalingConfig({required this.enabled, required this.minInstances, required this.maxInstances, required this.cpuThresholdUp, required this.cpuThresholdDown, required this.memoryThresholdUp, required this.memoryThresholdDown, required this.scaleUpCooldown, required this.scaleDownCooldown, final  List<ScalingPolicy> customPolicies = const []}): _customPolicies = customPolicies;
  factory _AutoScalingConfig.fromJson(Map<String, dynamic> json) => _$AutoScalingConfigFromJson(json);

@override final  bool enabled;
@override final  int minInstances;
@override final  int maxInstances;
@override final  double cpuThresholdUp;
@override final  double cpuThresholdDown;
@override final  double memoryThresholdUp;
@override final  double memoryThresholdDown;
@override final  int scaleUpCooldown;
@override final  int scaleDownCooldown;
 final  List<ScalingPolicy> _customPolicies;
@override@JsonKey() List<ScalingPolicy> get customPolicies {
  if (_customPolicies is EqualUnmodifiableListView) return _customPolicies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_customPolicies);
}


/// Create a copy of AutoScalingConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AutoScalingConfigCopyWith<_AutoScalingConfig> get copyWith => __$AutoScalingConfigCopyWithImpl<_AutoScalingConfig>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AutoScalingConfigToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AutoScalingConfig&&(identical(other.enabled, enabled) || other.enabled == enabled)&&(identical(other.minInstances, minInstances) || other.minInstances == minInstances)&&(identical(other.maxInstances, maxInstances) || other.maxInstances == maxInstances)&&(identical(other.cpuThresholdUp, cpuThresholdUp) || other.cpuThresholdUp == cpuThresholdUp)&&(identical(other.cpuThresholdDown, cpuThresholdDown) || other.cpuThresholdDown == cpuThresholdDown)&&(identical(other.memoryThresholdUp, memoryThresholdUp) || other.memoryThresholdUp == memoryThresholdUp)&&(identical(other.memoryThresholdDown, memoryThresholdDown) || other.memoryThresholdDown == memoryThresholdDown)&&(identical(other.scaleUpCooldown, scaleUpCooldown) || other.scaleUpCooldown == scaleUpCooldown)&&(identical(other.scaleDownCooldown, scaleDownCooldown) || other.scaleDownCooldown == scaleDownCooldown)&&const DeepCollectionEquality().equals(other._customPolicies, _customPolicies));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,enabled,minInstances,maxInstances,cpuThresholdUp,cpuThresholdDown,memoryThresholdUp,memoryThresholdDown,scaleUpCooldown,scaleDownCooldown,const DeepCollectionEquality().hash(_customPolicies));

@override
String toString() {
  return 'AutoScalingConfig(enabled: $enabled, minInstances: $minInstances, maxInstances: $maxInstances, cpuThresholdUp: $cpuThresholdUp, cpuThresholdDown: $cpuThresholdDown, memoryThresholdUp: $memoryThresholdUp, memoryThresholdDown: $memoryThresholdDown, scaleUpCooldown: $scaleUpCooldown, scaleDownCooldown: $scaleDownCooldown, customPolicies: $customPolicies)';
}


}

/// @nodoc
abstract mixin class _$AutoScalingConfigCopyWith<$Res> implements $AutoScalingConfigCopyWith<$Res> {
  factory _$AutoScalingConfigCopyWith(_AutoScalingConfig value, $Res Function(_AutoScalingConfig) _then) = __$AutoScalingConfigCopyWithImpl;
@override @useResult
$Res call({
 bool enabled, int minInstances, int maxInstances, double cpuThresholdUp, double cpuThresholdDown, double memoryThresholdUp, double memoryThresholdDown, int scaleUpCooldown, int scaleDownCooldown, List<ScalingPolicy> customPolicies
});




}
/// @nodoc
class __$AutoScalingConfigCopyWithImpl<$Res>
    implements _$AutoScalingConfigCopyWith<$Res> {
  __$AutoScalingConfigCopyWithImpl(this._self, this._then);

  final _AutoScalingConfig _self;
  final $Res Function(_AutoScalingConfig) _then;

/// Create a copy of AutoScalingConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? enabled = null,Object? minInstances = null,Object? maxInstances = null,Object? cpuThresholdUp = null,Object? cpuThresholdDown = null,Object? memoryThresholdUp = null,Object? memoryThresholdDown = null,Object? scaleUpCooldown = null,Object? scaleDownCooldown = null,Object? customPolicies = null,}) {
  return _then(_AutoScalingConfig(
enabled: null == enabled ? _self.enabled : enabled // ignore: cast_nullable_to_non_nullable
as bool,minInstances: null == minInstances ? _self.minInstances : minInstances // ignore: cast_nullable_to_non_nullable
as int,maxInstances: null == maxInstances ? _self.maxInstances : maxInstances // ignore: cast_nullable_to_non_nullable
as int,cpuThresholdUp: null == cpuThresholdUp ? _self.cpuThresholdUp : cpuThresholdUp // ignore: cast_nullable_to_non_nullable
as double,cpuThresholdDown: null == cpuThresholdDown ? _self.cpuThresholdDown : cpuThresholdDown // ignore: cast_nullable_to_non_nullable
as double,memoryThresholdUp: null == memoryThresholdUp ? _self.memoryThresholdUp : memoryThresholdUp // ignore: cast_nullable_to_non_nullable
as double,memoryThresholdDown: null == memoryThresholdDown ? _self.memoryThresholdDown : memoryThresholdDown // ignore: cast_nullable_to_non_nullable
as double,scaleUpCooldown: null == scaleUpCooldown ? _self.scaleUpCooldown : scaleUpCooldown // ignore: cast_nullable_to_non_nullable
as int,scaleDownCooldown: null == scaleDownCooldown ? _self.scaleDownCooldown : scaleDownCooldown // ignore: cast_nullable_to_non_nullable
as int,customPolicies: null == customPolicies ? _self._customPolicies : customPolicies // ignore: cast_nullable_to_non_nullable
as List<ScalingPolicy>,
  ));
}


}


/// @nodoc
mixin _$ScalingPolicy {

 String get name; ScalingMetric get metric; ScalingComparison get comparison; double get threshold; ScalingAction get action; int get cooldownSeconds;
/// Create a copy of ScalingPolicy
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ScalingPolicyCopyWith<ScalingPolicy> get copyWith => _$ScalingPolicyCopyWithImpl<ScalingPolicy>(this as ScalingPolicy, _$identity);

  /// Serializes this ScalingPolicy to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ScalingPolicy&&(identical(other.name, name) || other.name == name)&&(identical(other.metric, metric) || other.metric == metric)&&(identical(other.comparison, comparison) || other.comparison == comparison)&&(identical(other.threshold, threshold) || other.threshold == threshold)&&(identical(other.action, action) || other.action == action)&&(identical(other.cooldownSeconds, cooldownSeconds) || other.cooldownSeconds == cooldownSeconds));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,metric,comparison,threshold,action,cooldownSeconds);

@override
String toString() {
  return 'ScalingPolicy(name: $name, metric: $metric, comparison: $comparison, threshold: $threshold, action: $action, cooldownSeconds: $cooldownSeconds)';
}


}

/// @nodoc
abstract mixin class $ScalingPolicyCopyWith<$Res>  {
  factory $ScalingPolicyCopyWith(ScalingPolicy value, $Res Function(ScalingPolicy) _then) = _$ScalingPolicyCopyWithImpl;
@useResult
$Res call({
 String name, ScalingMetric metric, ScalingComparison comparison, double threshold, ScalingAction action, int cooldownSeconds
});




}
/// @nodoc
class _$ScalingPolicyCopyWithImpl<$Res>
    implements $ScalingPolicyCopyWith<$Res> {
  _$ScalingPolicyCopyWithImpl(this._self, this._then);

  final ScalingPolicy _self;
  final $Res Function(ScalingPolicy) _then;

/// Create a copy of ScalingPolicy
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? metric = null,Object? comparison = null,Object? threshold = null,Object? action = null,Object? cooldownSeconds = null,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,metric: null == metric ? _self.metric : metric // ignore: cast_nullable_to_non_nullable
as ScalingMetric,comparison: null == comparison ? _self.comparison : comparison // ignore: cast_nullable_to_non_nullable
as ScalingComparison,threshold: null == threshold ? _self.threshold : threshold // ignore: cast_nullable_to_non_nullable
as double,action: null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as ScalingAction,cooldownSeconds: null == cooldownSeconds ? _self.cooldownSeconds : cooldownSeconds // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [ScalingPolicy].
extension ScalingPolicyPatterns on ScalingPolicy {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ScalingPolicy value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ScalingPolicy() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ScalingPolicy value)  $default,){
final _that = this;
switch (_that) {
case _ScalingPolicy():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ScalingPolicy value)?  $default,){
final _that = this;
switch (_that) {
case _ScalingPolicy() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  ScalingMetric metric,  ScalingComparison comparison,  double threshold,  ScalingAction action,  int cooldownSeconds)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ScalingPolicy() when $default != null:
return $default(_that.name,_that.metric,_that.comparison,_that.threshold,_that.action,_that.cooldownSeconds);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  ScalingMetric metric,  ScalingComparison comparison,  double threshold,  ScalingAction action,  int cooldownSeconds)  $default,) {final _that = this;
switch (_that) {
case _ScalingPolicy():
return $default(_that.name,_that.metric,_that.comparison,_that.threshold,_that.action,_that.cooldownSeconds);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  ScalingMetric metric,  ScalingComparison comparison,  double threshold,  ScalingAction action,  int cooldownSeconds)?  $default,) {final _that = this;
switch (_that) {
case _ScalingPolicy() when $default != null:
return $default(_that.name,_that.metric,_that.comparison,_that.threshold,_that.action,_that.cooldownSeconds);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ScalingPolicy implements ScalingPolicy {
  const _ScalingPolicy({required this.name, required this.metric, required this.comparison, required this.threshold, required this.action, required this.cooldownSeconds});
  factory _ScalingPolicy.fromJson(Map<String, dynamic> json) => _$ScalingPolicyFromJson(json);

@override final  String name;
@override final  ScalingMetric metric;
@override final  ScalingComparison comparison;
@override final  double threshold;
@override final  ScalingAction action;
@override final  int cooldownSeconds;

/// Create a copy of ScalingPolicy
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ScalingPolicyCopyWith<_ScalingPolicy> get copyWith => __$ScalingPolicyCopyWithImpl<_ScalingPolicy>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ScalingPolicyToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ScalingPolicy&&(identical(other.name, name) || other.name == name)&&(identical(other.metric, metric) || other.metric == metric)&&(identical(other.comparison, comparison) || other.comparison == comparison)&&(identical(other.threshold, threshold) || other.threshold == threshold)&&(identical(other.action, action) || other.action == action)&&(identical(other.cooldownSeconds, cooldownSeconds) || other.cooldownSeconds == cooldownSeconds));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,metric,comparison,threshold,action,cooldownSeconds);

@override
String toString() {
  return 'ScalingPolicy(name: $name, metric: $metric, comparison: $comparison, threshold: $threshold, action: $action, cooldownSeconds: $cooldownSeconds)';
}


}

/// @nodoc
abstract mixin class _$ScalingPolicyCopyWith<$Res> implements $ScalingPolicyCopyWith<$Res> {
  factory _$ScalingPolicyCopyWith(_ScalingPolicy value, $Res Function(_ScalingPolicy) _then) = __$ScalingPolicyCopyWithImpl;
@override @useResult
$Res call({
 String name, ScalingMetric metric, ScalingComparison comparison, double threshold, ScalingAction action, int cooldownSeconds
});




}
/// @nodoc
class __$ScalingPolicyCopyWithImpl<$Res>
    implements _$ScalingPolicyCopyWith<$Res> {
  __$ScalingPolicyCopyWithImpl(this._self, this._then);

  final _ScalingPolicy _self;
  final $Res Function(_ScalingPolicy) _then;

/// Create a copy of ScalingPolicy
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? metric = null,Object? comparison = null,Object? threshold = null,Object? action = null,Object? cooldownSeconds = null,}) {
  return _then(_ScalingPolicy(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,metric: null == metric ? _self.metric : metric // ignore: cast_nullable_to_non_nullable
as ScalingMetric,comparison: null == comparison ? _self.comparison : comparison // ignore: cast_nullable_to_non_nullable
as ScalingComparison,threshold: null == threshold ? _self.threshold : threshold // ignore: cast_nullable_to_non_nullable
as double,action: null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as ScalingAction,cooldownSeconds: null == cooldownSeconds ? _self.cooldownSeconds : cooldownSeconds // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$ConfigTestResult {

 bool get success; String? get error; Map<String, dynamic> get details; DateTime? get timestamp;
/// Create a copy of ConfigTestResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ConfigTestResultCopyWith<ConfigTestResult> get copyWith => _$ConfigTestResultCopyWithImpl<ConfigTestResult>(this as ConfigTestResult, _$identity);

  /// Serializes this ConfigTestResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ConfigTestResult&&(identical(other.success, success) || other.success == success)&&(identical(other.error, error) || other.error == error)&&const DeepCollectionEquality().equals(other.details, details)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,error,const DeepCollectionEquality().hash(details),timestamp);

@override
String toString() {
  return 'ConfigTestResult(success: $success, error: $error, details: $details, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class $ConfigTestResultCopyWith<$Res>  {
  factory $ConfigTestResultCopyWith(ConfigTestResult value, $Res Function(ConfigTestResult) _then) = _$ConfigTestResultCopyWithImpl;
@useResult
$Res call({
 bool success, String? error, Map<String, dynamic> details, DateTime? timestamp
});




}
/// @nodoc
class _$ConfigTestResultCopyWithImpl<$Res>
    implements $ConfigTestResultCopyWith<$Res> {
  _$ConfigTestResultCopyWithImpl(this._self, this._then);

  final ConfigTestResult _self;
  final $Res Function(ConfigTestResult) _then;

/// Create a copy of ConfigTestResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = null,Object? error = freezed,Object? details = null,Object? timestamp = freezed,}) {
  return _then(_self.copyWith(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,details: null == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,timestamp: freezed == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [ConfigTestResult].
extension ConfigTestResultPatterns on ConfigTestResult {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ConfigTestResult value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ConfigTestResult() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ConfigTestResult value)  $default,){
final _that = this;
switch (_that) {
case _ConfigTestResult():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ConfigTestResult value)?  $default,){
final _that = this;
switch (_that) {
case _ConfigTestResult() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool success,  String? error,  Map<String, dynamic> details,  DateTime? timestamp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ConfigTestResult() when $default != null:
return $default(_that.success,_that.error,_that.details,_that.timestamp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool success,  String? error,  Map<String, dynamic> details,  DateTime? timestamp)  $default,) {final _that = this;
switch (_that) {
case _ConfigTestResult():
return $default(_that.success,_that.error,_that.details,_that.timestamp);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool success,  String? error,  Map<String, dynamic> details,  DateTime? timestamp)?  $default,) {final _that = this;
switch (_that) {
case _ConfigTestResult() when $default != null:
return $default(_that.success,_that.error,_that.details,_that.timestamp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ConfigTestResult implements ConfigTestResult {
  const _ConfigTestResult({required this.success, this.error, final  Map<String, dynamic> details = const {}, this.timestamp}): _details = details;
  factory _ConfigTestResult.fromJson(Map<String, dynamic> json) => _$ConfigTestResultFromJson(json);

@override final  bool success;
@override final  String? error;
 final  Map<String, dynamic> _details;
@override@JsonKey() Map<String, dynamic> get details {
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_details);
}

@override final  DateTime? timestamp;

/// Create a copy of ConfigTestResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ConfigTestResultCopyWith<_ConfigTestResult> get copyWith => __$ConfigTestResultCopyWithImpl<_ConfigTestResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ConfigTestResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ConfigTestResult&&(identical(other.success, success) || other.success == success)&&(identical(other.error, error) || other.error == error)&&const DeepCollectionEquality().equals(other._details, _details)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,error,const DeepCollectionEquality().hash(_details),timestamp);

@override
String toString() {
  return 'ConfigTestResult(success: $success, error: $error, details: $details, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class _$ConfigTestResultCopyWith<$Res> implements $ConfigTestResultCopyWith<$Res> {
  factory _$ConfigTestResultCopyWith(_ConfigTestResult value, $Res Function(_ConfigTestResult) _then) = __$ConfigTestResultCopyWithImpl;
@override @useResult
$Res call({
 bool success, String? error, Map<String, dynamic> details, DateTime? timestamp
});




}
/// @nodoc
class __$ConfigTestResultCopyWithImpl<$Res>
    implements _$ConfigTestResultCopyWith<$Res> {
  __$ConfigTestResultCopyWithImpl(this._self, this._then);

  final _ConfigTestResult _self;
  final $Res Function(_ConfigTestResult) _then;

/// Create a copy of ConfigTestResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = null,Object? error = freezed,Object? details = null,Object? timestamp = freezed,}) {
  return _then(_ConfigTestResult(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,details: null == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,timestamp: freezed == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$DatabaseConfig {

 String get host; int get port; String get database; String get username; String get password; int get maxConnections; int get connectionTimeout; bool get sslEnabled; String get type; Map<String, dynamic> get additionalParams;
/// Create a copy of DatabaseConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DatabaseConfigCopyWith<DatabaseConfig> get copyWith => _$DatabaseConfigCopyWithImpl<DatabaseConfig>(this as DatabaseConfig, _$identity);

  /// Serializes this DatabaseConfig to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DatabaseConfig&&(identical(other.host, host) || other.host == host)&&(identical(other.port, port) || other.port == port)&&(identical(other.database, database) || other.database == database)&&(identical(other.username, username) || other.username == username)&&(identical(other.password, password) || other.password == password)&&(identical(other.maxConnections, maxConnections) || other.maxConnections == maxConnections)&&(identical(other.connectionTimeout, connectionTimeout) || other.connectionTimeout == connectionTimeout)&&(identical(other.sslEnabled, sslEnabled) || other.sslEnabled == sslEnabled)&&(identical(other.type, type) || other.type == type)&&const DeepCollectionEquality().equals(other.additionalParams, additionalParams));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,host,port,database,username,password,maxConnections,connectionTimeout,sslEnabled,type,const DeepCollectionEquality().hash(additionalParams));

@override
String toString() {
  return 'DatabaseConfig(host: $host, port: $port, database: $database, username: $username, password: $password, maxConnections: $maxConnections, connectionTimeout: $connectionTimeout, sslEnabled: $sslEnabled, type: $type, additionalParams: $additionalParams)';
}


}

/// @nodoc
abstract mixin class $DatabaseConfigCopyWith<$Res>  {
  factory $DatabaseConfigCopyWith(DatabaseConfig value, $Res Function(DatabaseConfig) _then) = _$DatabaseConfigCopyWithImpl;
@useResult
$Res call({
 String host, int port, String database, String username, String password, int maxConnections, int connectionTimeout, bool sslEnabled, String type, Map<String, dynamic> additionalParams
});




}
/// @nodoc
class _$DatabaseConfigCopyWithImpl<$Res>
    implements $DatabaseConfigCopyWith<$Res> {
  _$DatabaseConfigCopyWithImpl(this._self, this._then);

  final DatabaseConfig _self;
  final $Res Function(DatabaseConfig) _then;

/// Create a copy of DatabaseConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? host = null,Object? port = null,Object? database = null,Object? username = null,Object? password = null,Object? maxConnections = null,Object? connectionTimeout = null,Object? sslEnabled = null,Object? type = null,Object? additionalParams = null,}) {
  return _then(_self.copyWith(
host: null == host ? _self.host : host // ignore: cast_nullable_to_non_nullable
as String,port: null == port ? _self.port : port // ignore: cast_nullable_to_non_nullable
as int,database: null == database ? _self.database : database // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,maxConnections: null == maxConnections ? _self.maxConnections : maxConnections // ignore: cast_nullable_to_non_nullable
as int,connectionTimeout: null == connectionTimeout ? _self.connectionTimeout : connectionTimeout // ignore: cast_nullable_to_non_nullable
as int,sslEnabled: null == sslEnabled ? _self.sslEnabled : sslEnabled // ignore: cast_nullable_to_non_nullable
as bool,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,additionalParams: null == additionalParams ? _self.additionalParams : additionalParams // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [DatabaseConfig].
extension DatabaseConfigPatterns on DatabaseConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DatabaseConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DatabaseConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DatabaseConfig value)  $default,){
final _that = this;
switch (_that) {
case _DatabaseConfig():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DatabaseConfig value)?  $default,){
final _that = this;
switch (_that) {
case _DatabaseConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String host,  int port,  String database,  String username,  String password,  int maxConnections,  int connectionTimeout,  bool sslEnabled,  String type,  Map<String, dynamic> additionalParams)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DatabaseConfig() when $default != null:
return $default(_that.host,_that.port,_that.database,_that.username,_that.password,_that.maxConnections,_that.connectionTimeout,_that.sslEnabled,_that.type,_that.additionalParams);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String host,  int port,  String database,  String username,  String password,  int maxConnections,  int connectionTimeout,  bool sslEnabled,  String type,  Map<String, dynamic> additionalParams)  $default,) {final _that = this;
switch (_that) {
case _DatabaseConfig():
return $default(_that.host,_that.port,_that.database,_that.username,_that.password,_that.maxConnections,_that.connectionTimeout,_that.sslEnabled,_that.type,_that.additionalParams);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String host,  int port,  String database,  String username,  String password,  int maxConnections,  int connectionTimeout,  bool sslEnabled,  String type,  Map<String, dynamic> additionalParams)?  $default,) {final _that = this;
switch (_that) {
case _DatabaseConfig() when $default != null:
return $default(_that.host,_that.port,_that.database,_that.username,_that.password,_that.maxConnections,_that.connectionTimeout,_that.sslEnabled,_that.type,_that.additionalParams);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DatabaseConfig implements DatabaseConfig {
  const _DatabaseConfig({required this.host, required this.port, required this.database, required this.username, required this.password, required this.maxConnections, required this.connectionTimeout, required this.sslEnabled, this.type = 'postgresql', final  Map<String, dynamic> additionalParams = const {}}): _additionalParams = additionalParams;
  factory _DatabaseConfig.fromJson(Map<String, dynamic> json) => _$DatabaseConfigFromJson(json);

@override final  String host;
@override final  int port;
@override final  String database;
@override final  String username;
@override final  String password;
@override final  int maxConnections;
@override final  int connectionTimeout;
@override final  bool sslEnabled;
@override@JsonKey() final  String type;
 final  Map<String, dynamic> _additionalParams;
@override@JsonKey() Map<String, dynamic> get additionalParams {
  if (_additionalParams is EqualUnmodifiableMapView) return _additionalParams;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_additionalParams);
}


/// Create a copy of DatabaseConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DatabaseConfigCopyWith<_DatabaseConfig> get copyWith => __$DatabaseConfigCopyWithImpl<_DatabaseConfig>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DatabaseConfigToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DatabaseConfig&&(identical(other.host, host) || other.host == host)&&(identical(other.port, port) || other.port == port)&&(identical(other.database, database) || other.database == database)&&(identical(other.username, username) || other.username == username)&&(identical(other.password, password) || other.password == password)&&(identical(other.maxConnections, maxConnections) || other.maxConnections == maxConnections)&&(identical(other.connectionTimeout, connectionTimeout) || other.connectionTimeout == connectionTimeout)&&(identical(other.sslEnabled, sslEnabled) || other.sslEnabled == sslEnabled)&&(identical(other.type, type) || other.type == type)&&const DeepCollectionEquality().equals(other._additionalParams, _additionalParams));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,host,port,database,username,password,maxConnections,connectionTimeout,sslEnabled,type,const DeepCollectionEquality().hash(_additionalParams));

@override
String toString() {
  return 'DatabaseConfig(host: $host, port: $port, database: $database, username: $username, password: $password, maxConnections: $maxConnections, connectionTimeout: $connectionTimeout, sslEnabled: $sslEnabled, type: $type, additionalParams: $additionalParams)';
}


}

/// @nodoc
abstract mixin class _$DatabaseConfigCopyWith<$Res> implements $DatabaseConfigCopyWith<$Res> {
  factory _$DatabaseConfigCopyWith(_DatabaseConfig value, $Res Function(_DatabaseConfig) _then) = __$DatabaseConfigCopyWithImpl;
@override @useResult
$Res call({
 String host, int port, String database, String username, String password, int maxConnections, int connectionTimeout, bool sslEnabled, String type, Map<String, dynamic> additionalParams
});




}
/// @nodoc
class __$DatabaseConfigCopyWithImpl<$Res>
    implements _$DatabaseConfigCopyWith<$Res> {
  __$DatabaseConfigCopyWithImpl(this._self, this._then);

  final _DatabaseConfig _self;
  final $Res Function(_DatabaseConfig) _then;

/// Create a copy of DatabaseConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? host = null,Object? port = null,Object? database = null,Object? username = null,Object? password = null,Object? maxConnections = null,Object? connectionTimeout = null,Object? sslEnabled = null,Object? type = null,Object? additionalParams = null,}) {
  return _then(_DatabaseConfig(
host: null == host ? _self.host : host // ignore: cast_nullable_to_non_nullable
as String,port: null == port ? _self.port : port // ignore: cast_nullable_to_non_nullable
as int,database: null == database ? _self.database : database // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,maxConnections: null == maxConnections ? _self.maxConnections : maxConnections // ignore: cast_nullable_to_non_nullable
as int,connectionTimeout: null == connectionTimeout ? _self.connectionTimeout : connectionTimeout // ignore: cast_nullable_to_non_nullable
as int,sslEnabled: null == sslEnabled ? _self.sslEnabled : sslEnabled // ignore: cast_nullable_to_non_nullable
as bool,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,additionalParams: null == additionalParams ? _self._additionalParams : additionalParams // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}


/// @nodoc
mixin _$RedisConfig {

 String get host; int get port; String get password; int get database; int get maxConnections; int get connectionTimeout; bool get clusterMode; List<String> get clusterNodes;
/// Create a copy of RedisConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RedisConfigCopyWith<RedisConfig> get copyWith => _$RedisConfigCopyWithImpl<RedisConfig>(this as RedisConfig, _$identity);

  /// Serializes this RedisConfig to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RedisConfig&&(identical(other.host, host) || other.host == host)&&(identical(other.port, port) || other.port == port)&&(identical(other.password, password) || other.password == password)&&(identical(other.database, database) || other.database == database)&&(identical(other.maxConnections, maxConnections) || other.maxConnections == maxConnections)&&(identical(other.connectionTimeout, connectionTimeout) || other.connectionTimeout == connectionTimeout)&&(identical(other.clusterMode, clusterMode) || other.clusterMode == clusterMode)&&const DeepCollectionEquality().equals(other.clusterNodes, clusterNodes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,host,port,password,database,maxConnections,connectionTimeout,clusterMode,const DeepCollectionEquality().hash(clusterNodes));

@override
String toString() {
  return 'RedisConfig(host: $host, port: $port, password: $password, database: $database, maxConnections: $maxConnections, connectionTimeout: $connectionTimeout, clusterMode: $clusterMode, clusterNodes: $clusterNodes)';
}


}

/// @nodoc
abstract mixin class $RedisConfigCopyWith<$Res>  {
  factory $RedisConfigCopyWith(RedisConfig value, $Res Function(RedisConfig) _then) = _$RedisConfigCopyWithImpl;
@useResult
$Res call({
 String host, int port, String password, int database, int maxConnections, int connectionTimeout, bool clusterMode, List<String> clusterNodes
});




}
/// @nodoc
class _$RedisConfigCopyWithImpl<$Res>
    implements $RedisConfigCopyWith<$Res> {
  _$RedisConfigCopyWithImpl(this._self, this._then);

  final RedisConfig _self;
  final $Res Function(RedisConfig) _then;

/// Create a copy of RedisConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? host = null,Object? port = null,Object? password = null,Object? database = null,Object? maxConnections = null,Object? connectionTimeout = null,Object? clusterMode = null,Object? clusterNodes = null,}) {
  return _then(_self.copyWith(
host: null == host ? _self.host : host // ignore: cast_nullable_to_non_nullable
as String,port: null == port ? _self.port : port // ignore: cast_nullable_to_non_nullable
as int,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,database: null == database ? _self.database : database // ignore: cast_nullable_to_non_nullable
as int,maxConnections: null == maxConnections ? _self.maxConnections : maxConnections // ignore: cast_nullable_to_non_nullable
as int,connectionTimeout: null == connectionTimeout ? _self.connectionTimeout : connectionTimeout // ignore: cast_nullable_to_non_nullable
as int,clusterMode: null == clusterMode ? _self.clusterMode : clusterMode // ignore: cast_nullable_to_non_nullable
as bool,clusterNodes: null == clusterNodes ? _self.clusterNodes : clusterNodes // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [RedisConfig].
extension RedisConfigPatterns on RedisConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RedisConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RedisConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RedisConfig value)  $default,){
final _that = this;
switch (_that) {
case _RedisConfig():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RedisConfig value)?  $default,){
final _that = this;
switch (_that) {
case _RedisConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String host,  int port,  String password,  int database,  int maxConnections,  int connectionTimeout,  bool clusterMode,  List<String> clusterNodes)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RedisConfig() when $default != null:
return $default(_that.host,_that.port,_that.password,_that.database,_that.maxConnections,_that.connectionTimeout,_that.clusterMode,_that.clusterNodes);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String host,  int port,  String password,  int database,  int maxConnections,  int connectionTimeout,  bool clusterMode,  List<String> clusterNodes)  $default,) {final _that = this;
switch (_that) {
case _RedisConfig():
return $default(_that.host,_that.port,_that.password,_that.database,_that.maxConnections,_that.connectionTimeout,_that.clusterMode,_that.clusterNodes);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String host,  int port,  String password,  int database,  int maxConnections,  int connectionTimeout,  bool clusterMode,  List<String> clusterNodes)?  $default,) {final _that = this;
switch (_that) {
case _RedisConfig() when $default != null:
return $default(_that.host,_that.port,_that.password,_that.database,_that.maxConnections,_that.connectionTimeout,_that.clusterMode,_that.clusterNodes);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RedisConfig implements RedisConfig {
  const _RedisConfig({required this.host, required this.port, required this.password, required this.database, required this.maxConnections, required this.connectionTimeout, this.clusterMode = false, final  List<String> clusterNodes = const []}): _clusterNodes = clusterNodes;
  factory _RedisConfig.fromJson(Map<String, dynamic> json) => _$RedisConfigFromJson(json);

@override final  String host;
@override final  int port;
@override final  String password;
@override final  int database;
@override final  int maxConnections;
@override final  int connectionTimeout;
@override@JsonKey() final  bool clusterMode;
 final  List<String> _clusterNodes;
@override@JsonKey() List<String> get clusterNodes {
  if (_clusterNodes is EqualUnmodifiableListView) return _clusterNodes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_clusterNodes);
}


/// Create a copy of RedisConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RedisConfigCopyWith<_RedisConfig> get copyWith => __$RedisConfigCopyWithImpl<_RedisConfig>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RedisConfigToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RedisConfig&&(identical(other.host, host) || other.host == host)&&(identical(other.port, port) || other.port == port)&&(identical(other.password, password) || other.password == password)&&(identical(other.database, database) || other.database == database)&&(identical(other.maxConnections, maxConnections) || other.maxConnections == maxConnections)&&(identical(other.connectionTimeout, connectionTimeout) || other.connectionTimeout == connectionTimeout)&&(identical(other.clusterMode, clusterMode) || other.clusterMode == clusterMode)&&const DeepCollectionEquality().equals(other._clusterNodes, _clusterNodes));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,host,port,password,database,maxConnections,connectionTimeout,clusterMode,const DeepCollectionEquality().hash(_clusterNodes));

@override
String toString() {
  return 'RedisConfig(host: $host, port: $port, password: $password, database: $database, maxConnections: $maxConnections, connectionTimeout: $connectionTimeout, clusterMode: $clusterMode, clusterNodes: $clusterNodes)';
}


}

/// @nodoc
abstract mixin class _$RedisConfigCopyWith<$Res> implements $RedisConfigCopyWith<$Res> {
  factory _$RedisConfigCopyWith(_RedisConfig value, $Res Function(_RedisConfig) _then) = __$RedisConfigCopyWithImpl;
@override @useResult
$Res call({
 String host, int port, String password, int database, int maxConnections, int connectionTimeout, bool clusterMode, List<String> clusterNodes
});




}
/// @nodoc
class __$RedisConfigCopyWithImpl<$Res>
    implements _$RedisConfigCopyWith<$Res> {
  __$RedisConfigCopyWithImpl(this._self, this._then);

  final _RedisConfig _self;
  final $Res Function(_RedisConfig) _then;

/// Create a copy of RedisConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? host = null,Object? port = null,Object? password = null,Object? database = null,Object? maxConnections = null,Object? connectionTimeout = null,Object? clusterMode = null,Object? clusterNodes = null,}) {
  return _then(_RedisConfig(
host: null == host ? _self.host : host // ignore: cast_nullable_to_non_nullable
as String,port: null == port ? _self.port : port // ignore: cast_nullable_to_non_nullable
as int,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,database: null == database ? _self.database : database // ignore: cast_nullable_to_non_nullable
as int,maxConnections: null == maxConnections ? _self.maxConnections : maxConnections // ignore: cast_nullable_to_non_nullable
as int,connectionTimeout: null == connectionTimeout ? _self.connectionTimeout : connectionTimeout // ignore: cast_nullable_to_non_nullable
as int,clusterMode: null == clusterMode ? _self.clusterMode : clusterMode // ignore: cast_nullable_to_non_nullable
as bool,clusterNodes: null == clusterNodes ? _self._clusterNodes : clusterNodes // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}

// dart format on
