import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:shivish/apps/seller/domain/models/document_model.dart';
import 'package:shivish/apps/seller/domain/repositories/document_repository.dart';
import 'package:shivish/apps/seller/presentation/providers/document_providers.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/utils/date_utils.dart' as date_utils;
import 'package:shivish/shared/services/auth/auth_service.dart';

final documentUploadProvider =
    StateNotifierProvider<DocumentUploadNotifier, AsyncValue<void>>((ref) {
  final repository = ref.watch(documentRepositoryProvider);
  return DocumentUploadNotifier(repository);
});

class DocumentUploadNotifier extends StateNotifier<AsyncValue<void>> {
  final DocumentRepository _repository;

  DocumentUploadNotifier(this._repository) : super(const AsyncValue.data(null));

  Future<void> uploadDocument({
    required String sellerId,
    required File file,
    required DocumentType type,
    DateTime? expiryDate,
  }) async {
    state = const AsyncValue.loading();
    try {
      final fileName = file.path.split('/').last;

      await _repository.uploadDocument(
        type: type,
        filePath: file.path,
        fileName: fileName,
      );
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }


}

class DocumentUploadScreen extends ConsumerStatefulWidget {
  const DocumentUploadScreen({super.key});

  @override
  ConsumerState<DocumentUploadScreen> createState() =>
      _DocumentUploadScreenState();
}

class _DocumentUploadScreenState extends ConsumerState<DocumentUploadScreen> {
  File? _selectedFile;
  DocumentType? _selectedType;
  DateTime? _expiryDate;

  Future<void> _pickFile() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
    );

    if (result != null) {
      setState(() {
        _selectedFile = File(result.files.single.path!);
      });
    }
  }

  Future<void> _selectExpiryDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );

    if (date != null) {
      setState(() {
        _expiryDate = date;
      });
    }
  }

  Future<void> _uploadDocument() async {
    if (_selectedFile == null || _selectedType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a file and document type')),
      );
      return;
    }

    final authService = AuthService();
    final currentUser = await authService.getCurrentUser();
    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please sign in to upload documents')),
      );
      return;
    }

    await ref.read(documentUploadProvider.notifier).uploadDocument(
          sellerId: currentUser.id,
          file: _selectedFile!,
          type: _selectedType!,
          expiryDate: _expiryDate,
        );
  }

  @override
  Widget build(BuildContext context) {
    final uploadState = ref.watch(documentUploadProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Upload Document'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Document Type',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<DocumentType>(
                      value: _selectedType,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                      items: DocumentType.values.map((type) {
                        return DropdownMenuItem(
                          value: type,
                          child: Text(type.name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedType = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Expiry Date (Optional)',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: _selectExpiryDate,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _expiryDate != null
                              ? date_utils.formatDate(_expiryDate!)
                              : 'Select Date',
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'File',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: _pickFile,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.upload_file),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _selectedFile != null
                                    ? _selectedFile!.path.split('/').last
                                    : 'Select File',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            AppButton(
              onPressed: uploadState.isLoading ? null : _uploadDocument,
              child: uploadState.isLoading
                  ? const LoadingIndicator()
                  : const Text('Upload Document'),
            ),
            if (uploadState.hasError) ...[
              const SizedBox(height: 16),
              Text(
                'Error: ${uploadState.error}',
                style: const TextStyle(color: Colors.red),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
