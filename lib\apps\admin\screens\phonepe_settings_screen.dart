import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/models/payment/phonepe_config_model.dart';
import '../../../shared/services/payment/payment_service_provider.dart';

/// Screen for configuring PhonePe payment gateway settings
class PhonePeSettingsScreen extends ConsumerStatefulWidget {
  const PhonePeSettingsScreen({super.key});

  @override
  ConsumerState<PhonePeSettingsScreen> createState() => _PhonePeSettingsScreenState();
}

class _PhonePeSettingsScreenState extends ConsumerState<PhonePeSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isEnabled = false;
  bool _isProduction = false;

  final _merchantIdController = TextEditingController();
  final _apiKeyController = TextEditingController();
  final _saltKeyController = TextEditingController();
  final _apiEndpointController = TextEditingController();
  final _callbackUrlController = TextEditingController();
  final _redirectUrlController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadConfig();
  }

  @override
  void dispose() {
    _merchantIdController.dispose();
    _apiKeyController.dispose();
    _saltKeyController.dispose();
    _apiEndpointController.dispose();
    _callbackUrlController.dispose();
    _redirectUrlController.dispose();
    super.dispose();
  }

  /// Loads the PhonePe configuration
  Future<void> _loadConfig() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final configService = ref.read(phonePeConfigServiceProvider);
      final config = await configService.getPhonePeConfig();

      setState(() {
        _isEnabled = config.isEnabled;
        _isProduction = config.isProduction;
        _merchantIdController.text = config.merchantId;
        _apiKeyController.text = config.apiKey;
        _saltKeyController.text = config.saltKey;
        _apiEndpointController.text = config.apiEndpoint;
        _callbackUrlController.text = config.callbackUrl;
        _redirectUrlController.text = config.redirectUrl;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load PhonePe configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Saves the PhonePe configuration
  Future<void> _saveConfig() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final configService = ref.read(phonePeConfigServiceProvider);

      final config = PhonePeConfigModel(
        isEnabled: _isEnabled,
        isProduction: _isProduction,
        merchantId: _merchantIdController.text.trim(),
        apiKey: _apiKeyController.text.trim(),
        saltKey: _saltKeyController.text.trim(),
        apiEndpoint: _apiEndpointController.text.trim(),
        callbackUrl: _callbackUrlController.text.trim(),
        redirectUrl: _redirectUrlController.text.trim(),
      );

      await configService.updatePhonePeConfig(config);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('PhonePe configuration saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save PhonePe configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PhonePe Settings'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Enable PhonePe
                    SwitchListTile(
                      title: const Text('Enable PhonePe Payments'),
                      subtitle: const Text('Allow customers to pay using PhonePe'),
                      value: _isEnabled,
                      onChanged: (value) {
                        setState(() {
                          _isEnabled = value;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // Environment
                    SwitchListTile(
                      title: const Text('Production Environment'),
                      subtitle: const Text('Use production environment instead of sandbox'),
                      value: _isProduction,
                      onChanged: (value) {
                        setState(() {
                          _isProduction = value;

                          // Update API endpoint based on environment
                          if (value) {
                            _apiEndpointController.text = 'https://api.phonepe.com/apis/hermes';
                          } else {
                            _apiEndpointController.text = 'https://api-preprod.phonepe.com/apis/pg-sandbox';
                          }
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // Merchant ID
                    TextFormField(
                      controller: _merchantIdController,
                      decoration: InputDecoration(
                        labelText: 'Merchant ID',
                        hintText: 'Enter your PhonePe merchant ID',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      validator: (value) {
                        if (_isEnabled && (value == null || value.isEmpty)) {
                          return 'Merchant ID is required';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // API Key
                    TextFormField(
                      controller: _apiKeyController,
                      decoration: InputDecoration(
                        labelText: 'API Key',
                        hintText: 'Enter your PhonePe API key',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      validator: (value) {
                        if (_isEnabled && (value == null || value.isEmpty)) {
                          return 'API Key is required';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // Salt Key
                    TextFormField(
                      controller: _saltKeyController,
                      decoration: InputDecoration(
                        labelText: 'Salt Key',
                        hintText: 'Enter your PhonePe salt key',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      obscureText: true,
                      validator: (value) {
                        if (_isEnabled && (value == null || value.isEmpty)) {
                          return 'Salt Key is required';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // API Endpoint
                    TextFormField(
                      controller: _apiEndpointController,
                      decoration: InputDecoration(
                        labelText: 'API Endpoint',
                        hintText: 'Enter the PhonePe API endpoint',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      validator: (value) {
                        if (_isEnabled && (value == null || value.isEmpty)) {
                          return 'API Endpoint is required';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // Callback URL
                    TextFormField(
                      controller: _callbackUrlController,
                      decoration: InputDecoration(
                        labelText: 'Callback URL',
                        hintText: 'Enter the callback URL for PhonePe',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      validator: (value) {
                        if (_isEnabled && (value == null || value.isEmpty)) {
                          return 'Callback URL is required';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // Redirect URL
                    TextFormField(
                      controller: _redirectUrlController,
                      decoration: InputDecoration(
                        labelText: 'Redirect URL',
                        hintText: 'Enter the redirect URL for PhonePe',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      validator: (value) {
                        if (_isEnabled && (value == null || value.isEmpty)) {
                          return 'Redirect URL is required';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 24),

                    // Save button
                    Center(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveConfig,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : const Text('Save Settings'),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // View transactions button
                    Center(
                      child: OutlinedButton.icon(
                        onPressed: () => context.go('/admin/payment-gateway/phonepe/transactions'),
                        icon: const Icon(Icons.history),
                        label: const Text('View Transaction History'),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
