import 'package:shivish/apps/seller/domain/repositories/order_repository.dart';
import 'package:shivish/shared/models/order/order_model.dart';
import 'package:shivish/shared/utils/logger.dart';

class GetOrdersUseCase {
  final OrderRepository _repository;
  final _logger = getLogger('GetOrdersUseCase');

  GetOrdersUseCase(this._repository);

  /// Gets all orders for a seller
  ///
  /// Throws [ArgumentError] if sellerId is empty
  /// Throws [Exception] if the operation fails
  Future<List<OrderModel>> call(String sellerId) async {
    try {
      if (sellerId.trim().isEmpty) {
        throw ArgumentError('Seller ID cannot be empty');
      }

      _logger.info('Fetching orders for seller: $sellerId');
      final orders = await _repository.getOrders(sellerId);
      _logger.info('Successfully fetched ${orders.length} orders for seller $sellerId');

      return orders;
    } catch (e) {
      _logger.severe('Failed to get orders for seller $sellerId: $e');
      rethrow;
    }
  }
}

class GetOrderByIdUseCase {
  final OrderRepository _repository;
  final _logger = getLogger('GetOrderByIdUseCase');

  GetOrderByIdUseCase(this._repository);

  /// Gets a specific order by ID
  ///
  /// Returns null if order is not found
  /// Throws [ArgumentError] if orderId is empty
  /// Throws [Exception] if the operation fails
  Future<OrderModel?> call(String orderId) async {
    try {
      if (orderId.trim().isEmpty) {
        throw ArgumentError('Order ID cannot be empty');
      }

      _logger.info('Fetching order: $orderId');
      final order = await _repository.getOrderById(orderId);

      if (order != null) {
        _logger.info('Successfully fetched order $orderId');
      } else {
        _logger.warning('Order not found: $orderId');
      }

      return order;
    } catch (e) {
      _logger.severe('Failed to get order $orderId: $e');
      rethrow;
    }
  }
}

class UpdateOrderStatusUseCase {
  final OrderRepository _repository;
  final _logger = getLogger('UpdateOrderStatusUseCase');

  UpdateOrderStatusUseCase(this._repository);

  /// Updates the status of an order
  ///
  /// Throws [ArgumentError] if orderId is empty
  /// Throws [Exception] if the operation fails
  Future<OrderModel> call(String orderId, OrderStatus status) async {
    try {
      if (orderId.trim().isEmpty) {
        throw ArgumentError('Order ID cannot be empty');
      }

      _logger.info('Updating order $orderId status to: ${status.name}');
      final updatedOrder = await _repository.updateOrderStatus(orderId, status);
      _logger.info('Successfully updated order $orderId status to ${status.name}');

      return updatedOrder;
    } catch (e) {
      _logger.severe('Failed to update order $orderId status to ${status.name}: $e');
      rethrow;
    }
  }
}

class UpdatePaymentStatusUseCase {
  final OrderRepository _repository;
  final _logger = getLogger('UpdatePaymentStatusUseCase');

  UpdatePaymentStatusUseCase(this._repository);

  /// Updates the payment status of an order
  ///
  /// Throws [ArgumentError] if orderId is empty
  /// Throws [Exception] if the operation fails
  Future<OrderModel> call(String orderId, PaymentStatus status) async {
    try {
      if (orderId.trim().isEmpty) {
        throw ArgumentError('Order ID cannot be empty');
      }

      _logger.info('Updating order $orderId payment status to: ${status.name}');
      final updatedOrder = await _repository.updatePaymentStatus(orderId, status);
      _logger.info('Successfully updated order $orderId payment status to ${status.name}');

      return updatedOrder;
    } catch (e) {
      _logger.severe('Failed to update order $orderId payment status to ${status.name}: $e');
      rethrow;
    }
  }
}

class UpdateShippingDetailsUseCase {
  final OrderRepository _repository;
  final _logger = getLogger('UpdateShippingDetailsUseCase');

  UpdateShippingDetailsUseCase(this._repository);

  /// Updates shipping details for an order
  ///
  /// Throws [ArgumentError] if orderId, trackingNumber, or shippingProvider is empty
  /// Throws [Exception] if the operation fails
  Future<OrderModel> call(
    String orderId, {
    required String trackingNumber,
    required String shippingProvider,
    DateTime? estimatedDeliveryDate,
  }) async {
    try {
      if (orderId.trim().isEmpty) {
        throw ArgumentError('Order ID cannot be empty');
      }
      if (trackingNumber.trim().isEmpty) {
        throw ArgumentError('Tracking number cannot be empty');
      }
      if (shippingProvider.trim().isEmpty) {
        throw ArgumentError('Shipping provider cannot be empty');
      }

      _logger.info('Updating shipping details for order $orderId');
      final updatedOrder = await _repository.addTrackingInfo(
        orderId,
        trackingNumber: trackingNumber.trim(),
        shippingProvider: shippingProvider.trim(),
        estimatedDeliveryDate: estimatedDeliveryDate,
      );
      _logger.info('Successfully updated shipping details for order $orderId');

      return updatedOrder;
    } catch (e) {
      _logger.severe('Failed to update shipping details for order $orderId: $e');
      rethrow;
    }
  }
}

class CancelOrderUseCase {
  final OrderRepository _repository;
  final _logger = getLogger('CancelOrderUseCase');

  CancelOrderUseCase(this._repository);

  /// Cancels an order with optional reason
  ///
  /// Throws [ArgumentError] if orderId is empty
  /// Throws [Exception] if the operation fails
  Future<OrderModel> call(String orderId, {String? reason}) async {
    try {
      if (orderId.trim().isEmpty) {
        throw ArgumentError('Order ID cannot be empty');
      }

      _logger.info('Cancelling order $orderId${reason != null ? ' with reason: $reason' : ''}');
      final cancelledOrder = await _repository.cancelOrder(
        orderId,
        reason: reason?.trim(),
      );
      _logger.info('Successfully cancelled order $orderId');

      return cancelledOrder;
    } catch (e) {
      _logger.severe('Failed to cancel order $orderId: $e');
      rethrow;
    }
  }
}

class ProcessRefundUseCase {
  final OrderRepository _repository;
  final _logger = getLogger('ProcessRefundUseCase');

  ProcessRefundUseCase(this._repository);

  /// Processes a refund for an order with optional reason
  ///
  /// Throws [ArgumentError] if orderId is empty
  /// Throws [Exception] if the operation fails
  Future<OrderModel> call(String orderId, {String? reason}) async {
    try {
      if (orderId.trim().isEmpty) {
        throw ArgumentError('Order ID cannot be empty');
      }

      _logger.info('Processing refund for order $orderId${reason != null ? ' with reason: $reason' : ''}');
      final refundedOrder = await _repository.refundOrder(
        orderId,
        reason: reason?.trim(),
      );
      _logger.info('Successfully processed refund for order $orderId');

      return refundedOrder;
    } catch (e) {
      _logger.severe('Failed to process refund for order $orderId: $e');
      rethrow;
    }
  }
}

class AddOrderNoteUseCase {
  final OrderRepository _repository;
  final _logger = getLogger('AddOrderNoteUseCase');

  AddOrderNoteUseCase(this._repository);

  /// Adds a note to an order
  ///
  /// Throws [Exception] if the operation fails
  Future<OrderModel> call({
    required String orderId,
    required String note,
  }) async {
    try {
      if (note.trim().isEmpty) {
        throw ArgumentError('Note cannot be empty');
      }

      _logger.info('Adding note to order $orderId: $note');

      final updatedOrder = await _repository.addOrderNote(orderId, note.trim());

      _logger.info('Successfully added note to order $orderId');
      return updatedOrder;
    } catch (e) {
      _logger.severe('Failed to add note to order $orderId: $e');
      rethrow;
    }
  }
}

class WatchOrdersUseCase {
  final OrderRepository _repository;
  final _logger = getLogger('WatchOrdersUseCase');

  WatchOrdersUseCase(this._repository);

  /// Watches orders for a seller with optional status filtering
  ///
  /// Returns a stream of order lists that updates periodically
  /// In case of errors, yields empty list and continues polling
  Stream<List<OrderModel>> call({
    required String sellerId,
    OrderStatus? status,
    Duration pollInterval = const Duration(seconds: 30),
  }) async* {
    if (sellerId.trim().isEmpty) {
      _logger.warning('Empty sellerId provided to WatchOrdersUseCase');
      yield [];
      return;
    }

    _logger.info('Starting to watch orders for seller: $sellerId');

    while (true) {
      try {
        final orders = await _repository.getOrders(sellerId);
        final filteredOrders = status != null
            ? orders.where((order) => order.status == status).toList()
            : orders;

        _logger.fine('Fetched ${filteredOrders.length} orders for seller $sellerId');
        yield filteredOrders;

        await Future.delayed(pollInterval);
      } catch (e) {
        _logger.warning('Error fetching orders for seller $sellerId: $e');
        yield [];
        await Future.delayed(pollInterval);
      }
    }
  }
}
