import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../apps/admin/admin_routes.dart';
import '../../../../apps/admin/bloc/auth/auth_bloc.dart';

class AdminDrawer extends StatelessWidget {
  const AdminDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        // Get user info from the AuthBloc state
        String displayName = 'Admin';
        String email = '';
        String? photoUrl;

        if (state is AuthAuthenticatedState) {
          displayName = state.user.userMetadata?['display_name'] as String? ??
                       state.user.userMetadata?['full_name'] as String? ??
                       state.user.email?.split('@').first ?? 'Admin';
          email = state.user.email ?? '';
          photoUrl = state.user.userMetadata?['avatar_url'] as String?;
        }

        return Drawer(
          child: Column(
            children: [
              UserAccountsDrawerHeader(
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                ),
                currentAccountPicture: CircleAvatar(
                  backgroundImage: photoUrl != null ? NetworkImage(photoUrl) : null,
                  child: photoUrl == null
                      ? Text(
                          displayName.isNotEmpty ? displayName[0].toUpperCase() : 'A',
                          style: theme.textTheme.headlineMedium?.copyWith(
                            color: theme.colorScheme.onPrimary,
                          ),
                        )
                      : null,
                ),
                accountName: Text(
                  displayName,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                accountEmail: Text(
                  email,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onPrimary.withAlpha(204),
                  ),
                ),
              ),
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    _DrawerItem(
                      icon: Icons.home_outlined,
                      label: 'Home',
                      onTap: () => _navigateTo(context, AdminRoutes.home),
                    ),
                    _DrawerItem(
                      icon: Icons.people_outline_rounded,
                      label: 'Media',
                      onTap: () => _navigateTo(context, AdminRoutes.media),
                    ),
                    _DrawerItem(
                      icon: Icons.admin_panel_settings_outlined,
                      label: 'Executors',
                      onTap: () => _navigateTo(context, AdminRoutes.executors),
                    ),
                    _DrawerItem(
                      icon: Icons.store_outlined,
                      label: 'Sellers',
                      onTap: () => _navigateTo(context, AdminRoutes.sellers),
                    ),
                    _DrawerItem(
                      icon: Icons.temple_hindu_outlined,
                      label: 'Priests',
                      onTap: () => _navigateTo(context, AdminRoutes.priests),
                    ),
                    _DrawerItem(
                      icon: Icons.handyman_outlined,
                      label: 'Technicians',
                      onTap: () =>
                          _navigateTo(context, AdminRoutes.technicians),
                    ),
                    const Divider(),
                    _DrawerItem(
                      icon: Icons.analytics_outlined,
                      label: 'Analytics',
                      onTap: () => _navigateTo(context, AdminRoutes.analytics),
                    ),
                    _DrawerItem(
                      icon: Icons.settings_outlined,
                      label: 'Settings',
                      onTap: () => _navigateTo(context, AdminRoutes.settings),
                    ),
                    _DrawerItem(
                      icon: Icons.payment_outlined,
                      label: 'Commission',
                      onTap: () => _navigateTo(context, AdminRoutes.commission),
                    ),
                    _DrawerItem(
                      icon: Icons.api_outlined,
                      label: 'API Keys',
                      onTap: () => _navigateTo(context, AdminRoutes.apiKeys),
                    ),
                    const Divider(),
                    _DrawerItem(
                      icon: Icons.person_outline_rounded,
                      label: 'Profile',
                      onTap: () => _navigateTo(context, AdminRoutes.profile),
                    ),
                    const Divider(),
                    _DrawerItem(
                      icon: Icons.logout_rounded,
                      label: 'Sign Out',
                      onTap: () {
                        context.read<AuthBloc>().add(SignOutEvent());
                        context.go(AdminRoutes.login);
                      },
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Version 1.0.0',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(153),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _navigateTo(BuildContext context, String route) {
    context.pop();
    context.go(route);
  }
}

class _DrawerItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _DrawerItem({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: theme.colorScheme.onSurface.withAlpha(204), // 0.8 * 255 = ~204
      ),
      title: Text(
        label,
        style: theme.textTheme.bodyLarge?.copyWith(
          color: theme.colorScheme.onSurface,
        ),
      ),
      onTap: onTap,
      dense: true,
      horizontalTitleGap: 0,
    );
  }
}
