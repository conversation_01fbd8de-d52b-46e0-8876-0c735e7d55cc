import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';

final passwordProvider = AsyncNotifierProvider<PasswordNotifier, void>(() {
  return PasswordNotifier();
});

class PasswordNotifier extends AsyncNotifier<void> {
  late final AuthService _authService;

  @override
  Future<void> build() async {
    _authService = AuthService();
  }

  Future<void> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    state = const AsyncLoading();
    try {
      // First reauthenticate with current password
      await _authService.reauthenticate(currentPassword);
      // Then update password
      await _authService.updatePassword(newPassword);
      state = const AsyncData(null);
    } catch (e, st) {
      state = AsyncError(e, st);
      rethrow;
    }
  }
}
