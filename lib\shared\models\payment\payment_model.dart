import 'package:freezed_annotation/freezed_annotation.dart';

import 'card_model.dart';

part 'payment_model.freezed.dart';
part 'payment_model.g.dart';

@JsonEnum(fieldRename: FieldRename.snake)
enum PaymentType {
  purchase,
  refund,
  subscription,
  donation,
}

@JsonEnum(fieldRename: FieldRename.snake)
enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  refunded,
  cancelled;

  String toJson() => name;
}

@JsonEnum(fieldRename: FieldRename.snake)
enum PaymentMethod {
  card,
  cash,
  upi,
  wallet,
}

@JsonEnum(fieldRename: FieldRename.snake)
enum PaymentGateway {
  phonepe,
  upi,
  cash,
}

@freezed
abstract class PaymentModel with _$PaymentModel {
  const factory PaymentModel({
    required String id,
    required String paymentNumber,
    required String orderId,
    required String customerId,
    required String merchantId,
    required PaymentType type,
    required PaymentStatus status,
    required PaymentMethod method,
    required PaymentGateway gateway,
    required double amount,
    required double taxAmount,
    required double feeAmount,
    required double totalAmount,
    CardModel? card,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isDeleted,
  }) = _PaymentModel;

  factory PaymentModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentModelFromJson(json);

  factory PaymentModel.empty() => PaymentModel(
        id: '',
        paymentNumber: '',
        orderId: '',
        customerId: '',
        merchantId: '',
        type: PaymentType.purchase,
        status: PaymentStatus.pending,
        method: PaymentMethod.cash,
        gateway: PaymentGateway.cash,
        amount: 0,
        taxAmount: 0,
        feeAmount: 0,
        totalAmount: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
}
