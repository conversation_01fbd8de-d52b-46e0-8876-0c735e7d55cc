import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/bookmark_provider.dart';
import '../../../../shared/providers/technician_provider.dart';
import '../../../../shared/utils/string_extensions.dart';
import 'technician_booking_form_screen.dart';

class TechnicianDetailsScreen extends ConsumerWidget {
  final String technicianId;

  const TechnicianDetailsScreen({
    super.key,
    required this.technicianId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final technicianAsync = ref.watch(technicianProvider(technicianId));
    final authState = ref.watch(authProvider);
    final userId = authState?.id;
    final bookmarksAsync =
        userId != null ? ref.watch(bookmarkProvider(userId)) : null;

    return Scaffold(
      body: technicianAsync.when(
        data: (technician) {
          if (technician == null) {
            return const Center(child: Text('Technician not found'));
          }

          return CustomScrollView(
            slivers: [
              SliverAppBar(
                expandedHeight: 200,
                pinned: true,
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    color: Colors.grey[300],
                    child: const Icon(Icons.build, size: 64),
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.share),
                    onPressed: () {
                      share_plus.SharePlus.instance.share(
                        share_plus.ShareParams(
                          text: 'Check out ${technician.name} on Shivish!\n\n'
                                'Specialization: ${technician.specializations.isNotEmpty ? technician.specializations.join(", ") : "Technician"}\n'
                                'Rating: ${technician.rating.toStringAsFixed(1)} ⭐\n'
                                'Experience: ${technician.experienceYears} years\n\n'
                                'Service Areas: ${technician.serviceAreas.join(", ")}\n'
                                'Certifications: ${technician.certifications.join(", ")}\n\n'
                                'Book now: https://shivish.com/technician/${technician.id}',
                        ),
                      );
                    },
                  ),
                  if (userId != null)
                    IconButton(
                      icon: bookmarksAsync?.when(
                            data: (bookmarks) => Icon(
                              bookmarks.contains(technicianId)
                                  ? Icons.bookmark
                                  : Icons.bookmark_border,
                            ),
                            loading: () => const Icon(Icons.bookmark_border),
                            error: (_, __) => const Icon(Icons.bookmark_border),
                          ) ??
                          const Icon(Icons.bookmark_border),
                      onPressed: () {
                        ref
                            .read(bookmarkProvider(userId).notifier)
                            .toggleBookmark(technicianId);
                      },
                    ),
                ],
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 40,
                            backgroundColor: Colors.grey[300],
                            backgroundImage: technician.profileImage != null
                                ? NetworkImage(technician.profileImage!)
                                : null,
                            onBackgroundImageError: (exception, stackTrace) {
                              // Handle error
                            },
                            child: technician.profileImage != null
                                ? null
                                : const Icon(Icons.person, size: 40, color: Colors.grey),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  technician.name,
                                  style:
                                      Theme.of(context).textTheme.headlineSmall,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  technician.specializations.isNotEmpty
                                      ? technician.specializations.first.toString().capitalize()
                                      : 'Technician',
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    const Icon(Icons.star, color: Colors.amber),
                                    const SizedBox(width: 4),
                                    Text(
                                      technician.rating.toStringAsFixed(1),
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      '(${technician.totalReviews} reviews)',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'About',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Professional technician with ${technician.experienceYears} years of experience',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Specializations',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: technician.specializations.map((specialization) {
                          return Chip(
                            label: Text(specialization.toString().capitalize()),
                            backgroundColor:
                                Theme.of(context).colorScheme.primaryContainer,
                            labelStyle: TextStyle(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onPrimaryContainer,
                            ),
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Service Areas',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: technician.serviceAreas.map((area) {
                          return Chip(
                            label: Text(area),
                            backgroundColor: Theme.of(context)
                                .colorScheme
                                .secondaryContainer,
                            labelStyle: TextStyle(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSecondaryContainer,
                            ),
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Certifications',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: technician.certifications.length,
                        itemBuilder: (context, index) {
                          final certification =
                              technician.certifications[index];
                          return ListTile(
                            leading: const Icon(Icons.verified, color: Colors.green),
                            title: Text(certification.toString().capitalize()),
                          );
                        },
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Experience',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${technician.experienceYears} years',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Location',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        technician.serviceAreas.isNotEmpty
                            ? technician.serviceAreas.join(', ')
                            : 'Location not available',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Contact',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      ListTile(
                        leading: const Icon(Icons.phone),
                        title: Text(technician.phone),
                      ),
                      ListTile(
                        leading: const Icon(Icons.email),
                        title: Text(technician.email),
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) =>
                                    TechnicianBookingFormScreen(
                                  technicianId: technician.id,
                                ),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: const Text('Book Now'),
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }
}
