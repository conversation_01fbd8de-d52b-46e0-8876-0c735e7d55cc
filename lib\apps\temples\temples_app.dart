import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:provider/provider.dart' as provider;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shivish/shared/core/localization/l10n/app_localizations.dart';
import '../../shared/core/theme/theme_provider.dart';
import '../../shared/core/service_locator.dart';
import 'services/notification_popup_service.dart';
import 'temples_routes.dart';
import 'theme/temples_theme.dart';

class TemplesApp extends ConsumerWidget {
  const TemplesApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return provider.ChangeNotifierProvider(
      create: (context) => ThemeProvider(serviceLocator<SharedPreferences>()),
      child: provider.Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp.router(
            title: 'Shivish Temples',
            theme: TemplesTheme.lightTheme,
            darkTheme: TemplesTheme.darkTheme,
            themeMode: themeProvider.themeMode,
            routerConfig: TemplesRoutes.router,
            debugShowCheckedModeBanner: false,
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            builder: (context, child) {
              // Initialize notification popup service
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (context.mounted) {
                  TempleNotificationPopupService.instance.initialize(
                    context,
                    ref,
                  );
                }
              });

              // Apply system UI overlay style
              SystemChrome.setSystemUIOverlayStyle(
                SystemUiOverlayStyle(
                  statusBarColor: Theme.of(context).primaryColor,
                  statusBarIconBrightness: Brightness.light,
                ),
              );
              return child ?? const SizedBox();
            },
          );
        },
      ),
    );
  }
}

/// Temple app entry point
class TemplesAppEntry extends StatelessWidget {
  const TemplesAppEntry({super.key});

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: MaterialApp(
        title: 'Shivish Temples',
        theme: TemplesTheme.lightTheme,
        home: const TemplesApp(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

/// Initialize temples app services
Future<void> initializeTemplesApp() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize service locator
  await setupServiceLocator();

  // NotificationService is initialized through service locator

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
}

/// Main function for temples app
void main() async {
  await initializeTemplesApp();
  runApp(const TemplesAppEntry());
}
