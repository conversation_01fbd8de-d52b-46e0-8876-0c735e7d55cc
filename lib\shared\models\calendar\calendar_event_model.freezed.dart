// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'calendar_event_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CalendarEventModel {

 String get id; String get title; String get description; DateTime get startDate; DateTime get endDate; CalendarEventType get type; CalendarEventVisibility get visibility; CalendarEventStatus get status; bool get isRecurring; CalendarEventRecurrence? get recurrenceRule; bool get hasReminder; CalendarEventReminder? get reminder; DateTime get createdAt; DateTime get updatedAt; String? get userId;// User ID of the event creator
 List<EventProduct>? get products;// Contact information for greeting messages
 String? get contactName; String? get contactPhone; String? get relationship; bool? get sendGreeting; String? get customGreetingMessage;
/// Create a copy of CalendarEventModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CalendarEventModelCopyWith<CalendarEventModel> get copyWith => _$CalendarEventModelCopyWithImpl<CalendarEventModel>(this as CalendarEventModel, _$identity);

  /// Serializes this CalendarEventModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CalendarEventModel&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.type, type) || other.type == type)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&(identical(other.status, status) || other.status == status)&&(identical(other.isRecurring, isRecurring) || other.isRecurring == isRecurring)&&(identical(other.recurrenceRule, recurrenceRule) || other.recurrenceRule == recurrenceRule)&&(identical(other.hasReminder, hasReminder) || other.hasReminder == hasReminder)&&(identical(other.reminder, reminder) || other.reminder == reminder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.userId, userId) || other.userId == userId)&&const DeepCollectionEquality().equals(other.products, products)&&(identical(other.contactName, contactName) || other.contactName == contactName)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.relationship, relationship) || other.relationship == relationship)&&(identical(other.sendGreeting, sendGreeting) || other.sendGreeting == sendGreeting)&&(identical(other.customGreetingMessage, customGreetingMessage) || other.customGreetingMessage == customGreetingMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,title,description,startDate,endDate,type,visibility,status,isRecurring,recurrenceRule,hasReminder,reminder,createdAt,updatedAt,userId,const DeepCollectionEquality().hash(products),contactName,contactPhone,relationship,sendGreeting,customGreetingMessage]);

@override
String toString() {
  return 'CalendarEventModel(id: $id, title: $title, description: $description, startDate: $startDate, endDate: $endDate, type: $type, visibility: $visibility, status: $status, isRecurring: $isRecurring, recurrenceRule: $recurrenceRule, hasReminder: $hasReminder, reminder: $reminder, createdAt: $createdAt, updatedAt: $updatedAt, userId: $userId, products: $products, contactName: $contactName, contactPhone: $contactPhone, relationship: $relationship, sendGreeting: $sendGreeting, customGreetingMessage: $customGreetingMessage)';
}


}

/// @nodoc
abstract mixin class $CalendarEventModelCopyWith<$Res>  {
  factory $CalendarEventModelCopyWith(CalendarEventModel value, $Res Function(CalendarEventModel) _then) = _$CalendarEventModelCopyWithImpl;
@useResult
$Res call({
 String id, String title, String description, DateTime startDate, DateTime endDate, CalendarEventType type, CalendarEventVisibility visibility, CalendarEventStatus status, bool isRecurring, CalendarEventRecurrence? recurrenceRule, bool hasReminder, CalendarEventReminder? reminder, DateTime createdAt, DateTime updatedAt, String? userId, List<EventProduct>? products, String? contactName, String? contactPhone, String? relationship, bool? sendGreeting, String? customGreetingMessage
});


$CalendarEventReminderCopyWith<$Res>? get reminder;

}
/// @nodoc
class _$CalendarEventModelCopyWithImpl<$Res>
    implements $CalendarEventModelCopyWith<$Res> {
  _$CalendarEventModelCopyWithImpl(this._self, this._then);

  final CalendarEventModel _self;
  final $Res Function(CalendarEventModel) _then;

/// Create a copy of CalendarEventModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? title = null,Object? description = null,Object? startDate = null,Object? endDate = null,Object? type = null,Object? visibility = null,Object? status = null,Object? isRecurring = null,Object? recurrenceRule = freezed,Object? hasReminder = null,Object? reminder = freezed,Object? createdAt = null,Object? updatedAt = null,Object? userId = freezed,Object? products = freezed,Object? contactName = freezed,Object? contactPhone = freezed,Object? relationship = freezed,Object? sendGreeting = freezed,Object? customGreetingMessage = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as CalendarEventType,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as CalendarEventVisibility,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as CalendarEventStatus,isRecurring: null == isRecurring ? _self.isRecurring : isRecurring // ignore: cast_nullable_to_non_nullable
as bool,recurrenceRule: freezed == recurrenceRule ? _self.recurrenceRule : recurrenceRule // ignore: cast_nullable_to_non_nullable
as CalendarEventRecurrence?,hasReminder: null == hasReminder ? _self.hasReminder : hasReminder // ignore: cast_nullable_to_non_nullable
as bool,reminder: freezed == reminder ? _self.reminder : reminder // ignore: cast_nullable_to_non_nullable
as CalendarEventReminder?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,products: freezed == products ? _self.products : products // ignore: cast_nullable_to_non_nullable
as List<EventProduct>?,contactName: freezed == contactName ? _self.contactName : contactName // ignore: cast_nullable_to_non_nullable
as String?,contactPhone: freezed == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String?,relationship: freezed == relationship ? _self.relationship : relationship // ignore: cast_nullable_to_non_nullable
as String?,sendGreeting: freezed == sendGreeting ? _self.sendGreeting : sendGreeting // ignore: cast_nullable_to_non_nullable
as bool?,customGreetingMessage: freezed == customGreetingMessage ? _self.customGreetingMessage : customGreetingMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of CalendarEventModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CalendarEventReminderCopyWith<$Res>? get reminder {
    if (_self.reminder == null) {
    return null;
  }

  return $CalendarEventReminderCopyWith<$Res>(_self.reminder!, (value) {
    return _then(_self.copyWith(reminder: value));
  });
}
}


/// Adds pattern-matching-related methods to [CalendarEventModel].
extension CalendarEventModelPatterns on CalendarEventModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CalendarEventModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CalendarEventModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CalendarEventModel value)  $default,){
final _that = this;
switch (_that) {
case _CalendarEventModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CalendarEventModel value)?  $default,){
final _that = this;
switch (_that) {
case _CalendarEventModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String title,  String description,  DateTime startDate,  DateTime endDate,  CalendarEventType type,  CalendarEventVisibility visibility,  CalendarEventStatus status,  bool isRecurring,  CalendarEventRecurrence? recurrenceRule,  bool hasReminder,  CalendarEventReminder? reminder,  DateTime createdAt,  DateTime updatedAt,  String? userId,  List<EventProduct>? products,  String? contactName,  String? contactPhone,  String? relationship,  bool? sendGreeting,  String? customGreetingMessage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CalendarEventModel() when $default != null:
return $default(_that.id,_that.title,_that.description,_that.startDate,_that.endDate,_that.type,_that.visibility,_that.status,_that.isRecurring,_that.recurrenceRule,_that.hasReminder,_that.reminder,_that.createdAt,_that.updatedAt,_that.userId,_that.products,_that.contactName,_that.contactPhone,_that.relationship,_that.sendGreeting,_that.customGreetingMessage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String title,  String description,  DateTime startDate,  DateTime endDate,  CalendarEventType type,  CalendarEventVisibility visibility,  CalendarEventStatus status,  bool isRecurring,  CalendarEventRecurrence? recurrenceRule,  bool hasReminder,  CalendarEventReminder? reminder,  DateTime createdAt,  DateTime updatedAt,  String? userId,  List<EventProduct>? products,  String? contactName,  String? contactPhone,  String? relationship,  bool? sendGreeting,  String? customGreetingMessage)  $default,) {final _that = this;
switch (_that) {
case _CalendarEventModel():
return $default(_that.id,_that.title,_that.description,_that.startDate,_that.endDate,_that.type,_that.visibility,_that.status,_that.isRecurring,_that.recurrenceRule,_that.hasReminder,_that.reminder,_that.createdAt,_that.updatedAt,_that.userId,_that.products,_that.contactName,_that.contactPhone,_that.relationship,_that.sendGreeting,_that.customGreetingMessage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String title,  String description,  DateTime startDate,  DateTime endDate,  CalendarEventType type,  CalendarEventVisibility visibility,  CalendarEventStatus status,  bool isRecurring,  CalendarEventRecurrence? recurrenceRule,  bool hasReminder,  CalendarEventReminder? reminder,  DateTime createdAt,  DateTime updatedAt,  String? userId,  List<EventProduct>? products,  String? contactName,  String? contactPhone,  String? relationship,  bool? sendGreeting,  String? customGreetingMessage)?  $default,) {final _that = this;
switch (_that) {
case _CalendarEventModel() when $default != null:
return $default(_that.id,_that.title,_that.description,_that.startDate,_that.endDate,_that.type,_that.visibility,_that.status,_that.isRecurring,_that.recurrenceRule,_that.hasReminder,_that.reminder,_that.createdAt,_that.updatedAt,_that.userId,_that.products,_that.contactName,_that.contactPhone,_that.relationship,_that.sendGreeting,_that.customGreetingMessage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CalendarEventModel implements CalendarEventModel {
  const _CalendarEventModel({required this.id, required this.title, required this.description, required this.startDate, required this.endDate, required this.type, required this.visibility, required this.status, required this.isRecurring, this.recurrenceRule, required this.hasReminder, this.reminder, required this.createdAt, required this.updatedAt, this.userId, final  List<EventProduct>? products, this.contactName, this.contactPhone, this.relationship, this.sendGreeting, this.customGreetingMessage}): _products = products;
  factory _CalendarEventModel.fromJson(Map<String, dynamic> json) => _$CalendarEventModelFromJson(json);

@override final  String id;
@override final  String title;
@override final  String description;
@override final  DateTime startDate;
@override final  DateTime endDate;
@override final  CalendarEventType type;
@override final  CalendarEventVisibility visibility;
@override final  CalendarEventStatus status;
@override final  bool isRecurring;
@override final  CalendarEventRecurrence? recurrenceRule;
@override final  bool hasReminder;
@override final  CalendarEventReminder? reminder;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override final  String? userId;
// User ID of the event creator
 final  List<EventProduct>? _products;
// User ID of the event creator
@override List<EventProduct>? get products {
  final value = _products;
  if (value == null) return null;
  if (_products is EqualUnmodifiableListView) return _products;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

// Contact information for greeting messages
@override final  String? contactName;
@override final  String? contactPhone;
@override final  String? relationship;
@override final  bool? sendGreeting;
@override final  String? customGreetingMessage;

/// Create a copy of CalendarEventModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CalendarEventModelCopyWith<_CalendarEventModel> get copyWith => __$CalendarEventModelCopyWithImpl<_CalendarEventModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CalendarEventModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CalendarEventModel&&(identical(other.id, id) || other.id == id)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.type, type) || other.type == type)&&(identical(other.visibility, visibility) || other.visibility == visibility)&&(identical(other.status, status) || other.status == status)&&(identical(other.isRecurring, isRecurring) || other.isRecurring == isRecurring)&&(identical(other.recurrenceRule, recurrenceRule) || other.recurrenceRule == recurrenceRule)&&(identical(other.hasReminder, hasReminder) || other.hasReminder == hasReminder)&&(identical(other.reminder, reminder) || other.reminder == reminder)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.userId, userId) || other.userId == userId)&&const DeepCollectionEquality().equals(other._products, _products)&&(identical(other.contactName, contactName) || other.contactName == contactName)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.relationship, relationship) || other.relationship == relationship)&&(identical(other.sendGreeting, sendGreeting) || other.sendGreeting == sendGreeting)&&(identical(other.customGreetingMessage, customGreetingMessage) || other.customGreetingMessage == customGreetingMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,title,description,startDate,endDate,type,visibility,status,isRecurring,recurrenceRule,hasReminder,reminder,createdAt,updatedAt,userId,const DeepCollectionEquality().hash(_products),contactName,contactPhone,relationship,sendGreeting,customGreetingMessage]);

@override
String toString() {
  return 'CalendarEventModel(id: $id, title: $title, description: $description, startDate: $startDate, endDate: $endDate, type: $type, visibility: $visibility, status: $status, isRecurring: $isRecurring, recurrenceRule: $recurrenceRule, hasReminder: $hasReminder, reminder: $reminder, createdAt: $createdAt, updatedAt: $updatedAt, userId: $userId, products: $products, contactName: $contactName, contactPhone: $contactPhone, relationship: $relationship, sendGreeting: $sendGreeting, customGreetingMessage: $customGreetingMessage)';
}


}

/// @nodoc
abstract mixin class _$CalendarEventModelCopyWith<$Res> implements $CalendarEventModelCopyWith<$Res> {
  factory _$CalendarEventModelCopyWith(_CalendarEventModel value, $Res Function(_CalendarEventModel) _then) = __$CalendarEventModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String title, String description, DateTime startDate, DateTime endDate, CalendarEventType type, CalendarEventVisibility visibility, CalendarEventStatus status, bool isRecurring, CalendarEventRecurrence? recurrenceRule, bool hasReminder, CalendarEventReminder? reminder, DateTime createdAt, DateTime updatedAt, String? userId, List<EventProduct>? products, String? contactName, String? contactPhone, String? relationship, bool? sendGreeting, String? customGreetingMessage
});


@override $CalendarEventReminderCopyWith<$Res>? get reminder;

}
/// @nodoc
class __$CalendarEventModelCopyWithImpl<$Res>
    implements _$CalendarEventModelCopyWith<$Res> {
  __$CalendarEventModelCopyWithImpl(this._self, this._then);

  final _CalendarEventModel _self;
  final $Res Function(_CalendarEventModel) _then;

/// Create a copy of CalendarEventModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? title = null,Object? description = null,Object? startDate = null,Object? endDate = null,Object? type = null,Object? visibility = null,Object? status = null,Object? isRecurring = null,Object? recurrenceRule = freezed,Object? hasReminder = null,Object? reminder = freezed,Object? createdAt = null,Object? updatedAt = null,Object? userId = freezed,Object? products = freezed,Object? contactName = freezed,Object? contactPhone = freezed,Object? relationship = freezed,Object? sendGreeting = freezed,Object? customGreetingMessage = freezed,}) {
  return _then(_CalendarEventModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,startDate: null == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as CalendarEventType,visibility: null == visibility ? _self.visibility : visibility // ignore: cast_nullable_to_non_nullable
as CalendarEventVisibility,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as CalendarEventStatus,isRecurring: null == isRecurring ? _self.isRecurring : isRecurring // ignore: cast_nullable_to_non_nullable
as bool,recurrenceRule: freezed == recurrenceRule ? _self.recurrenceRule : recurrenceRule // ignore: cast_nullable_to_non_nullable
as CalendarEventRecurrence?,hasReminder: null == hasReminder ? _self.hasReminder : hasReminder // ignore: cast_nullable_to_non_nullable
as bool,reminder: freezed == reminder ? _self.reminder : reminder // ignore: cast_nullable_to_non_nullable
as CalendarEventReminder?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,products: freezed == products ? _self._products : products // ignore: cast_nullable_to_non_nullable
as List<EventProduct>?,contactName: freezed == contactName ? _self.contactName : contactName // ignore: cast_nullable_to_non_nullable
as String?,contactPhone: freezed == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String?,relationship: freezed == relationship ? _self.relationship : relationship // ignore: cast_nullable_to_non_nullable
as String?,sendGreeting: freezed == sendGreeting ? _self.sendGreeting : sendGreeting // ignore: cast_nullable_to_non_nullable
as bool?,customGreetingMessage: freezed == customGreetingMessage ? _self.customGreetingMessage : customGreetingMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of CalendarEventModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CalendarEventReminderCopyWith<$Res>? get reminder {
    if (_self.reminder == null) {
    return null;
  }

  return $CalendarEventReminderCopyWith<$Res>(_self.reminder!, (value) {
    return _then(_self.copyWith(reminder: value));
  });
}
}

// dart format on
