import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/providers/settings_provider.dart';
import 'package:shivish/apps/technician/providers/region_settings_provider.dart';
import 'package:timezone/data/latest.dart' as tz;

class LanguageSettingsScreen extends ConsumerStatefulWidget {
  const LanguageSettingsScreen({super.key});

  @override
  ConsumerState<LanguageSettingsScreen> createState() =>
      _LanguageSettingsScreenState();
}

class _LanguageSettingsScreenState
    extends ConsumerState<LanguageSettingsScreen> {
  @override
  void initState() {
    super.initState();
    tz.initializeTimeZones();
  }

  @override
  Widget build(BuildContext context) {
    final currentLanguage = ref.watch(languageProvider);
    final setLanguage = ref.read(languageProvider.notifier).setLanguage;
    final regionState = ref.watch(regionSettingsProvider);

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Language Settings',
      ),
      body: regionState.when(
        data: (settings) => ListView(
          children: [
            _buildLanguageSection(
              context,
              'App Language',
              'Select your preferred language',
              currentLanguage,
              setLanguage,
            ),
            const Divider(),
            _buildRegionSection(context, settings),
          ],
        ),
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildLanguageSection(
    BuildContext context,
    String title,
    String subtitle,
    String currentLanguage,
    void Function(String) setLanguage,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        ListTile(
          leading: const Icon(Icons.language),
          title: const Text('Language'),
          subtitle: Text(subtitle),
          trailing: DropdownButton<String>(
            value: currentLanguage,
            items: const [
              DropdownMenuItem(
                value: 'en',
                child: Text('English'),
              ),
              DropdownMenuItem(
                value: 'hi',
                child: Text('हिंदी'),
              ),
              DropdownMenuItem(
                value: 'mr',
                child: Text('मराठी'),
              ),
              DropdownMenuItem(
                value: 'gu',
                child: Text('ગુજરાતી'),
              ),
              DropdownMenuItem(
                value: 'bn',
                child: Text('বাংলা'),
              ),
              DropdownMenuItem(
                value: 'ta',
                child: Text('தமிழ்'),
              ),
              DropdownMenuItem(
                value: 'te',
                child: Text('తెలుగు'),
              ),
              DropdownMenuItem(
                value: 'kn',
                child: Text('ಕನ್ನಡ'),
              ),
              DropdownMenuItem(
                value: 'ml',
                child: Text('മലയാളം'),
              ),
              DropdownMenuItem(
                value: 'pa',
                child: Text('ਪੰਜਾਬੀ'),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                setLanguage(value);
                _showSnackBar(
                  context,
                  'Language changed to ${_getLanguageName(value)}',
                );
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRegionSection(
      BuildContext context, Map<String, String> settings) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Region',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        ListTile(
          leading: const Icon(Icons.public),
          title: const Text('Region'),
          subtitle: Text(_getRegionName(settings['region'] ?? 'IN')),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showRegionDialog(context, settings),
        ),
        ListTile(
          leading: const Icon(Icons.access_time),
          title: const Text('Time Zone'),
          subtitle:
              Text(_getTimezoneName(settings['timezone'] ?? 'Asia/Kolkata')),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showTimezoneDialog(context, settings),
        ),
        ListTile(
          leading: const Icon(Icons.attach_money),
          title: const Text('Currency'),
          subtitle: Text(_getCurrencyName(settings['currency'] ?? 'INR')),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showCurrencyDialog(context, settings),
        ),
      ],
    );
  }

  void _showRegionDialog(BuildContext context, Map<String, String> settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Region'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('India'),
                subtitle: const Text('IN'),
                selected: settings['region'] == 'IN',
                onTap: () => _updateRegion(context, 'IN'),
              ),
              ListTile(
                title: const Text('United States'),
                subtitle: const Text('US'),
                selected: settings['region'] == 'US',
                onTap: () => _updateRegion(context, 'US'),
              ),
              ListTile(
                title: const Text('United Kingdom'),
                subtitle: const Text('GB'),
                selected: settings['region'] == 'GB',
                onTap: () => _updateRegion(context, 'GB'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showTimezoneDialog(BuildContext context, Map<String, String> settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Time Zone'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('India Standard Time'),
                subtitle: const Text('Asia/Kolkata'),
                selected: settings['timezone'] == 'Asia/Kolkata',
                onTap: () => _updateTimezone(context, 'Asia/Kolkata'),
              ),
              ListTile(
                title: const Text('Pacific Time'),
                subtitle: const Text('America/Los_Angeles'),
                selected: settings['timezone'] == 'America/Los_Angeles',
                onTap: () => _updateTimezone(context, 'America/Los_Angeles'),
              ),
              ListTile(
                title: const Text('Eastern Time'),
                subtitle: const Text('America/New_York'),
                selected: settings['timezone'] == 'America/New_York',
                onTap: () => _updateTimezone(context, 'America/New_York'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCurrencyDialog(BuildContext context, Map<String, String> settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Currency'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('Indian Rupee'),
                subtitle: const Text('INR'),
                selected: settings['currency'] == 'INR',
                onTap: () => _updateCurrency(context, 'INR'),
              ),
              ListTile(
                title: const Text('US Dollar'),
                subtitle: const Text('USD'),
                selected: settings['currency'] == 'USD',
                onTap: () => _updateCurrency(context, 'USD'),
              ),
              ListTile(
                title: const Text('British Pound'),
                subtitle: const Text('GBP'),
                selected: settings['currency'] == 'GBP',
                onTap: () => _updateCurrency(context, 'GBP'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _updateRegion(BuildContext context, String region) async {
    try {
      await ref.read(regionSettingsProvider.notifier).saveRegionSettings(
            region: region,
            timezone: ref.read(regionSettingsProvider).value?['timezone'] ??
                'Asia/Kolkata',
            currency:
                ref.read(regionSettingsProvider).value?['currency'] ?? 'INR',
          );
      if (context.mounted) {
        Navigator.pop(context);
        _showSnackBar(context, 'Region updated successfully');
      }
    } catch (e) {
      if (context.mounted) {
        _showSnackBar(context, 'Failed to update region: ${e.toString()}');
      }
    }
  }

  Future<void> _updateTimezone(BuildContext context, String timezone) async {
    try {
      await ref.read(regionSettingsProvider.notifier).saveRegionSettings(
            region: ref.read(regionSettingsProvider).value?['region'] ?? 'IN',
            timezone: timezone,
            currency:
                ref.read(regionSettingsProvider).value?['currency'] ?? 'INR',
          );
      if (context.mounted) {
        Navigator.pop(context);
        _showSnackBar(context, 'Time zone updated successfully');
      }
    } catch (e) {
      if (context.mounted) {
        _showSnackBar(context, 'Failed to update time zone: ${e.toString()}');
      }
    }
  }

  Future<void> _updateCurrency(BuildContext context, String currency) async {
    try {
      await ref.read(regionSettingsProvider.notifier).saveRegionSettings(
            region: ref.read(regionSettingsProvider).value?['region'] ?? 'IN',
            timezone: ref.read(regionSettingsProvider).value?['timezone'] ??
                'Asia/Kolkata',
            currency: currency,
          );
      if (context.mounted) {
        Navigator.pop(context);
        _showSnackBar(context, 'Currency updated successfully');
      }
    } catch (e) {
      if (context.mounted) {
        _showSnackBar(context, 'Failed to update currency: ${e.toString()}');
      }
    }
  }

  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'hi':
        return 'हिंदी';
      case 'mr':
        return 'मराठी';
      case 'gu':
        return 'ગુજરાતી';
      case 'bn':
        return 'বাংলা';
      case 'ta':
        return 'தமிழ்';
      case 'te':
        return 'తెలుగు';
      case 'kn':
        return 'ಕನ್ನಡ';
      case 'ml':
        return 'മലയാളം';
      case 'pa':
        return 'ਪੰਜਾਬੀ';
      default:
        return 'English';
    }
  }

  String _getRegionName(String code) {
    switch (code) {
      case 'IN':
        return 'India';
      case 'US':
        return 'United States';
      case 'GB':
        return 'United Kingdom';
      default:
        return 'India';
    }
  }

  String _getTimezoneName(String code) {
    switch (code) {
      case 'Asia/Kolkata':
        return 'India Standard Time';
      case 'America/Los_Angeles':
        return 'Pacific Time';
      case 'America/New_York':
        return 'Eastern Time';
      default:
        return 'India Standard Time';
    }
  }

  String _getCurrencyName(String code) {
    switch (code) {
      case 'INR':
        return 'Indian Rupee';
      case 'USD':
        return 'US Dollar';
      case 'GBP':
        return 'British Pound';
      default:
        return 'Indian Rupee';
    }
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
