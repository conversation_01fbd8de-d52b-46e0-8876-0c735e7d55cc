import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/ticket_booking_models.dart';

class BusSeatSelectionScreen extends ConsumerStatefulWidget {
  final Bus bus;
  final BusSearchRequest searchRequest;

  const BusSeatSelectionScreen({
    super.key,
    required this.bus,
    required this.searchRequest,
  });

  @override
  ConsumerState<BusSeatSelectionScreen> createState() =>
      _BusSeatSelectionScreenState();
}

class _BusSeatSelectionScreenState
    extends ConsumerState<BusSeatSelectionScreen> {
  final Set<String> _selectedSeats = {};
  final Map<String, SeatStatus> _seatStatuses = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeSeatLayout();
  }

  void _initializeSeatLayout() {
    // Generate seat layout for a typical bus (2x2 configuration)
    final seatRows = 15; // 15 rows
    final seatsPerRow = 4; // 2 on each side

    for (int row = 1; row <= seatRows; row++) {
      for (int seat = 1; seat <= seatsPerRow; seat++) {
        final seatNumber =
            '${row}${String.fromCharCode(64 + seat)}'; // 1A, 1B, 1C, 1D, etc.

        // Randomly assign some seats as occupied for demo
        if ((row + seat) % 7 == 0) {
          _seatStatuses[seatNumber] = SeatStatus.occupied;
        } else if ((row + seat) % 11 == 0) {
          _seatStatuses[seatNumber] = SeatStatus.reserved;
        } else {
          _seatStatuses[seatNumber] = SeatStatus.available;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Seats'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Stack(
        children: [
          Column(
            children: [
              // Bus Info Header
              _buildBusInfoHeader(),

              // Seat Legend
              _buildSeatLegend(),

              // Seat Layout
              Expanded(child: _buildSeatLayout()),

              // Selected Seats Summary
              _buildSelectedSeatsSummary(),

              // Continue Button
              _buildContinueButton(),
            ],
          ),

          // Loading overlay
          if (_isLoading)
            Container(
              color: Colors.black54,
              child: const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(color: Colors.white),
                    SizedBox(height: 16),
                    Text(
                      'Processing booking...',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBusInfoHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.green.shade50,
      child: Row(
        children: [
          Icon(Icons.directions_bus, color: Colors.green.shade700),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.bus.operator,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${widget.bus.origin} → ${widget.bus.destination}',
                  style: TextStyle(color: Colors.grey.shade600),
                ),
                Text(
                  '${_formatTime(widget.bus.departureTime)} - ${_formatTime(widget.bus.arrivalTime)}',
                  style: TextStyle(color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
          Text(
            '${widget.bus.currency} ${widget.bus.price.toStringAsFixed(0)}',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSeatLegend() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildLegendItem('Available', Colors.grey.shade300, Icons.event_seat),
          _buildLegendItem('Selected', Colors.green, Icons.event_seat),
          _buildLegendItem('Occupied', Colors.red, Icons.event_seat),
          _buildLegendItem('Reserved', Colors.orange, Icons.event_seat),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  Widget _buildSeatLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Driver section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Row(
              children: [
                Icon(Icons.airline_seat_recline_normal, color: Colors.grey),
                SizedBox(width: 8),
                Text('Driver', style: TextStyle(fontWeight: FontWeight.w500)),
              ],
            ),
          ),

          // Seat rows
          ..._buildSeatRows(),
        ],
      ),
    );
  }

  List<Widget> _buildSeatRows() {
    final rows = <Widget>[];

    for (int row = 1; row <= 15; row++) {
      rows.add(
        Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Row(
            children: [
              // Row number
              SizedBox(
                width: 30,
                child: Text(
                  '$row',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),

              // Left side seats (A, B)
              _buildSeat('${row}A'),
              const SizedBox(width: 8),
              _buildSeat('${row}B'),

              // Aisle
              const SizedBox(width: 40),

              // Right side seats (C, D)
              _buildSeat('${row}C'),
              const SizedBox(width: 8),
              _buildSeat('${row}D'),
            ],
          ),
        ),
      );
    }

    return rows;
  }

  Widget _buildSeat(String seatNumber) {
    final status = _seatStatuses[seatNumber] ?? SeatStatus.available;
    final isSelected = _selectedSeats.contains(seatNumber);

    Color seatColor;
    bool isSelectable = false;

    switch (status) {
      case SeatStatus.available:
        seatColor = isSelected ? Colors.green : Colors.grey.shade300;
        isSelectable = true;
        break;
      case SeatStatus.occupied:
        seatColor = Colors.red;
        break;
      case SeatStatus.reserved:
        seatColor = Colors.orange;
        break;
    }

    return GestureDetector(
      onTap: isSelectable ? () => _toggleSeat(seatNumber) : null,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: seatColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Colors.green.shade700 : Colors.grey.shade400,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Center(
          child: Text(
            seatNumber,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: isSelected || status != SeatStatus.available
                  ? Colors.white
                  : Colors.black87,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedSeatsSummary() {
    if (_selectedSeats.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Text(
          'Please select ${widget.searchRequest.passengers} seat${widget.searchRequest.passengers > 1 ? 's' : ''}',
          style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
        ),
      );
    }

    final totalPrice = widget.bus.price * _selectedSeats.length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Seats: ${_selectedSeats.join(', ')}',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total: ${_selectedSeats.length} seat${_selectedSeats.length > 1 ? 's' : ''}',
                style: const TextStyle(fontSize: 16),
              ),
              Text(
                '${widget.bus.currency} ${totalPrice.toStringAsFixed(0)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton() {
    final isValidSelection =
        _selectedSeats.length == widget.searchRequest.passengers;

    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: isValidSelection ? _proceedToBooking : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            isValidSelection
                ? 'Continue to Booking'
                : 'Select ${widget.searchRequest.passengers} seat${widget.searchRequest.passengers > 1 ? 's' : ''}',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
        ),
      ),
    );
  }

  void _toggleSeat(String seatNumber) {
    setState(() {
      if (_selectedSeats.contains(seatNumber)) {
        _selectedSeats.remove(seatNumber);
      } else {
        if (_selectedSeats.length < widget.searchRequest.passengers) {
          _selectedSeats.add(seatNumber);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'You can only select ${widget.searchRequest.passengers} seat${widget.searchRequest.passengers > 1 ? 's' : ''}',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    });
  }

  Future<void> _proceedToBooking() async {
    setState(() => _isLoading = true);

    try {
      // Simulate booking process
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bus seats booked successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Booking failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}

enum SeatStatus { available, occupied, reserved }
