import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/providers/settings_provider.dart';

class WorkingHoursSettingsScreen extends ConsumerStatefulWidget {
  const WorkingHoursSettingsScreen({super.key});

  @override
  ConsumerState<WorkingHoursSettingsScreen> createState() =>
      _WorkingHoursSettingsScreenState();
}

class _WorkingHoursSettingsScreenState
    extends ConsumerState<WorkingHoursSettingsScreen> {
  late Map<String, List<TimeOfDay>> _workingHours;

  @override
  void initState() {
    super.initState();
    _workingHours = Map.from(ref.read(workingHoursProvider));
  }

  Future<void> _selectTime(
      BuildContext context, String day, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime:
          isStartTime ? _workingHours[day]![0] : _workingHours[day]![1],
    );
    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _workingHours[day]![0] = picked;
        } else {
          _workingHours[day]![1] = picked;
        }
      });
    }
  }

  Future<void> _saveWorkingHours() async {
    try {
      await ref
          .read(workingHoursProvider.notifier)
          .saveWorkingHours(_workingHours);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Working hours updated successfully'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update working hours: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Working Hours',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Set your working hours for each day',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ..._workingHours.entries.map((entry) {
                      return Column(
                        children: [
                          ListTile(
                            title: Text(entry.key),
                            subtitle: Row(
                              children: [
                                Expanded(
                                  child: TextButton(
                                    onPressed: () =>
                                        _selectTime(context, entry.key, true),
                                    child: Text(
                                      '${entry.value[0].hour.toString().padLeft(2, '0')}:${entry.value[0].minute.toString().padLeft(2, '0')}',
                                    ),
                                  ),
                                ),
                                const Text('to'),
                                Expanded(
                                  child: TextButton(
                                    onPressed: () =>
                                        _selectTime(context, entry.key, false),
                                    child: Text(
                                      '${entry.value[1].hour.toString().padLeft(2, '0')}:${entry.value[1].minute.toString().padLeft(2, '0')}',
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (entry.key != 'Sunday') const Divider(),
                        ],
                      );
                    }),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            AppButton(
              onPressed: _saveWorkingHours,
              child: const Text('Save Working Hours'),
            ),
          ],
        ),
      ),
    );
  }
}
