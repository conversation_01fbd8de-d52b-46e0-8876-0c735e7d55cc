import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/shared/services/notification/notification_service.dart';
import 'package:shivish/shared/models/notification/notification_model.dart';

part 'notifications_cubit.freezed.dart';

@freezed
sealed class NotificationsState with _$NotificationsState {
  const factory NotificationsState.loading() = _Loading;
  const factory NotificationsState.error(String message) = _Error;
  const factory NotificationsState.loaded(
    List<NotificationModel> notifications,
  ) = _Loaded;
}

class NotificationsCubit extends Cubit<NotificationsState> {
  final NotificationService _notificationsService;
  final String _userId;

  NotificationsCubit(this._notificationsService, this._userId)
    : super(const NotificationsState.loading()) {
    loadNotifications();
  }

  Future<void> loadNotifications() async {
    try {
      emit(const NotificationsState.loading());
      final notifications = await _notificationsService
          .getNotifications(_userId)
          .first;
      emit(NotificationsState.loaded(notifications));
    } catch (e) {
      emit(NotificationsState.error(e.toString()));
    }
  }

  Future<void> markAsRead(String notificationId) async {
    try {
      await _notificationsService.markAsRead(notificationId);
      loadNotifications(); // Reload notifications to get updated state
    } catch (e) {
      emit(NotificationsState.error(e.toString()));
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    try {
      await _notificationsService.deleteNotification(notificationId);
      loadNotifications(); // Reload notifications to get updated state
    } catch (e) {
      emit(NotificationsState.error(e.toString()));
    }
  }
}
