-- Migration: Create system configuration and payment gateway tables
-- This migration creates tables for system configuration and payment gateway management

-- System configuration table
CREATE TABLE IF NOT EXISTS system_config (
    id VARCHAR(255) PRIMARY KEY DEFAULT 'config',
    security JSONB NOT NULL DEFAULT '{}',
    backup JSONB NOT NULL DEFAULT '{}',
    refund JSONB NOT NULL DEFAULT '{}',
    ai JSONB NOT NULL DEFAULT '{}',
    voice_command JSONB NOT NULL DEFAULT '{}',
    chatbot JSONB NOT NULL DEFAULT '{}',
    additional_settings JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment gateways table
CREATE TABLE IF NOT EXISTS payment_gateways (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    credentials JSONB NOT NULL DEFAULT '{}',
    transaction_fee DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    minimum_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    maximum_amount DECIMAL(10,2) NOT NULL DEFAULT 999999.99,
    supported_currencies TEXT[] DEFAULT '{"INR"}',
    supported_payment_methods TEXT[] DEFAULT '{}',
    transaction_fees JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_payment_gateways_type ON payment_gateways(type);
CREATE INDEX IF NOT EXISTS idx_payment_gateways_is_active ON payment_gateways(is_active);
CREATE INDEX IF NOT EXISTS idx_payment_gateways_is_deleted ON payment_gateways(is_deleted);

-- Add triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_system_config_updated_at 
    BEFORE UPDATE ON system_config 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_gateways_updated_at 
    BEFORE UPDATE ON payment_gateways 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default system configuration if not exists
INSERT INTO system_config (id, security, backup, refund, ai, voice_command, chatbot, additional_settings)
VALUES (
    'config',
    '{"enabled": true, "maxLoginAttempts": 5, "sessionTimeout": 3600, "requireTwoFactor": false, "allowedDomains": [], "blockedIPs": [], "encryptionSettings": {"algorithm": "AES-256", "keyRotationInterval": 86400}}',
    '{"enabled": true, "frequency": "daily", "retentionDays": 30, "compressionEnabled": true, "encryptionEnabled": true, "storageLocation": "hybrid", "maxBackupSize": 1073741824, "excludedTables": []}',
    '{"enabled": true, "maxRefundDays": 30, "autoRefundEnabled": false, "refundProcessingFee": 0.0, "minimumRefundAmount": 1.0, "maximumRefundAmount": 100000.0, "allowedReasons": ["defective_product", "wrong_item", "not_satisfied", "damaged_in_transit"], "requireApproval": true}',
    '{"enabled": true, "modelName": "gpt-3.5-turbo", "apiKey": "", "maxTokens": 1000, "temperature": 0.7, "language": "en", "supportedLanguages": ["en", "hi"], "features": {"textGeneration": true, "imageAnalysis": false, "voiceRecognition": false}, "rateLimits": {"requestsPerMinute": 60, "tokensPerDay": 100000}}',
    '{"enabled": true, "language": "en", "wakeWord": "hey assistant", "sensitivity": 0.7, "timeout": 5000, "supportedLanguages": ["en", "hi"], "commands": {"navigate": true, "search": true, "control": true}, "voiceSettings": {"pitch": 1.0, "speed": 1.0, "volume": 0.8}}',
    '{"enabled": true, "modelName": "gpt-3.5-turbo", "language": "en", "temperature": 0.7, "maxTokens": 1000, "supportedLanguages": ["en", "hi"], "responseTemplates": {"greeting": "Hello! How can I help you today?", "farewell": "Thank you for using our service!"}, "modelParameters": {"presence_penalty": 0.6, "frequency_penalty": 0.0}}',
    '{"darkModeEnabled": true, "notificationsEnabled": true}'
)
ON CONFLICT (id) DO NOTHING;
