import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/shared/core/navigation/shells/admin_shell.dart';
import 'package:shivish/apps/admin/screens/home_screen.dart';
import 'package:shivish/apps/admin/screens/login_screen.dart';
import 'package:shivish/apps/admin/screens/register_screen.dart';
import 'package:shivish/apps/admin/screens/executor_requests_screen.dart';
import 'package:shivish/apps/admin/screens/seller_requests_screen.dart';
import 'package:shivish/apps/admin/screens/priest_requests_screen.dart';
import 'package:shivish/apps/admin/screens/technician_requests_screen.dart';
import 'package:shivish/apps/admin/screens/delivery_partner_requests_screen.dart';
import 'package:shivish/apps/admin/screens/saviour_profile_change_requests_screen.dart';
import 'package:shivish/apps/admin/screens/hospital_requests_screen.dart';
import 'package:shivish/apps/admin/screens/temple/temple_approval_screen.dart';
import 'package:shivish/apps/admin/screens/registration_settings_screen.dart';
import 'package:shivish/apps/admin/screens/user_management_screen.dart';
import 'package:shivish/apps/admin/screens/executor_management_screen.dart';
import 'package:shivish/apps/admin/screens/seller_management_screen.dart';
import 'package:shivish/apps/admin/screens/priest_management_screen.dart';
import 'package:shivish/apps/admin/screens/technician_management_screen.dart';
import 'package:shivish/apps/admin/screens/product_management_screen.dart';
import 'package:shivish/apps/admin/screens/analytics_dashboard_screen.dart';
import 'package:shivish/apps/admin/screens/system_settings_screen.dart';
import 'package:shivish/apps/admin/screens/refund_management_screen.dart';
import 'package:shivish/apps/admin/screens/admin_profile_screen.dart';
import 'package:shivish/apps/admin/screens/commission_management_screen.dart';
import 'package:shivish/apps/admin/screens/api_key_management_screen.dart';
import 'package:shivish/apps/admin/screens/ai_settings_management_screen.dart';
import 'package:shivish/apps/admin/screens/voice_command_training_management_screen.dart';
import 'package:shivish/apps/admin/screens/notifications_screen.dart';
import 'package:shivish/apps/admin/screens/icon_management_screen.dart';
import 'package:shivish/apps/admin/screens/banner_pricing_screen.dart';
import 'package:shivish/apps/admin/screens/banner_management_screen.dart';
import 'package:shivish/apps/admin/screens/admin_event_management_screen.dart';
import 'package:shivish/apps/admin/screens/admin_event_form_screen.dart';
import 'package:shivish/apps/admin/screens/admin_media_management_screen.dart';
import 'package:shivish/shared/models/event/event_model.dart';
import 'package:shivish/shared/models/media/media_model.dart';
import 'package:shivish/shared/models/calendar/calendar_event_model.dart';
import 'screens/product_list/admin_product_list_screen.dart';
import 'screens/product_list/admin_product_list_form_screen_new.dart';
import 'package:shivish/shared/services/notification_service.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/shared/services/user/user_service.dart';
import 'package:shivish/shared/core/service_locator.dart';

import 'package:shivish/apps/admin/screens/security_settings_screen.dart';
import 'package:shivish/apps/admin/screens/analytics_settings_screen.dart';
import 'package:shivish/apps/admin/screens/app_config_screen.dart';
import 'package:shivish/apps/admin/screens/system_config_screen.dart';
import 'package:shivish/apps/admin/screens/admin_media_form_screen.dart';
import 'package:shivish/apps/admin/screens/tone_management_screen.dart';
import 'package:shivish/apps/admin/screens/tone_approval_screen.dart';
import 'package:shivish/apps/admin/screens/notification_settings_screen.dart';
import 'package:shivish/apps/admin/screens/phonepe_settings_screen.dart';
import 'package:shivish/apps/admin/screens/phonepe_transactions_screen.dart';
import 'package:shivish/apps/admin/screens/delivery_settings_screen.dart';
import 'package:shivish/apps/admin/screens/delivery_charges_screen.dart';
import 'screens/ai_assistant_settings_screen.dart';
import 'screens/offers/bank_offers_screen.dart';
import 'screens/webhook_endpoint_screen.dart';
import 'screens/tax/tax_rates_screen.dart';
import 'screens/storage_settings_screen.dart';
import 'screens/ride/vehicle_fare_management_screen.dart';
import 'screens/infrastructure/add_server_screen.dart';
import 'screens/infrastructure/infrastructure_dashboard_screen.dart';

class AdminRoutes {
  static const String initialLocation = '/admin/login';

  // Auth Routes
  static const String login = '/admin/login';
  static const String register = '/admin/register';

  // Main Routes
  static const String home = '/admin/home';
  static const String profile = '/admin/profile';

  // User Management Routes
  static const String users = '/admin/users';
  static const String executors = '/admin/executors';
  static const String executorRequests = '/admin/executor-requests';
  static const String sellers = '/admin/sellers';
  static const String sellerRequests = '/admin/seller-requests';
  static const String priests = '/admin/priests';
  static const String priestRequests = '/admin/priest-requests';
  static const String technicians = '/admin/technicians';
  static const String technicianRequests = '/admin/technician-requests';
  static const String deliveryPartners = '/admin/delivery-partners';
  static const String deliveryPartnerRequests =
      '/admin/delivery-partner-requests';
  static const String saviourProfileChangeRequests =
      '/admin/saviour-profile-change-requests';
  static const String hospitalRequests = '/admin/hospital-requests';
  static const String templeRequests = '/admin/temple-requests';

  // Content Management Routes
  static const String products = '/admin/products';
  static const String productLists = '/admin/product-lists';
  static const String productListsCreate = '/admin/product-lists/create';
  static const String productListsEdit = '/admin/product-lists/edit';
  static const String events = '/admin/events';
  static const String eventsCreate = '/admin/events/create';
  static const String eventsEdit = '/admin/events/edit';
  static const String media = '/admin/media';
  static const String banners = '/admin/banners';
  static const String bankOffers = '/admin/bank-offers';

  // System Routes
  static const String settings = '/admin/settings';
  static const String analytics = '/admin/analytics';
  static const String refunds = '/admin/refunds';
  static const String commission = '/admin/commission';
  static const String apiKeys = '/admin/api-keys';
  static const String aiSettings = '/admin/ai-settings';
  static const String voiceCommandTraining = '/admin/voice-command-training';
  static const String notifications = '/admin/notifications';
  static const String iconManagement = '/admin/settings/icon-management';
  static const String bannerPricing = '/admin/settings/banner-pricing';
  static const String registrationSettings = '/admin/settings/registration';
  static const String taxRates = '/admin/settings/tax-rates';

  // Additional Route Constants
  static const String securitySettings = '/admin/settings/security';
  static const String analyticsSettings = '/admin/settings/analytics';
  static const String paymentGateway = '/admin/settings/payment-gateway';
  static const String appConfig = '/admin/settings/app-config';
  static const String systemConfig = '/admin/settings/system-config';
  static const String mediaCreate = '/admin/media/create';
  static const String mediaEdit = '/admin/media/edit';
  static const String tones = '/admin/tones';
  static const String toneApproval = '/admin/tones/approval';
  static const String paymentGatewayFees = '/admin/payment-gateway/fees';
  static const String paymentGatewayCredentials =
      '/admin/payment-gateway/credentials';
  static const String notificationSettings = '/admin/notifications/settings';
  static const String razorpaySettings = '/admin/payment-gateway/razorpay';
  static const String phonePeSettings = '/admin/payment-gateway/phonepe';
  static const String phonePeTransactions =
      '/admin/payment-gateway/phonepe/transactions';
  static const String aiAssistantSettings = '/admin/settings/ai-assistant';
  static const String webhookEndpoints = '/admin/settings/webhook-endpoints';
  static const String deliverySettings = '/admin/settings/delivery';
  static const String deliveryCharges = '/admin/settings/delivery-charges';
  static const String storageSettings = '/admin/settings/storage';
  static const String vehicleFareManagement = '/admin/settings/vehicle-fares';

  // Infrastructure Routes
  static const String infrastructure = '/admin/infrastructure';
  static const String infrastructureAddServer =
      '/admin/infrastructure/add-server';

  static GoRouter createRouter(BuildContext context) {
    return GoRouter(
      initialLocation: initialLocation,
      routes: [
        ShellRoute(
          builder: (context, state, child) {
            // Determine the current index based on the route path
            int currentIndex = 2; // Default to home
            final path = state.uri.path;

            if (path.startsWith(settings)) {
              currentIndex = 0; // Settings tab
            } else if (path.startsWith(media)) {
              currentIndex = 1; // Media tab
            } else if (path.startsWith(home)) {
              currentIndex = 2; // Home tab
            } else if (path.startsWith(tones)) {
              currentIndex = 3; // Tones tab
            } else if (path.startsWith(profile)) {
              currentIndex = 4; // Profile tab
            }

            return AdminShell(currentIndex: currentIndex, child: child);
          },
          routes: [
            // Auth Routes
            GoRoute(
              path: login,
              builder: (context, state) => const AdminLoginScreen(),
            ),
            GoRoute(
              path: register,
              builder: (context, state) => const AdminRegisterScreen(),
            ),

            // Main Routes
            GoRoute(
              path: home,
              builder: (context, state) =>
                  HomeScreen(serviceLocator<NotificationService>()),
            ),

            GoRoute(
              path: profile,
              builder: (context, state) => const AdminProfileScreen(),
            ),

            // User Management Routes
            GoRoute(
              path: users,
              builder: (context, state) => const UserManagementScreen(),
            ),

            GoRoute(
              path: executors,
              builder: (context, state) => const ExecutorManagementScreen(),
            ),

            GoRoute(
              path: executorRequests,
              builder: (context, state) => const ExecutorRequestsScreen(),
            ),

            GoRoute(
              path: sellers,
              builder: (context, state) => const SellerManagementScreen(),
            ),

            GoRoute(
              path: sellerRequests,
              builder: (context, state) => const SellerRequestsScreen(),
            ),

            GoRoute(
              path: priests,
              builder: (context, state) => const PriestManagementScreen(),
            ),

            GoRoute(
              path: priestRequests,
              builder: (context, state) => const PriestRequestsScreen(),
            ),

            GoRoute(
              path: technicians,
              builder: (context, state) => const TechnicianManagementScreen(),
            ),

            GoRoute(
              path: technicianRequests,
              builder: (context, state) => const TechnicianRequestsScreen(),
            ),

            GoRoute(
              path: deliveryPartnerRequests,
              builder: (context, state) =>
                  const DeliveryPartnerRequestsScreen(),
            ),

            GoRoute(
              path: saviourProfileChangeRequests,
              builder: (context, state) =>
                  const SaviourProfileChangeRequestsScreen(),
            ),

            GoRoute(
              path: hospitalRequests,
              builder: (context, state) => const HospitalRequestsScreen(),
            ),

            GoRoute(
              path: templeRequests,
              builder: (context, state) => const TempleApprovalScreen(),
            ),

            // Content Management Routes
            GoRoute(
              path: products,
              builder: (context, state) => const ProductManagementScreen(),
            ),

            GoRoute(
              path: productLists,
              builder: (context, state) => const AdminProductListScreen(),
            ),

            GoRoute(
              path: productListsCreate,
              builder: (context, state) => const AdminProductListFormScreen(),
            ),

            GoRoute(
              path: '$productListsEdit/:id',
              builder: (context, state) => AdminProductListFormScreen(
                event: state.extra as CalendarEventModel?,
              ),
            ),

            GoRoute(
              path: events,
              builder: (context, state) => const AdminEventManagementScreen(),
            ),

            GoRoute(
              path: eventsCreate,
              builder: (context, state) => const AdminEventFormScreen(),
            ),

            GoRoute(
              path: '$eventsEdit/:id',
              builder: (context, state) =>
                  AdminEventFormScreen(event: state.extra as EventModel?),
            ),

            GoRoute(
              path: media,
              builder: (context, state) => const AdminMediaManagementScreen(),
            ),

            GoRoute(
              path: banners,
              builder: (context, state) => const BannerManagementScreen(),
            ),

            GoRoute(
              path: bankOffers,
              builder: (context, state) => const BankOffersScreen(),
            ),

            // System Routes
            GoRoute(
              path: settings,
              builder: (context, state) => const SystemSettingsScreen(),
              routes: [
                GoRoute(
                  path: 'security',
                  builder: (context, state) => const SecuritySettingsScreen(),
                ),
                GoRoute(
                  path: 'analytics',
                  builder: (context, state) => const AnalyticsSettingsScreen(),
                ),
                GoRoute(
                  path: 'payment-gateway',
                  builder: (context, state) => const PhonePeSettingsScreen(),
                ),
                GoRoute(
                  path: 'app-config',
                  builder: (context, state) => const AppConfigScreen(),
                ),
                GoRoute(
                  path: 'system-config',
                  builder: (context, state) => const SystemConfigScreen(),
                ),
                GoRoute(
                  path: 'icon-management',
                  builder: (context, state) => const IconManagementScreen(),
                ),
                GoRoute(
                  path: 'banner-pricing',
                  builder: (context, state) => const BannerPricingScreen(),
                ),
                GoRoute(
                  path: 'ai-assistant',
                  builder: (context, state) =>
                      const AdminAIAssistantSettingsScreen(),
                ),
                GoRoute(
                  path: 'registration',
                  builder: (context, state) =>
                      const RegistrationSettingsScreen(),
                ),
                GoRoute(
                  path: 'webhook-endpoints',
                  builder: (context, state) => const WebhookEndpointScreen(),
                ),
                GoRoute(
                  path: 'delivery',
                  builder: (context, state) => const DeliverySettingsScreen(),
                ),
                GoRoute(
                  path: 'delivery-charges',
                  builder: (context, state) => const DeliveryChargesScreen(),
                ),
                GoRoute(
                  path: 'tax-rates',
                  builder: (context, state) => const TaxRatesScreen(),
                ),
                GoRoute(
                  path: 'storage',
                  builder: (context, state) => const StorageSettingsScreen(),
                ),
                GoRoute(
                  path: 'vehicle-fares',
                  builder: (context, state) =>
                      const VehicleFareManagementScreen(),
                ),
              ],
            ),

            // Infrastructure Routes
            GoRoute(
              path: infrastructure,
              builder: (context, state) =>
                  const InfrastructureDashboardScreen(),
              routes: [
                GoRoute(
                  path: 'add-server',
                  builder: (context, state) => const AddServerScreen(),
                ),
              ],
            ),

            GoRoute(
              path: analytics,
              builder: (context, state) => const AnalyticsDashboardScreen(),
            ),

            GoRoute(
              path: refunds,
              builder: (context, state) => RefundManagementScreen(
                authService: serviceLocator<AuthService>(),
                userService: serviceLocator<UserService>(),
              ),
            ),

            GoRoute(
              path: commission,
              builder: (context, state) => const CommissionManagementScreen(),
            ),

            GoRoute(
              path: apiKeys,
              builder: (context, state) => const ApiKeyManagementScreen(),
            ),

            GoRoute(
              path: aiSettings,
              builder: (context, state) => const AISettingsManagementScreen(),
            ),

            GoRoute(
              path: voiceCommandTraining,
              builder: (context, state) =>
                  const VoiceCommandTrainingManagementScreen(),
            ),

            GoRoute(
              path: notifications,
              builder: (context, state) => const NotificationsScreen(),
            ),

            // Media Management Routes
            GoRoute(
              path: mediaCreate,
              builder: (context, state) => const AdminMediaFormScreen(),
            ),
            GoRoute(
              path: '$mediaEdit/:id',
              builder: (context, state) =>
                  AdminMediaFormScreen(media: state.extra as MediaModel?),
            ),

            // Tone Management Routes
            GoRoute(
              path: tones,
              builder: (context, state) => const ToneManagementScreen(),
            ),
            GoRoute(
              path: toneApproval,
              builder: (context, state) => const ToneApprovalScreen(),
            ),
            // Notification Settings Route
            GoRoute(
              path: notificationSettings,
              builder: (context, state) => const NotificationSettingsScreen(),
            ),

            // Payment Gateway Settings Routes
            GoRoute(
              path: razorpaySettings,
              builder: (context, state) => const PhonePeSettingsScreen(),
            ),
            GoRoute(
              path: phonePeSettings,
              builder: (context, state) => const PhonePeSettingsScreen(),
            ),
            GoRoute(
              path: phonePeTransactions,
              builder: (context, state) => const PhonePeTransactionsScreen(),
            ),
          ],
        ),
      ],
      errorBuilder: (context, state) => Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Error: ${state.error}',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go(home),
                child: const Text('Go to Home'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
