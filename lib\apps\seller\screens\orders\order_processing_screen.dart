import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/models/order/order_model.dart';
import 'package:shivish/shared/providers/order_provider.dart';
import 'package:shivish/shared/models/order/order_status.dart' as order_status;
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

class OrderProcessingScreen extends ConsumerStatefulWidget {
  const OrderProcessingScreen({super.key});

  @override
  ConsumerState<OrderProcessingScreen> createState() =>
      _OrderProcessingScreenState();
}

class _OrderProcessingScreenState extends ConsumerState<OrderProcessingScreen> {
  bool _isLoading = true;
  List<OrderModel> _processingOrders = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadProcessingOrders();
  }

  Future<void> _loadProcessingOrders() async {
    try {
      final orders = await ref
          .read(orderServiceProvider)
          .getOrdersByStatus(order_status.OrderStatus.processing);
      if (mounted) {
        setState(() {
          _processingOrders = orders
              .where(
                (order) =>
                    order.status == OrderStatus.processing ||
                    order.status == OrderStatus.readyForPickup ||
                    order.status == OrderStatus.outForDelivery,
              )
              .toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateOrderStatus(
    OrderModel order,
    order_status.OrderStatus status,
  ) async {
    try {
      await ref.read(orderServiceProvider).updateOrderStatus(order.id, status);
      await _loadProcessingOrders();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update order status: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(body: LoadingIndicator());
    }

    if (_error != null) {
      return Scaffold(
        body: ErrorMessage(message: _error!, onRetry: _loadProcessingOrders),
      );
    }

    if (_processingOrders.isEmpty) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle_outline,
                size: 64,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                'No orders in processing',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                'All orders are up to date',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _loadProcessingOrders,
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _processingOrders.length,
          itemBuilder: (context, index) {
            final order = _processingOrders[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Order #${order.id}',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor(
                              order.status,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            order.status.name.toUpperCase(),
                            style: TextStyle(
                              color: _getStatusColor(order.status),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${order.items.length} items • ₹${order.total.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (order.status == OrderStatus.processing)
                      FilledButton.icon(
                        onPressed: () => _updateOrderStatus(
                          order,
                          order_status.OrderStatus.delivered,
                        ),
                        icon: const Icon(Icons.local_shipping),
                        label: const Text('Mark as Shipped'),
                      ),
                    if (order.status == OrderStatus.delivered)
                      FilledButton.icon(
                        onPressed: () => _updateOrderStatus(
                          order,
                          order_status.OrderStatus.delivered,
                        ),
                        icon: const Icon(Icons.check_circle),
                        label: const Text('Mark as Delivered'),
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.confirmed:
        return Colors.blue;
      case OrderStatus.processing:
        return Colors.purple;
      case OrderStatus.readyForPickup:
        return Colors.indigo;
      case OrderStatus.outForDelivery:
        return Colors.deepPurple;
      case OrderStatus.inTransit:
        return Colors.blue;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
      case OrderStatus.refunded:
        return Colors.grey;
    }
  }
}
