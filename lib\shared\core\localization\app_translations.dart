import 'package:flutter/material.dart';

class AppTranslations {
  static const Map<String, Map<String, String>> translations = {
    'en': {
      // Alarm Screen
      'defaultAlarm': 'Default Alarm',
      'time': 'Time',
      'tone': 'Tone',
      'volume': 'Volume',
      'vibration': 'Vibration',
      'snoozeSettings': 'Snooze Settings',
      'snoozeDuration': 'Snooze Duration',
      'maxSnoozeCount': 'Max Snooze Count',
      'minutes': 'minutes',
      'save': 'Save',
      'alarmSaved': 'Alarm saved successfully',
      'errorSavingAlarm': 'Error saving alarm',

      // Common
      'cancel': 'Cancel',
      'ok': 'OK',
      'error': 'Error',
      'success': 'Success',
      'loading': 'Loading...',
      'retry': 'Retry',
      'noData': 'No data available',
      'noInternet': 'No internet connection',
      'serverError': 'Server error occurred',
      'tryAgain': 'Try again',
    },
    'hi': {
      // Alarm Screen
      'defaultAlarm': 'डिफ़ॉल्ट अलार्म',
      'time': 'समय',
      'tone': 'टोन',
      'volume': 'वॉल्यूम',
      'vibration': 'वाइब्रेशन',
      'snoozeSettings': 'स्नूज़ सेटिंग्स',
      'snoozeDuration': 'स्नूज़ अवधि',
      'maxSnoozeCount': 'अधिकतम स्नूज़ संख्या',
      'minutes': 'मिनट',
      'save': 'सहेजें',
      'alarmSaved': 'अलार्म सफलतापूर्वक सहेजा गया',
      'errorSavingAlarm': 'अलार्म सहेजने में त्रुटि',

      // Common
      'cancel': 'रद्द करें',
      'ok': 'ठीक है',
      'error': 'त्रुटि',
      'success': 'सफल',
      'loading': 'लोड हो रहा है...',
      'retry': 'पुनः प्रयास करें',
      'noData': 'कोई डेटा उपलब्ध नहीं है',
      'noInternet': 'इंटरनेट कनेक्शन नहीं है',
      'serverError': 'सर्वर त्रुटि हुई',
      'tryAgain': 'पुनः प्रयास करें',
    },
    'bn': {
      // Alarm Screen
      'defaultAlarm': 'ডিফল্ট অ্যালার্ম',
      'time': 'সময়',
      'tone': 'টোন',
      'volume': 'ভলিউম',
      'vibration': 'কম্পন',
      'snoozeSettings': 'স্নুজ সেটিংস',
      'snoozeDuration': 'স্নুজ সময়কাল',
      'maxSnoozeCount': 'সর্বোচ্চ স্নুজ সংখ্যা',
      'minutes': 'মিনিট',
      'save': 'সংরক্ষণ',
      'alarmSaved': 'অ্যালার্ম সফলভাবে সংরক্ষিত হয়েছে',
      'errorSavingAlarm': 'অ্যালার্ম সংরক্ষণে ত্রুটি',

      // Common
      'cancel': 'বাতিল',
      'ok': 'ঠিক আছে',
      'error': 'ত্রুটি',
      'success': 'সফল',
      'loading': 'লোড হচ্ছে...',
      'retry': 'পুনরায় চেষ্টা করুন',
      'noData': 'কোন ডেটা নেই',
      'noInternet': 'ইন্টারনেট সংযোগ নেই',
      'serverError': 'সার্ভার ত্রুটি ঘটেছে',
      'tryAgain': 'আবার চেষ্টা করুন',
    },
    'ta': {
      // Alarm Screen
      'defaultAlarm': 'இயல்புநிலை எச்சரிக்கை',
      'time': 'நேரம்',
      'tone': 'ஒலி',
      'volume': 'ஒலி அளவு',
      'vibration': 'துடிப்பு',
      'snoozeSettings': 'தூங்கு அமைப்புகள்',
      'snoozeDuration': 'தூங்கு காலம்',
      'maxSnoozeCount': 'அதிகபட்ச தூங்கு எண்ணிக்கை',
      'minutes': 'நிமிடங்கள்',
      'save': 'சேமி',
      'alarmSaved': 'எச்சரிக்கை வெற்றிகரமாக சேமிக்கப்பட்டது',
      'errorSavingAlarm': 'எச்சரிக்கை சேமிப்பதில் பிழை',

      // Common
      'cancel': 'ரத்து',
      'ok': 'சரி',
      'error': 'பிழை',
      'success': 'வெற்றி',
      'loading': 'ஏற்றுகிறது...',
      'retry': 'மீண்டும் முயற்சி',
      'noData': 'தரவு இல்லை',
      'noInternet': 'இணைய இணைப்பு இல்லை',
      'serverError': 'சேவையக பிழை ஏற்பட்டது',
      'tryAgain': 'மீண்டும் முயற்சி',
    },
    'te': {
      // Alarm Screen
      'defaultAlarm': 'డిఫాల్ట్ అలారం',
      'time': 'సమయం',
      'tone': 'టోన్',
      'volume': 'వాల్యూమ్',
      'vibration': 'వైబ్రేషన్',
      'snoozeSettings': 'స్నూజ్ సెట్టింగ్స్',
      'snoozeDuration': 'స్నూజ్ వ్యవధి',
      'maxSnoozeCount': 'గరిష్ట స్నూజ్ సంఖ్య',
      'minutes': 'నిమిషాలు',
      'save': 'సేవ్',
      'alarmSaved': 'అలారం విజయవంతంగా సేవ్ చేయబడింది',
      'errorSavingAlarm': 'అలారం సేవ్ చేయడంలో లోపం',

      // Common
      'cancel': 'రద్దు',
      'ok': 'సరే',
      'error': 'లోపం',
      'success': 'విజయం',
      'loading': 'లోడ్ అవుతోంది...',
      'retry': 'మళ్ళీ ప్రయత్నించండి',
      'noData': 'డేటా లేదు',
      'noInternet': 'ఇంటర్నెట్ కనెక్షన్ లేదు',
      'serverError': 'సర్వర్ లోపం సంభవించింది',
      'tryAgain': 'మళ్ళీ ప్రయత్నించండి',
    },
  };

  static String translate(String key, String languageCode) {
    return translations[languageCode]?[key] ?? translations['en']![key] ?? key;
  }
}

extension AppTranslationsX on BuildContext {
  String t(String key) {
    final locale = Localizations.localeOf(this);
    return AppTranslations.translate(key, locale.languageCode);
  }
}
