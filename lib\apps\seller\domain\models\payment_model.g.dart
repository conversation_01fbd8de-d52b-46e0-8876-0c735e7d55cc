// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PaymentSummary _$PaymentSummaryFromJson(Map<String, dynamic> json) =>
    _PaymentSummary(
      totalBalance: (json['totalBalance'] as num).toDouble(),
      pendingAmount: (json['pendingAmount'] as num).toDouble(),
      availableAmount: (json['availableAmount'] as num).toDouble(),
    );

Map<String, dynamic> _$PaymentSummaryToJson(_PaymentSummary instance) =>
    <String, dynamic>{
      'totalBalance': instance.totalBalance,
      'pendingAmount': instance.pendingAmount,
      'availableAmount': instance.availableAmount,
    };

_TransactionModel _$TransactionModelFromJson(Map<String, dynamic> json) =>
    _TransactionModel(
      id: json['id'] as String,
      description: json['description'] as String,
      amount: (json['amount'] as num).toDouble(),
      date: DateTime.parse(json['date'] as String),
      isCredit: json['isCredit'] as bool,
      orderId: json['orderId'] as String?,
      settlementId: json['settlementId'] as String?,
    );

Map<String, dynamic> _$TransactionModelToJson(_TransactionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'description': instance.description,
      'amount': instance.amount,
      'date': instance.date.toIso8601String(),
      'isCredit': instance.isCredit,
      'orderId': instance.orderId,
      'settlementId': instance.settlementId,
    };

_SettlementModel _$SettlementModelFromJson(Map<String, dynamic> json) =>
    _SettlementModel(
      id: json['id'] as String,
      amount: (json['amount'] as num).toDouble(),
      date: DateTime.parse(json['date'] as String),
      status: json['status'] as String,
      bankAccountId: json['bankAccountId'] as String?,
      transactionId: json['transactionId'] as String?,
      failureReason: json['failureReason'] as String?,
    );

Map<String, dynamic> _$SettlementModelToJson(_SettlementModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'amount': instance.amount,
      'date': instance.date.toIso8601String(),
      'status': instance.status,
      'bankAccountId': instance.bankAccountId,
      'transactionId': instance.transactionId,
      'failureReason': instance.failureReason,
    };

_BankAccountModel _$BankAccountModelFromJson(Map<String, dynamic> json) =>
    _BankAccountModel(
      id: json['id'] as String,
      accountNumber: json['accountNumber'] as String,
      bankName: json['bankName'] as String,
      accountHolderName: json['accountHolderName'] as String,
      ifscCode: json['ifscCode'] as String,
      isVerified: json['isVerified'] as bool? ?? false,
      isPrimary: json['isPrimary'] as bool? ?? false,
      verifiedAt: json['verifiedAt'] == null
          ? null
          : DateTime.parse(json['verifiedAt'] as String),
    );

Map<String, dynamic> _$BankAccountModelToJson(_BankAccountModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'accountNumber': instance.accountNumber,
      'bankName': instance.bankName,
      'accountHolderName': instance.accountHolderName,
      'ifscCode': instance.ifscCode,
      'isVerified': instance.isVerified,
      'isPrimary': instance.isPrimary,
      'verifiedAt': instance.verifiedAt?.toIso8601String(),
    };
