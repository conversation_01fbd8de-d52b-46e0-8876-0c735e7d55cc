import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/buyer/providers/list_submission_order_provider.dart';
import 'package:shivish/apps/buyer/models/list_submission_order.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/utils/logger.dart';
import 'package:shivish/shared/utils/string_utils.dart';

class ListSubmissionOrderScreen extends ConsumerStatefulWidget {
  final String submissionId;
  final String buyerId;
  final String sellerId;
  final double totalAmount;
  final List<ListSubmissionOrderItem> items;

  const ListSubmissionOrderScreen({
    super.key,
    required this.submissionId,
    required this.buyerId,
    required this.sellerId,
    required this.totalAmount,
    required this.items,
  });

  @override
  ConsumerState<ListSubmissionOrderScreen> createState() =>
      _ListSubmissionOrderScreenState();
}

class _ListSubmissionOrderScreenState
    extends ConsumerState<ListSubmissionOrderScreen> {
  final _logger = getLogger('ListSubmissionOrderScreen');

  @override
  void initState() {
    super.initState();
    _createOrder();
  }

  Future<void> _createOrder() async {
    try {
      await ref.read(listSubmissionOrderProvider.notifier).createOrder(
            submissionId: widget.submissionId,
            buyerId: widget.buyerId,
            sellerId: widget.sellerId,
            totalAmount: widget.totalAmount,
            items: widget.items,
          );
    } catch (e, st) {
      _logger.severe('Error creating order', e, st);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create order: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(listSubmissionOrderProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Details'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => context.pop(),
        ),
      ),
      body: Stack(
        children: [
          if (state.error != null)
            Center(
              child: ErrorMessage(
                message: state.error!,
              ),
            )
          else if (state.order != null)
            _buildOrderDetails(state.order!)
          else
            const SizedBox.shrink(),
          if (state.isLoading) const LoadingIndicator(),
        ],
      ),
    );
  }

  Widget _buildOrderDetails(ListSubmissionOrder order) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildOrderHeader(order),
          const SizedBox(height: 24),
          _buildItemsList(order.items),
          const SizedBox(height: 24),
          _buildTotalAmount(order.totalAmount),
          const SizedBox(height: 32),
          _buildActionButtons(order),
        ],
      ),
    );
  }

  Widget _buildOrderHeader(ListSubmissionOrder order) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order #${StringUtils.safeSubstring(order.id, 0, 8)}',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Created on ${_formatDate(order.createdAt)}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: _getStatusColor(order.status).withAlpha(25),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                order.status.toUpperCase(),
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: _getStatusColor(order.status),
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsList(List<ListSubmissionOrderItem> items) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Items',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ),
          const Divider(height: 1),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: items.length,
            separatorBuilder: (_, __) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final item = items[index];
              return ListTile(
                title: Text(item.name),
                subtitle: item.notes != null
                    ? Text(
                        item.notes!,
                        style: Theme.of(context).textTheme.bodySmall,
                      )
                    : null,
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '${item.quantity}x',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '₹${item.price.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTotalAmount(double totalAmount) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Total Amount',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              '₹${totalAmount.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(ListSubmissionOrder order) {
    if (order.isPaid) {
      return ElevatedButton.icon(
        onPressed: () {
          // Navigate to order tracking screen
          context.pushNamed('order_tracking', pathParameters: {'id': order.id});
        },
        icon: const Icon(Icons.local_shipping),
        label: const Text('Track Order'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        FilledButton(
          onPressed: () {
            // Navigate to payment screen
            context.push('/buyer/orders/${order.id}/payment');
          },
          child: const Text('Proceed to Payment'),
        ),
        const SizedBox(height: 12),
        OutlinedButton(
          onPressed: () {
            // Cancel order
            _showCancelConfirmation(order);
          },
          child: const Text('Cancel Order'),
        ),
      ],
    );
  }

  Future<void> _showCancelConfirmation(ListSubmissionOrder order) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order'),
        content: const Text(
          'Are you sure you want to cancel this order? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => context.pop(false),
            child: const Text('No, Keep Order'),
          ),
          FilledButton(
            onPressed: () => context.pop(true),
            child: const Text('Yes, Cancel Order'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        await ref
            .read(listSubmissionOrderProvider.notifier)
            .updateOrderStatus(order.id, 'cancelled');
        if (mounted) {
          context.pop();
        }
      } catch (e, st) {
        _logger.severe('Error cancelling order', e, st);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to cancel order: ${e.toString()}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'paid':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'delivery_assigned':
        return Colors.blue;
      case 'delivered':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
