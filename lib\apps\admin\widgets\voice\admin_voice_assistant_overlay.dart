import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/providers/voice_assistant_provider.dart';
import '../../../../shared/providers/ai_assistant_provider.dart';
import '../../../../shared/services/voice/voice_assistant_service.dart';
import '../../../../shared/ui_components/voice/voice_assistant_indicator.dart';
import '../../../../shared/ui_components/voice/floating_3d_assistant.dart';
import '../../services/admin_voice_command_handler.dart';

/// An overlay widget that shows the voice assistant status and handles commands for admin app
class AdminVoiceAssistantOverlay extends ConsumerStatefulWidget {
  /// The child widget to display
  final Widget child;

  /// Creates an [AdminVoiceAssistantOverlay]
  const AdminVoiceAssistantOverlay({super.key, required this.child});

  @override
  ConsumerState<AdminVoiceAssistantOverlay> createState() =>
      _AdminVoiceAssistantOverlayState();
}

class _AdminVoiceAssistantOverlayState
    extends ConsumerState<AdminVoiceAssistantOverlay> {
  late VoiceAssistantService _voiceAssistant;
  AdminVoiceCommandHandler? _commandHandler;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _voiceAssistant = ref.read(voiceAssistantProvider);
    _initializeVoiceAssistant();
  }

  Future<void> _initializeVoiceAssistant() async {
    final initialized = await _voiceAssistant.initialize();
    if (initialized) {
      setState(() {
        _isInitialized = true;
      });

      // Start listening for wake words
      await _voiceAssistant.startListeningForWakeWord();

      // Listen for commands
      _voiceAssistant.commandStream.listen(_handleCommand);
    }
  }

  void _handleCommand(String command) {
    _commandHandler ??= AdminVoiceCommandHandler(context, _voiceAssistant);
    _commandHandler!.processCommand(command);
  }

  void _toggleVoiceAssistant() async {
    if (!_isInitialized) {
      await _initializeVoiceAssistant();
      return;
    }

    final state = _voiceAssistant.state;
    if (state == VoiceAssistantState.idle) {
      await _voiceAssistant.startListeningForWakeWord();
    } else {
      await _voiceAssistant.stopListening();
    }
  }

  @override
  Widget build(BuildContext context) {
    final floatEnabledAsync = ref.watch(aiAssistantFloatEnabledProvider);

    return Stack(
      children: [
        // Main content
        widget.child,

        // Voice assistant indicator
        Positioned(
          top: MediaQuery.of(context).padding.top + 8,
          right: 16,
          child: VoiceAssistantIndicator(
            showLabel: false,
            onTap: _toggleVoiceAssistant,
          ),
        ),

        // Floating 3D AI assistant (only shown if enabled) - ON TOP
        // No more static listening overlay - the 3D assistant will handle visual feedback
        if (floatEnabledAsync.value == true) const Floating3DAssistant(),
      ],
    );
  }

  @override
  void dispose() {
    _voiceAssistant.stopListening();
    super.dispose();
  }
}
