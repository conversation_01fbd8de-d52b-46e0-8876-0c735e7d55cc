// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$PaymentState {

 bool get isLoading; bool get isProcessing; List<PaymentModel> get payments; List<CardModel> get cards; PaymentModel? get selectedPayment; CardModel? get selectedCard; String? get error;
/// Create a copy of PaymentState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentStateCopyWith<PaymentState> get copyWith => _$PaymentStateCopyWithImpl<PaymentState>(this as PaymentState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isProcessing, isProcessing) || other.isProcessing == isProcessing)&&const DeepCollectionEquality().equals(other.payments, payments)&&const DeepCollectionEquality().equals(other.cards, cards)&&(identical(other.selectedPayment, selectedPayment) || other.selectedPayment == selectedPayment)&&(identical(other.selectedCard, selectedCard) || other.selectedCard == selectedCard)&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,isProcessing,const DeepCollectionEquality().hash(payments),const DeepCollectionEquality().hash(cards),selectedPayment,selectedCard,error);

@override
String toString() {
  return 'PaymentState(isLoading: $isLoading, isProcessing: $isProcessing, payments: $payments, cards: $cards, selectedPayment: $selectedPayment, selectedCard: $selectedCard, error: $error)';
}


}

/// @nodoc
abstract mixin class $PaymentStateCopyWith<$Res>  {
  factory $PaymentStateCopyWith(PaymentState value, $Res Function(PaymentState) _then) = _$PaymentStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, bool isProcessing, List<PaymentModel> payments, List<CardModel> cards, PaymentModel? selectedPayment, CardModel? selectedCard, String? error
});


$PaymentModelCopyWith<$Res>? get selectedPayment;$CardModelCopyWith<$Res>? get selectedCard;

}
/// @nodoc
class _$PaymentStateCopyWithImpl<$Res>
    implements $PaymentStateCopyWith<$Res> {
  _$PaymentStateCopyWithImpl(this._self, this._then);

  final PaymentState _self;
  final $Res Function(PaymentState) _then;

/// Create a copy of PaymentState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? isProcessing = null,Object? payments = null,Object? cards = null,Object? selectedPayment = freezed,Object? selectedCard = freezed,Object? error = freezed,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isProcessing: null == isProcessing ? _self.isProcessing : isProcessing // ignore: cast_nullable_to_non_nullable
as bool,payments: null == payments ? _self.payments : payments // ignore: cast_nullable_to_non_nullable
as List<PaymentModel>,cards: null == cards ? _self.cards : cards // ignore: cast_nullable_to_non_nullable
as List<CardModel>,selectedPayment: freezed == selectedPayment ? _self.selectedPayment : selectedPayment // ignore: cast_nullable_to_non_nullable
as PaymentModel?,selectedCard: freezed == selectedCard ? _self.selectedCard : selectedCard // ignore: cast_nullable_to_non_nullable
as CardModel?,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of PaymentState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentModelCopyWith<$Res>? get selectedPayment {
    if (_self.selectedPayment == null) {
    return null;
  }

  return $PaymentModelCopyWith<$Res>(_self.selectedPayment!, (value) {
    return _then(_self.copyWith(selectedPayment: value));
  });
}/// Create a copy of PaymentState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CardModelCopyWith<$Res>? get selectedCard {
    if (_self.selectedCard == null) {
    return null;
  }

  return $CardModelCopyWith<$Res>(_self.selectedCard!, (value) {
    return _then(_self.copyWith(selectedCard: value));
  });
}
}


/// Adds pattern-matching-related methods to [PaymentState].
extension PaymentStatePatterns on PaymentState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PaymentState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PaymentState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PaymentState value)  $default,){
final _that = this;
switch (_that) {
case _PaymentState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PaymentState value)?  $default,){
final _that = this;
switch (_that) {
case _PaymentState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isLoading,  bool isProcessing,  List<PaymentModel> payments,  List<CardModel> cards,  PaymentModel? selectedPayment,  CardModel? selectedCard,  String? error)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PaymentState() when $default != null:
return $default(_that.isLoading,_that.isProcessing,_that.payments,_that.cards,_that.selectedPayment,_that.selectedCard,_that.error);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isLoading,  bool isProcessing,  List<PaymentModel> payments,  List<CardModel> cards,  PaymentModel? selectedPayment,  CardModel? selectedCard,  String? error)  $default,) {final _that = this;
switch (_that) {
case _PaymentState():
return $default(_that.isLoading,_that.isProcessing,_that.payments,_that.cards,_that.selectedPayment,_that.selectedCard,_that.error);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isLoading,  bool isProcessing,  List<PaymentModel> payments,  List<CardModel> cards,  PaymentModel? selectedPayment,  CardModel? selectedCard,  String? error)?  $default,) {final _that = this;
switch (_that) {
case _PaymentState() when $default != null:
return $default(_that.isLoading,_that.isProcessing,_that.payments,_that.cards,_that.selectedPayment,_that.selectedCard,_that.error);case _:
  return null;

}
}

}

/// @nodoc


class _PaymentState implements PaymentState {
  const _PaymentState({this.isLoading = false, this.isProcessing = false, final  List<PaymentModel> payments = const [], final  List<CardModel> cards = const [], this.selectedPayment, this.selectedCard, this.error}): _payments = payments,_cards = cards;
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool isProcessing;
 final  List<PaymentModel> _payments;
@override@JsonKey() List<PaymentModel> get payments {
  if (_payments is EqualUnmodifiableListView) return _payments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_payments);
}

 final  List<CardModel> _cards;
@override@JsonKey() List<CardModel> get cards {
  if (_cards is EqualUnmodifiableListView) return _cards;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_cards);
}

@override final  PaymentModel? selectedPayment;
@override final  CardModel? selectedCard;
@override final  String? error;

/// Create a copy of PaymentState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentStateCopyWith<_PaymentState> get copyWith => __$PaymentStateCopyWithImpl<_PaymentState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.isProcessing, isProcessing) || other.isProcessing == isProcessing)&&const DeepCollectionEquality().equals(other._payments, _payments)&&const DeepCollectionEquality().equals(other._cards, _cards)&&(identical(other.selectedPayment, selectedPayment) || other.selectedPayment == selectedPayment)&&(identical(other.selectedCard, selectedCard) || other.selectedCard == selectedCard)&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,isProcessing,const DeepCollectionEquality().hash(_payments),const DeepCollectionEquality().hash(_cards),selectedPayment,selectedCard,error);

@override
String toString() {
  return 'PaymentState(isLoading: $isLoading, isProcessing: $isProcessing, payments: $payments, cards: $cards, selectedPayment: $selectedPayment, selectedCard: $selectedCard, error: $error)';
}


}

/// @nodoc
abstract mixin class _$PaymentStateCopyWith<$Res> implements $PaymentStateCopyWith<$Res> {
  factory _$PaymentStateCopyWith(_PaymentState value, $Res Function(_PaymentState) _then) = __$PaymentStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, bool isProcessing, List<PaymentModel> payments, List<CardModel> cards, PaymentModel? selectedPayment, CardModel? selectedCard, String? error
});


@override $PaymentModelCopyWith<$Res>? get selectedPayment;@override $CardModelCopyWith<$Res>? get selectedCard;

}
/// @nodoc
class __$PaymentStateCopyWithImpl<$Res>
    implements _$PaymentStateCopyWith<$Res> {
  __$PaymentStateCopyWithImpl(this._self, this._then);

  final _PaymentState _self;
  final $Res Function(_PaymentState) _then;

/// Create a copy of PaymentState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? isProcessing = null,Object? payments = null,Object? cards = null,Object? selectedPayment = freezed,Object? selectedCard = freezed,Object? error = freezed,}) {
  return _then(_PaymentState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,isProcessing: null == isProcessing ? _self.isProcessing : isProcessing // ignore: cast_nullable_to_non_nullable
as bool,payments: null == payments ? _self._payments : payments // ignore: cast_nullable_to_non_nullable
as List<PaymentModel>,cards: null == cards ? _self._cards : cards // ignore: cast_nullable_to_non_nullable
as List<CardModel>,selectedPayment: freezed == selectedPayment ? _self.selectedPayment : selectedPayment // ignore: cast_nullable_to_non_nullable
as PaymentModel?,selectedCard: freezed == selectedCard ? _self.selectedCard : selectedCard // ignore: cast_nullable_to_non_nullable
as CardModel?,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of PaymentState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentModelCopyWith<$Res>? get selectedPayment {
    if (_self.selectedPayment == null) {
    return null;
  }

  return $PaymentModelCopyWith<$Res>(_self.selectedPayment!, (value) {
    return _then(_self.copyWith(selectedPayment: value));
  });
}/// Create a copy of PaymentState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CardModelCopyWith<$Res>? get selectedCard {
    if (_self.selectedCard == null) {
    return null;
  }

  return $CardModelCopyWith<$Res>(_self.selectedCard!, (value) {
    return _then(_self.copyWith(selectedCard: value));
  });
}
}

// dart format on
