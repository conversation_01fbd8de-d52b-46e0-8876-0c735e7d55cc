package com.shivish

import android.app.Application
import android.content.Context
import android.speech.tts.TextToSpeech
import android.util.Log
import com.shivish.utils.OpenGLESUtils
import java.util.Locale

class MainApplication : Application(), TextToSpeech.OnInitListener {
    private val TAG = "MainApplication"
    private var textToSpeech: TextToSpeech? = null

    override fun onCreate() {
        super.onCreate()

        // Skip OpenGL ES initialization at startup
        // This avoids potential issues with unimplemented OpenGL ES APIs
        // OpenGL ES will be initialized by Flutter when needed

        // Initialize TTS with error handling
        initializeTTS()
    }

    private fun initializeOpenGLES() {
        try {
            // Just check OpenGL ES support without creating a surface
            // This avoids potential issues with unimplemented OpenGL ES APIs
            val supported = OpenGLESUtils.isOpenGLES2Supported(this)
            Log.d(TAG, "OpenGL ES 2.0 supported: $supported")

            // Log OpenGL ES features support
            val gles20Supported = OpenGLESUtils.isFeatureSupported("GLES20")
            val gles30Supported = OpenGLESUtils.isFeatureSupported("GLES30")
            Log.d(TAG, "GLES20 supported: $gles20Supported, GLES30 supported: $gles30Supported")

            // Skip creating a GLSurfaceView to avoid potential issues
        } catch (e: Exception) {
            // Log the error but don't crash
            Log.e(TAG, "Error checking OpenGL ES support: ${e.message}")
        }
    }

    private fun initializeTTS() {
        try {
            // Initialize TTS with error handling
            textToSpeech = TextToSpeech(this, this)
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing TTS: ${e.message}")
        }
    }

    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            try {
                // Set language to English
                val result = textToSpeech?.setLanguage(Locale.US)
                if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                    Log.e(TAG, "Language not supported by TTS")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error setting TTS language: ${e.message}")
            }
        } else {
            Log.e(TAG, "TTS initialization failed with status: $status")
        }
    }

    override fun onTerminate() {
        try {
            // Shutdown TTS
            textToSpeech?.stop()
            textToSpeech?.shutdown()
        } catch (e: Exception) {
            Log.e(TAG, "Error shutting down TTS: ${e.message}")
        }

        super.onTerminate()
    }

    /**
     * Safely speak text using TTS with error handling
     */
    fun speakText(text: String) {
        try {
            if (textToSpeech != null) {
                textToSpeech?.speak(text, TextToSpeech.QUEUE_FLUSH, null, "utteranceId")
            } else {
                Log.w(TAG, "TTS not initialized, cannot speak text")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error speaking text: ${e.message}")
        }
    }
}