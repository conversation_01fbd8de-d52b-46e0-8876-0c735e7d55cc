import 'package:flutter/foundation.dart';
import 'package:shivish/shared/core/error/exceptions.dart';
import 'package:shivish/shared/core/error/failures.dart';

/// A utility class for handling errors throughout the application
class ErrorHandler {
  /// Handles errors and logs them appropriately
  static void handleError(
    dynamic error,
    String message, {
    bool showError = false,
    StackTrace? stackTrace,
  }) {
    // Log the error
    debugPrint('Error: $message');
    debugPrint('Details: $error');
    if (stackTrace != null) {
      debugPrint('StackTrace: $stackTrace');
    }

    // Convert exceptions to failures if needed
    if (error is Exception) {
      _convertExceptionToFailure(error);
    }

    // Show error to user if needed
    if (showError) {
      // This would typically show a snackbar or dialog
      // Implementation depends on the app's UI framework
    }
  }

  /// Converts exceptions to failures for consistent error handling
  static Failure _convertExceptionToFailure(Exception exception) {
    if (exception is ServerException) {
      return const ServerFailure(message: 'Server error occurred');
    } else if (exception is NotFoundException) {
      return const NotFoundFailure(message: 'Resource not found');
    } else if (exception is UnauthorizedException) {
      return const UnauthorizedFailure(message: 'Unauthorized access');
    } else if (exception is ValidationException) {
      return ValidationFailure(message: exception.message);
    } else if (exception is NetworkException) {
      return NetworkFailure(message: exception.message);
    } else if (exception is CacheException) {
      return CacheFailure(message: exception.message);
    } else {
      return ServerFailure(message: exception.toString());
    }
  }
}
