import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/shared/models/order/order_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/order_cubit.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';

class ReturnRequestDialog extends StatefulWidget {
  final OrderModel order;

  const ReturnRequestDialog({
    super.key,
    required this.order,
  });

  @override
  State<ReturnRequestDialog> createState() => _ReturnRequestDialogState();
}

class _ReturnRequestDialogState extends State<ReturnRequestDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _reasonController;
  late TextEditingController _amountController;
  String _selectedReason = 'Damaged Product';

  final _reasons = [
    'Damaged Product',
    'Wrong Item',
    'Size/Fit Issue',
    'Quality Issue',
    'Not as Described',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _reasonController = TextEditingController();
    _amountController = TextEditingController(
      text: widget.order.total.toString(),
    );
  }

  @override
  void dispose() {
    _reasonController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Process Return',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedReason,
                  decoration: const InputDecoration(
                    labelText: 'Return Reason',
                    border: OutlineInputBorder(),
                  ),
                  items: _reasons.map((reason) {
                    return DropdownMenuItem(
                      value: reason,
                      child: Text(reason),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedReason = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                if (_selectedReason == 'Other')
                  AppTextField(
                    controller: _reasonController,
                    label: 'Detailed Reason',
                    maxLines: 3,
                    validator: (value) {
                      if (_selectedReason == 'Other' &&
                          (value == null || value.isEmpty)) {
                        return 'Please provide a detailed reason';
                      }
                      return null;
                    },
                  ),
                if (_selectedReason == 'Other') const SizedBox(height: 16),
                AppTextField(
                  controller: _amountController,
                  label: 'Refund Amount',
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter refund amount';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null) {
                      return 'Please enter a valid amount';
                    }
                    if (amount <= 0) {
                      return 'Amount must be greater than 0';
                    }
                    if (amount > widget.order.total) {
                      return 'Amount cannot exceed order total';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 8),
                Text(
                  'Order Total: ${CurrencyFormatter.format(widget.order.total)}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _submit,
                      child: const Text('Process Return'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _submit() {
    if (_formKey.currentState?.validate() ?? false) {
      final reason =
          _selectedReason == 'Other' ? _reasonController.text : _selectedReason;
      final amount = double.parse(_amountController.text);

      context.read<OrderCubit>().processRefund(
            orderId: widget.order.id,
            amount: amount,
            reason: reason,
          );
      Navigator.of(context).pop();
    }
  }
}
