// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'api_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ApiResponse<T> _$ApiResponseFromJson<T>(
    Map<String, dynamic> json, T Function(Object?) fromJsonT) {
  return _ApiResponse<T>.fromJson(json, fromJsonT);
}

/// @nodoc
mixin _$ApiResponse<T> {
  T? get data => throw _privateConstructorUsedError;
  bool get isSuccess => throw _privateConstructorUsedError;
  ApiError? get error => throw _privateConstructorUsedError;
  ApiMetadata? get metadata => throw _privateConstructorUsedError;
  ApiStatus get status => throw _privateConstructorUsedError;
  PaginationInfo? get paginationInfo => throw _privateConstructorUsedError;
  RequestInfo? get requestInfo => throw _privateConstructorUsedError;

  /// Serializes this ApiResponse to a JSON map.
  Map<String, dynamic> toJson(Object? Function(T) toJsonT) =>
      throw _privateConstructorUsedError;

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ApiResponseCopyWith<T, ApiResponse<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApiResponseCopyWith<T, $Res> {
  factory $ApiResponseCopyWith(
          ApiResponse<T> value, $Res Function(ApiResponse<T>) then) =
      _$ApiResponseCopyWithImpl<T, $Res, ApiResponse<T>>;
  @useResult
  $Res call(
      {T? data,
      bool isSuccess,
      ApiError? error,
      ApiMetadata? metadata,
      ApiStatus status,
      PaginationInfo? paginationInfo,
      RequestInfo? requestInfo});

  $ApiErrorCopyWith<$Res>? get error;
  $ApiMetadataCopyWith<$Res>? get metadata;
  $PaginationInfoCopyWith<$Res>? get paginationInfo;
  $RequestInfoCopyWith<$Res>? get requestInfo;
}

/// @nodoc
class _$ApiResponseCopyWithImpl<T, $Res, $Val extends ApiResponse<T>>
    implements $ApiResponseCopyWith<T, $Res> {
  _$ApiResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
    Object? isSuccess = null,
    Object? error = freezed,
    Object? metadata = freezed,
    Object? status = null,
    Object? paginationInfo = freezed,
    Object? requestInfo = freezed,
  }) {
    return _then(_value.copyWith(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as T?,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as ApiError?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as ApiMetadata?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ApiStatus,
      paginationInfo: freezed == paginationInfo
          ? _value.paginationInfo
          : paginationInfo // ignore: cast_nullable_to_non_nullable
              as PaginationInfo?,
      requestInfo: freezed == requestInfo
          ? _value.requestInfo
          : requestInfo // ignore: cast_nullable_to_non_nullable
              as RequestInfo?,
    ) as $Val);
  }

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ApiErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $ApiErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ApiMetadataCopyWith<$Res>? get metadata {
    if (_value.metadata == null) {
      return null;
    }

    return $ApiMetadataCopyWith<$Res>(_value.metadata!, (value) {
      return _then(_value.copyWith(metadata: value) as $Val);
    });
  }

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaginationInfoCopyWith<$Res>? get paginationInfo {
    if (_value.paginationInfo == null) {
      return null;
    }

    return $PaginationInfoCopyWith<$Res>(_value.paginationInfo!, (value) {
      return _then(_value.copyWith(paginationInfo: value) as $Val);
    });
  }

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RequestInfoCopyWith<$Res>? get requestInfo {
    if (_value.requestInfo == null) {
      return null;
    }

    return $RequestInfoCopyWith<$Res>(_value.requestInfo!, (value) {
      return _then(_value.copyWith(requestInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ApiResponseImplCopyWith<T, $Res>
    implements $ApiResponseCopyWith<T, $Res> {
  factory _$$ApiResponseImplCopyWith(_$ApiResponseImpl<T> value,
          $Res Function(_$ApiResponseImpl<T>) then) =
      __$$ApiResponseImplCopyWithImpl<T, $Res>;
  @override
  @useResult
  $Res call(
      {T? data,
      bool isSuccess,
      ApiError? error,
      ApiMetadata? metadata,
      ApiStatus status,
      PaginationInfo? paginationInfo,
      RequestInfo? requestInfo});

  @override
  $ApiErrorCopyWith<$Res>? get error;
  @override
  $ApiMetadataCopyWith<$Res>? get metadata;
  @override
  $PaginationInfoCopyWith<$Res>? get paginationInfo;
  @override
  $RequestInfoCopyWith<$Res>? get requestInfo;
}

/// @nodoc
class __$$ApiResponseImplCopyWithImpl<T, $Res>
    extends _$ApiResponseCopyWithImpl<T, $Res, _$ApiResponseImpl<T>>
    implements _$$ApiResponseImplCopyWith<T, $Res> {
  __$$ApiResponseImplCopyWithImpl(
      _$ApiResponseImpl<T> _value, $Res Function(_$ApiResponseImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
    Object? isSuccess = null,
    Object? error = freezed,
    Object? metadata = freezed,
    Object? status = null,
    Object? paginationInfo = freezed,
    Object? requestInfo = freezed,
  }) {
    return _then(_$ApiResponseImpl<T>(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as T?,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as ApiError?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as ApiMetadata?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ApiStatus,
      paginationInfo: freezed == paginationInfo
          ? _value.paginationInfo
          : paginationInfo // ignore: cast_nullable_to_non_nullable
              as PaginationInfo?,
      requestInfo: freezed == requestInfo
          ? _value.requestInfo
          : requestInfo // ignore: cast_nullable_to_non_nullable
              as RequestInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable(genericArgumentFactories: true)
class _$ApiResponseImpl<T> implements _ApiResponse<T> {
  const _$ApiResponseImpl(
      {this.data,
      this.isSuccess = false,
      this.error,
      this.metadata,
      this.status = ApiStatus.unknown,
      this.paginationInfo,
      this.requestInfo});

  factory _$ApiResponseImpl.fromJson(
          Map<String, dynamic> json, T Function(Object?) fromJsonT) =>
      _$$ApiResponseImplFromJson(json, fromJsonT);

  @override
  final T? data;
  @override
  @JsonKey()
  final bool isSuccess;
  @override
  final ApiError? error;
  @override
  final ApiMetadata? metadata;
  @override
  @JsonKey()
  final ApiStatus status;
  @override
  final PaginationInfo? paginationInfo;
  @override
  final RequestInfo? requestInfo;

  @override
  String toString() {
    return 'ApiResponse<$T>(data: $data, isSuccess: $isSuccess, error: $error, metadata: $metadata, status: $status, paginationInfo: $paginationInfo, requestInfo: $requestInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApiResponseImpl<T> &&
            const DeepCollectionEquality().equals(other.data, data) &&
            (identical(other.isSuccess, isSuccess) ||
                other.isSuccess == isSuccess) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.paginationInfo, paginationInfo) ||
                other.paginationInfo == paginationInfo) &&
            (identical(other.requestInfo, requestInfo) ||
                other.requestInfo == requestInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(data),
      isSuccess,
      error,
      metadata,
      status,
      paginationInfo,
      requestInfo);

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApiResponseImplCopyWith<T, _$ApiResponseImpl<T>> get copyWith =>
      __$$ApiResponseImplCopyWithImpl<T, _$ApiResponseImpl<T>>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson(Object? Function(T) toJsonT) {
    return _$$ApiResponseImplToJson<T>(this, toJsonT);
  }
}

abstract class _ApiResponse<T> implements ApiResponse<T> {
  const factory _ApiResponse(
      {final T? data,
      final bool isSuccess,
      final ApiError? error,
      final ApiMetadata? metadata,
      final ApiStatus status,
      final PaginationInfo? paginationInfo,
      final RequestInfo? requestInfo}) = _$ApiResponseImpl<T>;

  factory _ApiResponse.fromJson(
          Map<String, dynamic> json, T Function(Object?) fromJsonT) =
      _$ApiResponseImpl<T>.fromJson;

  @override
  T? get data;
  @override
  bool get isSuccess;
  @override
  ApiError? get error;
  @override
  ApiMetadata? get metadata;
  @override
  ApiStatus get status;
  @override
  PaginationInfo? get paginationInfo;
  @override
  RequestInfo? get requestInfo;

  /// Create a copy of ApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApiResponseImplCopyWith<T, _$ApiResponseImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

ApiMetadata _$ApiMetadataFromJson(Map<String, dynamic> json) {
  return _ApiMetadata.fromJson(json);
}

/// @nodoc
mixin _$ApiMetadata {
  String get timestamp => throw _privateConstructorUsedError;
  String get version => throw _privateConstructorUsedError;
  String get environment => throw _privateConstructorUsedError;
  Map<String, dynamic>? get additionalData =>
      throw _privateConstructorUsedError;

  /// Serializes this ApiMetadata to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ApiMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ApiMetadataCopyWith<ApiMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApiMetadataCopyWith<$Res> {
  factory $ApiMetadataCopyWith(
          ApiMetadata value, $Res Function(ApiMetadata) then) =
      _$ApiMetadataCopyWithImpl<$Res, ApiMetadata>;
  @useResult
  $Res call(
      {String timestamp,
      String version,
      String environment,
      Map<String, dynamic>? additionalData});
}

/// @nodoc
class _$ApiMetadataCopyWithImpl<$Res, $Val extends ApiMetadata>
    implements $ApiMetadataCopyWith<$Res> {
  _$ApiMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ApiMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? version = null,
    Object? environment = null,
    Object? additionalData = freezed,
  }) {
    return _then(_value.copyWith(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
      environment: null == environment
          ? _value.environment
          : environment // ignore: cast_nullable_to_non_nullable
              as String,
      additionalData: freezed == additionalData
          ? _value.additionalData
          : additionalData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ApiMetadataImplCopyWith<$Res>
    implements $ApiMetadataCopyWith<$Res> {
  factory _$$ApiMetadataImplCopyWith(
          _$ApiMetadataImpl value, $Res Function(_$ApiMetadataImpl) then) =
      __$$ApiMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String timestamp,
      String version,
      String environment,
      Map<String, dynamic>? additionalData});
}

/// @nodoc
class __$$ApiMetadataImplCopyWithImpl<$Res>
    extends _$ApiMetadataCopyWithImpl<$Res, _$ApiMetadataImpl>
    implements _$$ApiMetadataImplCopyWith<$Res> {
  __$$ApiMetadataImplCopyWithImpl(
      _$ApiMetadataImpl _value, $Res Function(_$ApiMetadataImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? version = null,
    Object? environment = null,
    Object? additionalData = freezed,
  }) {
    return _then(_$ApiMetadataImpl(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
      environment: null == environment
          ? _value.environment
          : environment // ignore: cast_nullable_to_non_nullable
              as String,
      additionalData: freezed == additionalData
          ? _value._additionalData
          : additionalData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ApiMetadataImpl implements _ApiMetadata {
  const _$ApiMetadataImpl(
      {this.timestamp = '',
      this.version = '',
      this.environment = '',
      final Map<String, dynamic>? additionalData})
      : _additionalData = additionalData;

  factory _$ApiMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ApiMetadataImplFromJson(json);

  @override
  @JsonKey()
  final String timestamp;
  @override
  @JsonKey()
  final String version;
  @override
  @JsonKey()
  final String environment;
  final Map<String, dynamic>? _additionalData;
  @override
  Map<String, dynamic>? get additionalData {
    final value = _additionalData;
    if (value == null) return null;
    if (_additionalData is EqualUnmodifiableMapView) return _additionalData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'ApiMetadata(timestamp: $timestamp, version: $version, environment: $environment, additionalData: $additionalData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApiMetadataImpl &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.environment, environment) ||
                other.environment == environment) &&
            const DeepCollectionEquality()
                .equals(other._additionalData, _additionalData));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, timestamp, version, environment,
      const DeepCollectionEquality().hash(_additionalData));

  /// Create a copy of ApiMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApiMetadataImplCopyWith<_$ApiMetadataImpl> get copyWith =>
      __$$ApiMetadataImplCopyWithImpl<_$ApiMetadataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ApiMetadataImplToJson(
      this,
    );
  }
}

abstract class _ApiMetadata implements ApiMetadata {
  const factory _ApiMetadata(
      {final String timestamp,
      final String version,
      final String environment,
      final Map<String, dynamic>? additionalData}) = _$ApiMetadataImpl;

  factory _ApiMetadata.fromJson(Map<String, dynamic> json) =
      _$ApiMetadataImpl.fromJson;

  @override
  String get timestamp;
  @override
  String get version;
  @override
  String get environment;
  @override
  Map<String, dynamic>? get additionalData;

  /// Create a copy of ApiMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApiMetadataImplCopyWith<_$ApiMetadataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PaginationInfo _$PaginationInfoFromJson(Map<String, dynamic> json) {
  return _PaginationInfo.fromJson(json);
}

/// @nodoc
mixin _$PaginationInfo {
  int get currentPage => throw _privateConstructorUsedError;
  int get totalPages => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  int get totalItems => throw _privateConstructorUsedError;
  bool get hasNextPage => throw _privateConstructorUsedError;
  bool get hasPreviousPage => throw _privateConstructorUsedError;

  /// Serializes this PaginationInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaginationInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaginationInfoCopyWith<PaginationInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaginationInfoCopyWith<$Res> {
  factory $PaginationInfoCopyWith(
          PaginationInfo value, $Res Function(PaginationInfo) then) =
      _$PaginationInfoCopyWithImpl<$Res, PaginationInfo>;
  @useResult
  $Res call(
      {int currentPage,
      int totalPages,
      int pageSize,
      int totalItems,
      bool hasNextPage,
      bool hasPreviousPage});
}

/// @nodoc
class _$PaginationInfoCopyWithImpl<$Res, $Val extends PaginationInfo>
    implements $PaginationInfoCopyWith<$Res> {
  _$PaginationInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaginationInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentPage = null,
    Object? totalPages = null,
    Object? pageSize = null,
    Object? totalItems = null,
    Object? hasNextPage = null,
    Object? hasPreviousPage = null,
  }) {
    return _then(_value.copyWith(
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      totalPages: null == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
      hasNextPage: null == hasNextPage
          ? _value.hasNextPage
          : hasNextPage // ignore: cast_nullable_to_non_nullable
              as bool,
      hasPreviousPage: null == hasPreviousPage
          ? _value.hasPreviousPage
          : hasPreviousPage // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PaginationInfoImplCopyWith<$Res>
    implements $PaginationInfoCopyWith<$Res> {
  factory _$$PaginationInfoImplCopyWith(_$PaginationInfoImpl value,
          $Res Function(_$PaginationInfoImpl) then) =
      __$$PaginationInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int currentPage,
      int totalPages,
      int pageSize,
      int totalItems,
      bool hasNextPage,
      bool hasPreviousPage});
}

/// @nodoc
class __$$PaginationInfoImplCopyWithImpl<$Res>
    extends _$PaginationInfoCopyWithImpl<$Res, _$PaginationInfoImpl>
    implements _$$PaginationInfoImplCopyWith<$Res> {
  __$$PaginationInfoImplCopyWithImpl(
      _$PaginationInfoImpl _value, $Res Function(_$PaginationInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaginationInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentPage = null,
    Object? totalPages = null,
    Object? pageSize = null,
    Object? totalItems = null,
    Object? hasNextPage = null,
    Object? hasPreviousPage = null,
  }) {
    return _then(_$PaginationInfoImpl(
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      totalPages: null == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
      hasNextPage: null == hasNextPage
          ? _value.hasNextPage
          : hasNextPage // ignore: cast_nullable_to_non_nullable
              as bool,
      hasPreviousPage: null == hasPreviousPage
          ? _value.hasPreviousPage
          : hasPreviousPage // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaginationInfoImpl implements _PaginationInfo {
  const _$PaginationInfoImpl(
      {this.currentPage = 0,
      this.totalPages = 0,
      this.pageSize = 0,
      this.totalItems = 0,
      this.hasNextPage = false,
      this.hasPreviousPage = false});

  factory _$PaginationInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaginationInfoImplFromJson(json);

  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final int totalPages;
  @override
  @JsonKey()
  final int pageSize;
  @override
  @JsonKey()
  final int totalItems;
  @override
  @JsonKey()
  final bool hasNextPage;
  @override
  @JsonKey()
  final bool hasPreviousPage;

  @override
  String toString() {
    return 'PaginationInfo(currentPage: $currentPage, totalPages: $totalPages, pageSize: $pageSize, totalItems: $totalItems, hasNextPage: $hasNextPage, hasPreviousPage: $hasPreviousPage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaginationInfoImpl &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems) &&
            (identical(other.hasNextPage, hasNextPage) ||
                other.hasNextPage == hasNextPage) &&
            (identical(other.hasPreviousPage, hasPreviousPage) ||
                other.hasPreviousPage == hasPreviousPage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, currentPage, totalPages,
      pageSize, totalItems, hasNextPage, hasPreviousPage);

  /// Create a copy of PaginationInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaginationInfoImplCopyWith<_$PaginationInfoImpl> get copyWith =>
      __$$PaginationInfoImplCopyWithImpl<_$PaginationInfoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaginationInfoImplToJson(
      this,
    );
  }
}

abstract class _PaginationInfo implements PaginationInfo {
  const factory _PaginationInfo(
      {final int currentPage,
      final int totalPages,
      final int pageSize,
      final int totalItems,
      final bool hasNextPage,
      final bool hasPreviousPage}) = _$PaginationInfoImpl;

  factory _PaginationInfo.fromJson(Map<String, dynamic> json) =
      _$PaginationInfoImpl.fromJson;

  @override
  int get currentPage;
  @override
  int get totalPages;
  @override
  int get pageSize;
  @override
  int get totalItems;
  @override
  bool get hasNextPage;
  @override
  bool get hasPreviousPage;

  /// Create a copy of PaginationInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaginationInfoImplCopyWith<_$PaginationInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RequestInfo _$RequestInfoFromJson(Map<String, dynamic> json) {
  return _RequestInfo.fromJson(json);
}

/// @nodoc
mixin _$RequestInfo {
  String get requestId => throw _privateConstructorUsedError;
  String get endpoint => throw _privateConstructorUsedError;
  String get method => throw _privateConstructorUsedError;
  int get statusCode => throw _privateConstructorUsedError;
  int get responseTime => throw _privateConstructorUsedError;
  Map<String, dynamic>? get headers => throw _privateConstructorUsedError;

  /// Serializes this RequestInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RequestInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RequestInfoCopyWith<RequestInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RequestInfoCopyWith<$Res> {
  factory $RequestInfoCopyWith(
          RequestInfo value, $Res Function(RequestInfo) then) =
      _$RequestInfoCopyWithImpl<$Res, RequestInfo>;
  @useResult
  $Res call(
      {String requestId,
      String endpoint,
      String method,
      int statusCode,
      int responseTime,
      Map<String, dynamic>? headers});
}

/// @nodoc
class _$RequestInfoCopyWithImpl<$Res, $Val extends RequestInfo>
    implements $RequestInfoCopyWith<$Res> {
  _$RequestInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RequestInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestId = null,
    Object? endpoint = null,
    Object? method = null,
    Object? statusCode = null,
    Object? responseTime = null,
    Object? headers = freezed,
  }) {
    return _then(_value.copyWith(
      requestId: null == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      endpoint: null == endpoint
          ? _value.endpoint
          : endpoint // ignore: cast_nullable_to_non_nullable
              as String,
      method: null == method
          ? _value.method
          : method // ignore: cast_nullable_to_non_nullable
              as String,
      statusCode: null == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int,
      responseTime: null == responseTime
          ? _value.responseTime
          : responseTime // ignore: cast_nullable_to_non_nullable
              as int,
      headers: freezed == headers
          ? _value.headers
          : headers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RequestInfoImplCopyWith<$Res>
    implements $RequestInfoCopyWith<$Res> {
  factory _$$RequestInfoImplCopyWith(
          _$RequestInfoImpl value, $Res Function(_$RequestInfoImpl) then) =
      __$$RequestInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String requestId,
      String endpoint,
      String method,
      int statusCode,
      int responseTime,
      Map<String, dynamic>? headers});
}

/// @nodoc
class __$$RequestInfoImplCopyWithImpl<$Res>
    extends _$RequestInfoCopyWithImpl<$Res, _$RequestInfoImpl>
    implements _$$RequestInfoImplCopyWith<$Res> {
  __$$RequestInfoImplCopyWithImpl(
      _$RequestInfoImpl _value, $Res Function(_$RequestInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of RequestInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestId = null,
    Object? endpoint = null,
    Object? method = null,
    Object? statusCode = null,
    Object? responseTime = null,
    Object? headers = freezed,
  }) {
    return _then(_$RequestInfoImpl(
      requestId: null == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      endpoint: null == endpoint
          ? _value.endpoint
          : endpoint // ignore: cast_nullable_to_non_nullable
              as String,
      method: null == method
          ? _value.method
          : method // ignore: cast_nullable_to_non_nullable
              as String,
      statusCode: null == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int,
      responseTime: null == responseTime
          ? _value.responseTime
          : responseTime // ignore: cast_nullable_to_non_nullable
              as int,
      headers: freezed == headers
          ? _value._headers
          : headers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RequestInfoImpl implements _RequestInfo {
  const _$RequestInfoImpl(
      {this.requestId = '',
      this.endpoint = '',
      this.method = '',
      this.statusCode = 0,
      this.responseTime = 0,
      final Map<String, dynamic>? headers})
      : _headers = headers;

  factory _$RequestInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$RequestInfoImplFromJson(json);

  @override
  @JsonKey()
  final String requestId;
  @override
  @JsonKey()
  final String endpoint;
  @override
  @JsonKey()
  final String method;
  @override
  @JsonKey()
  final int statusCode;
  @override
  @JsonKey()
  final int responseTime;
  final Map<String, dynamic>? _headers;
  @override
  Map<String, dynamic>? get headers {
    final value = _headers;
    if (value == null) return null;
    if (_headers is EqualUnmodifiableMapView) return _headers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'RequestInfo(requestId: $requestId, endpoint: $endpoint, method: $method, statusCode: $statusCode, responseTime: $responseTime, headers: $headers)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RequestInfoImpl &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.endpoint, endpoint) ||
                other.endpoint == endpoint) &&
            (identical(other.method, method) || other.method == method) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.responseTime, responseTime) ||
                other.responseTime == responseTime) &&
            const DeepCollectionEquality().equals(other._headers, _headers));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, requestId, endpoint, method,
      statusCode, responseTime, const DeepCollectionEquality().hash(_headers));

  /// Create a copy of RequestInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RequestInfoImplCopyWith<_$RequestInfoImpl> get copyWith =>
      __$$RequestInfoImplCopyWithImpl<_$RequestInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RequestInfoImplToJson(
      this,
    );
  }
}

abstract class _RequestInfo implements RequestInfo {
  const factory _RequestInfo(
      {final String requestId,
      final String endpoint,
      final String method,
      final int statusCode,
      final int responseTime,
      final Map<String, dynamic>? headers}) = _$RequestInfoImpl;

  factory _RequestInfo.fromJson(Map<String, dynamic> json) =
      _$RequestInfoImpl.fromJson;

  @override
  String get requestId;
  @override
  String get endpoint;
  @override
  String get method;
  @override
  int get statusCode;
  @override
  int get responseTime;
  @override
  Map<String, dynamic>? get headers;

  /// Create a copy of RequestInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RequestInfoImplCopyWith<_$RequestInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
