import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/notification/notification_model.dart';
import '../../../shared/models/notification/notification_type.dart';
import '../saviour_routes.dart';

/// Widget for displaying popup notifications in the saviour app
class SaviourNotificationPopup extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onDismiss;
  final VoidCallback? onTap;

  const SaviourNotificationPopup({
    super.key,
    required this.notification,
    this.onDismiss,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: InkWell(
          onTap: () {
            onTap?.call();
            _handleNotificationTap(context);
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getNotificationColor(notification.type),
                  child: Icon(
                    _getNotificationIcon(notification.type),
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        notification.title,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        notification.body,
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  onPressed: onDismiss,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleNotificationTap(BuildContext context) {
    switch (notification.type) {
      case NotificationType.deliveryRequest:
        if (notification.data['requestId'] != null) {
          context.go(SaviourRoutes.getActiveDeliveryRoute(notification.data['requestId']));
        } else {
          context.go(SaviourRoutes.deliveryRequests);
        }
        break;
      case NotificationType.statusUpdate:
        if (notification.data['deliveryId'] != null) {
          context.go(SaviourRoutes.getActiveDeliveryRoute(notification.data['deliveryId']));
        } else if (notification.data['rideId'] != null) {
          context.go('${SaviourRoutes.activeRide}/${notification.data['rideId']}');
        } else {
          context.go(SaviourRoutes.deliveryRequests);
        }
        break;
      case NotificationType.earnings:
        context.go(SaviourRoutes.earnings);
        break;
      case NotificationType.system:
        if (notification.data['action'] != null) {
          _handleSystemAction(context, notification.data['action']);
        } else {
          context.go(SaviourRoutes.settings);
        }
        break;
      case NotificationType.general:
        if (notification.data['category'] != null) {
          _handleGeneralNotification(context, notification.data['category']);
        } else {
          context.go(SaviourRoutes.notifications);
        }
        break;
      default:
        // Navigate to notifications screen for other types
        context.go(SaviourRoutes.notifications);
        break;
    }
  }

  void _handleSystemAction(BuildContext context, String action) {
    switch (action) {
      case 'profile_update':
        context.go(SaviourRoutes.profile);
        break;
      case 'app_update':
        // Handle app update notification
        break;
      case 'maintenance':
        // Handle maintenance notification
        break;
      case 'emergency':
        // Handle emergency notification
        break;
      default:
        context.go(SaviourRoutes.settings);
        break;
    }
  }

  void _handleGeneralNotification(BuildContext context, String category) {
    switch (category) {
      case 'new_request':
        context.go(SaviourRoutes.deliveryRequests);
        break;
      case 'delivery_update':
        context.go(SaviourRoutes.deliveryRequests);
        break;
      case 'ride_request':
        context.go(SaviourRoutes.rideRequests);
        break;
      case 'earnings_update':
        context.go(SaviourRoutes.earnings);
        break;
      default:
        context.go(SaviourRoutes.notifications);
        break;
    }
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.deliveryRequest:
        return Icons.local_shipping;
      case NotificationType.statusUpdate:
        return Icons.update;
      case NotificationType.earnings:
        return Icons.monetization_on;
      case NotificationType.system:
        return Icons.warning;
      case NotificationType.general:
        return Icons.info;
      default:
        return Icons.notifications;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.deliveryRequest:
        return Colors.brown;
      case NotificationType.statusUpdate:
        return Colors.cyan;
      case NotificationType.earnings:
        return Colors.amber;
      case NotificationType.system:
        return Colors.red;
      case NotificationType.general:
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}

/// Service for showing notification popups
class SaviourNotificationPopupService {
  static OverlayEntry? _currentOverlay;

  /// Show a notification popup
  static void showNotificationPopup(
    BuildContext context,
    NotificationModel notification, {
    Duration duration = const Duration(seconds: 4),
  }) {
    // Remove any existing popup
    hideNotificationPopup();

    final overlay = Overlay.of(context);

    _currentOverlay = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 16,
        left: 0,
        right: 0,
        child: SaviourNotificationPopup(
          notification: notification,
          onDismiss: hideNotificationPopup,
          onTap: hideNotificationPopup,
        ),
      ),
    );

    overlay.insert(_currentOverlay!);

    // Auto-hide after duration
    Future.delayed(duration, () {
      hideNotificationPopup();
    });
  }

  /// Hide the current notification popup
  static void hideNotificationPopup() {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }
}
