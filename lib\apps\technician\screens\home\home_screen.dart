import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/technician/widgets/technician_app_toolbar.dart';
import 'package:shivish/apps/technician/widgets/notification_badge.dart';
import 'package:shivish/apps/technician/technician_routes.dart';
import 'package:shivish/shared/ui_components/navigation/custom_bottom_nav.dart';
import 'package:shivish/shared/models/technician/technician.dart';
import 'package:shivish/shared/models/booking/booking_model.dart';
import 'package:shivish/apps/technician/providers/technician_provider.dart';
import 'package:shivish/apps/technician/providers/booking_provider.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final technicianState = ref.watch(technicianProvider);
    final technician = technicianState.value;
    final bookingState = technician != null
        ? ref.watch(technicianBookingsProvider(technician.id))
        : const AsyncValue<List<BookingModel>>.data([]);

    return Scaffold(
      appBar: TechnicianAppToolbar.simple(
        title: 'Technician Dashboard',
        // Home screen doesn't need a back button, but we'll keep it consistent
        fallbackRoute: TechnicianRoutes.home,
        actions: [
          const TechnicianNotificationIcon(),
          IconButton(
            icon: const Icon(Icons.person_outline),
            onPressed: () {
              debugPrint('HomeScreen: Navigating to profile');
              context.go(TechnicianRoutes.profileSettings);
            },
          ),
        ],
      ),
      body: technicianState.when(
        data: (technician) {
          if (technician == null) {
            return const Center(child: Text('No technician data available'));
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildWelcomeSection(context, technician),
                const SizedBox(height: 24),
                _buildQuickActions(context),
                const SizedBox(height: 24),
                _buildUpcomingJobs(context, bookingState),
                const SizedBox(height: 24),
                _buildPerformanceMetrics(context, technician),
              ],
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => Center(child: Text('Error: $error')),
      ),
      bottomNavigationBar: CustomBottomNav(
        currentIndex: 0,
        onTap: (index) {
          switch (index) {
            case 0:
              // Already on home
              break;
            case 1:
              debugPrint('HomeScreen: Navigating to bookings calendar');
              context.go(TechnicianRoutes.bookingCalendar);
              break;
            case 2:
              debugPrint('HomeScreen: Navigating to bookings');
              context.go(TechnicianRoutes.bookings);
              break;
            case 3:
              debugPrint('HomeScreen: Navigating to payments');
              context.go(TechnicianRoutes.payments);
              break;
            case 4:
              debugPrint('HomeScreen: Navigating to settings');
              context.go(TechnicianRoutes.settings);
              break;
          }
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today_outlined),
            activeIcon: Icon(Icons.calendar_today),
            label: 'Schedule',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.work_outline),
            activeIcon: Icon(Icons.work),
            label: 'Jobs',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.payments_outlined),
            activeIcon: Icon(Icons.payments),
            label: 'Payments',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings_outlined),
            activeIcon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context, Technician technician) {
    return Card(
      elevation: 4,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primaryContainer,
              Theme.of(
                context,
              ).colorScheme.primaryContainer.withValues(alpha: 0.7),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 35,
                  backgroundImage:
                      technician.profileImage != null &&
                          technician.profileImage!.isNotEmpty
                      ? NetworkImage(technician.profileImage!)
                      : null,
                  backgroundColor: Colors.blue.shade100,
                  child:
                      technician.profileImage == null ||
                          technician.profileImage!.isEmpty
                      ? Text(
                          technician.name.isNotEmpty
                              ? technician.name[0].toUpperCase()
                              : 'T',
                          style: const TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Good ${_getGreeting()}!',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        technician.name,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(color: Colors.grey[700]),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Ready to serve customers today?',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: technician.isVerified ? Colors.green : Colors.orange,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        technician.isVerified ? Icons.verified : Icons.pending,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        technician.isVerified ? 'Verified' : 'Pending',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: technician.isActive ? Colors.blue : Colors.grey,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        technician.isActive ? Icons.work : Icons.work_off,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        technician.isActive ? 'Active' : 'Inactive',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Quick Actions', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          children: [
            _buildActionCard(
              context: context,
              icon: Icons.calendar_today,
              title: 'Schedule',
              onTap: () {
                debugPrint('HomeScreen: Navigating to bookings calendar');
                context.go(TechnicianRoutes.bookingCalendar);
              },
            ),
            _buildActionCard(
              context: context,
              icon: Icons.work,
              title: 'Active Jobs',
              onTap: () {
                debugPrint('HomeScreen: Navigating to bookings');
                context.go(TechnicianRoutes.bookings);
              },
            ),
            _buildActionCard(
              context: context,
              icon: Icons.payments,
              title: 'Payments',
              onTap: () {
                debugPrint('HomeScreen: Navigating to payments');
                context.go(TechnicianRoutes.payments);
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpcomingJobs(
    BuildContext context,
    AsyncValue<List<BookingModel>> bookingState,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Upcoming Jobs', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 16),
        bookingState.when(
          data: (bookings) {
            if (bookings.isEmpty) {
              return const Center(child: Text('No upcoming jobs'));
            }
            return ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: bookings.length,
              itemBuilder: (context, index) {
                final booking = bookings[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.blue.shade50,
                      child: const Icon(Icons.work, color: Colors.blue),
                    ),
                    title: Text('Job #${booking.bookingNumber}'),
                    subtitle: Text(
                      '${booking.serviceLocation.contactName} - ${booking.bookingDate.toString().split(' ')[0]}',
                    ),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      context.go(
                        '${TechnicianRoutes.bookingDetails}/${booking.id}',
                      );
                    },
                  ),
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(child: Text('Error: $error')),
        ),
      ],
    );
  }

  Widget _buildPerformanceMetrics(BuildContext context, Technician technician) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Overview',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Rating',
                    technician.rating.toStringAsFixed(1),
                    Icons.star,
                    Colors.amber,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Jobs Done',
                    technician.totalReviews.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Experience',
                    '${technician.experienceYears} years',
                    Icons.work_history,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Specializations',
                    technician.specializations.length.toString(),
                    Icons.build,
                    Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.trending_up, color: Colors.green, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Monthly Performance',
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.green[700],
                              ),
                        ),
                        Text(
                          'Great job! You\'re performing above average',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.green[600]),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'Morning';
    if (hour < 17) return 'Afternoon';
    return 'Evening';
  }
}
