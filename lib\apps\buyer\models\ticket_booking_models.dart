import 'package:json_annotation/json_annotation.dart';

part 'ticket_booking_models.g.dart';

// COMMON MODELS

@JsonSerializable()
class BookingResponse {
  final String bookingId;
  final String status;
  final double totalAmount;
  final String currency;
  final DateTime bookingDate;
  final Map<String, dynamic>? additionalInfo;

  BookingResponse({
    required this.bookingId,
    required this.status,
    required this.totalAmount,
    required this.currency,
    required this.bookingDate,
    this.additionalInfo,
  });

  factory BookingResponse.fromJson(Map<String, dynamic> json) =>
      _$BookingResponseFromJson(json);
  Map<String, dynamic> toJson() => _$BookingResponseToJson(this);
}

@JsonSerializable()
class BookingStatus {
  final String bookingId;
  final String status;
  final String? pnr;
  final DateTime? confirmedAt;
  final Map<String, dynamic>? details;

  BookingStatus({
    required this.bookingId,
    required this.status,
    this.pnr,
    this.confirmedAt,
    this.details,
  });

  factory BookingStatus.fromJson(Map<String, dynamic> json) =>
      _$BookingStatusFromJson(json);
  Map<String, dynamic> toJson() => _$BookingStatusToJson(this);
}

@JsonSerializable()
class CancellationResponse {
  final String bookingId;
  final bool cancelled;
  final double refundAmount;
  final String refundStatus;
  final DateTime? refundDate;

  CancellationResponse({
    required this.bookingId,
    required this.cancelled,
    required this.refundAmount,
    required this.refundStatus,
    this.refundDate,
  });

  factory CancellationResponse.fromJson(Map<String, dynamic> json) =>
      _$CancellationResponseFromJson(json);
  Map<String, dynamic> toJson() => _$CancellationResponseToJson(this);
}

@JsonSerializable()
class Passenger {
  final String firstName;
  final String lastName;
  final String gender;
  final DateTime dateOfBirth;
  final String? passportNumber;
  final String? nationality;

  Passenger({
    required this.firstName,
    required this.lastName,
    required this.gender,
    required this.dateOfBirth,
    this.passportNumber,
    this.nationality,
  });

  factory Passenger.fromJson(Map<String, dynamic> json) =>
      _$PassengerFromJson(json);
  Map<String, dynamic> toJson() => _$PassengerToJson(this);
}

// FLIGHT MODELS

@JsonSerializable()
class FlightSearchRequest {
  final String origin;
  final String destination;
  final DateTime departureDate;
  final DateTime? returnDate;
  final int adults;
  final int children;
  final int infants;
  final String classType;
  final bool isRoundTrip;

  FlightSearchRequest({
    required this.origin,
    required this.destination,
    required this.departureDate,
    this.returnDate,
    required this.adults,
    this.children = 0,
    this.infants = 0,
    this.classType = 'Economy',
    this.isRoundTrip = false,
  });

  factory FlightSearchRequest.fromJson(Map<String, dynamic> json) =>
      _$FlightSearchRequestFromJson(json);
  Map<String, dynamic> toJson() => _$FlightSearchRequestToJson(this);
}

@JsonSerializable()
class FlightSearchResponse {
  final List<Flight> flights;
  final int totalResults;
  final String searchId;

  FlightSearchResponse({
    required this.flights,
    required this.totalResults,
    required this.searchId,
  });

  factory FlightSearchResponse.fromJson(Map<String, dynamic> json) =>
      _$FlightSearchResponseFromJson(json);
  Map<String, dynamic> toJson() => _$FlightSearchResponseToJson(this);
}

@JsonSerializable()
class Flight {
  final String flightId;
  final String airline;
  final String flightNumber;
  final String origin;
  final String destination;
  final DateTime departureTime;
  final DateTime arrivalTime;
  final String duration;
  final double price;
  final String currency;
  final int availableSeats;
  final String aircraftType;

  Flight({
    required this.flightId,
    required this.airline,
    required this.flightNumber,
    required this.origin,
    required this.destination,
    required this.departureTime,
    required this.arrivalTime,
    required this.duration,
    required this.price,
    required this.currency,
    required this.availableSeats,
    required this.aircraftType,
  });

  factory Flight.fromJson(Map<String, dynamic> json) => _$FlightFromJson(json);
  Map<String, dynamic> toJson() => _$FlightToJson(this);
}

@JsonSerializable()
class FlightDetails {
  final Flight flight;
  final List<String> amenities;
  final Map<String, dynamic> baggage;
  final List<String> cancellationPolicy;

  FlightDetails({
    required this.flight,
    required this.amenities,
    required this.baggage,
    required this.cancellationPolicy,
  });

  factory FlightDetails.fromJson(Map<String, dynamic> json) =>
      _$FlightDetailsFromJson(json);
  Map<String, dynamic> toJson() => _$FlightDetailsToJson(this);
}

@JsonSerializable()
class FlightBookingRequest {
  final String flightId;
  final List<Passenger> passengers;
  final String contactEmail;
  final String contactPhone;
  final Map<String, dynamic>? preferences;

  FlightBookingRequest({
    required this.flightId,
    required this.passengers,
    required this.contactEmail,
    required this.contactPhone,
    this.preferences,
  });

  factory FlightBookingRequest.fromJson(Map<String, dynamic> json) =>
      _$FlightBookingRequestFromJson(json);
  Map<String, dynamic> toJson() => _$FlightBookingRequestToJson(this);
}

// BUS MODELS

@JsonSerializable()
class BusSearchRequest {
  final String origin;
  final String destination;
  final DateTime journeyDate;
  final int passengers;

  BusSearchRequest({
    required this.origin,
    required this.destination,
    required this.journeyDate,
    required this.passengers,
  });

  factory BusSearchRequest.fromJson(Map<String, dynamic> json) =>
      _$BusSearchRequestFromJson(json);
  Map<String, dynamic> toJson() => _$BusSearchRequestToJson(this);
}

@JsonSerializable()
class BusSearchResponse {
  final List<Bus> buses;
  final int totalResults;
  final String searchId;

  BusSearchResponse({
    required this.buses,
    required this.totalResults,
    required this.searchId,
  });

  factory BusSearchResponse.fromJson(Map<String, dynamic> json) =>
      _$BusSearchResponseFromJson(json);
  Map<String, dynamic> toJson() => _$BusSearchResponseToJson(this);
}

@JsonSerializable()
class Bus {
  final String busId;
  final String operator;
  final String busType;
  final String origin;
  final String destination;
  final DateTime departureTime;
  final DateTime arrivalTime;
  final String duration;
  final double price;
  final String currency;
  final int availableSeats;
  final List<String> amenities;

  Bus({
    required this.busId,
    required this.operator,
    required this.busType,
    required this.origin,
    required this.destination,
    required this.departureTime,
    required this.arrivalTime,
    required this.duration,
    required this.price,
    required this.currency,
    required this.availableSeats,
    required this.amenities,
  });

  factory Bus.fromJson(Map<String, dynamic> json) => _$BusFromJson(json);
  Map<String, dynamic> toJson() => _$BusToJson(this);
}

@JsonSerializable()
class BusBookingRequest {
  final String busId;
  final List<Passenger> passengers;
  final String contactEmail;
  final String contactPhone;
  final List<String>? selectedSeats;

  BusBookingRequest({
    required this.busId,
    required this.passengers,
    required this.contactEmail,
    required this.contactPhone,
    this.selectedSeats,
  });

  factory BusBookingRequest.fromJson(Map<String, dynamic> json) =>
      _$BusBookingRequestFromJson(json);
  Map<String, dynamic> toJson() => _$BusBookingRequestToJson(this);
}

// HOTEL MODELS

@JsonSerializable()
class HotelSearchRequest {
  final String city;
  final DateTime checkIn;
  final DateTime checkOut;
  final int rooms;
  final int adults;
  final int children;

  HotelSearchRequest({
    required this.city,
    required this.checkIn,
    required this.checkOut,
    required this.rooms,
    required this.adults,
    this.children = 0,
  });

  factory HotelSearchRequest.fromJson(Map<String, dynamic> json) =>
      _$HotelSearchRequestFromJson(json);
  Map<String, dynamic> toJson() => _$HotelSearchRequestToJson(this);
}

@JsonSerializable()
class HotelSearchResponse {
  final List<Hotel> hotels;
  final int totalResults;
  final String searchId;

  HotelSearchResponse({
    required this.hotels,
    required this.totalResults,
    required this.searchId,
  });

  factory HotelSearchResponse.fromJson(Map<String, dynamic> json) =>
      _$HotelSearchResponseFromJson(json);
  Map<String, dynamic> toJson() => _$HotelSearchResponseToJson(this);
}

@JsonSerializable()
class Hotel {
  final String hotelId;
  final String name;
  final String address;
  final double rating;
  final double price;
  final String currency;
  final List<String> amenities;
  final List<String> images;
  final String description;

  Hotel({
    required this.hotelId,
    required this.name,
    required this.address,
    required this.rating,
    required this.price,
    required this.currency,
    required this.amenities,
    required this.images,
    required this.description,
  });

  factory Hotel.fromJson(Map<String, dynamic> json) => _$HotelFromJson(json);
  Map<String, dynamic> toJson() => _$HotelToJson(this);
}

@JsonSerializable()
class HotelDetails {
  final Hotel hotel;
  final List<RoomType> roomTypes;
  final List<String> policies;
  final Map<String, dynamic> location;

  HotelDetails({
    required this.hotel,
    required this.roomTypes,
    required this.policies,
    required this.location,
  });

  factory HotelDetails.fromJson(Map<String, dynamic> json) =>
      _$HotelDetailsFromJson(json);
  Map<String, dynamic> toJson() => _$HotelDetailsToJson(this);
}

@JsonSerializable()
class RoomType {
  final String roomId;
  final String name;
  final double price;
  final String currency;
  final int maxOccupancy;
  final List<String> amenities;
  final bool isAvailable;

  RoomType({
    required this.roomId,
    required this.name,
    required this.price,
    required this.currency,
    required this.maxOccupancy,
    required this.amenities,
    required this.isAvailable,
  });

  factory RoomType.fromJson(Map<String, dynamic> json) =>
      _$RoomTypeFromJson(json);
  Map<String, dynamic> toJson() => _$RoomTypeToJson(this);
}

@JsonSerializable()
class HotelBookingRequest {
  final String hotelId;
  final String roomId;
  final DateTime checkIn;
  final DateTime checkOut;
  final List<Guest> guests;
  final String contactEmail;
  final String contactPhone;
  final Map<String, dynamic>? specialRequests;

  HotelBookingRequest({
    required this.hotelId,
    required this.roomId,
    required this.checkIn,
    required this.checkOut,
    required this.guests,
    required this.contactEmail,
    required this.contactPhone,
    this.specialRequests,
  });

  factory HotelBookingRequest.fromJson(Map<String, dynamic> json) =>
      _$HotelBookingRequestFromJson(json);
  Map<String, dynamic> toJson() => _$HotelBookingRequestToJson(this);
}

@JsonSerializable()
class Guest {
  final String firstName;
  final String lastName;
  final String gender;
  final DateTime? dateOfBirth;

  Guest({
    required this.firstName,
    required this.lastName,
    required this.gender,
    this.dateOfBirth,
  });

  factory Guest.fromJson(Map<String, dynamic> json) => _$GuestFromJson(json);
  Map<String, dynamic> toJson() => _$GuestToJson(this);
}

// TRAIN MODELS

@JsonSerializable()
class TrainSearchRequest {
  final String origin;
  final String destination;
  final DateTime journeyDate;
  final String classType;
  final int passengers;

  TrainSearchRequest({
    required this.origin,
    required this.destination,
    required this.journeyDate,
    required this.classType,
    required this.passengers,
  });

  factory TrainSearchRequest.fromJson(Map<String, dynamic> json) =>
      _$TrainSearchRequestFromJson(json);
  Map<String, dynamic> toJson() => _$TrainSearchRequestToJson(this);
}

@JsonSerializable()
class TrainSearchResponse {
  final List<Train> trains;
  final int totalResults;
  final String searchId;

  TrainSearchResponse({
    required this.trains,
    required this.totalResults,
    required this.searchId,
  });

  factory TrainSearchResponse.fromJson(Map<String, dynamic> json) =>
      _$TrainSearchResponseFromJson(json);
  Map<String, dynamic> toJson() => _$TrainSearchResponseToJson(this);
}

@JsonSerializable()
class Train {
  final String trainId;
  final String trainNumber;
  final String trainName;
  final String origin;
  final String destination;
  final DateTime departureTime;
  final DateTime arrivalTime;
  final String duration;
  final Map<String, double> classPrices;
  final Map<String, int> availableSeats;
  final List<String> amenities;

  Train({
    required this.trainId,
    required this.trainNumber,
    required this.trainName,
    required this.origin,
    required this.destination,
    required this.departureTime,
    required this.arrivalTime,
    required this.duration,
    required this.classPrices,
    required this.availableSeats,
    required this.amenities,
  });

  factory Train.fromJson(Map<String, dynamic> json) => _$TrainFromJson(json);
  Map<String, dynamic> toJson() => _$TrainToJson(this);
}

@JsonSerializable()
class TrainBookingRequest {
  final String trainId;
  final String classType;
  final List<Passenger> passengers;
  final String contactEmail;
  final String contactPhone;
  final Map<String, dynamic>? preferences;

  TrainBookingRequest({
    required this.trainId,
    required this.classType,
    required this.passengers,
    required this.contactEmail,
    required this.contactPhone,
    this.preferences,
  });

  factory TrainBookingRequest.fromJson(Map<String, dynamic> json) =>
      _$TrainBookingRequestFromJson(json);
  Map<String, dynamic> toJson() => _$TrainBookingRequestToJson(this);
}
