import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/providers/settings_provider.dart';

class NotificationSettingsScreen extends ConsumerWidget {
  const NotificationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pushNotifications = ref.watch(pushNotificationsProvider);
    final setPushNotifications =
        ref.read(pushNotificationsProvider.notifier).setPushNotifications;
    final emailNotifications = ref.watch(emailNotificationsProvider);
    final setEmailNotifications =
        ref.read(emailNotificationsProvider.notifier).setEmailNotifications;
    final smsNotifications = ref.watch(smsNotificationsProvider);
    final setSmsNotifications =
        ref.read(smsNotificationsProvider.notifier).setSmsNotifications;
    final orderNotifications = ref.watch(orderNotificationsProvider);
    final setOrderNotifications =
        ref.read(orderNotificationsProvider.notifier).setOrderNotifications;
    final promotionalNotifications =
        ref.watch(promotionalNotificationsProvider);
    final setPromotionalNotifications = ref
        .read(promotionalNotificationsProvider.notifier)
        .setPromotionalNotifications;
    final soundEnabled = ref.watch(notificationSoundProvider);
    final setSoundEnabled =
        ref.read(notificationSoundProvider.notifier).setNotificationSound;
    final vibrationEnabled = ref.watch(notificationVibrationProvider);
    final setVibrationEnabled = ref
        .read(notificationVibrationProvider.notifier)
        .setNotificationVibration;

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Notification Settings',
      ),
      body: ListView(
        children: [
          _buildNotificationSection(
            context,
            'Push Notifications',
            'Receive push notifications',
            pushNotifications,
            setPushNotifications,
            Icons.notifications,
          ),
          if (pushNotifications) ...[
            _buildNotificationSection(
              context,
              'Email Notifications',
              'Receive email notifications',
              emailNotifications,
              setEmailNotifications,
              Icons.email,
            ),
            _buildNotificationSection(
              context,
              'SMS Notifications',
              'Receive SMS notifications',
              smsNotifications,
              setSmsNotifications,
              Icons.sms,
            ),
            _buildNotificationSection(
              context,
              'Order Notifications',
              'Get notified about your orders',
              orderNotifications,
              setOrderNotifications,
              Icons.shopping_bag,
            ),
            _buildNotificationSection(
              context,
              'Promotional Notifications',
              'Receive promotional offers and updates',
              promotionalNotifications,
              setPromotionalNotifications,
              Icons.local_offer,
            ),
            const Divider(),
            _buildNotificationSection(
              context,
              'Sound',
              'Play sound for notifications',
              soundEnabled,
              setSoundEnabled,
              Icons.volume_up,
            ),
            _buildNotificationSection(
              context,
              'Vibration',
              'Vibrate for notifications',
              vibrationEnabled,
              setVibrationEnabled,
              Icons.vibration,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNotificationSection(
    BuildContext context,
    String title,
    String subtitle,
    bool enabled,
    void Function(bool) setEnabled,
    IconData icon,
  ) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: enabled,
        onChanged: (value) {
          setEnabled(value);
          _showSnackBar(
            context,
            '$title ${value ? 'enabled' : 'disabled'}',
          );
        },
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
