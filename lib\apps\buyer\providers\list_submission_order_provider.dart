import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/apps/buyer/repositories/list_submission_order_repository.dart';
import 'package:shivish/apps/buyer/models/list_submission_order.dart';
import 'package:shivish/shared/utils/logger.dart';

part 'list_submission_order_provider.freezed.dart';

@freezed
sealed class ListSubmissionOrderState with _$ListSubmissionOrderState {
  const factory ListSubmissionOrderState({
    @Default(false) bool isLoading,
    @Default(null) String? error,
    @Default(null) ListSubmissionOrder? order,
  }) = _ListSubmissionOrderState;
}

final listSubmissionOrderProvider = StateNotifierProvider.autoDispose<
    ListSubmissionOrderNotifier, ListSubmissionOrderState>(
  (ref) => ListSubmissionOrderNotifier(
    repository: ref.watch(listSubmissionOrderRepositoryProvider),
  ),
);

class ListSubmissionOrderNotifier
    extends StateNotifier<ListSubmissionOrderState> {
  final ListSubmissionOrderRepository _repository;
  final _logger = getLogger('ListSubmissionOrderNotifier');

  ListSubmissionOrderNotifier({
    required ListSubmissionOrderRepository repository,
  })  : _repository = repository,
        super(const ListSubmissionOrderState());

  Future<void> createOrder({
    required String submissionId,
    required String buyerId,
    required String sellerId,
    required double totalAmount,
    required List<ListSubmissionOrderItem> items,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final order = await _repository.createOrder(
        submissionId: submissionId,
        buyerId: buyerId,
        sellerId: sellerId,
        totalAmount: totalAmount,
        items: items,
      );

      state = state.copyWith(
        isLoading: false,
        order: order,
      );
    } catch (e, st) {
      _logger.severe('Error creating order', e, st);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create order: ${e.toString()}',
      );
    }
  }

  Future<void> updateOrderStatus(String orderId, String status) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _repository.updateOrderStatus(
        orderId: orderId,
        status: status,
      );

      state = state.copyWith(isLoading: false);
    } catch (e, st) {
      _logger.severe('Error updating order status', e, st);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update order status: ${e.toString()}',
      );
    }
  }

  Future<void> updatePaymentStatus({
    required String orderId,
    required String paymentId,
    required bool isPaid,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _repository.updatePaymentStatus(
        orderId: orderId,
        paymentId: paymentId,
        isPaid: isPaid,
      );

      state = state.copyWith(isLoading: false);
    } catch (e, st) {
      _logger.severe('Error updating payment status', e, st);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update payment status: ${e.toString()}',
      );
    }
  }

  Future<void> updateDeliveryId({
    required String orderId,
    required String deliveryId,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _repository.updateDeliveryId(
        orderId: orderId,
        deliveryId: deliveryId,
      );

      state = state.copyWith(isLoading: false);
    } catch (e, st) {
      _logger.severe('Error updating delivery ID', e, st);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update delivery ID: ${e.toString()}',
      );
    }
  }

  void watchOrder(String orderId) {
    _repository.watchOrder(orderId).listen(
      (order) {
        state = state.copyWith(order: order);
      },
      onError: (e, st) {
        _logger.severe('Error watching order', e, st);
        state = state.copyWith(
          error: 'Failed to watch order: ${e.toString()}',
        );
      },
    );
  }
}
