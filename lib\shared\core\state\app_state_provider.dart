import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'app_state.dart';

final appStateProvider =
    StateNotifierProvider<AppStateNotifier, AppState>((ref) {
  return AppStateNotifier();
});

class AppStateNotifier extends StateNotifier<AppState> {
  AppStateNotifier() : super(const AppState());

  static const _stateKey = 'app_state';

  Future<void> loadState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final stateJson = prefs.getString(_stateKey);
      if (stateJson != null) {
        state = AppState.fromJson(Map<String, dynamic>.from(
          jsonDecode(stateJson),
        ));
      }
    } catch (e) {
      // If loading fails, keep the default state
      debugPrint('Error loading state: $e');
    }
  }

  Future<void> saveState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _stateKey,
        jsonEncode(state.toJson()),
      );
    } catch (e) {
      debugPrint('Error saving state: $e');
    }
  }

  void setAuthenticated(bool value) {
    state = state.copyWith(isAuthenticated: value);
    saveState();
  }

  void setDarkMode(bool value) {
    state = state.copyWith(isDarkMode: value);
    saveState();
  }

  void setLocale(String locale) {
    state = state.copyWith(locale: locale);
    saveState();
  }

  void setOfflineStatus(bool value) {
    state = state.copyWith(isOffline: value);
  }

  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  void updateUserData(Map<String, dynamic> data) {
    state = state.copyWith(userData: {
      ...state.userData,
      ...data,
    });
    saveState();
  }

  void clearUserData() {
    state = state.copyWith(
      isAuthenticated: false,
      userData: {},
    );
    saveState();
  }
}
