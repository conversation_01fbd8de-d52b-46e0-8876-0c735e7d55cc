import 'package:shivish/apps/seller/domain/models/payment_model.dart';

abstract class PaymentRepository {
  Future<PaymentSummary> getPaymentSummary();
  Future<List<TransactionModel>> getTransactions();
  Future<List<SettlementModel>> getSettlements();
  Future<List<BankAccountModel>> getBankAccounts();
  Future<void> addBankAccount(BankAccountModel account);
  Future<void> updateBankAccount(BankAccountModel account);
  Future<void> deleteBankAccount(String accountId);
  Future<void> setPrimaryBankAccount(String accountId);
  Future<void> requestSettlement({
    required double amount,
    required String bankAccountId,
  });
}
