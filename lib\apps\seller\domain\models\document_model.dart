import 'package:freezed_annotation/freezed_annotation.dart';

part 'document_model.freezed.dart';
part 'document_model.g.dart';

enum DocumentType {
  @JsonValue(1)
  businessLicense,
  @JsonValue(2)
  taxRegistration,
  @JsonValue(3)
  identityProof,
  @JsonValue(4)
  addressProof,
  @JsonValue(5)
  bankStatement,
  @JsonValue(6)
  tradeLicense,
  @JsonValue(7)
  gstRegistration,
  @JsonValue(8)
  qualification,
  @JsonValue(9)
  experience,
  @JsonValue(10)
  other
}

enum DocumentStatus {
  @JsonValue(1)
  pending,
  @JsonValue(2)
  approved,
  @JsonValue(3)
  rejected,
  @JsonValue(4)
  expired
}

@freezed
sealed class DocumentModel with _$DocumentModel {
  const factory DocumentModel({
    required String id,
    required String sellerId,
    required DocumentType type,
    required String fileName,
    required String fileUrl,
    required DocumentStatus status,
    String? rejectionReason,
    DateTime? expiryDate,
    required DateTime uploadedAt,
    DateTime? verifiedAt,
    String? verifiedBy,
    @Default(false) bool isDeleted,
    DateTime? deletedAt,
    String? deletedBy,
  }) = _DocumentModel;

  factory DocumentModel.fromJson(Map<String, dynamic> json) =>
      _$DocumentModelFromJson(json);
}
