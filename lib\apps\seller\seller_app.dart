import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shivish/shared/core/localization/l10n/app_localizations.dart';
import 'package:shivish/shared/services/settings/settings_service.dart';
import 'package:shivish/shared/ui_components/navigation/global_back_handler.dart';
import '../../shared/core/di/injection.dart';
import 'theme/seller_theme.dart';
import 'seller_routes.dart';
import 'di/seller_module.dart';
import 'presentation/providers/settings_provider.dart';
import 'presentation/cubits/settings_cubit.dart';
import 'presentation/providers/settings_notifier.dart';
import 'services/notification_popup_service.dart';

@injectable
class SellerApp extends ConsumerStatefulWidget {
  const SellerApp({super.key});

  @override
  ConsumerState<SellerApp> createState() => _SellerAppState();
}

class _SellerAppState extends ConsumerState<SellerApp> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      await initializeDependencies();
      registerSellerDependencies(getIt);
      debugPrint('SellerApp: Dependencies initialized');
    } catch (e) {
      debugPrint('Error initializing app: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('SellerApp: build called');

    // Create the app with router configuration
    final app = MaterialApp.router(
      title: 'Seller App',
      theme: SellerTheme.lightTheme,
      darkTheme: SellerTheme.darkTheme,
      routerConfig: SellerRoutes.router,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppLocalizations.supportedLocales,
      builder: (context, child) {
        return GlobalBackHandler(
          fallbackRoute: SellerRoutes.home,
          child: child!,
        );
      },
    );

    // Initialize SharedPreferences
    return FutureBuilder<SharedPreferences>(
      future: SharedPreferences.getInstance(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.hasError) {
            return MaterialApp(
              home: Scaffold(
                body: Center(
                  child: Text('Error initializing app: ${snapshot.error}'),
                ),
              ),
            );
          }

          // Create a shared preferences instance to use across providers
          final sharedPrefs = snapshot.data!;
          final settingsService = SettingsService(sharedPrefs);

          // Wrap the app with ProviderScope and override the providers
          return ProviderScope(
            overrides: [
              // Override the SharedPreferences provider with the actual instance
              sharedPreferencesProvider.overrideWithValue(sharedPrefs),

              // Override the SettingsService provider with a direct instance
              settingsServiceProvider.overrideWithValue(settingsService),

              // Override the settings provider with a direct instance
              settingsProvider.overrideWith(
                (ref) => SettingsNotifier(settingsService),
              ),

              // Override the settings cubit provider with a direct instance
              settingsCubitProvider.overrideWithValue(
                SettingsCubit(settingsService),
              ),
            ],
            child: SellerNotificationPopupInitializer(child: app),
          );
        }

        // Show loading indicator while SharedPreferences is initializing
        return MaterialApp(
          home: Scaffold(
            body: const Center(child: CircularProgressIndicator()),
          ),
        );
      },
    );
  }
}
