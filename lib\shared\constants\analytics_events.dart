/// Analytics event names used throughout the app
class AnalyticsEvents {
  // User events
  static const String userSignUp = 'user_sign_up';
  static const String userLogin = 'user_login';
  static const String userLogout = 'user_logout';
  static const String userProfileUpdate = 'user_profile_update';
  static const String userSettingsChange = 'user_settings_change';

  // Product events
  static const String productView = 'product_view';
  static const String productSearch = 'product_search';
  static const String addToCart = 'add_to_cart';
  static const String removeFromCart = 'remove_from_cart';
  static const String checkout = 'checkout';
  static const String purchase = 'purchase';

  // List events
  static const String createList = 'create_list';
  static const String updateList = 'update_list';
  static const String submitList = 'submit_list';
  static const String listPurchase = 'list_purchase';

  // Booking events
  static const String priestBooking = 'priest_booking';
  static const String technicianBooking = 'technician_booking';
  static const String bookingCancel = 'booking_cancel';
  static const String bookingComplete = 'booking_complete';

  // Calendar events
  static const String eventCreate = 'event_create';
  static const String eventUpdate = 'event_update';
  static const String eventDelete = 'event_delete';
  static const String eventReminder = 'event_reminder';

  // Alarm events
  static const String alarmCreate = 'alarm_create';
  static const String alarmUpdate = 'alarm_update';
  static const String alarmDelete = 'alarm_delete';
  static const String alarmTrigger = 'alarm_trigger';

  // Media events
  static const String mediaPlay = 'media_play';
  static const String mediaPause = 'media_pause';
  static const String mediaStop = 'media_stop';
  static const String mediaDownload = 'media_download';

  // Chat events
  static const String chatStart = 'chat_start';
  static const String chatMessage = 'chat_message';
  static const String chatEnd = 'chat_end';
  static const String voiceCommand = 'voice_command';

  // Review events
  static const String reviewSubmit = 'review_submit';
  static const String reviewUpdate = 'review_update';
  static const String reviewDelete = 'review_delete';
  static const String reviewVote = 'review_vote';

  // Error events
  static const String appError = 'app_error';
  static const String networkError = 'network_error';
  static const String paymentError = 'payment_error';
  static const String locationError = 'location_error';

  // Performance events
  static const String appStart = 'app_start';
  static const String appBackground = 'app_background';
  static const String appForeground = 'app_foreground';
  static const String screenLoad = 'screen_load';
  static const String apiCall = 'api_call';
}
