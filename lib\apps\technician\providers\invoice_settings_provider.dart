import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final invoiceSettingsProvider =
    AsyncNotifierProvider<InvoiceSettingsNotifier, Map<String, String>>(() {
  return InvoiceSettingsNotifier();
});

class InvoiceSettingsNotifier extends AsyncNotifier<Map<String, String>> {
  late final DatabaseService _databaseService;

  @override
  Future<Map<String, String>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadInvoiceSettings();
  }

  Future<Map<String, String>> _loadInvoiceSettings() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('users', userId);
      final settings = userData?['invoice_settings'] as Map<String, dynamic>?;
      return {
        'prefix': settings?['prefix'] as String? ?? '',
        'number': settings?['number'] as String? ?? '1',
        'footer': settings?['footer'] as String? ?? '',
      };
    } catch (e) {
      debugPrint('Failed to load invoice settings: $e');
      throw Exception('Failed to load invoice settings: $e');
    }
  }

  Future<void> saveInvoiceSettings({
    required String prefix,
    required String number,
    required String footer,
  }) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      await _databaseService.update('users', userId, {
        'invoice_settings': {
          'prefix': prefix,
          'number': number,
          'footer': footer,
          'updated_at': DateTime.now().toIso8601String(),
        },
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData({
        'prefix': prefix,
        'number': number,
        'footer': footer,
      });
    } catch (e) {
      debugPrint('Failed to save invoice settings: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}
