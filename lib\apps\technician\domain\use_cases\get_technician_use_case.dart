import 'package:injectable/injectable.dart';
import '../../../../shared/models/technician/technician.dart';
import '../repositories/technician_repository.dart';

@injectable
class GetTechnicianUseCase {
  final TechnicianRepository _repository;

  GetTechnicianUseCase(this._repository);

  Future<Technician?> call(String id) => _repository.getTechnician(id);
  Stream<Technician?> stream(String id) => _repository.streamTechnician(id);
}
