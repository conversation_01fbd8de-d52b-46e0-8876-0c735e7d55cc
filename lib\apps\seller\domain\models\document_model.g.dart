// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_DocumentModel _$DocumentModelFromJson(Map<String, dynamic> json) =>
    _DocumentModel(
      id: json['id'] as String,
      sellerId: json['sellerId'] as String,
      type: $enumDecode(_$DocumentTypeEnumMap, json['type']),
      fileName: json['fileName'] as String,
      fileUrl: json['fileUrl'] as String,
      status: $enumDecode(_$DocumentStatusEnumMap, json['status']),
      rejectionReason: json['rejectionReason'] as String?,
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
      uploadedAt: DateTime.parse(json['uploadedAt'] as String),
      verifiedAt: json['verifiedAt'] == null
          ? null
          : DateTime.parse(json['verifiedAt'] as String),
      verifiedBy: json['verifiedBy'] as String?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      deletedBy: json['deletedBy'] as String?,
    );

Map<String, dynamic> _$DocumentModelToJson(_DocumentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sellerId': instance.sellerId,
      'type': _$DocumentTypeEnumMap[instance.type]!,
      'fileName': instance.fileName,
      'fileUrl': instance.fileUrl,
      'status': _$DocumentStatusEnumMap[instance.status]!,
      'rejectionReason': instance.rejectionReason,
      'expiryDate': instance.expiryDate?.toIso8601String(),
      'uploadedAt': instance.uploadedAt.toIso8601String(),
      'verifiedAt': instance.verifiedAt?.toIso8601String(),
      'verifiedBy': instance.verifiedBy,
      'isDeleted': instance.isDeleted,
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'deletedBy': instance.deletedBy,
    };

const _$DocumentTypeEnumMap = {
  DocumentType.businessLicense: 1,
  DocumentType.taxRegistration: 2,
  DocumentType.identityProof: 3,
  DocumentType.addressProof: 4,
  DocumentType.bankStatement: 5,
  DocumentType.tradeLicense: 6,
  DocumentType.gstRegistration: 7,
  DocumentType.qualification: 8,
  DocumentType.experience: 9,
  DocumentType.other: 10,
};

const _$DocumentStatusEnumMap = {
  DocumentStatus.pending: 1,
  DocumentStatus.approved: 2,
  DocumentStatus.rejected: 3,
  DocumentStatus.expired: 4,
};
