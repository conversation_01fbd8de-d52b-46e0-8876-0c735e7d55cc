import 'package:freezed_annotation/freezed_annotation.dart';

part 'banner_model.freezed.dart';
part 'banner_model.g.dart';

enum BannerStatus { pending, approved, rejected }

@freezed
abstract class BannerModel with _$BannerModel {
  const factory BannerModel({
    required String id,
    required String title,
    String? description,
    required String imageUrl,
    required String actionType,
    String? actionData,
    required int priority,
    required bool isActive,
    required DateTime startDate,
    required DateTime endDate,
    required DateTime createdAt,
    required DateTime updatedAt,
    required String submitterId,
    required String submitterRole,
    @Default(BannerStatus.pending) BannerStatus status,
    String? rejectionReason,
  }) = _BannerModel;

  factory BannerModel.fromJson(Map<String, dynamic> json) =>
      _$BannerModelFromJson(json);
}
