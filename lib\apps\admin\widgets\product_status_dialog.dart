import 'package:flutter/material.dart';
import 'package:shivish/shared/models/product/product_model.dart';

class ProductStatusDialog extends StatefulWidget {
  final ProductModel product;

  const ProductStatusDialog({
    super.key,
    required this.product,
  });

  @override
  State<ProductStatusDialog> createState() => _ProductStatusDialogState();
}

class _ProductStatusDialogState extends State<ProductStatusDialog> {
  late ProductStatus _selectedStatus;
  late bool _isApproved;
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.product.productStatus;
    _isApproved = widget.product.isApproved;
    _notesController.text = widget.product.verificationNotes ?? '';
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Update Product Status'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Product: ${widget.product.name}',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<ProductStatus>(
              value: _selectedStatus,
              decoration: const InputDecoration(
                labelText: 'Status',
                border: OutlineInputBorder(),
              ),
              items: ProductStatus.values
                  .map((status) => DropdownMenuItem(
                        value: status,
                        child: Text(
                            status.toString().split('.').last.toUpperCase()),
                      ))
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedStatus = value;
                    // Automatically set isApproved based on status
                    _isApproved = value == ProductStatus.approved;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Approved'),
              subtitle: const Text('Product is approved for sale'),
              value: _isApproved,
              onChanged: (value) {
                setState(() {
                  _isApproved = value;
                  // Automatically update status when approval changes
                  if (value) {
                    _selectedStatus = ProductStatus.approved;
                  } else if (_selectedStatus == ProductStatus.approved) {
                    _selectedStatus = ProductStatus.pending;
                  }
                });
              },
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Verification Notes',
                border: OutlineInputBorder(),
                hintText: 'Add notes about the verification decision...',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('CANCEL'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop({
            'status': _selectedStatus,
            'isApproved': _isApproved,
            'verificationNotes': _notesController.text.trim(),
          }),
          child: const Text('UPDATE'),
        ),
      ],
    );
  }
}
