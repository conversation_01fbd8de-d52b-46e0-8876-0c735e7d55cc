import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import '../services/friend_tracking_service.dart';
import '../../../shared/providers/auth_provider.dart';

/// Provider for friend tracking service with proper lifecycle management
final friendTrackingServiceProvider = Provider<FriendTrackingService>((ref) {
  final service = FriendTrackingService();

  // Ensure proper disposal when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for active friend tracking sessions
final activeFriendTrackingSessionsProvider =
    Provider<Map<String, FriendTrackingSession>>((ref) {
      final service = ref.watch(friendTrackingServiceProvider);
      return service.activeSessions;
    });

/// Provider for friend tracking actions
final friendTrackingActionsProvider = Provider<FriendTrackingActions>((ref) {
  final service = ref.watch(friendTrackingServiceProvider);
  final auth = ref.watch(authProvider);
  return FriendTrackingActions(service, auth?.id);
});

/// Actions for friend tracking with error handling
class FriendTrackingActions {
  final FriendTrackingService _service;
  final String? _userId;

  FriendTrackingActions(this._service, this._userId);

  /// Setup friend tracking for a ride
  Future<bool> setupFriendTracking({
    required String rideId,
    String? startFriendPhone,
    String? endFriendPhone,
  }) async {
    if (_userId == null || _userId.isEmpty) {
      print('Cannot setup friend tracking: user not authenticated');
      return false;
    }

    if (rideId.isEmpty) {
      print('Cannot setup friend tracking: invalid ride ID');
      return false;
    }

    try {
      return await _service.setupFriendTracking(
        rideId: rideId,
        userId: _userId,
        startFriendPhone: startFriendPhone,
        endFriendPhone: endFriendPhone,
      );
    } catch (e) {
      print('Error setting up friend tracking: $e');
      return false;
    }
  }

  /// Update live location for tracking
  Future<void> updateLiveLocation({
    required String rideId,
    required Position location,
  }) async {
    if (rideId.isEmpty) {
      print('Cannot update location: invalid ride ID');
      return;
    }

    try {
      await _service.updateLiveLocation(rideId: rideId, location: location);
    } catch (e) {
      print('Error updating live location: $e');
    }
  }

  /// Stop friend tracking for a ride
  Future<void> stopFriendTracking(String rideId) async {
    if (rideId.isEmpty) {
      print('Cannot stop friend tracking: invalid ride ID');
      return;
    }

    try {
      await _service.stopFriendTracking(rideId);
    } catch (e) {
      print('Error stopping friend tracking: $e');
    }
  }

  /// Get friends tracking a specific ride
  Future<List<Map<String, dynamic>>> getFriendsTrackingRide(
    String rideId,
  ) async {
    if (rideId.isEmpty) {
      print('Cannot get friends: invalid ride ID');
      return [];
    }

    try {
      return await _service.getFriendsTrackingRide(rideId);
    } catch (e) {
      print('Error getting friends tracking ride: $e');
      return [];
    }
  }

  /// Get user's tracking invitations (rides where user is invited to track)
  Future<List<Map<String, dynamic>>> getUserTrackingInvitations() async {
    if (_userId == null || _userId.isEmpty) {
      print('Cannot get tracking invitations: user not authenticated');
      return [];
    }

    try {
      return await _service.getUserTrackingInvitations(_userId);
    } catch (e) {
      print('Error getting user tracking invitations: $e');
      return [];
    }
  }

  /// Check if user is authenticated
  bool get isUserAuthenticated => _userId != null && _userId.isNotEmpty;

  /// Get user ID
  String? get userId => _userId;

  /// Check if service has active sessions
  bool get hasActiveSessions => _service.hasActiveSessions;

  /// Get active sessions
  Map<String, FriendTrackingSession> get activeSessions =>
      _service.activeSessions;
}
