import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/models/ride/vehicle_fare_model.dart';
import 'package:shivish/shared/providers/vehicle_fare_provider.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';

/// Screen for managing vehicle fares
class VehicleFareManagementScreen extends ConsumerStatefulWidget {
  const VehicleFareManagementScreen({super.key});

  @override
  ConsumerState<VehicleFareManagementScreen> createState() => _VehicleFareManagementScreenState();
}

class _VehicleFareManagementScreenState extends ConsumerState<VehicleFareManagementScreen> {
  @override
  Widget build(BuildContext context) {
    final vehicleFaresAsync = ref.watch(vehicleFaresProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Vehicle Fare Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showFareGuidelines(context),
          ),
        ],
      ),
      body: vehicleFaresAsync.when(
        data: (fares) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Header with explanation
              Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Ride Fare Configuration',
                            style: theme.textTheme.titleLarge,
                          ),
                          ElevatedButton.icon(
                            onPressed: () => _showFareGuidelines(context),
                            icon: const Icon(Icons.help_outline),
                            label: const Text('Fare Guidelines'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: theme.colorScheme.primary.withValues(alpha: 26), // 0.1 * 255 = ~26
                              foregroundColor: theme.colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Configure fare rates for different vehicle types. These rates will be used to calculate ride fares for customers.',
                        style: theme.textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: _InfoCard(
                              title: 'Base Fare',
                              description: 'Minimum charge for any ride',
                              icon: Icons.money,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _InfoCard(
                              title: 'Per KM Rate',
                              description: 'Charge per kilometer traveled',
                              icon: Icons.route,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _InfoCard(
                              title: 'Per Minute Rate',
                              description: 'Charge per minute of ride time',
                              icon: Icons.timer,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Vehicle fare cards
              ...fares.map((fare) => _VehicleFareCard(
                    fare: fare,
                    onEdit: () => _showEditFareDialog(context, fare),
                  )),

              // Add new fare button
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () => _showEditFareDialog(context, null),
                icon: const Icon(Icons.add),
                label: const Text('Add New Vehicle Type'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              Text('Error loading vehicle fares: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(vehicleFaresProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show dialog with fare guidelines
  void _showFareGuidelines(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fare Setting Guidelines'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _GuidelineItem(
                title: 'Base Fare',
                description: 'Set a reasonable minimum charge that covers fixed costs. Typically higher for premium vehicles.',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Per KM Rate',
                description: 'Consider fuel efficiency, maintenance costs, and market rates. Two-wheelers should have lower rates than cars.',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Per Minute Rate',
                description: 'Compensates for time spent in traffic. Should be higher for premium vehicles to reflect opportunity cost.',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Market Research',
                description: 'Research competitor rates in your area to stay competitive while ensuring profitability.',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Vehicle Operating Costs',
                description: 'Different vehicle types have different operating costs. Factor in fuel efficiency, maintenance, and depreciation.',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Driver Earnings',
                description: 'Ensure the fare structure allows drivers to earn a reasonable income after expenses.',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Dynamic Pricing',
                description: 'Consider implementing surge pricing during peak hours or in high-demand areas (feature coming soon).',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Transparent Breakdown',
                description: 'Make fare calculation transparent to both riders and drivers, showing base fare, distance fare, and time fare.',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Promotional Rates',
                description: 'Consider offering promotional rates for new users or during special events to attract more customers.',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Minimum Fare',
                description: 'Set a minimum fare to ensure that short trips are still profitable for drivers.',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Cancellation Fees',
                description: 'Implement cancellation fees to discourage riders from canceling rides after a driver has been assigned.',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Waiting Time Charges',
                description: 'Consider adding waiting time charges if the rider keeps the driver waiting for an extended period.',
              ),
              const SizedBox(height: 16),
              _GuidelineItem(
                title: 'Regular Updates',
                description: 'Review and update fares periodically to account for changing fuel prices and market conditions.',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show dialog to edit or create a fare
  void _showEditFareDialog(BuildContext context, VehicleFareModel? fare) {
    showDialog(
      context: context,
      builder: (context) => _EditFareDialog(fare: fare),
    );
  }
}

/// Card displaying vehicle fare information
class _VehicleFareCard extends ConsumerStatefulWidget {
  final VehicleFareModel fare;
  final VoidCallback onEdit;

  const _VehicleFareCard({
    required this.fare,
    required this.onEdit,
  });

  @override
  ConsumerState<_VehicleFareCard> createState() => _VehicleFareCardState();
}

class _VehicleFareCardState extends ConsumerState<_VehicleFareCard> {
  bool _isUpdating = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final fare = widget.fare;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      _getVehicleIcon(fare.vehicleType),
                      color: theme.colorScheme.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      fare.vehicleType,
                      style: theme.textTheme.titleLarge,
                    ),
                  ],
                ),
                _isUpdating
                    ? const SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                        ),
                      )
                    : Switch(
                        value: fare.isActive,
                        onChanged: _updateActiveStatus,
                      ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _FareDetailItem(
                    label: 'Base Fare',
                    value: '${fare.currency}${fare.baseFare.toStringAsFixed(0)}',
                  ),
                ),
                Expanded(
                  child: _FareDetailItem(
                    label: 'Per KM',
                    value: '${fare.currency}${fare.perKmRate.toStringAsFixed(1)}',
                  ),
                ),
                Expanded(
                  child: _FareDetailItem(
                    label: 'Per Minute',
                    value: '${fare.currency}${fare.perMinuteRate.toStringAsFixed(1)}',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: widget.onEdit,
                  icon: const Icon(Icons.edit),
                  label: const Text('Edit'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _updateActiveStatus(bool value) async {
    if (_isUpdating) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final service = ref.read(vehicleFareServiceProvider);
      final success = await service.saveVehicleFare(widget.fare.copyWith(isActive: value));

      if (!mounted) return;

      if (success) {
        // Refresh the provider
        final _ = ref.refresh(vehicleFaresProvider);
      } else {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update status'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  IconData _getVehicleIcon(String type) {
    switch (type) {
      case 'Two-Wheeler':
        return Icons.motorcycle;
      case 'Auto':
        return Icons.electric_rickshaw;
      case 'Standard':
        return Icons.directions_car;
      case 'Premium':
        return Icons.directions_car;
      case 'XL':
        return Icons.airport_shuttle;
      default:
        return Icons.directions_car;
    }
  }
}

/// Dialog for editing or creating a fare
class _EditFareDialog extends ConsumerStatefulWidget {
  final VehicleFareModel? fare;

  const _EditFareDialog({this.fare});

  @override
  ConsumerState<_EditFareDialog> createState() => _EditFareDialogState();
}

class _EditFareDialogState extends ConsumerState<_EditFareDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _vehicleTypeController;
  late final TextEditingController _baseFareController;
  late final TextEditingController _perKmRateController;
  late final TextEditingController _perMinuteRateController;
  late bool _isActive;

  @override
  void initState() {
    super.initState();
    final fare = widget.fare;
    _vehicleTypeController = TextEditingController(text: fare?.vehicleType ?? '');
    _baseFareController = TextEditingController(text: fare?.baseFare.toString() ?? '50');
    _perKmRateController = TextEditingController(text: fare?.perKmRate.toString() ?? '12');
    _perMinuteRateController = TextEditingController(text: fare?.perMinuteRate.toString() ?? '2');
    _isActive = fare?.isActive ?? true;
  }

  @override
  void dispose() {
    _vehicleTypeController.dispose();
    _baseFareController.dispose();
    _perKmRateController.dispose();
    _perMinuteRateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.fare != null;

    return AlertDialog(
      title: Text(isEditing ? 'Edit Vehicle Fare' : 'Add New Vehicle Fare'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextFormField(
                controller: _vehicleTypeController,
                decoration: const InputDecoration(
                  labelText: 'Vehicle Type',
                  hintText: 'e.g., Standard, Premium, etc.',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a vehicle type';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _baseFareController,
                decoration: const InputDecoration(
                  labelText: 'Base Fare (₹)',
                  hintText: 'Minimum charge for any ride',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a base fare';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _perKmRateController,
                decoration: const InputDecoration(
                  labelText: 'Per KM Rate (₹)',
                  hintText: 'Charge per kilometer traveled',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a per km rate';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _perMinuteRateController,
                decoration: const InputDecoration(
                  labelText: 'Per Minute Rate (₹)',
                  hintText: 'Charge per minute of ride time',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a per minute rate';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Checkbox(
                    value: _isActive,
                    onChanged: (value) {
                      setState(() {
                        _isActive = value ?? true;
                      });
                    },
                  ),
                  const Text('Active'),
                ],
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveFare,
          child: Text(isEditing ? 'Update' : 'Add'),
        ),
      ],
    );
  }

  void _saveFare() async {
    if (_formKey.currentState?.validate() ?? false) {
      final service = ref.read(vehicleFareServiceProvider);

      final fare = VehicleFareModel(
        id: widget.fare?.id ?? '',
        vehicleType: _vehicleTypeController.text,
        baseFare: double.parse(_baseFareController.text),
        perKmRate: double.parse(_perKmRateController.text),
        perMinuteRate: double.parse(_perMinuteRateController.text),
        isActive: _isActive,
        createdAt: widget.fare?.createdAt,
        updatedAt: DateTime.now(),
      );

      try {
        final success = await service.saveVehicleFare(fare);

        if (!mounted) return;

        if (success) {
          // Refresh the provider
          final _ = ref.refresh(vehicleFaresProvider);
          Navigator.of(context).pop();
        } else {
          // Show error
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to save fare'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving fare: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// Widget for displaying fare details
class _FareDetailItem extends StatelessWidget {
  final String label;
  final String value;

  const _FareDetailItem({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}

/// Widget for displaying fare guidelines
class _GuidelineItem extends StatelessWidget {
  final String title;
  final String description;

  const _GuidelineItem({
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          description,
          style: theme.textTheme.bodyMedium,
        ),
      ],
    );
  }
}

/// Widget for displaying info cards
class _InfoCard extends StatelessWidget {
  final String title;
  final String description;
  final IconData icon;

  const _InfoCard({
    required this.title,
    required this.description,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 26), // 0.1 * 255 = ~26
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: theme.textTheme.bodySmall,
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
