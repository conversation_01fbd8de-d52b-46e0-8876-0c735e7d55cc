import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../shared/models/favorite_seller.dart';

/// Service to manage favorite sellers
class FavoriteSellerService {
  static const String _favoriteSellersKey = 'favorite_sellers';

  /// Get all favorite sellers
  Future<List<FavoriteSeller>> getFavoriteSellers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_favoriteSellersKey);

      if (jsonString == null) {
        return [];
      }

      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList.map((json) => FavoriteSeller.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting favorite sellers: $e');
      return [];
    }
  }

  /// Add a seller to favorites
  Future<bool> addFavoriteSeller(FavoriteSeller seller) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sellers = await getFavoriteSellers();

      // Check if seller already exists
      if (sellers.any((s) => s.id == seller.id)) {
        return false;
      }

      // Add new seller
      sellers.add(seller);

      // Save to shared preferences
      final jsonList = sellers.map((s) => s.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      return await prefs.setString(_favoriteSellersKey, jsonString);
    } catch (e) {
      debugPrint('Error adding favorite seller: $e');
      return false;
    }
  }

  /// Remove a seller from favorites
  Future<bool> removeFavoriteSeller(String sellerId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sellers = await getFavoriteSellers();

      // Remove seller
      sellers.removeWhere((s) => s.id == sellerId);

      // Save to shared preferences
      final jsonList = sellers.map((s) => s.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      return await prefs.setString(_favoriteSellersKey, jsonString);
    } catch (e) {
      debugPrint('Error removing favorite seller: $e');
      return false;
    }
  }

  /// Check if a seller is in favorites
  Future<bool> isFavoriteSeller(String sellerId) async {
    try {
      final sellers = await getFavoriteSellers();
      return sellers.any((s) => s.id == sellerId);
    } catch (e) {
      debugPrint('Error checking favorite seller: $e');
      return false;
    }
  }

  /// Parse seller data from QR code
  FavoriteSeller? parseSellerDataFromQR(String qrData) {
    try {
      // Check if it's a UPI payment QR code
      if (qrData.startsWith('upi://')) {
        // Parse UPI payment URL
        final uri = Uri.parse(qrData);
        final params = uri.queryParameters;

        // Extract UPI ID (pa parameter)
        final upiId = params['pa'];
        final name = params['pn'];

        if (upiId != null && name != null) {
          // Create a basic seller with UPI information
          return FavoriteSeller(
            id: 'upi-${DateTime.now().millisecondsSinceEpoch}',
            businessName: Uri.decodeComponent(name),
            category: 'UPI Merchant',
            email: '',
            phoneNumber: '',
            rating: 0.0,
            totalReviews: 0,
            upiId: upiId,
          );
        }
        return null;
      }

      // Handle regular seller QR code
      // Decode the URI-encoded data
      final decodedData = Uri.decodeFull(qrData);

      // Remove any extra characters like curly braces
      final cleanData = decodedData
          .replaceAll('{', '')
          .replaceAll('}', '')
          .replaceAll(' ', '');

      // Split by commas and create key-value pairs
      final pairs = cleanData.split(',');
      final Map<String, dynamic> data = {};

      for (final pair in pairs) {
        final keyValue = pair.split(':');
        if (keyValue.length == 2) {
          final key = keyValue[0].trim();
          final value = keyValue[1].trim();

          // Convert values to appropriate types
          if (key == 'rating') {
            data[key] = double.tryParse(value) ?? 0.0;
          } else if (key == 'totalReviews') {
            data[key] = int.tryParse(value) ?? 0;
          } else {
            data[key] = value;
          }
        }
      }

      // Check if all required fields are present
      if (data.containsKey('id') &&
          data.containsKey('businessName') &&
          data.containsKey('category')) {
        return FavoriteSeller(
          id: data['id'] as String,
          businessName: data['businessName'] as String,
          category: data['category'] as String,
          email: (data['email'] as String?) ?? '',
          phoneNumber: (data['phoneNumber'] as String?) ?? '',
          rating: (data['rating'] as double?) ?? 0.0,
          totalReviews: (data['totalReviews'] as int?) ?? 0,
          upiId: data['upiId'] as String?,
        );
      }

      return null;
    } catch (e) {
      debugPrint('Error parsing seller data from QR: $e');
      return null;
    }
  }
}
