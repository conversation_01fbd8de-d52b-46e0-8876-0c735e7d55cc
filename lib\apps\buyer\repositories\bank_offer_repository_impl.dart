import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/offer/bank_offer_model.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';
import 'bank_offer_repository.dart';

/// Implementation of the bank offer repository
class BankOfferRepositoryImpl implements BankOfferRepository {
  final DatabaseService _databaseService;
  final _logger = getLogger('BankOfferRepositoryImpl');
  final String _collection = 'bank_offers';

  BankOfferRepositoryImpl(this._databaseService);

  @override
  Future<List<BankOfferModel>> getActiveBankOffers() async {
    try {
      // Get all bank offers and filter in memory since hybrid storage may not support complex queries
      final allOffers = await _databaseService.getAll(_collection);

      final activeOffers = allOffers.where((offerData) {
        final isActive = offerData['isActive'] == true;
        final isDeleted = offerData['isDeleted'] == true;

        if (!isActive || isDeleted) return false;

        final startDateStr = offerData['startDate'] as String?;
        final endDateStr = offerData['endDate'] as String?;

        if (startDateStr == null || endDateStr == null) return false;

        final startDate = DateTime.tryParse(startDateStr);
        final endDate = DateTime.tryParse(endDateStr);
        final nowDate = DateTime.now();

        return startDate != null &&
               endDate != null &&
               nowDate.isAfter(startDate) &&
               nowDate.isBefore(endDate);
      }).toList();

      return activeOffers
          .map((offerData) => BankOfferModel.fromJson(offerData))
          .toList();
    } catch (e) {
      _logger.severe('Error getting active bank offers: $e');
      return [];
    }
  }

  @override
  Future<List<BankOfferModel>> getBankOffersByProductId(String productId) async {
    try {
      // Get all active bank offers first
      final activeOffers = await getActiveBankOffers();

      // Filter offers that apply to this product
      return activeOffers
          .where((offer) =>
              offer.applicableProductIds.contains(productId) ||
              offer.applicableProductIds.contains('all'))
          .toList();
    } catch (e) {
      _logger.severe('Error getting bank offers by product ID: $e');
      return [];
    }
  }

  @override
  Future<List<BankOfferModel>> getBankOffersByCategoryId(String categoryId) async {
    try {
      // Get all active bank offers first
      final activeOffers = await getActiveBankOffers();

      // Filter offers that apply to this category
      return activeOffers
          .where((offer) =>
              offer.applicableCategories.contains(categoryId) ||
              offer.applicableCategories.contains('all'))
          .toList();
    } catch (e) {
      _logger.severe('Error getting bank offers by category ID: $e');
      return [];
    }
  }
}

/// Provider for the bank offer repository implementation
final bankOfferRepositoryImplProvider = Provider<BankOfferRepository>((ref) {
  return BankOfferRepositoryImpl(DatabaseService(DatabaseConfig.fromEnvironment()));
});
