import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import '../config/api_config.dart';
import '../models/api_error.dart';
import '../models/api_response.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/error_interceptor.dart';

// HTTP status codes
const status403Forbidden = 403;

class ApiClient {
  late final Dio _dio;
  late final ApiConfig _config;
  late final CacheStore _cacheStore;

  ApiClient({ApiConfig? config}) {
    _config = config ?? const ApiConfig();
    _initializeDio();
  }

  void _initializeDio() {
    _dio = Dio(
      BaseOptions(
        baseUrl: _config.baseUrl,
        connectTimeout: Duration(milliseconds: _config.connectTimeout),
        receiveTimeout: Duration(milliseconds: _config.receiveTimeout),
        sendTimeout: Duration(milliseconds: _config.sendTimeout),
        headers: _config.defaultHeaders,
      ),
    );

    // Initialize cache store
    _cacheStore = MemCacheStore(
      maxSize: _config.maxCacheSize,
      maxEntrySize: _config.maxCacheSize ~/ 10,
    );

    // Add interceptors
    _dio.interceptors.addAll([
      // Cache interceptor
      if (_config.enableCaching)
        DioCacheInterceptor(
          options: CacheOptions(
            store: _cacheStore,
            policy: CachePolicy.request,
            maxStale: _config.cacheDuration,
            priority: CachePriority.normal,
            keyBuilder: CacheOptions.defaultCacheKeyBuilder,
            allowPostMethod: false,
          ),
        ),

      // Retry interceptor
      RetryInterceptor(
        dio: _dio,
        logPrint: _config.enableLogging ? debugPrint : null,
        retries: _config.maxRetries,
        retryDelays: const [
          Duration(seconds: 1),
          Duration(seconds: 2),
          Duration(seconds: 3),
        ],
        retryableExtraStatuses: {status403Forbidden},
      ),

      // Custom interceptors
      AuthInterceptor(),
      ErrorInterceptor(),

      // Logging interceptor
      if (_config.enableLogging)
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          logPrint: (obj) => debugPrint(obj.toString()),
        ),
    ]);
  }

  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );

      return _handleResponse(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? fromJson,
  ) {
    if (response.statusCode == null ||
        response.statusCode! < 200 ||
        response.statusCode! >= 300) {
      return ApiResponse(
        isSuccess: false,
        error: ApiError.server(
          message: 'Server returned status code ${response.statusCode}',
        ),
        status: ApiStatus.error,
      );
    }

    try {
      final data = fromJson != null ? fromJson(response.data) : response.data;

      return ApiResponse(
        data: data as T,
        isSuccess: true,
        status: ApiStatus.success,
        requestInfo: RequestInfo(
          requestId:
              response.requestOptions.extra['requestId'] as String? ?? '',
          endpoint: response.requestOptions.path,
          method: response.requestOptions.method,
          statusCode: response.statusCode ?? 0,
          responseTime:
              response.requestOptions.extra['responseTime'] as int? ?? 0,
          headers: response.headers.map,
        ),
      );
    } catch (e) {
      return ApiResponse(
        isSuccess: false,
        error: ApiError.unknown(
          message: 'Failed to parse response: ${e.toString()}',
        ),
        status: ApiStatus.error,
      );
    }
  }

  ApiResponse<T> _handleError<T>(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return ApiResponse(
            isSuccess: false,
            error: ApiError.timeout(),
            status: ApiStatus.error,
          );

        case DioExceptionType.badResponse:
          return ApiResponse(
            isSuccess: false,
            error: ApiError.server(
              message: 'Server error: ${error.response?.statusCode}',
              details: error.response?.data,
            ),
            status: ApiStatus.error,
          );

        case DioExceptionType.cancel:
          return ApiResponse(
            isSuccess: false,
            error: ApiError.unknown(message: 'Request was cancelled'),
            status: ApiStatus.error,
          );

        default:
          if (error.error is SocketException) {
            return ApiResponse(
              isSuccess: false,
              error: ApiError.network(),
              status: ApiStatus.error,
            );
          }
          return ApiResponse(
            isSuccess: false,
            error: ApiError.unknown(
              message: error.message ?? 'An unknown error occurred',
            ),
            status: ApiStatus.error,
          );
      }
    }

    return ApiResponse(
      isSuccess: false,
      error: ApiError.unknown(
        message: error.toString(),
      ),
      status: ApiStatus.error,
    );
  }

  void dispose() {
    _dio.close();
    _cacheStore.clean();
  }
}
