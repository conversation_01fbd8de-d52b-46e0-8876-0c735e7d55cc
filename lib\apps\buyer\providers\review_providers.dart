import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/buyer/repositories/review_repository.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';

/// Database service provider for hybrid storage
final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

/// Review repository provider using hybrid storage
final reviewRepositoryProvider = Provider<ReviewRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return ReviewRepository(databaseService: databaseService);
});
