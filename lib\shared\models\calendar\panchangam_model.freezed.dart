// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'panchangam_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PanchangamModel {

 DateTime get date; bool get isAuspicious; String get tithi; String get nakshatra; String get yoga; String get karana; String get sunrise; String get sunset; String get moonrise; String get moonset; String get rahuKalam; String get gulikaKalam; String get yamagandam; String get abhijitMuhurta; List<String> get festivals; List<String> get vratas; List<String> get specialEvents;
/// Create a copy of PanchangamModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PanchangamModelCopyWith<PanchangamModel> get copyWith => _$PanchangamModelCopyWithImpl<PanchangamModel>(this as PanchangamModel, _$identity);

  /// Serializes this PanchangamModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PanchangamModel&&(identical(other.date, date) || other.date == date)&&(identical(other.isAuspicious, isAuspicious) || other.isAuspicious == isAuspicious)&&(identical(other.tithi, tithi) || other.tithi == tithi)&&(identical(other.nakshatra, nakshatra) || other.nakshatra == nakshatra)&&(identical(other.yoga, yoga) || other.yoga == yoga)&&(identical(other.karana, karana) || other.karana == karana)&&(identical(other.sunrise, sunrise) || other.sunrise == sunrise)&&(identical(other.sunset, sunset) || other.sunset == sunset)&&(identical(other.moonrise, moonrise) || other.moonrise == moonrise)&&(identical(other.moonset, moonset) || other.moonset == moonset)&&(identical(other.rahuKalam, rahuKalam) || other.rahuKalam == rahuKalam)&&(identical(other.gulikaKalam, gulikaKalam) || other.gulikaKalam == gulikaKalam)&&(identical(other.yamagandam, yamagandam) || other.yamagandam == yamagandam)&&(identical(other.abhijitMuhurta, abhijitMuhurta) || other.abhijitMuhurta == abhijitMuhurta)&&const DeepCollectionEquality().equals(other.festivals, festivals)&&const DeepCollectionEquality().equals(other.vratas, vratas)&&const DeepCollectionEquality().equals(other.specialEvents, specialEvents));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,isAuspicious,tithi,nakshatra,yoga,karana,sunrise,sunset,moonrise,moonset,rahuKalam,gulikaKalam,yamagandam,abhijitMuhurta,const DeepCollectionEquality().hash(festivals),const DeepCollectionEquality().hash(vratas),const DeepCollectionEquality().hash(specialEvents));

@override
String toString() {
  return 'PanchangamModel(date: $date, isAuspicious: $isAuspicious, tithi: $tithi, nakshatra: $nakshatra, yoga: $yoga, karana: $karana, sunrise: $sunrise, sunset: $sunset, moonrise: $moonrise, moonset: $moonset, rahuKalam: $rahuKalam, gulikaKalam: $gulikaKalam, yamagandam: $yamagandam, abhijitMuhurta: $abhijitMuhurta, festivals: $festivals, vratas: $vratas, specialEvents: $specialEvents)';
}


}

/// @nodoc
abstract mixin class $PanchangamModelCopyWith<$Res>  {
  factory $PanchangamModelCopyWith(PanchangamModel value, $Res Function(PanchangamModel) _then) = _$PanchangamModelCopyWithImpl;
@useResult
$Res call({
 DateTime date, bool isAuspicious, String tithi, String nakshatra, String yoga, String karana, String sunrise, String sunset, String moonrise, String moonset, String rahuKalam, String gulikaKalam, String yamagandam, String abhijitMuhurta, List<String> festivals, List<String> vratas, List<String> specialEvents
});




}
/// @nodoc
class _$PanchangamModelCopyWithImpl<$Res>
    implements $PanchangamModelCopyWith<$Res> {
  _$PanchangamModelCopyWithImpl(this._self, this._then);

  final PanchangamModel _self;
  final $Res Function(PanchangamModel) _then;

/// Create a copy of PanchangamModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? date = null,Object? isAuspicious = null,Object? tithi = null,Object? nakshatra = null,Object? yoga = null,Object? karana = null,Object? sunrise = null,Object? sunset = null,Object? moonrise = null,Object? moonset = null,Object? rahuKalam = null,Object? gulikaKalam = null,Object? yamagandam = null,Object? abhijitMuhurta = null,Object? festivals = null,Object? vratas = null,Object? specialEvents = null,}) {
  return _then(_self.copyWith(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,isAuspicious: null == isAuspicious ? _self.isAuspicious : isAuspicious // ignore: cast_nullable_to_non_nullable
as bool,tithi: null == tithi ? _self.tithi : tithi // ignore: cast_nullable_to_non_nullable
as String,nakshatra: null == nakshatra ? _self.nakshatra : nakshatra // ignore: cast_nullable_to_non_nullable
as String,yoga: null == yoga ? _self.yoga : yoga // ignore: cast_nullable_to_non_nullable
as String,karana: null == karana ? _self.karana : karana // ignore: cast_nullable_to_non_nullable
as String,sunrise: null == sunrise ? _self.sunrise : sunrise // ignore: cast_nullable_to_non_nullable
as String,sunset: null == sunset ? _self.sunset : sunset // ignore: cast_nullable_to_non_nullable
as String,moonrise: null == moonrise ? _self.moonrise : moonrise // ignore: cast_nullable_to_non_nullable
as String,moonset: null == moonset ? _self.moonset : moonset // ignore: cast_nullable_to_non_nullable
as String,rahuKalam: null == rahuKalam ? _self.rahuKalam : rahuKalam // ignore: cast_nullable_to_non_nullable
as String,gulikaKalam: null == gulikaKalam ? _self.gulikaKalam : gulikaKalam // ignore: cast_nullable_to_non_nullable
as String,yamagandam: null == yamagandam ? _self.yamagandam : yamagandam // ignore: cast_nullable_to_non_nullable
as String,abhijitMuhurta: null == abhijitMuhurta ? _self.abhijitMuhurta : abhijitMuhurta // ignore: cast_nullable_to_non_nullable
as String,festivals: null == festivals ? _self.festivals : festivals // ignore: cast_nullable_to_non_nullable
as List<String>,vratas: null == vratas ? _self.vratas : vratas // ignore: cast_nullable_to_non_nullable
as List<String>,specialEvents: null == specialEvents ? _self.specialEvents : specialEvents // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [PanchangamModel].
extension PanchangamModelPatterns on PanchangamModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PanchangamModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PanchangamModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PanchangamModel value)  $default,){
final _that = this;
switch (_that) {
case _PanchangamModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PanchangamModel value)?  $default,){
final _that = this;
switch (_that) {
case _PanchangamModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( DateTime date,  bool isAuspicious,  String tithi,  String nakshatra,  String yoga,  String karana,  String sunrise,  String sunset,  String moonrise,  String moonset,  String rahuKalam,  String gulikaKalam,  String yamagandam,  String abhijitMuhurta,  List<String> festivals,  List<String> vratas,  List<String> specialEvents)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PanchangamModel() when $default != null:
return $default(_that.date,_that.isAuspicious,_that.tithi,_that.nakshatra,_that.yoga,_that.karana,_that.sunrise,_that.sunset,_that.moonrise,_that.moonset,_that.rahuKalam,_that.gulikaKalam,_that.yamagandam,_that.abhijitMuhurta,_that.festivals,_that.vratas,_that.specialEvents);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( DateTime date,  bool isAuspicious,  String tithi,  String nakshatra,  String yoga,  String karana,  String sunrise,  String sunset,  String moonrise,  String moonset,  String rahuKalam,  String gulikaKalam,  String yamagandam,  String abhijitMuhurta,  List<String> festivals,  List<String> vratas,  List<String> specialEvents)  $default,) {final _that = this;
switch (_that) {
case _PanchangamModel():
return $default(_that.date,_that.isAuspicious,_that.tithi,_that.nakshatra,_that.yoga,_that.karana,_that.sunrise,_that.sunset,_that.moonrise,_that.moonset,_that.rahuKalam,_that.gulikaKalam,_that.yamagandam,_that.abhijitMuhurta,_that.festivals,_that.vratas,_that.specialEvents);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( DateTime date,  bool isAuspicious,  String tithi,  String nakshatra,  String yoga,  String karana,  String sunrise,  String sunset,  String moonrise,  String moonset,  String rahuKalam,  String gulikaKalam,  String yamagandam,  String abhijitMuhurta,  List<String> festivals,  List<String> vratas,  List<String> specialEvents)?  $default,) {final _that = this;
switch (_that) {
case _PanchangamModel() when $default != null:
return $default(_that.date,_that.isAuspicious,_that.tithi,_that.nakshatra,_that.yoga,_that.karana,_that.sunrise,_that.sunset,_that.moonrise,_that.moonset,_that.rahuKalam,_that.gulikaKalam,_that.yamagandam,_that.abhijitMuhurta,_that.festivals,_that.vratas,_that.specialEvents);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PanchangamModel implements PanchangamModel {
  const _PanchangamModel({required this.date, required this.isAuspicious, required this.tithi, required this.nakshatra, required this.yoga, required this.karana, required this.sunrise, required this.sunset, required this.moonrise, required this.moonset, required this.rahuKalam, required this.gulikaKalam, required this.yamagandam, required this.abhijitMuhurta, final  List<String> festivals = const [], final  List<String> vratas = const [], final  List<String> specialEvents = const []}): _festivals = festivals,_vratas = vratas,_specialEvents = specialEvents;
  factory _PanchangamModel.fromJson(Map<String, dynamic> json) => _$PanchangamModelFromJson(json);

@override final  DateTime date;
@override final  bool isAuspicious;
@override final  String tithi;
@override final  String nakshatra;
@override final  String yoga;
@override final  String karana;
@override final  String sunrise;
@override final  String sunset;
@override final  String moonrise;
@override final  String moonset;
@override final  String rahuKalam;
@override final  String gulikaKalam;
@override final  String yamagandam;
@override final  String abhijitMuhurta;
 final  List<String> _festivals;
@override@JsonKey() List<String> get festivals {
  if (_festivals is EqualUnmodifiableListView) return _festivals;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_festivals);
}

 final  List<String> _vratas;
@override@JsonKey() List<String> get vratas {
  if (_vratas is EqualUnmodifiableListView) return _vratas;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_vratas);
}

 final  List<String> _specialEvents;
@override@JsonKey() List<String> get specialEvents {
  if (_specialEvents is EqualUnmodifiableListView) return _specialEvents;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_specialEvents);
}


/// Create a copy of PanchangamModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PanchangamModelCopyWith<_PanchangamModel> get copyWith => __$PanchangamModelCopyWithImpl<_PanchangamModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PanchangamModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PanchangamModel&&(identical(other.date, date) || other.date == date)&&(identical(other.isAuspicious, isAuspicious) || other.isAuspicious == isAuspicious)&&(identical(other.tithi, tithi) || other.tithi == tithi)&&(identical(other.nakshatra, nakshatra) || other.nakshatra == nakshatra)&&(identical(other.yoga, yoga) || other.yoga == yoga)&&(identical(other.karana, karana) || other.karana == karana)&&(identical(other.sunrise, sunrise) || other.sunrise == sunrise)&&(identical(other.sunset, sunset) || other.sunset == sunset)&&(identical(other.moonrise, moonrise) || other.moonrise == moonrise)&&(identical(other.moonset, moonset) || other.moonset == moonset)&&(identical(other.rahuKalam, rahuKalam) || other.rahuKalam == rahuKalam)&&(identical(other.gulikaKalam, gulikaKalam) || other.gulikaKalam == gulikaKalam)&&(identical(other.yamagandam, yamagandam) || other.yamagandam == yamagandam)&&(identical(other.abhijitMuhurta, abhijitMuhurta) || other.abhijitMuhurta == abhijitMuhurta)&&const DeepCollectionEquality().equals(other._festivals, _festivals)&&const DeepCollectionEquality().equals(other._vratas, _vratas)&&const DeepCollectionEquality().equals(other._specialEvents, _specialEvents));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,isAuspicious,tithi,nakshatra,yoga,karana,sunrise,sunset,moonrise,moonset,rahuKalam,gulikaKalam,yamagandam,abhijitMuhurta,const DeepCollectionEquality().hash(_festivals),const DeepCollectionEquality().hash(_vratas),const DeepCollectionEquality().hash(_specialEvents));

@override
String toString() {
  return 'PanchangamModel(date: $date, isAuspicious: $isAuspicious, tithi: $tithi, nakshatra: $nakshatra, yoga: $yoga, karana: $karana, sunrise: $sunrise, sunset: $sunset, moonrise: $moonrise, moonset: $moonset, rahuKalam: $rahuKalam, gulikaKalam: $gulikaKalam, yamagandam: $yamagandam, abhijitMuhurta: $abhijitMuhurta, festivals: $festivals, vratas: $vratas, specialEvents: $specialEvents)';
}


}

/// @nodoc
abstract mixin class _$PanchangamModelCopyWith<$Res> implements $PanchangamModelCopyWith<$Res> {
  factory _$PanchangamModelCopyWith(_PanchangamModel value, $Res Function(_PanchangamModel) _then) = __$PanchangamModelCopyWithImpl;
@override @useResult
$Res call({
 DateTime date, bool isAuspicious, String tithi, String nakshatra, String yoga, String karana, String sunrise, String sunset, String moonrise, String moonset, String rahuKalam, String gulikaKalam, String yamagandam, String abhijitMuhurta, List<String> festivals, List<String> vratas, List<String> specialEvents
});




}
/// @nodoc
class __$PanchangamModelCopyWithImpl<$Res>
    implements _$PanchangamModelCopyWith<$Res> {
  __$PanchangamModelCopyWithImpl(this._self, this._then);

  final _PanchangamModel _self;
  final $Res Function(_PanchangamModel) _then;

/// Create a copy of PanchangamModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? date = null,Object? isAuspicious = null,Object? tithi = null,Object? nakshatra = null,Object? yoga = null,Object? karana = null,Object? sunrise = null,Object? sunset = null,Object? moonrise = null,Object? moonset = null,Object? rahuKalam = null,Object? gulikaKalam = null,Object? yamagandam = null,Object? abhijitMuhurta = null,Object? festivals = null,Object? vratas = null,Object? specialEvents = null,}) {
  return _then(_PanchangamModel(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as DateTime,isAuspicious: null == isAuspicious ? _self.isAuspicious : isAuspicious // ignore: cast_nullable_to_non_nullable
as bool,tithi: null == tithi ? _self.tithi : tithi // ignore: cast_nullable_to_non_nullable
as String,nakshatra: null == nakshatra ? _self.nakshatra : nakshatra // ignore: cast_nullable_to_non_nullable
as String,yoga: null == yoga ? _self.yoga : yoga // ignore: cast_nullable_to_non_nullable
as String,karana: null == karana ? _self.karana : karana // ignore: cast_nullable_to_non_nullable
as String,sunrise: null == sunrise ? _self.sunrise : sunrise // ignore: cast_nullable_to_non_nullable
as String,sunset: null == sunset ? _self.sunset : sunset // ignore: cast_nullable_to_non_nullable
as String,moonrise: null == moonrise ? _self.moonrise : moonrise // ignore: cast_nullable_to_non_nullable
as String,moonset: null == moonset ? _self.moonset : moonset // ignore: cast_nullable_to_non_nullable
as String,rahuKalam: null == rahuKalam ? _self.rahuKalam : rahuKalam // ignore: cast_nullable_to_non_nullable
as String,gulikaKalam: null == gulikaKalam ? _self.gulikaKalam : gulikaKalam // ignore: cast_nullable_to_non_nullable
as String,yamagandam: null == yamagandam ? _self.yamagandam : yamagandam // ignore: cast_nullable_to_non_nullable
as String,abhijitMuhurta: null == abhijitMuhurta ? _self.abhijitMuhurta : abhijitMuhurta // ignore: cast_nullable_to_non_nullable
as String,festivals: null == festivals ? _self._festivals : festivals // ignore: cast_nullable_to_non_nullable
as List<String>,vratas: null == vratas ? _self._vratas : vratas // ignore: cast_nullable_to_non_nullable
as List<String>,specialEvents: null == specialEvents ? _self._specialEvents : specialEvents // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}

// dart format on
