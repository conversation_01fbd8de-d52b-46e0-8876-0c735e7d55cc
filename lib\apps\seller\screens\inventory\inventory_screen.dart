import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../presentation/cubits/inventory/inventory_cubit.dart';
import '../../../../../shared/core/localization/app_localizations.dart';
import '../../../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../../../shared/ui_components/errors/error_message.dart';
import 'widgets/inventory_list.dart';
import 'widgets/low_stock_alert.dart';

class InventoryScreen extends StatelessWidget {
  const InventoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n('inventory')),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<InventoryCubit>().loadInventory();
            },
          ),
        ],
      ),
      body: Bloc<PERSON><PERSON>er<InventoryCubit, InventoryState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const LoadingIndicator();
          }

          if (state.error != null) {
            return ErrorMessage(
              message: state.error!,
              onRetry: () {
                context.read<InventoryCubit>().loadInventory();
              },
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              await context.read<InventoryCubit>().loadInventory();
            },
            child: CustomScrollView(
              slivers: [
                if (state.lowStockItems.isNotEmpty)
                  SliverToBoxAdapter(
                    child: LowStockAlert(
                      items: state.lowStockItems,
                      onDismiss: () {
                        context.read<InventoryCubit>().dismissLowStockAlert();
                      },
                    ),
                  ),
                SliverPadding(
                  padding: const EdgeInsets.all(16),
                  sliver: InventoryList(
                    inventory: state.inventory,
                    onStockUpdate: (productId, newStock) {
                      context.read<InventoryCubit>().updateStock(
                            productId,
                            newStock,
                          );
                    },
                    onAddStock: (productId, quantity) {
                      context.read<InventoryCubit>().addStock(
                            productId,
                            quantity,
                          );
                    },
                    onRemoveStock: (productId, quantity) {
                      context.read<InventoryCubit>().removeStock(
                            productId,
                            quantity,
                          );
                    },
                    onSetLowStockAlert: (productId, enabled) {
                      context.read<InventoryCubit>().setLowStockAlert(
                            productId,
                            enabled,
                          );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
