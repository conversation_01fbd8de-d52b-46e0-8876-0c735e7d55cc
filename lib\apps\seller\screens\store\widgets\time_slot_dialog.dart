import 'package:flutter/material.dart';
import 'package:shivish/apps/seller/domain/models/schedule_model.dart';
import 'package:timezone/timezone.dart' as tz;

class TimeSlotDialog extends StatefulWidget {
  final TimeSlot? initialSlot;
  final ValueChanged<TimeSlot> onSave;
  final String timeZone;

  const TimeSlotDialog({
    super.key,
    this.initialSlot,
    required this.onSave,
    required this.timeZone,
  });

  @override
  State<TimeSlotDialog> createState() => _TimeSlotDialogState();
}

class _TimeSlotDialogState extends State<TimeSlotDialog> {
  late TimeOfDay _openTime;
  late TimeOfDay _closeTime;
  TimeOfDay? _breakStart;
  TimeOfDay? _breakEnd;
  bool _hasBreak = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialSlot != null) {
      _openTime = _parseTimeString(widget.initialSlot!.openTime);
      _closeTime = _parseTimeString(widget.initialSlot!.closeTime);
      if (widget.initialSlot!.breakStart != null &&
          widget.initialSlot!.breakEnd != null) {
        _breakStart = _parseTimeString(widget.initialSlot!.breakStart!);
        _breakEnd = _parseTimeString(widget.initialSlot!.breakEnd!);
        _hasBreak = true;
      }
    } else {
      _openTime = const TimeOfDay(hour: 9, minute: 0);
      _closeTime = const TimeOfDay(hour: 17, minute: 0);
    }
  }

  TimeOfDay _parseTimeString(String time) {
    final parts = time.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  TimeOfDay _convertToLocalTime(TimeOfDay time) {
    final now = DateTime.now();
    final location = tz.getLocation(widget.timeZone);
    final tzDateTime = tz.TZDateTime(
      location,
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );
    final localDateTime = tzDateTime.toLocal();
    return TimeOfDay(hour: localDateTime.hour, minute: localDateTime.minute);
  }

  TimeOfDay _convertToTimeZone(TimeOfDay time) {
    final now = DateTime.now();
    final location = tz.getLocation(widget.timeZone);
    final localDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );
    final tzDateTime = tz.TZDateTime.from(localDateTime, location);
    return TimeOfDay(hour: tzDateTime.hour, minute: tzDateTime.minute);
  }

  Future<void> _selectTime(
    BuildContext context,
    TimeOfDay initialTime,
    void Function(TimeOfDay) onSelect,
  ) async {
    final localTime = _convertToLocalTime(initialTime);
    final TimeOfDay? time = await showTimePicker(
      context: context,
      initialTime: localTime,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            alwaysUse24HourFormat: true,
          ),
          child: child!,
        );
      },
    );
    if (time != null) {
      final tzTime = _convertToTimeZone(time);
      onSelect(tzTime);
    }
  }

  bool _validateTimes() {
    if (_openTime.hour > _closeTime.hour ||
        (_openTime.hour == _closeTime.hour &&
            _openTime.minute >= _closeTime.minute)) {
      return false;
    }

    if (_hasBreak && _breakStart != null && _breakEnd != null) {
      if (_breakStart!.hour < _openTime.hour ||
          _breakEnd!.hour > _closeTime.hour) {
        return false;
      }

      if (_breakStart!.hour > _breakEnd!.hour ||
          (_breakStart!.hour == _breakEnd!.hour &&
              _breakStart!.minute >= _breakEnd!.minute)) {
        return false;
      }
    }

    return true;
  }

  void _handleSave() {
    if (!_validateTimes()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Invalid time range. Please check your times.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    widget.onSave(
      TimeSlot(
        openTime: _formatTimeOfDay(_openTime),
        closeTime: _formatTimeOfDay(_closeTime),
        breakStart: _hasBreak ? _formatTimeOfDay(_breakStart!) : null,
        breakEnd: _hasBreak ? _formatTimeOfDay(_breakEnd!) : null,
      ),
    );

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title:
          Text(widget.initialSlot == null ? 'Add Time Slot' : 'Edit Time Slot'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Opening Time'),
              trailing: TextButton(
                onPressed: () => _selectTime(
                  context,
                  _openTime,
                  (time) => setState(() => _openTime = time),
                ),
                child: Text(_formatTimeOfDay(_openTime)),
              ),
            ),
            ListTile(
              title: const Text('Closing Time'),
              trailing: TextButton(
                onPressed: () => _selectTime(
                  context,
                  _closeTime,
                  (time) => setState(() => _closeTime = time),
                ),
                child: Text(_formatTimeOfDay(_closeTime)),
              ),
            ),
            SwitchListTile(
              title: const Text('Add Break Time'),
              value: _hasBreak,
              onChanged: (value) {
                setState(() {
                  _hasBreak = value;
                  if (value && _breakStart == null) {
                    _breakStart = TimeOfDay(
                      hour: (_openTime.hour + _closeTime.hour) ~/ 2,
                      minute: 0,
                    );
                    _breakEnd = TimeOfDay(
                      hour: _breakStart!.hour + 1,
                      minute: 0,
                    );
                  }
                });
              },
            ),
            if (_hasBreak) ...[
              ListTile(
                title: const Text('Break Start'),
                trailing: TextButton(
                  onPressed: () => _selectTime(
                    context,
                    _breakStart!,
                    (time) => setState(() => _breakStart = time),
                  ),
                  child: Text(_formatTimeOfDay(_breakStart!)),
                ),
              ),
              ListTile(
                title: const Text('Break End'),
                trailing: TextButton(
                  onPressed: () => _selectTime(
                    context,
                    _breakEnd!,
                    (time) => setState(() => _breakEnd = time),
                  ),
                  child: Text(_formatTimeOfDay(_breakEnd!)),
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _handleSave,
          child: const Text('Save'),
        ),
      ],
    );
  }
}
