import '../domain/entities/cart/cart_model.dart';
import '../../../shared/models/product/product_model.dart';

/// Cart repository interface
abstract class CartRepository {
  /// Gets the current cart
  Future<CartModel> getCart();

  /// Adds a product to the cart
  Future<void> addToCart({
    required ProductModel product,
    required int quantity,
    String? sellerId,
    String? notes,
  });

  /// Updates the quantity of a product in the cart
  Future<void> updateQuantity({
    required ProductModel product,
    required int quantity,
  });

  /// Removes a product from the cart
  Future<void> removeFromCart(ProductModel product);

  /// Clears the cart
  Future<void> clearCart();

  /// Gets the total price of the cart
  Future<double> getTotalPrice();

  /// Gets the total quantity of items in the cart
  Future<int> getTotalQuantity();

  /// Updates the seller for a product in the cart
  Future<void> updateSeller({
    required ProductModel product,
    required String sellerId,
  });

  /// Updates the notes for a product in the cart
  Future<void> updateNotes({
    required ProductModel product,
    required String notes,
  });
}
