import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../../shared/models/inventory_model.dart';
import '../../../domain/repositories/inventory_repository.dart';
import '../../../../../shared/services/auth/auth_service.dart';

part 'inventory_cubit.freezed.dart';

@freezed
sealed class InventoryState with _$InventoryState {
  const factory InventoryState({
    @Default([]) List<InventoryModel> inventory,
    @Default([]) List<InventoryModel> lowStockItems,
    @Default(false) bool isLoading,
    String? error,
  }) = _InventoryState;
}

class InventoryCubit extends Cubit<InventoryState> {
  final InventoryRepository _repository;
  final AuthService _authService;

  InventoryCubit(this._repository, this._authService)
      : super(const InventoryState()) {
    loadInventory();
  }

  Future<void> loadInventory() async {
    emit(state.copyWith(isLoading: true, error: null));
    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final inventory = await _repository.getInventory();
      final lowStockItems = await _repository.getLowStockItems(currentUser.id);
      emit(state.copyWith(
        inventory: inventory,
        lowStockItems: lowStockItems,
        isLoading: false,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> addStock(String inventoryId, int quantity) async {
    try {
      await _repository.addStock(inventoryId, quantity);
      await loadInventory();
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> removeStock(String inventoryId, int quantity) async {
    try {
      await _repository.removeStock(inventoryId, quantity);
      await loadInventory();
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> setLowStockAlert(String inventoryId, bool enabled) async {
    try {
      await _repository.setLowStockAlert(inventoryId, enabled);
      await loadInventory();
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> updateStock(String inventoryId, int newStock) async {
    try {
      final currentInventory = await _repository.getInventoryById(inventoryId);
      final difference = newStock - currentInventory.currentStock;
      if (difference > 0) {
        await addStock(inventoryId, difference);
      } else if (difference < 0) {
        await removeStock(inventoryId, -difference);
      }
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  void dismissLowStockAlert() {
    emit(state.copyWith(lowStockItems: []));
  }
}
