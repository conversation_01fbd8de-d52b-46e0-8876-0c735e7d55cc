import 'package:flutter/foundation.dart';
import '../../../../shared/models/order/order_model.dart';
import '../../domain/repositories/order_repository.dart';
import '../../../../shared/database/services/database_service.dart';

/// Implementation of [OrderRepository]
class OrderRepositoryImpl implements OrderRepository {
  /// Creates an [OrderRepositoryImpl]
  OrderRepositoryImpl({
    required DatabaseService databaseService,
  }) : _databaseService = databaseService;

  final DatabaseService _databaseService;

  @override
  Future<List<OrderModel>> getOrders(String sellerId) async {
    try {
      final orders = await _databaseService.getAll(
        'orders',
        where: 'seller_id = @param0',
        whereParams: [sellerId],
        orderBy: 'created_at DESC',
      );

      return orders
          .map((data) => OrderModel.fromJson(data))
          .toList();
    } catch (e) {
      debugPrint('Error getting orders: $e');
      rethrow;
    }
  }

  @override
  Future<OrderModel?> getOrderById(String id) async {
    try {
      final doc = await _databaseService.find('orders', id);

      if (doc == null) {
        return null;
      }

      return OrderModel.fromJson(doc);
    } catch (e) {
      debugPrint('Error getting order by ID: $e');
      rethrow;
    }
  }

  @override
  Future<OrderModel> updateOrderStatus(String id, OrderStatus status) async {
    try {
      await _databaseService.update('orders', id, {
        'status': status.name,
        'updated_at': DateTime.now().toIso8601String(),
      });

      final doc = await _databaseService.find('orders', id);
      if (doc == null) {
        throw Exception('Order not found after update');
      }
      return OrderModel.fromJson(doc);
    } catch (e) {
      debugPrint('Error updating order status: $e');
      rethrow;
    }
  }

  @override
  Future<OrderModel> updatePaymentStatus(
      String id, PaymentStatus status) async {
    try {
      await _databaseService.update('orders', id, {
        'payment_status': status.name,
        'updated_at': DateTime.now().toIso8601String(),
      });

      final doc = await _databaseService.find('orders', id);
      if (doc == null) {
        throw Exception('Order not found after update');
      }
      return OrderModel.fromJson(doc);
    } catch (e) {
      debugPrint('Error updating payment status: $e');
      rethrow;
    }
  }

  @override
  Future<OrderModel> addTrackingInfo(
    String id, {
    required String trackingNumber,
    required String shippingProvider,
    DateTime? estimatedDeliveryDate,
  }) async {
    try {
      await _databaseService.update('orders', id, {
        'tracking_number': trackingNumber,
        'shipping_provider': shippingProvider,
        'estimated_delivery_date': estimatedDeliveryDate?.toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      final doc = await _databaseService.find('orders', id);
      if (doc == null) {
        throw Exception('Order not found after update');
      }
      return OrderModel.fromJson(doc);
    } catch (e) {
      debugPrint('Error adding tracking info: $e');
      rethrow;
    }
  }

  @override
  Future<OrderModel> cancelOrder(String id, {String? reason}) async {
    try {
      await _databaseService.update('orders', id, {
        'status': OrderStatus.cancelled.name,
        'cancellation_reason': reason,
        'updated_at': DateTime.now().toIso8601String(),
      });

      final doc = await _databaseService.find('orders', id);
      if (doc == null) {
        throw Exception('Order not found after update');
      }
      return OrderModel.fromJson(doc);
    } catch (e) {
      debugPrint('Error cancelling order: $e');
      rethrow;
    }
  }

  @override
  Future<OrderModel> refundOrder(String id, {String? reason}) async {
    try {
      await _databaseService.update('orders', id, {
        'status': OrderStatus.refunded.name,
        'payment_status': PaymentStatus.refunded.name,
        'refund_reason': reason,
        'updated_at': DateTime.now().toIso8601String(),
      });

      final doc = await _databaseService.find('orders', id);
      if (doc == null) {
        throw Exception('Order not found after update');
      }
      return OrderModel.fromJson(doc);
    } catch (e) {
      debugPrint('Error refunding order: $e');
      rethrow;
    }
  }

  @override
  Future<OrderModel> addOrderNote(String id, String note) async {
    try {
      // Get current order to append to existing notes
      final currentOrder = await _databaseService.find('orders', id);
      if (currentOrder == null) {
        throw Exception('Order not found');
      }

      final currentNotes = (currentOrder['order_notes'] as List<dynamic>?)?.cast<String>() ?? <String>[];
      currentNotes.add(note);

      await _databaseService.update('orders', id, {
        'order_notes': currentNotes,
        'updated_at': DateTime.now().toIso8601String(),
      });

      final doc = await _databaseService.find('orders', id);
      if (doc == null) {
        throw Exception('Order not found after update');
      }
      return OrderModel.fromJson(doc);
    } catch (e) {
      debugPrint('Error adding order note: $e');
      rethrow;
    }
  }
}
