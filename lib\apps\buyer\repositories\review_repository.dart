import 'package:injectable/injectable.dart';
import 'package:shivish/shared/models/review.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';

@injectable
class ReviewRepository {
  final DatabaseService _databaseService;
  final _logger = getLogger('ReviewRepository');

  ReviewRepository({DatabaseService? databaseService})
      : _databaseService = databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment());

  Future<List<Review>> getProductReviews(String productId) async {
    try {
      final allReviews = await _databaseService.getAll('reviews');

      final productReviews = allReviews
          .where((reviewData) => reviewData['productId'] == productId)
          .toList();

      // Sort by createdAt (descending)
      productReviews.sort((a, b) {
        final createdAtA = DateTime.tryParse(a['createdAt'] as String? ?? '') ?? DateTime.now();
        final createdAtB = DateTime.tryParse(b['createdAt'] as String? ?? '') ?? DateTime.now();
        return createdAtB.compareTo(createdAtA);
      });

      return productReviews.map((reviewData) => Review.fromJson(reviewData)).toList();
    } catch (e) {
      _logger.severe('Failed to get product reviews: $e');
      throw Exception('Failed to get product reviews: $e');
    }
  }

  Future<List<Review>> getUserReviews(String userId) async {
    try {
      final allReviews = await _databaseService.getAll('reviews');

      final userReviews = allReviews
          .where((reviewData) => reviewData['userId'] == userId)
          .toList();

      // Sort by createdAt (descending)
      userReviews.sort((a, b) {
        final createdAtA = DateTime.tryParse(a['createdAt'] as String? ?? '') ?? DateTime.now();
        final createdAtB = DateTime.tryParse(b['createdAt'] as String? ?? '') ?? DateTime.now();
        return createdAtB.compareTo(createdAtA);
      });

      return userReviews.map((reviewData) => Review.fromJson(reviewData)).toList();
    } catch (e) {
      _logger.severe('Failed to get user reviews: $e');
      throw Exception('Failed to get user reviews: $e');
    }
  }

  Future<void> createReview(Review review) async {
    try {
      final reviewData = {
        'id': review.id,
        ...review.toJson(),
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };
      await _databaseService.create('reviews', reviewData);
    } catch (e) {
      _logger.severe('Failed to create review: $e');
      throw Exception('Failed to create review: $e');
    }
  }

  Future<void> updateReview(Review review) async {
    try {
      final reviewData = {
        'id': review.id,
        ...review.toJson(),
        'updatedAt': DateTime.now().toIso8601String(),
      };
      await _databaseService.update('reviews', review.id, reviewData);
    } catch (e) {
      _logger.severe('Failed to update review: $e');
      throw Exception('Failed to update review: $e');
    }
  }

  Future<void> deleteReview(String reviewId) async {
    try {
      await _databaseService.delete('reviews', reviewId);
    } catch (e) {
      _logger.severe('Failed to delete review: $e');
      throw Exception('Failed to delete review: $e');
    }
  }

  Future<void> voteReview(String reviewId, bool isHelpful) async {
    try {
      final reviewData = await _databaseService.find('reviews', reviewId);
      if (reviewData == null) {
        throw Exception('Review not found');
      }

      final review = Review.fromJson(reviewData);

      final updatedData = {
        ...reviewData,
        if (isHelpful)
          'helpfulVotes': review.helpfulVotes + 1
        else
          'notHelpfulVotes': review.notHelpfulVotes + 1,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      await _databaseService.update('reviews', reviewId, updatedData);
    } catch (e) {
      _logger.severe('Failed to vote on review: $e');
      throw Exception('Failed to vote on review: $e');
    }
  }

  Stream<List<Review>> watchProductReviews(String productId) {
    return _databaseService.watchCollection(
      'reviews',
      where: 'productId = ?',
      whereParams: [productId],
      orderBy: 'createdAt DESC',
    ).map((reviewsData) =>
        reviewsData.map((reviewData) => Review.fromJson(reviewData)).toList());
  }
}
