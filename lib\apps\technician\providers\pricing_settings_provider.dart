import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final pricingSettingsProvider =
    AsyncNotifierProvider<PricingSettingsNotifier, Map<String, dynamic>>(() {
  return PricingSettingsNotifier();
});

class PricingSettingsNotifier extends AsyncNotifier<Map<String, dynamic>> {
  late final DatabaseService _databaseService;

  @override
  Future<Map<String, dynamic>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadPricingSettings();
  }

  Future<Map<String, dynamic>> _loadPricingSettings() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('users', userId);
      final settings = userData?['pricing_settings'] as Map<String, dynamic>?;

      return {
        'base_price': settings?['base_price'] ?? 0.0,
        'hourly_rate': settings?['hourly_rate'] ?? 0.0,
        'minimum_price': settings?['minimum_price'] ?? 0.0,
        'maximum_price': settings?['maximum_price'] ?? 0.0,
        'include_travel_fee': settings?['include_travel_fee'] ?? true,
        'travel_fee': settings?['travel_fee'] ?? 0.0,
        'include_emergency_fee': settings?['include_emergency_fee'] ?? true,
        'emergency_fee': settings?['emergency_fee'] ?? 0.0,
        'show_price_range': settings?['show_price_range'] ?? true,
        'currency': settings?['currency'] ?? 'USD',
        'discount_percentage': settings?['discount_percentage'] ?? 0.0,
      };
    } catch (e) {
      debugPrint('Failed to load pricing settings: $e');
      throw Exception('Failed to load pricing settings: $e');
    }
  }

  Future<void> savePricingSettings(Map<String, dynamic> settings) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Validate pricing settings
      final basePrice = settings['base_price'] as double? ?? 0.0;
      final hourlyRate = settings['hourly_rate'] as double? ?? 0.0;
      final minPrice = settings['minimum_price'] as double? ?? 0.0;
      final maxPrice = settings['maximum_price'] as double? ?? 0.0;

      if (basePrice < 0 || hourlyRate < 0 || minPrice < 0 || maxPrice < 0) {
        throw Exception('Prices cannot be negative');
      }

      if (maxPrice > 0 && minPrice > maxPrice) {
        throw Exception('Minimum price cannot be greater than maximum price');
      }

      await _databaseService.update('users', userId, {
        'pricing_settings': settings,
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData(settings);
    } catch (e) {
      debugPrint('Failed to save pricing settings: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}
