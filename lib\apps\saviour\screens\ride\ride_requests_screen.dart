import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/delivery/delivery_person_model.dart';
import '../../../../shared/utils/logger.dart';
import '../../providers/saviour_auth_provider.dart';
import '../../providers/location_provider.dart';
import '../../providers/ride_request_provider.dart';
import '../../saviour_routes.dart';
import 'widgets/ride_request_card.dart';

final _logger = getLogger('RideRequestsScreen');

class RideRequestsScreen extends ConsumerStatefulWidget {
  final bool showBackButton;

  const RideRequestsScreen({super.key, this.showBackButton = true});

  @override
  ConsumerState<RideRequestsScreen> createState() => _RideRequestsScreenState();
}

class _RideRequestsScreenState extends ConsumerState<RideRequestsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Ensure location tracking is active if delivery person is available
    final authState = ref.read(saviourAuthStateProvider);
    if (authState.deliveryPerson?.status == DeliveryPersonStatus.available) {
      ref.read(saviourLocationProvider.notifier).startTracking();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(saviourAuthStateProvider);
    // We need to watch the location provider to rebuild when location changes
    // even though we don't directly use the value
    ref.watch(saviourLocationProvider);
    final nearbyRideRequestsAsync = ref.watch(nearbyRideRequestsProvider);
    final activeRideRequestsAsync = ref.watch(activeRideRequestsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Ride Requests'),
        leading: widget.showBackButton
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  // Check if we can pop the current route
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  } else {
                    // Navigate to home screen when back button is pressed
                    context.go(SaviourRoutes.home);
                  }
                },
              )
            : null,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Nearby'),
            Tab(text: 'Active'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Nearby Requests Tab
          _buildNearbyRequestsTab(nearbyRideRequestsAsync, authState),

          // Active Rides Tab
          _buildActiveRidesTab(activeRideRequestsAsync),
        ],
      ),
      floatingActionButton: _buildStatusToggleButton(authState),
    );
  }

  Widget? _buildStatusToggleButton(dynamic authState) {
    // Only show the toggle button if we're on the nearby tab
    if (_tabController.index != 0) return null;

    final deliveryPerson = authState.deliveryPerson;
    if (deliveryPerson == null) return null;

    final isAvailable = deliveryPerson.status == DeliveryPersonStatus.available;

    return FloatingActionButton.extended(
      onPressed: () {
        if (isAvailable) {
          // Set status to offline
          ref
              .read(saviourAuthStateProvider.notifier)
              .updateStatus(DeliveryPersonStatus.offline);
          ref.read(saviourLocationProvider.notifier).stopTracking();
        } else {
          // Set status to available
          ref
              .read(saviourAuthStateProvider.notifier)
              .updateStatus(DeliveryPersonStatus.available);
          ref.read(saviourLocationProvider.notifier).startTracking();
        }
      },
      icon: Icon(isAvailable ? Icons.pause : Icons.play_arrow),
      label: Text(isAvailable ? 'Go Offline' : 'Go Online'),
      backgroundColor: isAvailable ? Colors.orange : Colors.green,
    );
  }

  Widget _buildNearbyRequestsTab(
    AsyncValue<List<dynamic>> nearbyRequestsAsync,
    dynamic authState,
  ) {
    final deliveryPerson = authState.deliveryPerson;
    if (deliveryPerson == null) {
      return const Center(child: Text('Please complete your profile'));
    }

    final isAvailable = deliveryPerson.status == DeliveryPersonStatus.available;

    if (!isAvailable) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.info_outline, size: 64, color: Colors.grey),
              const SizedBox(height: 16),
              const Text(
                'You need to be available to see nearby ride requests',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  ref
                      .read(saviourAuthStateProvider.notifier)
                      .updateStatus(DeliveryPersonStatus.available);
                  ref.read(saviourLocationProvider.notifier).startTracking();
                },
                child: const Text('Go Available'),
              ),
            ],
          ),
        ),
      );
    }

    return nearbyRequestsAsync.when(
      data: (requests) {
        if (requests.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.search, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  const Text(
                    'No nearby ride requests found',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Pull down to refresh',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            // Force update location
            await ref
                .read(saviourLocationProvider.notifier)
                .forceUpdateLocation();
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: requests.length,
            itemBuilder: (context, index) {
              final request = requests[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: RideRequestCard(
                  request: request,
                  isActive: false,
                  onTap: () {
                    _showAcceptRequestDialog(request.id);
                  },
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) {
        _logger.severe('Error loading nearby requests: $error\n$stackTrace');
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text('Error loading nearby requests: $error'),
          ),
        );
      },
    );
  }

  Widget _buildActiveRidesTab(AsyncValue<List<dynamic>> activeRequestsAsync) {
    return activeRequestsAsync.when(
      data: (requests) {
        if (requests.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.directions_car, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'No active rides',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: requests.length,
          itemBuilder: (context, index) {
            final request = requests[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: RideRequestCard(
                request: request,
                isActive: true,
                onTap: () {
                  context.go(SaviourRoutes.getActiveRideRoute(request.id));
                },
              ),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) {
        _logger.severe('Error loading active requests: $error\n$stackTrace');
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text('Error loading active requests: $error'),
          ),
        );
      },
    );
  }

  Future<void> _showAcceptRequestDialog(String requestId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Accept Ride Request'),
        content: const Text(
          'Are you sure you want to accept this ride request? You will be responsible for picking up the passenger and completing the ride.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Accept'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(rideRequestActionsProvider).acceptRideRequest(requestId);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Ride request accepted'),
              backgroundColor: Colors.green,
            ),
          );

          // Navigate to active ride screen
          context.go(SaviourRoutes.getActiveRideRoute(requestId));
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error accepting ride request: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
