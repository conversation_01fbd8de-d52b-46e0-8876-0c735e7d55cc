import 'package:flutter/material.dart';
import '../../../../../shared/models/media/media_model.dart';
import 'package:intl/intl.dart';

class MediaListItem extends StatelessWidget {
  final MediaModel media;
  final VoidCallback onTap;

  const MediaListItem({
    super.key,
    required this.media,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      leading: _buildLeadingIcon(),
      title: Text(
        media.title,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _getMediaTypeText(),
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(height: 4),
          Text(
            'Uploaded ${_formatDate(media.uploadedAt)}',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
      trailing: _buildTrailingWidget(context),
    );
  }

  Widget _buildLeadingIcon() {
    return switch (media.type) {
      MediaType.image => SizedBox(
          width: 48,
          height: 48,
          child: Image.network(
            media.url,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => const Icon(
              Icons.image_not_supported,
            ),
          ),
        ),
      MediaType.video => SizedBox(
          width: 48,
          height: 48,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Image.network(
                media.url,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => const Icon(
                  Icons.video_library,
                ),
              ),
              const Icon(
                Icons.play_circle_outline,
                color: Colors.white,
              ),
            ],
          ),
        ),
      MediaType.audio => const Icon(Icons.audio_file),
    };
  }

  Widget _buildTrailingWidget(BuildContext context) {
    return Text(
      _getMediaTypeText(),
      style: Theme.of(context).textTheme.bodySmall,
    );
  }

  String _getMediaTypeText() {
    return switch (media.type) {
      MediaType.image => 'Image',
      MediaType.video => 'Video',
      MediaType.audio => 'Audio',
    };
  }

  String _formatDate(DateTime date) {
    return DateFormat.yMMMd().format(date);
  }
}
