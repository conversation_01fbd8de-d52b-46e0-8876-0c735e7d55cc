import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../admin_routes.dart';
import '../../../shared/screens/auth/enhanced_register_screen.dart';
import '../bloc/auth/auth_bloc.dart';

class AdminRegisterScreen extends StatefulWidget {
  final VoidCallback? onRegisterSuccess;

  const AdminRegisterScreen({
    super.key,
    this.onRegisterSuccess,
  });

  @override
  State<AdminRegisterScreen> createState() => _AdminRegisterScreenState();
}

class _AdminRegisterScreenState extends State<AdminRegisterScreen> {
  bool _isLoading = false;
  String? _errorMessage;

  void _onRegisterPressed(
      String email, String password, String displayName) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      debugPrint(
          'AdminRegisterScreen: Starting registration process for email: $email');

      // Request admin access using the AuthBloc
      context.read<AuthBloc>().add(RegisterAdminEvent(
            email: email.trim(),
            password: password,
            displayName: displayName.trim(),
            reason: 'New admin registration',
          ));

      // Note: The rest of the process will be handled by the BlocListener
    } catch (e) {
      debugPrint('AdminRegisterScreen: Error during registration: $e');
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  void _onLoginPressed() {
    context.go(AdminRoutes.login);
  }

  @override
  Widget build(BuildContext context) {
    // Get theme colors from admin theme
    final primaryColor = Theme.of(context).colorScheme.primary;
    final secondaryColor = Theme.of(context).colorScheme.secondary;

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthLoadingState) {
          setState(() {
            _isLoading = true;
          });
        } else if (state is AdminRegistrationSuccessState) {
          setState(() {
            _isLoading = false;
          });

          if (widget.onRegisterSuccess != null) {
            widget.onRegisterSuccess!();
          } else {
            if (state.isApproved) {
              // First admin - automatically approved
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                      'You are the first admin. Your account has been automatically approved.'),
                  duration: Duration(seconds: 5),
                ),
              );

              // Navigate to home screen
              context.go(AdminRoutes.home);
            } else {
              // Regular admin registration - needs approval
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                      'Registration request submitted. Please wait for approval.'),
                  duration: Duration(seconds: 5),
                ),
              );

              // Navigate back to login
              context.go(AdminRoutes.login);
            }
          }
        } else if (state is AuthErrorState) {
          setState(() {
            _isLoading = false;
            _errorMessage = state.message;
          });
        }
      },
      child: EnhancedRegisterScreen(
        flavor: 'admin',
        appName: 'Shivish Admin',
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
        onRegisterPressed: _onRegisterPressed,
        onLoginPressed: _onLoginPressed,
        isLoading: _isLoading,
        errorMessage: _errorMessage,
      ),
    );
  }
}
