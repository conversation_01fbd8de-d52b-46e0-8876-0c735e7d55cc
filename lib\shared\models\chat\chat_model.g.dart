// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ChatMessage _$ChatMessageFromJson(Map<String, dynamic> json) => _ChatMessage(
  id: json['id'] as String,
  content: json['content'] as String,
  type: $enumDecode(_$ChatMessageTypeEnumMap, json['type']),
  sender: $enumDecode(_$ChatSenderTypeEnumMap, json['sender']),
  timestamp: DateTime.parse(json['timestamp'] as String),
  voiceUrl: json['voiceUrl'] as String?,
  productId: json['productId'] as String?,
  orderId: json['orderId'] as String?,
);

Map<String, dynamic> _$ChatMessageToJson(_ChatMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'content': instance.content,
      'type': _$ChatMessageTypeEnumMap[instance.type]!,
      'sender': _$ChatSenderTypeEnumMap[instance.sender]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'voiceUrl': instance.voiceUrl,
      'productId': instance.productId,
      'orderId': instance.orderId,
    };

const _$ChatMessageTypeEnumMap = {
  ChatMessageType.text: 'text',
  ChatMessageType.voice: 'voice',
  ChatMessageType.product: 'product',
  ChatMessageType.order: 'order',
};

const _$ChatSenderTypeEnumMap = {
  ChatSenderType.user: 'user',
  ChatSenderType.ai: 'ai',
};

_ChatState _$ChatStateFromJson(Map<String, dynamic> json) => _ChatState(
  messages: (json['messages'] as List<dynamic>)
      .map((e) => ChatMessage.fromJson(e as Map<String, dynamic>))
      .toList(),
  isLoading: json['isLoading'] as bool,
  hasError: json['hasError'] as bool,
  errorMessage: json['errorMessage'] as String?,
  isVoiceRecording: json['isVoiceRecording'] as bool? ?? false,
  isVoicePlaying: json['isVoicePlaying'] as bool? ?? false,
  currentVoiceUrl: json['currentVoiceUrl'] as String?,
  currentVoicePosition: (json['currentVoicePosition'] as num?)?.toInt() ?? 0,
  currentVoiceDuration: (json['currentVoiceDuration'] as num?)?.toInt() ?? 0,
  chatContext: json['chatContext'] == null
      ? null
      : ChatContext.fromJson(json['chatContext'] as Map<String, dynamic>),
  currentVoiceMessageId: json['currentVoiceMessageId'] as String?,
);

Map<String, dynamic> _$ChatStateToJson(_ChatState instance) =>
    <String, dynamic>{
      'messages': instance.messages.map((e) => e.toJson()).toList(),
      'isLoading': instance.isLoading,
      'hasError': instance.hasError,
      'errorMessage': instance.errorMessage,
      'isVoiceRecording': instance.isVoiceRecording,
      'isVoicePlaying': instance.isVoicePlaying,
      'currentVoiceUrl': instance.currentVoiceUrl,
      'currentVoicePosition': instance.currentVoicePosition,
      'currentVoiceDuration': instance.currentVoiceDuration,
      'chatContext': instance.chatContext?.toJson(),
      'currentVoiceMessageId': instance.currentVoiceMessageId,
    };

_ChatContext _$ChatContextFromJson(Map<String, dynamic> json) => _ChatContext(
  sessionId: json['sessionId'] as String,
  userId: json['userId'] as String,
  messages: (json['messages'] as List<dynamic>)
      .map((e) => ChatMessage.fromJson(e as Map<String, dynamic>))
      .toList(),
  lastUpdated: DateTime.parse(json['lastUpdated'] as String),
);

Map<String, dynamic> _$ChatContextToJson(_ChatContext instance) =>
    <String, dynamic>{
      'sessionId': instance.sessionId,
      'userId': instance.userId,
      'messages': instance.messages.map((e) => e.toJson()).toList(),
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
