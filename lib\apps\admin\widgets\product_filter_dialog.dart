import 'package:flutter/material.dart';
import 'package:shivish/shared/models/product/product_model.dart';

class ProductFilterDialog extends StatefulWidget {
  final Map<String, dynamic> currentFilters;

  const ProductFilterDialog({
    super.key,
    required this.currentFilters,
  });

  @override
  State<ProductFilterDialog> createState() => _ProductFilterDialogState();
}

class _ProductFilterDialogState extends State<ProductFilterDialog> {
  late Map<String, dynamic> _filters;
  final _priceRangeController = TextEditingController();
  final _stockRangeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _filters = Map.from(widget.currentFilters);
    _initializeControllers();
  }

  void _initializeControllers() {
    if (_filters['priceRange'] != null) {
      _priceRangeController.text = _filters['priceRange'].toString();
    }
    if (_filters['stockRange'] != null) {
      _stockRangeController.text = _filters['stockRange'].toString();
    }
  }

  @override
  void dispose() {
    _priceRangeController.dispose();
    _stockRangeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Filter Products'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: ProductStatus.values.map((status) {
                final statusString = status.toString().split('.').last;
                final isSelected = _filters['status'] == statusString;
                return FilterChip(
                  label: Text(statusString.toUpperCase()),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _filters['status'] = statusString;
                      } else {
                        _filters.remove('status');
                      }
                    });
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            Text(
              'Price Range',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _priceRangeController,
              decoration: const InputDecoration(
                hintText: 'e.g., 0-100, 100-500, 500+',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                if (value.isNotEmpty) {
                  _filters['priceRange'] = value;
                } else {
                  _filters.remove('priceRange');
                }
              },
            ),
            const SizedBox(height: 16),
            Text(
              'Stock Range',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _stockRangeController,
              decoration: const InputDecoration(
                hintText: 'e.g., 0-10, 10-50, 50+',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                if (value.isNotEmpty) {
                  _filters['stockRange'] = value;
                } else {
                  _filters.remove('stockRange');
                }
              },
            ),
            const SizedBox(height: 16),
            Text(
              'Sort By',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _filters['sortBy'],
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(
                  value: 'createdAt',
                  child: Text('Date Created'),
                ),
                DropdownMenuItem(
                  value: 'price',
                  child: Text('Price'),
                ),
                DropdownMenuItem(
                  value: 'stock',
                  child: Text('Stock'),
                ),
                DropdownMenuItem(
                  value: 'rating',
                  child: Text('Rating'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  if (value != null) {
                    _filters['sortBy'] = value;
                  } else {
                    _filters.remove('sortBy');
                  }
                });
              },
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _filters['sortOrder'] ?? 'desc',
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(
                  value: 'asc',
                  child: Text('Ascending'),
                ),
                DropdownMenuItem(
                  value: 'desc',
                  child: Text('Descending'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  if (value != null) {
                    _filters['sortOrder'] = value;
                  } else {
                    _filters.remove('sortOrder');
                  }
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop({});
          },
          child: const Text('CLEAR'),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(_filters);
          },
          child: const Text('APPLY'),
        ),
      ],
    );
  }
}
