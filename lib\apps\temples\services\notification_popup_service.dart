import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/notification/notification_model.dart';
import '../../../shared/services/notification/notification_service.dart';
import '../widgets/notification_popup.dart' as popup_widget;
import '../providers/temple_auth_provider.dart';

/// Service for handling notification popups in the temple app
class TempleNotificationPopupService {
  static TempleNotificationPopupService? _instance;
  static TempleNotificationPopupService get instance {
    _instance ??= TempleNotificationPopupService._();
    return _instance!;
  }

  TempleNotificationPopupService._();

  StreamSubscription<List<NotificationModel>>? _notificationSubscription;
  List<NotificationModel> _lastNotifications = [];
  BuildContext? _context;
  WidgetRef? _ref;

  /// Initialize the popup service
  void initialize(BuildContext context, WidgetRef ref) {
    _context = context;
    _ref = ref;
    _startListening();
  }

  /// Start listening for new notifications
  void _startListening() {
    if (_ref == null) return;

    final authState = _ref!.watch(templeAuthProvider);
    final userId = authState.temple?.managedBy;

    if (userId == null) return;

    final notificationService = NotificationService();

    _notificationSubscription?.cancel();
    _notificationSubscription = notificationService
        .getNotifications(userId)
        .listen(_handleNotifications);
  }

  /// Handle incoming notifications
  void _handleNotifications(List<NotificationModel> notifications) {
    if (_context == null || !_context!.mounted) return;

    // Find new notifications
    final newNotifications = notifications
        .where(
          (notification) =>
              !_lastNotifications.any((last) => last.id == notification.id),
        )
        .toList();

    // Show popup for new notifications
    for (final notification in newNotifications) {
      _showNotificationPopup(notification);
    }

    _lastNotifications = notifications;
  }

  /// Show notification popup
  void _showNotificationPopup(NotificationModel notification) {
    if (_context == null || !_context!.mounted) return;

    popup_widget.TempleNotificationPopupService.showNotificationPopup(
      _context!,
      notification,
      duration: const Duration(seconds: 5),
    );
  }

  /// Dispose the service
  void dispose() {
    _notificationSubscription?.cancel();
    _notificationSubscription = null;
    _context = null;
    _ref = null;
  }

  /// Restart listening (useful when user logs in/out)
  void restart() {
    dispose();
    if (_context != null && _ref != null) {
      initialize(_context!, _ref!);
    }
  }
}
