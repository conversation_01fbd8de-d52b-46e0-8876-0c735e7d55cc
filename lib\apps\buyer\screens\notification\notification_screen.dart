import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/notification_provider.dart';
import '../../../../shared/models/notification/notification_model.dart';
import '../../../../shared/models/notification/notification_type.dart';
import '../../../../shared/models/notification/notification_status.dart';
import '../../buyer_routes.dart';

class NotificationScreen extends ConsumerWidget {
  const NotificationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationsAsync = ref.watch(notificationsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              context.push(BuyerRoutes.settings);
            },
          ),
        ],
      ),
      body: notificationsAsync.when(
        data: (notifications) {
          if (notifications.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.notifications_none,
                    size: 64,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No notifications',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              final notification = notifications[index];
              return Dismissible(
                key: Key(notification.id),
                direction: DismissDirection.endToStart,
                background: Container(
                  alignment: Alignment.centerRight,
                  padding: const EdgeInsets.only(right: 16),
                  color: Theme.of(context).colorScheme.error,
                  child: const Icon(Icons.delete, color: Colors.white),
                ),
                onDismissed: (direction) {
                  ref
                      .read(notificationsProvider.notifier)
                      .deleteNotification(notification.id);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Notification deleted'),
                      duration: const Duration(seconds: 2),
                      action: SnackBarAction(
                        label: 'Undo',
                        onPressed: () {
                          ref
                              .read(notificationsProvider.notifier)
                              .restoreNotification(notification);
                        },
                      ),
                    ),
                  );
                },
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor:
                        notification.status == NotificationStatus.read
                        ? Theme.of(context).colorScheme.surfaceContainerHighest
                        : Theme.of(context).colorScheme.primary,
                    child: Icon(
                      _getNotificationIcon(notification.type),
                      color: notification.status == NotificationStatus.read
                          ? Theme.of(context).colorScheme.onSurfaceVariant
                          : Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                  title: Text(
                    notification.title,
                    style: TextStyle(
                      fontWeight: notification.status == NotificationStatus.read
                          ? FontWeight.normal
                          : FontWeight.bold,
                    ),
                  ),
                  subtitle: Text(notification.body),
                  trailing: Text(
                    _formatTimestamp(notification.createdAt),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  onTap: () {
                    if (notification.status == NotificationStatus.unread) {
                      ref
                          .read(notificationsProvider.notifier)
                          .markAsRead(notification.id);
                    }
                    _handleNotificationTap(context, notification);
                  },
                ),
              );
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => Center(
          child: Text(
            'Error: $error',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.order:
        return Icons.shopping_bag;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.booking:
        return Icons.calendar_today;
      case NotificationType.event:
        return Icons.event;
      case NotificationType.chat:
        return Icons.chat;
      case NotificationType.system:
        return Icons.info;
      case NotificationType.verification:
        return Icons.verified_user;
      case NotificationType.deliveryRequest:
        return Icons.local_shipping;
      case NotificationType.statusUpdate:
        return Icons.update;
      case NotificationType.earnings:
        return Icons.monetization_on;
      // Buyer-specific notification icons
      case NotificationType.healthcareReminder:
        return Icons.medical_services;
      case NotificationType.dailyHoroscope:
        return Icons.star;
      case NotificationType.panchangam:
        return Icons.calendar_view_day;
      case NotificationType.shoppingListAlert:
        return Icons.shopping_cart;
      case NotificationType.safetyTracking:
        return Icons.security;
      case NotificationType.friendTracking:
        return Icons.people;
      case NotificationType.alarmNotification:
        return Icons.alarm;
      case NotificationType.calendarReminder:
        return Icons.event_note;
      case NotificationType.promotions:
        return Icons.local_offer;
      case NotificationType.festivalReminder:
        return Icons.celebration;
      case NotificationType.priceAlert:
        return Icons.price_change;
      case NotificationType.lowStockAlert:
        return Icons.inventory_2;
      case NotificationType.rideBooking:
        return Icons.directions_car;
      case NotificationType.ticketBooking:
        return Icons.airplane_ticket;
      case NotificationType.medicineOrder:
        return Icons.medication;
      case NotificationType.doctorConsultation:
        return Icons.medical_services;
      case NotificationType.aiAssistant:
        return Icons.smart_toy;
      case NotificationType.technicianBooking:
        return Icons.build;
      case NotificationType.priestBooking:
        return Icons.temple_hindu;
      default:
        return Icons.notifications;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _handleNotificationTap(
    BuildContext context,
    NotificationModel notification,
  ) {
    switch (notification.type) {
      case NotificationType.order:
        context.push('${BuyerRoutes.orders}/${notification.data['orderId']}');
        break;
      case NotificationType.payment:
        context.push(BuyerRoutes.addCard);
        break;
      case NotificationType.booking:
        context.push(
          '${BuyerRoutes.bookingDetails}/${notification.data['bookingId']}',
        );
        break;
      case NotificationType.event:
      case NotificationType.calendarReminder:
      case NotificationType.festivalReminder:
        context.push(BuyerRoutes.calendar);
        break;
      case NotificationType.healthcareReminder:
        // Navigate to healthcare appointments
        context.push('/buyer/healthcare/appointments');
        break;
      case NotificationType.dailyHoroscope:
        // Navigate to horoscope screen
        context.push('/buyer/horoscope');
        break;
      case NotificationType.panchangam:
        // Navigate to panchangam screen
        context.push('/buyer/panchangam');
        break;
      case NotificationType.shoppingListAlert:
      case NotificationType.priceAlert:
      case NotificationType.lowStockAlert:
        // Navigate to shopping lists
        context.push('/buyer/shopping/lists');
        break;
      case NotificationType.safetyTracking:
        // Navigate to safety settings
        context.push('/buyer/settings/safety');
        break;
      case NotificationType.friendTracking:
        // Navigate to friend tracking
        context.push('/buyer/friend-tracking');
        break;
      case NotificationType.alarmNotification:
        // Navigate to alarm screen
        context.push('/buyer/alarm');
        break;
      case NotificationType.promotions:
        // Navigate to promotions/offers
        context.push('/buyer/promotions');
        break;
      case NotificationType.rideBooking:
        // Navigate to ride tracking or ride history
        context.push('/buyer/ride/tracking');
        break;
      case NotificationType.ticketBooking:
        // Navigate to ticket bookings
        context.push('/buyer/ticket/bookings');
        break;
      case NotificationType.medicineOrder:
        // Navigate to medicine orders
        context.push('/buyer/healthcare/medicines/orders');
        break;
      case NotificationType.doctorConsultation:
        // Navigate to doctor consultations
        context.push('/buyer/healthcare/consultations');
        break;
      case NotificationType.aiAssistant:
        // Navigate to AI assistant
        context.push('/buyer/ai-assistant');
        break;
      case NotificationType.technicianBooking:
        // Navigate to technician bookings
        context.push('/buyer/technician/bookings');
        break;
      case NotificationType.priestBooking:
        // Navigate to priest bookings
        context.push('/buyer/priest/bookings');
        break;
      default:
        break;
    }
  }
}
