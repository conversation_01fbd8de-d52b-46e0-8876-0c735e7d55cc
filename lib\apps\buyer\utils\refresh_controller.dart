import 'dart:async';
import 'package:flutter/foundation.dart';

/// A controller that manages automatic refresh for buyer screens
class RefreshController extends ChangeNotifier {
  // Singleton instance
  static final RefreshController _instance = RefreshController._internal();
  
  // Factory constructor
  factory RefreshController() => _instance;
  
  // Private constructor
  RefreshController._internal() {
    _startRefreshTimer();
  }
  
  // Timer for automatic refresh
  Timer? _refreshTimer;
  
  // Last refresh timestamp
  DateTime _lastRefreshTime = DateTime.now();
  
  // Refresh interval in milliseconds (1 second by default)
  int _refreshInterval = 1000;
  
  // Flag to indicate if automatic refresh is enabled
  bool _autoRefreshEnabled = true;
  
  // Getters
  DateTime get lastRefreshTime => _lastRefreshTime;
  int get refreshInterval => _refreshInterval;
  bool get autoRefreshEnabled => _autoRefreshEnabled;
  
  // Start the refresh timer
  void _startRefreshTimer() {
    debugPrint('Starting refresh timer with interval: $_refreshInterval ms');
    
    // Cancel any existing timer
    _refreshTimer?.cancel();
    
    // Create a new timer that fires every [refreshInterval] milliseconds
    _refreshTimer = Timer.periodic(
      Duration(milliseconds: _refreshInterval),
      (_) => _onTimerTick(),
    );
  }
  
  // Handle timer tick
  void _onTimerTick() {
    if (!_autoRefreshEnabled) return;
    
    // Update last refresh time
    _lastRefreshTime = DateTime.now();
    
    // Notify listeners to trigger refresh
    notifyListeners();
  }
  
  // Set refresh interval
  void setRefreshInterval(int milliseconds) {
    if (milliseconds < 100) {
      debugPrint('Warning: Refresh interval too small, setting to 100ms minimum');
      milliseconds = 100;
    }
    
    _refreshInterval = milliseconds;
    debugPrint('Refresh interval set to: $_refreshInterval ms');
    
    // Restart timer with new interval
    _startRefreshTimer();
  }
  
  // Enable or disable automatic refresh
  void setAutoRefreshEnabled(bool enabled) {
    _autoRefreshEnabled = enabled;
    debugPrint('Auto refresh ${enabled ? 'enabled' : 'disabled'}');
    
    if (enabled && _refreshTimer == null) {
      _startRefreshTimer();
    }
  }
  
  // Manually trigger a refresh
  void refresh() {
    _lastRefreshTime = DateTime.now();
    notifyListeners();
  }
  
  // Dispose the timer when the controller is no longer needed
  @override
  void dispose() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
    super.dispose();
  }
}
