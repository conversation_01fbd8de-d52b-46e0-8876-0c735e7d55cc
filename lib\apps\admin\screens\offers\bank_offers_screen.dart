import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class BankOffersScreen extends StatefulWidget {
  const BankOffersScreen({super.key});

  @override
  State<BankOffersScreen> createState() => _BankOffersScreenState();
}

class _BankOffersScreenState extends State<BankOffersScreen> {
  final List<Map<String, dynamic>> _bankOffers = [
    {
      'id': '1',
      'title': '10% Instant Discount on HDFC Bank Credit Cards',
      'description': 'Get 10% instant discount on HDFC Bank Credit Cards on a min spend of ₹3,000',
      'bankName': 'HDFC Bank',
      'bankLogoUrl': 'https://example.com/hdfc-logo.png',
      'offerType': 'discount',
      'value': 10.0,
      'code': 'HDFC10',
      'startDate': DateTime.now().subtract(const Duration(days: 10)),
      'endDate': DateTime.now().add(const Duration(days: 20)),
      'applicableProductIds': ['all'],
      'applicableCategories': ['all'],
      'minimumPurchaseAmount': 3000.0,
      'maximumDiscountAmount': 1500.0,
      'isActive': true,
      'createdAt': DateTime.now(),
      'updatedAt': DateTime.now(),
      'termsAndConditions': 'Offer valid once per customer. Not applicable on EMI transactions.',
    },
    {
      'id': '2',
      'title': 'No Cost EMI on Axis Bank Credit Cards',
      'description': 'No Cost EMI on Axis Bank Credit Cards on a min spend of ₹5,000',
      'bankName': 'Axis Bank',
      'bankLogoUrl': 'https://example.com/axis-logo.png',
      'offerType': 'emi',
      'value': 0.0,
      'startDate': DateTime.now().subtract(const Duration(days: 5)),
      'endDate': DateTime.now().add(const Duration(days: 25)),
      'applicableProductIds': ['all'],
      'applicableCategories': ['all'],
      'minimumPurchaseAmount': 5000.0,
      'isActive': true,
      'createdAt': DateTime.now(),
      'updatedAt': DateTime.now(),
      'termsAndConditions': 'EMI available for 3, 6, 9, and 12 months tenures.',
    },
    {
      'id': '3',
      'title': '5% Cashback on SBI Bank Credit Cards',
      'description': 'Get 5% cashback on SBI Bank Credit Cards on a min spend of ₹2,000',
      'bankName': 'SBI Bank',
      'bankLogoUrl': 'https://example.com/sbi-logo.png',
      'offerType': 'cashback',
      'value': 5.0,
      'code': 'SBI5',
      'startDate': DateTime.now().subtract(const Duration(days: 15)),
      'endDate': DateTime.now().add(const Duration(days: 15)),
      'applicableProductIds': ['all'],
      'applicableCategories': ['all'],
      'minimumPurchaseAmount': 2000.0,
      'maximumDiscountAmount': 750.0,
      'isActive': true,
      'createdAt': DateTime.now(),
      'updatedAt': DateTime.now(),
      'termsAndConditions': 'Cashback will be credited within 7 working days after order delivery.',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bank Offers'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              _showAddEditOfferDialog(context);
            },
          ),
        ],
      ),
      body: _bankOffers.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.local_offer_outlined,
                    size: 64,
                    color: theme.colorScheme.outline,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No bank offers available',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 24),
                  FilledButton.icon(
                    onPressed: () {
                      _showAddEditOfferDialog(context);
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('Add Bank Offer'),
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: _bankOffers.length,
              itemBuilder: (context, index) {
                final offer = _bankOffers[index];
                
                // Format dates
                final startDate = DateFormat('dd MMM yyyy').format(offer['startDate']);
                final endDate = DateFormat('dd MMM yyyy').format(offer['endDate']);
                
                // Choose icon based on offer type
                IconData offerIcon;
                Color iconColor;
                
                switch (offer['offerType']) {
                  case 'cashback':
                    offerIcon = Icons.monetization_on;
                    iconColor = Colors.green;
                    break;
                  case 'discount':
                    offerIcon = Icons.percent;
                    iconColor = Colors.blue;
                    break;
                  case 'emi':
                    offerIcon = Icons.calendar_today;
                    iconColor = Colors.orange;
                    break;
                  case 'reward':
                    offerIcon = Icons.card_giftcard;
                    iconColor = Colors.purple;
                    break;
                  default:
                    offerIcon = Icons.local_offer;
                    iconColor = Colors.grey;
                    break;
                }
                
                return Card(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              offerIcon,
                              color: iconColor,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                offer['title'],
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Switch(
                              value: offer['isActive'],
                              onChanged: (value) {
                                setState(() {
                                  offer['isActive'] = value;
                                });
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          offer['description'],
                          style: theme.textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Text(
                              'Bank: ',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              offer['bankName'],
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'Validity: ',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '$startDate to $endDate',
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              'Min. Purchase: ',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '₹${offer['minimumPurchaseAmount']}',
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton.icon(
                              onPressed: () {
                                _showAddEditOfferDialog(context, offer: offer);
                              },
                              icon: const Icon(Icons.edit),
                              label: const Text('Edit'),
                            ),
                            const SizedBox(width: 8),
                            TextButton.icon(
                              onPressed: () {
                                _showDeleteConfirmationDialog(context, offer);
                              },
                              icon: const Icon(Icons.delete),
                              label: const Text('Delete'),
                              style: TextButton.styleFrom(
                                foregroundColor: theme.colorScheme.error,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }

  void _showAddEditOfferDialog(BuildContext context, {Map<String, dynamic>? offer}) {
    final isEditing = offer != null;
    final titleController = TextEditingController(text: offer?['title'] ?? '');
    final descriptionController = TextEditingController(text: offer?['description'] ?? '');
    final bankNameController = TextEditingController(text: offer?['bankName'] ?? '');
    final bankLogoUrlController = TextEditingController(text: offer?['bankLogoUrl'] ?? '');
    final valueController = TextEditingController(text: offer?['value']?.toString() ?? '');
    final codeController = TextEditingController(text: offer?['code'] ?? '');
    final minimumPurchaseAmountController = TextEditingController(
        text: offer?['minimumPurchaseAmount']?.toString() ?? '');
    final maximumDiscountAmountController = TextEditingController(
        text: offer?['maximumDiscountAmount']?.toString() ?? '');
    final termsAndConditionsController = TextEditingController(text: offer?['termsAndConditions'] ?? '');
    
    String selectedOfferType = offer?['offerType'] ?? 'discount';
    DateTime startDate = offer?['startDate'] ?? DateTime.now();
    DateTime endDate = offer?['endDate'] ?? DateTime.now().add(const Duration(days: 30));
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'Edit Bank Offer' : 'Add Bank Offer'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Offer Title',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: bankNameController,
                      decoration: const InputDecoration(
                        labelText: 'Bank Name',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: bankLogoUrlController,
                      decoration: const InputDecoration(
                        labelText: 'Bank Logo URL',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedOfferType,
                decoration: const InputDecoration(
                  labelText: 'Offer Type',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: 'discount',
                    child: Text('Discount'),
                  ),
                  DropdownMenuItem(
                    value: 'cashback',
                    child: Text('Cashback'),
                  ),
                  DropdownMenuItem(
                    value: 'emi',
                    child: Text('EMI'),
                  ),
                  DropdownMenuItem(
                    value: 'reward',
                    child: Text('Reward'),
                  ),
                ],
                onChanged: (value) {
                  selectedOfferType = value!;
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: valueController,
                      decoration: const InputDecoration(
                        labelText: 'Value (%)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: codeController,
                      decoration: const InputDecoration(
                        labelText: 'Promo Code',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: minimumPurchaseAmountController,
                      decoration: const InputDecoration(
                        labelText: 'Min. Purchase (₹)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: maximumDiscountAmountController,
                      decoration: const InputDecoration(
                        labelText: 'Max. Discount (₹)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Text('Start Date:'),
              ListTile(
                title: Text(DateFormat('dd MMM yyyy').format(startDate)),
                trailing: const Icon(Icons.calendar_today),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: startDate,
                    firstDate: DateTime.now().subtract(const Duration(days: 30)),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );
                  if (date != null) {
                    startDate = date;
                  }
                },
              ),
              const Text('End Date:'),
              ListTile(
                title: Text(DateFormat('dd MMM yyyy').format(endDate)),
                trailing: const Icon(Icons.calendar_today),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: endDate,
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );
                  if (date != null) {
                    endDate = date;
                  }
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: termsAndConditionsController,
                decoration: const InputDecoration(
                  labelText: 'Terms & Conditions',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              // Create or update offer
              final newOffer = {
                'id': offer?['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
                'title': titleController.text,
                'description': descriptionController.text,
                'bankName': bankNameController.text,
                'bankLogoUrl': bankLogoUrlController.text,
                'offerType': selectedOfferType,
                'value': double.tryParse(valueController.text) ?? 0.0,
                'code': codeController.text,
                'startDate': startDate,
                'endDate': endDate,
                'applicableProductIds': offer?['applicableProductIds'] ?? ['all'],
                'applicableCategories': offer?['applicableCategories'] ?? ['all'],
                'minimumPurchaseAmount': double.tryParse(minimumPurchaseAmountController.text) ?? 0.0,
                'maximumDiscountAmount': double.tryParse(maximumDiscountAmountController.text),
                'isActive': offer?['isActive'] ?? true,
                'createdAt': offer?['createdAt'] ?? DateTime.now(),
                'updatedAt': DateTime.now(),
                'termsAndConditions': termsAndConditionsController.text,
              };
              
              setState(() {
                if (isEditing) {
                  // Update existing offer
                  final index = _bankOffers.indexWhere((o) => o['id'] == offer['id']);
                  if (index != -1) {
                    _bankOffers[index] = newOffer;
                  }
                } else {
                  // Add new offer
                  _bankOffers.add(newOffer);
                }
              });
              
              Navigator.pop(context);
            },
            child: Text(isEditing ? 'Update' : 'Add'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context, Map<String, dynamic> offer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Bank Offer'),
        content: Text('Are you sure you want to delete "${offer['title']}"?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              setState(() {
                _bankOffers.removeWhere((o) => o['id'] == offer['id']);
              });
              Navigator.pop(context);
            },
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
