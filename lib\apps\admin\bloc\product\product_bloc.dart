import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/admin/bloc/product/product_event.dart';
import 'package:shivish/apps/admin/bloc/product/product_state.dart';
import 'package:shivish/shared/models/product/product_model.dart';
import 'package:shivish/shared/services/product/product_service.dart';
import 'package:shivish/shared/utils/logger.dart';

@injectable
class ProductBloc extends Bloc<ProductEvent, ProductState> {
  final ProductService _productService;
  final _logger = getLogger('AdminProductBloc');
  List<ProductModel> _products = [];
  Map<String, dynamic> _filters = {};
  static const int _pageSize = 20;
  int _currentOffset = 0;

  Map<String, dynamic> get filters => _filters;

  ProductBloc(this._productService) : super(const ProductState.initial()) {
    on<ProductEvent>((event, emit) async {
      await event.when(
        loadProducts: () => _onLoadProducts(emit),
        loadMoreProducts: () => _onLoadMoreProducts(emit),
        updateProductStatus: (product, status, notes, isActive) =>
            _onUpdateProductStatus(
          emit,
          product,
          status,
          notes,
          isActive,
        ),
        deleteProduct: (id) => _onDeleteProduct(emit, id),
        applyFilters: (filters) => _onApplyFilters(emit, filters),
      );
    });
  }

  Future<void> _onLoadProducts(Emitter<ProductState> emit) async {
    try {
      _logger.info('Loading products');
      emit(const ProductState.loading());
      _currentOffset = 0; // Reset offset for fresh load
      _products = await _productService.getProducts(
        limit: _pageSize,
        offset: _currentOffset,
      ).first;
      _currentOffset = _products.length;
      _logger.info('Successfully loaded ${_products.length} products');
      emit(ProductState.loaded(_products));
    } catch (e) {
      _logger.severe('Failed to load products: $e');
      emit(ProductState.error(e.toString()));
    }
  }

  Future<void> _onLoadMoreProducts(Emitter<ProductState> emit) async {
    try {
      if (_products.isEmpty) return;
      _logger.info('Loading more products from offset: $_currentOffset');
      emit(ProductState.loadingMore(_products));

      final moreProducts = await _productService
          .getProducts(
            limit: _pageSize,
            offset: _currentOffset,
          )
          .first;

      if (moreProducts.isEmpty) {
        _logger.info('No more products to load');
        emit(ProductState.loaded(_products));
        return;
      }

      _products = [..._products, ...moreProducts];
      _currentOffset += moreProducts.length;
      _logger.info('Successfully loaded ${moreProducts.length} more products. Total: ${_products.length}');
      emit(ProductState.loaded(_products));
    } catch (e) {
      _logger.severe('Failed to load more products: $e');
      emit(ProductState.error(e.toString()));
    }
  }

  Future<void> _onUpdateProductStatus(
    Emitter<ProductState> emit,
    ProductModel product,
    String status,
    String? notes,
    bool isActive,
  ) async {
    try {
      _logger.info('Updating product status: ${product.id} -> $status (active: $isActive)');
      final updatedProduct = product.copyWith(
        productStatus: ProductStatus.values.firstWhere(
          (e) => e.toString() == status,
        ),
        isApproved: isActive,
        verificationNotes: notes,
        updatedAt: DateTime.now(),
      );

      await _productService.createOrUpdateProduct(updatedProduct);

      _products = _products.map((p) {
        return p.id == product.id ? updatedProduct : p;
      }).toList();

      _logger.info('Successfully updated product status: ${product.id}');
      emit(ProductState.loaded(_products));
    } catch (e) {
      _logger.severe('Failed to update product status: $e');
      emit(ProductState.error(e.toString()));
    }
  }

  Future<void> _onDeleteProduct(
    Emitter<ProductState> emit,
    String id,
  ) async {
    try {
      _logger.info('Deleting product: $id');
      await _productService.deleteProduct(id);

      _products = _products.where((p) => p.id != id).toList();

      _logger.info('Successfully deleted product: $id');
      emit(ProductState.loaded(_products));
    } catch (e) {
      _logger.severe('Failed to delete product: $e');
      emit(ProductState.error(e.toString()));
    }
  }

  Future<void> _onApplyFilters(
    Emitter<ProductState> emit,
    Map<String, dynamic> filters,
  ) async {
    try {
      _logger.info('Applying filters: $filters');
      _filters = filters;
      _currentOffset = 0; // Reset offset when applying filters
      emit(const ProductState.loading());

      _products = await _productService
          .getProducts(
            limit: _pageSize,
            offset: _currentOffset,
            filters: _filters,
          )
          .first;

      _currentOffset = _products.length;
      _logger.info('Successfully applied filters. Loaded ${_products.length} products');
      emit(ProductState.loaded(_products));
    } catch (e) {
      _logger.severe('Failed to apply filters: $e');
      emit(ProductState.error(e.toString()));
    }
  }
}
