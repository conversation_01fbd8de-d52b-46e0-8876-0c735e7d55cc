import 'package:injectable/injectable.dart';
import 'package:flutter/foundation.dart';
import '../../../../shared/models/technician/technician.dart';
import '../../domain/repositories/technician_repository.dart';
import '../../../../shared/database/services/database_service.dart';

@Injectable(as: TechnicianRepository)
class TechnicianRepositoryImpl implements TechnicianRepository {
  final DatabaseService _databaseService;
  static const String _collection = 'technicians';

  TechnicianRepositoryImpl(this._databaseService);

  @override
  Future<Technician?> getTechnician(String id) async {
    try {
      final data = await _databaseService.find(_collection, id);
      if (data == null) return null;
      return Technician.fromJson(data);
    } catch (e) {
      debugPrint('Error getting technician: $e');
      return null;
    }
  }

  @override
  Future<void> updateTechnician(Technician technician) async {
    try {
      final technicianData = technician.toJson();
      technicianData['updated_at'] = DateTime.now().toIso8601String();

      await _databaseService.update(_collection, technician.id, technicianData);
    } catch (e) {
      debugPrint('Error updating technician: $e');
      rethrow;
    }
  }

  @override
  Stream<Technician?> streamTechnician(String id) {
    try {
      return _databaseService.watchRecord(_collection, id)
          .map((data) => data != null ? Technician.fromJson(data) : null);
    } catch (e) {
      debugPrint('Error streaming technician: $e');
      return Stream.value(null);
    }
  }

  @override
  Future<List<Technician>> getTechnicians() async {
    try {
      final data = await _databaseService.getAll(
        _collection,
        where: 'is_deleted = ?',
        whereParams: [false],
        orderBy: 'created_at DESC',
      );
      return data.map((item) => Technician.fromJson(item)).toList();
    } catch (e) {
      debugPrint('Error getting technicians: $e');
      return [];
    }
  }

  @override
  Future<void> createTechnician(Technician technician) async {
    try {
      final technicianData = technician.toJson();
      technicianData['created_at'] = DateTime.now().toIso8601String();
      technicianData['updated_at'] = DateTime.now().toIso8601String();
      technicianData['is_deleted'] = false;

      await _databaseService.create(_collection, technicianData);
    } catch (e) {
      debugPrint('Error creating technician: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteTechnician(String id) async {
    try {
      await _databaseService.update(_collection, id, {
        'is_deleted': true,
        'deleted_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error deleting technician: $e');
      rethrow;
    }
  }
}
