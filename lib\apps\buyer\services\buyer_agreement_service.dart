import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../widgets/dialogs/buyer_legal_agreement_dialog.dart';

/// Service to handle the buyer legal agreement
class BuyerAgreementService {
  static const String _agreementKey = 'buyer_legal_agreement_accepted';
  static const String _agreementDateKey = 'buyer_legal_agreement_date';
  static const int _agreementValidityDays = 90; // Reconfirm every 90 days

  /// Checks if the buyer has already accepted the agreement
  static Future<bool> hasAcceptedAgreement() async {
    final prefs = await SharedPreferences.getInstance();
    final isAccepted = prefs.getBool(_agreementKey) ?? false;

    if (!isAccepted) {
      return false;
    }

    // Check if the agreement acceptance has expired
    final agreementDateStr = prefs.getString(_agreementDateKey);
    if (agreementDateStr == null) {
      return false;
    }

    try {
      final agreementDate = DateTime.parse(agreementDateStr);
      final now = DateTime.now();
      final difference = now.difference(agreementDate).inDays;

      // If the agreement is older than the validity period, it's expired
      if (difference > _agreementValidityDays) {
        return false;
      }

      return true;
    } catch (e) {
      // If there's an error parsing the date, assume not accepted
      return false;
    }
  }

  /// Saves the buyer's agreement acceptance
  static Future<void> saveAgreementAcceptance(bool accepted) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_agreementKey, accepted);

    if (accepted) {
      // Save the agreement date
      final now = DateTime.now().toIso8601String();
      await prefs.setString(_agreementDateKey, now);
    }
  }

  /// Shows the agreement dialog if needed
  /// Returns true if the buyer has accepted, false otherwise
  static Future<bool> checkAgreement(BuildContext context) async {
    // Check if the buyer has already accepted
    final hasAlreadyAccepted = await hasAcceptedAgreement();
    if (hasAlreadyAccepted) {
      return true;
    }

    // Show the agreement dialog
    final accepted = await BuyerLegalAgreementDialog.show(context);

    // If the buyer accepted, save the acceptance
    if (accepted == true) {
      await saveAgreementAcceptance(true);
      return true;
    }

    // If we get here, the buyer didn't accept
    return false;
  }
}
