// Analytics event names
class AnalyticsEvents {
  // Screen views
  static const String screenView = 'screen_view';

  // User actions
  static const String userLogin = 'user_login';
  static const String userSignup = 'user_signup';
  static const String userLogout = 'user_logout';

  // E-commerce events
  static const String viewProduct = 'view_product';
  static const String addToCart = 'add_to_cart';
  static const String removeFromCart = 'remove_from_cart';
  static const String beginCheckout = 'begin_checkout';
  static const String purchase = 'purchase';

  // Booking events
  static const String viewService = 'view_service';
  static const String bookService = 'book_service';
  static const String cancelBooking = 'cancel_booking';

  // Media events
  static const String playMedia = 'play_media';
  static const String pauseMedia = 'pause_media';
  static const String downloadMedia = 'download_media';

  // Calendar events
  static const String createEvent = 'create_event';
  static const String updateEvent = 'update_event';
  static const String deleteEvent = 'delete_event';

  // Alarm events
  static const String setAlarm = 'set_alarm';
  static const String updateAlarm = 'update_alarm';
  static const String deleteAlarm = 'delete_alarm';

  // Chat events
  static const String sendMessage = 'send_message';
  static const String receiveMessage = 'receive_message';

  // Performance events
  static const String networkLatency = 'network_latency';
  static const String appStartup = 'app_startup';
  static const String renderLatency = 'render_latency';

  // Error events
  static const String appError = 'app_error';
  static const String networkError = 'network_error';
  static const String validationError = 'validation_error';
}

// Analytics parameter names
class AnalyticsParams {
  static const String userId = 'userId';
  static const String userType = 'user_type';
  static const String screenName = 'screen_name';
  static const String screenClass = 'screen_class';
  static const String productId = 'productId';
  static const String productName = 'productName';
  static const String price = 'price';
  static const String currency = 'currency';
  static const String quantity = 'quantity';
  static const String serviceId = 'service_id';
  static const String serviceName = 'service_name';
  static const String bookingId = 'booking_id';
  static const String mediaId = 'media_id';
  static const String mediaType = 'media_type';
  static const String eventId = 'event_id';
  static const String eventType = 'event_type';
  static const String alarmId = 'alarm_id';
  static const String alarmType = 'alarm_type';
  static const String messageId = 'message_id';
  static const String messageType = 'message_type';
  static const String errorMessage = 'error_message';
  static const String errorCode = 'error_code';
  static const String stackTrace = 'stack_trace';
  static const String latencyMs = 'latency_ms';
  static const String endpoint = 'endpoint';
}
