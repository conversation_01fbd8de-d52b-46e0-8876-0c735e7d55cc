part of 'priest_bloc.dart';

@freezed
sealed class PriestEvent with _$PriestEvent {
  const factory PriestEvent.loadPriests() = _LoadPriests;
  const factory PriestEvent.loadMorePriests() = _LoadMorePriests;
  const factory PriestEvent.updatePriestStatus({
    required String id,
    required bool isActive,
  }) = _UpdatePriestStatus;
  const factory PriestEvent.updatePriestVerification({
    required String id,
    required bool isVerified,
    required String verificationStatus,
    String? verificationNotes,
  }) = _UpdatePriestVerification;
  const factory PriestEvent.deletePriest({
    required String id,
  }) = _DeletePriest;
  const factory PriestEvent.startRealtimeUpdates() = _StartRealtimeUpdates;
  const factory PriestEvent.stopRealtimeUpdates() = _StopRealtimeUpdates;
}
