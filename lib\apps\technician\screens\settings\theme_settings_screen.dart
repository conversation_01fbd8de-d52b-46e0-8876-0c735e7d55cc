import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/providers/settings_provider.dart';

class ThemeSettingsScreen extends ConsumerWidget {
  const ThemeSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);
    final setThemeMode = ref.read(themeModeProvider.notifier).setThemeMode;
    final colorScheme = ref.watch(colorSchemeProvider);
    final setColorScheme =
        ref.read(colorSchemeProvider.notifier).setColorScheme;
    final textSize = ref.watch(textSizeProvider);
    final setTextSize = ref.read(textSizeProvider.notifier).setTextSize;

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Theme Settings',
      ),
      body: ListView(
        children: [
          _buildThemeModeSection(
            context,
            'Theme Mode',
            'Select your preferred theme mode',
            themeMode,
            setThemeMode,
          ),
          const Divider(),
          _buildColorSchemeSection(
            context,
            'Color Scheme',
            'Select your preferred color scheme',
            colorScheme,
            setColorScheme,
          ),
          const Divider(),
          _buildTextSizeSection(
            context,
            'Text Size',
            'Adjust the text size',
            textSize,
            setTextSize,
          ),
        ],
      ),
    );
  }

  Widget _buildThemeModeSection(
    BuildContext context,
    String title,
    String subtitle,
    ThemeMode themeMode,
    void Function(ThemeMode) setThemeMode,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        ListTile(
          leading: const Icon(Icons.brightness_6),
          title: const Text('Theme Mode'),
          subtitle: Text(subtitle),
          trailing: DropdownButton<ThemeMode>(
            value: themeMode,
            items: const [
              DropdownMenuItem(
                value: ThemeMode.system,
                child: Text('System'),
              ),
              DropdownMenuItem(
                value: ThemeMode.light,
                child: Text('Light'),
              ),
              DropdownMenuItem(
                value: ThemeMode.dark,
                child: Text('Dark'),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                setThemeMode(value);
                _showSnackBar(
                  context,
                  'Theme mode changed to ${value.name.capitalize()}',
                );
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildColorSchemeSection(
    BuildContext context,
    String title,
    String subtitle,
    String colorScheme,
    void Function(String) setColorScheme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        ListTile(
          leading: const Icon(Icons.palette),
          title: const Text('Color Scheme'),
          subtitle: Text(subtitle),
          trailing: DropdownButton<String>(
            value: colorScheme,
            items: const [
              DropdownMenuItem(
                value: 'blue',
                child: Text('Blue'),
              ),
              DropdownMenuItem(
                value: 'green',
                child: Text('Green'),
              ),
              DropdownMenuItem(
                value: 'purple',
                child: Text('Purple'),
              ),
              DropdownMenuItem(
                value: 'orange',
                child: Text('Orange'),
              ),
              DropdownMenuItem(
                value: 'pink',
                child: Text('Pink'),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                setColorScheme(value);
                _showSnackBar(
                  context,
                  'Color scheme changed to ${value.capitalize()}',
                );
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTextSizeSection(
    BuildContext context,
    String title,
    String subtitle,
    String textSize,
    void Function(String) setTextSize,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        ListTile(
          leading: const Icon(Icons.text_fields),
          title: const Text('Text Size'),
          subtitle: Text(subtitle),
          trailing: DropdownButton<String>(
            value: textSize,
            items: const [
              DropdownMenuItem(
                value: 'small',
                child: Text('Small'),
              ),
              DropdownMenuItem(
                value: 'medium',
                child: Text('Medium'),
              ),
              DropdownMenuItem(
                value: 'large',
                child: Text('Large'),
              ),
              DropdownMenuItem(
                value: 'extra_large',
                child: Text('Extra Large'),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                setTextSize(value);
                _showSnackBar(
                  context,
                  'Text size changed to ${value.replaceAll('_', ' ').capitalize()}',
                );
              }
            },
          ),
        ),
      ],
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
