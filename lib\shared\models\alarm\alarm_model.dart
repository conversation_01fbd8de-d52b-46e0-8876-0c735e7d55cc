import 'package:freezed_annotation/freezed_annotation.dart';

part 'alarm_model.freezed.dart';
part 'alarm_model.g.dart';

enum AlarmType {
  @JsonValue(0)
  defaultAlarm,
  @JsonValue(1)
  aiAlarm,
  @JsonValue(2)
  customAlarm,
}

enum AlarmRepeat {
  @JsonValue(0)
  once,
  @JsonValue(1)
  daily,
  @JsonValue(2)
  weekly,
  @JsonValue(3)
  monthly,
  @JsonValue(4)
  weekdays,
  @JsonValue(5)
  weekends,
}

@freezed
abstract class AlarmModel with _$AlarmModel {
  const factory AlarmModel({
    required String id,
    required String userId,
    required AlarmType type,
    required DateTime time,
    required bool isEnabled,
    required String toneId,
    required double volume,
    required bool vibrate,
    required AlarmRepeat repeat,
    required List<int> repeatDays, // 0-6 for Sunday-Saturday
    required int snoozeDuration, // in minutes
    required int maxSnoozeCount,
    required String label,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(0) int currentSnoozeCount,
    @Default(false) bool isSnoozed,
    DateTime? snoozeTime,
    // AI Alarm specific fields
    @Default(false) bool isLearning,
    @Default({}) Map<String, dynamic> usagePatterns,
    @Default(0) int smartWakeUpOffset, // in minutes
    // Custom Alarm specific fields
    @Default(false) bool isCustom,
    @Default({}) Map<String, dynamic> customSettings,
  }) = _AlarmModel;

  factory AlarmModel.fromJson(Map<String, dynamic> json) =>
      _$AlarmModelFromJson(json);

  /// Create a new alarm with current timestamp
  factory AlarmModel.create({
    required String userId,
    required AlarmType type,
    required DateTime time,
    required String toneId,
    required String label,
    bool isEnabled = true,
    double volume = 1.0,
    bool vibrate = true,
    AlarmRepeat repeat = AlarmRepeat.once,
    List<int>? repeatDays,
    int snoozeDuration = 5,
    int maxSnoozeCount = 3,
  }) {
    final now = DateTime.now();
    return AlarmModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      type: type,
      time: time,
      isEnabled: isEnabled,
      toneId: toneId,
      volume: volume,
      vibrate: vibrate,
      repeat: repeat,
      repeatDays: repeatDays ?? [],
      snoozeDuration: snoozeDuration,
      maxSnoozeCount: maxSnoozeCount,
      label: label,
      createdAt: now,
      updatedAt: now,
    );
  }
}

/// Business logic extensions for AlarmModel
extension AlarmModelX on AlarmModel {
  /// Check if alarm should ring on a specific day
  bool shouldRingOnDay(DateTime day) {
    switch (repeat) {
      case AlarmRepeat.once:
        return day.day == time.day &&
               day.month == time.month &&
               day.year == time.year;
      case AlarmRepeat.daily:
        return true;
      case AlarmRepeat.weekly:
        return day.weekday == time.weekday;
      case AlarmRepeat.monthly:
        return day.day == time.day;
      case AlarmRepeat.weekdays:
        return day.weekday >= 1 && day.weekday <= 5; // Monday to Friday
      case AlarmRepeat.weekends:
        return day.weekday == 6 || day.weekday == 7; // Saturday and Sunday
    }
  }

  /// Check if alarm can be snoozed
  bool get canSnooze => currentSnoozeCount < maxSnoozeCount;

  /// Get next alarm time considering snooze
  DateTime get nextAlarmTime {
    if (isSnoozed && snoozeTime != null) {
      return snoozeTime!;
    }
    return time;
  }

  /// Snooze the alarm
  AlarmModel snooze() {
    if (!canSnooze) return this;

    final snoozeTime = DateTime.now().add(Duration(minutes: snoozeDuration));
    return copyWith(
      isSnoozed: true,
      snoozeTime: snoozeTime,
      currentSnoozeCount: currentSnoozeCount + 1,
      updatedAt: DateTime.now(),
    );
  }

  /// Stop snoozing and reset
  AlarmModel stopSnooze() {
    return copyWith(
      isSnoozed: false,
      snoozeTime: null,
      currentSnoozeCount: 0,
      updatedAt: DateTime.now(),
    );
  }

  /// Toggle alarm enabled state
  AlarmModel toggle() {
    return copyWith(
      isEnabled: !isEnabled,
      updatedAt: DateTime.now(),
    );
  }

  /// Update usage patterns for AI learning
  AlarmModel updateUsagePattern(String pattern, dynamic value) {
    final newPatterns = Map<String, dynamic>.from(usagePatterns);
    newPatterns[pattern] = value;
    return copyWith(
      usagePatterns: newPatterns,
      updatedAt: DateTime.now(),
    );
  }

  /// Check if this is an AI alarm
  bool get isAIAlarm => type == AlarmType.aiAlarm;

  /// Check if this is a custom alarm
  bool get isCustomAlarm => type == AlarmType.customAlarm;

  /// Get formatted time string
  String get formattedTime {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Get repeat description
  String get repeatDescription {
    switch (repeat) {
      case AlarmRepeat.once:
        return 'Once';
      case AlarmRepeat.daily:
        return 'Daily';
      case AlarmRepeat.weekly:
        return 'Weekly';
      case AlarmRepeat.monthly:
        return 'Monthly';
      case AlarmRepeat.weekdays:
        return 'Weekdays';
      case AlarmRepeat.weekends:
        return 'Weekends';
    }
  }
}
