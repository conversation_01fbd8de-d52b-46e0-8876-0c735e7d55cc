import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:shivish/apps/seller/presentation/cubits/price_management/price_management_cubit.dart';
import 'package:shivish/shared/models/product/product_model.dart';

class BulkPriceUpdate extends StatefulWidget {
  final Function(BulkUpdateType, double, List<String>) onBulkUpdate;
  final List<String> selectedProductIds;
  final List<ProductModel> products;

  const BulkPriceUpdate({
    super.key,
    required this.onBulkUpdate,
    required this.selectedProductIds,
    required this.products,
  });

  @override
  State<BulkPriceUpdate> createState() => _BulkPriceUpdateState();
}

class _BulkPriceUpdateState extends State<BulkPriceUpdate> {
  BulkUpdateType _selectedType = BulkUpdateType.percentageIncrease;
  final _valueController = TextEditingController();
  String? _errorText;
  bool _showPreview = false;
  final _currencyFormat = NumberFormat.currency(symbol: '₹');

  @override
  void dispose() {
    _valueController.dispose();
    super.dispose();
  }

  double _calculateNewPrice(double currentPrice, double value) {
    switch (_selectedType) {
      case BulkUpdateType.percentageIncrease:
        return currentPrice * (1 + value / 100);
      case BulkUpdateType.percentageDecrease:
        return currentPrice * (1 - value / 100);
      case BulkUpdateType.fixedAmount:
        return currentPrice + value;
      case BulkUpdateType.setPrice:
        return value;
    }
  }

  bool _validate() {
    if (_valueController.text.isEmpty) {
      setState(() {
        _errorText = 'Please enter a value';
      });
      return false;
    }

    final value = double.tryParse(_valueController.text);
    if (value == null) {
      setState(() {
        _errorText = 'Please enter a valid number';
      });
      return false;
    }

    if (value < 0) {
      setState(() {
        _errorText = 'Value cannot be negative';
      });
      return false;
    }

    if (_selectedType == BulkUpdateType.percentageIncrease ||
        _selectedType == BulkUpdateType.percentageDecrease) {
      if (value > 100) {
        setState(() {
          _errorText = 'Percentage cannot be greater than 100';
        });
        return false;
      }
    }

    setState(() {
      _errorText = null;
    });
    return true;
  }

  void _handlePreview() {
    if (_validate()) {
      setState(() {
        _showPreview = true;
      });
    }
  }

  void _handleUpdate() {
    if (_validate()) {
      final value = double.parse(_valueController.text);
      widget.onBulkUpdate(_selectedType, value, widget.selectedProductIds);
      setState(() {
        _showPreview = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedProducts = widget.products
        .where((p) => widget.selectedProductIds.contains(p.id))
        .toList();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          DropdownButtonFormField<BulkUpdateType>(
            value: _selectedType,
            decoration: const InputDecoration(
              labelText: 'Update Type',
              border: OutlineInputBorder(),
            ),
            items: BulkUpdateType.values.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(_getUpdateTypeLabel(type)),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedType = value;
                  _valueController.clear();
                  _errorText = null;
                  _showPreview = false;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _valueController,
            decoration: InputDecoration(
              labelText: _getValueLabel(),
              border: const OutlineInputBorder(),
              errorText: _errorText,
              suffixText: _selectedType == BulkUpdateType.percentageIncrease ||
                      _selectedType == BulkUpdateType.percentageDecrease
                  ? '%'
                  : '\$',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            onChanged: (_) {
              if (_errorText != null || _showPreview) {
                setState(() {
                  _errorText = null;
                  _showPreview = false;
                });
              }
            },
          ),
          const SizedBox(height: 24),
          if (!_showPreview)
            ElevatedButton(
              onPressed: selectedProducts.isEmpty ? null : _handlePreview,
              child: Text(
                selectedProducts.isEmpty
                    ? 'Select products to update'
                    : 'Preview changes for ${selectedProducts.length} products',
              ),
            )
          else ...[
            Text(
              'Preview Changes',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Card(
                child: ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: selectedProducts.length,
                  itemBuilder: (context, index) {
                    final product = selectedProducts[index];
                    final currentPrice = product.price;
                    final newPrice = _calculateNewPrice(
                      currentPrice,
                      double.parse(_valueController.text),
                    );
                    final priceDiff = newPrice - currentPrice;
                    final percentDiff = (priceDiff / currentPrice) * 100;

                    return ListTile(
                      title: Text(product.name),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Current: ${_currencyFormat.format(currentPrice)}',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          Text(
                            'New: ${_currencyFormat.format(newPrice)}',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: priceDiff > 0
                                          ? Colors.green
                                          : priceDiff < 0
                                              ? Colors.red
                                              : null,
                                      fontWeight: FontWeight.bold,
                                    ),
                          ),
                          Text(
                            'Change: ${priceDiff >= 0 ? '+' : ''}${_currencyFormat.format(priceDiff)} (${percentDiff.toStringAsFixed(1)}%)',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _showPreview = false;
                      });
                    },
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _handleUpdate,
                    child: const Text('Apply Changes'),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  String _getUpdateTypeLabel(BulkUpdateType type) {
    switch (type) {
      case BulkUpdateType.percentageIncrease:
        return 'Increase by percentage';
      case BulkUpdateType.percentageDecrease:
        return 'Decrease by percentage';
      case BulkUpdateType.fixedAmount:
        return 'Add/subtract fixed amount';
      case BulkUpdateType.setPrice:
        return 'Set to specific price';
    }
  }

  String _getValueLabel() {
    switch (_selectedType) {
      case BulkUpdateType.percentageIncrease:
        return 'Percentage to increase';
      case BulkUpdateType.percentageDecrease:
        return 'Percentage to decrease';
      case BulkUpdateType.fixedAmount:
        return 'Amount to add/subtract';
      case BulkUpdateType.setPrice:
        return 'New price';
    }
  }
}
