// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'booking_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BookingAddress {

 String get street; String get city; String get state; String get country; String get postalCode; String? get landmark; String get contactName; String get contactPhone; String? get contactEmail; double? get latitude; double? get longitude;
/// Create a copy of BookingAddress
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BookingAddressCopyWith<BookingAddress> get copyWith => _$BookingAddressCopyWithImpl<BookingAddress>(this as BookingAddress, _$identity);

  /// Serializes this BookingAddress to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BookingAddress&&(identical(other.street, street) || other.street == street)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.postalCode, postalCode) || other.postalCode == postalCode)&&(identical(other.landmark, landmark) || other.landmark == landmark)&&(identical(other.contactName, contactName) || other.contactName == contactName)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,street,city,state,country,postalCode,landmark,contactName,contactPhone,contactEmail,latitude,longitude);

@override
String toString() {
  return 'BookingAddress(street: $street, city: $city, state: $state, country: $country, postalCode: $postalCode, landmark: $landmark, contactName: $contactName, contactPhone: $contactPhone, contactEmail: $contactEmail, latitude: $latitude, longitude: $longitude)';
}


}

/// @nodoc
abstract mixin class $BookingAddressCopyWith<$Res>  {
  factory $BookingAddressCopyWith(BookingAddress value, $Res Function(BookingAddress) _then) = _$BookingAddressCopyWithImpl;
@useResult
$Res call({
 String street, String city, String state, String country, String postalCode, String? landmark, String contactName, String contactPhone, String? contactEmail, double? latitude, double? longitude
});




}
/// @nodoc
class _$BookingAddressCopyWithImpl<$Res>
    implements $BookingAddressCopyWith<$Res> {
  _$BookingAddressCopyWithImpl(this._self, this._then);

  final BookingAddress _self;
  final $Res Function(BookingAddress) _then;

/// Create a copy of BookingAddress
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? street = null,Object? city = null,Object? state = null,Object? country = null,Object? postalCode = null,Object? landmark = freezed,Object? contactName = null,Object? contactPhone = null,Object? contactEmail = freezed,Object? latitude = freezed,Object? longitude = freezed,}) {
  return _then(_self.copyWith(
street: null == street ? _self.street : street // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,postalCode: null == postalCode ? _self.postalCode : postalCode // ignore: cast_nullable_to_non_nullable
as String,landmark: freezed == landmark ? _self.landmark : landmark // ignore: cast_nullable_to_non_nullable
as String?,contactName: null == contactName ? _self.contactName : contactName // ignore: cast_nullable_to_non_nullable
as String,contactPhone: null == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}

}


/// Adds pattern-matching-related methods to [BookingAddress].
extension BookingAddressPatterns on BookingAddress {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BookingAddress value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BookingAddress() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BookingAddress value)  $default,){
final _that = this;
switch (_that) {
case _BookingAddress():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BookingAddress value)?  $default,){
final _that = this;
switch (_that) {
case _BookingAddress() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String street,  String city,  String state,  String country,  String postalCode,  String? landmark,  String contactName,  String contactPhone,  String? contactEmail,  double? latitude,  double? longitude)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BookingAddress() when $default != null:
return $default(_that.street,_that.city,_that.state,_that.country,_that.postalCode,_that.landmark,_that.contactName,_that.contactPhone,_that.contactEmail,_that.latitude,_that.longitude);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String street,  String city,  String state,  String country,  String postalCode,  String? landmark,  String contactName,  String contactPhone,  String? contactEmail,  double? latitude,  double? longitude)  $default,) {final _that = this;
switch (_that) {
case _BookingAddress():
return $default(_that.street,_that.city,_that.state,_that.country,_that.postalCode,_that.landmark,_that.contactName,_that.contactPhone,_that.contactEmail,_that.latitude,_that.longitude);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String street,  String city,  String state,  String country,  String postalCode,  String? landmark,  String contactName,  String contactPhone,  String? contactEmail,  double? latitude,  double? longitude)?  $default,) {final _that = this;
switch (_that) {
case _BookingAddress() when $default != null:
return $default(_that.street,_that.city,_that.state,_that.country,_that.postalCode,_that.landmark,_that.contactName,_that.contactPhone,_that.contactEmail,_that.latitude,_that.longitude);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BookingAddress implements BookingAddress {
  const _BookingAddress({required this.street, required this.city, required this.state, required this.country, required this.postalCode, this.landmark, required this.contactName, required this.contactPhone, this.contactEmail, this.latitude, this.longitude});
  factory _BookingAddress.fromJson(Map<String, dynamic> json) => _$BookingAddressFromJson(json);

@override final  String street;
@override final  String city;
@override final  String state;
@override final  String country;
@override final  String postalCode;
@override final  String? landmark;
@override final  String contactName;
@override final  String contactPhone;
@override final  String? contactEmail;
@override final  double? latitude;
@override final  double? longitude;

/// Create a copy of BookingAddress
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BookingAddressCopyWith<_BookingAddress> get copyWith => __$BookingAddressCopyWithImpl<_BookingAddress>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BookingAddressToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BookingAddress&&(identical(other.street, street) || other.street == street)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.postalCode, postalCode) || other.postalCode == postalCode)&&(identical(other.landmark, landmark) || other.landmark == landmark)&&(identical(other.contactName, contactName) || other.contactName == contactName)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,street,city,state,country,postalCode,landmark,contactName,contactPhone,contactEmail,latitude,longitude);

@override
String toString() {
  return 'BookingAddress(street: $street, city: $city, state: $state, country: $country, postalCode: $postalCode, landmark: $landmark, contactName: $contactName, contactPhone: $contactPhone, contactEmail: $contactEmail, latitude: $latitude, longitude: $longitude)';
}


}

/// @nodoc
abstract mixin class _$BookingAddressCopyWith<$Res> implements $BookingAddressCopyWith<$Res> {
  factory _$BookingAddressCopyWith(_BookingAddress value, $Res Function(_BookingAddress) _then) = __$BookingAddressCopyWithImpl;
@override @useResult
$Res call({
 String street, String city, String state, String country, String postalCode, String? landmark, String contactName, String contactPhone, String? contactEmail, double? latitude, double? longitude
});




}
/// @nodoc
class __$BookingAddressCopyWithImpl<$Res>
    implements _$BookingAddressCopyWith<$Res> {
  __$BookingAddressCopyWithImpl(this._self, this._then);

  final _BookingAddress _self;
  final $Res Function(_BookingAddress) _then;

/// Create a copy of BookingAddress
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? street = null,Object? city = null,Object? state = null,Object? country = null,Object? postalCode = null,Object? landmark = freezed,Object? contactName = null,Object? contactPhone = null,Object? contactEmail = freezed,Object? latitude = freezed,Object? longitude = freezed,}) {
  return _then(_BookingAddress(
street: null == street ? _self.street : street // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,postalCode: null == postalCode ? _self.postalCode : postalCode // ignore: cast_nullable_to_non_nullable
as String,landmark: freezed == landmark ? _self.landmark : landmark // ignore: cast_nullable_to_non_nullable
as String?,contactName: null == contactName ? _self.contactName : contactName // ignore: cast_nullable_to_non_nullable
as String,contactPhone: null == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}


}


/// @nodoc
mixin _$BookingModel {

 String get id; String get bookingNumber; String get customerId; String get providerId; BookingType get type; BookingStatus get status; PaymentStatus get paymentStatus; DateTime get bookingDate; DateTime get startTime; DateTime get endTime; BookingAddress get serviceLocation; List<String> get services; double get subtotalAmount; double get taxAmount; double get discountAmount; double get totalAmount; String? get paymentMethod; String? get paymentId; String? get transactionId; String? get refundId; String? get notes; String? get customerNotes; String? get providerNotes; String? get cancellationReason; String? get refundReason; String? get customerName; String? get customerPhone; String? get customerEmail; String? get providerName; String? get providerPhone; String? get providerEmail; bool get reminderSent; DateTime? get reminderSentAt; bool get confirmationSent; DateTime? get confirmationSentAt; DateTime get createdAt; DateTime get updatedAt; bool get isDeleted; double? get rating; String? get review; List<String>? get attachments;
/// Create a copy of BookingModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BookingModelCopyWith<BookingModel> get copyWith => _$BookingModelCopyWithImpl<BookingModel>(this as BookingModel, _$identity);

  /// Serializes this BookingModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BookingModel&&(identical(other.id, id) || other.id == id)&&(identical(other.bookingNumber, bookingNumber) || other.bookingNumber == bookingNumber)&&(identical(other.customerId, customerId) || other.customerId == customerId)&&(identical(other.providerId, providerId) || other.providerId == providerId)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.bookingDate, bookingDate) || other.bookingDate == bookingDate)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime)&&(identical(other.serviceLocation, serviceLocation) || other.serviceLocation == serviceLocation)&&const DeepCollectionEquality().equals(other.services, services)&&(identical(other.subtotalAmount, subtotalAmount) || other.subtotalAmount == subtotalAmount)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.refundId, refundId) || other.refundId == refundId)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.customerNotes, customerNotes) || other.customerNotes == customerNotes)&&(identical(other.providerNotes, providerNotes) || other.providerNotes == providerNotes)&&(identical(other.cancellationReason, cancellationReason) || other.cancellationReason == cancellationReason)&&(identical(other.refundReason, refundReason) || other.refundReason == refundReason)&&(identical(other.customerName, customerName) || other.customerName == customerName)&&(identical(other.customerPhone, customerPhone) || other.customerPhone == customerPhone)&&(identical(other.customerEmail, customerEmail) || other.customerEmail == customerEmail)&&(identical(other.providerName, providerName) || other.providerName == providerName)&&(identical(other.providerPhone, providerPhone) || other.providerPhone == providerPhone)&&(identical(other.providerEmail, providerEmail) || other.providerEmail == providerEmail)&&(identical(other.reminderSent, reminderSent) || other.reminderSent == reminderSent)&&(identical(other.reminderSentAt, reminderSentAt) || other.reminderSentAt == reminderSentAt)&&(identical(other.confirmationSent, confirmationSent) || other.confirmationSent == confirmationSent)&&(identical(other.confirmationSentAt, confirmationSentAt) || other.confirmationSentAt == confirmationSentAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.review, review) || other.review == review)&&const DeepCollectionEquality().equals(other.attachments, attachments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,bookingNumber,customerId,providerId,type,status,paymentStatus,bookingDate,startTime,endTime,serviceLocation,const DeepCollectionEquality().hash(services),subtotalAmount,taxAmount,discountAmount,totalAmount,paymentMethod,paymentId,transactionId,refundId,notes,customerNotes,providerNotes,cancellationReason,refundReason,customerName,customerPhone,customerEmail,providerName,providerPhone,providerEmail,reminderSent,reminderSentAt,confirmationSent,confirmationSentAt,createdAt,updatedAt,isDeleted,rating,review,const DeepCollectionEquality().hash(attachments)]);

@override
String toString() {
  return 'BookingModel(id: $id, bookingNumber: $bookingNumber, customerId: $customerId, providerId: $providerId, type: $type, status: $status, paymentStatus: $paymentStatus, bookingDate: $bookingDate, startTime: $startTime, endTime: $endTime, serviceLocation: $serviceLocation, services: $services, subtotalAmount: $subtotalAmount, taxAmount: $taxAmount, discountAmount: $discountAmount, totalAmount: $totalAmount, paymentMethod: $paymentMethod, paymentId: $paymentId, transactionId: $transactionId, refundId: $refundId, notes: $notes, customerNotes: $customerNotes, providerNotes: $providerNotes, cancellationReason: $cancellationReason, refundReason: $refundReason, customerName: $customerName, customerPhone: $customerPhone, customerEmail: $customerEmail, providerName: $providerName, providerPhone: $providerPhone, providerEmail: $providerEmail, reminderSent: $reminderSent, reminderSentAt: $reminderSentAt, confirmationSent: $confirmationSent, confirmationSentAt: $confirmationSentAt, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, rating: $rating, review: $review, attachments: $attachments)';
}


}

/// @nodoc
abstract mixin class $BookingModelCopyWith<$Res>  {
  factory $BookingModelCopyWith(BookingModel value, $Res Function(BookingModel) _then) = _$BookingModelCopyWithImpl;
@useResult
$Res call({
 String id, String bookingNumber, String customerId, String providerId, BookingType type, BookingStatus status, PaymentStatus paymentStatus, DateTime bookingDate, DateTime startTime, DateTime endTime, BookingAddress serviceLocation, List<String> services, double subtotalAmount, double taxAmount, double discountAmount, double totalAmount, String? paymentMethod, String? paymentId, String? transactionId, String? refundId, String? notes, String? customerNotes, String? providerNotes, String? cancellationReason, String? refundReason, String? customerName, String? customerPhone, String? customerEmail, String? providerName, String? providerPhone, String? providerEmail, bool reminderSent, DateTime? reminderSentAt, bool confirmationSent, DateTime? confirmationSentAt, DateTime createdAt, DateTime updatedAt, bool isDeleted, double? rating, String? review, List<String>? attachments
});


$BookingAddressCopyWith<$Res> get serviceLocation;

}
/// @nodoc
class _$BookingModelCopyWithImpl<$Res>
    implements $BookingModelCopyWith<$Res> {
  _$BookingModelCopyWithImpl(this._self, this._then);

  final BookingModel _self;
  final $Res Function(BookingModel) _then;

/// Create a copy of BookingModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? bookingNumber = null,Object? customerId = null,Object? providerId = null,Object? type = null,Object? status = null,Object? paymentStatus = null,Object? bookingDate = null,Object? startTime = null,Object? endTime = null,Object? serviceLocation = null,Object? services = null,Object? subtotalAmount = null,Object? taxAmount = null,Object? discountAmount = null,Object? totalAmount = null,Object? paymentMethod = freezed,Object? paymentId = freezed,Object? transactionId = freezed,Object? refundId = freezed,Object? notes = freezed,Object? customerNotes = freezed,Object? providerNotes = freezed,Object? cancellationReason = freezed,Object? refundReason = freezed,Object? customerName = freezed,Object? customerPhone = freezed,Object? customerEmail = freezed,Object? providerName = freezed,Object? providerPhone = freezed,Object? providerEmail = freezed,Object? reminderSent = null,Object? reminderSentAt = freezed,Object? confirmationSent = null,Object? confirmationSentAt = freezed,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,Object? rating = freezed,Object? review = freezed,Object? attachments = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,bookingNumber: null == bookingNumber ? _self.bookingNumber : bookingNumber // ignore: cast_nullable_to_non_nullable
as String,customerId: null == customerId ? _self.customerId : customerId // ignore: cast_nullable_to_non_nullable
as String,providerId: null == providerId ? _self.providerId : providerId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as BookingType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as BookingStatus,paymentStatus: null == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as PaymentStatus,bookingDate: null == bookingDate ? _self.bookingDate : bookingDate // ignore: cast_nullable_to_non_nullable
as DateTime,startTime: null == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as DateTime,endTime: null == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as DateTime,serviceLocation: null == serviceLocation ? _self.serviceLocation : serviceLocation // ignore: cast_nullable_to_non_nullable
as BookingAddress,services: null == services ? _self.services : services // ignore: cast_nullable_to_non_nullable
as List<String>,subtotalAmount: null == subtotalAmount ? _self.subtotalAmount : subtotalAmount // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as double,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,paymentMethod: freezed == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as String?,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,refundId: freezed == refundId ? _self.refundId : refundId // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,customerNotes: freezed == customerNotes ? _self.customerNotes : customerNotes // ignore: cast_nullable_to_non_nullable
as String?,providerNotes: freezed == providerNotes ? _self.providerNotes : providerNotes // ignore: cast_nullable_to_non_nullable
as String?,cancellationReason: freezed == cancellationReason ? _self.cancellationReason : cancellationReason // ignore: cast_nullable_to_non_nullable
as String?,refundReason: freezed == refundReason ? _self.refundReason : refundReason // ignore: cast_nullable_to_non_nullable
as String?,customerName: freezed == customerName ? _self.customerName : customerName // ignore: cast_nullable_to_non_nullable
as String?,customerPhone: freezed == customerPhone ? _self.customerPhone : customerPhone // ignore: cast_nullable_to_non_nullable
as String?,customerEmail: freezed == customerEmail ? _self.customerEmail : customerEmail // ignore: cast_nullable_to_non_nullable
as String?,providerName: freezed == providerName ? _self.providerName : providerName // ignore: cast_nullable_to_non_nullable
as String?,providerPhone: freezed == providerPhone ? _self.providerPhone : providerPhone // ignore: cast_nullable_to_non_nullable
as String?,providerEmail: freezed == providerEmail ? _self.providerEmail : providerEmail // ignore: cast_nullable_to_non_nullable
as String?,reminderSent: null == reminderSent ? _self.reminderSent : reminderSent // ignore: cast_nullable_to_non_nullable
as bool,reminderSentAt: freezed == reminderSentAt ? _self.reminderSentAt : reminderSentAt // ignore: cast_nullable_to_non_nullable
as DateTime?,confirmationSent: null == confirmationSent ? _self.confirmationSent : confirmationSent // ignore: cast_nullable_to_non_nullable
as bool,confirmationSentAt: freezed == confirmationSentAt ? _self.confirmationSentAt : confirmationSentAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,rating: freezed == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double?,review: freezed == review ? _self.review : review // ignore: cast_nullable_to_non_nullable
as String?,attachments: freezed == attachments ? _self.attachments : attachments // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}
/// Create a copy of BookingModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BookingAddressCopyWith<$Res> get serviceLocation {
  
  return $BookingAddressCopyWith<$Res>(_self.serviceLocation, (value) {
    return _then(_self.copyWith(serviceLocation: value));
  });
}
}


/// Adds pattern-matching-related methods to [BookingModel].
extension BookingModelPatterns on BookingModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BookingModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BookingModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BookingModel value)  $default,){
final _that = this;
switch (_that) {
case _BookingModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BookingModel value)?  $default,){
final _that = this;
switch (_that) {
case _BookingModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String bookingNumber,  String customerId,  String providerId,  BookingType type,  BookingStatus status,  PaymentStatus paymentStatus,  DateTime bookingDate,  DateTime startTime,  DateTime endTime,  BookingAddress serviceLocation,  List<String> services,  double subtotalAmount,  double taxAmount,  double discountAmount,  double totalAmount,  String? paymentMethod,  String? paymentId,  String? transactionId,  String? refundId,  String? notes,  String? customerNotes,  String? providerNotes,  String? cancellationReason,  String? refundReason,  String? customerName,  String? customerPhone,  String? customerEmail,  String? providerName,  String? providerPhone,  String? providerEmail,  bool reminderSent,  DateTime? reminderSentAt,  bool confirmationSent,  DateTime? confirmationSentAt,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  double? rating,  String? review,  List<String>? attachments)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BookingModel() when $default != null:
return $default(_that.id,_that.bookingNumber,_that.customerId,_that.providerId,_that.type,_that.status,_that.paymentStatus,_that.bookingDate,_that.startTime,_that.endTime,_that.serviceLocation,_that.services,_that.subtotalAmount,_that.taxAmount,_that.discountAmount,_that.totalAmount,_that.paymentMethod,_that.paymentId,_that.transactionId,_that.refundId,_that.notes,_that.customerNotes,_that.providerNotes,_that.cancellationReason,_that.refundReason,_that.customerName,_that.customerPhone,_that.customerEmail,_that.providerName,_that.providerPhone,_that.providerEmail,_that.reminderSent,_that.reminderSentAt,_that.confirmationSent,_that.confirmationSentAt,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.rating,_that.review,_that.attachments);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String bookingNumber,  String customerId,  String providerId,  BookingType type,  BookingStatus status,  PaymentStatus paymentStatus,  DateTime bookingDate,  DateTime startTime,  DateTime endTime,  BookingAddress serviceLocation,  List<String> services,  double subtotalAmount,  double taxAmount,  double discountAmount,  double totalAmount,  String? paymentMethod,  String? paymentId,  String? transactionId,  String? refundId,  String? notes,  String? customerNotes,  String? providerNotes,  String? cancellationReason,  String? refundReason,  String? customerName,  String? customerPhone,  String? customerEmail,  String? providerName,  String? providerPhone,  String? providerEmail,  bool reminderSent,  DateTime? reminderSentAt,  bool confirmationSent,  DateTime? confirmationSentAt,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  double? rating,  String? review,  List<String>? attachments)  $default,) {final _that = this;
switch (_that) {
case _BookingModel():
return $default(_that.id,_that.bookingNumber,_that.customerId,_that.providerId,_that.type,_that.status,_that.paymentStatus,_that.bookingDate,_that.startTime,_that.endTime,_that.serviceLocation,_that.services,_that.subtotalAmount,_that.taxAmount,_that.discountAmount,_that.totalAmount,_that.paymentMethod,_that.paymentId,_that.transactionId,_that.refundId,_that.notes,_that.customerNotes,_that.providerNotes,_that.cancellationReason,_that.refundReason,_that.customerName,_that.customerPhone,_that.customerEmail,_that.providerName,_that.providerPhone,_that.providerEmail,_that.reminderSent,_that.reminderSentAt,_that.confirmationSent,_that.confirmationSentAt,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.rating,_that.review,_that.attachments);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String bookingNumber,  String customerId,  String providerId,  BookingType type,  BookingStatus status,  PaymentStatus paymentStatus,  DateTime bookingDate,  DateTime startTime,  DateTime endTime,  BookingAddress serviceLocation,  List<String> services,  double subtotalAmount,  double taxAmount,  double discountAmount,  double totalAmount,  String? paymentMethod,  String? paymentId,  String? transactionId,  String? refundId,  String? notes,  String? customerNotes,  String? providerNotes,  String? cancellationReason,  String? refundReason,  String? customerName,  String? customerPhone,  String? customerEmail,  String? providerName,  String? providerPhone,  String? providerEmail,  bool reminderSent,  DateTime? reminderSentAt,  bool confirmationSent,  DateTime? confirmationSentAt,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  double? rating,  String? review,  List<String>? attachments)?  $default,) {final _that = this;
switch (_that) {
case _BookingModel() when $default != null:
return $default(_that.id,_that.bookingNumber,_that.customerId,_that.providerId,_that.type,_that.status,_that.paymentStatus,_that.bookingDate,_that.startTime,_that.endTime,_that.serviceLocation,_that.services,_that.subtotalAmount,_that.taxAmount,_that.discountAmount,_that.totalAmount,_that.paymentMethod,_that.paymentId,_that.transactionId,_that.refundId,_that.notes,_that.customerNotes,_that.providerNotes,_that.cancellationReason,_that.refundReason,_that.customerName,_that.customerPhone,_that.customerEmail,_that.providerName,_that.providerPhone,_that.providerEmail,_that.reminderSent,_that.reminderSentAt,_that.confirmationSent,_that.confirmationSentAt,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.rating,_that.review,_that.attachments);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BookingModel implements BookingModel {
  const _BookingModel({required this.id, required this.bookingNumber, required this.customerId, required this.providerId, required this.type, required this.status, required this.paymentStatus, required this.bookingDate, required this.startTime, required this.endTime, required this.serviceLocation, required final  List<String> services, required this.subtotalAmount, required this.taxAmount, required this.discountAmount, required this.totalAmount, this.paymentMethod, this.paymentId, this.transactionId, this.refundId, this.notes, this.customerNotes, this.providerNotes, this.cancellationReason, this.refundReason, this.customerName, this.customerPhone, this.customerEmail, this.providerName, this.providerPhone, this.providerEmail, this.reminderSent = false, this.reminderSentAt, this.confirmationSent = false, this.confirmationSentAt, required this.createdAt, required this.updatedAt, this.isDeleted = false, this.rating, this.review, final  List<String>? attachments}): _services = services,_attachments = attachments;
  factory _BookingModel.fromJson(Map<String, dynamic> json) => _$BookingModelFromJson(json);

@override final  String id;
@override final  String bookingNumber;
@override final  String customerId;
@override final  String providerId;
@override final  BookingType type;
@override final  BookingStatus status;
@override final  PaymentStatus paymentStatus;
@override final  DateTime bookingDate;
@override final  DateTime startTime;
@override final  DateTime endTime;
@override final  BookingAddress serviceLocation;
 final  List<String> _services;
@override List<String> get services {
  if (_services is EqualUnmodifiableListView) return _services;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_services);
}

@override final  double subtotalAmount;
@override final  double taxAmount;
@override final  double discountAmount;
@override final  double totalAmount;
@override final  String? paymentMethod;
@override final  String? paymentId;
@override final  String? transactionId;
@override final  String? refundId;
@override final  String? notes;
@override final  String? customerNotes;
@override final  String? providerNotes;
@override final  String? cancellationReason;
@override final  String? refundReason;
@override final  String? customerName;
@override final  String? customerPhone;
@override final  String? customerEmail;
@override final  String? providerName;
@override final  String? providerPhone;
@override final  String? providerEmail;
@override@JsonKey() final  bool reminderSent;
@override final  DateTime? reminderSentAt;
@override@JsonKey() final  bool confirmationSent;
@override final  DateTime? confirmationSentAt;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool isDeleted;
@override final  double? rating;
@override final  String? review;
 final  List<String>? _attachments;
@override List<String>? get attachments {
  final value = _attachments;
  if (value == null) return null;
  if (_attachments is EqualUnmodifiableListView) return _attachments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of BookingModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BookingModelCopyWith<_BookingModel> get copyWith => __$BookingModelCopyWithImpl<_BookingModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BookingModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BookingModel&&(identical(other.id, id) || other.id == id)&&(identical(other.bookingNumber, bookingNumber) || other.bookingNumber == bookingNumber)&&(identical(other.customerId, customerId) || other.customerId == customerId)&&(identical(other.providerId, providerId) || other.providerId == providerId)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.bookingDate, bookingDate) || other.bookingDate == bookingDate)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime)&&(identical(other.serviceLocation, serviceLocation) || other.serviceLocation == serviceLocation)&&const DeepCollectionEquality().equals(other._services, _services)&&(identical(other.subtotalAmount, subtotalAmount) || other.subtotalAmount == subtotalAmount)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.refundId, refundId) || other.refundId == refundId)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.customerNotes, customerNotes) || other.customerNotes == customerNotes)&&(identical(other.providerNotes, providerNotes) || other.providerNotes == providerNotes)&&(identical(other.cancellationReason, cancellationReason) || other.cancellationReason == cancellationReason)&&(identical(other.refundReason, refundReason) || other.refundReason == refundReason)&&(identical(other.customerName, customerName) || other.customerName == customerName)&&(identical(other.customerPhone, customerPhone) || other.customerPhone == customerPhone)&&(identical(other.customerEmail, customerEmail) || other.customerEmail == customerEmail)&&(identical(other.providerName, providerName) || other.providerName == providerName)&&(identical(other.providerPhone, providerPhone) || other.providerPhone == providerPhone)&&(identical(other.providerEmail, providerEmail) || other.providerEmail == providerEmail)&&(identical(other.reminderSent, reminderSent) || other.reminderSent == reminderSent)&&(identical(other.reminderSentAt, reminderSentAt) || other.reminderSentAt == reminderSentAt)&&(identical(other.confirmationSent, confirmationSent) || other.confirmationSent == confirmationSent)&&(identical(other.confirmationSentAt, confirmationSentAt) || other.confirmationSentAt == confirmationSentAt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.review, review) || other.review == review)&&const DeepCollectionEquality().equals(other._attachments, _attachments));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,bookingNumber,customerId,providerId,type,status,paymentStatus,bookingDate,startTime,endTime,serviceLocation,const DeepCollectionEquality().hash(_services),subtotalAmount,taxAmount,discountAmount,totalAmount,paymentMethod,paymentId,transactionId,refundId,notes,customerNotes,providerNotes,cancellationReason,refundReason,customerName,customerPhone,customerEmail,providerName,providerPhone,providerEmail,reminderSent,reminderSentAt,confirmationSent,confirmationSentAt,createdAt,updatedAt,isDeleted,rating,review,const DeepCollectionEquality().hash(_attachments)]);

@override
String toString() {
  return 'BookingModel(id: $id, bookingNumber: $bookingNumber, customerId: $customerId, providerId: $providerId, type: $type, status: $status, paymentStatus: $paymentStatus, bookingDate: $bookingDate, startTime: $startTime, endTime: $endTime, serviceLocation: $serviceLocation, services: $services, subtotalAmount: $subtotalAmount, taxAmount: $taxAmount, discountAmount: $discountAmount, totalAmount: $totalAmount, paymentMethod: $paymentMethod, paymentId: $paymentId, transactionId: $transactionId, refundId: $refundId, notes: $notes, customerNotes: $customerNotes, providerNotes: $providerNotes, cancellationReason: $cancellationReason, refundReason: $refundReason, customerName: $customerName, customerPhone: $customerPhone, customerEmail: $customerEmail, providerName: $providerName, providerPhone: $providerPhone, providerEmail: $providerEmail, reminderSent: $reminderSent, reminderSentAt: $reminderSentAt, confirmationSent: $confirmationSent, confirmationSentAt: $confirmationSentAt, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, rating: $rating, review: $review, attachments: $attachments)';
}


}

/// @nodoc
abstract mixin class _$BookingModelCopyWith<$Res> implements $BookingModelCopyWith<$Res> {
  factory _$BookingModelCopyWith(_BookingModel value, $Res Function(_BookingModel) _then) = __$BookingModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String bookingNumber, String customerId, String providerId, BookingType type, BookingStatus status, PaymentStatus paymentStatus, DateTime bookingDate, DateTime startTime, DateTime endTime, BookingAddress serviceLocation, List<String> services, double subtotalAmount, double taxAmount, double discountAmount, double totalAmount, String? paymentMethod, String? paymentId, String? transactionId, String? refundId, String? notes, String? customerNotes, String? providerNotes, String? cancellationReason, String? refundReason, String? customerName, String? customerPhone, String? customerEmail, String? providerName, String? providerPhone, String? providerEmail, bool reminderSent, DateTime? reminderSentAt, bool confirmationSent, DateTime? confirmationSentAt, DateTime createdAt, DateTime updatedAt, bool isDeleted, double? rating, String? review, List<String>? attachments
});


@override $BookingAddressCopyWith<$Res> get serviceLocation;

}
/// @nodoc
class __$BookingModelCopyWithImpl<$Res>
    implements _$BookingModelCopyWith<$Res> {
  __$BookingModelCopyWithImpl(this._self, this._then);

  final _BookingModel _self;
  final $Res Function(_BookingModel) _then;

/// Create a copy of BookingModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? bookingNumber = null,Object? customerId = null,Object? providerId = null,Object? type = null,Object? status = null,Object? paymentStatus = null,Object? bookingDate = null,Object? startTime = null,Object? endTime = null,Object? serviceLocation = null,Object? services = null,Object? subtotalAmount = null,Object? taxAmount = null,Object? discountAmount = null,Object? totalAmount = null,Object? paymentMethod = freezed,Object? paymentId = freezed,Object? transactionId = freezed,Object? refundId = freezed,Object? notes = freezed,Object? customerNotes = freezed,Object? providerNotes = freezed,Object? cancellationReason = freezed,Object? refundReason = freezed,Object? customerName = freezed,Object? customerPhone = freezed,Object? customerEmail = freezed,Object? providerName = freezed,Object? providerPhone = freezed,Object? providerEmail = freezed,Object? reminderSent = null,Object? reminderSentAt = freezed,Object? confirmationSent = null,Object? confirmationSentAt = freezed,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,Object? rating = freezed,Object? review = freezed,Object? attachments = freezed,}) {
  return _then(_BookingModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,bookingNumber: null == bookingNumber ? _self.bookingNumber : bookingNumber // ignore: cast_nullable_to_non_nullable
as String,customerId: null == customerId ? _self.customerId : customerId // ignore: cast_nullable_to_non_nullable
as String,providerId: null == providerId ? _self.providerId : providerId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as BookingType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as BookingStatus,paymentStatus: null == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as PaymentStatus,bookingDate: null == bookingDate ? _self.bookingDate : bookingDate // ignore: cast_nullable_to_non_nullable
as DateTime,startTime: null == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as DateTime,endTime: null == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as DateTime,serviceLocation: null == serviceLocation ? _self.serviceLocation : serviceLocation // ignore: cast_nullable_to_non_nullable
as BookingAddress,services: null == services ? _self._services : services // ignore: cast_nullable_to_non_nullable
as List<String>,subtotalAmount: null == subtotalAmount ? _self.subtotalAmount : subtotalAmount // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as double,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,paymentMethod: freezed == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as String?,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,refundId: freezed == refundId ? _self.refundId : refundId // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,customerNotes: freezed == customerNotes ? _self.customerNotes : customerNotes // ignore: cast_nullable_to_non_nullable
as String?,providerNotes: freezed == providerNotes ? _self.providerNotes : providerNotes // ignore: cast_nullable_to_non_nullable
as String?,cancellationReason: freezed == cancellationReason ? _self.cancellationReason : cancellationReason // ignore: cast_nullable_to_non_nullable
as String?,refundReason: freezed == refundReason ? _self.refundReason : refundReason // ignore: cast_nullable_to_non_nullable
as String?,customerName: freezed == customerName ? _self.customerName : customerName // ignore: cast_nullable_to_non_nullable
as String?,customerPhone: freezed == customerPhone ? _self.customerPhone : customerPhone // ignore: cast_nullable_to_non_nullable
as String?,customerEmail: freezed == customerEmail ? _self.customerEmail : customerEmail // ignore: cast_nullable_to_non_nullable
as String?,providerName: freezed == providerName ? _self.providerName : providerName // ignore: cast_nullable_to_non_nullable
as String?,providerPhone: freezed == providerPhone ? _self.providerPhone : providerPhone // ignore: cast_nullable_to_non_nullable
as String?,providerEmail: freezed == providerEmail ? _self.providerEmail : providerEmail // ignore: cast_nullable_to_non_nullable
as String?,reminderSent: null == reminderSent ? _self.reminderSent : reminderSent // ignore: cast_nullable_to_non_nullable
as bool,reminderSentAt: freezed == reminderSentAt ? _self.reminderSentAt : reminderSentAt // ignore: cast_nullable_to_non_nullable
as DateTime?,confirmationSent: null == confirmationSent ? _self.confirmationSent : confirmationSent // ignore: cast_nullable_to_non_nullable
as bool,confirmationSentAt: freezed == confirmationSentAt ? _self.confirmationSentAt : confirmationSentAt // ignore: cast_nullable_to_non_nullable
as DateTime?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,rating: freezed == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double?,review: freezed == review ? _self.review : review // ignore: cast_nullable_to_non_nullable
as String?,attachments: freezed == attachments ? _self._attachments : attachments // ignore: cast_nullable_to_non_nullable
as List<String>?,
  ));
}

/// Create a copy of BookingModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BookingAddressCopyWith<$Res> get serviceLocation {
  
  return $BookingAddressCopyWith<$Res>(_self.serviceLocation, (value) {
    return _then(_self.copyWith(serviceLocation: value));
  });
}
}

// dart format on
