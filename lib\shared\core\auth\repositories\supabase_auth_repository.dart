import 'dart:async';
import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shivish/shared/core/auth/repositories/auth_repository.dart';
import 'package:shivish/shared/core/auth/models/login_request.dart';
import 'package:shivish/shared/core/auth/models/register_request.dart';
import 'package:shivish/shared/models/user/user_model.dart';
import 'package:shivish/shared/services/auth/supabase_auth_service.dart';

@Injectable(as: AuthRepository)
class SupabaseAuthRepository implements AuthRepository {
  final SupabaseAuthService _authService;
  
  SupabaseAuthRepository(this._authService);

  @override
  Stream<UserModel?> get authStateChanges {
    return _authService.authStateChanges.asyncMap((authState) async {
      final user = authState.session?.user;
      if (user == null) return null;
      
      // Get user data from hybrid database to maintain compatibility
      return await _authService.getUserFromDatabase(user.id);
    });
  }

  @override
  UserModel? get currentUser {
    final user = _authService.currentUser;
    if (user == null) return null;
    return _convertSupabaseUserToUserModel(user);
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    final user = _authService.currentUser;
    if (user == null) return null;
    
    // Get full user data from hybrid database
    return await _authService.getUserFromDatabase(user.id);
  }

  @override
  Future<UserModel> signInWithEmailAndPassword(LoginRequest request) async {
    try {
      final response = await _authService.signInWithEmail(
        email: request.email,
        password: request.password,
      );
      
      if (response.user == null) {
        throw Exception('Failed to sign in');
      }

      // Get user data from hybrid database
      final userModel = await _authService.getUserFromDatabase(response.user!.id);
      if (userModel == null) {
        throw Exception('User data not found');
      }

      return userModel;
    } catch (e) {
      throw Exception('Sign in failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> signInWithGoogle() async {
    try {
      final response = await _authService.signInWithGoogle();
      
      if (response.user == null) {
        throw Exception('Failed to sign in with Google');
      }

      // Get user data from hybrid database
      final userModel = await _authService.getUserFromDatabase(response.user!.id);
      if (userModel == null) {
        throw Exception('User data not found');
      }

      return userModel;
    } catch (e) {
      throw Exception('Google sign in failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> signInWithApple() async {
    try {
      final response = await _authService.signInWithApple();
      
      if (response.user == null) {
        throw Exception('Failed to sign in with Apple');
      }

      // Get user data from hybrid database
      final userModel = await _authService.getUserFromDatabase(response.user!.id);
      if (userModel == null) {
        throw Exception('User data not found');
      }

      return userModel;
    } catch (e) {
      throw Exception('Apple sign in failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> register(RegisterRequest request) async {
    try {
      final response = await _authService.signUpWithEmail(
        email: request.email,
        password: request.password,
        role: request.role,
        userData: {
          'displayName': request.displayName,
          'phoneNumber': request.phoneNumber,
          ...?request.metadata,
        },
      );
      
      if (response.user == null) {
        throw Exception('Failed to create user');
      }

      // Get user data from hybrid database
      final userModel = await _authService.getUserFromDatabase(response.user!.id);
      if (userModel == null) {
        throw Exception('User data not found after registration');
      }

      return userModel;
    } catch (e) {
      throw Exception('Registration failed: ${e.toString()}');
    }
  }

  @override
  Future<void> signOut({bool clearAllCredentials = false}) async {
    try {
      await _authService.signOut();
    } catch (e) {
      throw Exception('Sign out failed: ${e.toString()}');
    }
  }

  @override
  Future<void> resetPassword(String email) async {
    try {
      await _authService.resetPassword(email);
    } catch (e) {
      throw Exception('Password reset failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> updateProfile({
    String? displayName,
    String? photoUrl,
    String? phoneNumber,
  }) async {
    try {
      final response = await _authService.updateProfile(
        displayName: displayName,
        photoUrl: photoUrl,
        data: phoneNumber != null ? {'phone_number': phoneNumber} : null,
      );
      
      if (response.user == null) {
        throw Exception('Failed to update profile');
      }

      // Get updated user data from hybrid database
      final userModel = await _authService.getUserFromDatabase(response.user!.id);
      if (userModel == null) {
        throw Exception('User data not found after update');
      }

      return userModel;
    } catch (e) {
      throw Exception('Profile update failed: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteAccount() async {
    try {
      await _authService.deleteAccount();
    } catch (e) {
      throw Exception('Account deletion failed: ${e.toString()}');
    }
  }

  @override
  Future<void> verifyPhone(String phoneNumber) async {
    try {
      await _authService.signInWithPhone(phoneNumber);
    } catch (e) {
      throw Exception('Phone verification failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> verifyPhoneCode(String code) async {
    try {
      final user = _authService.currentUser;
      if (user?.phone == null) {
        throw Exception('No phone number to verify');
      }

      final response = await _authService.verifyPhoneOTP(
        phone: user!.phone!,
        token: code,
      );

      if (response.user == null) {
        throw Exception('Failed to verify phone code');
      }

      // Get user data from hybrid database
      final userModel = await _authService.getUserFromDatabase(response.user!.id);
      if (userModel == null) {
        throw Exception('User data not found after phone verification');
      }

      return userModel;
    } catch (e) {
      throw Exception('Phone code verification failed: ${e.toString()}');
    }
  }

  @override
  Future<bool> isEmailVerified() async {
    final user = _authService.currentUser;
    return user?.emailConfirmedAt != null;
  }

  @override
  Future<void> sendEmailVerification() async {
    try {
      final user = _authService.currentUser;
      if (user?.email == null) {
        throw Exception('No email to verify');
      }

      // Supabase handles email verification during sign up
      // For manual verification, we can resend the confirmation email
      await _authService.resetPassword(user!.email!);
    } catch (e) {
      throw Exception('Email verification failed: ${e.toString()}');
    }
  }

  /// Convert Supabase User to UserModel for compatibility
  UserModel _convertSupabaseUserToUserModel(User user) {
    return UserModel(
      id: user.id,
      email: user.email ?? '',
      displayName: user.userMetadata?['display_name'] ?? '',
      photoUrl: user.userMetadata?['avatar_url'],
      phoneNumber: user.phone,
      emailVerified: user.emailConfirmedAt != null,
      role: _parseUserRole(user.userMetadata?['role']),
      status: UserStatus.active,
      verificationStatus: user.emailConfirmedAt != null 
          ? VerificationStatus.verified 
          : VerificationStatus.unverified,
      firstName: user.userMetadata?['first_name'] ?? '',
      lastName: user.userMetadata?['last_name'] ?? '',
      createdAt: _parseDateTime(user.createdAt) ?? DateTime.now(),
      updatedAt: _parseDateTime(user.updatedAt) ?? DateTime.now(),
    );
  }

  /// Parse user role from string
  UserRole _parseUserRole(dynamic roleData) {
    if (roleData is String) {
      try {
        return UserRole.values.firstWhere(
          (role) => role.name == roleData,
          orElse: () => UserRole.buyer,
        );
      } catch (e) {
        return UserRole.buyer;
      }
    }
    return UserRole.buyer;
  }



  /// Parse DateTime from various formats
  DateTime? _parseDateTime(dynamic dateTime) {
    if (dateTime == null) return null;
    if (dateTime is DateTime) return dateTime;
    if (dateTime is String) {
      try {
        return DateTime.parse(dateTime);
      } catch (e) {
        return null;
      }
    }
    return null;
  }
}
