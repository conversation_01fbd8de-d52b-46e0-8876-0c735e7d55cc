import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Supabase configuration class
/// Production-ready configuration using environment variables
class SupabaseConfig {
  // Production-ready configuration using environment variables
  // Set these in your environment or CI/CD pipeline:
  // - SUPABASE_URL: Your Supabase project URL
  // - SUPABASE_ANON_KEY: Your Supabase anonymous key
  // - SUPABASE_SERVICE_ROLE_KEY: Your Supabase service role key (for admin operations)

  static String get supabaseUrl =>
      dotenv.env['SUPABASE_URL'] ??
      const String.fromEnvironment(
        'SUPABASE_URL',
        defaultValue: kDebugMode
            ? 'https://localhost:54321' // Local Supabase for development
            : 'https://your-project-ref.supabase.co', // Fallback for production
      );

  static String get supabaseAnonKey =>
      dotenv.env['SUPABASE_ANON_KEY'] ??
      const String.fromEnvironment(
        'SUPABASE_ANON_KEY',
        defaultValue: kDebugMode
            ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' // Local Supabase anon key
            : 'your-anon-key-here', // Fallback for production
      );

  // Service role key for admin operations (optional)
  static String get supabaseServiceRoleKey =>
      dotenv.env['SUPABASE_SERVICE_ROLE_KEY'] ??
      const String.fromEnvironment(
        'SUPABASE_SERVICE_ROLE_KEY',
        defaultValue: kDebugMode
            ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU' // Local Supabase service role key
            : '', // Empty string for production - must be set explicitly
      );

  // Helper methods for configuration validation
  static bool get isConfigured =>
      supabaseUrl.isNotEmpty &&
      supabaseUrl != 'https://your-project-ref.supabase.co' &&
      supabaseUrl != 'https://localhost:54321' &&
      supabaseAnonKey.isNotEmpty &&
      supabaseAnonKey != 'your-anon-key-here';

  static bool get hasServiceRoleKey => supabaseServiceRoleKey.isNotEmpty;

  static bool get isLocalDevelopment =>
      supabaseUrl.contains('localhost') || supabaseUrl.contains('127.0.0.1');

  // OAuth redirect URLs for different platforms
  static const String googleRedirectUrl = 'com.shivish://auth/callback';
  static const String appleRedirectUrl = 'com.shivish://auth/callback';

  // Deep link scheme for your app
  static const String deepLinkScheme = 'com.shivish';

  // Auth settings
  static const Duration sessionTimeout = Duration(hours: 24);
  static const bool persistSession = true;

  // Email templates (configurable via environment variables)
  static const String confirmEmailRedirectUrl = String.fromEnvironment(
    'SUPABASE_CONFIRM_EMAIL_REDIRECT_URL',
    defaultValue: 'com.shivish://auth/confirm',
  );

  static const String resetPasswordRedirectUrl = String.fromEnvironment(
    'SUPABASE_RESET_PASSWORD_REDIRECT_URL',
    defaultValue: 'com.shivish://auth/reset',
  );

  // Additional configuration options
  static const String jwtSecret = String.fromEnvironment(
    'SUPABASE_JWT_SECRET',
    defaultValue: kDebugMode
        ? 'super-secret-jwt-token-with-at-least-32-characters-long'
        : '',
  );

  // Database configuration
  static const String databaseUrl = String.fromEnvironment(
    'SUPABASE_DATABASE_URL',
    defaultValue: kDebugMode
        ? 'postgresql://postgres:postgres@localhost:54322/postgres'
        : '',
  );

  // Storage configuration
  static const String storageUrl = String.fromEnvironment(
    'SUPABASE_STORAGE_URL',
    defaultValue: kDebugMode ? 'http://localhost:54321/storage/v1' : '',
  );

  // Edge functions configuration
  static const String functionsUrl = String.fromEnvironment(
    'SUPABASE_FUNCTIONS_URL',
    defaultValue: kDebugMode ? 'http://localhost:54321/functions/v1' : '',
  );

  // Realtime configuration
  static const String realtimeUrl = String.fromEnvironment(
    'SUPABASE_REALTIME_URL',
    defaultValue: kDebugMode ? 'ws://localhost:54321/realtime/v1' : '',
  );

  /// Get the complete Supabase configuration as a map
  static Map<String, dynamic> get configMap => {
    'url': supabaseUrl,
    'anonKey': supabaseAnonKey,
    'serviceRoleKey': supabaseServiceRoleKey,
    'jwtSecret': jwtSecret,
    'databaseUrl': databaseUrl,
    'storageUrl': storageUrl,
    'functionsUrl': functionsUrl,
    'realtimeUrl': realtimeUrl,
    'confirmEmailRedirectUrl': confirmEmailRedirectUrl,
    'resetPasswordRedirectUrl': resetPasswordRedirectUrl,
    'googleRedirectUrl': googleRedirectUrl,
    'appleRedirectUrl': appleRedirectUrl,
    'deepLinkScheme': deepLinkScheme,
    'sessionTimeout': sessionTimeout.inMilliseconds,
    'persistSession': persistSession,
    'isConfigured': isConfigured,
    'hasServiceRoleKey': hasServiceRoleKey,
    'isLocalDevelopment': isLocalDevelopment,
  };

  /// Validate the configuration
  static String? validateConfiguration() {
    if (!isConfigured) {
      return 'Supabase configuration is incomplete. Please set SUPABASE_URL and SUPABASE_ANON_KEY environment variables.';
    }

    if (!kDebugMode && supabaseUrl.contains('localhost')) {
      return 'Production build should not use localhost URLs. Please set proper SUPABASE_URL.';
    }

    if (!kDebugMode && supabaseAnonKey.length < 100) {
      return 'Supabase anonymous key appears to be invalid. Please check SUPABASE_ANON_KEY.';
    }

    return null; // Configuration is valid
  }
}
