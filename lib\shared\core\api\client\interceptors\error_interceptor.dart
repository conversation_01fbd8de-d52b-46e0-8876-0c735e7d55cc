import 'dart:developer' as developer;
import 'package:dio/dio.dart';

class <PERSON><PERSON>rInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Log the error
    developer.log(
      'API Error',
      error: err,
      name: 'ErrorInterceptor',
      time: DateTime.now(),
    );

    // Add request info to error
    final requestInfo = {
      'method': err.requestOptions.method,
      'path': err.requestOptions.path,
      'data': err.requestOptions.data,
      'queryParameters': err.requestOptions.queryParameters,
      'headers': err.requestOptions.headers,
    };

    // Add response info to error if available
    final responseInfo = err.response != null
        ? {
            'statusCode': err.response?.statusCode,
            'data': err.response?.data,
            'headers': err.response?.headers.map,
          }
        : null;

    // Add error context to DioException
    err.requestOptions.extra['errorContext'] = {
      'request': requestInfo,
      'response': responseInfo,
      'timestamp': DateTime.now().toIso8601String(),
      'type': err.type.toString(),
      'message': err.message,
    };

    handler.next(err);
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Add request timestamp
    options.extra['requestTimestamp'] = DateTime.now().millisecondsSinceEpoch;
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Calculate response time
    final requestTimestamp =
        response.requestOptions.extra['requestTimestamp'] as int?;
    if (requestTimestamp != null) {
      final responseTime =
          DateTime.now().millisecondsSinceEpoch - requestTimestamp;
      response.requestOptions.extra['responseTime'] = responseTime;
    }

    handler.next(response);
  }
}
