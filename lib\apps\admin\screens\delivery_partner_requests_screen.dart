import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../shared/utils/logger.dart';
import '../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../shared/ui_components/errors/error_message.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';

final _logger = getLogger('DeliveryPartnerRequestsScreen');

/// Provider for pending delivery partners using hybrid database
final pendingDeliveryPartnersProvider = FutureProvider<List<Map<String, dynamic>>>((
  ref,
) async {
  final databaseService = DatabaseService(DatabaseConfig.fromEnvironment());

  try {
    _logger.info('Loading pending delivery partner requests');
    final deliveryPartners = await databaseService.getAll(
      'delivery_persons',
      where: 'is_verified = ? AND is_active = ? AND is_deleted = ?',
      whereParams: [false, false, false],
      orderBy: 'created_at DESC',
    );

    _logger.info(
      'Successfully loaded ${deliveryPartners.length} pending delivery partner requests',
    );
    return deliveryPartners;
  } catch (e) {
    _logger.severe('Error loading pending delivery partners: $e');
    throw Exception('Failed to load delivery partner requests: $e');
  }
});

/// Provider for tracking loading states of individual operations
final loadingStatesProvider = StateProvider<Map<String, bool>>((ref) => {});

/// Screen for admin to approve delivery partner requests
class DeliveryPartnerRequestsScreen extends ConsumerWidget {
  const DeliveryPartnerRequestsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pendingPartnersAsync = ref.watch(pendingDeliveryPartnersProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Delivery Partner Requests')),
      body: pendingPartnersAsync.when(
        data: (pendingPartners) {
          if (pendingPartners.isEmpty) {
            return const Center(child: Text('No pending delivery partners'));
          }

          return ListView.builder(
            itemCount: pendingPartners.length,
            itemBuilder: (context, index) {
              final partner = pendingPartners[index];
              return _buildPartnerCard(context, ref, partner);
            },
          );
        },
        loading: () => const LoadingIndicator(),
        error: (error, stackTrace) {
          _logger.severe(
            'Error loading pending delivery partners: $error\n$stackTrace',
          );
          return ErrorMessage(message: error.toString());
        },
      ),
    );
  }

  Widget _buildPartnerCard(
    BuildContext context,
    WidgetRef ref,
    Map<String, dynamic> partner,
  ) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM d, yyyy');

    // Format created date - handle different timestamp formats
    String createdDate = 'Unknown date';
    final createdAt = partner['created_at'] ?? partner['createdAt'];

    if (createdAt is String) {
      try {
        createdDate = dateFormat.format(DateTime.parse(createdAt));
      } catch (e) {
        _logger.warning('Failed to parse createdAt string: $e');
      }
    } else if (createdAt is Map<String, dynamic>) {
      // Handle potential timestamp objects from database
      if (createdAt.containsKey('_seconds')) {
        final date = DateTime.fromMillisecondsSinceEpoch(
          (createdAt['_seconds'] as int) * 1000,
        );
        createdDate = dateFormat.format(date);
      }
    } else if (createdAt != null) {
      _logger.warning('Unexpected createdAt type: ${createdAt.runtimeType}');
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: theme.colorScheme.primary,
                  child: Text(
                    partner['name']?.substring(0, 1).toUpperCase() ?? 'D',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        partner['name'] ?? 'Unknown',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        partner['email'] ?? 'No email',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
                Chip(
                  label: const Text('Pending'),
                  backgroundColor: Colors.orange.shade100,
                  labelStyle: TextStyle(color: Colors.orange.shade800),
                ),
              ],
            ),
            const Divider(height: 24),
            _buildInfoRow('Phone', partner['phone'] ?? 'Not provided'),
            _buildInfoRow(
              'Vehicle Type',
              partner['vehicleType'] ?? 'Not provided',
            ),
            _buildInfoRow(
              'Vehicle Number',
              partner['vehicleNumber'] ?? 'Not provided',
            ),
            _buildInfoRow(
              'Aadhar Number',
              partner['aadharNumber'] ?? 'Not provided',
            ),
            _buildInfoRow(
              'Driving License',
              partner['drivingLicense'] ?? 'Not provided',
            ),
            _buildInfoRow('Registered On', createdDate),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => _showRejectDialog(context, ref, partner),
                  child: const Text('Reject'),
                ),
                const SizedBox(width: 8),
                FilledButton(
                  onPressed: () => _showApproveDialog(context, ref, partner),
                  child: const Text('Approve'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showApproveDialog(
    BuildContext context,
    WidgetRef ref,
    Map<String, dynamic> partner,
  ) {
    final notesController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false, // Prevent accidental dismissal
      builder: (context) => AlertDialog(
        icon: Icon(Icons.check_circle_outline, color: Colors.green, size: 48),
        title: const Text(
          'Approve Delivery Partner',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                style: Theme.of(context).textTheme.bodyMedium,
                children: [
                  const TextSpan(text: 'Are you sure you want to approve '),
                  TextSpan(
                    text: '${partner['name']}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const TextSpan(text: '?'),
                ],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'This action will grant them access to accept delivery requests.',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: notesController,
              decoration: const InputDecoration(
                labelText: 'Approval Notes (Optional)',
                hintText: 'Add any notes about this approval...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note_add),
              ),
              maxLines: 3,
              maxLength: 500,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          Consumer(
            builder: (context, ref, child) {
              final loadingStates = ref.watch(loadingStatesProvider);
              final isLoading =
                  loadingStates['approve_${partner['id']}'] ?? false;

              return FilledButton(
                onPressed: isLoading
                    ? null
                    : () async {
                        Navigator.pop(context);

                        // Set loading state
                        ref
                            .read(loadingStatesProvider.notifier)
                            .update(
                              (state) => {
                                ...state,
                                'approve_${partner['id']}': true,
                              },
                            );

                        try {
                          await _approveDeliveryPartner(
                            partner['id'],
                            notes: notesController.text.isNotEmpty
                                ? notesController.text
                                : null,
                          );

                          // Refresh the provider to update the UI
                          ref.invalidate(pendingDeliveryPartnersProvider);

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  '${partner['name']} has been approved successfully',
                                ),
                                backgroundColor: Colors.green,
                                duration: const Duration(seconds: 3),
                              ),
                            );
                          }
                        } on ArgumentError catch (e) {
                          _logger.warning(
                            'Validation error approving delivery partner: $e',
                          );

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Validation Error: ${e.message}'),
                                backgroundColor: Colors.orange,
                                duration: const Duration(seconds: 4),
                              ),
                            );
                          }
                        } on Exception catch (e) {
                          _logger.severe(
                            'Business error approving delivery partner: $e',
                          );

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Error: ${e.toString().replaceFirst('Exception: ', '')}',
                                ),
                                backgroundColor: Colors.red,
                                duration: const Duration(seconds: 4),
                              ),
                            );
                          }
                        } catch (e) {
                          _logger.severe(
                            'Unexpected error approving delivery partner: $e',
                          );

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Unexpected error occurred. Please try again.',
                                ),
                                backgroundColor: Colors.red,
                                duration: const Duration(seconds: 4),
                              ),
                            );
                          }
                        } finally {
                          // Clear loading state
                          ref
                              .read(loadingStatesProvider.notifier)
                              .update(
                                (state) => {
                                  ...state,
                                  'approve_${partner['id']}': false,
                                },
                              );
                        }
                      },
                child: isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Text('Approve'),
              );
            },
          ),
        ],
      ),
    );
  }

  void _showRejectDialog(
    BuildContext context,
    WidgetRef ref,
    Map<String, dynamic> partner,
  ) {
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false, // Prevent accidental dismissal
      builder: (context) => AlertDialog(
        icon: Icon(Icons.cancel_outlined, color: Colors.red, size: 48),
        title: const Text(
          'Reject Delivery Partner',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                style: Theme.of(context).textTheme.bodyMedium,
                children: [
                  const TextSpan(text: 'Are you sure you want to reject '),
                  TextSpan(
                    text: '${partner['name']}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const TextSpan(text: '?'),
                ],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'This action will prevent them from accessing the delivery platform. Please provide a clear reason.',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason for Rejection (Required)',
                hintText: 'Please provide a detailed reason for rejection...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.report_problem),
              ),
              maxLines: 3,
              maxLength: 500,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          Consumer(
            builder: (context, ref, child) {
              final loadingStates = ref.watch(loadingStatesProvider);
              final isLoading =
                  loadingStates['reject_${partner['id']}'] ?? false;

              return FilledButton(
                onPressed: isLoading
                    ? null
                    : () async {
                        if (reasonController.text.isEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Please provide a reason for rejection',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        Navigator.pop(context);

                        // Set loading state
                        ref
                            .read(loadingStatesProvider.notifier)
                            .update(
                              (state) => {
                                ...state,
                                'reject_${partner['id']}': true,
                              },
                            );

                        try {
                          await _rejectDeliveryPartner(
                            partner['id'],
                            reason: reasonController.text,
                          );

                          // Refresh the provider to update the UI
                          ref.invalidate(pendingDeliveryPartnersProvider);

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  '${partner['name']} has been rejected successfully',
                                ),
                                backgroundColor: Colors.orange,
                                duration: const Duration(seconds: 3),
                              ),
                            );
                          }
                        } on ArgumentError catch (e) {
                          _logger.warning(
                            'Validation error rejecting delivery partner: $e',
                          );

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Validation Error: ${e.message}'),
                                backgroundColor: Colors.orange,
                                duration: const Duration(seconds: 4),
                              ),
                            );
                          }
                        } on Exception catch (e) {
                          _logger.severe(
                            'Business error rejecting delivery partner: $e',
                          );

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Error: ${e.toString().replaceFirst('Exception: ', '')}',
                                ),
                                backgroundColor: Colors.red,
                                duration: const Duration(seconds: 4),
                              ),
                            );
                          }
                        } catch (e) {
                          _logger.severe(
                            'Unexpected error rejecting delivery partner: $e',
                          );

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Unexpected error occurred. Please try again.',
                                ),
                                backgroundColor: Colors.red,
                                duration: const Duration(seconds: 4),
                              ),
                            );
                          }
                        } finally {
                          // Clear loading state
                          ref
                              .read(loadingStatesProvider.notifier)
                              .update(
                                (state) => {
                                  ...state,
                                  'reject_${partner['id']}': false,
                                },
                              );
                        }
                      },
                child: isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Text('Reject'),
              );
            },
          ),
        ],
      ),
    );
  }

  Future<void> _approveDeliveryPartner(
    String deliveryPartnerId, {
    String? notes,
  }) async {
    // Input validation
    if (deliveryPartnerId.isEmpty) {
      throw ArgumentError('Delivery partner ID cannot be empty');
    }

    try {
      final databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
      final now = DateTime.now().toIso8601String();

      // Verify the delivery partner exists and is in pending state
      final existingPartner = await databaseService.find(
        'delivery_persons',
        deliveryPartnerId,
      );
      if (existingPartner == null) {
        throw Exception(
          'Delivery partner not found with ID: $deliveryPartnerId',
        );
      }

      // Check if already processed
      final currentStatus = existingPartner['status'] as String?;
      if (currentStatus == 'approved') {
        throw Exception('Delivery partner is already approved');
      }
      if (currentStatus == 'rejected') {
        throw Exception(
          'Cannot approve a rejected delivery partner. Please contact support.',
        );
      }

      // Get current admin user ID from Supabase auth
      final supabase = Supabase.instance.client;
      final currentUser = supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('Admin user not authenticated');
      }

      final adminId = currentUser.id;
      final adminEmail = currentUser.email ?? '<EMAIL>';

      _logger.info(
        'Approving delivery partner: $deliveryPartnerId by admin: $adminId ($adminEmail)',
      );

      // Perform the approval update
      await databaseService.update('delivery_persons', deliveryPartnerId, {
        'is_verified': true,
        'is_active': true,
        'approved_by': adminId,
        'approved_by_email': adminEmail,
        'approver_role': 'admin',
        'approved_at': now,
        'approval_notes': notes?.trim(),
        'updated_at': now,
        'status': 'approved',
      });

      // Create audit log entry
      await _createAuditLog(
        databaseService: databaseService,
        action: 'APPROVE_DELIVERY_PARTNER',
        entityType: 'delivery_person',
        entityId: deliveryPartnerId,
        adminId: adminId,
        adminEmail: adminEmail,
        details: {
          'partner_name': existingPartner['name'],
          'partner_email': existingPartner['email'],
          'partner_phone': existingPartner['phone'],
          'approval_notes': notes?.trim(),
          'previous_status': currentStatus,
          'new_status': 'approved',
        },
        timestamp: now,
      );

      _logger.info(
        'Successfully approved delivery partner: $deliveryPartnerId by admin: $adminId',
      );
    } on ArgumentError catch (e) {
      _logger.warning('Invalid argument for approving delivery partner: $e');
      rethrow;
    } on Exception catch (e) {
      _logger.severe('Business logic error approving delivery partner: $e');
      rethrow;
    } catch (e, stackTrace) {
      _logger.severe(
        'Unexpected error approving delivery partner: $e\n$stackTrace',
      );
      throw Exception('Failed to approve delivery partner: ${e.toString()}');
    }
  }

  Future<void> _rejectDeliveryPartner(
    String deliveryPartnerId, {
    required String reason,
  }) async {
    // Input validation
    if (deliveryPartnerId.isEmpty) {
      throw ArgumentError('Delivery partner ID cannot be empty');
    }
    if (reason.trim().isEmpty) {
      throw ArgumentError('Rejection reason cannot be empty');
    }
    if (reason.trim().length < 10) {
      throw ArgumentError(
        'Rejection reason must be at least 10 characters long',
      );
    }

    try {
      final databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
      final now = DateTime.now().toIso8601String();

      // Verify the delivery partner exists and is in pending state
      final existingPartner = await databaseService.find(
        'delivery_persons',
        deliveryPartnerId,
      );
      if (existingPartner == null) {
        throw Exception(
          'Delivery partner not found with ID: $deliveryPartnerId',
        );
      }

      // Check if already processed
      final currentStatus = existingPartner['status'] as String?;
      if (currentStatus == 'approved') {
        throw Exception(
          'Cannot reject an approved delivery partner. Please contact support.',
        );
      }
      if (currentStatus == 'rejected') {
        throw Exception('Delivery partner is already rejected');
      }

      // Get current admin user ID from Supabase auth
      final supabase = Supabase.instance.client;
      final currentUser = supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('Admin user not authenticated');
      }

      final adminId = currentUser.id;
      final adminEmail = currentUser.email ?? '<EMAIL>';
      final trimmedReason = reason.trim();

      _logger.info(
        'Rejecting delivery partner: $deliveryPartnerId with reason: $trimmedReason by admin: $adminId ($adminEmail)',
      );

      // Perform the rejection update
      await databaseService.update('delivery_persons', deliveryPartnerId, {
        'is_verified': false,
        'is_active': false,
        'rejected_by': adminId,
        'rejected_by_email': adminEmail,
        'rejector_role': 'admin',
        'rejected_at': now,
        'rejection_reason': trimmedReason,
        'updated_at': now,
        'status': 'rejected',
      });

      // Create audit log entry
      await _createAuditLog(
        databaseService: databaseService,
        action: 'REJECT_DELIVERY_PARTNER',
        entityType: 'delivery_person',
        entityId: deliveryPartnerId,
        adminId: adminId,
        adminEmail: adminEmail,
        details: {
          'partner_name': existingPartner['name'],
          'partner_email': existingPartner['email'],
          'partner_phone': existingPartner['phone'],
          'rejection_reason': trimmedReason,
          'previous_status': currentStatus,
          'new_status': 'rejected',
        },
        timestamp: now,
      );

      _logger.info(
        'Successfully rejected delivery partner: $deliveryPartnerId by admin: $adminId',
      );
    } on ArgumentError catch (e) {
      _logger.warning('Invalid argument for rejecting delivery partner: $e');
      rethrow;
    } on Exception catch (e) {
      _logger.severe('Business logic error rejecting delivery partner: $e');
      rethrow;
    } catch (e, stackTrace) {
      _logger.severe(
        'Unexpected error rejecting delivery partner: $e\n$stackTrace',
      );
      throw Exception('Failed to reject delivery partner: ${e.toString()}');
    }
  }

  /// Create audit log entry for admin actions
  Future<void> _createAuditLog({
    required DatabaseService databaseService,
    required String action,
    required String entityType,
    required String entityId,
    required String adminId,
    required String adminEmail,
    required Map<String, dynamic> details,
    required String timestamp,
  }) async {
    try {
      final auditLogEntry = {
        'id': '${DateTime.now().millisecondsSinceEpoch}_${adminId}_$action',
        'action': action,
        'entity_type': entityType,
        'entity_id': entityId,
        'admin_id': adminId,
        'admin_email': adminEmail,
        'details': details,
        'timestamp': timestamp,
        'created_at': timestamp,
        'ip_address': 'unknown', // Could be enhanced to capture actual IP
        'user_agent':
            'admin_app', // Could be enhanced to capture actual user agent
      };

      await databaseService.create('admin_audit_logs', auditLogEntry);

      _logger.info(
        'Audit log created: $action for $entityType:$entityId by $adminId',
      );
    } catch (e) {
      // Don't fail the main operation if audit logging fails, but log the error
      _logger.severe('Failed to create audit log: $e');
    }
  }
}
