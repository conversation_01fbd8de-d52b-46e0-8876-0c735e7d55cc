import 'package:flutter/foundation.dart';
import '../../domain/repositories/inventory_repository.dart';
import '../../../../shared/models/inventory_model.dart';
import '../../../../shared/database/services/database_service.dart';

class InventoryRepositoryImpl implements InventoryRepository {
  final DatabaseService _databaseService;
  static const String _collection = 'inventory';

  InventoryRepositoryImpl(this._databaseService);

  @override
  Future<List<InventoryModel>> getInventory() async {
    try {
      final inventory = await _databaseService.getAll(
        _collection,
        where: 'is_deleted = @param0',
        whereParams: [false],
      );

      return inventory.map((data) => InventoryModel.fromJson(data)).toList();
    } catch (e) {
      debugPrint('Error getting inventory: $e');
      rethrow;
    }
  }

  @override
  Future<void> addStock(String inventoryId, int quantity) async {
    try {
      final doc = await _databaseService.find(_collection, inventoryId);
      if (doc == null) {
        throw Exception('Inventory not found');
      }

      final currentStock = doc['current_stock'] as int;
      await _updateStock(inventoryId, currentStock + quantity);
    } catch (e) {
      debugPrint('Error adding stock: $e');
      rethrow;
    }
  }

  @override
  Future<void> removeStock(String inventoryId, int quantity) async {
    try {
      final doc = await _databaseService.find(_collection, inventoryId);
      if (doc == null) {
        throw Exception('Inventory not found');
      }

      final currentStock = doc['current_stock'] as int;
      if (currentStock < quantity) {
        throw Exception('Insufficient stock');
      }

      await _updateStock(inventoryId, currentStock - quantity);
    } catch (e) {
      debugPrint('Error removing stock: $e');
      rethrow;
    }
  }

  @override
  Future<void> setLowStockAlert(String inventoryId, bool enabled) async {
    try {
      await _databaseService.update(_collection, inventoryId, {
        'has_low_stock_alert': enabled,
        'last_updated': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error setting low stock alert: $e');
      rethrow;
    }
  }

  Future<void> _updateStock(String inventoryId, int newStock) async {
    try {
      await _databaseService.update(_collection, inventoryId, {
        'current_stock': newStock,
        'last_updated': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error updating stock: $e');
      rethrow;
    }
  }

  @override
  Future<InventoryModel> getInventoryById(String productId) async {
    try {
      final doc = await _databaseService.find(_collection, productId);
      if (doc == null) {
        throw Exception('Inventory not found');
      }
      return InventoryModel.fromJson(doc);
    } catch (e) {
      debugPrint('Error getting inventory by ID: $e');
      rethrow;
    }
  }

  @override
  Future<List<InventoryModel>> getSellerInventory(String sellerId) async {
    try {
      final inventory = await _databaseService.getAll(
        _collection,
        where: 'seller_id = @param0 AND is_deleted = @param1',
        whereParams: [sellerId, false],
      );

      return inventory.map((data) => InventoryModel.fromJson(data)).toList();
    } catch (e) {
      debugPrint('Error getting seller inventory: $e');
      rethrow;
    }
  }

  @override
  Future<List<InventoryModel>> getLowStockItems(String sellerId) async {
    try {
      final inventory = await _databaseService.getAll(
        _collection,
        where: 'seller_id = @param0 AND is_deleted = @param1',
        whereParams: [sellerId, false],
      );

      return inventory
          .map((data) => InventoryModel.fromJson(data))
          .where(
            (inventory) => inventory.currentStock <= inventory.minimumStock,
          )
          .toList();
    } catch (e) {
      debugPrint('Error getting low stock items: $e');
      rethrow;
    }
  }

  @override
  Stream<List<InventoryModel>> watchInventory(String sellerId) {
    try {
      return _databaseService
          .watchCollection(
            _collection,
            where: 'seller_id = @param0 AND is_deleted = @param1',
            whereParams: [sellerId, false],
          )
          .map(
            (inventory) =>
                inventory.map((data) => InventoryModel.fromJson(data)).toList(),
          );
    } catch (e) {
      debugPrint('Error watching inventory: $e');
      return Stream.value([]);
    }
  }

  @override
  Future<void> createInventory(InventoryModel inventory) async {
    try {
      final data = inventory.toJson();
      data['is_deleted'] = false;
      await _databaseService.create(_collection, data);
    } catch (e) {
      debugPrint('Error creating inventory: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteInventory(String productId) async {
    try {
      await _databaseService.update(_collection, productId, {
        'is_deleted': true,
        'last_updated': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error deleting inventory: $e');
      rethrow;
    }
  }
}
