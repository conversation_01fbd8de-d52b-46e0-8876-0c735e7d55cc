import 'dart:io';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Class to generate a specialized Excel template for food products
class FoodBulkUploadTemplate {
  /// Generate and save a food product upload template
  static Future<String> generateTemplate() async {
    // Create a new Excel workbook
    final excel = Excel.createExcel();

    // Add a sheet for food products
    final Sheet sheet = excel['Food Products'];

    // Add headers
    final List<String> headers = [
      'name',
      'description',
      'price',
      'quantity',
      'categoryId', // Will be set to 'food' automatically
      'originalPrice',
      'brand',
      'unit',
      'weight',
      'imageUrls',
      'highlights', // Key features of the product
      'foodType', // Vegetarian or Non-vegetarian
      'mondayFrom',
      'mondayTo',
      'tuesdayFrom',
      'tuesdayTo',
      'wednesdayFrom',
      'wednesdayTo',
      'thursdayFrom',
      'thursdayTo',
      'fridayFrom',
      'fridayTo',
      'saturdayFrom',
      'saturdayTo',
      'sundayFrom',
      'sundayTo',
    ];

    // Add header row with cell styling
    for (var i = 0; i < headers.length; i++) {
      final cell =
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
      );
    }

    // Add sample data row for vegetarian food
    final List<String> vegSampleData = [
      'Vegetable Samosa',
      'Delicious potato filled samosa',
      '20.00',
      '50',
      'food', // Category ID (always 'food')
      '25.00',
      'Sharma Foods',
      'piece',
      '0.1',
      'https://example.com/samosa.jpg',
      'Fresh|Crispy|Homemade', // Highlights (pipe-separated)
      'veg', // Vegetarian
      '8:00 AM',
      '8:00 PM',
      '8:00 AM',
      '8:00 PM',
      '8:00 AM',
      '8:00 PM',
      '8:00 AM',
      '8:00 PM',
      '8:00 AM',
      '8:00 PM',
      '8:00 AM',
      '8:00 PM',
      '8:00 AM',
      '8:00 PM',
    ];

    for (var i = 0; i < vegSampleData.length; i++) {
      final cell =
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 1));
      cell.value = TextCellValue(vegSampleData[i]);

      // Add green background for the foodType cell if vegetarian
      if (i == 9) {
        // foodType column
        cell.cellStyle = CellStyle(
            // Light green background
            );
      }
    }

    // Add sample data row for non-vegetarian food
    final List<String> nonVegSampleData = [
      'Chicken Biryani',
      'Flavorful rice dish with chicken',
      '150.00',
      '20',
      'food', // Category ID (always 'food')
      '180.00',
      'Royal Kitchen',
      'plate',
      '0.5',
      'https://example.com/biryani.jpg',
      'Spicy|Aromatic|Authentic', // Highlights (pipe-separated)
      'non-veg', // Non-vegetarian
      '11:00 AM',
      '10:00 PM',
      '11:00 AM',
      '10:00 PM',
      '11:00 AM',
      '10:00 PM',
      '11:00 AM',
      '10:00 PM',
      '11:00 AM',
      '10:00 PM',
      '11:00 AM',
      '10:00 PM',
      '11:00 AM',
      '10:00 PM',
    ];

    for (var i = 0; i < nonVegSampleData.length; i++) {
      final cell =
          sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 2));
      cell.value = TextCellValue(nonVegSampleData[i]);

      // Add red background for the foodType cell if non-vegetarian
      if (i == 9) {
        // foodType column
        cell.cellStyle = CellStyle(
            // Light red background
            );
      }
    }

    // Add instructions sheet
    final Sheet instructionSheet = excel['Instructions'];

    final List<String> instructions = [
      'Instructions for Food Product Bulk Upload:',
      '',
      '1. Fill in the "Food Products" sheet with your food product details.',
      '2. Required fields: name, description, price, quantity',
      '3. The categoryId will automatically be set to "food"',
      '4. For highlights, provide key features separated by pipe symbol (|) e.g., "Fresh|Homemade|Spicy"',
      '5. For foodType, use "veg" for vegetarian or "non-veg" for non-vegetarian products',
      '6. For each day of the week, provide availability times in the format "HH:MM AM/PM"',
      '7. Leave the time fields empty if the food is not available on a particular day',
      '8. For image URLs, provide comma-separated URLs or upload images separately after importing the file',
      '9. Save the file and upload it back to the system',
      '',
      'Note: Do not modify the header row or sheet structure'
    ];

    for (var i = 0; i < instructions.length; i++) {
      final cell = instructionSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: i));
      cell.value = TextCellValue(instructions[i]);
      if (i == 0) {
        cell.cellStyle = CellStyle(bold: true, fontSize: 14);
      }
    }

    // Encode the Excel file
    final List<int>? bytes = excel.encode();
    if (bytes == null) {
      throw Exception('Failed to encode Excel file');
    }

    // Get temporary directory to save the file
    final directory = await getTemporaryDirectory();
    final filePath =
        path.join(directory.path, 'food_product_upload_template.xlsx');

    // Save the Excel file
    File(filePath)
      ..createSync(recursive: true)
      ..writeAsBytesSync(bytes);

    return filePath;
  }
}
