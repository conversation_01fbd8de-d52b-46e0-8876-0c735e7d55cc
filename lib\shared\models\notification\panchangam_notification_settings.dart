import 'package:freezed_annotation/freezed_annotation.dart';

part 'panchangam_notification_settings.freezed.dart';
part 'panchangam_notification_settings.g.dart';

@freezed
abstract class PanchangamNotificationSettings with _$PanchangamNotificationSettings {
  const factory PanchangamNotificationSettings({
    @Default(true) bool isEnabled,
    @Default(7) int notificationHour,
    @Default(0) int notificationMinute,
    @Default('en') String language,
    @Default('Mumbai') String location,
  }) = _PanchangamNotificationSettings;

  factory PanchangamNotificationSettings.fromJson(Map<String, dynamic> json) =>
      _$PanchangamNotificationSettingsFromJson(json);
}
