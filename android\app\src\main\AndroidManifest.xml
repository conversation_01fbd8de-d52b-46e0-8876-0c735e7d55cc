<?xml
    version="1.0"
    encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-sdk
        android:minSdkVersion="30"
        android:targetSdkVersion="36"/>
    <!-- Internet Permission -->
    <uses-permission
        android:name="android.permission.INTERNET"/>
    <!-- Network State Permission -->
    <uses-permission
        android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <!-- Location Permissions -->
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <!-- Camera Permission -->
    <uses-permission
        android:name="android.permission.CAMERA"/>
    <!-- Storage Permissions -->
    <!-- Legacy storage permissions (for Android < 13) -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"/>
    <!-- Granular media permissions (for Android 13+) -->
    <uses-permission
        android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission
        android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission
        android:name="android.permission.READ_MEDIA_AUDIO"/>
    <!-- Notification Permissions -->
    <uses-permission
        android:name="android.permission.POST_NOTIFICATIONS"/>
    <!-- Vibration Permission -->
    <uses-permission
        android:name="android.permission.VIBRATE"/>
    <!-- New permissions from the code block -->
    <uses-permission
        android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission
        android:name="android.permission.READ_CONTACTS"/>
    <uses-permission
        android:name="android.permission.WRITE_CONTACTS"/>
    <uses-permission
        android:name="android.permission.READ_CALENDAR"/>
    <uses-permission
        android:name="android.permission.WRITE_CALENDAR"/>
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission
        android:name="android.permission.READ_SMS"/>
    <uses-permission
        android:name="android.permission.RECEIVE_SMS"/>
    <uses-permission
        android:name="android.permission.SEND_SMS"/>
    <uses-permission
        android:name="android.permission.WAKE_LOCK"/>
    <uses-permission
        android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <!-- Bluetooth permissions - Legacy and Modern -->
    <!-- Legacy Bluetooth permissions (for Android < 12) -->
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30"/>
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30"/>
    <!-- Modern Bluetooth permissions (for Android 12+) -->
    <uses-permission
        android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s"/>
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADVERTISE"/>
    <!-- Needed for Bluetooth on some devices -->
    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="false"/>
    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="false"/>
    <uses-permission
        android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission
        android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission
        android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission
        android:name="android.permission.REQUEST_DELETE_PACKAGES"/>
    <uses-permission
        android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission
        android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission
        android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK"
        tools:targetApi="34"/>
    <uses-permission
        android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
    <uses-permission
        android:name="android.permission.SCHEDULE_EXACT_ALARM"/>
    <uses-permission
        android:name="android.permission.USE_EXACT_ALARM"
        tools:targetApi="31"/>
    <uses-permission
        android:name="android.permission.USE_FULL_SCREEN_INTENT"/>
    <!-- OpenGL ES features - marked as not required to avoid issues on devices with limited support -->
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="false"/>
    <uses-feature
        android:name="android.hardware.opengles.version"
        android:required="false"/>
    <application
        android:name=".MainApplication"
        android:label="@string/app_name"
        android:icon="${ic_launcher}"
        android:roundIcon="${ic_launcher_round}"
        android:allowBackup="true"
        android:fullBackupContent="@xml/backup_rules"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:usesCleartextTraffic="true"
        android:extractNativeLibs="true"
        android:hardwareAccelerated="true"
        android:enableOnBackInvokedCallback="true"
        android:attributionTags="VCN">
        <!-- Ensure proper back navigation -->
        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize"
            android:showWhenLocked="true"
            android:turnScreenOn="true"
            android:statusBarColor="@color/primary">
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"/>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <!-- Deep Links -->
            <intent-filter>
                <action
                    android:name="android.intent.action.VIEW"/>
                <category
                    android:name="android.intent.category.DEFAULT"/>
                <category
                    android:name="android.intent.category.BROWSABLE"/>
                <data
                    android:scheme="shivish"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2"/>
        <!-- Firebase Cloud Messaging -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="high_importance_channel"/>
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/notification_icon"/>
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification_icon_color"/>
        <!-- Google Maps API Key -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="${MAPS_API_KEY}"/>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths"/>
        </provider>
        <!-- Firebase services are handled by the Flutter Firebase Messaging plugin -->
        <!-- Razorpay Standard Checkout Activity -->
        <activity
            android:name="com.razorpay.CheckoutActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:theme="@style/CheckoutTheme"
            tools:replace="android:exported">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <data
                    android:host="rzp.io"
                    android:scheme="io.rzp"/>
            </intent-filter>
        </activity>
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action
                android:name="android.intent.action.PROCESS_TEXT"/>
            <data
                android:mimeType="text/plain"/>
        </intent>
    </queries>
    <!-- Required permissions for Vosk -->
    <uses-permission
        android:name="android.permission.RECORD_AUDIO"/>
</manifest>