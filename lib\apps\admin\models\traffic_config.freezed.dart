// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'traffic_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TrafficConfig {

 TrafficRoutingMode get routingMode; double get awsTrafficPercentage; DateTime get lastUpdated; bool get maintenanceMode; bool get autoFailoverEnabled; Map<String, dynamic> get customRules;
/// Create a copy of TrafficConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TrafficConfigCopyWith<TrafficConfig> get copyWith => _$TrafficConfigCopyWithImpl<TrafficConfig>(this as TrafficConfig, _$identity);

  /// Serializes this TrafficConfig to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TrafficConfig&&(identical(other.routingMode, routingMode) || other.routingMode == routingMode)&&(identical(other.awsTrafficPercentage, awsTrafficPercentage) || other.awsTrafficPercentage == awsTrafficPercentage)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&(identical(other.maintenanceMode, maintenanceMode) || other.maintenanceMode == maintenanceMode)&&(identical(other.autoFailoverEnabled, autoFailoverEnabled) || other.autoFailoverEnabled == autoFailoverEnabled)&&const DeepCollectionEquality().equals(other.customRules, customRules));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,routingMode,awsTrafficPercentage,lastUpdated,maintenanceMode,autoFailoverEnabled,const DeepCollectionEquality().hash(customRules));

@override
String toString() {
  return 'TrafficConfig(routingMode: $routingMode, awsTrafficPercentage: $awsTrafficPercentage, lastUpdated: $lastUpdated, maintenanceMode: $maintenanceMode, autoFailoverEnabled: $autoFailoverEnabled, customRules: $customRules)';
}


}

/// @nodoc
abstract mixin class $TrafficConfigCopyWith<$Res>  {
  factory $TrafficConfigCopyWith(TrafficConfig value, $Res Function(TrafficConfig) _then) = _$TrafficConfigCopyWithImpl;
@useResult
$Res call({
 TrafficRoutingMode routingMode, double awsTrafficPercentage, DateTime lastUpdated, bool maintenanceMode, bool autoFailoverEnabled, Map<String, dynamic> customRules
});




}
/// @nodoc
class _$TrafficConfigCopyWithImpl<$Res>
    implements $TrafficConfigCopyWith<$Res> {
  _$TrafficConfigCopyWithImpl(this._self, this._then);

  final TrafficConfig _self;
  final $Res Function(TrafficConfig) _then;

/// Create a copy of TrafficConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? routingMode = null,Object? awsTrafficPercentage = null,Object? lastUpdated = null,Object? maintenanceMode = null,Object? autoFailoverEnabled = null,Object? customRules = null,}) {
  return _then(_self.copyWith(
routingMode: null == routingMode ? _self.routingMode : routingMode // ignore: cast_nullable_to_non_nullable
as TrafficRoutingMode,awsTrafficPercentage: null == awsTrafficPercentage ? _self.awsTrafficPercentage : awsTrafficPercentage // ignore: cast_nullable_to_non_nullable
as double,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,maintenanceMode: null == maintenanceMode ? _self.maintenanceMode : maintenanceMode // ignore: cast_nullable_to_non_nullable
as bool,autoFailoverEnabled: null == autoFailoverEnabled ? _self.autoFailoverEnabled : autoFailoverEnabled // ignore: cast_nullable_to_non_nullable
as bool,customRules: null == customRules ? _self.customRules : customRules // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [TrafficConfig].
extension TrafficConfigPatterns on TrafficConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TrafficConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TrafficConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TrafficConfig value)  $default,){
final _that = this;
switch (_that) {
case _TrafficConfig():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TrafficConfig value)?  $default,){
final _that = this;
switch (_that) {
case _TrafficConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( TrafficRoutingMode routingMode,  double awsTrafficPercentage,  DateTime lastUpdated,  bool maintenanceMode,  bool autoFailoverEnabled,  Map<String, dynamic> customRules)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TrafficConfig() when $default != null:
return $default(_that.routingMode,_that.awsTrafficPercentage,_that.lastUpdated,_that.maintenanceMode,_that.autoFailoverEnabled,_that.customRules);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( TrafficRoutingMode routingMode,  double awsTrafficPercentage,  DateTime lastUpdated,  bool maintenanceMode,  bool autoFailoverEnabled,  Map<String, dynamic> customRules)  $default,) {final _that = this;
switch (_that) {
case _TrafficConfig():
return $default(_that.routingMode,_that.awsTrafficPercentage,_that.lastUpdated,_that.maintenanceMode,_that.autoFailoverEnabled,_that.customRules);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( TrafficRoutingMode routingMode,  double awsTrafficPercentage,  DateTime lastUpdated,  bool maintenanceMode,  bool autoFailoverEnabled,  Map<String, dynamic> customRules)?  $default,) {final _that = this;
switch (_that) {
case _TrafficConfig() when $default != null:
return $default(_that.routingMode,_that.awsTrafficPercentage,_that.lastUpdated,_that.maintenanceMode,_that.autoFailoverEnabled,_that.customRules);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TrafficConfig implements TrafficConfig {
  const _TrafficConfig({required this.routingMode, required this.awsTrafficPercentage, required this.lastUpdated, this.maintenanceMode = false, this.autoFailoverEnabled = true, final  Map<String, dynamic> customRules = const {}}): _customRules = customRules;
  factory _TrafficConfig.fromJson(Map<String, dynamic> json) => _$TrafficConfigFromJson(json);

@override final  TrafficRoutingMode routingMode;
@override final  double awsTrafficPercentage;
@override final  DateTime lastUpdated;
@override@JsonKey() final  bool maintenanceMode;
@override@JsonKey() final  bool autoFailoverEnabled;
 final  Map<String, dynamic> _customRules;
@override@JsonKey() Map<String, dynamic> get customRules {
  if (_customRules is EqualUnmodifiableMapView) return _customRules;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_customRules);
}


/// Create a copy of TrafficConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TrafficConfigCopyWith<_TrafficConfig> get copyWith => __$TrafficConfigCopyWithImpl<_TrafficConfig>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TrafficConfigToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TrafficConfig&&(identical(other.routingMode, routingMode) || other.routingMode == routingMode)&&(identical(other.awsTrafficPercentage, awsTrafficPercentage) || other.awsTrafficPercentage == awsTrafficPercentage)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&(identical(other.maintenanceMode, maintenanceMode) || other.maintenanceMode == maintenanceMode)&&(identical(other.autoFailoverEnabled, autoFailoverEnabled) || other.autoFailoverEnabled == autoFailoverEnabled)&&const DeepCollectionEquality().equals(other._customRules, _customRules));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,routingMode,awsTrafficPercentage,lastUpdated,maintenanceMode,autoFailoverEnabled,const DeepCollectionEquality().hash(_customRules));

@override
String toString() {
  return 'TrafficConfig(routingMode: $routingMode, awsTrafficPercentage: $awsTrafficPercentage, lastUpdated: $lastUpdated, maintenanceMode: $maintenanceMode, autoFailoverEnabled: $autoFailoverEnabled, customRules: $customRules)';
}


}

/// @nodoc
abstract mixin class _$TrafficConfigCopyWith<$Res> implements $TrafficConfigCopyWith<$Res> {
  factory _$TrafficConfigCopyWith(_TrafficConfig value, $Res Function(_TrafficConfig) _then) = __$TrafficConfigCopyWithImpl;
@override @useResult
$Res call({
 TrafficRoutingMode routingMode, double awsTrafficPercentage, DateTime lastUpdated, bool maintenanceMode, bool autoFailoverEnabled, Map<String, dynamic> customRules
});




}
/// @nodoc
class __$TrafficConfigCopyWithImpl<$Res>
    implements _$TrafficConfigCopyWith<$Res> {
  __$TrafficConfigCopyWithImpl(this._self, this._then);

  final _TrafficConfig _self;
  final $Res Function(_TrafficConfig) _then;

/// Create a copy of TrafficConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? routingMode = null,Object? awsTrafficPercentage = null,Object? lastUpdated = null,Object? maintenanceMode = null,Object? autoFailoverEnabled = null,Object? customRules = null,}) {
  return _then(_TrafficConfig(
routingMode: null == routingMode ? _self.routingMode : routingMode // ignore: cast_nullable_to_non_nullable
as TrafficRoutingMode,awsTrafficPercentage: null == awsTrafficPercentage ? _self.awsTrafficPercentage : awsTrafficPercentage // ignore: cast_nullable_to_non_nullable
as double,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,maintenanceMode: null == maintenanceMode ? _self.maintenanceMode : maintenanceMode // ignore: cast_nullable_to_non_nullable
as bool,autoFailoverEnabled: null == autoFailoverEnabled ? _self.autoFailoverEnabled : autoFailoverEnabled // ignore: cast_nullable_to_non_nullable
as bool,customRules: null == customRules ? _self._customRules : customRules // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}


/// @nodoc
mixin _$ServerStatus {

 ServerHealth get awsStatus; ServerHealth get datacenterStatus; List<ServerInfo> get awsServers; List<ServerInfo> get datacenterServers; DateTime get lastChecked; int get totalRequests; int get awsRequests; int get datacenterRequests;
/// Create a copy of ServerStatus
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ServerStatusCopyWith<ServerStatus> get copyWith => _$ServerStatusCopyWithImpl<ServerStatus>(this as ServerStatus, _$identity);

  /// Serializes this ServerStatus to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ServerStatus&&(identical(other.awsStatus, awsStatus) || other.awsStatus == awsStatus)&&(identical(other.datacenterStatus, datacenterStatus) || other.datacenterStatus == datacenterStatus)&&const DeepCollectionEquality().equals(other.awsServers, awsServers)&&const DeepCollectionEquality().equals(other.datacenterServers, datacenterServers)&&(identical(other.lastChecked, lastChecked) || other.lastChecked == lastChecked)&&(identical(other.totalRequests, totalRequests) || other.totalRequests == totalRequests)&&(identical(other.awsRequests, awsRequests) || other.awsRequests == awsRequests)&&(identical(other.datacenterRequests, datacenterRequests) || other.datacenterRequests == datacenterRequests));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,awsStatus,datacenterStatus,const DeepCollectionEquality().hash(awsServers),const DeepCollectionEquality().hash(datacenterServers),lastChecked,totalRequests,awsRequests,datacenterRequests);

@override
String toString() {
  return 'ServerStatus(awsStatus: $awsStatus, datacenterStatus: $datacenterStatus, awsServers: $awsServers, datacenterServers: $datacenterServers, lastChecked: $lastChecked, totalRequests: $totalRequests, awsRequests: $awsRequests, datacenterRequests: $datacenterRequests)';
}


}

/// @nodoc
abstract mixin class $ServerStatusCopyWith<$Res>  {
  factory $ServerStatusCopyWith(ServerStatus value, $Res Function(ServerStatus) _then) = _$ServerStatusCopyWithImpl;
@useResult
$Res call({
 ServerHealth awsStatus, ServerHealth datacenterStatus, List<ServerInfo> awsServers, List<ServerInfo> datacenterServers, DateTime lastChecked, int totalRequests, int awsRequests, int datacenterRequests
});




}
/// @nodoc
class _$ServerStatusCopyWithImpl<$Res>
    implements $ServerStatusCopyWith<$Res> {
  _$ServerStatusCopyWithImpl(this._self, this._then);

  final ServerStatus _self;
  final $Res Function(ServerStatus) _then;

/// Create a copy of ServerStatus
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? awsStatus = null,Object? datacenterStatus = null,Object? awsServers = null,Object? datacenterServers = null,Object? lastChecked = null,Object? totalRequests = null,Object? awsRequests = null,Object? datacenterRequests = null,}) {
  return _then(_self.copyWith(
awsStatus: null == awsStatus ? _self.awsStatus : awsStatus // ignore: cast_nullable_to_non_nullable
as ServerHealth,datacenterStatus: null == datacenterStatus ? _self.datacenterStatus : datacenterStatus // ignore: cast_nullable_to_non_nullable
as ServerHealth,awsServers: null == awsServers ? _self.awsServers : awsServers // ignore: cast_nullable_to_non_nullable
as List<ServerInfo>,datacenterServers: null == datacenterServers ? _self.datacenterServers : datacenterServers // ignore: cast_nullable_to_non_nullable
as List<ServerInfo>,lastChecked: null == lastChecked ? _self.lastChecked : lastChecked // ignore: cast_nullable_to_non_nullable
as DateTime,totalRequests: null == totalRequests ? _self.totalRequests : totalRequests // ignore: cast_nullable_to_non_nullable
as int,awsRequests: null == awsRequests ? _self.awsRequests : awsRequests // ignore: cast_nullable_to_non_nullable
as int,datacenterRequests: null == datacenterRequests ? _self.datacenterRequests : datacenterRequests // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [ServerStatus].
extension ServerStatusPatterns on ServerStatus {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ServerStatus value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ServerStatus() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ServerStatus value)  $default,){
final _that = this;
switch (_that) {
case _ServerStatus():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ServerStatus value)?  $default,){
final _that = this;
switch (_that) {
case _ServerStatus() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( ServerHealth awsStatus,  ServerHealth datacenterStatus,  List<ServerInfo> awsServers,  List<ServerInfo> datacenterServers,  DateTime lastChecked,  int totalRequests,  int awsRequests,  int datacenterRequests)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ServerStatus() when $default != null:
return $default(_that.awsStatus,_that.datacenterStatus,_that.awsServers,_that.datacenterServers,_that.lastChecked,_that.totalRequests,_that.awsRequests,_that.datacenterRequests);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( ServerHealth awsStatus,  ServerHealth datacenterStatus,  List<ServerInfo> awsServers,  List<ServerInfo> datacenterServers,  DateTime lastChecked,  int totalRequests,  int awsRequests,  int datacenterRequests)  $default,) {final _that = this;
switch (_that) {
case _ServerStatus():
return $default(_that.awsStatus,_that.datacenterStatus,_that.awsServers,_that.datacenterServers,_that.lastChecked,_that.totalRequests,_that.awsRequests,_that.datacenterRequests);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( ServerHealth awsStatus,  ServerHealth datacenterStatus,  List<ServerInfo> awsServers,  List<ServerInfo> datacenterServers,  DateTime lastChecked,  int totalRequests,  int awsRequests,  int datacenterRequests)?  $default,) {final _that = this;
switch (_that) {
case _ServerStatus() when $default != null:
return $default(_that.awsStatus,_that.datacenterStatus,_that.awsServers,_that.datacenterServers,_that.lastChecked,_that.totalRequests,_that.awsRequests,_that.datacenterRequests);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ServerStatus implements ServerStatus {
  const _ServerStatus({required this.awsStatus, required this.datacenterStatus, required final  List<ServerInfo> awsServers, required final  List<ServerInfo> datacenterServers, required this.lastChecked, this.totalRequests = 0, this.awsRequests = 0, this.datacenterRequests = 0}): _awsServers = awsServers,_datacenterServers = datacenterServers;
  factory _ServerStatus.fromJson(Map<String, dynamic> json) => _$ServerStatusFromJson(json);

@override final  ServerHealth awsStatus;
@override final  ServerHealth datacenterStatus;
 final  List<ServerInfo> _awsServers;
@override List<ServerInfo> get awsServers {
  if (_awsServers is EqualUnmodifiableListView) return _awsServers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_awsServers);
}

 final  List<ServerInfo> _datacenterServers;
@override List<ServerInfo> get datacenterServers {
  if (_datacenterServers is EqualUnmodifiableListView) return _datacenterServers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_datacenterServers);
}

@override final  DateTime lastChecked;
@override@JsonKey() final  int totalRequests;
@override@JsonKey() final  int awsRequests;
@override@JsonKey() final  int datacenterRequests;

/// Create a copy of ServerStatus
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ServerStatusCopyWith<_ServerStatus> get copyWith => __$ServerStatusCopyWithImpl<_ServerStatus>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ServerStatusToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ServerStatus&&(identical(other.awsStatus, awsStatus) || other.awsStatus == awsStatus)&&(identical(other.datacenterStatus, datacenterStatus) || other.datacenterStatus == datacenterStatus)&&const DeepCollectionEquality().equals(other._awsServers, _awsServers)&&const DeepCollectionEquality().equals(other._datacenterServers, _datacenterServers)&&(identical(other.lastChecked, lastChecked) || other.lastChecked == lastChecked)&&(identical(other.totalRequests, totalRequests) || other.totalRequests == totalRequests)&&(identical(other.awsRequests, awsRequests) || other.awsRequests == awsRequests)&&(identical(other.datacenterRequests, datacenterRequests) || other.datacenterRequests == datacenterRequests));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,awsStatus,datacenterStatus,const DeepCollectionEquality().hash(_awsServers),const DeepCollectionEquality().hash(_datacenterServers),lastChecked,totalRequests,awsRequests,datacenterRequests);

@override
String toString() {
  return 'ServerStatus(awsStatus: $awsStatus, datacenterStatus: $datacenterStatus, awsServers: $awsServers, datacenterServers: $datacenterServers, lastChecked: $lastChecked, totalRequests: $totalRequests, awsRequests: $awsRequests, datacenterRequests: $datacenterRequests)';
}


}

/// @nodoc
abstract mixin class _$ServerStatusCopyWith<$Res> implements $ServerStatusCopyWith<$Res> {
  factory _$ServerStatusCopyWith(_ServerStatus value, $Res Function(_ServerStatus) _then) = __$ServerStatusCopyWithImpl;
@override @useResult
$Res call({
 ServerHealth awsStatus, ServerHealth datacenterStatus, List<ServerInfo> awsServers, List<ServerInfo> datacenterServers, DateTime lastChecked, int totalRequests, int awsRequests, int datacenterRequests
});




}
/// @nodoc
class __$ServerStatusCopyWithImpl<$Res>
    implements _$ServerStatusCopyWith<$Res> {
  __$ServerStatusCopyWithImpl(this._self, this._then);

  final _ServerStatus _self;
  final $Res Function(_ServerStatus) _then;

/// Create a copy of ServerStatus
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? awsStatus = null,Object? datacenterStatus = null,Object? awsServers = null,Object? datacenterServers = null,Object? lastChecked = null,Object? totalRequests = null,Object? awsRequests = null,Object? datacenterRequests = null,}) {
  return _then(_ServerStatus(
awsStatus: null == awsStatus ? _self.awsStatus : awsStatus // ignore: cast_nullable_to_non_nullable
as ServerHealth,datacenterStatus: null == datacenterStatus ? _self.datacenterStatus : datacenterStatus // ignore: cast_nullable_to_non_nullable
as ServerHealth,awsServers: null == awsServers ? _self._awsServers : awsServers // ignore: cast_nullable_to_non_nullable
as List<ServerInfo>,datacenterServers: null == datacenterServers ? _self._datacenterServers : datacenterServers // ignore: cast_nullable_to_non_nullable
as List<ServerInfo>,lastChecked: null == lastChecked ? _self.lastChecked : lastChecked // ignore: cast_nullable_to_non_nullable
as DateTime,totalRequests: null == totalRequests ? _self.totalRequests : totalRequests // ignore: cast_nullable_to_non_nullable
as int,awsRequests: null == awsRequests ? _self.awsRequests : awsRequests // ignore: cast_nullable_to_non_nullable
as int,datacenterRequests: null == datacenterRequests ? _self.datacenterRequests : datacenterRequests // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$ServerInfo {

 String get id; String get name; String get ipAddress; ServerHealth get health; double get cpuUsage; double get memoryUsage; double get diskUsage; int get responseTime; int get activeConnections; DateTime get lastHealthCheck; String get region; String get serverType;
/// Create a copy of ServerInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ServerInfoCopyWith<ServerInfo> get copyWith => _$ServerInfoCopyWithImpl<ServerInfo>(this as ServerInfo, _$identity);

  /// Serializes this ServerInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ServerInfo&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.ipAddress, ipAddress) || other.ipAddress == ipAddress)&&(identical(other.health, health) || other.health == health)&&(identical(other.cpuUsage, cpuUsage) || other.cpuUsage == cpuUsage)&&(identical(other.memoryUsage, memoryUsage) || other.memoryUsage == memoryUsage)&&(identical(other.diskUsage, diskUsage) || other.diskUsage == diskUsage)&&(identical(other.responseTime, responseTime) || other.responseTime == responseTime)&&(identical(other.activeConnections, activeConnections) || other.activeConnections == activeConnections)&&(identical(other.lastHealthCheck, lastHealthCheck) || other.lastHealthCheck == lastHealthCheck)&&(identical(other.region, region) || other.region == region)&&(identical(other.serverType, serverType) || other.serverType == serverType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,ipAddress,health,cpuUsage,memoryUsage,diskUsage,responseTime,activeConnections,lastHealthCheck,region,serverType);

@override
String toString() {
  return 'ServerInfo(id: $id, name: $name, ipAddress: $ipAddress, health: $health, cpuUsage: $cpuUsage, memoryUsage: $memoryUsage, diskUsage: $diskUsage, responseTime: $responseTime, activeConnections: $activeConnections, lastHealthCheck: $lastHealthCheck, region: $region, serverType: $serverType)';
}


}

/// @nodoc
abstract mixin class $ServerInfoCopyWith<$Res>  {
  factory $ServerInfoCopyWith(ServerInfo value, $Res Function(ServerInfo) _then) = _$ServerInfoCopyWithImpl;
@useResult
$Res call({
 String id, String name, String ipAddress, ServerHealth health, double cpuUsage, double memoryUsage, double diskUsage, int responseTime, int activeConnections, DateTime lastHealthCheck, String region, String serverType
});




}
/// @nodoc
class _$ServerInfoCopyWithImpl<$Res>
    implements $ServerInfoCopyWith<$Res> {
  _$ServerInfoCopyWithImpl(this._self, this._then);

  final ServerInfo _self;
  final $Res Function(ServerInfo) _then;

/// Create a copy of ServerInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? ipAddress = null,Object? health = null,Object? cpuUsage = null,Object? memoryUsage = null,Object? diskUsage = null,Object? responseTime = null,Object? activeConnections = null,Object? lastHealthCheck = null,Object? region = null,Object? serverType = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,ipAddress: null == ipAddress ? _self.ipAddress : ipAddress // ignore: cast_nullable_to_non_nullable
as String,health: null == health ? _self.health : health // ignore: cast_nullable_to_non_nullable
as ServerHealth,cpuUsage: null == cpuUsage ? _self.cpuUsage : cpuUsage // ignore: cast_nullable_to_non_nullable
as double,memoryUsage: null == memoryUsage ? _self.memoryUsage : memoryUsage // ignore: cast_nullable_to_non_nullable
as double,diskUsage: null == diskUsage ? _self.diskUsage : diskUsage // ignore: cast_nullable_to_non_nullable
as double,responseTime: null == responseTime ? _self.responseTime : responseTime // ignore: cast_nullable_to_non_nullable
as int,activeConnections: null == activeConnections ? _self.activeConnections : activeConnections // ignore: cast_nullable_to_non_nullable
as int,lastHealthCheck: null == lastHealthCheck ? _self.lastHealthCheck : lastHealthCheck // ignore: cast_nullable_to_non_nullable
as DateTime,region: null == region ? _self.region : region // ignore: cast_nullable_to_non_nullable
as String,serverType: null == serverType ? _self.serverType : serverType // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [ServerInfo].
extension ServerInfoPatterns on ServerInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ServerInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ServerInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ServerInfo value)  $default,){
final _that = this;
switch (_that) {
case _ServerInfo():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ServerInfo value)?  $default,){
final _that = this;
switch (_that) {
case _ServerInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String ipAddress,  ServerHealth health,  double cpuUsage,  double memoryUsage,  double diskUsage,  int responseTime,  int activeConnections,  DateTime lastHealthCheck,  String region,  String serverType)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ServerInfo() when $default != null:
return $default(_that.id,_that.name,_that.ipAddress,_that.health,_that.cpuUsage,_that.memoryUsage,_that.diskUsage,_that.responseTime,_that.activeConnections,_that.lastHealthCheck,_that.region,_that.serverType);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String ipAddress,  ServerHealth health,  double cpuUsage,  double memoryUsage,  double diskUsage,  int responseTime,  int activeConnections,  DateTime lastHealthCheck,  String region,  String serverType)  $default,) {final _that = this;
switch (_that) {
case _ServerInfo():
return $default(_that.id,_that.name,_that.ipAddress,_that.health,_that.cpuUsage,_that.memoryUsage,_that.diskUsage,_that.responseTime,_that.activeConnections,_that.lastHealthCheck,_that.region,_that.serverType);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String ipAddress,  ServerHealth health,  double cpuUsage,  double memoryUsage,  double diskUsage,  int responseTime,  int activeConnections,  DateTime lastHealthCheck,  String region,  String serverType)?  $default,) {final _that = this;
switch (_that) {
case _ServerInfo() when $default != null:
return $default(_that.id,_that.name,_that.ipAddress,_that.health,_that.cpuUsage,_that.memoryUsage,_that.diskUsage,_that.responseTime,_that.activeConnections,_that.lastHealthCheck,_that.region,_that.serverType);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ServerInfo implements ServerInfo {
  const _ServerInfo({required this.id, required this.name, required this.ipAddress, required this.health, required this.cpuUsage, required this.memoryUsage, required this.diskUsage, required this.responseTime, required this.activeConnections, required this.lastHealthCheck, this.region = 'Unknown', this.serverType = 'Unknown'});
  factory _ServerInfo.fromJson(Map<String, dynamic> json) => _$ServerInfoFromJson(json);

@override final  String id;
@override final  String name;
@override final  String ipAddress;
@override final  ServerHealth health;
@override final  double cpuUsage;
@override final  double memoryUsage;
@override final  double diskUsage;
@override final  int responseTime;
@override final  int activeConnections;
@override final  DateTime lastHealthCheck;
@override@JsonKey() final  String region;
@override@JsonKey() final  String serverType;

/// Create a copy of ServerInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ServerInfoCopyWith<_ServerInfo> get copyWith => __$ServerInfoCopyWithImpl<_ServerInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ServerInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ServerInfo&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.ipAddress, ipAddress) || other.ipAddress == ipAddress)&&(identical(other.health, health) || other.health == health)&&(identical(other.cpuUsage, cpuUsage) || other.cpuUsage == cpuUsage)&&(identical(other.memoryUsage, memoryUsage) || other.memoryUsage == memoryUsage)&&(identical(other.diskUsage, diskUsage) || other.diskUsage == diskUsage)&&(identical(other.responseTime, responseTime) || other.responseTime == responseTime)&&(identical(other.activeConnections, activeConnections) || other.activeConnections == activeConnections)&&(identical(other.lastHealthCheck, lastHealthCheck) || other.lastHealthCheck == lastHealthCheck)&&(identical(other.region, region) || other.region == region)&&(identical(other.serverType, serverType) || other.serverType == serverType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,ipAddress,health,cpuUsage,memoryUsage,diskUsage,responseTime,activeConnections,lastHealthCheck,region,serverType);

@override
String toString() {
  return 'ServerInfo(id: $id, name: $name, ipAddress: $ipAddress, health: $health, cpuUsage: $cpuUsage, memoryUsage: $memoryUsage, diskUsage: $diskUsage, responseTime: $responseTime, activeConnections: $activeConnections, lastHealthCheck: $lastHealthCheck, region: $region, serverType: $serverType)';
}


}

/// @nodoc
abstract mixin class _$ServerInfoCopyWith<$Res> implements $ServerInfoCopyWith<$Res> {
  factory _$ServerInfoCopyWith(_ServerInfo value, $Res Function(_ServerInfo) _then) = __$ServerInfoCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String ipAddress, ServerHealth health, double cpuUsage, double memoryUsage, double diskUsage, int responseTime, int activeConnections, DateTime lastHealthCheck, String region, String serverType
});




}
/// @nodoc
class __$ServerInfoCopyWithImpl<$Res>
    implements _$ServerInfoCopyWith<$Res> {
  __$ServerInfoCopyWithImpl(this._self, this._then);

  final _ServerInfo _self;
  final $Res Function(_ServerInfo) _then;

/// Create a copy of ServerInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? ipAddress = null,Object? health = null,Object? cpuUsage = null,Object? memoryUsage = null,Object? diskUsage = null,Object? responseTime = null,Object? activeConnections = null,Object? lastHealthCheck = null,Object? region = null,Object? serverType = null,}) {
  return _then(_ServerInfo(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,ipAddress: null == ipAddress ? _self.ipAddress : ipAddress // ignore: cast_nullable_to_non_nullable
as String,health: null == health ? _self.health : health // ignore: cast_nullable_to_non_nullable
as ServerHealth,cpuUsage: null == cpuUsage ? _self.cpuUsage : cpuUsage // ignore: cast_nullable_to_non_nullable
as double,memoryUsage: null == memoryUsage ? _self.memoryUsage : memoryUsage // ignore: cast_nullable_to_non_nullable
as double,diskUsage: null == diskUsage ? _self.diskUsage : diskUsage // ignore: cast_nullable_to_non_nullable
as double,responseTime: null == responseTime ? _self.responseTime : responseTime // ignore: cast_nullable_to_non_nullable
as int,activeConnections: null == activeConnections ? _self.activeConnections : activeConnections // ignore: cast_nullable_to_non_nullable
as int,lastHealthCheck: null == lastHealthCheck ? _self.lastHealthCheck : lastHealthCheck // ignore: cast_nullable_to_non_nullable
as DateTime,region: null == region ? _self.region : region // ignore: cast_nullable_to_non_nullable
as String,serverType: null == serverType ? _self.serverType : serverType // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$RoutingRule {

 String get id; String get name; RoutingCondition get condition; RoutingAction get action; int get priority; bool get isActive; DateTime get createdAt; DateTime? get updatedAt;
/// Create a copy of RoutingRule
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RoutingRuleCopyWith<RoutingRule> get copyWith => _$RoutingRuleCopyWithImpl<RoutingRule>(this as RoutingRule, _$identity);

  /// Serializes this RoutingRule to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RoutingRule&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.action, action) || other.action == action)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,condition,action,priority,isActive,createdAt,updatedAt);

@override
String toString() {
  return 'RoutingRule(id: $id, name: $name, condition: $condition, action: $action, priority: $priority, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $RoutingRuleCopyWith<$Res>  {
  factory $RoutingRuleCopyWith(RoutingRule value, $Res Function(RoutingRule) _then) = _$RoutingRuleCopyWithImpl;
@useResult
$Res call({
 String id, String name, RoutingCondition condition, RoutingAction action, int priority, bool isActive, DateTime createdAt, DateTime? updatedAt
});




}
/// @nodoc
class _$RoutingRuleCopyWithImpl<$Res>
    implements $RoutingRuleCopyWith<$Res> {
  _$RoutingRuleCopyWithImpl(this._self, this._then);

  final RoutingRule _self;
  final $Res Function(RoutingRule) _then;

/// Create a copy of RoutingRule
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? condition = null,Object? action = null,Object? priority = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as RoutingCondition,action: null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as RoutingAction,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as int,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [RoutingRule].
extension RoutingRulePatterns on RoutingRule {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RoutingRule value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RoutingRule() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RoutingRule value)  $default,){
final _that = this;
switch (_that) {
case _RoutingRule():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RoutingRule value)?  $default,){
final _that = this;
switch (_that) {
case _RoutingRule() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  RoutingCondition condition,  RoutingAction action,  int priority,  bool isActive,  DateTime createdAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RoutingRule() when $default != null:
return $default(_that.id,_that.name,_that.condition,_that.action,_that.priority,_that.isActive,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  RoutingCondition condition,  RoutingAction action,  int priority,  bool isActive,  DateTime createdAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _RoutingRule():
return $default(_that.id,_that.name,_that.condition,_that.action,_that.priority,_that.isActive,_that.createdAt,_that.updatedAt);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  RoutingCondition condition,  RoutingAction action,  int priority,  bool isActive,  DateTime createdAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _RoutingRule() when $default != null:
return $default(_that.id,_that.name,_that.condition,_that.action,_that.priority,_that.isActive,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RoutingRule implements RoutingRule {
  const _RoutingRule({required this.id, required this.name, required this.condition, required this.action, required this.priority, required this.isActive, required this.createdAt, this.updatedAt});
  factory _RoutingRule.fromJson(Map<String, dynamic> json) => _$RoutingRuleFromJson(json);

@override final  String id;
@override final  String name;
@override final  RoutingCondition condition;
@override final  RoutingAction action;
@override final  int priority;
@override final  bool isActive;
@override final  DateTime createdAt;
@override final  DateTime? updatedAt;

/// Create a copy of RoutingRule
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RoutingRuleCopyWith<_RoutingRule> get copyWith => __$RoutingRuleCopyWithImpl<_RoutingRule>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RoutingRuleToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RoutingRule&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.action, action) || other.action == action)&&(identical(other.priority, priority) || other.priority == priority)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,condition,action,priority,isActive,createdAt,updatedAt);

@override
String toString() {
  return 'RoutingRule(id: $id, name: $name, condition: $condition, action: $action, priority: $priority, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$RoutingRuleCopyWith<$Res> implements $RoutingRuleCopyWith<$Res> {
  factory _$RoutingRuleCopyWith(_RoutingRule value, $Res Function(_RoutingRule) _then) = __$RoutingRuleCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, RoutingCondition condition, RoutingAction action, int priority, bool isActive, DateTime createdAt, DateTime? updatedAt
});




}
/// @nodoc
class __$RoutingRuleCopyWithImpl<$Res>
    implements _$RoutingRuleCopyWith<$Res> {
  __$RoutingRuleCopyWithImpl(this._self, this._then);

  final _RoutingRule _self;
  final $Res Function(_RoutingRule) _then;

/// Create a copy of RoutingRule
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? condition = null,Object? action = null,Object? priority = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = freezed,}) {
  return _then(_RoutingRule(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as RoutingCondition,action: null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as RoutingAction,priority: null == priority ? _self.priority : priority // ignore: cast_nullable_to_non_nullable
as int,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$TrafficAnalytics {

 DateTime get timestamp; int get totalRequests; int get awsRequests; int get datacenterRequests; double get averageResponseTime; double get errorRate; Map<String, int> get requestsByCountry; Map<String, int> get requestsByEndpoint; int get blockedRequests; int get rateLimitedRequests;
/// Create a copy of TrafficAnalytics
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TrafficAnalyticsCopyWith<TrafficAnalytics> get copyWith => _$TrafficAnalyticsCopyWithImpl<TrafficAnalytics>(this as TrafficAnalytics, _$identity);

  /// Serializes this TrafficAnalytics to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TrafficAnalytics&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.totalRequests, totalRequests) || other.totalRequests == totalRequests)&&(identical(other.awsRequests, awsRequests) || other.awsRequests == awsRequests)&&(identical(other.datacenterRequests, datacenterRequests) || other.datacenterRequests == datacenterRequests)&&(identical(other.averageResponseTime, averageResponseTime) || other.averageResponseTime == averageResponseTime)&&(identical(other.errorRate, errorRate) || other.errorRate == errorRate)&&const DeepCollectionEquality().equals(other.requestsByCountry, requestsByCountry)&&const DeepCollectionEquality().equals(other.requestsByEndpoint, requestsByEndpoint)&&(identical(other.blockedRequests, blockedRequests) || other.blockedRequests == blockedRequests)&&(identical(other.rateLimitedRequests, rateLimitedRequests) || other.rateLimitedRequests == rateLimitedRequests));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,timestamp,totalRequests,awsRequests,datacenterRequests,averageResponseTime,errorRate,const DeepCollectionEquality().hash(requestsByCountry),const DeepCollectionEquality().hash(requestsByEndpoint),blockedRequests,rateLimitedRequests);

@override
String toString() {
  return 'TrafficAnalytics(timestamp: $timestamp, totalRequests: $totalRequests, awsRequests: $awsRequests, datacenterRequests: $datacenterRequests, averageResponseTime: $averageResponseTime, errorRate: $errorRate, requestsByCountry: $requestsByCountry, requestsByEndpoint: $requestsByEndpoint, blockedRequests: $blockedRequests, rateLimitedRequests: $rateLimitedRequests)';
}


}

/// @nodoc
abstract mixin class $TrafficAnalyticsCopyWith<$Res>  {
  factory $TrafficAnalyticsCopyWith(TrafficAnalytics value, $Res Function(TrafficAnalytics) _then) = _$TrafficAnalyticsCopyWithImpl;
@useResult
$Res call({
 DateTime timestamp, int totalRequests, int awsRequests, int datacenterRequests, double averageResponseTime, double errorRate, Map<String, int> requestsByCountry, Map<String, int> requestsByEndpoint, int blockedRequests, int rateLimitedRequests
});




}
/// @nodoc
class _$TrafficAnalyticsCopyWithImpl<$Res>
    implements $TrafficAnalyticsCopyWith<$Res> {
  _$TrafficAnalyticsCopyWithImpl(this._self, this._then);

  final TrafficAnalytics _self;
  final $Res Function(TrafficAnalytics) _then;

/// Create a copy of TrafficAnalytics
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? timestamp = null,Object? totalRequests = null,Object? awsRequests = null,Object? datacenterRequests = null,Object? averageResponseTime = null,Object? errorRate = null,Object? requestsByCountry = null,Object? requestsByEndpoint = null,Object? blockedRequests = null,Object? rateLimitedRequests = null,}) {
  return _then(_self.copyWith(
timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,totalRequests: null == totalRequests ? _self.totalRequests : totalRequests // ignore: cast_nullable_to_non_nullable
as int,awsRequests: null == awsRequests ? _self.awsRequests : awsRequests // ignore: cast_nullable_to_non_nullable
as int,datacenterRequests: null == datacenterRequests ? _self.datacenterRequests : datacenterRequests // ignore: cast_nullable_to_non_nullable
as int,averageResponseTime: null == averageResponseTime ? _self.averageResponseTime : averageResponseTime // ignore: cast_nullable_to_non_nullable
as double,errorRate: null == errorRate ? _self.errorRate : errorRate // ignore: cast_nullable_to_non_nullable
as double,requestsByCountry: null == requestsByCountry ? _self.requestsByCountry : requestsByCountry // ignore: cast_nullable_to_non_nullable
as Map<String, int>,requestsByEndpoint: null == requestsByEndpoint ? _self.requestsByEndpoint : requestsByEndpoint // ignore: cast_nullable_to_non_nullable
as Map<String, int>,blockedRequests: null == blockedRequests ? _self.blockedRequests : blockedRequests // ignore: cast_nullable_to_non_nullable
as int,rateLimitedRequests: null == rateLimitedRequests ? _self.rateLimitedRequests : rateLimitedRequests // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [TrafficAnalytics].
extension TrafficAnalyticsPatterns on TrafficAnalytics {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TrafficAnalytics value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TrafficAnalytics() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TrafficAnalytics value)  $default,){
final _that = this;
switch (_that) {
case _TrafficAnalytics():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TrafficAnalytics value)?  $default,){
final _that = this;
switch (_that) {
case _TrafficAnalytics() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( DateTime timestamp,  int totalRequests,  int awsRequests,  int datacenterRequests,  double averageResponseTime,  double errorRate,  Map<String, int> requestsByCountry,  Map<String, int> requestsByEndpoint,  int blockedRequests,  int rateLimitedRequests)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TrafficAnalytics() when $default != null:
return $default(_that.timestamp,_that.totalRequests,_that.awsRequests,_that.datacenterRequests,_that.averageResponseTime,_that.errorRate,_that.requestsByCountry,_that.requestsByEndpoint,_that.blockedRequests,_that.rateLimitedRequests);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( DateTime timestamp,  int totalRequests,  int awsRequests,  int datacenterRequests,  double averageResponseTime,  double errorRate,  Map<String, int> requestsByCountry,  Map<String, int> requestsByEndpoint,  int blockedRequests,  int rateLimitedRequests)  $default,) {final _that = this;
switch (_that) {
case _TrafficAnalytics():
return $default(_that.timestamp,_that.totalRequests,_that.awsRequests,_that.datacenterRequests,_that.averageResponseTime,_that.errorRate,_that.requestsByCountry,_that.requestsByEndpoint,_that.blockedRequests,_that.rateLimitedRequests);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( DateTime timestamp,  int totalRequests,  int awsRequests,  int datacenterRequests,  double averageResponseTime,  double errorRate,  Map<String, int> requestsByCountry,  Map<String, int> requestsByEndpoint,  int blockedRequests,  int rateLimitedRequests)?  $default,) {final _that = this;
switch (_that) {
case _TrafficAnalytics() when $default != null:
return $default(_that.timestamp,_that.totalRequests,_that.awsRequests,_that.datacenterRequests,_that.averageResponseTime,_that.errorRate,_that.requestsByCountry,_that.requestsByEndpoint,_that.blockedRequests,_that.rateLimitedRequests);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TrafficAnalytics implements TrafficAnalytics {
  const _TrafficAnalytics({required this.timestamp, required this.totalRequests, required this.awsRequests, required this.datacenterRequests, required this.averageResponseTime, required this.errorRate, required final  Map<String, int> requestsByCountry, required final  Map<String, int> requestsByEndpoint, this.blockedRequests = 0, this.rateLimitedRequests = 0}): _requestsByCountry = requestsByCountry,_requestsByEndpoint = requestsByEndpoint;
  factory _TrafficAnalytics.fromJson(Map<String, dynamic> json) => _$TrafficAnalyticsFromJson(json);

@override final  DateTime timestamp;
@override final  int totalRequests;
@override final  int awsRequests;
@override final  int datacenterRequests;
@override final  double averageResponseTime;
@override final  double errorRate;
 final  Map<String, int> _requestsByCountry;
@override Map<String, int> get requestsByCountry {
  if (_requestsByCountry is EqualUnmodifiableMapView) return _requestsByCountry;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_requestsByCountry);
}

 final  Map<String, int> _requestsByEndpoint;
@override Map<String, int> get requestsByEndpoint {
  if (_requestsByEndpoint is EqualUnmodifiableMapView) return _requestsByEndpoint;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_requestsByEndpoint);
}

@override@JsonKey() final  int blockedRequests;
@override@JsonKey() final  int rateLimitedRequests;

/// Create a copy of TrafficAnalytics
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TrafficAnalyticsCopyWith<_TrafficAnalytics> get copyWith => __$TrafficAnalyticsCopyWithImpl<_TrafficAnalytics>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TrafficAnalyticsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TrafficAnalytics&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.totalRequests, totalRequests) || other.totalRequests == totalRequests)&&(identical(other.awsRequests, awsRequests) || other.awsRequests == awsRequests)&&(identical(other.datacenterRequests, datacenterRequests) || other.datacenterRequests == datacenterRequests)&&(identical(other.averageResponseTime, averageResponseTime) || other.averageResponseTime == averageResponseTime)&&(identical(other.errorRate, errorRate) || other.errorRate == errorRate)&&const DeepCollectionEquality().equals(other._requestsByCountry, _requestsByCountry)&&const DeepCollectionEquality().equals(other._requestsByEndpoint, _requestsByEndpoint)&&(identical(other.blockedRequests, blockedRequests) || other.blockedRequests == blockedRequests)&&(identical(other.rateLimitedRequests, rateLimitedRequests) || other.rateLimitedRequests == rateLimitedRequests));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,timestamp,totalRequests,awsRequests,datacenterRequests,averageResponseTime,errorRate,const DeepCollectionEquality().hash(_requestsByCountry),const DeepCollectionEquality().hash(_requestsByEndpoint),blockedRequests,rateLimitedRequests);

@override
String toString() {
  return 'TrafficAnalytics(timestamp: $timestamp, totalRequests: $totalRequests, awsRequests: $awsRequests, datacenterRequests: $datacenterRequests, averageResponseTime: $averageResponseTime, errorRate: $errorRate, requestsByCountry: $requestsByCountry, requestsByEndpoint: $requestsByEndpoint, blockedRequests: $blockedRequests, rateLimitedRequests: $rateLimitedRequests)';
}


}

/// @nodoc
abstract mixin class _$TrafficAnalyticsCopyWith<$Res> implements $TrafficAnalyticsCopyWith<$Res> {
  factory _$TrafficAnalyticsCopyWith(_TrafficAnalytics value, $Res Function(_TrafficAnalytics) _then) = __$TrafficAnalyticsCopyWithImpl;
@override @useResult
$Res call({
 DateTime timestamp, int totalRequests, int awsRequests, int datacenterRequests, double averageResponseTime, double errorRate, Map<String, int> requestsByCountry, Map<String, int> requestsByEndpoint, int blockedRequests, int rateLimitedRequests
});




}
/// @nodoc
class __$TrafficAnalyticsCopyWithImpl<$Res>
    implements _$TrafficAnalyticsCopyWith<$Res> {
  __$TrafficAnalyticsCopyWithImpl(this._self, this._then);

  final _TrafficAnalytics _self;
  final $Res Function(_TrafficAnalytics) _then;

/// Create a copy of TrafficAnalytics
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? timestamp = null,Object? totalRequests = null,Object? awsRequests = null,Object? datacenterRequests = null,Object? averageResponseTime = null,Object? errorRate = null,Object? requestsByCountry = null,Object? requestsByEndpoint = null,Object? blockedRequests = null,Object? rateLimitedRequests = null,}) {
  return _then(_TrafficAnalytics(
timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,totalRequests: null == totalRequests ? _self.totalRequests : totalRequests // ignore: cast_nullable_to_non_nullable
as int,awsRequests: null == awsRequests ? _self.awsRequests : awsRequests // ignore: cast_nullable_to_non_nullable
as int,datacenterRequests: null == datacenterRequests ? _self.datacenterRequests : datacenterRequests // ignore: cast_nullable_to_non_nullable
as int,averageResponseTime: null == averageResponseTime ? _self.averageResponseTime : averageResponseTime // ignore: cast_nullable_to_non_nullable
as double,errorRate: null == errorRate ? _self.errorRate : errorRate // ignore: cast_nullable_to_non_nullable
as double,requestsByCountry: null == requestsByCountry ? _self._requestsByCountry : requestsByCountry // ignore: cast_nullable_to_non_nullable
as Map<String, int>,requestsByEndpoint: null == requestsByEndpoint ? _self._requestsByEndpoint : requestsByEndpoint // ignore: cast_nullable_to_non_nullable
as Map<String, int>,blockedRequests: null == blockedRequests ? _self.blockedRequests : blockedRequests // ignore: cast_nullable_to_non_nullable
as int,rateLimitedRequests: null == rateLimitedRequests ? _self.rateLimitedRequests : rateLimitedRequests // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$LoadBalancerConfig {

 String get id; String get name; LoadBalancingAlgorithm get algorithm; List<ServerEndpoint> get endpoints; HealthCheckConfig get healthCheck; bool get stickySession; int get sessionTimeout; int get maxRetries; int get timeoutMs;
/// Create a copy of LoadBalancerConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LoadBalancerConfigCopyWith<LoadBalancerConfig> get copyWith => _$LoadBalancerConfigCopyWithImpl<LoadBalancerConfig>(this as LoadBalancerConfig, _$identity);

  /// Serializes this LoadBalancerConfig to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadBalancerConfig&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.algorithm, algorithm) || other.algorithm == algorithm)&&const DeepCollectionEquality().equals(other.endpoints, endpoints)&&(identical(other.healthCheck, healthCheck) || other.healthCheck == healthCheck)&&(identical(other.stickySession, stickySession) || other.stickySession == stickySession)&&(identical(other.sessionTimeout, sessionTimeout) || other.sessionTimeout == sessionTimeout)&&(identical(other.maxRetries, maxRetries) || other.maxRetries == maxRetries)&&(identical(other.timeoutMs, timeoutMs) || other.timeoutMs == timeoutMs));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,algorithm,const DeepCollectionEquality().hash(endpoints),healthCheck,stickySession,sessionTimeout,maxRetries,timeoutMs);

@override
String toString() {
  return 'LoadBalancerConfig(id: $id, name: $name, algorithm: $algorithm, endpoints: $endpoints, healthCheck: $healthCheck, stickySession: $stickySession, sessionTimeout: $sessionTimeout, maxRetries: $maxRetries, timeoutMs: $timeoutMs)';
}


}

/// @nodoc
abstract mixin class $LoadBalancerConfigCopyWith<$Res>  {
  factory $LoadBalancerConfigCopyWith(LoadBalancerConfig value, $Res Function(LoadBalancerConfig) _then) = _$LoadBalancerConfigCopyWithImpl;
@useResult
$Res call({
 String id, String name, LoadBalancingAlgorithm algorithm, List<ServerEndpoint> endpoints, HealthCheckConfig healthCheck, bool stickySession, int sessionTimeout, int maxRetries, int timeoutMs
});


$HealthCheckConfigCopyWith<$Res> get healthCheck;

}
/// @nodoc
class _$LoadBalancerConfigCopyWithImpl<$Res>
    implements $LoadBalancerConfigCopyWith<$Res> {
  _$LoadBalancerConfigCopyWithImpl(this._self, this._then);

  final LoadBalancerConfig _self;
  final $Res Function(LoadBalancerConfig) _then;

/// Create a copy of LoadBalancerConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? algorithm = null,Object? endpoints = null,Object? healthCheck = null,Object? stickySession = null,Object? sessionTimeout = null,Object? maxRetries = null,Object? timeoutMs = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,algorithm: null == algorithm ? _self.algorithm : algorithm // ignore: cast_nullable_to_non_nullable
as LoadBalancingAlgorithm,endpoints: null == endpoints ? _self.endpoints : endpoints // ignore: cast_nullable_to_non_nullable
as List<ServerEndpoint>,healthCheck: null == healthCheck ? _self.healthCheck : healthCheck // ignore: cast_nullable_to_non_nullable
as HealthCheckConfig,stickySession: null == stickySession ? _self.stickySession : stickySession // ignore: cast_nullable_to_non_nullable
as bool,sessionTimeout: null == sessionTimeout ? _self.sessionTimeout : sessionTimeout // ignore: cast_nullable_to_non_nullable
as int,maxRetries: null == maxRetries ? _self.maxRetries : maxRetries // ignore: cast_nullable_to_non_nullable
as int,timeoutMs: null == timeoutMs ? _self.timeoutMs : timeoutMs // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of LoadBalancerConfig
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HealthCheckConfigCopyWith<$Res> get healthCheck {
  
  return $HealthCheckConfigCopyWith<$Res>(_self.healthCheck, (value) {
    return _then(_self.copyWith(healthCheck: value));
  });
}
}


/// Adds pattern-matching-related methods to [LoadBalancerConfig].
extension LoadBalancerConfigPatterns on LoadBalancerConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LoadBalancerConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LoadBalancerConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LoadBalancerConfig value)  $default,){
final _that = this;
switch (_that) {
case _LoadBalancerConfig():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LoadBalancerConfig value)?  $default,){
final _that = this;
switch (_that) {
case _LoadBalancerConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  LoadBalancingAlgorithm algorithm,  List<ServerEndpoint> endpoints,  HealthCheckConfig healthCheck,  bool stickySession,  int sessionTimeout,  int maxRetries,  int timeoutMs)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LoadBalancerConfig() when $default != null:
return $default(_that.id,_that.name,_that.algorithm,_that.endpoints,_that.healthCheck,_that.stickySession,_that.sessionTimeout,_that.maxRetries,_that.timeoutMs);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  LoadBalancingAlgorithm algorithm,  List<ServerEndpoint> endpoints,  HealthCheckConfig healthCheck,  bool stickySession,  int sessionTimeout,  int maxRetries,  int timeoutMs)  $default,) {final _that = this;
switch (_that) {
case _LoadBalancerConfig():
return $default(_that.id,_that.name,_that.algorithm,_that.endpoints,_that.healthCheck,_that.stickySession,_that.sessionTimeout,_that.maxRetries,_that.timeoutMs);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  LoadBalancingAlgorithm algorithm,  List<ServerEndpoint> endpoints,  HealthCheckConfig healthCheck,  bool stickySession,  int sessionTimeout,  int maxRetries,  int timeoutMs)?  $default,) {final _that = this;
switch (_that) {
case _LoadBalancerConfig() when $default != null:
return $default(_that.id,_that.name,_that.algorithm,_that.endpoints,_that.healthCheck,_that.stickySession,_that.sessionTimeout,_that.maxRetries,_that.timeoutMs);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _LoadBalancerConfig implements LoadBalancerConfig {
  const _LoadBalancerConfig({required this.id, required this.name, required this.algorithm, required final  List<ServerEndpoint> endpoints, required this.healthCheck, this.stickySession = true, this.sessionTimeout = 30, this.maxRetries = 5, this.timeoutMs = 1000}): _endpoints = endpoints;
  factory _LoadBalancerConfig.fromJson(Map<String, dynamic> json) => _$LoadBalancerConfigFromJson(json);

@override final  String id;
@override final  String name;
@override final  LoadBalancingAlgorithm algorithm;
 final  List<ServerEndpoint> _endpoints;
@override List<ServerEndpoint> get endpoints {
  if (_endpoints is EqualUnmodifiableListView) return _endpoints;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_endpoints);
}

@override final  HealthCheckConfig healthCheck;
@override@JsonKey() final  bool stickySession;
@override@JsonKey() final  int sessionTimeout;
@override@JsonKey() final  int maxRetries;
@override@JsonKey() final  int timeoutMs;

/// Create a copy of LoadBalancerConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LoadBalancerConfigCopyWith<_LoadBalancerConfig> get copyWith => __$LoadBalancerConfigCopyWithImpl<_LoadBalancerConfig>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LoadBalancerConfigToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadBalancerConfig&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.algorithm, algorithm) || other.algorithm == algorithm)&&const DeepCollectionEquality().equals(other._endpoints, _endpoints)&&(identical(other.healthCheck, healthCheck) || other.healthCheck == healthCheck)&&(identical(other.stickySession, stickySession) || other.stickySession == stickySession)&&(identical(other.sessionTimeout, sessionTimeout) || other.sessionTimeout == sessionTimeout)&&(identical(other.maxRetries, maxRetries) || other.maxRetries == maxRetries)&&(identical(other.timeoutMs, timeoutMs) || other.timeoutMs == timeoutMs));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,algorithm,const DeepCollectionEquality().hash(_endpoints),healthCheck,stickySession,sessionTimeout,maxRetries,timeoutMs);

@override
String toString() {
  return 'LoadBalancerConfig(id: $id, name: $name, algorithm: $algorithm, endpoints: $endpoints, healthCheck: $healthCheck, stickySession: $stickySession, sessionTimeout: $sessionTimeout, maxRetries: $maxRetries, timeoutMs: $timeoutMs)';
}


}

/// @nodoc
abstract mixin class _$LoadBalancerConfigCopyWith<$Res> implements $LoadBalancerConfigCopyWith<$Res> {
  factory _$LoadBalancerConfigCopyWith(_LoadBalancerConfig value, $Res Function(_LoadBalancerConfig) _then) = __$LoadBalancerConfigCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, LoadBalancingAlgorithm algorithm, List<ServerEndpoint> endpoints, HealthCheckConfig healthCheck, bool stickySession, int sessionTimeout, int maxRetries, int timeoutMs
});


@override $HealthCheckConfigCopyWith<$Res> get healthCheck;

}
/// @nodoc
class __$LoadBalancerConfigCopyWithImpl<$Res>
    implements _$LoadBalancerConfigCopyWith<$Res> {
  __$LoadBalancerConfigCopyWithImpl(this._self, this._then);

  final _LoadBalancerConfig _self;
  final $Res Function(_LoadBalancerConfig) _then;

/// Create a copy of LoadBalancerConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? algorithm = null,Object? endpoints = null,Object? healthCheck = null,Object? stickySession = null,Object? sessionTimeout = null,Object? maxRetries = null,Object? timeoutMs = null,}) {
  return _then(_LoadBalancerConfig(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,algorithm: null == algorithm ? _self.algorithm : algorithm // ignore: cast_nullable_to_non_nullable
as LoadBalancingAlgorithm,endpoints: null == endpoints ? _self._endpoints : endpoints // ignore: cast_nullable_to_non_nullable
as List<ServerEndpoint>,healthCheck: null == healthCheck ? _self.healthCheck : healthCheck // ignore: cast_nullable_to_non_nullable
as HealthCheckConfig,stickySession: null == stickySession ? _self.stickySession : stickySession // ignore: cast_nullable_to_non_nullable
as bool,sessionTimeout: null == sessionTimeout ? _self.sessionTimeout : sessionTimeout // ignore: cast_nullable_to_non_nullable
as int,maxRetries: null == maxRetries ? _self.maxRetries : maxRetries // ignore: cast_nullable_to_non_nullable
as int,timeoutMs: null == timeoutMs ? _self.timeoutMs : timeoutMs // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of LoadBalancerConfig
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HealthCheckConfigCopyWith<$Res> get healthCheck {
  
  return $HealthCheckConfigCopyWith<$Res>(_self.healthCheck, (value) {
    return _then(_self.copyWith(healthCheck: value));
  });
}
}


/// @nodoc
mixin _$ServerEndpoint {

 String get id; String get host; int get port; int get weight; bool get isActive; String get protocol; String get healthCheckPath;
/// Create a copy of ServerEndpoint
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ServerEndpointCopyWith<ServerEndpoint> get copyWith => _$ServerEndpointCopyWithImpl<ServerEndpoint>(this as ServerEndpoint, _$identity);

  /// Serializes this ServerEndpoint to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ServerEndpoint&&(identical(other.id, id) || other.id == id)&&(identical(other.host, host) || other.host == host)&&(identical(other.port, port) || other.port == port)&&(identical(other.weight, weight) || other.weight == weight)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.protocol, protocol) || other.protocol == protocol)&&(identical(other.healthCheckPath, healthCheckPath) || other.healthCheckPath == healthCheckPath));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,host,port,weight,isActive,protocol,healthCheckPath);

@override
String toString() {
  return 'ServerEndpoint(id: $id, host: $host, port: $port, weight: $weight, isActive: $isActive, protocol: $protocol, healthCheckPath: $healthCheckPath)';
}


}

/// @nodoc
abstract mixin class $ServerEndpointCopyWith<$Res>  {
  factory $ServerEndpointCopyWith(ServerEndpoint value, $Res Function(ServerEndpoint) _then) = _$ServerEndpointCopyWithImpl;
@useResult
$Res call({
 String id, String host, int port, int weight, bool isActive, String protocol, String healthCheckPath
});




}
/// @nodoc
class _$ServerEndpointCopyWithImpl<$Res>
    implements $ServerEndpointCopyWith<$Res> {
  _$ServerEndpointCopyWithImpl(this._self, this._then);

  final ServerEndpoint _self;
  final $Res Function(ServerEndpoint) _then;

/// Create a copy of ServerEndpoint
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? host = null,Object? port = null,Object? weight = null,Object? isActive = null,Object? protocol = null,Object? healthCheckPath = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,host: null == host ? _self.host : host // ignore: cast_nullable_to_non_nullable
as String,port: null == port ? _self.port : port // ignore: cast_nullable_to_non_nullable
as int,weight: null == weight ? _self.weight : weight // ignore: cast_nullable_to_non_nullable
as int,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,protocol: null == protocol ? _self.protocol : protocol // ignore: cast_nullable_to_non_nullable
as String,healthCheckPath: null == healthCheckPath ? _self.healthCheckPath : healthCheckPath // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [ServerEndpoint].
extension ServerEndpointPatterns on ServerEndpoint {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ServerEndpoint value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ServerEndpoint() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ServerEndpoint value)  $default,){
final _that = this;
switch (_that) {
case _ServerEndpoint():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ServerEndpoint value)?  $default,){
final _that = this;
switch (_that) {
case _ServerEndpoint() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String host,  int port,  int weight,  bool isActive,  String protocol,  String healthCheckPath)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ServerEndpoint() when $default != null:
return $default(_that.id,_that.host,_that.port,_that.weight,_that.isActive,_that.protocol,_that.healthCheckPath);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String host,  int port,  int weight,  bool isActive,  String protocol,  String healthCheckPath)  $default,) {final _that = this;
switch (_that) {
case _ServerEndpoint():
return $default(_that.id,_that.host,_that.port,_that.weight,_that.isActive,_that.protocol,_that.healthCheckPath);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String host,  int port,  int weight,  bool isActive,  String protocol,  String healthCheckPath)?  $default,) {final _that = this;
switch (_that) {
case _ServerEndpoint() when $default != null:
return $default(_that.id,_that.host,_that.port,_that.weight,_that.isActive,_that.protocol,_that.healthCheckPath);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ServerEndpoint implements ServerEndpoint {
  const _ServerEndpoint({required this.id, required this.host, required this.port, required this.weight, required this.isActive, this.protocol = 'http', this.healthCheckPath = '/health'});
  factory _ServerEndpoint.fromJson(Map<String, dynamic> json) => _$ServerEndpointFromJson(json);

@override final  String id;
@override final  String host;
@override final  int port;
@override final  int weight;
@override final  bool isActive;
@override@JsonKey() final  String protocol;
@override@JsonKey() final  String healthCheckPath;

/// Create a copy of ServerEndpoint
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ServerEndpointCopyWith<_ServerEndpoint> get copyWith => __$ServerEndpointCopyWithImpl<_ServerEndpoint>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ServerEndpointToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ServerEndpoint&&(identical(other.id, id) || other.id == id)&&(identical(other.host, host) || other.host == host)&&(identical(other.port, port) || other.port == port)&&(identical(other.weight, weight) || other.weight == weight)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.protocol, protocol) || other.protocol == protocol)&&(identical(other.healthCheckPath, healthCheckPath) || other.healthCheckPath == healthCheckPath));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,host,port,weight,isActive,protocol,healthCheckPath);

@override
String toString() {
  return 'ServerEndpoint(id: $id, host: $host, port: $port, weight: $weight, isActive: $isActive, protocol: $protocol, healthCheckPath: $healthCheckPath)';
}


}

/// @nodoc
abstract mixin class _$ServerEndpointCopyWith<$Res> implements $ServerEndpointCopyWith<$Res> {
  factory _$ServerEndpointCopyWith(_ServerEndpoint value, $Res Function(_ServerEndpoint) _then) = __$ServerEndpointCopyWithImpl;
@override @useResult
$Res call({
 String id, String host, int port, int weight, bool isActive, String protocol, String healthCheckPath
});




}
/// @nodoc
class __$ServerEndpointCopyWithImpl<$Res>
    implements _$ServerEndpointCopyWith<$Res> {
  __$ServerEndpointCopyWithImpl(this._self, this._then);

  final _ServerEndpoint _self;
  final $Res Function(_ServerEndpoint) _then;

/// Create a copy of ServerEndpoint
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? host = null,Object? port = null,Object? weight = null,Object? isActive = null,Object? protocol = null,Object? healthCheckPath = null,}) {
  return _then(_ServerEndpoint(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,host: null == host ? _self.host : host // ignore: cast_nullable_to_non_nullable
as String,port: null == port ? _self.port : port // ignore: cast_nullable_to_non_nullable
as int,weight: null == weight ? _self.weight : weight // ignore: cast_nullable_to_non_nullable
as int,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,protocol: null == protocol ? _self.protocol : protocol // ignore: cast_nullable_to_non_nullable
as String,healthCheckPath: null == healthCheckPath ? _self.healthCheckPath : healthCheckPath // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$HealthCheckConfig {

 String get path; int get intervalSeconds; int get timeoutSeconds; int get healthyThreshold; int get unhealthyThreshold; int get expectedStatusCode; String? get expectedResponse;
/// Create a copy of HealthCheckConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HealthCheckConfigCopyWith<HealthCheckConfig> get copyWith => _$HealthCheckConfigCopyWithImpl<HealthCheckConfig>(this as HealthCheckConfig, _$identity);

  /// Serializes this HealthCheckConfig to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HealthCheckConfig&&(identical(other.path, path) || other.path == path)&&(identical(other.intervalSeconds, intervalSeconds) || other.intervalSeconds == intervalSeconds)&&(identical(other.timeoutSeconds, timeoutSeconds) || other.timeoutSeconds == timeoutSeconds)&&(identical(other.healthyThreshold, healthyThreshold) || other.healthyThreshold == healthyThreshold)&&(identical(other.unhealthyThreshold, unhealthyThreshold) || other.unhealthyThreshold == unhealthyThreshold)&&(identical(other.expectedStatusCode, expectedStatusCode) || other.expectedStatusCode == expectedStatusCode)&&(identical(other.expectedResponse, expectedResponse) || other.expectedResponse == expectedResponse));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,path,intervalSeconds,timeoutSeconds,healthyThreshold,unhealthyThreshold,expectedStatusCode,expectedResponse);

@override
String toString() {
  return 'HealthCheckConfig(path: $path, intervalSeconds: $intervalSeconds, timeoutSeconds: $timeoutSeconds, healthyThreshold: $healthyThreshold, unhealthyThreshold: $unhealthyThreshold, expectedStatusCode: $expectedStatusCode, expectedResponse: $expectedResponse)';
}


}

/// @nodoc
abstract mixin class $HealthCheckConfigCopyWith<$Res>  {
  factory $HealthCheckConfigCopyWith(HealthCheckConfig value, $Res Function(HealthCheckConfig) _then) = _$HealthCheckConfigCopyWithImpl;
@useResult
$Res call({
 String path, int intervalSeconds, int timeoutSeconds, int healthyThreshold, int unhealthyThreshold, int expectedStatusCode, String? expectedResponse
});




}
/// @nodoc
class _$HealthCheckConfigCopyWithImpl<$Res>
    implements $HealthCheckConfigCopyWith<$Res> {
  _$HealthCheckConfigCopyWithImpl(this._self, this._then);

  final HealthCheckConfig _self;
  final $Res Function(HealthCheckConfig) _then;

/// Create a copy of HealthCheckConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? path = null,Object? intervalSeconds = null,Object? timeoutSeconds = null,Object? healthyThreshold = null,Object? unhealthyThreshold = null,Object? expectedStatusCode = null,Object? expectedResponse = freezed,}) {
  return _then(_self.copyWith(
path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,intervalSeconds: null == intervalSeconds ? _self.intervalSeconds : intervalSeconds // ignore: cast_nullable_to_non_nullable
as int,timeoutSeconds: null == timeoutSeconds ? _self.timeoutSeconds : timeoutSeconds // ignore: cast_nullable_to_non_nullable
as int,healthyThreshold: null == healthyThreshold ? _self.healthyThreshold : healthyThreshold // ignore: cast_nullable_to_non_nullable
as int,unhealthyThreshold: null == unhealthyThreshold ? _self.unhealthyThreshold : unhealthyThreshold // ignore: cast_nullable_to_non_nullable
as int,expectedStatusCode: null == expectedStatusCode ? _self.expectedStatusCode : expectedStatusCode // ignore: cast_nullable_to_non_nullable
as int,expectedResponse: freezed == expectedResponse ? _self.expectedResponse : expectedResponse // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [HealthCheckConfig].
extension HealthCheckConfigPatterns on HealthCheckConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _HealthCheckConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _HealthCheckConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _HealthCheckConfig value)  $default,){
final _that = this;
switch (_that) {
case _HealthCheckConfig():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _HealthCheckConfig value)?  $default,){
final _that = this;
switch (_that) {
case _HealthCheckConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String path,  int intervalSeconds,  int timeoutSeconds,  int healthyThreshold,  int unhealthyThreshold,  int expectedStatusCode,  String? expectedResponse)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _HealthCheckConfig() when $default != null:
return $default(_that.path,_that.intervalSeconds,_that.timeoutSeconds,_that.healthyThreshold,_that.unhealthyThreshold,_that.expectedStatusCode,_that.expectedResponse);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String path,  int intervalSeconds,  int timeoutSeconds,  int healthyThreshold,  int unhealthyThreshold,  int expectedStatusCode,  String? expectedResponse)  $default,) {final _that = this;
switch (_that) {
case _HealthCheckConfig():
return $default(_that.path,_that.intervalSeconds,_that.timeoutSeconds,_that.healthyThreshold,_that.unhealthyThreshold,_that.expectedStatusCode,_that.expectedResponse);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String path,  int intervalSeconds,  int timeoutSeconds,  int healthyThreshold,  int unhealthyThreshold,  int expectedStatusCode,  String? expectedResponse)?  $default,) {final _that = this;
switch (_that) {
case _HealthCheckConfig() when $default != null:
return $default(_that.path,_that.intervalSeconds,_that.timeoutSeconds,_that.healthyThreshold,_that.unhealthyThreshold,_that.expectedStatusCode,_that.expectedResponse);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _HealthCheckConfig implements HealthCheckConfig {
  const _HealthCheckConfig({required this.path, required this.intervalSeconds, required this.timeoutSeconds, required this.healthyThreshold, required this.unhealthyThreshold, this.expectedStatusCode = 200, this.expectedResponse});
  factory _HealthCheckConfig.fromJson(Map<String, dynamic> json) => _$HealthCheckConfigFromJson(json);

@override final  String path;
@override final  int intervalSeconds;
@override final  int timeoutSeconds;
@override final  int healthyThreshold;
@override final  int unhealthyThreshold;
@override@JsonKey() final  int expectedStatusCode;
@override final  String? expectedResponse;

/// Create a copy of HealthCheckConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HealthCheckConfigCopyWith<_HealthCheckConfig> get copyWith => __$HealthCheckConfigCopyWithImpl<_HealthCheckConfig>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HealthCheckConfigToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HealthCheckConfig&&(identical(other.path, path) || other.path == path)&&(identical(other.intervalSeconds, intervalSeconds) || other.intervalSeconds == intervalSeconds)&&(identical(other.timeoutSeconds, timeoutSeconds) || other.timeoutSeconds == timeoutSeconds)&&(identical(other.healthyThreshold, healthyThreshold) || other.healthyThreshold == healthyThreshold)&&(identical(other.unhealthyThreshold, unhealthyThreshold) || other.unhealthyThreshold == unhealthyThreshold)&&(identical(other.expectedStatusCode, expectedStatusCode) || other.expectedStatusCode == expectedStatusCode)&&(identical(other.expectedResponse, expectedResponse) || other.expectedResponse == expectedResponse));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,path,intervalSeconds,timeoutSeconds,healthyThreshold,unhealthyThreshold,expectedStatusCode,expectedResponse);

@override
String toString() {
  return 'HealthCheckConfig(path: $path, intervalSeconds: $intervalSeconds, timeoutSeconds: $timeoutSeconds, healthyThreshold: $healthyThreshold, unhealthyThreshold: $unhealthyThreshold, expectedStatusCode: $expectedStatusCode, expectedResponse: $expectedResponse)';
}


}

/// @nodoc
abstract mixin class _$HealthCheckConfigCopyWith<$Res> implements $HealthCheckConfigCopyWith<$Res> {
  factory _$HealthCheckConfigCopyWith(_HealthCheckConfig value, $Res Function(_HealthCheckConfig) _then) = __$HealthCheckConfigCopyWithImpl;
@override @useResult
$Res call({
 String path, int intervalSeconds, int timeoutSeconds, int healthyThreshold, int unhealthyThreshold, int expectedStatusCode, String? expectedResponse
});




}
/// @nodoc
class __$HealthCheckConfigCopyWithImpl<$Res>
    implements _$HealthCheckConfigCopyWith<$Res> {
  __$HealthCheckConfigCopyWithImpl(this._self, this._then);

  final _HealthCheckConfig _self;
  final $Res Function(_HealthCheckConfig) _then;

/// Create a copy of HealthCheckConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? path = null,Object? intervalSeconds = null,Object? timeoutSeconds = null,Object? healthyThreshold = null,Object? unhealthyThreshold = null,Object? expectedStatusCode = null,Object? expectedResponse = freezed,}) {
  return _then(_HealthCheckConfig(
path: null == path ? _self.path : path // ignore: cast_nullable_to_non_nullable
as String,intervalSeconds: null == intervalSeconds ? _self.intervalSeconds : intervalSeconds // ignore: cast_nullable_to_non_nullable
as int,timeoutSeconds: null == timeoutSeconds ? _self.timeoutSeconds : timeoutSeconds // ignore: cast_nullable_to_non_nullable
as int,healthyThreshold: null == healthyThreshold ? _self.healthyThreshold : healthyThreshold // ignore: cast_nullable_to_non_nullable
as int,unhealthyThreshold: null == unhealthyThreshold ? _self.unhealthyThreshold : unhealthyThreshold // ignore: cast_nullable_to_non_nullable
as int,expectedStatusCode: null == expectedStatusCode ? _self.expectedStatusCode : expectedStatusCode // ignore: cast_nullable_to_non_nullable
as int,expectedResponse: freezed == expectedResponse ? _self.expectedResponse : expectedResponse // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
