import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/temple/temple_model.dart';
import '../../../../shared/models/temple/temple_booking_model.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../providers/temple_provider.dart';
import '../../widgets/temple/service_selection_widget.dart';
import '../../widgets/temple/devotee_info_form.dart';
import '../../widgets/temple/booking_summary_widget.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';

class TempleBookingScreen extends ConsumerStatefulWidget {
  final String templeId;

  const TempleBookingScreen({super.key, required this.templeId});

  @override
  ConsumerState<TempleBookingScreen> createState() =>
      _TempleBookingScreenState();
}

class _TempleBookingScreenState extends ConsumerState<TempleBookingScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentStep = 0;

  // Booking data
  List<TempleBookingItem> _selectedServices = [];
  DevoteeInfo? _devoteeInfo;
  DateTime? _selectedDate;
  bool _isBooking = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final templeAsync = ref.watch(templeDetailsProvider(widget.templeId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Book Temple Services'),
        backgroundColor: Colors.orange.shade800,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: templeAsync.when(
        data: (temple) {
          if (temple == null) {
            return const Center(child: Text('Temple not found'));
          }
          return _buildBookingFlow(context, temple);
        },
        loading: () => const LoadingIndicator(),
        error: (error, stack) => ErrorView(
          message: 'Failed to load temple: $error',
          onRetry: () => ref.refresh(templeDetailsProvider(widget.templeId)),
        ),
      ),
    );
  }

  Widget _buildBookingFlow(BuildContext context, Temple temple) {
    return Column(
      children: [
        _buildProgressIndicator(),
        Expanded(child: _buildStepContent(context, temple)),
        _buildBottomActions(context, temple),
      ],
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        border: Border(bottom: BorderSide(color: Colors.orange.shade200)),
      ),
      child: Row(
        children: [
          _buildStepIndicator(0, 'Services', Icons.temple_hindu),
          _buildStepConnector(),
          _buildStepIndicator(1, 'Details', Icons.person),
          _buildStepConnector(),
          _buildStepIndicator(2, 'Confirm', Icons.check_circle),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String label, IconData icon) {
    final isActive = step == _currentStep;
    final isCompleted = step < _currentStep;

    return Expanded(
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isCompleted
                  ? Colors.green
                  : isActive
                  ? Colors.orange.shade600
                  : Colors.grey.shade300,
              shape: BoxShape.circle,
            ),
            child: Icon(
              isCompleted ? Icons.check : icon,
              color: isCompleted || isActive ? Colors.white : Colors.grey,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
              color: isActive ? Colors.orange.shade700 : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepConnector() {
    return Container(
      height: 2,
      width: 30,
      color: Colors.grey.shade300,
      margin: const EdgeInsets.only(bottom: 20),
    );
  }

  Widget _buildStepContent(BuildContext context, Temple temple) {
    switch (_currentStep) {
      case 0:
        return ServiceSelectionWidget(
          temple: temple,
          selectedServices: _selectedServices,
          selectedDate: _selectedDate,
          onServicesChanged: (services) {
            setState(() {
              _selectedServices = services;
            });
          },
          onDateChanged: (date) {
            setState(() {
              _selectedDate = date;
            });
          },
        );
      case 1:
        return DevoteeInfoForm(
          devoteeInfo: _devoteeInfo,
          onDevoteeInfoChanged: (info) {
            setState(() {
              _devoteeInfo = info;
            });
          },
        );
      case 2:
        return BookingSummaryWidget(
          temple: temple,
          selectedServices: _selectedServices,
          devoteeInfo: _devoteeInfo!,
          selectedDate: _selectedDate!,
        );
      default:
        return const SizedBox();
    }
  }

  Widget _buildBottomActions(BuildContext context, Temple temple) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.orange.shade600,
                  side: BorderSide(color: Colors.orange.shade600),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Previous'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isBooking ? null : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isBooking
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(_getNextButtonText()),
            ),
          ),
        ],
      ),
    );
  }

  String _getNextButtonText() {
    switch (_currentStep) {
      case 0:
        return 'Continue';
      case 1:
        return 'Review Booking';
      case 2:
        return 'Confirm Booking';
      default:
        return 'Next';
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }

  void _nextStep() async {
    switch (_currentStep) {
      case 0:
        if (_validateServiceSelection()) {
          setState(() {
            _currentStep++;
          });
        }
        break;
      case 1:
        if (_validateDevoteeInfo()) {
          setState(() {
            _currentStep++;
          });
        }
        break;
      case 2:
        await _confirmBooking();
        break;
    }
  }

  bool _validateServiceSelection() {
    if (_selectedServices.isEmpty) {
      _showErrorSnackBar('Please select at least one service');
      return false;
    }
    if (_selectedDate == null) {
      _showErrorSnackBar('Please select a date');
      return false;
    }
    return true;
  }

  bool _validateDevoteeInfo() {
    if (_devoteeInfo == null) {
      _showErrorSnackBar('Please fill in devotee information');
      return false;
    }
    if (_devoteeInfo!.name.trim().isEmpty) {
      _showErrorSnackBar('Please enter devotee name');
      return false;
    }
    if (_devoteeInfo!.phone.trim().isEmpty) {
      _showErrorSnackBar('Please enter phone number');
      return false;
    }
    return true;
  }

  Future<void> _confirmBooking() async {
    setState(() {
      _isBooking = true;
    });

    try {
      // First create the booking
      final booking = await ref
          .read(templeProvider.notifier)
          .bookTempleService(
            templeId: widget.templeId,
            items: _selectedServices,
            devoteeInfo: _devoteeInfo!,
            visitDate: _selectedDate!,
          );

      if (booking != null) {
        // Navigate to payment screen
        if (mounted) {
          _navigateToPayment(booking);
        }
      } else {
        _showErrorSnackBar('Failed to create booking. Please try again.');
      }
    } catch (e) {
      _showErrorSnackBar('Error creating booking: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isBooking = false;
        });
      }
    }
  }

  void _navigateToPayment(TempleBooking booking) {
    final user = ref.read(currentUserProvider).value;
    if (user == null) {
      _showErrorSnackBar('Please log in to continue with payment');
      return;
    }

    // Navigate to PhonePe full payment screen with temple booking details
    context.go(
      '/buyer/payment/phonepe-full',
      extra: {
        'amount': booking.totalAmount,
        'orderId': booking.bookingNumber,
        'customerName':
            user.userMetadata?['display_name'] ??
            user.userMetadata?['full_name'] ??
            booking.devoteeInfo.name,
        'customerPhone': user.phone ?? booking.devoteeInfo.phone,
        'customerEmail': user.email ?? booking.devoteeInfo.email ?? '',
        'description':
            'Temple booking at ${booking.templeName} on ${_formatDate(booking.visitDate)}',
      },
    );
  }

  /// Format date for display in payment description
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
