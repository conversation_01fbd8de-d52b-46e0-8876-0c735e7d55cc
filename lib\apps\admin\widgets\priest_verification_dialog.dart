import 'package:flutter/material.dart';
import '../../../shared/models/priest.dart';

class PriestVerificationDialog extends StatefulWidget {
  final Priest priest;

  const PriestVerificationDialog({
    super.key,
    required this.priest,
  });

  @override
  State<PriestVerificationDialog> createState() =>
      _PriestVerificationDialogState();
}

class _PriestVerificationDialogState extends State<PriestVerificationDialog> {
  late bool _isVerified;
  late String _verificationStatus;
  late TextEditingController _notesController;

  @override
  void initState() {
    super.initState();
    _isVerified = widget.priest.isVerified;
    _verificationStatus = widget.priest.verificationStatus;
    _notesController =
        TextEditingController(text: widget.priest.verificationNotes);
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Update Priest Verification'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SwitchListTile(
            title: const Text('Verified'),
            value: _isVerified,
            onChanged: (value) {
              setState(() {
                _isVerified = value;
                _verificationStatus = value ? 'verified' : 'rejected';
              });
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _verificationStatus,
            decoration: const InputDecoration(
              labelText: 'Verification Status',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(
                value: 'pending',
                child: Text('Pending'),
              ),
              DropdownMenuItem(
                value: 'verified',
                child: Text('Verified'),
              ),
              DropdownMenuItem(
                value: 'rejected',
                child: Text('Rejected'),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _verificationStatus = value;
                  _isVerified = value == 'verified';
                });
              }
            },
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'Verification Notes',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, null),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(
            context,
            {
              'isVerified': _isVerified,
              'verificationStatus': _verificationStatus,
              'verificationNotes': _notesController.text,
            },
          ),
          child: const Text('Update'),
        ),
      ],
    );
  }
}
