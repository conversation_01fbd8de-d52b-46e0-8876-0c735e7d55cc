// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'list_submission_order_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ListSubmissionOrderState {

 bool get isLoading; String? get error; ListSubmissionOrder? get order;
/// Create a copy of ListSubmissionOrderState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ListSubmissionOrderStateCopyWith<ListSubmissionOrderState> get copyWith => _$ListSubmissionOrderStateCopyWithImpl<ListSubmissionOrderState>(this as ListSubmissionOrderState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ListSubmissionOrderState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error)&&(identical(other.order, order) || other.order == order));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,error,order);

@override
String toString() {
  return 'ListSubmissionOrderState(isLoading: $isLoading, error: $error, order: $order)';
}


}

/// @nodoc
abstract mixin class $ListSubmissionOrderStateCopyWith<$Res>  {
  factory $ListSubmissionOrderStateCopyWith(ListSubmissionOrderState value, $Res Function(ListSubmissionOrderState) _then) = _$ListSubmissionOrderStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, String? error, ListSubmissionOrder? order
});


$ListSubmissionOrderCopyWith<$Res>? get order;

}
/// @nodoc
class _$ListSubmissionOrderStateCopyWithImpl<$Res>
    implements $ListSubmissionOrderStateCopyWith<$Res> {
  _$ListSubmissionOrderStateCopyWithImpl(this._self, this._then);

  final ListSubmissionOrderState _self;
  final $Res Function(ListSubmissionOrderState) _then;

/// Create a copy of ListSubmissionOrderState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? error = freezed,Object? order = freezed,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as ListSubmissionOrder?,
  ));
}
/// Create a copy of ListSubmissionOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ListSubmissionOrderCopyWith<$Res>? get order {
    if (_self.order == null) {
    return null;
  }

  return $ListSubmissionOrderCopyWith<$Res>(_self.order!, (value) {
    return _then(_self.copyWith(order: value));
  });
}
}


/// Adds pattern-matching-related methods to [ListSubmissionOrderState].
extension ListSubmissionOrderStatePatterns on ListSubmissionOrderState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ListSubmissionOrderState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ListSubmissionOrderState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ListSubmissionOrderState value)  $default,){
final _that = this;
switch (_that) {
case _ListSubmissionOrderState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ListSubmissionOrderState value)?  $default,){
final _that = this;
switch (_that) {
case _ListSubmissionOrderState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isLoading,  String? error,  ListSubmissionOrder? order)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ListSubmissionOrderState() when $default != null:
return $default(_that.isLoading,_that.error,_that.order);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isLoading,  String? error,  ListSubmissionOrder? order)  $default,) {final _that = this;
switch (_that) {
case _ListSubmissionOrderState():
return $default(_that.isLoading,_that.error,_that.order);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isLoading,  String? error,  ListSubmissionOrder? order)?  $default,) {final _that = this;
switch (_that) {
case _ListSubmissionOrderState() when $default != null:
return $default(_that.isLoading,_that.error,_that.order);case _:
  return null;

}
}

}

/// @nodoc


class _ListSubmissionOrderState implements ListSubmissionOrderState {
  const _ListSubmissionOrderState({this.isLoading = false, this.error = null, this.order = null});
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  String? error;
@override@JsonKey() final  ListSubmissionOrder? order;

/// Create a copy of ListSubmissionOrderState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ListSubmissionOrderStateCopyWith<_ListSubmissionOrderState> get copyWith => __$ListSubmissionOrderStateCopyWithImpl<_ListSubmissionOrderState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ListSubmissionOrderState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error)&&(identical(other.order, order) || other.order == order));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,error,order);

@override
String toString() {
  return 'ListSubmissionOrderState(isLoading: $isLoading, error: $error, order: $order)';
}


}

/// @nodoc
abstract mixin class _$ListSubmissionOrderStateCopyWith<$Res> implements $ListSubmissionOrderStateCopyWith<$Res> {
  factory _$ListSubmissionOrderStateCopyWith(_ListSubmissionOrderState value, $Res Function(_ListSubmissionOrderState) _then) = __$ListSubmissionOrderStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, String? error, ListSubmissionOrder? order
});


@override $ListSubmissionOrderCopyWith<$Res>? get order;

}
/// @nodoc
class __$ListSubmissionOrderStateCopyWithImpl<$Res>
    implements _$ListSubmissionOrderStateCopyWith<$Res> {
  __$ListSubmissionOrderStateCopyWithImpl(this._self, this._then);

  final _ListSubmissionOrderState _self;
  final $Res Function(_ListSubmissionOrderState) _then;

/// Create a copy of ListSubmissionOrderState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? error = freezed,Object? order = freezed,}) {
  return _then(_ListSubmissionOrderState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,order: freezed == order ? _self.order : order // ignore: cast_nullable_to_non_nullable
as ListSubmissionOrder?,
  ));
}

/// Create a copy of ListSubmissionOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ListSubmissionOrderCopyWith<$Res>? get order {
    if (_self.order == null) {
    return null;
  }

  return $ListSubmissionOrderCopyWith<$Res>(_self.order!, (value) {
    return _then(_self.copyWith(order: value));
  });
}
}

// dart format on
