import '../service_locator.dart';

// Use the same GetIt instance as service_locator.dart
final getIt = serviceLocator;

// This flag helps prevent duplicate initialization
bool _dependenciesInitialized = false;

Future<void> initializeDependencies() async {
  // Skip if already initialized
  if (_dependenciesInitialized) return;

  _dependenciesInitialized = true;
  // We don't need to call setupServiceLocator() here since it's already called in main.dart
  // This prevents duplicate registration of services
}
