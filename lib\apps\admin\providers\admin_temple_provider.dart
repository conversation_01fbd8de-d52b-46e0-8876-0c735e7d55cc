import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../shared/models/temple/temple_model.dart';
import '../../../shared/services/admin/admin_temple_service.dart';
import '../../../shared/core/service_locator.dart';

/// Admin temple state class
@immutable
class AdminTempleState {
  final List<Temple> pendingTemples;
  final List<Temple> approvedTemples;
  final List<Temple> rejectedTemples;
  final List<Temple> allTemples;
  final bool isLoading;
  final String? error;

  const AdminTempleState({
    this.pendingTemples = const [],
    this.approvedTemples = const [],
    this.rejectedTemples = const [],
    this.allTemples = const [],
    this.isLoading = false,
    this.error,
  });

  AdminTempleState copyWith({
    List<Temple>? pendingTemples,
    List<Temple>? approvedTemples,
    List<Temple>? rejectedTemples,
    List<Temple>? allTemples,
    bool? isLoading,
    String? error,
  }) {
    return AdminTempleState(
      pendingTemples: pendingTemples ?? this.pendingTemples,
      approvedTemples: approvedTemples ?? this.approvedTemples,
      rejectedTemples: rejectedTemples ?? this.rejectedTemples,
      allTemples: allTemples ?? this.allTemples,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Admin temple notifier class
class AdminTempleNotifier extends StateNotifier<AdminTempleState> {
  final AdminTempleService _adminTempleService;

  AdminTempleNotifier(this._adminTempleService)
    : super(const AdminTempleState());

  /// Load pending temples
  Future<void> loadPendingTemples() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temples = await _adminTempleService.getPendingTemples();
      state = state.copyWith(pendingTemples: temples, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Load approved temples
  Future<void> loadApprovedTemples() async {
    try {
      final temples = await _adminTempleService.getApprovedTemples();
      state = state.copyWith(approvedTemples: temples);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Load rejected temples
  Future<void> loadRejectedTemples() async {
    try {
      final temples = await _adminTempleService.getRejectedTemples();
      state = state.copyWith(rejectedTemples: temples);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Load all temples
  Future<void> loadAllTemples() async {
    try {
      final temples = await _adminTempleService.getAllTemples();
      state = state.copyWith(allTemples: temples);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Approve temple
  Future<bool> approveTemple(String templeId, String notes) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final success = await _adminTempleService.approveTemple(templeId, notes);

      if (success) {
        // Remove from pending and add to approved
        final updatedPending = state.pendingTemples
            .where((temple) => temple.id != templeId)
            .toList();

        // Find the approved temple and update its status
        final approvedTemple = state.pendingTemples
            .firstWhere((temple) => temple.id == templeId)
            .copyWith(
              status: TempleStatus.approved,
              verificationNotes: notes,
              updatedAt: DateTime.now(),
            );

        final updatedApproved = [...state.approvedTemples, approvedTemple];

        state = state.copyWith(
          pendingTemples: updatedPending,
          approvedTemples: updatedApproved,
          isLoading: false,
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Reject temple
  Future<bool> rejectTemple(String templeId, String reason) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final success = await _adminTempleService.rejectTemple(templeId, reason);

      if (success) {
        // Remove from pending and add to rejected
        final updatedPending = state.pendingTemples
            .where((temple) => temple.id != templeId)
            .toList();

        // Find the rejected temple and update its status
        final rejectedTemple = state.pendingTemples
            .firstWhere((temple) => temple.id == templeId)
            .copyWith(
              status: TempleStatus.rejected,
              rejectionReason: reason,
              updatedAt: DateTime.now(),
            );

        final updatedRejected = [...state.rejectedTemples, rejectedTemple];

        state = state.copyWith(
          pendingTemples: updatedPending,
          rejectedTemples: updatedRejected,
          isLoading: false,
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Suspend temple
  Future<bool> suspendTemple(String templeId, String reason) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final success = await _adminTempleService.suspendTemple(templeId, reason);

      if (success) {
        // Update temple status in all lists
        final updatedApproved = state.approvedTemples.map((temple) {
          if (temple.id == templeId) {
            return temple.copyWith(
              status: TempleStatus.suspended,
              verificationNotes: reason,
              updatedAt: DateTime.now(),
            );
          }
          return temple;
        }).toList();

        state = state.copyWith(
          approvedTemples: updatedApproved,
          isLoading: false,
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Reactivate temple
  Future<bool> reactivateTemple(String templeId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final success = await _adminTempleService.reactivateTemple(templeId);

      if (success) {
        // Update temple status
        final updatedApproved = state.approvedTemples.map((temple) {
          if (temple.id == templeId) {
            return temple.copyWith(
              status: TempleStatus.active,
              updatedAt: DateTime.now(),
            );
          }
          return temple;
        }).toList();

        state = state.copyWith(
          approvedTemples: updatedApproved,
          isLoading: false,
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Get temple statistics
  Map<String, int> getTempleStatistics() {
    return {
      'pending': state.pendingTemples.length,
      'approved': state.approvedTemples.length,
      'rejected': state.rejectedTemples.length,
      'total': state.allTemples.length,
      'active': state.approvedTemples
          .where((temple) => temple.status == TempleStatus.active)
          .length,
      'suspended': state.approvedTemples
          .where((temple) => temple.status == TempleStatus.suspended)
          .length,
    };
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Admin temple service provider
final adminTempleServiceProvider = Provider<AdminTempleService>((ref) {
  return serviceLocator<AdminTempleService>();
});

/// Admin temple provider
final adminTempleProvider =
    StateNotifierProvider<AdminTempleNotifier, AdminTempleState>((ref) {
      final adminTempleService = ref.watch(adminTempleServiceProvider);
      return AdminTempleNotifier(adminTempleService);
    });
