import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/order/order_model.dart';

abstract class OrdersRepository {
  Future<List<OrderModel>> getOrders();
  Future<OrderModel?> getOrder(String id);
  Future<OrderModel> createOrder(OrderModel order);
  Future<OrderModel> updateOrder(OrderModel order);
  Future<void> deleteOrder(String id);
  Future<List<OrderModel>> getOrdersByStatus(OrderStatus status);
  Future<List<OrderModel>> getOrdersBySeller(String sellerId);
  Future<List<OrderModel>> getOrdersByBuyer(String buyerId);
}

final ordersRepositoryProvider = Provider<OrdersRepository>((ref) {
  throw UnimplementedError('OrdersRepository not implemented');
});
