import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/services/delivery/ecom_express_service.dart';
import '../../../shared/utils/logger.dart';

/// Provider for the webhook handler service
/// Note: This provider is deprecated. Use dependency injection instead.
/// The WebhookHandlerService is now injectable and should be obtained through DI.
@Deprecated('Use dependency injection instead')
final webhookHandlerServiceProvider = Provider<WebhookHandlerService>((ref) {
  throw UnimplementedError(
    'WebhookHandlerService provider is deprecated. '
    'Use @injectable annotation and dependency injection instead.'
  );
});

/// Service for handling webhooks from various delivery providers
@injectable
class WebhookHandlerService {
  final DatabaseService _databaseService;
  final EcomExpressService _ecomExpressService;
  final _logger = getLogger('WebhookHandlerService');

  WebhookHandlerService({
    required DatabaseService databaseService,
    required EcomExpressService ecomExpressService,
  }) : _databaseService = databaseService,
       _ecomExpressService = ecomExpressService;
  
  /// Handle webhook from Ecom Express
  Future<bool> handleEcomExpressWebhook(
    String payload,
    Map<String, String> headers,
  ) async {
    try {
      // Log webhook receipt
      _logger.info('Received Ecom Express webhook');
      
      // Parse payload
      final Map<String, dynamic> data = jsonDecode(payload);
      
      // Get signature from headers
      final signature = headers['x-ecomexpress-signature'] ?? '';
      
      // Process webhook notification
      final result = await _ecomExpressService.processWebhookNotification(data, signature);
      
      // Log webhook processing result
      if (result) {
        _logger.info('Successfully processed Ecom Express webhook');
      } else {
        _logger.warning('Failed to process Ecom Express webhook');
      }
      
      // Store webhook for audit purposes
      await _storeWebhook('ecom_express', data, headers, result);
      
      return result;
    } catch (e) {
      _logger.severe('Error handling Ecom Express webhook: $e');
      
      // Store failed webhook
      await _storeWebhook(
        'ecom_express',
        {'error': e.toString()},
        headers,
        false,
      );
      
      return false;
    }
  }
  
  /// Store webhook data for audit purposes
  Future<void> _storeWebhook(
    String provider,
    Map<String, dynamic> data,
    Map<String, String> headers,
    bool processed,
  ) async {
    try {
      _logger.info('Storing webhook data for provider: $provider');

      final webhookData = {
        'provider': provider,
        'data': data,
        'headers': headers,
        'processed': processed,
        'receivedAt': DateTime.now().toIso8601String(),
        'processedAt': processed ? DateTime.now().toIso8601String() : null,
      };

      final result = await _databaseService.create('webhooks', webhookData);

      _logger.info('Successfully stored webhook with ID: ${result['id']}');
    } catch (e) {
      _logger.warning('Error storing webhook data: $e');
      // Don't rethrow as this is for audit purposes and shouldn't break the main flow
    }
  }

  /// Get webhook history for a specific provider
  Future<List<Map<String, dynamic>>> getWebhookHistory(
    String provider, {
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      _logger.info('Fetching webhook history for provider: $provider');

      final webhooks = await _databaseService.getAll(
        'webhooks',
        where: 'provider = ?',
        whereParams: [provider],
        orderBy: 'created_at DESC',
        limit: limit,
        offset: offset,
      );

      _logger.info('Found ${webhooks.length} webhook records for provider: $provider');
      return webhooks;
    } catch (e) {
      _logger.severe('Failed to get webhook history: $e');
      throw Exception('Failed to get webhook history: $e');
    }
  }

  /// Get failed webhooks for retry
  Future<List<Map<String, dynamic>>> getFailedWebhooks({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      _logger.info('Fetching failed webhooks for retry');

      final failedWebhooks = await _databaseService.getAll(
        'webhooks',
        where: 'processed = ?',
        whereParams: [false],
        orderBy: 'created_at ASC',
        limit: limit,
        offset: offset,
      );

      _logger.info('Found ${failedWebhooks.length} failed webhook records');
      return failedWebhooks;
    } catch (e) {
      _logger.severe('Failed to get failed webhooks: $e');
      throw Exception('Failed to get failed webhooks: $e');
    }
  }

  /// Mark webhook as processed
  Future<void> markWebhookAsProcessed(String webhookId) async {
    try {
      _logger.info('Marking webhook as processed: $webhookId');

      final updateData = {
        'processed': true,
        'processedAt': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final result = await _databaseService.update('webhooks', webhookId, updateData);

      if (result == null) {
        throw Exception('Webhook not found with ID: $webhookId');
      }

      _logger.info('Successfully marked webhook as processed: $webhookId');
    } catch (e) {
      _logger.severe('Failed to mark webhook as processed: $e');
      throw Exception('Failed to mark webhook as processed: $e');
    }
  }

  /// Handle webhook from any delivery provider
  Future<bool> handleDeliveryWebhook(
    String provider,
    String payload,
    Map<String, String> headers,
  ) async {
    try {
      _logger.info('Received webhook from provider: $provider');

      switch (provider.toLowerCase()) {
        case 'ecom_express':
        case 'ecomexpress':
          return await handleEcomExpressWebhook(payload, headers);
        default:
          _logger.warning('Unknown delivery provider: $provider');
          await _storeWebhook(provider, {'error': 'Unknown provider'}, headers, false);
          return false;
      }
    } catch (e) {
      _logger.severe('Error handling delivery webhook: $e');
      await _storeWebhook(provider, {'error': e.toString()}, headers, false);
      return false;
    }
  }

  /// Retry failed webhook processing
  Future<bool> retryFailedWebhook(String webhookId) async {
    try {
      _logger.info('Retrying failed webhook: $webhookId');

      final webhook = await _databaseService.find('webhooks', webhookId);
      if (webhook == null) {
        throw Exception('Webhook not found with ID: $webhookId');
      }

      if (webhook['processed'] == true) {
        _logger.info('Webhook already processed: $webhookId');
        return true;
      }

      final provider = webhook['provider'] as String;
      final data = webhook['data'] as Map<String, dynamic>;
      final headers = Map<String, String>.from(webhook['headers'] as Map);

      // Retry processing based on provider
      bool success = false;
      switch (provider.toLowerCase()) {
        case 'ecom_express':
        case 'ecomexpress':
          success = await _ecomExpressService.processWebhookNotification(data, headers['x-ecomexpress-signature'] ?? '');
          break;
        default:
          _logger.warning('Cannot retry unknown provider: $provider');
          return false;
      }

      if (success) {
        await markWebhookAsProcessed(webhookId);
        _logger.info('Successfully retried webhook: $webhookId');
      } else {
        _logger.warning('Retry failed for webhook: $webhookId');
      }

      return success;
    } catch (e) {
      _logger.severe('Failed to retry webhook: $e');
      throw Exception('Failed to retry webhook: $e');
    }
  }

  /// Get webhook statistics
  Future<Map<String, dynamic>> getWebhookStats({
    String? provider,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      _logger.info('Fetching webhook statistics');

      // Build where clause
      final whereConditions = <String>[];
      final whereParams = <dynamic>[];

      if (provider != null) {
        whereConditions.add('provider = ?');
        whereParams.add(provider);
      }

      if (startDate != null) {
        whereConditions.add('created_at >= ?');
        whereParams.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereConditions.add('created_at <= ?');
        whereParams.add(endDate.toIso8601String());
      }

      final whereClause = whereConditions.isNotEmpty ? whereConditions.join(' AND ') : null;

      final allWebhooks = await _databaseService.getAll(
        'webhooks',
        where: whereClause,
        whereParams: whereParams.isNotEmpty ? whereParams : null,
      );

      final total = allWebhooks.length;
      final processed = allWebhooks.where((w) => w['processed'] == true).length;
      final failed = total - processed;

      final stats = {
        'total': total,
        'processed': processed,
        'failed': failed,
        'success_rate': total > 0 ? (processed / total * 100).toStringAsFixed(2) : '0.00',
        'provider': provider,
        'start_date': startDate?.toIso8601String(),
        'end_date': endDate?.toIso8601String(),
      };

      _logger.info('Webhook stats: $stats');
      return stats;
    } catch (e) {
      _logger.severe('Failed to get webhook stats: $e');
      throw Exception('Failed to get webhook stats: $e');
    }
  }
}
