import 'package:injectable/injectable.dart';
import 'package:flutter/foundation.dart';
import '../../../../../../shared/models/product/product_model.dart';
import '../../../../../../shared/core/error/exceptions.dart';
import '../../../../../../shared/database/services/database_service.dart';
import '../../../../../../shared/services/auth/auth_service.dart';

@injectable
class PriceManagementRepository {
  final DatabaseService _databaseService;
  final AuthService _authService;

  PriceManagementRepository(this._databaseService, this._authService);

  Future<List<ProductModel>> getProducts() async {
    try {
      final products = await _databaseService.getAll('products');
      return products
          .map((data) => ProductModel.fromJson(data))
          .toList();
    } catch (e) {
      debugPrint('Error getting products: $e');
      throw ServerException();
    }
  }

  Future<void> updatePrice(String productId, double newPrice) async {
    try {
      final doc = await _databaseService.find('products', productId);
      if (doc == null) {
        throw NotFoundException();
      }

      final oldPrice = (doc['price'] as num?)?.toDouble() ?? 0.0;
      final currentUser = _authService.currentUser?.id ?? 'unknown';

      // Update product price
      await _databaseService.update('products', productId, {
        'price': newPrice,
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Add price history record
      await _databaseService.create('price_history', {
        'product_id': productId,
        'old_price': oldPrice,
        'new_price': newPrice,
        'reason': 'Manual price update',
        'timestamp': DateTime.now().toIso8601String(),
        'updated_by': currentUser,
      });
    } catch (e) {
      debugPrint('Error updating price: $e');
      if (e is NotFoundException) rethrow;
      throw ServerException();
    }
  }

  Future<void> updateDiscount(String productId, double discount) async {
    try {
      final doc = await _databaseService.find('products', productId);
      if (doc == null) {
        throw NotFoundException();
      }

      final product = ProductModel.fromJson(doc);
      final newSalePrice = product.price * (1 - discount / 100);
      final currentUser = _authService.currentUser?.id ?? 'unknown';

      // Update product with discount
      await _databaseService.update('products', productId, {
        'sale_price': newSalePrice,
        'discount_percentage': discount,
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Add price history record
      await _databaseService.create('price_history', {
        'product_id': productId,
        'old_price': product.salePrice,
        'new_price': newSalePrice,
        'reason': 'Discount update: $discount%',
        'timestamp': DateTime.now().toIso8601String(),
        'updated_by': currentUser,
      });
    } catch (e) {
      debugPrint('Error updating discount: $e');
      if (e is NotFoundException) rethrow;
      throw ServerException();
    }
  }

  Future<void> bulkUpdatePrices(
      String updateType, double value, List<String> productIds) async {
    try {
      final currentUser = _authService.currentUser?.id ?? 'unknown';

      // Use transaction for bulk operations to ensure consistency
      await _databaseService.executeInTransaction((transaction) async {
        for (final productId in productIds) {
          final doc = await transaction.find('products', productId);
          if (doc == null) continue;

          final product = ProductModel.fromJson(doc);
          double newPrice = product.price;

          switch (updateType) {
            case 'percentageIncrease':
              newPrice = product.price * (1 + value / 100);
              break;
            case 'percentageDecrease':
              newPrice = product.price * (1 - value / 100);
              break;
            case 'fixedAmount':
              newPrice = product.price + value;
              break;
            case 'setPrice':
              newPrice = value;
              break;
          }

          // Update product price
          await transaction.update('products', productId, {
            'price': newPrice,
            'updated_at': DateTime.now().toIso8601String(),
          });

          // Add price history record
          await transaction.create('price_history', {
            'product_id': productId,
            'old_price': product.price,
            'new_price': newPrice,
            'reason': 'Bulk update: $updateType ($value)',
            'timestamp': DateTime.now().toIso8601String(),
            'updated_by': currentUser,
          });
        }
      });
    } catch (e) {
      debugPrint('Error bulk updating prices: $e');
      throw ServerException();
    }
  }

  Future<List<Map<String, dynamic>>> getPriceHistory(
      String productId, String filter) async {
    try {
      String whereClause = 'product_id = @param0';
      List<dynamic> whereParams = [productId];

      final now = DateTime.now();
      switch (filter) {
        case 'today':
          final todayStart = DateTime(now.year, now.month, now.day);
          whereClause += ' AND timestamp >= @param1';
          whereParams.add(todayStart.toIso8601String());
          break;
        case 'thisWeek':
          final weekStart = now.subtract(Duration(days: now.weekday - 1));
          final weekStartDate = DateTime(weekStart.year, weekStart.month, weekStart.day);
          whereClause += ' AND timestamp >= @param1';
          whereParams.add(weekStartDate.toIso8601String());
          break;
        case 'thisMonth':
          final monthStart = DateTime(now.year, now.month, 1);
          whereClause += ' AND timestamp >= @param1';
          whereParams.add(monthStart.toIso8601String());
          break;
        case 'thisYear':
          final yearStart = DateTime(now.year, 1, 1);
          whereClause += ' AND timestamp >= @param1';
          whereParams.add(yearStart.toIso8601String());
          break;
      }

      final history = await _databaseService.getAll(
        'price_history',
        where: whereClause,
        whereParams: whereParams,
        orderBy: 'timestamp DESC',
      );

      return history;
    } catch (e) {
      debugPrint('Error getting price history: $e');
      throw ServerException();
    }
  }
}
