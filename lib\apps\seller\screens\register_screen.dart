import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:file_picker/file_picker.dart';
import 'package:shivish/apps/seller/domain/models/seller_category.dart';
import 'package:shivish/apps/seller/presentation/widgets/seller_category_selector.dart';
import 'package:shivish/apps/seller/presentation/widgets/service_types_selector.dart';
import 'package:shivish/shared/core/auth/models/register_request.dart';
import 'package:shivish/shared/models/user/user_model.dart';
import '../application/seller_provider.dart';
import '../../../shared/ui_components/inputs/text_field.dart';
import '../../../shared/ui_components/buttons/app_button.dart';
import '../../../shared/ui_components/loading/loading_indicator.dart';
import '../seller_routes.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/services/storage/adaptive_storage_service.dart';

// Set to true to bypass document upload requirements during development
const _debugSkipDocumentUploads = false;

class RegisterScreen extends ConsumerStatefulWidget {
  /// Callback function that will be called when registration is successful
  final VoidCallback? onRegisterSuccess;

  const RegisterScreen({super.key, this.onRegisterSuccess});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _businessNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;
  bool _isCheckingDocumentRequirements = true;
  bool _documentsRequired = false;
  SellerCategory _selectedCategory = SellerCategory.other;
  List<String> _selectedServiceTypes = [];

  // Document files
  File? _businessLicenseDocument;
  File? _idProofDocument;
  File? _addressProofDocument;
  bool _isUploadingDocuments = false;

  // Services
  late final DatabaseService _databaseService;
  late final AdaptiveStorageService _storageService;

  @override
  void initState() {
    super.initState();
    _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
    _storageService = AdaptiveStorageService();
    _checkDocumentRequirements();
  }

  Future<void> _checkDocumentRequirements() async {
    setState(() {
      _isCheckingDocumentRequirements = true;
    });

    try {
      // Force documents to be required for now
      setState(() {
        _documentsRequired = true;
        _isCheckingDocumentRequirements = false;
      });

      // Also try to fetch from hybrid database as a backup
      try {
        final configData = await _databaseService.getAll(
          'system_config',
          where: 'config_type = @param0',
          whereParams: ['registration'],
          limit: 1,
        );

        if (configData.isNotEmpty) {
          final data = configData.first;
          final bool docsRequired = data['documents_required'] == true;
          debugPrint('Database documents_required: $docsRequired');

          // Only update if we're not already requiring documents
          if (!_documentsRequired) {
            setState(() {
              _documentsRequired = docsRequired;
            });
          }
        } else {
          debugPrint('Registration config document does not exist in database');
        }
      } catch (databaseError) {
        debugPrint('Error checking database: $databaseError');
        // We already set _documentsRequired = true above, so no need to update state
      }
    } catch (e) {
      debugPrint('Error in _checkDocumentRequirements: $e');
      setState(() {
        // Force documents to be required even if there's an error
        _documentsRequired = true;
        _isCheckingDocumentRequirements = false;
      });
    }
  }

  // Pick document file
  Future<void> _pickDocument(String type) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        setState(() {
          if (type == 'business_license') {
            _businessLicenseDocument = file;
          } else if (type == 'id_proof') {
            _idProofDocument = file;
          } else if (type == 'address_proof') {
            _addressProofDocument = file;
          }
        });
      }
    } catch (e) {
      debugPrint('SellerRegisterScreen: Error picking $type document: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick $type document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // Upload document to hybrid storage
  Future<String?> _uploadDocument(File file, String type) async {
    // If in debug mode with document uploads disabled, return a placeholder URL
    if (_debugSkipDocumentUploads) {
      debugPrint('DEBUG MODE: Skipping actual document upload for $type');
      return 'https://placeholder.com/${type}_document.jpg';
    }

    try {
      setState(() {
        _isUploadingDocuments = true;
      });

      // For registration, we don't have a user ID yet, so we'll use a temporary ID based on email
      final tempUserId = _emailController.text
          .trim()
          .replaceAll('@', '_')
          .replaceAll('.', '_');
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${type}_${timestamp}_${file.path.split('/').last}';

      // Upload to hybrid storage
      final fileBytes = await file.readAsBytes();
      final downloadUrl = await _storageService.uploadData(
        data: fileBytes,
        fileName: fileName,
        storagePath: 'documents/$tempUserId',
      );

      debugPrint('Document uploaded to hybrid storage: $downloadUrl');

      return downloadUrl;
    } catch (e) {
      debugPrint('SellerRegisterScreen: Error uploading $type document: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to upload $type document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  Future<void> _handleRegister() async {
    if (_formKey.currentState?.validate() ?? false) {
      // Validate service types for event services
      if (_selectedCategory.isEventService && _selectedServiceTypes.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Please select at least one service type for ${_selectedCategory.displayName}',
            ),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }
      // Get the current state
      final currentState = ref.read(sellerProvider);

      // Check if already loading
      if (currentState.isLoading) return;

      // Set loading state
      ref.read(sellerProvider.notifier).clearError();
      setState(() {
        _isLoading = true;
      });

      // Check if documents are required but not provided (skip in debug mode)
      if (_documentsRequired && !_debugSkipDocumentUploads) {
        if (_businessLicenseDocument == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please upload your Business License document'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() {
            _isLoading = false;
          });
          return;
        }

        if (_idProofDocument == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please upload your ID Proof document'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() {
            _isLoading = false;
          });
          return;
        }

        if (_addressProofDocument == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please upload your Address Proof document'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() {
            _isLoading = false;
          });
          return;
        }
      } else if (_documentsRequired && _debugSkipDocumentUploads) {
        debugPrint('DEBUG MODE: Skipping document validation');
      }

      try {
        // Document URLs
        List<Map<String, dynamic>>? documents;

        // Upload documents if required
        if (_documentsRequired) {
          setState(() {
            _isUploadingDocuments = true;
          });

          String? businessLicenseUrl;
          String? idProofUrl;
          String? addressProofUrl;

          // In debug mode, use placeholder URLs instead of actual uploads
          if (_debugSkipDocumentUploads) {
            debugPrint('DEBUG MODE: Using placeholder document URLs');
            businessLicenseUrl =
                'https://placeholder.com/business_license_document.jpg';
            idProofUrl = 'https://placeholder.com/id_proof_document.jpg';
            addressProofUrl =
                'https://placeholder.com/address_proof_document.jpg';
          } else {
            // Normal document upload process
            businessLicenseUrl = await _uploadDocument(
              _businessLicenseDocument!,
              'business_license',
            );
            idProofUrl = await _uploadDocument(_idProofDocument!, 'id_proof');
            addressProofUrl = await _uploadDocument(
              _addressProofDocument!,
              'address_proof',
            );

            // Check if all uploads were successful
            if (businessLicenseUrl == null ||
                idProofUrl == null ||
                addressProofUrl == null) {
              setState(() {
                _isLoading = false;
                _isUploadingDocuments = false;
              });
              return;
            }
          }

          // Create documents list
          documents = [
            {
              'type': 'Business License',
              'url': businessLicenseUrl,
              'uploadedAt': DateTime.now().toIso8601String(),
            },
            {
              'type': 'ID Proof',
              'url': idProofUrl,
              'uploadedAt': DateTime.now().toIso8601String(),
            },
            {
              'type': 'Address Proof',
              'url': addressProofUrl,
              'uploadedAt': DateTime.now().toIso8601String(),
            },
          ];

          setState(() {
            _isUploadingDocuments = false;
          });
        }

        final request = RegisterRequest(
          email: _emailController.text.trim(),
          password: _passwordController.text,
          displayName: _businessNameController.text.trim(),
          phoneNumber: _phoneController.text.trim(),
          role: UserRole.seller,
          metadata: {
            'category': _selectedCategory.name,
            'businessName': _businessNameController.text.trim(),
            if (_selectedCategory.isEventService &&
                _selectedServiceTypes.isNotEmpty)
              'serviceTypes': _selectedServiceTypes,
            if (documents != null) 'documents': documents,
          },
        );

        await ref.read(sellerProvider.notifier).register(request);

        if (mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Registration successful! Your account is pending approval.',
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );

          // Wait a moment for the snackbar to be visible
          await Future.delayed(const Duration(milliseconds: 1000));

          // Call the onRegisterSuccess callback if provided
          if (widget.onRegisterSuccess != null && mounted) {
            widget.onRegisterSuccess!();
          } else {
            // Try to navigate using GoRouter as a fallback
            if (mounted) {
              try {
                context.go(SellerRoutes.pendingApproval);
              } catch (e) {
                // Fallback if GoRouter fails
              }
            }
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(e.toString()), backgroundColor: Colors.red),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _isUploadingDocuments = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Register as Seller')),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const Text(
                    'Create Seller Account',
                    style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Join our marketplace as a seller',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 48),
                  AppTextField(
                    controller: _businessNameController,
                    label: 'Business Name',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your business name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  SellerCategorySelector(
                    selectedCategory: _selectedCategory,
                    onCategorySelected: (category) {
                      setState(() {
                        _selectedCategory = category;
                        // Clear service types when category changes
                        _selectedServiceTypes.clear();
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  // Service Types Selector (only for event services)
                  ServiceTypesSelector(
                    selectedCategory: _selectedCategory,
                    selectedServiceTypes: _selectedServiceTypes,
                    onServiceTypesChanged: (serviceTypes) {
                      setState(() {
                        _selectedServiceTypes = serviceTypes;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTextField(
                    controller: _emailController,
                    label: 'Email',
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your email';
                      }
                      if (!value.contains('@')) {
                        return 'Please enter a valid email';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTextField(
                    controller: _phoneController,
                    label: 'Phone Number',
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your phone number';
                      }
                      if (value.length < 10) {
                        return 'Please enter a valid phone number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTextField(
                    controller: _passwordController,
                    label: 'Password',
                    obscureText: !_isPasswordVisible,
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isPasswordVisible
                            ? Icons.visibility_off
                            : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your password';
                      }
                      if (value.length < 6) {
                        return 'Password must be at least 6 characters';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTextField(
                    controller: _confirmPasswordController,
                    label: 'Confirm Password',
                    obscureText: !_isConfirmPasswordVisible,
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isConfirmPasswordVisible
                            ? Icons.visibility_off
                            : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _isConfirmPasswordVisible =
                              !_isConfirmPasswordVisible;
                        });
                      },
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please confirm your password';
                      }
                      if (value != _passwordController.text) {
                        return 'Passwords do not match';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  // Document upload section
                  if (_isCheckingDocumentRequirements)
                    const Center(child: CircularProgressIndicator())
                  else if (_documentsRequired) ...[
                    const Divider(),
                    const SizedBox(height: 16),
                    const Text(
                      'Required Documents',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Please upload the following documents to complete your registration.',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                    if (_debugSkipDocumentUploads) ...[
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.amber[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.amber),
                        ),
                        child: const Row(
                          children: [
                            Icon(Icons.info_outline, color: Colors.amber),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Debug mode: Document uploads are optional. Placeholder URLs will be used if needed.',
                                style: TextStyle(
                                  color: Colors.amber,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    const SizedBox(height: 16),

                    // Business License document upload
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Business License',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        InkWell(
                          onTap: () => _pickDocument('business_license'),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.upload_file,
                                  color: Colors.green,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    _businessLicenseDocument != null
                                        ? _businessLicenseDocument!.path
                                              .split('/')
                                              .last
                                        : 'Upload Business License',
                                    style: TextStyle(
                                      color: _businessLicenseDocument != null
                                          ? Colors.black
                                          : Colors.grey[600],
                                    ),
                                  ),
                                ),
                                if (_businessLicenseDocument != null)
                                  const Icon(
                                    Icons.check_circle,
                                    color: Colors.green,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // ID Proof document upload
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'ID Proof',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        InkWell(
                          onTap: () => _pickDocument('id_proof'),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.upload_file,
                                  color: Colors.green,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    _idProofDocument != null
                                        ? _idProofDocument!.path.split('/').last
                                        : 'Upload ID Proof',
                                    style: TextStyle(
                                      color: _idProofDocument != null
                                          ? Colors.black
                                          : Colors.grey[600],
                                    ),
                                  ),
                                ),
                                if (_idProofDocument != null)
                                  const Icon(
                                    Icons.check_circle,
                                    color: Colors.green,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Address Proof document upload
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Address Proof',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        InkWell(
                          onTap: () => _pickDocument('address_proof'),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.upload_file,
                                  color: Colors.green,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    _addressProofDocument != null
                                        ? _addressProofDocument!.path
                                              .split('/')
                                              .last
                                        : 'Upload Address Proof',
                                    style: TextStyle(
                                      color: _addressProofDocument != null
                                          ? Colors.black
                                          : Colors.grey[600],
                                    ),
                                  ),
                                ),
                                if (_addressProofDocument != null)
                                  const Icon(
                                    Icons.check_circle,
                                    color: Colors.green,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                  ],

                  if (_isLoading || _isUploadingDocuments)
                    Column(
                      children: [
                        const LoadingIndicator(),
                        if (_isUploadingDocuments)
                          const Padding(
                            padding: EdgeInsets.only(top: 8.0),
                            child: Text(
                              'Uploading documents...',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                      ],
                    )
                  else
                    AppButton(
                      onPressed: _handleRegister,
                      child: const Text('Register'),
                    ),
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: () {
                      // Try to use GoRouter if available, otherwise just pop
                      try {
                        context.pop();
                      } catch (e) {
                        // Use Navigator directly
                        Navigator.of(context).pop();
                      }
                    },
                    child: const Text('Already have an account? Sign in'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
