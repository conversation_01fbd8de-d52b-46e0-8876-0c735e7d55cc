import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/data/repositories/order_repository_impl.dart';
import 'package:shivish/apps/seller/domain/use_cases/order_use_cases.dart';
import 'package:shivish/apps/seller/presentation/cubits/order_cubit.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

class OrderDI {
  static void init() {
    // Services
    final databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
    final authService = AuthService();

    // Repository
    final orderRepository = OrderRepositoryImpl(databaseService: databaseService);

    // Use Cases
    final getOrdersUseCase = GetOrdersUseCase(orderRepository);
    final getOrderByIdUseCase = GetOrderByIdUseCase(orderRepository);
    final updateOrderStatusUseCase = UpdateOrderStatusUseCase(orderRepository);
    final updatePaymentStatusUseCase =
        UpdatePaymentStatusUseCase(orderRepository);
    final updateShippingDetailsUseCase =
        UpdateShippingDetailsUseCase(orderRepository);
    final cancelOrderUseCase = CancelOrderUseCase(orderRepository);
    final processRefundUseCase = ProcessRefundUseCase(orderRepository);
    final addOrderNoteUseCase = AddOrderNoteUseCase(orderRepository);
    final watchOrdersUseCase = WatchOrdersUseCase(orderRepository);

    // Cubit
    final orderCubit = OrderCubit(
      getOrdersUseCase: getOrdersUseCase,
      getOrderByIdUseCase: getOrderByIdUseCase,
      updateOrderStatusUseCase: updateOrderStatusUseCase,
      updatePaymentStatusUseCase: updatePaymentStatusUseCase,
      updateShippingDetailsUseCase: updateShippingDetailsUseCase,
      cancelOrderUseCase: cancelOrderUseCase,
      processRefundUseCase: processRefundUseCase,
      addOrderNoteUseCase: addOrderNoteUseCase,
      watchOrdersUseCase: watchOrdersUseCase,
      authService: authService,
    );

    // Register Cubit
    BlocProvider<OrderCubit>.value(value: orderCubit);
  }
}
