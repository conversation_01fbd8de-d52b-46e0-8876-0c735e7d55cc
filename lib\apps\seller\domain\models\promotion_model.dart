import 'package:freezed_annotation/freezed_annotation.dart';

part 'promotion_model.freezed.dart';
part 'promotion_model.g.dart';

enum PromotionType {
  percentage,
  fixedAmount,
  buyOneGetOne,
  bundleDiscount,
}

@freezed
sealed class PromotionModel with _$PromotionModel {
  const factory PromotionModel({
    required String id,
    required String name,
    required String description,
    required PromotionType type,
    required double value,
    required DateTime startDate,
    required DateTime endDate,
    required List<String> productIds,
    @Default(false) bool isActive,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isDeleted,
  }) = _PromotionModel;

  factory PromotionModel.fromJson(Map<String, dynamic> json) =>
      _$PromotionModelFromJson(json);
}
