// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'medicine_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MedicineModel {

 String get id; String get name; String get description; String get hospitalId; MedicineType get type; MedicineCategory get category; String get manufacturer; String get composition; double get price; int get stockQuantity; String get dosageForm; String get strength; bool get requiresPrescription; bool get isAvailable; String? get imageUrl; String get batchNumber; DateTime get expiryDate; DateTime get createdAt; DateTime get updatedAt; bool get isDeleted; DateTime? get deletedAt;
/// Create a copy of MedicineModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MedicineModelCopyWith<MedicineModel> get copyWith => _$MedicineModelCopyWithImpl<MedicineModel>(this as MedicineModel, _$identity);

  /// Serializes this MedicineModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MedicineModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.hospitalId, hospitalId) || other.hospitalId == hospitalId)&&(identical(other.type, type) || other.type == type)&&(identical(other.category, category) || other.category == category)&&(identical(other.manufacturer, manufacturer) || other.manufacturer == manufacturer)&&(identical(other.composition, composition) || other.composition == composition)&&(identical(other.price, price) || other.price == price)&&(identical(other.stockQuantity, stockQuantity) || other.stockQuantity == stockQuantity)&&(identical(other.dosageForm, dosageForm) || other.dosageForm == dosageForm)&&(identical(other.strength, strength) || other.strength == strength)&&(identical(other.requiresPrescription, requiresPrescription) || other.requiresPrescription == requiresPrescription)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.batchNumber, batchNumber) || other.batchNumber == batchNumber)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,description,hospitalId,type,category,manufacturer,composition,price,stockQuantity,dosageForm,strength,requiresPrescription,isAvailable,imageUrl,batchNumber,expiryDate,createdAt,updatedAt,isDeleted,deletedAt]);

@override
String toString() {
  return 'MedicineModel(id: $id, name: $name, description: $description, hospitalId: $hospitalId, type: $type, category: $category, manufacturer: $manufacturer, composition: $composition, price: $price, stockQuantity: $stockQuantity, dosageForm: $dosageForm, strength: $strength, requiresPrescription: $requiresPrescription, isAvailable: $isAvailable, imageUrl: $imageUrl, batchNumber: $batchNumber, expiryDate: $expiryDate, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, deletedAt: $deletedAt)';
}


}

/// @nodoc
abstract mixin class $MedicineModelCopyWith<$Res>  {
  factory $MedicineModelCopyWith(MedicineModel value, $Res Function(MedicineModel) _then) = _$MedicineModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String description, String hospitalId, MedicineType type, MedicineCategory category, String manufacturer, String composition, double price, int stockQuantity, String dosageForm, String strength, bool requiresPrescription, bool isAvailable, String? imageUrl, String batchNumber, DateTime expiryDate, DateTime createdAt, DateTime updatedAt, bool isDeleted, DateTime? deletedAt
});




}
/// @nodoc
class _$MedicineModelCopyWithImpl<$Res>
    implements $MedicineModelCopyWith<$Res> {
  _$MedicineModelCopyWithImpl(this._self, this._then);

  final MedicineModel _self;
  final $Res Function(MedicineModel) _then;

/// Create a copy of MedicineModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? description = null,Object? hospitalId = null,Object? type = null,Object? category = null,Object? manufacturer = null,Object? composition = null,Object? price = null,Object? stockQuantity = null,Object? dosageForm = null,Object? strength = null,Object? requiresPrescription = null,Object? isAvailable = null,Object? imageUrl = freezed,Object? batchNumber = null,Object? expiryDate = null,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,Object? deletedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,hospitalId: null == hospitalId ? _self.hospitalId : hospitalId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as MedicineType,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as MedicineCategory,manufacturer: null == manufacturer ? _self.manufacturer : manufacturer // ignore: cast_nullable_to_non_nullable
as String,composition: null == composition ? _self.composition : composition // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,stockQuantity: null == stockQuantity ? _self.stockQuantity : stockQuantity // ignore: cast_nullable_to_non_nullable
as int,dosageForm: null == dosageForm ? _self.dosageForm : dosageForm // ignore: cast_nullable_to_non_nullable
as String,strength: null == strength ? _self.strength : strength // ignore: cast_nullable_to_non_nullable
as String,requiresPrescription: null == requiresPrescription ? _self.requiresPrescription : requiresPrescription // ignore: cast_nullable_to_non_nullable
as bool,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,batchNumber: null == batchNumber ? _self.batchNumber : batchNumber // ignore: cast_nullable_to_non_nullable
as String,expiryDate: null == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as DateTime,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [MedicineModel].
extension MedicineModelPatterns on MedicineModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MedicineModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MedicineModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MedicineModel value)  $default,){
final _that = this;
switch (_that) {
case _MedicineModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MedicineModel value)?  $default,){
final _that = this;
switch (_that) {
case _MedicineModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String description,  String hospitalId,  MedicineType type,  MedicineCategory category,  String manufacturer,  String composition,  double price,  int stockQuantity,  String dosageForm,  String strength,  bool requiresPrescription,  bool isAvailable,  String? imageUrl,  String batchNumber,  DateTime expiryDate,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  DateTime? deletedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MedicineModel() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.hospitalId,_that.type,_that.category,_that.manufacturer,_that.composition,_that.price,_that.stockQuantity,_that.dosageForm,_that.strength,_that.requiresPrescription,_that.isAvailable,_that.imageUrl,_that.batchNumber,_that.expiryDate,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.deletedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String description,  String hospitalId,  MedicineType type,  MedicineCategory category,  String manufacturer,  String composition,  double price,  int stockQuantity,  String dosageForm,  String strength,  bool requiresPrescription,  bool isAvailable,  String? imageUrl,  String batchNumber,  DateTime expiryDate,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  DateTime? deletedAt)  $default,) {final _that = this;
switch (_that) {
case _MedicineModel():
return $default(_that.id,_that.name,_that.description,_that.hospitalId,_that.type,_that.category,_that.manufacturer,_that.composition,_that.price,_that.stockQuantity,_that.dosageForm,_that.strength,_that.requiresPrescription,_that.isAvailable,_that.imageUrl,_that.batchNumber,_that.expiryDate,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.deletedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String description,  String hospitalId,  MedicineType type,  MedicineCategory category,  String manufacturer,  String composition,  double price,  int stockQuantity,  String dosageForm,  String strength,  bool requiresPrescription,  bool isAvailable,  String? imageUrl,  String batchNumber,  DateTime expiryDate,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  DateTime? deletedAt)?  $default,) {final _that = this;
switch (_that) {
case _MedicineModel() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.hospitalId,_that.type,_that.category,_that.manufacturer,_that.composition,_that.price,_that.stockQuantity,_that.dosageForm,_that.strength,_that.requiresPrescription,_that.isAvailable,_that.imageUrl,_that.batchNumber,_that.expiryDate,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.deletedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MedicineModel implements MedicineModel {
  const _MedicineModel({required this.id, required this.name, required this.description, required this.hospitalId, required this.type, required this.category, required this.manufacturer, required this.composition, required this.price, required this.stockQuantity, required this.dosageForm, required this.strength, required this.requiresPrescription, required this.isAvailable, this.imageUrl, required this.batchNumber, required this.expiryDate, required this.createdAt, required this.updatedAt, this.isDeleted = false, this.deletedAt});
  factory _MedicineModel.fromJson(Map<String, dynamic> json) => _$MedicineModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String description;
@override final  String hospitalId;
@override final  MedicineType type;
@override final  MedicineCategory category;
@override final  String manufacturer;
@override final  String composition;
@override final  double price;
@override final  int stockQuantity;
@override final  String dosageForm;
@override final  String strength;
@override final  bool requiresPrescription;
@override final  bool isAvailable;
@override final  String? imageUrl;
@override final  String batchNumber;
@override final  DateTime expiryDate;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool isDeleted;
@override final  DateTime? deletedAt;

/// Create a copy of MedicineModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MedicineModelCopyWith<_MedicineModel> get copyWith => __$MedicineModelCopyWithImpl<_MedicineModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MedicineModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MedicineModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.hospitalId, hospitalId) || other.hospitalId == hospitalId)&&(identical(other.type, type) || other.type == type)&&(identical(other.category, category) || other.category == category)&&(identical(other.manufacturer, manufacturer) || other.manufacturer == manufacturer)&&(identical(other.composition, composition) || other.composition == composition)&&(identical(other.price, price) || other.price == price)&&(identical(other.stockQuantity, stockQuantity) || other.stockQuantity == stockQuantity)&&(identical(other.dosageForm, dosageForm) || other.dosageForm == dosageForm)&&(identical(other.strength, strength) || other.strength == strength)&&(identical(other.requiresPrescription, requiresPrescription) || other.requiresPrescription == requiresPrescription)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.batchNumber, batchNumber) || other.batchNumber == batchNumber)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,description,hospitalId,type,category,manufacturer,composition,price,stockQuantity,dosageForm,strength,requiresPrescription,isAvailable,imageUrl,batchNumber,expiryDate,createdAt,updatedAt,isDeleted,deletedAt]);

@override
String toString() {
  return 'MedicineModel(id: $id, name: $name, description: $description, hospitalId: $hospitalId, type: $type, category: $category, manufacturer: $manufacturer, composition: $composition, price: $price, stockQuantity: $stockQuantity, dosageForm: $dosageForm, strength: $strength, requiresPrescription: $requiresPrescription, isAvailable: $isAvailable, imageUrl: $imageUrl, batchNumber: $batchNumber, expiryDate: $expiryDate, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, deletedAt: $deletedAt)';
}


}

/// @nodoc
abstract mixin class _$MedicineModelCopyWith<$Res> implements $MedicineModelCopyWith<$Res> {
  factory _$MedicineModelCopyWith(_MedicineModel value, $Res Function(_MedicineModel) _then) = __$MedicineModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String description, String hospitalId, MedicineType type, MedicineCategory category, String manufacturer, String composition, double price, int stockQuantity, String dosageForm, String strength, bool requiresPrescription, bool isAvailable, String? imageUrl, String batchNumber, DateTime expiryDate, DateTime createdAt, DateTime updatedAt, bool isDeleted, DateTime? deletedAt
});




}
/// @nodoc
class __$MedicineModelCopyWithImpl<$Res>
    implements _$MedicineModelCopyWith<$Res> {
  __$MedicineModelCopyWithImpl(this._self, this._then);

  final _MedicineModel _self;
  final $Res Function(_MedicineModel) _then;

/// Create a copy of MedicineModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? description = null,Object? hospitalId = null,Object? type = null,Object? category = null,Object? manufacturer = null,Object? composition = null,Object? price = null,Object? stockQuantity = null,Object? dosageForm = null,Object? strength = null,Object? requiresPrescription = null,Object? isAvailable = null,Object? imageUrl = freezed,Object? batchNumber = null,Object? expiryDate = null,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,Object? deletedAt = freezed,}) {
  return _then(_MedicineModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,hospitalId: null == hospitalId ? _self.hospitalId : hospitalId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as MedicineType,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as MedicineCategory,manufacturer: null == manufacturer ? _self.manufacturer : manufacturer // ignore: cast_nullable_to_non_nullable
as String,composition: null == composition ? _self.composition : composition // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,stockQuantity: null == stockQuantity ? _self.stockQuantity : stockQuantity // ignore: cast_nullable_to_non_nullable
as int,dosageForm: null == dosageForm ? _self.dosageForm : dosageForm // ignore: cast_nullable_to_non_nullable
as String,strength: null == strength ? _self.strength : strength // ignore: cast_nullable_to_non_nullable
as String,requiresPrescription: null == requiresPrescription ? _self.requiresPrescription : requiresPrescription // ignore: cast_nullable_to_non_nullable
as bool,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,batchNumber: null == batchNumber ? _self.batchNumber : batchNumber // ignore: cast_nullable_to_non_nullable
as String,expiryDate: null == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as DateTime,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
