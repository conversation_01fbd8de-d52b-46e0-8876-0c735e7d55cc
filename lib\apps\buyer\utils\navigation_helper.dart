import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../buyer_routes.dart';

/// Helper class for navigation to avoid stack overflow issues
class NavigationHelper {
  /// Global navigator key for accessing the navigator from anywhere
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  /// Navigate to a route and clear all previous routes
  /// This prevents navigation stack issues and potential stack overflows
  static void navigateAndClearStack(BuildContext context, String route) {
    // Use go_router for navigation as it's the primary navigation system in the app
    try {
      context.go(route);
    } catch (e) {
      // If go_router fails, fall back to Navigator
      try {
        Navigator.of(context).pushNamedAndRemoveUntil(
          route,
          (r) => false, // Remove all previous routes
        );
      } catch (e) {
        // Last resort: navigate to home
        context.go(BuyerRoutes.home);
      }
    }
  }

  /// Navigate to a route and maintain the ability to go back
  static void navigateTo(BuildContext context, String route, {Object? extra}) {
    try {
      if (extra != null) {
        context.push(route, extra: extra);
      } else {
        context.push(route);
      }
    } catch (e) {
      // If push fails, try go as a fallback
      try {
        if (extra != null) {
          context.go(route, extra: extra);
        } else {
          context.go(route);
        }
      } catch (e) {
        // Last resort: show error dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Navigation Error'),
            content: Text('Failed to navigate to $route'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
  }

  /// Navigate back to the previous screen
  static void goBack(BuildContext context) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    } else {
      // If we can't pop, try to go to home
      context.go(BuyerRoutes.home);
    }
  }
}
