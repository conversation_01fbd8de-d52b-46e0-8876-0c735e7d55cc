import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/infrastructure_provider.dart';
import '../../models/server_config.dart';
import '../../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../../shared/ui_components/errors/error_message.dart';


/// Screen for configuring server settings in real-time
class ServerConfigurationScreen extends ConsumerStatefulWidget {
  const ServerConfigurationScreen({super.key});

  @override
  ConsumerState<ServerConfigurationScreen> createState() => _ServerConfigurationScreenState();
}

class _ServerConfigurationScreenState extends ConsumerState<ServerConfigurationScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isUpdating = false;
  String _selectedEnvironment = 'production';

  // Controllers for form fields
  final _awsRegionController = TextEditingController();
  final _datacenterIpController = TextEditingController();
  final _databaseUrlController = TextEditingController();
  final _redisUrlController = TextEditingController();
  final _maxConnectionsController = TextEditingController();
  final _timeoutController = TextEditingController();

  @override
  void dispose() {
    _awsRegionController.dispose();
    _datacenterIpController.dispose();
    _databaseUrlController.dispose();
    _redisUrlController.dispose();
    _maxConnectionsController.dispose();
    _timeoutController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final serverConfigs = ref.watch(serverConfigsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Server Configuration'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isUpdating ? null : _saveConfiguration,
          ),
        ],
      ),
      body: serverConfigs.when(
        data: (configs) => _buildContent(configs),
        loading: () => const LoadingIndicator(),
        error: (error, stack) => ErrorMessage(
          message: error.toString(),
          onRetry: () => ref.refresh(serverConfigsProvider),
        ),
      ),
    );
  }

  Widget _buildContent(Map<String, ServerConfig> configs) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildEnvironmentSelector(configs),
            const SizedBox(height: 24),
            _buildServerSettings(configs[_selectedEnvironment]),
            const SizedBox(height: 24),
            _buildDatabaseSettings(configs[_selectedEnvironment]),
            const SizedBox(height: 24),
            _buildPerformanceSettings(configs[_selectedEnvironment]),
            const SizedBox(height: 24),
            _buildSecuritySettings(configs[_selectedEnvironment]),
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildEnvironmentSelector(Map<String, ServerConfig> configs) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Environment',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedEnvironment,
              decoration: const InputDecoration(
                labelText: 'Select Environment',
                border: OutlineInputBorder(),
              ),
              items: configs.keys.map((env) => DropdownMenuItem(
                value: env,
                child: Text(env.toUpperCase()),
              )).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedEnvironment = value);
                  _loadConfigurationValues(configs[value]!);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServerSettings(ServerConfig? config) {
    if (config == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Server Settings',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _awsRegionController,
              decoration: const InputDecoration(
                labelText: 'AWS Region',
                hintText: 'us-east-1',
                border: OutlineInputBorder(),
              ),
              validator: (value) => value?.isEmpty == true ? 'AWS Region is required' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _datacenterIpController,
              decoration: const InputDecoration(
                labelText: 'Datacenter IP',
                hintText: '*************',
                border: OutlineInputBorder(),
              ),
              validator: (value) => value?.isEmpty == true ? 'Datacenter IP is required' : null,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildServerStatusCard('AWS Status', config.awsStatus),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildServerStatusCard('Datacenter Status', config.datacenterStatus),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServerStatusCard(String title, ServerState status) {
    Color color;
    IconData icon;

    switch (status) {
      case ServerState.online:
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case ServerState.offline:
        color = Colors.red;
        icon = Icons.error;
        break;
      case ServerState.maintenance:
        color = Colors.orange;
        icon = Icons.build;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(status.name.toUpperCase(), style: TextStyle(color: color, fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildDatabaseSettings(ServerConfig? config) {
    if (config == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Database Settings',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _databaseUrlController,
              decoration: const InputDecoration(
                labelText: 'Database URL',
                hintText: '********************************/db',
                border: OutlineInputBorder(),
              ),
              validator: (value) => value?.isEmpty == true ? 'Database URL is required' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _redisUrlController,
              decoration: const InputDecoration(
                labelText: 'Redis URL',
                hintText: 'redis://localhost:6379',
                border: OutlineInputBorder(),
              ),
              validator: (value) => value?.isEmpty == true ? 'Redis URL is required' : null,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _maxConnectionsController,
                    decoration: const InputDecoration(
                      labelText: 'Max Connections',
                      hintText: '100',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value?.isEmpty == true) return 'Max connections is required';
                      if (int.tryParse(value!) == null) return 'Must be a number';
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _timeoutController,
                    decoration: const InputDecoration(
                      labelText: 'Timeout (seconds)',
                      hintText: '30',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value?.isEmpty == true) return 'Timeout is required';
                      if (int.tryParse(value!) == null) return 'Must be a number';
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceSettings(ServerConfig? config) {
    if (config == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Performance Settings',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Auto Scaling'),
              subtitle: const Text('Automatically scale servers based on load'),
              value: config.autoScaling,
              onChanged: (value) {
                // Update config
              },
            ),
            SwitchListTile(
              title: const Text('Load Balancing'),
              subtitle: const Text('Distribute traffic across multiple servers'),
              value: config.loadBalancing,
              onChanged: (value) {
                // Update config
              },
            ),
            SwitchListTile(
              title: const Text('Caching'),
              subtitle: const Text('Enable Redis caching for better performance'),
              value: config.cachingEnabled,
              onChanged: (value) {
                // Update config
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecuritySettings(ServerConfig? config) {
    if (config == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Security Settings',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('SSL/TLS'),
              subtitle: const Text('Enable HTTPS encryption'),
              value: config.sslEnabled,
              onChanged: (value) {
                // Update config
              },
            ),
            SwitchListTile(
              title: const Text('Rate Limiting'),
              subtitle: const Text('Limit requests per IP address'),
              value: config.rateLimitingEnabled,
              onChanged: (value) {
                // Update config
              },
            ),
            SwitchListTile(
              title: const Text('DDoS Protection'),
              subtitle: const Text('Enable DDoS protection'),
              value: config.ddosProtection,
              onChanged: (value) {
                // Update config
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isUpdating ? null : _saveConfiguration,
            child: _isUpdating
                ? const SizedBox(
                    height: 16,
                    width: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save Configuration'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: OutlinedButton(
            onPressed: _testConfiguration,
            child: const Text('Test Configuration'),
          ),
        ),
      ],
    );
  }

  void _loadConfigurationValues(ServerConfig config) {
    _awsRegionController.text = config.awsRegion;
    _datacenterIpController.text = config.datacenterIp;
    _databaseUrlController.text = config.databaseUrl;
    _redisUrlController.text = config.redisUrl;
    _maxConnectionsController.text = config.maxConnections.toString();
    _timeoutController.text = config.timeout.toString();
  }

  Future<void> _saveConfiguration() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isUpdating = true);

    try {
      final config = ServerConfig(
        environment: _selectedEnvironment,
        awsRegion: _awsRegionController.text,
        datacenterIp: _datacenterIpController.text,
        databaseUrl: _databaseUrlController.text,
        redisUrl: _redisUrlController.text,
        maxConnections: int.parse(_maxConnectionsController.text),
        timeout: int.parse(_timeoutController.text),
        awsStatus: ServerState.online, // This would be determined by health checks
        datacenterStatus: ServerState.online,
        autoScaling: true,
        loadBalancing: true,
        cachingEnabled: true,
        sslEnabled: true,
        rateLimitingEnabled: true,
        ddosProtection: true,
        lastUpdated: DateTime.now(),
      );

      await ref.read(infrastructureServiceProvider).updateServerConfig(config);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Server configuration updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating configuration: $e')),
        );
      }
    } finally {
      setState(() => _isUpdating = false);
    }
  }

  Future<void> _testConfiguration() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final result = await ref.read(infrastructureServiceProvider).testServerConfiguration(
        _selectedEnvironment,
        _datacenterIpController.text,
        _databaseUrlController.text,
      );

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Configuration Test'),
            content: Text(result.success 
                ? 'Configuration test passed successfully!' 
                : 'Configuration test failed: ${result.error}'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error testing configuration: $e')),
        );
      }
    }
  }
}
