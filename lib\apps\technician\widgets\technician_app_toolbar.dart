import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import '../technician_routes.dart';

/// A custom AppToolbar for the Technician app that always shows a back button
/// regardless of whether the screen can be popped.
class TechnicianAppToolbar extends StatelessWidget implements PreferredSizeWidget {
  const TechnicianAppToolbar({
    super.key,
    required this.title,
    this.actions,
    this.bottom,
    this.elevation,
    this.backgroundColor,
    this.centerTitle = true,
    this.fallbackRoute,
    this.onBackPressed,
  });

  final String title;
  final List<Widget>? actions;
  final PreferredSizeWidget? bottom;
  final double? elevation;
  final Color? backgroundColor;
  final bool centerTitle;
  final String? fallbackRoute;
  final VoidCallback? onBackPressed;

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0.0),
      );

  @override
  Widget build(BuildContext context) {
    return AppToolbar(
      title: title,
      actions: actions,
      bottom: bottom,
      elevation: elevation,
      backgroundColor: backgroundColor,
      centerTitle: centerTitle,
      // Always show back button
      showBackButton: true,
      // Custom back button that works even if the screen can't be popped
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () {
          if (onBackPressed != null) {
            onBackPressed!();
          } else {
            // Try to pop first
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              // If we can't pop, try to use GoRouter to navigate back
              try {
                context.pop();
              } catch (e) {
                // If that fails too, navigate to the fallback route or home
                if (fallbackRoute != null) {
                  context.go(fallbackRoute!);
                } else {
                  // Default to home route
                  context.go(TechnicianRoutes.home);
                }
              }
            }
          }
        },
      ),
    );
  }

  // Factory methods for convenience
  factory TechnicianAppToolbar.simple({
    required String title,
    List<Widget>? actions,
    double? elevation,
    Color? backgroundColor,
    bool centerTitle = true,
    String? fallbackRoute,
    VoidCallback? onBackPressed,
  }) {
    return TechnicianAppToolbar(
      title: title,
      actions: actions,
      elevation: elevation,
      backgroundColor: backgroundColor,
      centerTitle: centerTitle,
      fallbackRoute: fallbackRoute,
      onBackPressed: onBackPressed,
    );
  }

  factory TechnicianAppToolbar.withBottom({
    required String title,
    required PreferredSizeWidget bottom,
    List<Widget>? actions,
    double? elevation,
    Color? backgroundColor,
    bool centerTitle = true,
    String? fallbackRoute,
    VoidCallback? onBackPressed,
  }) {
    return TechnicianAppToolbar(
      title: title,
      actions: actions,
      bottom: bottom,
      elevation: elevation,
      backgroundColor: backgroundColor,
      centerTitle: centerTitle,
      fallbackRoute: fallbackRoute,
      onBackPressed: onBackPressed,
    );
  }
}
