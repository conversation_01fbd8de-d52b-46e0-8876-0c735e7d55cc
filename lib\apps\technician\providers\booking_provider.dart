import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/models/booking/booking_model.dart';
import 'package:shivish/apps/technician/services/booking_service.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final technicianBookingsProvider =
    StreamProvider.family<List<BookingModel>, String>(
  (ref, technicianId) {
    final bookingService = ref.watch(bookingServiceProvider);
    return bookingService.streamTechnicianBookings(technicianId);
  },
);

final technicianBookingsByStatusProvider = StreamProvider.family<
    List<BookingModel>, ({String technicianId, BookingStatus status})>(
  (ref, params) {
    final bookingService = ref.watch(bookingServiceProvider);
    return bookingService.streamTechnicianBookingsByStatus(
        params.technicianId, params.status);
  },
);

final technicianBookingCountProvider = FutureProvider.family<int, String>(
  (ref, technicianId) {
    final bookingService = ref.watch(bookingServiceProvider);
    return bookingService.getTechnicianBookingCount(technicianId);
  },
);

final technicianBookingCountsByStatusProvider =
    FutureProvider.family<Map<BookingStatus, int>, String>(
  (ref, technicianId) {
    final bookingService = ref.watch(bookingServiceProvider);
    return bookingService.getTechnicianBookingCountsByStatus(technicianId);
  },
);

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final bookingProvider =
    FutureProvider.family<BookingModel?, String>((ref, id) async {
  final databaseService = ref.watch(databaseServiceProvider);

  try {
    final data = await databaseService.find('bookings', id);
    if (data != null) {
      return BookingModel.fromJson(data);
    }
    return null;
  } catch (e) {
    debugPrint('Failed to load booking: $e');
    throw Exception('Failed to load booking: $e');
  }
});

final bookingStatusProvider = StateNotifierProvider.family<
    BookingStatusNotifier, AsyncValue<void>, String>((ref, id) {
  return BookingStatusNotifier(ref, id);
});

class BookingStatusNotifier extends StateNotifier<AsyncValue<void>> {
  final Ref _ref;
  final String _bookingId;

  BookingStatusNotifier(this._ref, this._bookingId)
      : super(const AsyncValue.data(null));

  Future<void> updateBookingStatus(BookingStatus status) async {
    state = const AsyncValue.loading();

    try {
      final databaseService = _ref.read(databaseServiceProvider);
      await databaseService.update(
        'bookings',
        _bookingId,
        {
          'status': status.index,
          'updated_at': DateTime.now().toIso8601String(),
        },
      );
      state = const AsyncValue.data(null);
    } catch (e, stack) {
      debugPrint('Failed to update booking status: $e');
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }
}
