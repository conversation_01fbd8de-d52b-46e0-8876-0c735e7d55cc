import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:shivish/shared/models/hospital/doctor_model.dart';
import 'package:shivish/shared/models/hospital/doctor_schedule_model.dart';
import 'package:shivish/shared/models/hospital/appointment_model.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/providers/hospital/hospital_provider.dart';
import 'package:shivish/shared/providers/hospital/appointment_provider.dart';
import 'package:shivish/shared/providers/auth_provider.dart';

class BookAppointmentScreen extends ConsumerStatefulWidget {
  final String doctorId;

  const BookAppointmentScreen({
    super.key,
    required this.doctorId,
  });

  @override
  ConsumerState<BookAppointmentScreen> createState() => _BookAppointmentScreenState();
}

class _BookAppointmentScreenState extends ConsumerState<BookAppointmentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _symptomsController = TextEditingController();

  late DoctorModel _doctor;
  late List<TimeSlot> _availableSlots = [];

  DateTime _selectedDate = DateTime.now();
  TimeSlot? _selectedTimeSlot;
  AppointmentType _appointmentType = AppointmentType.consultation;

  bool _isLoading = true;
  bool _isBooking = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _symptomsController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Load doctor data from provider
      final doctorAsync = await ref.read(doctorProvider(widget.doctorId).future);
      _doctor = doctorAsync!;
      await _loadAvailableSlots();
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load doctor details: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAvailableSlots() async {
    try {
      // Load available slots from provider
      final params = AvailableTimeSlotsParams(
        doctorId: widget.doctorId,
        date: _selectedDate,
      );
      final slots = await ref.read(
        availableTimeSlotsProvider(params).future,
      );
      setState(() {
        _availableSlots = slots;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load available slots: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Book Appointment'),
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _errorMessage != null
              ? ErrorMessage(
                  message: _errorMessage!,
                  onRetry: _loadData,
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Doctor info card
                        _buildDoctorInfoCard(),
                        const SizedBox(height: 24),

                        // Date selection
                        const Text(
                          'Select Date',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildCalendar(),
                        const SizedBox(height: 24),

                        // Time slot selection
                        const Text(
                          'Select Time Slot',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildTimeSlots(),
                        const SizedBox(height: 24),

                        // Appointment type
                        const Text(
                          'Appointment Type',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildAppointmentTypeSelection(),
                        const SizedBox(height: 24),

                        // Symptoms
                        const Text(
                          'Symptoms',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _symptomsController,
                          decoration: InputDecoration(
                            hintText: 'Describe your symptoms',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            contentPadding: const EdgeInsets.all(16),
                          ),
                          maxLines: 4,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please describe your symptoms';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 32),

                        // Booking button
                        AppButton(
                          label: 'Book Appointment',
                          onPressed: _isBooking ? null : _bookAppointment,
                          isLoading: _isBooking,
                          isDisabled: _selectedTimeSlot == null,
                        ),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildDoctorInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Doctor image
            CircleAvatar(
              radius: 30,
              backgroundImage: _doctor.profileImage.isNotEmpty
                  ? NetworkImage(_doctor.profileImage)
                  : null,
              child: _doctor.profileImage.isEmpty
                  ? const Icon(Icons.person, size: 30)
                  : null,
            ),
            const SizedBox(width: 16),

            // Doctor details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Dr. ${_doctor.name}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _doctor.designation,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        size: 16,
                        color: Colors.amber[700],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${_doctor.rating} (${_doctor.totalReviews})',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.currency_rupee,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '₹${_doctor.consultationFee.toStringAsFixed(0)} per session',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendar() {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: TableCalendar(
          firstDay: DateTime.now(),
          lastDay: DateTime.now().add(const Duration(days: 30)),
          focusedDay: _selectedDate,
          selectedDayPredicate: (day) {
            return isSameDay(_selectedDate, day);
          },
          onDaySelected: (selectedDay, focusedDay) {
            setState(() {
              _selectedDate = selectedDay;
              _selectedTimeSlot = null;
              _loadAvailableSlots();
            });
          },
          calendarStyle: const CalendarStyle(
            todayDecoration: BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
            selectedDecoration: BoxDecoration(
              color: Colors.deepPurple,
              shape: BoxShape.circle,
            ),
          ),
          headerStyle: const HeaderStyle(
            formatButtonVisible: false,
            titleCentered: true,
          ),
        ),
      ),
    );
  }

  Widget _buildTimeSlots() {
    if (_availableSlots.isEmpty) {
      return Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Center(
            child: Column(
              children: [
                Icon(
                  Icons.event_busy,
                  size: 48,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No available slots for ${DateFormat('MMMM d, yyyy').format(_selectedDate)}',
                  style: TextStyle(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'Please select another date',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _availableSlots.map((slot) {
        final isSelected = _selectedTimeSlot?.id == slot.id;
        return ChoiceChip(
          label: Text('${slot.startTime} - ${slot.endTime}'),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _selectedTimeSlot = selected ? slot : null;
            });
          },
          backgroundColor: Colors.grey.shade100,
          selectedColor: Colors.blue.shade100,
          labelStyle: TextStyle(
            color: isSelected ? Colors.blue.shade700 : Colors.black,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAppointmentTypeSelection() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildAppointmentTypeChip(
          AppointmentType.consultation,
          'Consultation',
          Icons.medical_services,
        ),
        _buildAppointmentTypeChip(
          AppointmentType.followUp,
          'Follow-up',
          Icons.repeat,
        ),
        _buildAppointmentTypeChip(
          AppointmentType.routineCheckup,
          'Routine Checkup',
          Icons.health_and_safety,
        ),
      ],
    );
  }

  Widget _buildAppointmentTypeChip(
    AppointmentType type,
    String label,
    IconData icon,
  ) {
    final isSelected = _appointmentType == type;
    return ChoiceChip(
      avatar: Icon(
        icon,
        size: 16,
        color: isSelected ? Colors.blue.shade700 : Colors.grey,
      ),
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _appointmentType = type;
        });
      },
      backgroundColor: Colors.grey.shade100,
      selectedColor: Colors.blue.shade100,
      labelStyle: TextStyle(
        color: isSelected ? Colors.blue.shade700 : Colors.black,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  Future<void> _bookAppointment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedTimeSlot == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a time slot'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isBooking = true;
      _errorMessage = null;
    });

    try {
      // Create appointment model
      final user = ref.read(authProvider);
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final appointmentDate = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
      );

      // Generate appointment number
      final appointmentNumber = 'APT-${DateTime.now().millisecondsSinceEpoch}';

      // Create appointment model with all required fields
      final appointment = AppointmentModel(
        id: '', // Will be set by hybrid database
        appointmentNumber: appointmentNumber,
        patientId: user.id,
        patientName: user.userMetadata?['display_name'] ??
                     user.userMetadata?['full_name'] ?? 'Patient',
        patientPhone: user.phone ?? '',
        patientEmail: user.email,
        hospitalId: _doctor.hospitalId,
        hospitalName: 'Hospital', // Ideally this would come from a hospital provider
        doctorId: _doctor.id,
        doctorName: _doctor.name,
        departmentId: _doctor.departmentId,
        departmentName: 'Department', // Ideally this would come from a department provider
        appointmentDate: appointmentDate,
        timeSlotId: _selectedTimeSlot!.id,
        startTime: _selectedTimeSlot!.startTime,
        endTime: _selectedTimeSlot!.endTime,
        symptoms: _symptomsController.text,
        type: _appointmentType,
        status: AppointmentStatus.pending,
        consultationFee: _doctor.consultationFee,
        isPaid: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Book appointment using provider
      final appointmentService = ref.read(appointmentServiceProvider);
      await appointmentService.bookAppointment(appointment);

      if (mounted) {
        // Show success dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('Appointment Booked'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 64,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Your appointment has been booked successfully!',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'Date: ${DateFormat('MMMM d, yyyy').format(_selectedDate)}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  'Time: ${_selectedTimeSlot!.startTime} - ${_selectedTimeSlot!.endTime}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  'Doctor: Dr. ${_doctor.name}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // Close dialog
                  context.go('/buyer/healthcare/appointments'); // Navigate to appointments
                },
                child: const Text('View My Appointments'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to book appointment: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isBooking = false;
      });
    }
  }


}
