import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/presentation/cubits/notifications_cubit.dart';
import 'package:shivish/shared/services/notification/notification_service.dart';
import 'package:shivish/shared/providers/auth_provider.dart';

// Provider for NotificationService
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

// Provider for NotificationsCubit
final notificationsCubitProvider = Provider<NotificationsCubit>((ref) {
  final notificationService = ref.watch(notificationServiceProvider);
  final userId = ref.watch(userIdProvider);
  return NotificationsCubit(notificationService, userId);
});
