import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart' as provider;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/shared/screens/language_selection_screen.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';
import 'services/technician_agreement_service.dart';
import 'services/technician_confirmation_service.dart';

// Auth Screens
import 'screens/auth/login_screen.dart';
import 'screens/auth/register_screen.dart';
import 'screens/auth/pending_approval_screen.dart';
import 'screens/agreement_screen.dart';

// Main Screens
import 'screens/home/<USER>';
import 'screens/notifications/notification_screen.dart';

// Profile Screens
import 'screens/profile/profile_settings_screen.dart';

// Booking Screens
import 'screens/bookings/booking_list_screen.dart';
import 'screens/bookings/booking_detail_screen.dart';
import 'screens/bookings/booking_calendar_screen.dart';

// Payment Screens
import 'screens/payments/payment_screen.dart';

// Settings Screens
import 'screens/settings/settings_screen.dart';
import 'screens/settings/bank_account_settings_screen.dart';
import 'screens/settings/invoice_settings_screen.dart';
import 'screens/settings/tax_settings_screen.dart';
import 'screens/settings/profile_picture_settings_screen.dart';
import 'screens/settings/service_areas_settings_screen.dart';
import 'screens/settings/booking_reminder_settings_screen.dart';
import 'screens/settings/payment_alert_settings_screen.dart';
import 'screens/settings/working_hours_settings_screen.dart';
import 'screens/settings/holiday_settings_screen.dart';
import 'screens/settings/break_time_settings_screen.dart';
import 'screens/settings/services_settings_screen.dart';
import 'screens/settings/pricing_settings_screen.dart';
import 'screens/settings/password_settings_screen.dart';
import 'screens/settings/two_factor_settings_screen.dart';
import 'screens/settings/device_management_screen.dart';
import 'screens/settings/language_settings_screen.dart';
import 'screens/settings/theme_settings_screen.dart';
import 'screens/settings/about_screen.dart';

class TechnicianRoutes {
  static const String initialLocation = '/technician/language-selection';

  // Auth Routes
  static const String login = '/technician/login';
  static const String register = '/technician/register';
  static const String languageSelection = '/technician/language-selection';
  static const String agreement = '/technician/agreement';
  static const String pendingApproval = '/technician/pending-approval';

  // Main Routes
  static const String home = '/technician/home';
  static const String notifications = '/technician/notifications';

  // Profile Routes
  static const String profileSettings = '/technician/profile/settings';

  // Booking Routes
  static const String bookings = '/technician/bookings';
  static const String bookingDetails = '/technician/bookings/:id';
  static const String bookingCalendar = '/technician/bookings/calendar';

  // Payment Routes
  static const String payments = '/technician/payments';

  // Settings Routes
  static const String settings = '/technician/settings';
  static const String bankAccount = '/technician/settings/bank-account';
  static const String invoice = '/technician/settings/invoice';
  static const String tax = '/technician/settings/tax';
  static const String profilePicture = '/technician/settings/profile-picture';
  static const String serviceAreas = '/technician/settings/service-areas';
  static const String bookingReminders =
      '/technician/settings/booking-reminders';
  static const String paymentAlerts = '/technician/settings/payment-alerts';
  static const String workingHours = '/technician/settings/working-hours';
  static const String holidays = '/technician/settings/holidays';
  static const String breakTime = '/technician/settings/break-time';
  static const String services = '/technician/settings/services';
  static const String pricing = '/technician/settings/pricing';
  static const String password = '/technician/settings/password';
  static const String twoFactor = '/technician/settings/two-factor';
  static const String devices = '/technician/settings/devices';
  static const String language = '/technician/settings/language';
  static const String theme = '/technician/settings/theme';
  static const String about = '/technician/settings/about';

  static final router = GoRouter(
    initialLocation: initialLocation,
    redirect: (context, state) async {
      debugPrint(
          'TechnicianRoutes: Handling redirect for ${state.matchedLocation}');

      // Don't redirect if we're on the language selection screen
      if (state.matchedLocation == languageSelection) {
        debugPrint(
            'TechnicianRoutes: On language selection screen, no redirect needed');
        return null;
      }

      // Don't redirect if we're on the agreement screen
      if (state.matchedLocation == agreement) {
        debugPrint(
            'TechnicianRoutes: On agreement screen, no redirect needed');
        return null;
      }

      // Safely get the AuthService
      AuthService? authService;
      bool isAuthenticated = false;

      try {
        authService = provider.Provider.of<AuthService>(context, listen: false);
        isAuthenticated = authService.currentUser != null;
        debugPrint('TechnicianRoutes: AuthService found, isAuthenticated: $isAuthenticated');
      } catch (e) {
        debugPrint('TechnicianRoutes: Error accessing AuthService: $e');
        // If we can't access the auth service, redirect to language selection
        return languageSelection;
      }
      final isAuthRoute =
          state.matchedLocation == login || state.matchedLocation == register;
      final isAgreementRoute = state.matchedLocation == agreement;
      final isPendingApprovalRoute = state.matchedLocation == pendingApproval;

      // Check if language and confirmation are completed
      final hasConfirmed = await TechnicianConfirmationService.hasConfirmed();
      debugPrint('TechnicianRoutes: hasConfirmed = $hasConfirmed');

      // If language selection and confirmation are not completed, redirect to language selection
      if (!hasConfirmed && state.matchedLocation != languageSelection) {
        debugPrint('TechnicianRoutes: Redirecting to language selection');
        return languageSelection;
      }

      // Check if the user has accepted the legal agreement
      // We only check this for non-auth routes to avoid redirect loops
      if (!isAgreementRoute && !isAuthRoute) {
        final hasAcceptedAgreement =
            await TechnicianAgreementService.hasAcceptedAgreement();
        debugPrint(
            'TechnicianRoutes: hasAcceptedAgreement = $hasAcceptedAgreement');

        // If the user hasn't accepted the agreement and is not on the agreement screen,
        // redirect to the agreement screen
        if (!hasAcceptedAgreement) {
          debugPrint('TechnicianRoutes: Redirecting to agreement screen');
          return agreement;
        }
      }

      // Handle authentication redirects
      if (!isAuthenticated && !isAuthRoute && !isAgreementRoute) {
        debugPrint('TechnicianRoutes: Not authenticated, redirecting to login');
        return login;
      }

      if (isAuthenticated) {
        // Check if the technician account is pending approval
        final userId = authService.currentUser!.id;

        try {
          final databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
          final data = await databaseService.find('technicians', userId);

          debugPrint('Technician document data: $data');

          if (data != null) {
            final verificationStatus = data['verification_status'] as String? ?? 'pending';
            final isActive = data['is_active'] == true;

            debugPrint('Verification status: $verificationStatus, isActive: $isActive');

            // If technician is not approved and not on the pending approval screen, redirect to pending approval
            if (!(verificationStatus == 'approved' && isActive) && !isPendingApprovalRoute) {
              debugPrint('TechnicianRoutes: Technician not approved, redirecting to pending approval');
              return pendingApproval;
            }
          } else {
            // If technician document doesn't exist, redirect to pending approval
            if (!isPendingApprovalRoute) {
              debugPrint('TechnicianRoutes: Technician document not found, redirecting to pending approval');
              return pendingApproval;
            }
          }
        } catch (e) {
          debugPrint('TechnicianRoutes: Error checking technician status: $e');
          // On error, redirect to pending approval to be safe
          if (!isPendingApprovalRoute) {
            return pendingApproval;
          }
        }

        // If technician is approved but on auth routes, redirect to home
        if (isAuthRoute) {
          debugPrint('TechnicianRoutes: Already authenticated, redirecting to home');
          return home;
        }
      }

      debugPrint('TechnicianRoutes: No redirect needed');
      return null;
    },
    routes: [
      // Language selection screen
      GoRoute(
        path: languageSelection,
        builder: (context, state) => LanguageSelectionScreen(
          onComplete: (confirmed) async {
            if (confirmed) {
              debugPrint(
                  'Technician: Language selection completed, showing confirmation dialog');

              // Show the confirmation dialog
              final confirmationResult =
                  await TechnicianConfirmationService.checkConfirmation(
                      context);
              debugPrint(
                  'Technician: Confirmation result: $confirmationResult');

              if (confirmationResult && context.mounted) {
                debugPrint(
                    'Technician: Confirmation successful, navigating to agreement screen');
                // Navigate to agreement screen
                try {
                  context.go(agreement);
                  debugPrint(
                      'Technician: Navigation to agreement screen initiated');
                } catch (e) {
                  debugPrint(
                      'Technician: Error navigating to agreement screen: $e');
                }
              }
            }
          },
        ),
      ),

      // Auth Routes
      GoRoute(
        path: agreement,
        builder: (context, state) => const AgreementScreen(),
      ),
      GoRoute(
        path: login,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: register,
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: pendingApproval,
        builder: (context, state) => const PendingApprovalScreen(),
      ),

      // Main Routes
      GoRoute(
        path: home,
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: notifications,
        builder: (context, state) => const NotificationScreen(),
      ),

      // Profile Routes
      GoRoute(
        path: profileSettings,
        builder: (context, state) => const ProfileSettingsScreen(),
      ),

      // Booking Routes
      GoRoute(
        path: bookings,
        builder: (context, state) => const BookingListScreen(),
      ),
      GoRoute(
        path: bookingDetails,
        builder: (context, state) => BookingDetailScreen(
          bookingId: state.pathParameters['id']!,
        ),
      ),
      GoRoute(
        path: bookingCalendar,
        builder: (context, state) => const BookingCalendarScreen(),
      ),

      // Payment Routes
      GoRoute(
        path: payments,
        builder: (context, state) => const PaymentScreen(),
      ),

      // Settings Routes
      GoRoute(
        path: settings,
        builder: (context, state) => const SettingsScreen(),
      ),
      GoRoute(
        path: bankAccount,
        builder: (context, state) => const BankAccountSettingsScreen(),
      ),
      GoRoute(
        path: invoice,
        builder: (context, state) => const InvoiceSettingsScreen(),
      ),
      GoRoute(
        path: tax,
        builder: (context, state) => const TaxSettingsScreen(),
      ),
      GoRoute(
        path: profilePicture,
        builder: (context, state) => const ProfilePictureSettingsScreen(),
      ),
      GoRoute(
        path: serviceAreas,
        builder: (context, state) => const ServiceAreasSettingsScreen(),
      ),
      GoRoute(
        path: bookingReminders,
        builder: (context, state) => const BookingReminderSettingsScreen(),
      ),
      GoRoute(
        path: paymentAlerts,
        builder: (context, state) => const PaymentAlertSettingsScreen(),
      ),
      GoRoute(
        path: workingHours,
        builder: (context, state) => const WorkingHoursSettingsScreen(),
      ),
      GoRoute(
        path: holidays,
        builder: (context, state) => const HolidaySettingsScreen(),
      ),
      GoRoute(
        path: breakTime,
        builder: (context, state) => const BreakTimeSettingsScreen(),
      ),
      GoRoute(
        path: services,
        builder: (context, state) => const ServicesSettingsScreen(),
      ),
      GoRoute(
        path: pricing,
        builder: (context, state) => const PricingSettingsScreen(),
      ),
      GoRoute(
        path: password,
        builder: (context, state) => const PasswordSettingsScreen(),
      ),
      GoRoute(
        path: twoFactor,
        builder: (context, state) => const TwoFactorSettingsScreen(),
      ),
      GoRoute(
        path: devices,
        builder: (context, state) => const DeviceManagementScreen(),
      ),
      GoRoute(
        path: language,
        builder: (context, state) => const LanguageSettingsScreen(),
      ),
      GoRoute(
        path: theme,
        builder: (context, state) => const ThemeSettingsScreen(),
      ),
      GoRoute(
        path: about,
        builder: (context, state) => const AboutScreen(),
      ),
    ],
  );

  // Navigation helper methods
  static void navigateToHome(BuildContext context) => context.go(home);
  static void navigateToLogin(BuildContext context) => context.go(login);
  static void navigateToRegister(BuildContext context) => context.go(register);
  static void navigateToAgreement(BuildContext context) =>
      context.go(agreement);
  static void navigateToPendingApproval(BuildContext context) =>
      context.go(pendingApproval);
  static void navigateToNotifications(BuildContext context) =>
      context.go(notifications);
  static void navigateToProfileSettings(BuildContext context) =>
      context.go(profileSettings);
  static void navigateToBookings(BuildContext context) => context.go(bookings);
  static void navigateToBookingCalendar(BuildContext context) =>
      context.go(bookingCalendar);
  static void navigateToPayments(BuildContext context) => context.go(payments);
  static void navigateToSettings(BuildContext context) => context.go(settings);
  static void navigateToBankAccount(BuildContext context) =>
      context.go(bankAccount);
  static void navigateToInvoice(BuildContext context) => context.go(invoice);
  static void navigateToTax(BuildContext context) => context.go(tax);
  static void navigateToProfilePicture(BuildContext context) =>
      context.go(profilePicture);
  static void navigateToServiceAreas(BuildContext context) =>
      context.go(serviceAreas);
  static void navigateToBookingReminders(BuildContext context) =>
      context.go(bookingReminders);
  static void navigateToPaymentAlerts(BuildContext context) =>
      context.go(paymentAlerts);
  static void navigateToWorkingHours(BuildContext context) =>
      context.go(workingHours);
  static void navigateToHolidays(BuildContext context) => context.go(holidays);
  static void navigateToBreakTime(BuildContext context) =>
      context.go(breakTime);
  static void navigateToServices(BuildContext context) => context.go(services);
  static void navigateToPricing(BuildContext context) => context.go(pricing);
  static void navigateToPassword(BuildContext context) => context.go(password);
  static void navigateToTwoFactor(BuildContext context) =>
      context.go(twoFactor);
  static void navigateToDevices(BuildContext context) => context.go(devices);
  static void navigateToLanguage(BuildContext context) => context.go(language);
  static void navigateToTheme(BuildContext context) => context.go(theme);
  static void navigateToAbout(BuildContext context) => context.go(about);

  // Get routes for centralized router
  static List<RouteBase> getRoutes() {
    return [
      // Language selection screen
      GoRoute(
        path: languageSelection,
        builder: (context, state) => LanguageSelectionScreen(
          onComplete: (confirmed) async {
            if (confirmed) {
              debugPrint(
                  'Technician getRoutes: Language selection completed, showing confirmation dialog');

              // Show the confirmation dialog
              final confirmationResult =
                  await TechnicianConfirmationService.checkConfirmation(
                      context);
              debugPrint(
                  'Technician getRoutes: Confirmation result: $confirmationResult');

              if (confirmationResult && context.mounted) {
                debugPrint(
                    'Technician getRoutes: Confirmation successful, navigating to agreement screen');
                // Navigate to agreement screen
                try {
                  context.go(agreement);
                  debugPrint(
                      'Technician getRoutes: Navigation to agreement screen initiated');
                } catch (e) {
                  debugPrint(
                      'Technician getRoutes: Error navigating to agreement screen: $e');
                }
              }
            }
          },
        ),
      ),

      // Auth Routes
      GoRoute(
        path: agreement,
        builder: (context, state) => const AgreementScreen(),
      ),
      GoRoute(
        path: login,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: register,
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: pendingApproval,
        builder: (context, state) => const PendingApprovalScreen(),
      ),

      // Main Routes
      GoRoute(
        path: home,
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: notifications,
        builder: (context, state) => const NotificationScreen(),
      ),

      // Profile Routes
      GoRoute(
        path: profileSettings,
        builder: (context, state) => const ProfileSettingsScreen(),
      ),

      // Booking Routes
      GoRoute(
        path: bookings,
        builder: (context, state) => const BookingListScreen(),
      ),
      GoRoute(
        path: bookingDetails,
        builder: (context, state) => BookingDetailScreen(
          bookingId: state.pathParameters['id']!,
        ),
      ),
      GoRoute(
        path: bookingCalendar,
        builder: (context, state) => const BookingCalendarScreen(),
      ),

      // Payment Routes
      GoRoute(
        path: payments,
        builder: (context, state) => const PaymentScreen(),
      ),

      // Settings Routes
      GoRoute(
        path: settings,
        builder: (context, state) => const SettingsScreen(),
      ),
      GoRoute(
        path: bankAccount,
        builder: (context, state) => const BankAccountSettingsScreen(),
      ),
      GoRoute(
        path: invoice,
        builder: (context, state) => const InvoiceSettingsScreen(),
      ),
      GoRoute(
        path: tax,
        builder: (context, state) => const TaxSettingsScreen(),
      ),
      GoRoute(
        path: profilePicture,
        builder: (context, state) => const ProfilePictureSettingsScreen(),
      ),
      GoRoute(
        path: serviceAreas,
        builder: (context, state) => const ServiceAreasSettingsScreen(),
      ),
      GoRoute(
        path: bookingReminders,
        builder: (context, state) => const BookingReminderSettingsScreen(),
      ),
      GoRoute(
        path: paymentAlerts,
        builder: (context, state) => const PaymentAlertSettingsScreen(),
      ),
      GoRoute(
        path: workingHours,
        builder: (context, state) => const WorkingHoursSettingsScreen(),
      ),
      GoRoute(
        path: holidays,
        builder: (context, state) => const HolidaySettingsScreen(),
      ),
      GoRoute(
        path: breakTime,
        builder: (context, state) => const BreakTimeSettingsScreen(),
      ),
      GoRoute(
        path: services,
        builder: (context, state) => const ServicesSettingsScreen(),
      ),
      GoRoute(
        path: pricing,
        builder: (context, state) => const PricingSettingsScreen(),
      ),
      GoRoute(
        path: password,
        builder: (context, state) => const PasswordSettingsScreen(),
      ),
      GoRoute(
        path: twoFactor,
        builder: (context, state) => const TwoFactorSettingsScreen(),
      ),
      GoRoute(
        path: devices,
        builder: (context, state) => const DeviceManagementScreen(),
      ),
      GoRoute(
        path: language,
        builder: (context, state) => const LanguageSettingsScreen(),
      ),
      GoRoute(
        path: theme,
        builder: (context, state) => const ThemeSettingsScreen(),
      ),
      GoRoute(
        path: about,
        builder: (context, state) => const AboutScreen(),
      ),
    ];
  }
}

// Router Provider
final technicianRouterProvider =
    Provider<GoRouter>((ref) => TechnicianRoutes.router);
