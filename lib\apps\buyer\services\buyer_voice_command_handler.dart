import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import '../../../shared/services/voice/voice_assistant_service.dart';
import '../../../shared/services/ai/payment_restriction_service.dart';

/// A service that handles voice commands for the buyer app
class BuyerVoiceCommandHandler {
  final VoiceAssistantService _voiceAssistant;
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  final BuildContext _context;
  final PaymentRestrictionService _paymentRestriction =
      PaymentRestrictionService();

  BuyerVoiceCommandHandler(this._context, this._voiceAssistant);

  /// Process a voice command
  Future<void> processCommand(String command) async {
    try {
      final lowerCommand = command.toLowerCase();

      // Log the command
      await _analytics.logEvent(
        name: 'buyer_voice_command',
        parameters: {'command': command},
      );

      // Check if this is a payment-related command
      if (_paymentRestriction.containsPaymentKeywords(lowerCommand) &&
          _paymentRestriction.isPaymentProcessingRequest(lowerCommand)) {
        _paymentRestriction.logPaymentRestriction(
            command, "buyer_payment_processing");
        await _voiceAssistant
            .speakResponse(_paymentRestriction.getPaymentRestrictionResponse());
        return;
      }

      // Search command
      if (_isSearchCommand(lowerCommand)) {
        await _handleSearchCommand(lowerCommand);
        return;
      }

      // Navigation commands
      if (_isNavigationCommand(lowerCommand)) {
        await _handleNavigationCommand(lowerCommand);
        return;
      }

      // Cart commands
      if (_isCartCommand(lowerCommand)) {
        await _handleCartCommand(lowerCommand);
        return;
      }

      // Help commands
      if (_isHelpCommand(lowerCommand)) {
        await _handleHelpCommand(lowerCommand);
        return;
      }

      // Unknown command
      await _voiceAssistant.speakResponse(
          "I'm sorry, I didn't understand that command. Please try again.");
    } catch (e) {
      log('Error processing voice command: $e');
      await _voiceAssistant.speakResponse(
          "I'm sorry, there was an error processing your command.");
    }
  }

  /// Check if the command is a search command
  bool _isSearchCommand(String command) {
    return command.contains('search') ||
        command.contains('find') ||
        command.contains('look for');
  }

  /// Handle search commands
  Future<void> _handleSearchCommand(String command) async {
    try {
      // Extract search query
      String searchQuery = '';

      if (command.contains('search for')) {
        searchQuery = command.split('search for').last.trim();
      } else if (command.contains('find')) {
        searchQuery = command.split('find').last.trim();
      } else if (command.contains('look for')) {
        searchQuery = command.split('look for').last.trim();
      } else if (command.contains('search')) {
        searchQuery = command.split('search').last.trim();
      }

      if (searchQuery.isEmpty) {
        await _voiceAssistant
            .speakResponse("What would you like to search for?");
        return;
      }

      // Navigate to search screen with query
      await _voiceAssistant.speakResponse("Searching for $searchQuery");

      // Use GoRouter to navigate to search screen with query
      if (_context.mounted) {
        GoRouter.of(_context).go('/search', extra: {'query': searchQuery});
      }
    } catch (e) {
      log('Error handling search command: $e');
      await _voiceAssistant
          .speakResponse("I'm sorry, I couldn't perform that search.");
    }
  }

  /// Check if the command is a navigation command
  bool _isNavigationCommand(String command) {
    return command.contains('go to') ||
        command.contains('open') ||
        command.contains('show') ||
        command.contains('navigate to');
  }

  /// Handle navigation commands
  Future<void> _handleNavigationCommand(String command) async {
    try {
      String destination = '';

      // Extract destination
      if (command.contains('go to')) {
        destination = command.split('go to').last.trim();
      } else if (command.contains('open')) {
        destination = command.split('open').last.trim();
      } else if (command.contains('show')) {
        destination = command.split('show').last.trim();
      } else if (command.contains('navigate to')) {
        destination = command.split('navigate to').last.trim();
      }

      // Map common destinations to routes
      String route = '';
      String response = '';

      switch (destination) {
        case 'home':
          route = '/';
          response = 'Going to home screen';
          break;
        case 'cart':
        case 'shopping cart':
          route = '/cart';
          response = 'Opening your shopping cart';
          break;
        case 'profile':
        case 'my profile':
        case 'account':
          route = '/profile';
          response = 'Opening your profile';
          break;
        case 'orders':
        case 'my orders':
          route = '/orders';
          response = 'Showing your orders';
          break;
        case 'categories':
          route = '/categories';
          response = 'Opening categories';
          break;
        case 'wishlist':
        case 'favorites':
          route = '/wishlist';
          response = 'Opening your wishlist';
          break;
        case 'notifications':
          route = '/notifications';
          response = 'Showing your notifications';
          break;
        case 'help':
        case 'support':
          route = '/help';
          response = 'Opening help and support';
          break;
        default:
          await _voiceAssistant.speakResponse(
              "I'm sorry, I don't know how to navigate to $destination.");
          return;
      }

      // Navigate to the destination
      await _voiceAssistant.speakResponse(response);

      if (_context.mounted && route.isNotEmpty) {
        GoRouter.of(_context).go(route);
      }
    } catch (e) {
      log('Error handling navigation command: $e');
      await _voiceAssistant
          .speakResponse("I'm sorry, I couldn't navigate to that destination.");
    }
  }

  /// Check if the command is a cart command
  bool _isCartCommand(String command) {
    return command.contains('cart') ||
        command.contains('add to cart') ||
        command.contains('remove from cart') ||
        command.contains('checkout');
  }

  /// Handle cart commands
  Future<void> _handleCartCommand(String command) async {
    try {
      if (command.contains('open cart') ||
          command.contains('show cart') ||
          command.contains('go to cart')) {
        await _voiceAssistant.speakResponse('Opening your shopping cart');
        if (_context.mounted) {
          GoRouter.of(_context).go('/cart');
        }
        return;
      }

      if (command.contains('checkout')) {
        // Check if this is a payment processing request
        if (_paymentRestriction.isPaymentProcessingRequest(command)) {
          _paymentRestriction.logPaymentRestriction(
              command, "checkout_payment_processing");
          await _voiceAssistant.speakResponse(
              _paymentRestriction.getPaymentRestrictionResponse());
          return;
        }

        // Just navigate to checkout without processing payment
        await _voiceAssistant.speakResponse('Taking you to checkout');
        if (_context.mounted) {
          GoRouter.of(_context).go('/checkout');
        }
        return;
      }

      // For add/remove commands, we would need product context
      // This is a simplified implementation
      await _voiceAssistant.speakResponse(
          "I'm sorry, I need more information to manage your cart.");
    } catch (e) {
      log('Error handling cart command: $e');
      await _voiceAssistant
          .speakResponse("I'm sorry, I couldn't process your cart request.");
    }
  }

  /// Check if the command is a help command
  bool _isHelpCommand(String command) {
    return command.contains('help') ||
        command.contains('support') ||
        command.contains('assistance');
  }

  /// Handle help commands
  Future<void> _handleHelpCommand(String command) async {
    try {
      await _voiceAssistant.speakResponse('Opening help and support');
      if (_context.mounted) {
        GoRouter.of(_context).go('/help');
      }
    } catch (e) {
      log('Error handling help command: $e');
      await _voiceAssistant
          .speakResponse("I'm sorry, I couldn't open help and support.");
    }
  }
}
