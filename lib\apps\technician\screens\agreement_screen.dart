import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../services/technician_agreement_service.dart';

/// Screen that shows the legal agreement for technicians
class AgreementScreen extends StatefulWidget {
  /// Creates an [AgreementScreen]
  const AgreementScreen({super.key});

  @override
  State<AgreementScreen> createState() => _AgreementScreenState();
}

class _AgreementScreenState extends State<AgreementScreen> {
  bool _isLoading = true;
  String _statusMessage = 'Checking agreement status...';

  @override
  void initState() {
    super.initState();
    debugPrint('AgreementScreen: initState called');
    _checkAgreement();
  }

  Future<void> _checkAgreement() async {
    try {
      setState(() {
        _isLoading = true;
        _statusMessage = 'Checking agreement status...';
      });

      debugPrint('AgreementScreen: Checking agreement status');

      // First check if the agreement has already been accepted
      final hasAccepted =
          await TechnicianAgreementService.hasAcceptedAgreement();
      debugPrint(
          'AgreementScreen: Has already accepted agreement: $hasAccepted');

      if (hasAccepted) {
        // If already accepted, navigate to login
        if (mounted) {
          debugPrint(
              'AgreementScreen: Agreement already accepted, navigating to login');
          context.go('/technician/login');
          return;
        }
      }

      // Show the agreement dialog
      bool accepted = false;
      if (mounted) {
        accepted = await TechnicianAgreementService.checkAgreement(context);
        debugPrint('AgreementScreen: Agreement check result: $accepted');
      }

      if (mounted) {
        if (accepted) {
          // If accepted, navigate to login
          debugPrint(
              'AgreementScreen: Agreement accepted, navigating to login');
          context.go('/technician/login');
        } else {
          // If not accepted, show error
          debugPrint('AgreementScreen: Agreement not accepted, showing error');
          setState(() {
            _isLoading = false;
            _statusMessage = 'You must accept the agreement to continue.';
          });
        }
      }
    } catch (e) {
      debugPrint('AgreementScreen: Error checking agreement: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _statusMessage = 'Error: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint(
        'AgreementScreen: Building agreement screen, isLoading: $_isLoading');
    return Scaffold(
      appBar: AppBar(
        title: const Text('Technician Agreement'),
      ),
      body: Center(
        child: _isLoading
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(_statusMessage),
                ],
              )
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _statusMessage,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      debugPrint('AgreementScreen: Try Again button pressed');
                      _checkAgreement();
                    },
                    child: const Text('Try Again'),
                  ),
                ],
              ),
      ),
    );
  }
}
