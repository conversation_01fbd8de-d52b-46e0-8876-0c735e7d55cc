import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shivish/shared/core/config/supabase_config.dart';

/// Supabase initialization and configuration service
class SupabaseService {
  static SupabaseClient? _client;

  /// Get the Supabase client instance
  static SupabaseClient get client {
    if (_client == null) {
      throw Exception(
        'Supabase not initialized. Call SupabaseService.initialize() first.',
      );
    }
    return _client!;
  }

  /// Initialize Supabase
  static Future<void> initialize() async {
    try {
      // Check if Supabase is configured
      if (!SupabaseConfig.isConfigured) {
        debugPrint('Supabase not configured, skipping initialization');
        return;
      }

      await Supabase.initialize(
        url: SupabaseConfig.supabaseUrl,
        anonKey: SupabaseConfig.supabaseAnonKey,
        authOptions: const FlutterAuthClientOptions(
          authFlowType: AuthFlowType.pkce,
          autoRefreshToken: true,
        ),
        realtimeClientOptions: const RealtimeClientOptions(
          logLevel: kDebugMode ? RealtimeLogLevel.info : RealtimeLogLevel.error,
        ),
        storageOptions: const StorageClientOptions(retryAttempts: 3),
        postgrestOptions: const PostgrestClientOptions(schema: 'public'),
      );

      _client = Supabase.instance.client;
      debugPrint('Supabase initialized successfully');

      // Set up auth state listener for debugging
      if (kDebugMode) {
        _client!.auth.onAuthStateChange.listen((data) {
          debugPrint('Supabase Auth State Changed: ${data.event}');
          if (data.session?.user != null) {
            debugPrint('User: ${data.session!.user.email}');
          }
        });
      }
    } catch (e) {
      debugPrint('Supabase initialization failed: $e');
      rethrow;
    }
  }

  /// Check if Supabase is initialized
  static bool get isInitialized => _client != null;

  /// Get current session
  static Session? get currentSession => _client?.auth.currentSession;

  /// Get current user
  static User? get currentUser => _client?.auth.currentUser;

  /// Sign out and clear session
  static Future<void> signOut() async {
    try {
      await _client?.auth.signOut();
      debugPrint('Supabase user signed out');
    } catch (e) {
      debugPrint('Error signing out from Supabase: $e');
      rethrow;
    }
  }

  /// Refresh the current session
  static Future<AuthResponse> refreshSession() async {
    try {
      final response = await _client!.auth.refreshSession();
      debugPrint('Supabase session refreshed');
      return response;
    } catch (e) {
      debugPrint('Error refreshing Supabase session: $e');
      rethrow;
    }
  }

  /// Get auth headers for API calls
  static Map<String, String> getAuthHeaders() {
    final session = currentSession;
    if (session?.accessToken != null) {
      return {
        'Authorization': 'Bearer ${session!.accessToken}',
        'apikey': SupabaseConfig.supabaseAnonKey,
      };
    }
    return {'apikey': SupabaseConfig.supabaseAnonKey};
  }

  /// Handle deep links for OAuth callbacks
  static Future<bool> handleDeepLink(String url) async {
    try {
      if (url.startsWith(SupabaseConfig.deepLinkScheme)) {
        final uri = Uri.parse(url);

        // Handle OAuth callback
        if (uri.path.contains('/auth/callback')) {
          final fragment = uri.fragment;
          if (fragment.isNotEmpty) {
            // Parse the fragment for auth tokens
            final params = Uri.splitQueryString(fragment);
            if (params.containsKey('access_token')) {
              debugPrint('Handling OAuth callback');
              return true;
            }
          }
        }

        // Handle email confirmation
        if (uri.path.contains('/auth/confirm')) {
          final token = uri.queryParameters['token'];
          final type = uri.queryParameters['type'];

          if (token != null && type != null) {
            await _client!.auth.verifyOTP(
              type: OtpType.values.firstWhere((e) => e.name == type),
              token: token,
            );
            debugPrint('Email confirmed via deep link');
            return true;
          }
        }

        // Handle password reset
        if (uri.path.contains('/auth/reset')) {
          final token = uri.queryParameters['token'];
          if (token != null) {
            // Handle password reset token
            debugPrint('Password reset token received');
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      debugPrint('Error handling deep link: $e');
      return false;
    }
  }

  /// Dispose resources
  static Future<void> dispose() async {
    try {
      await _client?.dispose();
      _client = null;
      debugPrint('Supabase service disposed');
    } catch (e) {
      debugPrint('Error disposing Supabase service: $e');
    }
  }
}
