import 'package:flutter/material.dart';
import '../bloc/notification_settings/notification_settings_state.dart';
import '../utils/icon_mapping.dart';

class NotificationSettingCard extends StatelessWidget {
  final NotificationSetting setting;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const NotificationSettingCard({
    super.key,
    required this.setting,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: Icon(
          getIconFromString(setting.icon),
          color: Theme.of(context).colorScheme.primary,
        ),
        title: Text(setting.title),
        subtitle:
            setting.description != null ? Text(setting.description!) : null,
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Switch(
              value: setting.isEnabled,
              onChanged: (value) {
                // Handle toggle
              },
            ),
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: onEdit,
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: onDelete,
            ),
          ],
        ),
      ),
    );
  }
}
