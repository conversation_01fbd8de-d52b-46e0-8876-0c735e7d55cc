import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/user/user_model.dart';
import '../services/user_service.dart';

final userRepositoryProvider = Provider<UserRepository>(
  (ref) => UserRepository(ref.watch(userServiceProvider)),
);

class UserRepository {
  final UserService _service;

  UserRepository(this._service);

  Future<UserModel?> getCurrentUser() async {
    return await _service.getCurrentUser();
  }

  Future<UserModel?> getUserById(String userId) async {
    return await _service.getUserById(userId);
  }

  Future<UserModel?> getUserByEmail(String email) async {
    return await _service.getUserByEmail(email);
  }

  Future<void> updateUser(UserModel user) async {
    await _service.updateUser(user);
  }

  Future<void> deleteUser() async {
    await _service.deleteUser();
  }
}
