import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';
import '../../../shared/models/ride/ride_request_model.dart';
import 'safety_tracking_service.dart';
import '../widgets/safety/safety_pin_entry_screen.dart';

final _logger = getLogger('SafetyMonitoringService');

/// Production-ready service that monitors ride status and triggers safety checks
///
/// This service provides:
/// - Real-time ride status monitoring
/// - Automatic safety tracking activation/deactivation
/// - Error handling and recovery
/// - Resource cleanup and lifecycle management
class SafetyMonitoringService extends ChangeNotifier {
  final DatabaseService _databaseService;
  final SafetyTrackingService _safetyService;

  StreamSubscription<Map<String, dynamic>?>? _rideStatusSubscription;
  String? _currentRideId;
  bool _isMonitoring = false;
  bool _isDisposed = false;

  SafetyMonitoringService()
    : _databaseService = DatabaseService(DatabaseConfig.fromEnvironment()),
      _safetyService = SafetyTrackingService();

  // Getters
  bool get isMonitoring => _isMonitoring && !_isDisposed;
  String? get currentRideId => _currentRideId;
  bool get isDisposed => _isDisposed;

  /// Start monitoring a ride for safety checks with validation
  Future<void> startMonitoring(String rideId) async {
    if (_isDisposed) {
      _logger.warning('Cannot start monitoring on disposed service');
      return;
    }

    if (rideId.isEmpty) {
      _logger.warning('Cannot start monitoring with empty ride ID');
      return;
    }

    if (_isMonitoring && _currentRideId == rideId) {
      _logger.info('Already monitoring ride: $rideId');
      return;
    }

    try {
      _logger.info('Starting safety monitoring for ride: $rideId');

      // Stop any existing monitoring
      await stopMonitoring();

      _currentRideId = rideId;
      _isMonitoring = true;

      // Start listening to ride status changes using periodic polling
      // since streamDocument might not be available
      _startRideStatusPolling(rideId);

      notifyListeners();
      _logger.info('Safety monitoring started for ride: $rideId');
    } catch (e, stackTrace) {
      _logger.severe('Error starting safety monitoring: $e', e, stackTrace);
      _isMonitoring = false;
      _currentRideId = null;
      notifyListeners();
    }
  }

  /// Start polling for ride status changes
  void _startRideStatusPolling(String rideId) {
    // Poll every 30 seconds for ride status changes
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isDisposed || !_isMonitoring || _currentRideId != rideId) {
        timer.cancel();
        return;
      }

      _checkRideStatus(rideId).catchError((e) {
        _logger.warning('Error during ride status check: $e');
      });
    });
  }

  /// Check ride status manually
  Future<void> _checkRideStatus(String rideId) async {
    try {
      final rideData = await _databaseService.find('ride_requests', rideId);
      _onRideStatusChanged(rideData);
    } catch (e) {
      _logger.warning('Error checking ride status: $e');
    }
  }

  /// Stop monitoring the current ride with proper cleanup
  Future<void> stopMonitoring() async {
    if (!_isMonitoring && _currentRideId == null) return;

    try {
      _logger.info('Stopping safety monitoring for ride: $_currentRideId');

      // Cancel ride status subscription
      await _rideStatusSubscription?.cancel();
      _rideStatusSubscription = null;

      // Stop safety tracking if active
      if (_safetyService.isActive) {
        await _safetyService.stopSafetyTracking();
      }

      _currentRideId = null;
      _isMonitoring = false;

      if (!_isDisposed) {
        notifyListeners();
      }
      _logger.info('Safety monitoring stopped');
    } catch (e, stackTrace) {
      _logger.severe('Error stopping safety monitoring: $e', e, stackTrace);
      // Ensure state is reset even if there's an error
      _currentRideId = null;
      _isMonitoring = false;
      if (!_isDisposed) {
        notifyListeners();
      }
    }
  }

  /// Handle ride status changes
  void _onRideStatusChanged(Map<String, dynamic>? rideData) {
    if (rideData == null) {
      _logger.warning('Ride data is null, stopping monitoring');
      stopMonitoring();
      return;
    }

    try {
      final ride = RideRequestModel.fromJson(rideData);
      _logger.info(
        'Ride status changed: ${ride.status.name} for ride: ${ride.id}',
      );

      switch (ride.status) {
        case RideStatus.inProgress:
          _onRideStarted(ride);
          break;
        case RideStatus.completed:
        case RideStatus.cancelled:
          _onRideEnded(ride);
          break;
        default:
          // No action needed for other statuses
          break;
      }
    } catch (e) {
      _logger.severe('Error processing ride status change: $e');
    }
  }

  /// Handle when ride starts (status becomes inProgress)
  void _onRideStarted(RideRequestModel ride) {
    _logger.info(
      'Ride started, checking if safety tracking should begin: ${ride.id}',
    );

    // Check if safety tracking is configured for this ride
    _checkAndStartSafetyTracking(ride);
  }

  /// Handle when ride ends (completed or cancelled)
  void _onRideEnded(RideRequestModel ride) {
    _logger.info('Ride ended: ${ride.status.name} for ride: ${ride.id}');

    // Stop safety tracking if active
    if (_safetyService.isActive &&
        _safetyService.currentSession?.rideId == ride.id) {
      _safetyService.stopSafetyTracking();
    }

    // Stop monitoring
    stopMonitoring();
  }

  /// Check if safety tracking should start and start it
  Future<void> _checkAndStartSafetyTracking(RideRequestModel ride) async {
    try {
      // Check if there's a safety session for this ride
      final sessionData = await _databaseService.find(
        'safety_sessions',
        ride.id,
      );

      if (sessionData != null) {
        final session = SafetyTrackingSession.fromJson(sessionData);

        if (session.config.isEnabled &&
            session.status != SafetyTrackingStatus.inactive) {
          _logger.info('Starting safety tracking for ride: ${ride.id}');

          // Start safety tracking
          final success = await _safetyService.startSafetyTracking(
            rideId: ride.id,
            userId: session.userId,
            config: session.config,
          );

          if (success) {
            _logger.info(
              'Safety tracking started successfully for ride: ${ride.id}',
            );
          } else {
            _logger.warning(
              'Failed to start safety tracking for ride: ${ride.id}',
            );
          }
        }
      }
    } catch (e) {
      _logger.severe('Error checking safety tracking configuration: $e');
    }
  }

  /// Show PIN entry screen for safety check
  static Future<void> showPinEntryScreen({
    required BuildContext context,
    required String rideId,
    required SafetyTrackingService safetyService,
  }) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SafetyPinEntryScreen(
          rideId: rideId,
          safetyService: safetyService,
          onSuccess: () {
            Navigator.of(context).pop();
            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('✅ Safety check completed successfully!'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          },
          onTimeout: () {
            Navigator.of(context).pop();
            // Show timeout message
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  '⚠️ Safety check timed out. Emergency alert sent.',
                ),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 4),
              ),
            );
          },
        ),
        fullscreenDialog: true,
      ),
    );
  }

  /// Dispose of the service and clean up resources
  @override
  void dispose() {
    if (_isDisposed) return;

    _logger.info('Disposing SafetyMonitoringService');

    try {
      _isDisposed = true;

      // Stop monitoring without notifying listeners (since we're disposing)
      _rideStatusSubscription?.cancel();
      _rideStatusSubscription = null;

      // Dispose of safety service
      _safetyService.dispose();

      _currentRideId = null;
      _isMonitoring = false;

      _logger.info('SafetyMonitoringService disposed successfully');
    } catch (e) {
      _logger.warning('Error during SafetyMonitoringService disposal: $e');
    } finally {
      super.dispose();
    }
  }

  /// Get service health status for monitoring
  Map<String, dynamic> get healthStatus => {
    'isMonitoring': _isMonitoring,
    'isDisposed': _isDisposed,
    'currentRideId': _currentRideId,
    'hasActiveSubscription': _rideStatusSubscription != null,
    'safetyServiceActive': _safetyService.isActive,
  };
}

/// Provider for safety monitoring service
final safetyMonitoringServiceProvider = Provider<SafetyMonitoringService>((
  ref,
) {
  return SafetyMonitoringService();
});

/// Provider for monitoring status
final safetyMonitoringStatusProvider =
    StateNotifierProvider<SafetyMonitoringNotifier, SafetyMonitoringStatus>((
      ref,
    ) {
      final service = ref.watch(safetyMonitoringServiceProvider);
      return SafetyMonitoringNotifier(service);
    });

/// Safety monitoring status
class SafetyMonitoringStatus {
  final bool isMonitoring;
  final String? currentRideId;
  final bool safetyTrackingActive;

  const SafetyMonitoringStatus({
    required this.isMonitoring,
    this.currentRideId,
    required this.safetyTrackingActive,
  });

  SafetyMonitoringStatus copyWith({
    bool? isMonitoring,
    String? currentRideId,
    bool? safetyTrackingActive,
  }) {
    return SafetyMonitoringStatus(
      isMonitoring: isMonitoring ?? this.isMonitoring,
      currentRideId: currentRideId ?? this.currentRideId,
      safetyTrackingActive: safetyTrackingActive ?? this.safetyTrackingActive,
    );
  }
}

/// State notifier for safety monitoring
class SafetyMonitoringNotifier extends StateNotifier<SafetyMonitoringStatus> {
  final SafetyMonitoringService _service;

  SafetyMonitoringNotifier(this._service)
    : super(
        const SafetyMonitoringStatus(
          isMonitoring: false,
          safetyTrackingActive: false,
        ),
      ) {
    _service.addListener(_onServiceChanged);
    _updateState();
  }

  void _onServiceChanged() {
    _updateState();
  }

  void _updateState() {
    state = SafetyMonitoringStatus(
      isMonitoring: _service.isMonitoring,
      currentRideId: _service.currentRideId,
      safetyTrackingActive: _service._safetyService.isActive,
    );
  }

  /// Start monitoring a ride
  Future<void> startMonitoring(String rideId) async {
    await _service.startMonitoring(rideId);
  }

  /// Stop monitoring
  Future<void> stopMonitoring() async {
    await _service.stopMonitoring();
  }

  @override
  void dispose() {
    _service.removeListener(_onServiceChanged);
    super.dispose();
  }
}
