// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'doctor_schedule_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DoctorScheduleModel {

 String get id; String get doctorId; Map<String, DaySchedule> get weeklySchedule; List<DateTime> get holidays; DateTime get createdAt; DateTime get updatedAt;
/// Create a copy of DoctorScheduleModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DoctorScheduleModelCopyWith<DoctorScheduleModel> get copyWith => _$DoctorScheduleModelCopyWithImpl<DoctorScheduleModel>(this as DoctorScheduleModel, _$identity);

  /// Serializes this DoctorScheduleModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DoctorScheduleModel&&(identical(other.id, id) || other.id == id)&&(identical(other.doctorId, doctorId) || other.doctorId == doctorId)&&const DeepCollectionEquality().equals(other.weeklySchedule, weeklySchedule)&&const DeepCollectionEquality().equals(other.holidays, holidays)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,doctorId,const DeepCollectionEquality().hash(weeklySchedule),const DeepCollectionEquality().hash(holidays),createdAt,updatedAt);

@override
String toString() {
  return 'DoctorScheduleModel(id: $id, doctorId: $doctorId, weeklySchedule: $weeklySchedule, holidays: $holidays, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $DoctorScheduleModelCopyWith<$Res>  {
  factory $DoctorScheduleModelCopyWith(DoctorScheduleModel value, $Res Function(DoctorScheduleModel) _then) = _$DoctorScheduleModelCopyWithImpl;
@useResult
$Res call({
 String id, String doctorId, Map<String, DaySchedule> weeklySchedule, List<DateTime> holidays, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class _$DoctorScheduleModelCopyWithImpl<$Res>
    implements $DoctorScheduleModelCopyWith<$Res> {
  _$DoctorScheduleModelCopyWithImpl(this._self, this._then);

  final DoctorScheduleModel _self;
  final $Res Function(DoctorScheduleModel) _then;

/// Create a copy of DoctorScheduleModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? doctorId = null,Object? weeklySchedule = null,Object? holidays = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,doctorId: null == doctorId ? _self.doctorId : doctorId // ignore: cast_nullable_to_non_nullable
as String,weeklySchedule: null == weeklySchedule ? _self.weeklySchedule : weeklySchedule // ignore: cast_nullable_to_non_nullable
as Map<String, DaySchedule>,holidays: null == holidays ? _self.holidays : holidays // ignore: cast_nullable_to_non_nullable
as List<DateTime>,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [DoctorScheduleModel].
extension DoctorScheduleModelPatterns on DoctorScheduleModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DoctorScheduleModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DoctorScheduleModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DoctorScheduleModel value)  $default,){
final _that = this;
switch (_that) {
case _DoctorScheduleModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DoctorScheduleModel value)?  $default,){
final _that = this;
switch (_that) {
case _DoctorScheduleModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String doctorId,  Map<String, DaySchedule> weeklySchedule,  List<DateTime> holidays,  DateTime createdAt,  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DoctorScheduleModel() when $default != null:
return $default(_that.id,_that.doctorId,_that.weeklySchedule,_that.holidays,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String doctorId,  Map<String, DaySchedule> weeklySchedule,  List<DateTime> holidays,  DateTime createdAt,  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _DoctorScheduleModel():
return $default(_that.id,_that.doctorId,_that.weeklySchedule,_that.holidays,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String doctorId,  Map<String, DaySchedule> weeklySchedule,  List<DateTime> holidays,  DateTime createdAt,  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _DoctorScheduleModel() when $default != null:
return $default(_that.id,_that.doctorId,_that.weeklySchedule,_that.holidays,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DoctorScheduleModel implements DoctorScheduleModel {
  const _DoctorScheduleModel({required this.id, required this.doctorId, required final  Map<String, DaySchedule> weeklySchedule, required final  List<DateTime> holidays, required this.createdAt, required this.updatedAt}): _weeklySchedule = weeklySchedule,_holidays = holidays;
  factory _DoctorScheduleModel.fromJson(Map<String, dynamic> json) => _$DoctorScheduleModelFromJson(json);

@override final  String id;
@override final  String doctorId;
 final  Map<String, DaySchedule> _weeklySchedule;
@override Map<String, DaySchedule> get weeklySchedule {
  if (_weeklySchedule is EqualUnmodifiableMapView) return _weeklySchedule;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_weeklySchedule);
}

 final  List<DateTime> _holidays;
@override List<DateTime> get holidays {
  if (_holidays is EqualUnmodifiableListView) return _holidays;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_holidays);
}

@override final  DateTime createdAt;
@override final  DateTime updatedAt;

/// Create a copy of DoctorScheduleModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DoctorScheduleModelCopyWith<_DoctorScheduleModel> get copyWith => __$DoctorScheduleModelCopyWithImpl<_DoctorScheduleModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DoctorScheduleModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DoctorScheduleModel&&(identical(other.id, id) || other.id == id)&&(identical(other.doctorId, doctorId) || other.doctorId == doctorId)&&const DeepCollectionEquality().equals(other._weeklySchedule, _weeklySchedule)&&const DeepCollectionEquality().equals(other._holidays, _holidays)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,doctorId,const DeepCollectionEquality().hash(_weeklySchedule),const DeepCollectionEquality().hash(_holidays),createdAt,updatedAt);

@override
String toString() {
  return 'DoctorScheduleModel(id: $id, doctorId: $doctorId, weeklySchedule: $weeklySchedule, holidays: $holidays, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$DoctorScheduleModelCopyWith<$Res> implements $DoctorScheduleModelCopyWith<$Res> {
  factory _$DoctorScheduleModelCopyWith(_DoctorScheduleModel value, $Res Function(_DoctorScheduleModel) _then) = __$DoctorScheduleModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String doctorId, Map<String, DaySchedule> weeklySchedule, List<DateTime> holidays, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class __$DoctorScheduleModelCopyWithImpl<$Res>
    implements _$DoctorScheduleModelCopyWith<$Res> {
  __$DoctorScheduleModelCopyWithImpl(this._self, this._then);

  final _DoctorScheduleModel _self;
  final $Res Function(_DoctorScheduleModel) _then;

/// Create a copy of DoctorScheduleModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? doctorId = null,Object? weeklySchedule = null,Object? holidays = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_DoctorScheduleModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,doctorId: null == doctorId ? _self.doctorId : doctorId // ignore: cast_nullable_to_non_nullable
as String,weeklySchedule: null == weeklySchedule ? _self._weeklySchedule : weeklySchedule // ignore: cast_nullable_to_non_nullable
as Map<String, DaySchedule>,holidays: null == holidays ? _self._holidays : holidays // ignore: cast_nullable_to_non_nullable
as List<DateTime>,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$DaySchedule {

 bool get isAvailable; List<TimeSlot> get slots;
/// Create a copy of DaySchedule
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DayScheduleCopyWith<DaySchedule> get copyWith => _$DayScheduleCopyWithImpl<DaySchedule>(this as DaySchedule, _$identity);

  /// Serializes this DaySchedule to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DaySchedule&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&const DeepCollectionEquality().equals(other.slots, slots));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isAvailable,const DeepCollectionEquality().hash(slots));

@override
String toString() {
  return 'DaySchedule(isAvailable: $isAvailable, slots: $slots)';
}


}

/// @nodoc
abstract mixin class $DayScheduleCopyWith<$Res>  {
  factory $DayScheduleCopyWith(DaySchedule value, $Res Function(DaySchedule) _then) = _$DayScheduleCopyWithImpl;
@useResult
$Res call({
 bool isAvailable, List<TimeSlot> slots
});




}
/// @nodoc
class _$DayScheduleCopyWithImpl<$Res>
    implements $DayScheduleCopyWith<$Res> {
  _$DayScheduleCopyWithImpl(this._self, this._then);

  final DaySchedule _self;
  final $Res Function(DaySchedule) _then;

/// Create a copy of DaySchedule
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isAvailable = null,Object? slots = null,}) {
  return _then(_self.copyWith(
isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,slots: null == slots ? _self.slots : slots // ignore: cast_nullable_to_non_nullable
as List<TimeSlot>,
  ));
}

}


/// Adds pattern-matching-related methods to [DaySchedule].
extension DaySchedulePatterns on DaySchedule {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DaySchedule value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DaySchedule() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DaySchedule value)  $default,){
final _that = this;
switch (_that) {
case _DaySchedule():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DaySchedule value)?  $default,){
final _that = this;
switch (_that) {
case _DaySchedule() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isAvailable,  List<TimeSlot> slots)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DaySchedule() when $default != null:
return $default(_that.isAvailable,_that.slots);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isAvailable,  List<TimeSlot> slots)  $default,) {final _that = this;
switch (_that) {
case _DaySchedule():
return $default(_that.isAvailable,_that.slots);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isAvailable,  List<TimeSlot> slots)?  $default,) {final _that = this;
switch (_that) {
case _DaySchedule() when $default != null:
return $default(_that.isAvailable,_that.slots);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DaySchedule implements DaySchedule {
  const _DaySchedule({required this.isAvailable, required final  List<TimeSlot> slots}): _slots = slots;
  factory _DaySchedule.fromJson(Map<String, dynamic> json) => _$DayScheduleFromJson(json);

@override final  bool isAvailable;
 final  List<TimeSlot> _slots;
@override List<TimeSlot> get slots {
  if (_slots is EqualUnmodifiableListView) return _slots;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_slots);
}


/// Create a copy of DaySchedule
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DayScheduleCopyWith<_DaySchedule> get copyWith => __$DayScheduleCopyWithImpl<_DaySchedule>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DayScheduleToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DaySchedule&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&const DeepCollectionEquality().equals(other._slots, _slots));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isAvailable,const DeepCollectionEquality().hash(_slots));

@override
String toString() {
  return 'DaySchedule(isAvailable: $isAvailable, slots: $slots)';
}


}

/// @nodoc
abstract mixin class _$DayScheduleCopyWith<$Res> implements $DayScheduleCopyWith<$Res> {
  factory _$DayScheduleCopyWith(_DaySchedule value, $Res Function(_DaySchedule) _then) = __$DayScheduleCopyWithImpl;
@override @useResult
$Res call({
 bool isAvailable, List<TimeSlot> slots
});




}
/// @nodoc
class __$DayScheduleCopyWithImpl<$Res>
    implements _$DayScheduleCopyWith<$Res> {
  __$DayScheduleCopyWithImpl(this._self, this._then);

  final _DaySchedule _self;
  final $Res Function(_DaySchedule) _then;

/// Create a copy of DaySchedule
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isAvailable = null,Object? slots = null,}) {
  return _then(_DaySchedule(
isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,slots: null == slots ? _self._slots : slots // ignore: cast_nullable_to_non_nullable
as List<TimeSlot>,
  ));
}


}


/// @nodoc
mixin _$TimeSlot {

 String get id; String get startTime;// 24-hour format HH:mm
 String get endTime;// 24-hour format HH:mm
 int get maxPatients; int get bookedPatients; bool get isAvailable;
/// Create a copy of TimeSlot
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TimeSlotCopyWith<TimeSlot> get copyWith => _$TimeSlotCopyWithImpl<TimeSlot>(this as TimeSlot, _$identity);

  /// Serializes this TimeSlot to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TimeSlot&&(identical(other.id, id) || other.id == id)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime)&&(identical(other.maxPatients, maxPatients) || other.maxPatients == maxPatients)&&(identical(other.bookedPatients, bookedPatients) || other.bookedPatients == bookedPatients)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,startTime,endTime,maxPatients,bookedPatients,isAvailable);

@override
String toString() {
  return 'TimeSlot(id: $id, startTime: $startTime, endTime: $endTime, maxPatients: $maxPatients, bookedPatients: $bookedPatients, isAvailable: $isAvailable)';
}


}

/// @nodoc
abstract mixin class $TimeSlotCopyWith<$Res>  {
  factory $TimeSlotCopyWith(TimeSlot value, $Res Function(TimeSlot) _then) = _$TimeSlotCopyWithImpl;
@useResult
$Res call({
 String id, String startTime, String endTime, int maxPatients, int bookedPatients, bool isAvailable
});




}
/// @nodoc
class _$TimeSlotCopyWithImpl<$Res>
    implements $TimeSlotCopyWith<$Res> {
  _$TimeSlotCopyWithImpl(this._self, this._then);

  final TimeSlot _self;
  final $Res Function(TimeSlot) _then;

/// Create a copy of TimeSlot
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? startTime = null,Object? endTime = null,Object? maxPatients = null,Object? bookedPatients = null,Object? isAvailable = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,startTime: null == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as String,endTime: null == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as String,maxPatients: null == maxPatients ? _self.maxPatients : maxPatients // ignore: cast_nullable_to_non_nullable
as int,bookedPatients: null == bookedPatients ? _self.bookedPatients : bookedPatients // ignore: cast_nullable_to_non_nullable
as int,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [TimeSlot].
extension TimeSlotPatterns on TimeSlot {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TimeSlot value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TimeSlot() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TimeSlot value)  $default,){
final _that = this;
switch (_that) {
case _TimeSlot():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TimeSlot value)?  $default,){
final _that = this;
switch (_that) {
case _TimeSlot() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String startTime,  String endTime,  int maxPatients,  int bookedPatients,  bool isAvailable)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TimeSlot() when $default != null:
return $default(_that.id,_that.startTime,_that.endTime,_that.maxPatients,_that.bookedPatients,_that.isAvailable);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String startTime,  String endTime,  int maxPatients,  int bookedPatients,  bool isAvailable)  $default,) {final _that = this;
switch (_that) {
case _TimeSlot():
return $default(_that.id,_that.startTime,_that.endTime,_that.maxPatients,_that.bookedPatients,_that.isAvailable);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String startTime,  String endTime,  int maxPatients,  int bookedPatients,  bool isAvailable)?  $default,) {final _that = this;
switch (_that) {
case _TimeSlot() when $default != null:
return $default(_that.id,_that.startTime,_that.endTime,_that.maxPatients,_that.bookedPatients,_that.isAvailable);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TimeSlot implements TimeSlot {
  const _TimeSlot({required this.id, required this.startTime, required this.endTime, required this.maxPatients, this.bookedPatients = 0, this.isAvailable = true});
  factory _TimeSlot.fromJson(Map<String, dynamic> json) => _$TimeSlotFromJson(json);

@override final  String id;
@override final  String startTime;
// 24-hour format HH:mm
@override final  String endTime;
// 24-hour format HH:mm
@override final  int maxPatients;
@override@JsonKey() final  int bookedPatients;
@override@JsonKey() final  bool isAvailable;

/// Create a copy of TimeSlot
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TimeSlotCopyWith<_TimeSlot> get copyWith => __$TimeSlotCopyWithImpl<_TimeSlot>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TimeSlotToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TimeSlot&&(identical(other.id, id) || other.id == id)&&(identical(other.startTime, startTime) || other.startTime == startTime)&&(identical(other.endTime, endTime) || other.endTime == endTime)&&(identical(other.maxPatients, maxPatients) || other.maxPatients == maxPatients)&&(identical(other.bookedPatients, bookedPatients) || other.bookedPatients == bookedPatients)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,startTime,endTime,maxPatients,bookedPatients,isAvailable);

@override
String toString() {
  return 'TimeSlot(id: $id, startTime: $startTime, endTime: $endTime, maxPatients: $maxPatients, bookedPatients: $bookedPatients, isAvailable: $isAvailable)';
}


}

/// @nodoc
abstract mixin class _$TimeSlotCopyWith<$Res> implements $TimeSlotCopyWith<$Res> {
  factory _$TimeSlotCopyWith(_TimeSlot value, $Res Function(_TimeSlot) _then) = __$TimeSlotCopyWithImpl;
@override @useResult
$Res call({
 String id, String startTime, String endTime, int maxPatients, int bookedPatients, bool isAvailable
});




}
/// @nodoc
class __$TimeSlotCopyWithImpl<$Res>
    implements _$TimeSlotCopyWith<$Res> {
  __$TimeSlotCopyWithImpl(this._self, this._then);

  final _TimeSlot _self;
  final $Res Function(_TimeSlot) _then;

/// Create a copy of TimeSlot
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? startTime = null,Object? endTime = null,Object? maxPatients = null,Object? bookedPatients = null,Object? isAvailable = null,}) {
  return _then(_TimeSlot(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,startTime: null == startTime ? _self.startTime : startTime // ignore: cast_nullable_to_non_nullable
as String,endTime: null == endTime ? _self.endTime : endTime // ignore: cast_nullable_to_non_nullable
as String,maxPatients: null == maxPatients ? _self.maxPatients : maxPatients // ignore: cast_nullable_to_non_nullable
as int,bookedPatients: null == bookedPatients ? _self.bookedPatients : bookedPatients // ignore: cast_nullable_to_non_nullable
as int,isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
