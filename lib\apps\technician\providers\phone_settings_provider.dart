import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final phoneSettingsProvider =
    AsyncNotifierProvider<PhoneSettingsNotifier, String?>(() {
  return PhoneSettingsNotifier();
});

class PhoneSettingsNotifier extends AsyncNotifier<String?> {
  late final DatabaseService _databaseService;

  @override
  Future<String?> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadPhoneNumber();
  }

  Future<String?> _loadPhoneNumber() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('users', userId);
      return userData?['phone_number'] as String?;
    } catch (e) {
      debugPrint('Failed to load phone number: $e');
      throw Exception('Failed to load phone number: $e');
    }
  }

  Future<void> updatePhoneNumber(String phoneNumber) async {
    state = const AsyncLoading();

    try {
      final user = ref.read(authProvider);
      if (user == null) throw Exception('User not authenticated');

      // Validate phone number format
      if (phoneNumber.isEmpty) {
        throw Exception('Phone number cannot be empty');
      }

      // Update phone number in Supabase Auth
      await Supabase.instance.client.auth.updateUser(
        UserAttributes(
          phone: phoneNumber,
        ),
      );

      // Update phone number in database
      await _databaseService.update('users', user.id, {
        'phone_number': phoneNumber,
        'phone_verified': false, // Reset verification status
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData(phoneNumber);
    } catch (e) {
      debugPrint('Failed to update phone number: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> setSmsVerification(bool enabled) async {
    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      await _databaseService.update('users', userId, {
        'sms_verification_enabled': enabled,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to update SMS verification settings: $e');
      throw Exception('Failed to update SMS verification settings: $e');
    }
  }
}
