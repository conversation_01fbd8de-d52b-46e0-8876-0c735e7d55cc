import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:share_plus/share_plus.dart';
import 'package:csv/csv.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final dataManagementProvider =
    AsyncNotifierProvider<DataManagementNotifier, void>(() {
  return DataManagementNotifier();
});

class DataManagementNotifier extends AsyncNotifier<void> {
  late final DatabaseService _databaseService;

  @override
  Future<void> build() async {
    _databaseService = ref.read(databaseServiceProvider);
  }

  Future<String> downloadUserData() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      // Get user data from database
      final userData = await _databaseService.find('users', userId);

      // Get user's bookings
      final bookings = await _databaseService.getAll(
        'bookings',
        where: 'provider_id = ? AND is_deleted = ?',
        whereParams: [userId, false],
      );

      // Get user's reviews
      final reviews = await _databaseService.getAll(
        'reviews',
        where: 'technician_id = ? AND is_deleted = ?',
        whereParams: [userId, false],
      );

      // Combine all data
      final data = {
        'userData': userData,
        'bookings': bookings,
        'reviews': reviews,
        'exportedAt': DateTime.now().toIso8601String(),
      };

      // Save to local file
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/user_data.json');
      await file.writeAsString(jsonEncode(data));

      return file.path;
    } catch (e) {
      debugPrint('Failed to download data: $e');
      throw Exception('Failed to download data: $e');
    }
  }

  Future<void> deleteAccount() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      // Soft delete user data from database
      await _databaseService.update('users', userId, {
        'is_deleted': true,
        'deleted_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Sign out user
      await ref.read(authServiceProvider).signOut();
    } catch (e) {
      debugPrint('Failed to delete account: $e');
      throw Exception('Failed to delete account: $e');
    }
  }

  Future<void> clearActivityHistory() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      await _databaseService.update('users', userId, {
        'last_searches': [],
        'recent_views': [],
        'activity_history': [],
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to clear activity history: $e');
      throw Exception('Failed to clear activity history: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getConnectedApps() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final apps = await _databaseService.getAll(
        'user_connected_apps',
        where: 'user_id = ? AND is_deleted = ?',
        whereParams: [userId, false],
      );
      return apps;
    } catch (e) {
      debugPrint('Failed to get connected apps: $e');
      throw Exception('Failed to get connected apps: $e');
    }
  }

  Future<void> revokeAppAccess(String appId) async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      await _databaseService.update('user_connected_apps', appId, {
        'is_deleted': true,
        'deleted_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to revoke app access: $e');
      throw Exception('Failed to revoke app access: $e');
    }
  }

  Future<void> updateDataRetention(int days) async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      await _databaseService.update('users', userId, {
        'data_retention_days': days,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to update data retention: $e');
      throw Exception('Failed to update data retention: $e');
    }
  }

  Future<void> exportData(String collectionName) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Get data from database
      final data = await _databaseService.getAll(
        collectionName,
        where: 'user_id = ? AND is_deleted = ?',
        whereParams: [userId, false],
      );

      // Convert to CSV
      List<List<dynamic>> rows = [];

      // Add headers
      if (data.isNotEmpty) {
        rows.add(data.first.keys.toList());
      }

      // Add data rows
      for (var record in data) {
        rows.add(record.values.toList());
      }

      String csv = const ListToCsvConverter().convert(rows);

      // Get temporary directory
      final directory = await getTemporaryDirectory();
      final file = File('${directory.path}/$collectionName.csv');

      // Write to file
      await file.writeAsString(csv);

      // Share file
      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(file.path)],
          text: '$collectionName data export',
        ),
      );

      state = const AsyncData(null);
    } catch (e, st) {
      debugPrint('Error exporting data: $e');
      state = AsyncError(e, st);
    }
  }

  Future<void> clearData(String collectionName) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Get all documents for the user
      final data = await _databaseService.getAll(
        collectionName,
        where: 'user_id = ? AND is_deleted = ?',
        whereParams: [userId, false],
      );

      // Soft delete each document
      for (var record in data) {
        await _databaseService.update(collectionName, record['id'], {
          'is_deleted': true,
          'deleted_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }

      state = const AsyncData(null);
    } catch (e, st) {
      debugPrint('Error clearing data: $e');
      state = AsyncError(e, st);
    }
  }

  Future<void> backupData(String collectionName) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Get data from database
      final data = await _databaseService.getAll(
        collectionName,
        where: 'user_id = ? AND is_deleted = ?',
        whereParams: [userId, false],
      );

      // Create backup collection
      final backupCollection = '${collectionName}_backup';
      final timestamp = DateTime.now().toIso8601String();

      // Store each document in backup collection
      for (var record in data) {
        final backupData = {
          ...record,
          'backup_timestamp': timestamp,
          'original_id': record['id'],
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };
        backupData.remove('id'); // Remove original ID to generate new one
        await _databaseService.create(backupCollection, backupData);
      }

      state = const AsyncData(null);
    } catch (e, st) {
      debugPrint('Error backing up data: $e');
      state = AsyncError(e, st);
    }
  }

  Future<void> restoreData(String collectionName) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Get latest backup
      final backupCollection = '${collectionName}_backup';
      final backupData = await _databaseService.getAll(
        backupCollection,
        where: 'user_id = ? AND is_deleted = ?',
        whereParams: [userId, false],
        orderBy: 'backup_timestamp DESC',
        limit: 1,
      );

      if (backupData.isEmpty) {
        throw Exception('No backup found');
      }

      // Clear current data
      await clearData(collectionName);

      // Restore from backup
      for (var record in backupData) {
        final restoreData = Map<String, dynamic>.from(record);
        restoreData.remove('backup_timestamp'); // Remove backup timestamp
        restoreData.remove('original_id'); // Remove original ID reference
        restoreData['id'] = restoreData['original_id']; // Restore original ID
        restoreData['created_at'] = DateTime.now().toIso8601String();
        restoreData['updated_at'] = DateTime.now().toIso8601String();
        restoreData['is_deleted'] = false;

        await _databaseService.create(collectionName, restoreData);
      }

      state = const AsyncData(null);
    } catch (e, st) {
      debugPrint('Error restoring data: $e');
      state = AsyncError(e, st);
    }
  }
}
