// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_gateway_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$PaymentGatewayEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentGatewayEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PaymentGatewayEvent()';
}


}

/// @nodoc
class $PaymentGatewayEventCopyWith<$Res>  {
$PaymentGatewayEventCopyWith(PaymentGatewayEvent _, $Res Function(PaymentGatewayEvent) __);
}


/// Adds pattern-matching-related methods to [PaymentGatewayEvent].
extension PaymentGatewayEventPatterns on PaymentGatewayEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( LoadGateways value)?  loadGateways,TResult Function( CreatePaymentGateway value)?  createPaymentGateway,TResult Function( UpdatePaymentGateway value)?  updatePaymentGateway,TResult Function( DeletePaymentGateway value)?  deletePaymentGateway,TResult Function( UpdateGatewayStatus value)?  updateGatewayStatus,TResult Function( UpdateGatewayCredentials value)?  updateGatewayCredentials,TResult Function( UpdateGatewayFees value)?  updateGatewayFees,TResult Function( CalculateTransactionFee value)?  calculateTransactionFee,required TResult orElse(),}){
final _that = this;
switch (_that) {
case LoadGateways() when loadGateways != null:
return loadGateways(_that);case CreatePaymentGateway() when createPaymentGateway != null:
return createPaymentGateway(_that);case UpdatePaymentGateway() when updatePaymentGateway != null:
return updatePaymentGateway(_that);case DeletePaymentGateway() when deletePaymentGateway != null:
return deletePaymentGateway(_that);case UpdateGatewayStatus() when updateGatewayStatus != null:
return updateGatewayStatus(_that);case UpdateGatewayCredentials() when updateGatewayCredentials != null:
return updateGatewayCredentials(_that);case UpdateGatewayFees() when updateGatewayFees != null:
return updateGatewayFees(_that);case CalculateTransactionFee() when calculateTransactionFee != null:
return calculateTransactionFee(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( LoadGateways value)  loadGateways,required TResult Function( CreatePaymentGateway value)  createPaymentGateway,required TResult Function( UpdatePaymentGateway value)  updatePaymentGateway,required TResult Function( DeletePaymentGateway value)  deletePaymentGateway,required TResult Function( UpdateGatewayStatus value)  updateGatewayStatus,required TResult Function( UpdateGatewayCredentials value)  updateGatewayCredentials,required TResult Function( UpdateGatewayFees value)  updateGatewayFees,required TResult Function( CalculateTransactionFee value)  calculateTransactionFee,}){
final _that = this;
switch (_that) {
case LoadGateways():
return loadGateways(_that);case CreatePaymentGateway():
return createPaymentGateway(_that);case UpdatePaymentGateway():
return updatePaymentGateway(_that);case DeletePaymentGateway():
return deletePaymentGateway(_that);case UpdateGatewayStatus():
return updateGatewayStatus(_that);case UpdateGatewayCredentials():
return updateGatewayCredentials(_that);case UpdateGatewayFees():
return updateGatewayFees(_that);case CalculateTransactionFee():
return calculateTransactionFee(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( LoadGateways value)?  loadGateways,TResult? Function( CreatePaymentGateway value)?  createPaymentGateway,TResult? Function( UpdatePaymentGateway value)?  updatePaymentGateway,TResult? Function( DeletePaymentGateway value)?  deletePaymentGateway,TResult? Function( UpdateGatewayStatus value)?  updateGatewayStatus,TResult? Function( UpdateGatewayCredentials value)?  updateGatewayCredentials,TResult? Function( UpdateGatewayFees value)?  updateGatewayFees,TResult? Function( CalculateTransactionFee value)?  calculateTransactionFee,}){
final _that = this;
switch (_that) {
case LoadGateways() when loadGateways != null:
return loadGateways(_that);case CreatePaymentGateway() when createPaymentGateway != null:
return createPaymentGateway(_that);case UpdatePaymentGateway() when updatePaymentGateway != null:
return updatePaymentGateway(_that);case DeletePaymentGateway() when deletePaymentGateway != null:
return deletePaymentGateway(_that);case UpdateGatewayStatus() when updateGatewayStatus != null:
return updateGatewayStatus(_that);case UpdateGatewayCredentials() when updateGatewayCredentials != null:
return updateGatewayCredentials(_that);case UpdateGatewayFees() when updateGatewayFees != null:
return updateGatewayFees(_that);case CalculateTransactionFee() when calculateTransactionFee != null:
return calculateTransactionFee(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  loadGateways,TResult Function( PaymentGateway gateway)?  createPaymentGateway,TResult Function( PaymentGateway gateway)?  updatePaymentGateway,TResult Function( String id)?  deletePaymentGateway,TResult Function( String id,  bool isActive)?  updateGatewayStatus,TResult Function( String id,  Map<String, dynamic> credentials)?  updateGatewayCredentials,TResult Function( String id,  Map<String, dynamic> fees)?  updateGatewayFees,TResult Function( String id,  double amount)?  calculateTransactionFee,required TResult orElse(),}) {final _that = this;
switch (_that) {
case LoadGateways() when loadGateways != null:
return loadGateways();case CreatePaymentGateway() when createPaymentGateway != null:
return createPaymentGateway(_that.gateway);case UpdatePaymentGateway() when updatePaymentGateway != null:
return updatePaymentGateway(_that.gateway);case DeletePaymentGateway() when deletePaymentGateway != null:
return deletePaymentGateway(_that.id);case UpdateGatewayStatus() when updateGatewayStatus != null:
return updateGatewayStatus(_that.id,_that.isActive);case UpdateGatewayCredentials() when updateGatewayCredentials != null:
return updateGatewayCredentials(_that.id,_that.credentials);case UpdateGatewayFees() when updateGatewayFees != null:
return updateGatewayFees(_that.id,_that.fees);case CalculateTransactionFee() when calculateTransactionFee != null:
return calculateTransactionFee(_that.id,_that.amount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  loadGateways,required TResult Function( PaymentGateway gateway)  createPaymentGateway,required TResult Function( PaymentGateway gateway)  updatePaymentGateway,required TResult Function( String id)  deletePaymentGateway,required TResult Function( String id,  bool isActive)  updateGatewayStatus,required TResult Function( String id,  Map<String, dynamic> credentials)  updateGatewayCredentials,required TResult Function( String id,  Map<String, dynamic> fees)  updateGatewayFees,required TResult Function( String id,  double amount)  calculateTransactionFee,}) {final _that = this;
switch (_that) {
case LoadGateways():
return loadGateways();case CreatePaymentGateway():
return createPaymentGateway(_that.gateway);case UpdatePaymentGateway():
return updatePaymentGateway(_that.gateway);case DeletePaymentGateway():
return deletePaymentGateway(_that.id);case UpdateGatewayStatus():
return updateGatewayStatus(_that.id,_that.isActive);case UpdateGatewayCredentials():
return updateGatewayCredentials(_that.id,_that.credentials);case UpdateGatewayFees():
return updateGatewayFees(_that.id,_that.fees);case CalculateTransactionFee():
return calculateTransactionFee(_that.id,_that.amount);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  loadGateways,TResult? Function( PaymentGateway gateway)?  createPaymentGateway,TResult? Function( PaymentGateway gateway)?  updatePaymentGateway,TResult? Function( String id)?  deletePaymentGateway,TResult? Function( String id,  bool isActive)?  updateGatewayStatus,TResult? Function( String id,  Map<String, dynamic> credentials)?  updateGatewayCredentials,TResult? Function( String id,  Map<String, dynamic> fees)?  updateGatewayFees,TResult? Function( String id,  double amount)?  calculateTransactionFee,}) {final _that = this;
switch (_that) {
case LoadGateways() when loadGateways != null:
return loadGateways();case CreatePaymentGateway() when createPaymentGateway != null:
return createPaymentGateway(_that.gateway);case UpdatePaymentGateway() when updatePaymentGateway != null:
return updatePaymentGateway(_that.gateway);case DeletePaymentGateway() when deletePaymentGateway != null:
return deletePaymentGateway(_that.id);case UpdateGatewayStatus() when updateGatewayStatus != null:
return updateGatewayStatus(_that.id,_that.isActive);case UpdateGatewayCredentials() when updateGatewayCredentials != null:
return updateGatewayCredentials(_that.id,_that.credentials);case UpdateGatewayFees() when updateGatewayFees != null:
return updateGatewayFees(_that.id,_that.fees);case CalculateTransactionFee() when calculateTransactionFee != null:
return calculateTransactionFee(_that.id,_that.amount);case _:
  return null;

}
}

}

/// @nodoc


class LoadGateways implements PaymentGatewayEvent {
  const LoadGateways();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadGateways);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PaymentGatewayEvent.loadGateways()';
}


}




/// @nodoc


class CreatePaymentGateway implements PaymentGatewayEvent {
  const CreatePaymentGateway(this.gateway);
  

 final  PaymentGateway gateway;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreatePaymentGatewayCopyWith<CreatePaymentGateway> get copyWith => _$CreatePaymentGatewayCopyWithImpl<CreatePaymentGateway>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreatePaymentGateway&&(identical(other.gateway, gateway) || other.gateway == gateway));
}


@override
int get hashCode => Object.hash(runtimeType,gateway);

@override
String toString() {
  return 'PaymentGatewayEvent.createPaymentGateway(gateway: $gateway)';
}


}

/// @nodoc
abstract mixin class $CreatePaymentGatewayCopyWith<$Res> implements $PaymentGatewayEventCopyWith<$Res> {
  factory $CreatePaymentGatewayCopyWith(CreatePaymentGateway value, $Res Function(CreatePaymentGateway) _then) = _$CreatePaymentGatewayCopyWithImpl;
@useResult
$Res call({
 PaymentGateway gateway
});


$PaymentGatewayCopyWith<$Res> get gateway;

}
/// @nodoc
class _$CreatePaymentGatewayCopyWithImpl<$Res>
    implements $CreatePaymentGatewayCopyWith<$Res> {
  _$CreatePaymentGatewayCopyWithImpl(this._self, this._then);

  final CreatePaymentGateway _self;
  final $Res Function(CreatePaymentGateway) _then;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? gateway = null,}) {
  return _then(CreatePaymentGateway(
null == gateway ? _self.gateway : gateway // ignore: cast_nullable_to_non_nullable
as PaymentGateway,
  ));
}

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentGatewayCopyWith<$Res> get gateway {
  
  return $PaymentGatewayCopyWith<$Res>(_self.gateway, (value) {
    return _then(_self.copyWith(gateway: value));
  });
}
}

/// @nodoc


class UpdatePaymentGateway implements PaymentGatewayEvent {
  const UpdatePaymentGateway(this.gateway);
  

 final  PaymentGateway gateway;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdatePaymentGatewayCopyWith<UpdatePaymentGateway> get copyWith => _$UpdatePaymentGatewayCopyWithImpl<UpdatePaymentGateway>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdatePaymentGateway&&(identical(other.gateway, gateway) || other.gateway == gateway));
}


@override
int get hashCode => Object.hash(runtimeType,gateway);

@override
String toString() {
  return 'PaymentGatewayEvent.updatePaymentGateway(gateway: $gateway)';
}


}

/// @nodoc
abstract mixin class $UpdatePaymentGatewayCopyWith<$Res> implements $PaymentGatewayEventCopyWith<$Res> {
  factory $UpdatePaymentGatewayCopyWith(UpdatePaymentGateway value, $Res Function(UpdatePaymentGateway) _then) = _$UpdatePaymentGatewayCopyWithImpl;
@useResult
$Res call({
 PaymentGateway gateway
});


$PaymentGatewayCopyWith<$Res> get gateway;

}
/// @nodoc
class _$UpdatePaymentGatewayCopyWithImpl<$Res>
    implements $UpdatePaymentGatewayCopyWith<$Res> {
  _$UpdatePaymentGatewayCopyWithImpl(this._self, this._then);

  final UpdatePaymentGateway _self;
  final $Res Function(UpdatePaymentGateway) _then;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? gateway = null,}) {
  return _then(UpdatePaymentGateway(
null == gateway ? _self.gateway : gateway // ignore: cast_nullable_to_non_nullable
as PaymentGateway,
  ));
}

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentGatewayCopyWith<$Res> get gateway {
  
  return $PaymentGatewayCopyWith<$Res>(_self.gateway, (value) {
    return _then(_self.copyWith(gateway: value));
  });
}
}

/// @nodoc


class DeletePaymentGateway implements PaymentGatewayEvent {
  const DeletePaymentGateway(this.id);
  

 final  String id;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DeletePaymentGatewayCopyWith<DeletePaymentGateway> get copyWith => _$DeletePaymentGatewayCopyWithImpl<DeletePaymentGateway>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DeletePaymentGateway&&(identical(other.id, id) || other.id == id));
}


@override
int get hashCode => Object.hash(runtimeType,id);

@override
String toString() {
  return 'PaymentGatewayEvent.deletePaymentGateway(id: $id)';
}


}

/// @nodoc
abstract mixin class $DeletePaymentGatewayCopyWith<$Res> implements $PaymentGatewayEventCopyWith<$Res> {
  factory $DeletePaymentGatewayCopyWith(DeletePaymentGateway value, $Res Function(DeletePaymentGateway) _then) = _$DeletePaymentGatewayCopyWithImpl;
@useResult
$Res call({
 String id
});




}
/// @nodoc
class _$DeletePaymentGatewayCopyWithImpl<$Res>
    implements $DeletePaymentGatewayCopyWith<$Res> {
  _$DeletePaymentGatewayCopyWithImpl(this._self, this._then);

  final DeletePaymentGateway _self;
  final $Res Function(DeletePaymentGateway) _then;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,}) {
  return _then(DeletePaymentGateway(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class UpdateGatewayStatus implements PaymentGatewayEvent {
  const UpdateGatewayStatus(this.id, this.isActive);
  

 final  String id;
 final  bool isActive;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateGatewayStatusCopyWith<UpdateGatewayStatus> get copyWith => _$UpdateGatewayStatusCopyWithImpl<UpdateGatewayStatus>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateGatewayStatus&&(identical(other.id, id) || other.id == id)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}


@override
int get hashCode => Object.hash(runtimeType,id,isActive);

@override
String toString() {
  return 'PaymentGatewayEvent.updateGatewayStatus(id: $id, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class $UpdateGatewayStatusCopyWith<$Res> implements $PaymentGatewayEventCopyWith<$Res> {
  factory $UpdateGatewayStatusCopyWith(UpdateGatewayStatus value, $Res Function(UpdateGatewayStatus) _then) = _$UpdateGatewayStatusCopyWithImpl;
@useResult
$Res call({
 String id, bool isActive
});




}
/// @nodoc
class _$UpdateGatewayStatusCopyWithImpl<$Res>
    implements $UpdateGatewayStatusCopyWith<$Res> {
  _$UpdateGatewayStatusCopyWithImpl(this._self, this._then);

  final UpdateGatewayStatus _self;
  final $Res Function(UpdateGatewayStatus) _then;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? isActive = null,}) {
  return _then(UpdateGatewayStatus(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class UpdateGatewayCredentials implements PaymentGatewayEvent {
  const UpdateGatewayCredentials(this.id, final  Map<String, dynamic> credentials): _credentials = credentials;
  

 final  String id;
 final  Map<String, dynamic> _credentials;
 Map<String, dynamic> get credentials {
  if (_credentials is EqualUnmodifiableMapView) return _credentials;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_credentials);
}


/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateGatewayCredentialsCopyWith<UpdateGatewayCredentials> get copyWith => _$UpdateGatewayCredentialsCopyWithImpl<UpdateGatewayCredentials>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateGatewayCredentials&&(identical(other.id, id) || other.id == id)&&const DeepCollectionEquality().equals(other._credentials, _credentials));
}


@override
int get hashCode => Object.hash(runtimeType,id,const DeepCollectionEquality().hash(_credentials));

@override
String toString() {
  return 'PaymentGatewayEvent.updateGatewayCredentials(id: $id, credentials: $credentials)';
}


}

/// @nodoc
abstract mixin class $UpdateGatewayCredentialsCopyWith<$Res> implements $PaymentGatewayEventCopyWith<$Res> {
  factory $UpdateGatewayCredentialsCopyWith(UpdateGatewayCredentials value, $Res Function(UpdateGatewayCredentials) _then) = _$UpdateGatewayCredentialsCopyWithImpl;
@useResult
$Res call({
 String id, Map<String, dynamic> credentials
});




}
/// @nodoc
class _$UpdateGatewayCredentialsCopyWithImpl<$Res>
    implements $UpdateGatewayCredentialsCopyWith<$Res> {
  _$UpdateGatewayCredentialsCopyWithImpl(this._self, this._then);

  final UpdateGatewayCredentials _self;
  final $Res Function(UpdateGatewayCredentials) _then;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? credentials = null,}) {
  return _then(UpdateGatewayCredentials(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == credentials ? _self._credentials : credentials // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

/// @nodoc


class UpdateGatewayFees implements PaymentGatewayEvent {
  const UpdateGatewayFees(this.id, final  Map<String, dynamic> fees): _fees = fees;
  

 final  String id;
 final  Map<String, dynamic> _fees;
 Map<String, dynamic> get fees {
  if (_fees is EqualUnmodifiableMapView) return _fees;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_fees);
}


/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateGatewayFeesCopyWith<UpdateGatewayFees> get copyWith => _$UpdateGatewayFeesCopyWithImpl<UpdateGatewayFees>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateGatewayFees&&(identical(other.id, id) || other.id == id)&&const DeepCollectionEquality().equals(other._fees, _fees));
}


@override
int get hashCode => Object.hash(runtimeType,id,const DeepCollectionEquality().hash(_fees));

@override
String toString() {
  return 'PaymentGatewayEvent.updateGatewayFees(id: $id, fees: $fees)';
}


}

/// @nodoc
abstract mixin class $UpdateGatewayFeesCopyWith<$Res> implements $PaymentGatewayEventCopyWith<$Res> {
  factory $UpdateGatewayFeesCopyWith(UpdateGatewayFees value, $Res Function(UpdateGatewayFees) _then) = _$UpdateGatewayFeesCopyWithImpl;
@useResult
$Res call({
 String id, Map<String, dynamic> fees
});




}
/// @nodoc
class _$UpdateGatewayFeesCopyWithImpl<$Res>
    implements $UpdateGatewayFeesCopyWith<$Res> {
  _$UpdateGatewayFeesCopyWithImpl(this._self, this._then);

  final UpdateGatewayFees _self;
  final $Res Function(UpdateGatewayFees) _then;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? fees = null,}) {
  return _then(UpdateGatewayFees(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == fees ? _self._fees : fees // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

/// @nodoc


class CalculateTransactionFee implements PaymentGatewayEvent {
  const CalculateTransactionFee(this.id, this.amount);
  

 final  String id;
 final  double amount;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CalculateTransactionFeeCopyWith<CalculateTransactionFee> get copyWith => _$CalculateTransactionFeeCopyWithImpl<CalculateTransactionFee>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CalculateTransactionFee&&(identical(other.id, id) || other.id == id)&&(identical(other.amount, amount) || other.amount == amount));
}


@override
int get hashCode => Object.hash(runtimeType,id,amount);

@override
String toString() {
  return 'PaymentGatewayEvent.calculateTransactionFee(id: $id, amount: $amount)';
}


}

/// @nodoc
abstract mixin class $CalculateTransactionFeeCopyWith<$Res> implements $PaymentGatewayEventCopyWith<$Res> {
  factory $CalculateTransactionFeeCopyWith(CalculateTransactionFee value, $Res Function(CalculateTransactionFee) _then) = _$CalculateTransactionFeeCopyWithImpl;
@useResult
$Res call({
 String id, double amount
});




}
/// @nodoc
class _$CalculateTransactionFeeCopyWithImpl<$Res>
    implements $CalculateTransactionFeeCopyWith<$Res> {
  _$CalculateTransactionFeeCopyWithImpl(this._self, this._then);

  final CalculateTransactionFee _self;
  final $Res Function(CalculateTransactionFee) _then;

/// Create a copy of PaymentGatewayEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? amount = null,}) {
  return _then(CalculateTransactionFee(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
