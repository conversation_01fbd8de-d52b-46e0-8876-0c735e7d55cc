import '../../../../shared/models/shopping/shopping_list_model.dart';
import '../services/shopping_list_service.dart';

/// Repository for managing shopping lists for sellers
class SellerShoppingListRepository {
  final SellerShoppingListService _service;

  /// Creates a [SellerShoppingListRepository]
  SellerShoppingListRepository(this._service);

  /// Get a shopping list by ID
  Stream<ShoppingListModel?> getShoppingList(String listId) {
    return _service.getShoppingList(listId);
  }

  /// Get all shopping lists requested from this seller
  Stream<List<ShoppingListModel>> getRequestedShoppingLists(String sellerId) {
    return _service.getRequestedShoppingLists(sellerId);
  }

  /// Update a shopping list with pricing information
  Future<void> updateShoppingList(ShoppingListModel list) async {
    await _service.updateShoppingList(list);
  }
}
