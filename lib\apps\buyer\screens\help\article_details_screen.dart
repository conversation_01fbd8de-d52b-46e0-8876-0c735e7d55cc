import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:markdown_widget/markdown_widget.dart';
import '../../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../../shared/ui_components/errors/error_message.dart';
import '../../providers/help/article_provider.dart';

class ArticleDetailsScreen extends ConsumerStatefulWidget {
  final String articleId;

  const ArticleDetailsScreen({
    super.key,
    required this.articleId,
  });

  @override
  ConsumerState<ArticleDetailsScreen> createState() =>
      _ArticleDetailsScreenState();
}

class _ArticleDetailsScreenState extends ConsumerState<ArticleDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final articleAsync = ref.watch(articleProvider(widget.articleId));
    final articleState = ref.watch(articleStateProvider);

    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            title: const Text('Article'),
            actions: [
              IconButton(
                icon: articleAsync.when(
                  data: (article) => Icon(
                    article?.isBookmarked ?? false
                        ? Icons.bookmark
                        : Icons.bookmark_border,
                  ),
                  loading: () => const Icon(Icons.bookmark_border),
                  error: (_, __) => const Icon(Icons.bookmark_border),
                ),
                onPressed: () {
                  articleAsync.whenData((article) {
                    if (article == null) return;
                    if (article.isBookmarked) {
                      ref
                          .read(articleStateProvider.notifier)
                          .unbookmarkArticle(article.id);
                    } else {
                      ref
                          .read(articleStateProvider.notifier)
                          .bookmarkArticle(article.id);
                    }
                  });
                },
              ),
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: () {
                  articleAsync.whenData((article) {
                    if (article == null) return;
                    ref
                        .read(articleStateProvider.notifier)
                        .shareArticle(article.id);
                  });
                },
              ),
            ],
          ),
          body: articleAsync.when(
            data: (article) {
              if (article == null) {
                return const Center(
                  child: Text('Article not found'),
                );
              }

              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      article.title,
                      style: theme.textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            article.category,
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.access_time,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          article.readTime,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    MarkdownWidget(
                      data: article.content,
                      config: MarkdownConfig(configs: [
                        LinkConfig(
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ]),
                    ),
                    if (article.relatedArticles?.isNotEmpty ?? false) ...[
                      const SizedBox(height: 32),
                      Text(
                        'Related Articles',
                        style: theme.textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: article.relatedArticles!.length,
                        itemBuilder: (context, index) {
                          final relatedArticle =
                              article.relatedArticles![index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 16),
                            child: InkWell(
                              onTap: () {
                                context.push(
                                  '/help/articles/${relatedArticle.id}',
                                );
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      relatedArticle.title,
                                      style: theme.textTheme.titleMedium,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      relatedArticle.description,
                                      style:
                                          theme.textTheme.bodyMedium?.copyWith(
                                        color:
                                            theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.access_time,
                                          size: 16,
                                          color: theme
                                              .colorScheme.onSurfaceVariant,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          relatedArticle.readTime,
                                          style: theme.textTheme.bodySmall
                                              ?.copyWith(
                                            color: theme
                                                .colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ],
                ),
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stackTrace) => Center(
              child: ErrorMessage(
                message: error.toString(),
                onRetry: () {
                  ref.invalidate(articleProvider(widget.articleId));
                },
              ),
            ),
          ),
        ),
        if (articleState.isLoading) LoadingIndicator.overlay(),
      ],
    );
  }
}
