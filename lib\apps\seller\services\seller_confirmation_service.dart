import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import '../../../shared/widgets/dialogs/confirmation_dialog.dart';

/// Seller-specific override for the confirmation service
/// This ensures that the seller app shows the confirmation dialog
/// without affecting other app flavors
class SellerConfirmationService {
  static const String _confirmationKey = 'seller_sanathana_dharma_confirmed';
  static const String _confirmationDateKey =
      'seller_sanathana_dharma_confirmed_date';
  static const int _confirmationValidityDays = 30; // Reconfirm every 30 days

  /// Checks if the user has already confirmed
  static Future<bool> hasConfirmed() async {
    // Get the confirmation status from SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final isConfirmed = prefs.getBool(_confirmationKey) ?? false;

    debugPrint('SellerConfirmationService: hasConfirmed() = $isConfirmed');

    // Check if the confirmation has expired
    if (isConfirmed) {
      final confirmationDateStr = prefs.getString(_confirmationDateKey);
      if (confirmationDateStr == null) {
        debugPrint('SellerConfirmationService: No confirmation date found');
        return false;
      }

      try {
        final confirmationDate = DateTime.parse(confirmationDateStr);
        final now = DateTime.now();
        final difference = now.difference(confirmationDate).inDays;

        debugPrint(
            'SellerConfirmationService: Days since confirmation: $difference');

        // If the confirmation is older than the validity period, it's expired
        if (difference > _confirmationValidityDays) {
          debugPrint('SellerConfirmationService: Confirmation expired');
          return false;
        }

        debugPrint('SellerConfirmationService: Confirmation is valid');
        return true;
      } catch (e) {
        debugPrint('SellerConfirmationService: Error parsing date: $e');
        return false;
      }
    }

    return false;
  }

  /// Saves the user's confirmation
  static Future<void> saveConfirmation(bool confirmed) async {
    debugPrint('SellerConfirmationService: saveConfirmation($confirmed)');
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_confirmationKey, confirmed);

    if (confirmed) {
      // Save the confirmation date
      final now = DateTime.now().toIso8601String();
      await prefs.setString(_confirmationDateKey, now);
      debugPrint('SellerConfirmationService: Saved confirmation date: $now');
    }

    // Verify the save was successful
    final savedValue = prefs.getBool(_confirmationKey);
    debugPrint(
        'SellerConfirmationService: Verification - saved value is $savedValue');
  }

  /// Shows the confirmation dialog if needed
  /// Returns true if the user has confirmed, false otherwise
  static Future<bool> checkConfirmation(BuildContext context) async {
    debugPrint('SellerConfirmationService: checkConfirmation() called');

    // Check if the user has already confirmed
    final hasAlreadyConfirmed = await hasConfirmed();
    if (hasAlreadyConfirmed) {
      debugPrint('SellerConfirmationService: User already confirmed');
      return true;
    }

    debugPrint('SellerConfirmationService: Showing confirmation dialog');
    try {
      // Show the confirmation dialog directly
      final confirmed = await SanathanaDharmaConfirmationDialog.show(context);
      debugPrint('SellerConfirmationService: Dialog result: $confirmed');

      // If the user confirmed, save the confirmation
      if (confirmed == true) {
        debugPrint(
            'SellerConfirmationService: User confirmed, saving confirmation');

        // Save the confirmation with a retry mechanism
        bool saveSuccess = false;
        for (int i = 0; i < 3; i++) {
          try {
            await saveConfirmation(true);

            // Force a small delay to ensure the confirmation is saved
            await Future.delayed(const Duration(milliseconds: 300));

            // Verify the confirmation was saved
            final verifyConfirmed = await hasConfirmed();
            debugPrint(
                'SellerConfirmationService: Verification attempt ${i + 1} - hasConfirmed() = $verifyConfirmed');

            if (verifyConfirmed) {
              saveSuccess = true;
              break;
            }
          } catch (e) {
            debugPrint(
                'SellerConfirmationService: Error saving confirmation (attempt ${i + 1}): $e');
          }
        }

        if (!saveSuccess) {
          debugPrint(
              'SellerConfirmationService: WARNING - Failed to save confirmation after 3 attempts');
        } else {
          debugPrint(
              'SellerConfirmationService: Confirmation saved successfully');
        }

        // Force another small delay to ensure the UI updates properly
        await Future.delayed(const Duration(milliseconds: 300));

        return true;
      } else {
        // If the user rejected, exit the app
        debugPrint('SellerConfirmationService: User rejected, exiting app');
        SystemNavigator.pop();
        return false;
      }
    } catch (e) {
      debugPrint('SellerConfirmationService: Error showing dialog: $e');
      return false;
    }
  }
}
