import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';
import 'package:shivish/apps/technician/providers/invoice_settings_provider.dart';

class InvoiceSettingsScreen extends ConsumerStatefulWidget {
  const InvoiceSettingsScreen({super.key});

  @override
  ConsumerState<InvoiceSettingsScreen> createState() =>
      _InvoiceSettingsScreenState();
}

class _InvoiceSettingsScreenState extends ConsumerState<InvoiceSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _invoicePrefixController;
  late TextEditingController _invoiceNumberController;
  late TextEditingController _invoiceFooterController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _invoicePrefixController = TextEditingController();
    _invoiceNumberController = TextEditingController();
    _invoiceFooterController = TextEditingController();
    _loadInvoiceSettings();
  }

  @override
  void dispose() {
    _invoicePrefixController.dispose();
    _invoiceNumberController.dispose();
    _invoiceFooterController.dispose();
    super.dispose();
  }

  Future<void> _loadInvoiceSettings() async {
    final settings = await ref.read(invoiceSettingsProvider.future);
    if (mounted) {
      setState(() {
        _invoicePrefixController.text = settings['prefix'] ?? '';
        _invoiceNumberController.text = settings['number'] ?? '1';
        _invoiceFooterController.text = settings['footer'] ?? '';
      });
    }
  }

  Future<void> _updateInvoiceSettings() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      await ref.read(invoiceSettingsProvider.notifier).saveInvoiceSettings(
            prefix: _invoicePrefixController.text,
            number: _invoiceNumberController.text,
            footer: _invoiceFooterController.text,
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invoice settings updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update invoice settings: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final invoiceState = ref.watch(invoiceSettingsProvider);

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Invoice Settings',
      ),
      body: invoiceState.when(
        data: (settings) => SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Invoice Configuration',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        AppTextField(
                          controller: _invoicePrefixController,
                          label: 'Invoice Prefix',
                          hint: 'Enter invoice prefix',
                          prefixIcon: const Icon(Icons.receipt),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter an invoice prefix';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        AppTextField(
                          controller: _invoiceNumberController,
                          label: 'Starting Invoice Number',
                          hint: 'Enter starting invoice number',
                          prefixIcon: const Icon(Icons.numbers),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a starting invoice number';
                            }
                            if (int.tryParse(value) == null) {
                              return 'Please enter a valid number';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        AppTextField(
                          controller: _invoiceFooterController,
                          label: 'Invoice Footer',
                          hint: 'Enter invoice footer text',
                          prefixIcon: const Icon(Icons.text_fields),
                          maxLines: 3,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter invoice footer text';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                AppButton(
                  onPressed: _isLoading ? null : _updateInvoiceSettings,
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : const Text('Update Invoice Settings'),
                ),
              ],
            ),
          ),
        ),
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }
}
