import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/booking/booking_model.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/booking_provider.dart';
import 'technician_review_screen.dart';

class TechnicianBookingDetailsScreen extends ConsumerWidget {
  final String bookingId;

  const TechnicianBookingDetailsScreen({
    super.key,
    required this.bookingId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    final user = authState;
    if (user == null) {
      return const Center(
          child: Text('Please sign in to view booking details'));
    }

    final bookingsAsync = ref.watch(bookingStateNotifierProvider(user.id));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Details'),
      ),
      body: bookingsAsync.when(
        data: (bookings) {
          final booking = bookings.firstWhere(
            (b) => b.id == bookingId,
            orElse: () => throw Exception('Booking not found'),
          );

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBookingHeader(context, booking),
                const SizedBox(height: 24),
                _buildServiceDetails(context, booking),
                const SizedBox(height: 24),
                _buildLocationDetails(context, booking),
                const SizedBox(height: 24),
                _buildContactDetails(context, booking),
                const SizedBox(height: 24),
                _buildPaymentDetails(context, booking),
                const SizedBox(height: 24),
                _buildBookingActions(context, booking),
              ],
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildBookingHeader(BuildContext context, BookingModel booking) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Booking #${booking.bookingNumber}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                _buildStatusChip(context, booking.status),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Date: ${_formatDate(booking.bookingDate)}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 4),
            Text(
              'Time: ${_formatTime(booking.startTime)} - ${_formatTime(booking.endTime)}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceDetails(BuildContext context, BookingModel booking) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Service Details',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Services: ${booking.services.join(", ")}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            if (booking.notes != null && booking.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Notes: ${booking.notes}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLocationDetails(BuildContext context, BookingModel booking) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Location Details',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Address: ${booking.serviceLocation.street}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            Text(
              '${booking.serviceLocation.city}, ${booking.serviceLocation.state}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              '${booking.serviceLocation.country} ${booking.serviceLocation.postalCode}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactDetails(BuildContext context, BookingModel booking) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contact Details',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Provider: ${booking.providerName}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            Text(
              'Phone: ${booking.providerPhone ?? "Not available"}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'Email: ${booking.providerEmail ?? "Not available"}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetails(BuildContext context, BookingModel booking) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Details',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Subtotal',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                Text(
                  '₹${booking.subtotalAmount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tax',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                Text(
                  '₹${booking.taxAmount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
            if (booking.discountAmount > 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Discount',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  Text(
                    '-₹${booking.discountAmount.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ],
              ),
            const Divider(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                Text(
                  '₹${booking.totalAmount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 8),
            _buildPaymentStatusChip(context, booking.paymentStatus),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingActions(BuildContext context, BookingModel booking) {
    if (booking.status != BookingStatus.completed ||
        booking.paymentStatus != PaymentStatus.completed) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Booking Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => TechnicianReviewScreen(
                        bookingId: booking.id,
                      ),
                    ),
                  );
                },
                icon: const Icon(Icons.star),
                label: const Text('Add Review'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, BookingStatus status) {
    Color color;
    String text;

    switch (status) {
      case BookingStatus.pending:
        color = Colors.orange;
        text = 'Pending';
        break;
      case BookingStatus.confirmed:
        color = Colors.blue;
        text = 'Confirmed';
        break;
      case BookingStatus.inProgress:
        color = Colors.purple;
        text = 'In Progress';
        break;
      case BookingStatus.completed:
        color = Colors.green;
        text = 'Completed';
        break;
      case BookingStatus.cancelled:
        color = Colors.red;
        text = 'Cancelled';
        break;
      case BookingStatus.rejected:
        color = Colors.red;
        text = 'Rejected';
        break;
      case BookingStatus.rescheduled:
        color = Colors.orange;
        text = 'Rescheduled';
        break;
      case BookingStatus.noShow:
        color = Colors.red;
        text = 'No Show';
        break;
    }

    return Chip(
      label: Text(
        text,
        style: const TextStyle(color: Colors.white),
      ),
      backgroundColor: color,
    );
  }

  Widget _buildPaymentStatusChip(BuildContext context, PaymentStatus status) {
    Color color;
    String text;

    switch (status) {
      case PaymentStatus.pending:
        color = Colors.orange;
        text = 'Pending';
        break;
      case PaymentStatus.completed:
        color = Colors.green;
        text = 'Completed';
        break;
      case PaymentStatus.failed:
        color = Colors.red;
        text = 'Failed';
        break;
      case PaymentStatus.refunded:
        color = Colors.blue;
        text = 'Refunded';
        break;
      case PaymentStatus.partiallyRefunded:
        color = Colors.blue;
        text = 'Partially Refunded';
        break;
    }

    return Chip(
      label: Text(
        text,
        style: const TextStyle(color: Colors.white),
      ),
      backgroundColor: color,
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
