// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'upi_payment_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UpiPaymentSettings {

 String get upiId; bool get isVerified; bool get isEnabled; DateTime? get verifiedAt; DateTime? get updatedAt;
/// Create a copy of UpiPaymentSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpiPaymentSettingsCopyWith<UpiPaymentSettings> get copyWith => _$UpiPaymentSettingsCopyWithImpl<UpiPaymentSettings>(this as UpiPaymentSettings, _$identity);

  /// Serializes this UpiPaymentSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpiPaymentSettings&&(identical(other.upiId, upiId) || other.upiId == upiId)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.verifiedAt, verifiedAt) || other.verifiedAt == verifiedAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,upiId,isVerified,isEnabled,verifiedAt,updatedAt);

@override
String toString() {
  return 'UpiPaymentSettings(upiId: $upiId, isVerified: $isVerified, isEnabled: $isEnabled, verifiedAt: $verifiedAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $UpiPaymentSettingsCopyWith<$Res>  {
  factory $UpiPaymentSettingsCopyWith(UpiPaymentSettings value, $Res Function(UpiPaymentSettings) _then) = _$UpiPaymentSettingsCopyWithImpl;
@useResult
$Res call({
 String upiId, bool isVerified, bool isEnabled, DateTime? verifiedAt, DateTime? updatedAt
});




}
/// @nodoc
class _$UpiPaymentSettingsCopyWithImpl<$Res>
    implements $UpiPaymentSettingsCopyWith<$Res> {
  _$UpiPaymentSettingsCopyWithImpl(this._self, this._then);

  final UpiPaymentSettings _self;
  final $Res Function(UpiPaymentSettings) _then;

/// Create a copy of UpiPaymentSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? upiId = null,Object? isVerified = null,Object? isEnabled = null,Object? verifiedAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_self.copyWith(
upiId: null == upiId ? _self.upiId : upiId // ignore: cast_nullable_to_non_nullable
as String,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,verifiedAt: freezed == verifiedAt ? _self.verifiedAt : verifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [UpiPaymentSettings].
extension UpiPaymentSettingsPatterns on UpiPaymentSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UpiPaymentSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UpiPaymentSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UpiPaymentSettings value)  $default,){
final _that = this;
switch (_that) {
case _UpiPaymentSettings():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UpiPaymentSettings value)?  $default,){
final _that = this;
switch (_that) {
case _UpiPaymentSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String upiId,  bool isVerified,  bool isEnabled,  DateTime? verifiedAt,  DateTime? updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UpiPaymentSettings() when $default != null:
return $default(_that.upiId,_that.isVerified,_that.isEnabled,_that.verifiedAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String upiId,  bool isVerified,  bool isEnabled,  DateTime? verifiedAt,  DateTime? updatedAt)  $default,) {final _that = this;
switch (_that) {
case _UpiPaymentSettings():
return $default(_that.upiId,_that.isVerified,_that.isEnabled,_that.verifiedAt,_that.updatedAt);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String upiId,  bool isVerified,  bool isEnabled,  DateTime? verifiedAt,  DateTime? updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _UpiPaymentSettings() when $default != null:
return $default(_that.upiId,_that.isVerified,_that.isEnabled,_that.verifiedAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UpiPaymentSettings implements UpiPaymentSettings {
  const _UpiPaymentSettings({required this.upiId, this.isVerified = false, this.isEnabled = true, this.verifiedAt, this.updatedAt});
  factory _UpiPaymentSettings.fromJson(Map<String, dynamic> json) => _$UpiPaymentSettingsFromJson(json);

@override final  String upiId;
@override@JsonKey() final  bool isVerified;
@override@JsonKey() final  bool isEnabled;
@override final  DateTime? verifiedAt;
@override final  DateTime? updatedAt;

/// Create a copy of UpiPaymentSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpiPaymentSettingsCopyWith<_UpiPaymentSettings> get copyWith => __$UpiPaymentSettingsCopyWithImpl<_UpiPaymentSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UpiPaymentSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpiPaymentSettings&&(identical(other.upiId, upiId) || other.upiId == upiId)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.verifiedAt, verifiedAt) || other.verifiedAt == verifiedAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,upiId,isVerified,isEnabled,verifiedAt,updatedAt);

@override
String toString() {
  return 'UpiPaymentSettings(upiId: $upiId, isVerified: $isVerified, isEnabled: $isEnabled, verifiedAt: $verifiedAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$UpiPaymentSettingsCopyWith<$Res> implements $UpiPaymentSettingsCopyWith<$Res> {
  factory _$UpiPaymentSettingsCopyWith(_UpiPaymentSettings value, $Res Function(_UpiPaymentSettings) _then) = __$UpiPaymentSettingsCopyWithImpl;
@override @useResult
$Res call({
 String upiId, bool isVerified, bool isEnabled, DateTime? verifiedAt, DateTime? updatedAt
});




}
/// @nodoc
class __$UpiPaymentSettingsCopyWithImpl<$Res>
    implements _$UpiPaymentSettingsCopyWith<$Res> {
  __$UpiPaymentSettingsCopyWithImpl(this._self, this._then);

  final _UpiPaymentSettings _self;
  final $Res Function(_UpiPaymentSettings) _then;

/// Create a copy of UpiPaymentSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? upiId = null,Object? isVerified = null,Object? isEnabled = null,Object? verifiedAt = freezed,Object? updatedAt = freezed,}) {
  return _then(_UpiPaymentSettings(
upiId: null == upiId ? _self.upiId : upiId // ignore: cast_nullable_to_non_nullable
as String,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,verifiedAt: freezed == verifiedAt ? _self.verifiedAt : verifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
