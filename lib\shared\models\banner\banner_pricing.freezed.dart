// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'banner_pricing.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BannerPricing {

 String get id; double get basePrice; Map<String, double> get durationPrices; Map<String, double> get priorityMultiplier; Map<String, double> get categoryMultipliers; Map<String, double> get seasonalMultipliers; double get aiSuggestedPrice; DateTime get lastUpdated; bool get isActive;
/// Create a copy of BannerPricing
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BannerPricingCopyWith<BannerPricing> get copyWith => _$BannerPricingCopyWithImpl<BannerPricing>(this as BannerPricing, _$identity);

  /// Serializes this BannerPricing to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BannerPricing&&(identical(other.id, id) || other.id == id)&&(identical(other.basePrice, basePrice) || other.basePrice == basePrice)&&const DeepCollectionEquality().equals(other.durationPrices, durationPrices)&&const DeepCollectionEquality().equals(other.priorityMultiplier, priorityMultiplier)&&const DeepCollectionEquality().equals(other.categoryMultipliers, categoryMultipliers)&&const DeepCollectionEquality().equals(other.seasonalMultipliers, seasonalMultipliers)&&(identical(other.aiSuggestedPrice, aiSuggestedPrice) || other.aiSuggestedPrice == aiSuggestedPrice)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,basePrice,const DeepCollectionEquality().hash(durationPrices),const DeepCollectionEquality().hash(priorityMultiplier),const DeepCollectionEquality().hash(categoryMultipliers),const DeepCollectionEquality().hash(seasonalMultipliers),aiSuggestedPrice,lastUpdated,isActive);

@override
String toString() {
  return 'BannerPricing(id: $id, basePrice: $basePrice, durationPrices: $durationPrices, priorityMultiplier: $priorityMultiplier, categoryMultipliers: $categoryMultipliers, seasonalMultipliers: $seasonalMultipliers, aiSuggestedPrice: $aiSuggestedPrice, lastUpdated: $lastUpdated, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class $BannerPricingCopyWith<$Res>  {
  factory $BannerPricingCopyWith(BannerPricing value, $Res Function(BannerPricing) _then) = _$BannerPricingCopyWithImpl;
@useResult
$Res call({
 String id, double basePrice, Map<String, double> durationPrices, Map<String, double> priorityMultiplier, Map<String, double> categoryMultipliers, Map<String, double> seasonalMultipliers, double aiSuggestedPrice, DateTime lastUpdated, bool isActive
});




}
/// @nodoc
class _$BannerPricingCopyWithImpl<$Res>
    implements $BannerPricingCopyWith<$Res> {
  _$BannerPricingCopyWithImpl(this._self, this._then);

  final BannerPricing _self;
  final $Res Function(BannerPricing) _then;

/// Create a copy of BannerPricing
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? basePrice = null,Object? durationPrices = null,Object? priorityMultiplier = null,Object? categoryMultipliers = null,Object? seasonalMultipliers = null,Object? aiSuggestedPrice = null,Object? lastUpdated = null,Object? isActive = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,basePrice: null == basePrice ? _self.basePrice : basePrice // ignore: cast_nullable_to_non_nullable
as double,durationPrices: null == durationPrices ? _self.durationPrices : durationPrices // ignore: cast_nullable_to_non_nullable
as Map<String, double>,priorityMultiplier: null == priorityMultiplier ? _self.priorityMultiplier : priorityMultiplier // ignore: cast_nullable_to_non_nullable
as Map<String, double>,categoryMultipliers: null == categoryMultipliers ? _self.categoryMultipliers : categoryMultipliers // ignore: cast_nullable_to_non_nullable
as Map<String, double>,seasonalMultipliers: null == seasonalMultipliers ? _self.seasonalMultipliers : seasonalMultipliers // ignore: cast_nullable_to_non_nullable
as Map<String, double>,aiSuggestedPrice: null == aiSuggestedPrice ? _self.aiSuggestedPrice : aiSuggestedPrice // ignore: cast_nullable_to_non_nullable
as double,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [BannerPricing].
extension BannerPricingPatterns on BannerPricing {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BannerPricing value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BannerPricing() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BannerPricing value)  $default,){
final _that = this;
switch (_that) {
case _BannerPricing():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BannerPricing value)?  $default,){
final _that = this;
switch (_that) {
case _BannerPricing() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  double basePrice,  Map<String, double> durationPrices,  Map<String, double> priorityMultiplier,  Map<String, double> categoryMultipliers,  Map<String, double> seasonalMultipliers,  double aiSuggestedPrice,  DateTime lastUpdated,  bool isActive)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BannerPricing() when $default != null:
return $default(_that.id,_that.basePrice,_that.durationPrices,_that.priorityMultiplier,_that.categoryMultipliers,_that.seasonalMultipliers,_that.aiSuggestedPrice,_that.lastUpdated,_that.isActive);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  double basePrice,  Map<String, double> durationPrices,  Map<String, double> priorityMultiplier,  Map<String, double> categoryMultipliers,  Map<String, double> seasonalMultipliers,  double aiSuggestedPrice,  DateTime lastUpdated,  bool isActive)  $default,) {final _that = this;
switch (_that) {
case _BannerPricing():
return $default(_that.id,_that.basePrice,_that.durationPrices,_that.priorityMultiplier,_that.categoryMultipliers,_that.seasonalMultipliers,_that.aiSuggestedPrice,_that.lastUpdated,_that.isActive);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  double basePrice,  Map<String, double> durationPrices,  Map<String, double> priorityMultiplier,  Map<String, double> categoryMultipliers,  Map<String, double> seasonalMultipliers,  double aiSuggestedPrice,  DateTime lastUpdated,  bool isActive)?  $default,) {final _that = this;
switch (_that) {
case _BannerPricing() when $default != null:
return $default(_that.id,_that.basePrice,_that.durationPrices,_that.priorityMultiplier,_that.categoryMultipliers,_that.seasonalMultipliers,_that.aiSuggestedPrice,_that.lastUpdated,_that.isActive);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BannerPricing implements BannerPricing {
  const _BannerPricing({required this.id, required this.basePrice, required final  Map<String, double> durationPrices, required final  Map<String, double> priorityMultiplier, required final  Map<String, double> categoryMultipliers, required final  Map<String, double> seasonalMultipliers, required this.aiSuggestedPrice, required this.lastUpdated, this.isActive = true}): _durationPrices = durationPrices,_priorityMultiplier = priorityMultiplier,_categoryMultipliers = categoryMultipliers,_seasonalMultipliers = seasonalMultipliers;
  factory _BannerPricing.fromJson(Map<String, dynamic> json) => _$BannerPricingFromJson(json);

@override final  String id;
@override final  double basePrice;
 final  Map<String, double> _durationPrices;
@override Map<String, double> get durationPrices {
  if (_durationPrices is EqualUnmodifiableMapView) return _durationPrices;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_durationPrices);
}

 final  Map<String, double> _priorityMultiplier;
@override Map<String, double> get priorityMultiplier {
  if (_priorityMultiplier is EqualUnmodifiableMapView) return _priorityMultiplier;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_priorityMultiplier);
}

 final  Map<String, double> _categoryMultipliers;
@override Map<String, double> get categoryMultipliers {
  if (_categoryMultipliers is EqualUnmodifiableMapView) return _categoryMultipliers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_categoryMultipliers);
}

 final  Map<String, double> _seasonalMultipliers;
@override Map<String, double> get seasonalMultipliers {
  if (_seasonalMultipliers is EqualUnmodifiableMapView) return _seasonalMultipliers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_seasonalMultipliers);
}

@override final  double aiSuggestedPrice;
@override final  DateTime lastUpdated;
@override@JsonKey() final  bool isActive;

/// Create a copy of BannerPricing
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BannerPricingCopyWith<_BannerPricing> get copyWith => __$BannerPricingCopyWithImpl<_BannerPricing>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BannerPricingToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BannerPricing&&(identical(other.id, id) || other.id == id)&&(identical(other.basePrice, basePrice) || other.basePrice == basePrice)&&const DeepCollectionEquality().equals(other._durationPrices, _durationPrices)&&const DeepCollectionEquality().equals(other._priorityMultiplier, _priorityMultiplier)&&const DeepCollectionEquality().equals(other._categoryMultipliers, _categoryMultipliers)&&const DeepCollectionEquality().equals(other._seasonalMultipliers, _seasonalMultipliers)&&(identical(other.aiSuggestedPrice, aiSuggestedPrice) || other.aiSuggestedPrice == aiSuggestedPrice)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,basePrice,const DeepCollectionEquality().hash(_durationPrices),const DeepCollectionEquality().hash(_priorityMultiplier),const DeepCollectionEquality().hash(_categoryMultipliers),const DeepCollectionEquality().hash(_seasonalMultipliers),aiSuggestedPrice,lastUpdated,isActive);

@override
String toString() {
  return 'BannerPricing(id: $id, basePrice: $basePrice, durationPrices: $durationPrices, priorityMultiplier: $priorityMultiplier, categoryMultipliers: $categoryMultipliers, seasonalMultipliers: $seasonalMultipliers, aiSuggestedPrice: $aiSuggestedPrice, lastUpdated: $lastUpdated, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class _$BannerPricingCopyWith<$Res> implements $BannerPricingCopyWith<$Res> {
  factory _$BannerPricingCopyWith(_BannerPricing value, $Res Function(_BannerPricing) _then) = __$BannerPricingCopyWithImpl;
@override @useResult
$Res call({
 String id, double basePrice, Map<String, double> durationPrices, Map<String, double> priorityMultiplier, Map<String, double> categoryMultipliers, Map<String, double> seasonalMultipliers, double aiSuggestedPrice, DateTime lastUpdated, bool isActive
});




}
/// @nodoc
class __$BannerPricingCopyWithImpl<$Res>
    implements _$BannerPricingCopyWith<$Res> {
  __$BannerPricingCopyWithImpl(this._self, this._then);

  final _BannerPricing _self;
  final $Res Function(_BannerPricing) _then;

/// Create a copy of BannerPricing
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? basePrice = null,Object? durationPrices = null,Object? priorityMultiplier = null,Object? categoryMultipliers = null,Object? seasonalMultipliers = null,Object? aiSuggestedPrice = null,Object? lastUpdated = null,Object? isActive = null,}) {
  return _then(_BannerPricing(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,basePrice: null == basePrice ? _self.basePrice : basePrice // ignore: cast_nullable_to_non_nullable
as double,durationPrices: null == durationPrices ? _self._durationPrices : durationPrices // ignore: cast_nullable_to_non_nullable
as Map<String, double>,priorityMultiplier: null == priorityMultiplier ? _self._priorityMultiplier : priorityMultiplier // ignore: cast_nullable_to_non_nullable
as Map<String, double>,categoryMultipliers: null == categoryMultipliers ? _self._categoryMultipliers : categoryMultipliers // ignore: cast_nullable_to_non_nullable
as Map<String, double>,seasonalMultipliers: null == seasonalMultipliers ? _self._seasonalMultipliers : seasonalMultipliers // ignore: cast_nullable_to_non_nullable
as Map<String, double>,aiSuggestedPrice: null == aiSuggestedPrice ? _self.aiSuggestedPrice : aiSuggestedPrice // ignore: cast_nullable_to_non_nullable
as double,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
