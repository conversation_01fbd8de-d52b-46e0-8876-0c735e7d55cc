import 'package:flutter/material.dart';
import '../../../presentation/cubits/price_management/price_management_cubit.dart';
import 'package:intl/intl.dart';

class PriceHistory extends StatelessWidget {
  final List<PriceHistoryEntry> history;
  final void Function(PriceHistoryFilter) onFilterChange;

  const PriceHistory({
    super.key,
    required this.history,
    required this.onFilterChange,
  });

  String _formatDate(DateTime date) {
    return DateFormat('MMM d, y h:mm a').format(date);
  }

  @override
  Widget build(BuildContext context) {
    if (history.isEmpty) {
      return const Center(
        child: Text('No price history found'),
      );
    }

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: DropdownButtonFormField<PriceHistoryFilter>(
            value: PriceHistoryFilter.all,
            decoration: const InputDecoration(
              labelText: 'Filter',
              border: OutlineInputBorder(),
            ),
            items: PriceHistoryFilter.values.map((filter) {
              return DropdownMenuItem(
                value: filter,
                child: Text(filter.toString().split('.').last),
              );
            }).toList(),
            onChanged: (filter) {
              if (filter != null) {
                onFilterChange(filter);
              }
            },
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: history.length,
            itemBuilder: (context, index) {
              final entry = history[index];
              return Card(
                child: ListTile(
                  title: Text(entry.productName),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      Text(
                        'Old Price: ₹${entry.oldPrice.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        'New Price: ₹${entry.newPrice.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: entry.newPrice > entry.oldPrice
                                  ? Colors.green
                                  : Colors.red,
                            ),
                      ),
                      if (entry.reason.isNotEmpty)
                        Text(
                          'Reason: ${entry.reason}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      Text(
                        'Updated by: ${entry.updatedBy}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        'Date: ${_formatDate(entry.timestamp)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
