import 'package:freezed_annotation/freezed_annotation.dart';
import 'api_error.dart';

part 'api_response.g.dart';
part 'api_response.freezed.dart';

@Freezed(genericArgumentFactories: true)
class ApiResponse<T> with _$ApiResponse<T> {
  const factory ApiResponse({
    T? data,
    @Default(false) bool isSuccess,
    ApiError? error,
    ApiMetadata? metadata,
    @Default(ApiStatus.unknown) ApiStatus status,
    PaginationInfo? paginationInfo,
    RequestInfo? requestInfo,
  }) = _ApiResponse;

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);
}

@freezed
sealed class ApiMetadata with _$ApiMetadata {
  const factory ApiMetadata({
    @Default('') String timestamp,
    @Default('') String version,
    @Default('') String environment,
    Map<String, dynamic>? additionalData,
  }) = _ApiMetadata;

  factory ApiMetadata.fromJson(Map<String, dynamic> json) =>
      _$ApiMetadataFromJson(json);
}

@freezed
sealed class PaginationInfo with _$PaginationInfo {
  const factory PaginationInfo({
    @Default(0) int currentPage,
    @Default(0) int totalPages,
    @Default(0) int pageSize,
    @Default(0) int totalItems,
    @Default(false) bool hasNextPage,
    @Default(false) bool hasPreviousPage,
  }) = _PaginationInfo;

  factory PaginationInfo.fromJson(Map<String, dynamic> json) =>
      _$PaginationInfoFromJson(json);
}

@freezed
sealed class RequestInfo with _$RequestInfo {
  const factory RequestInfo({
    @Default('') String requestId,
    @Default('') String endpoint,
    @Default('') String method,
    @Default(0) int statusCode,
    @Default(0) int responseTime,
    Map<String, dynamic>? headers,
  }) = _RequestInfo;

  factory RequestInfo.fromJson(Map<String, dynamic> json) =>
      _$RequestInfoFromJson(json);
}

enum ApiStatus {
  @JsonValue('success')
  success,
  @JsonValue('error')
  error,
  @JsonValue('loading')
  loading,
  @JsonValue('unknown')
  unknown,
}
