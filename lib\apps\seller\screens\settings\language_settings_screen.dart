import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/presentation/providers/settings_provider.dart';
import 'package:shivish/apps/seller/presentation/cubits/settings_cubit.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';

class LanguageSettingsScreen extends ConsumerStatefulWidget {
  const LanguageSettingsScreen({super.key});

  @override
  ConsumerState<LanguageSettingsScreen> createState() =>
      _LanguageSettingsScreenState();
}

class _LanguageSettingsScreenState
    extends ConsumerState<LanguageSettingsScreen> {
  String _selectedLanguage = 'en';
  bool _isLoading = false;
  String? _error;

  final List<Map<String, String>> _languages = [
    {'code': 'en', 'name': 'English', 'nativeName': 'English'},
    {'code': 'hi', 'name': 'Hindi', 'nativeName': 'हिन्दी'},
    {'code': 'bn', 'name': 'Bengali', 'nativeName': 'বাংলা'},
    {'code': 'te', 'name': 'Telugu', 'nativeName': 'తెలుగు'},
    {'code': 'mr', 'name': 'Marathi', 'nativeName': 'मराठी'},
    {'code': 'ta', 'name': 'Tamil', 'nativeName': 'தமிழ்'},
    {'code': 'gu', 'name': 'Gujarati', 'nativeName': 'ગુજરાતી'},
    {'code': 'kn', 'name': 'Kannada', 'nativeName': 'ಕನ್ನಡ'},
    {'code': 'ml', 'name': 'Malayalam', 'nativeName': 'മലയാളം'},
    {'code': 'pa', 'name': 'Punjabi', 'nativeName': 'ਪੰਜਾਬੀ'},
  ];

  @override
  void initState() {
    super.initState();
    _loadLanguageSettings();
  }

  Future<void> _loadLanguageSettings() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final settingsState = ref.read(settingsProvider);

      settingsState.maybeWhen(
        loaded: (settings) {
          setState(() {
            _selectedLanguage = settings.language;
          });
        },
        orElse: () {},
      );
    } catch (e) {
      setState(() {
        _error = 'Failed to load language settings: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveLanguageSettings(String language) async {
    setState(() {
      _isLoading = true;
      _error = null;
      _selectedLanguage = language;
    });

    try {
      final settingsState = ref.read(settingsProvider);

      settingsState.maybeWhen(
        loaded: (settings) async {
          final updatedSettings = Settings(
            orderNotifications: settings.orderNotifications,
            productNotifications: settings.productNotifications,
            systemNotifications: settings.systemNotifications,
            theme: settings.theme,
            language: language,
            currency: settings.currency,
          );

          await ref
              .read(settingsProvider.notifier)
              .updateSettings(updatedSettings);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Language settings updated successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        orElse: () {},
      );
    } catch (e) {
      setState(() {
        _error = 'Failed to save language settings: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save language settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        appBar: AppToolbar(
          title: 'Language Settings',
        ),
        body: Center(
          child: LoadingIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: const AppToolbar(
          title: 'Language Settings',
        ),
        body: ErrorMessage(
          message: _error!,
          onRetry: _loadLanguageSettings,
        ),
      );
    }

    return Scaffold(
      appBar: const AppToolbar(
        title: 'Language Settings',
      ),
      body: ListView.builder(
        itemCount: _languages.length,
        itemBuilder: (context, index) {
          final language = _languages[index];
          return RadioListTile<String>(
            title: Text(language['name']!),
            subtitle: Text(language['nativeName']!),
            value: language['code']!,
            groupValue: _selectedLanguage,
            onChanged: (value) {
              if (value != null) {
                _saveLanguageSettings(value);
              }
            },
          );
        },
      ),
    );
  }
}
