import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/domain/models/schedule_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/store_cubit.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/apps/seller/screens/store/widgets/time_slot_dialog.dart';
import 'package:shivish/apps/seller/screens/store/widgets/holiday_dialog.dart';
import 'package:shivish/apps/seller/screens/store/widgets/timezone_selector.dart';

class ScheduleManager extends StatefulWidget {
  const ScheduleManager({super.key});

  @override
  State<ScheduleManager> createState() => _ScheduleManagerState();
}

class _ScheduleManagerState extends State<ScheduleManager>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _weekDays = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StoreCubit, StoreState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: LoadingIndicator());
        }

        if (state.error != null) {
          return ErrorMessage(
            message: state.error!,
          );
        }

        if (state.store == null) {
          return const ErrorMessage(
            message: 'Store not found',
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'Weekly Schedule'),
                Tab(text: 'Holidays'),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildWeeklySchedule(state),
                  _buildHolidays(state),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildWeeklySchedule(StoreState state) {
    return Column(
      children: [
        TimeZoneSelector(
          currentTimeZone: state.store!.schedule?.timeZone ?? 'UTC',
          onTimeZoneChanged: (newTimeZone) {
            final now = DateTime.now();
            final schedule = state.store!.schedule?.copyWith(
                  timeZone: newTimeZone,
                  updatedAt: now,
                ) ??
                ScheduleModel(
                  id: 'schedule_${state.store!.id}',
                  isOpen24x7: false,
                  weeklySchedule: {},
                  holidays: [],
                  timeZone: newTimeZone,
                  createdAt: now,
                  updatedAt: now,
                );
            context.read<StoreCubit>().updateSchedule(schedule);
          },
        ),
        SwitchListTile(
          title: const Text('Open 24x7'),
          value: state.store!.schedule?.isOpen24x7 ?? false,
          onChanged: (value) {
            final now = DateTime.now();
            final schedule =
                state.store!.schedule?.copyWith(isOpen24x7: value) ??
                    ScheduleModel(
                      id: 'schedule_${state.store!.id}',
                      isOpen24x7: value,
                      weeklySchedule: {},
                      holidays: [],
                      timeZone: 'UTC',
                      createdAt: now,
                      updatedAt: now,
                    );
            context.read<StoreCubit>().updateSchedule(schedule);
          },
        ),
        const Divider(),
        Expanded(
          child: ListView.builder(
            itemCount: _weekDays.length,
            itemBuilder: (context, index) {
              final day = _weekDays[index].toLowerCase();
              final schedule = state.store!.schedule?.weeklySchedule[day];
              return _DayScheduleItem(
                day: _weekDays[index],
                schedule: schedule,
                timeZone: state.store!.schedule?.timeZone ?? 'UTC',
                onScheduleChanged: (newSchedule) {
                  final now = DateTime.now();
                  final weeklySchedule = Map<String, DaySchedule>.from(
                    state.store!.schedule?.weeklySchedule ?? {},
                  );
                  weeklySchedule[day] = newSchedule;
                  final schedule = state.store!.schedule?.copyWith(
                        weeklySchedule: weeklySchedule,
                      ) ??
                      ScheduleModel(
                        id: 'schedule_${state.store!.id}',
                        isOpen24x7: false,
                        weeklySchedule: weeklySchedule,
                        holidays: [],
                        timeZone: 'UTC',
                        createdAt: now,
                        updatedAt: now,
                      );
                  context.read<StoreCubit>().updateSchedule(schedule);
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHolidays(StoreState state) {
    final holidays = state.store!.schedule?.holidays ?? [];
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: ElevatedButton.icon(
            onPressed: () => _showHolidayDialog(context, state),
            icon: const Icon(Icons.add),
            label: const Text('Add Holiday'),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: holidays.length,
            itemBuilder: (context, index) {
              final holiday = holidays[index];
              return _HolidayItem(
                holiday: holiday,
                onDelete: () {
                  final now = DateTime.now();
                  final updatedHolidays = List<HolidaySchedule>.from(holidays)
                    ..removeAt(index);
                  final schedule = state.store!.schedule?.copyWith(
                        holidays: updatedHolidays,
                      ) ??
                      ScheduleModel(
                        id: 'schedule_${state.store!.id}',
                        isOpen24x7: false,
                        weeklySchedule: {},
                        holidays: updatedHolidays,
                        timeZone: 'UTC',
                        createdAt: now,
                        updatedAt: now,
                      );
                  context.read<StoreCubit>().updateSchedule(schedule);
                },
                onEdit: () => _showHolidayDialog(context, state, holiday),
              );
            },
          ),
        ),
      ],
    );
  }

  void _showHolidayDialog(
    BuildContext context,
    StoreState state, [
    HolidaySchedule? initialHoliday,
  ]) {
    showDialog<void>(
      context: context,
      builder: (context) => HolidayDialog(
        initialHoliday: initialHoliday,
        onSave: (holiday) {
          final now = DateTime.now();
          final holidays = List<HolidaySchedule>.from(
            state.store!.schedule?.holidays ?? [],
          );

          if (initialHoliday != null) {
            final index = holidays.indexWhere((h) => h.id == initialHoliday.id);
            if (index != -1) {
              holidays[index] = holiday;
            }
          } else {
            holidays.add(holiday);
          }

          final schedule = state.store!.schedule?.copyWith(
                holidays: holidays,
              ) ??
              ScheduleModel(
                id: 'schedule_${state.store!.id}',
                isOpen24x7: false,
                weeklySchedule: {},
                holidays: holidays,
                timeZone: 'UTC',
                createdAt: now,
                updatedAt: now,
              );
          context.read<StoreCubit>().updateSchedule(schedule);
        },
      ),
    );
  }
}

class _DayScheduleItem extends StatelessWidget {
  final String day;
  final DaySchedule? schedule;
  final ValueChanged<DaySchedule> onScheduleChanged;
  final String timeZone;

  const _DayScheduleItem({
    required this.day,
    required this.schedule,
    required this.onScheduleChanged,
    required this.timeZone,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  day,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Switch(
                  value: schedule?.isOpen ?? false,
                  onChanged: (value) {
                    onScheduleChanged(
                      DaySchedule(isOpen: value, slots: schedule?.slots ?? []),
                    );
                  },
                ),
              ],
            ),
            if (schedule?.isOpen ?? false) ...[
              const SizedBox(height: 8),
              ..._buildTimeSlots(context),
              TextButton.icon(
                onPressed: () => _showTimeSlotDialog(context),
                icon: const Icon(Icons.add),
                label: const Text('Add Time Slot'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTimeSlots(BuildContext context) {
    return (schedule?.slots ?? []).map((slot) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Expanded(
              child: Text('${slot.openTime} - ${slot.closeTime}'),
            ),
            if (slot.breakStart != null && slot.breakEnd != null)
              Expanded(
                child: Text(
                  'Break: ${slot.breakStart} - ${slot.breakEnd}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              onPressed: () => _showTimeSlotDialog(context, slot),
            ),
            IconButton(
              icon: const Icon(Icons.delete, size: 20),
              onPressed: () {
                final slots = List<TimeSlot>.from(schedule?.slots ?? [])
                  ..remove(slot);
                onScheduleChanged(
                  DaySchedule(isOpen: schedule?.isOpen ?? false, slots: slots),
                );
              },
            ),
          ],
        ),
      );
    }).toList();
  }

  void _showTimeSlotDialog(BuildContext context, [TimeSlot? initialSlot]) {
    showDialog<void>(
      context: context,
      builder: (context) => TimeSlotDialog(
        initialSlot: initialSlot,
        timeZone: timeZone,
        onSave: (slot) {
          final slots = List<TimeSlot>.from(schedule?.slots ?? []);
          if (initialSlot != null) {
            final index = slots.indexOf(initialSlot);
            slots[index] = slot;
          } else {
            slots.add(slot);
          }
          onScheduleChanged(
            DaySchedule(isOpen: schedule?.isOpen ?? false, slots: slots),
          );
        },
      ),
    );
  }
}

class _HolidayItem extends StatelessWidget {
  final HolidaySchedule holiday;
  final VoidCallback onDelete;
  final VoidCallback onEdit;

  const _HolidayItem({
    required this.holiday,
    required this.onDelete,
    required this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(holiday.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${_formatDate(holiday.startDate)} - ${_formatDate(holiday.endDate)}',
            ),
            if (holiday.description != null)
              Text(
                holiday.description!,
                style: Theme.of(context).textTheme.bodySmall,
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (holiday.isRecurring)
              const Padding(
                padding: EdgeInsets.only(right: 8),
                child: Icon(Icons.repeat, size: 20),
              ),
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              onPressed: onEdit,
            ),
            IconButton(
              icon: const Icon(Icons.delete, size: 20),
              onPressed: onDelete,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
