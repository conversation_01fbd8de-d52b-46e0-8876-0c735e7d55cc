// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'storage_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_StorageProviderConfig _$StorageProviderConfigFromJson(
  Map<String, dynamic> json,
) => _StorageProviderConfig(
  type: $enumDecode(_$StorageProviderTypeEnumMap, json['type']),
  name: json['name'] as String,
  isEnabled: json['isEnabled'] as bool,
  settings: json['settings'] as Map<String, dynamic>,
);

Map<String, dynamic> _$StorageProviderConfigToJson(
  _StorageProviderConfig instance,
) => <String, dynamic>{
  'type': _$StorageProviderTypeEnumMap[instance.type]!,
  'name': instance.name,
  'isEnabled': instance.isEnabled,
  'settings': instance.settings,
};

const _$StorageProviderTypeEnumMap = {
  StorageProviderType.local: 'local',
  StorageProviderType.googleDrive: 'googleDrive',
  StorageProviderType.firebase: 'firebase',
  StorageProviderType.custom: 'custom',
};

_CollectionStorageConfig _$CollectionStorageConfigFromJson(
  Map<String, dynamic> json,
) => _CollectionStorageConfig(
  collectionType: $enumDecode(
    _$ContentCollectionTypeEnumMap,
    json['collectionType'],
  ),
  primaryProviderName: json['primaryProviderName'] as String,
  additionalProviderNames:
      (json['additionalProviderNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  basePath: json['basePath'] as String,
  isEnabled: json['isEnabled'] as bool,
  balancingStrategy:
      $enumDecodeNullable(
        _$BalancingStrategyEnumMap,
        json['balancingStrategy'],
      ) ??
      BalancingStrategy.singleProvider,
  additionalSettings: json['additionalSettings'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$CollectionStorageConfigToJson(
  _CollectionStorageConfig instance,
) => <String, dynamic>{
  'collectionType': _$ContentCollectionTypeEnumMap[instance.collectionType]!,
  'primaryProviderName': instance.primaryProviderName,
  'additionalProviderNames': instance.additionalProviderNames,
  'basePath': instance.basePath,
  'isEnabled': instance.isEnabled,
  'balancingStrategy': _$BalancingStrategyEnumMap[instance.balancingStrategy]!,
  'additionalSettings': instance.additionalSettings,
};

const _$ContentCollectionTypeEnumMap = {
  ContentCollectionType.documents: 'documents',
  ContentCollectionType.images: 'images',
  ContentCollectionType.media: 'media',
  ContentCollectionType.backups: 'backups',
  ContentCollectionType.userFiles: 'userFiles',
  ContentCollectionType.other: 'other',
};

const _$BalancingStrategyEnumMap = {
  BalancingStrategy.singleProvider: 'singleProvider',
  BalancingStrategy.roundRobin: 'roundRobin',
  BalancingStrategy.random: 'random',
};

_StorageConfigModel _$StorageConfigModelFromJson(Map<String, dynamic> json) =>
    _StorageConfigModel(
      providers: (json['providers'] as List<dynamic>)
          .map((e) => StorageProviderConfig.fromJson(e as Map<String, dynamic>))
          .toList(),
      collectionConfigs: (json['collectionConfigs'] as List<dynamic>)
          .map(
            (e) => CollectionStorageConfig.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      defaultProvider: json['defaultProvider'] as String,
      debugSkipUploads: json['debugSkipUploads'] as bool? ?? false,
    );

Map<String, dynamic> _$StorageConfigModelToJson(_StorageConfigModel instance) =>
    <String, dynamic>{
      'providers': instance.providers.map((e) => e.toJson()).toList(),
      'collectionConfigs': instance.collectionConfigs
          .map((e) => e.toJson())
          .toList(),
      'defaultProvider': instance.defaultProvider,
      'debugSkipUploads': instance.debugSkipUploads,
    };
