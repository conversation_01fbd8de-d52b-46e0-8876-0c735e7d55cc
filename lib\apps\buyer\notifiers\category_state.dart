import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/entities/category/category_model.dart';

part 'category_state.freezed.dart';

/// State for categories
@freezed
sealed class CategoryState with _$CategoryState {
  /// Initial state
  const factory CategoryState.initial() = _Initial;

  /// Loading state
  const factory CategoryState.loading() = _Loading;

  /// Loaded state with categories
  const factory CategoryState.loaded({
    required List<CategoryModel> categories,
  }) = _Loaded;

  /// Error state
  const factory CategoryState.error({
    required String message,
  }) = _Error;
}
