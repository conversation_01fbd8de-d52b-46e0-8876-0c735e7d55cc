import 'package:freezed_annotation/freezed_annotation.dart';

import 'notification_type.dart';

part 'notification_settings_model.freezed.dart';
part 'notification_settings_model.g.dart';

/// Model class for notification settings
@freezed
abstract class NotificationSettingsModel with _$NotificationSettingsModel {
  const factory NotificationSettingsModel({
    required String id,
    required String userId,
    @Default(true) bool pushEnabled,
    @Default(true) bool emailEnabled,
    @Default(true) bool smsEnabled,
    @Default(true) bool inAppEnabled,
    required Map<NotificationType, bool> typeSettings,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isDeleted,
  }) = _NotificationSettingsModel;

  factory NotificationSettingsModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationSettingsModelFromJson(json);

  /// Empty notification settings model with default values
  factory NotificationSettingsModel.empty() => NotificationSettingsModel(
    id: '',
    userId: '',
    typeSettings: {
      NotificationType.general: true,
      NotificationType.order: true,
      NotificationType.payment: true,
      NotificationType.booking: true,
      NotificationType.event: true,
      NotificationType.chat: true,
      NotificationType.system: true,
      NotificationType.verification: true,
      NotificationType.deliveryRequest: true,
      NotificationType.statusUpdate: true,
      NotificationType.earnings: true,
      // Buyer-specific notification types
      NotificationType.healthcareReminder: true,
      NotificationType.dailyHoroscope: true,
      NotificationType.panchangam: true,
      NotificationType.shoppingListAlert: true,
      NotificationType.safetyTracking: true,
      NotificationType.friendTracking: true,
      NotificationType.alarmNotification: true,
      NotificationType.calendarReminder: true,
      NotificationType.promotions: true,
      NotificationType.festivalReminder: true,
      NotificationType.priceAlert: true,
      NotificationType.lowStockAlert: true,
      NotificationType.rideBooking: true,
      NotificationType.ticketBooking: true,
      NotificationType.medicineOrder: true,
      NotificationType.doctorConsultation: true,
      NotificationType.aiAssistant: true,
      NotificationType.technicianBooking: true,
      NotificationType.priestBooking: true,
    },
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}
