import 'package:flutter/material.dart';
import 'package:shivish/shared/core/localization/l10n/app_localizations.dart'
    as gen;

/// Extension on BuildContext to provide easy access to translations
extension AppLocalizationsX on BuildContext {
  /// Get the current locale's translation for the given key
  String l10n(String key) {
    final localizations = gen.AppLocalizations.of(this);
    if (localizations == null) return key;

    switch (key) {
      case 'inventory':
        return localizations.inventory;
      case 'unitPrice':
        return localizations.unitPrice;
      case 'minStock':
        return localizations.minStock;
      case 'addStock':
        return localizations.addStock;
      case 'removeStock':
        return localizations.removeStock;
      case 'setLowStockAlert':
        return localizations.setLowStockAlert;
      case 'lowStockAlert':
        return localizations.lowStockAlert;
      case 'retry':
        return localizations.retry;
      case 'error':
        return localizations.error;
      case 'loading':
        return localizations.loading;
      case 'noItems':
        return localizations.noItems;
      case 'stock':
        return localizations.stock;
      case 'currentStock':
        return localizations.currentStock;
      case 'maximumStock':
        return localizations.maximumStock;
      case 'minimumStock':
        return localizations.minimumStock;
      case 'unit':
        return localizations.unit;
      case 'productId':
        return localizations.productId;
      case 'defaultAlarm':
        return localizations.defaultAlarm;
      case 'time':
        return localizations.time;
      case 'tone':
        return localizations.tone;
      case 'volume':
        return localizations.volume;
      case 'vibration':
        return localizations.vibration;
      case 'snoozeSettings':
        return localizations.snoozeSettings;
      case 'snoozeDuration':
        return localizations.snoozeDuration;
      case 'maxSnoozeCount':
        return localizations.maxSnoozeCount;
      case 'minutes':
        return localizations.minutes;
      case 'save':
        return localizations.save;
      case 'alarmSaved':
        return localizations.alarmSaved;
      case 'errorSavingAlarm':
        return localizations.errorSavingAlarm;
      default:
        return key;
    }
  }
}
