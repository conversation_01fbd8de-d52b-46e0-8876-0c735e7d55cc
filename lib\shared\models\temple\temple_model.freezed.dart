// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'temple_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TempleLocation {

 String get address; String get city; String get state; String get country; String get pincode; double get latitude; double get longitude; String? get landmark; String? get directions;
/// Create a copy of TempleLocation
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TempleLocationCopyWith<TempleLocation> get copyWith => _$TempleLocationCopyWithImpl<TempleLocation>(this as TempleLocation, _$identity);

  /// Serializes this TempleLocation to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TempleLocation&&(identical(other.address, address) || other.address == address)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.pincode, pincode) || other.pincode == pincode)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.landmark, landmark) || other.landmark == landmark)&&(identical(other.directions, directions) || other.directions == directions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,address,city,state,country,pincode,latitude,longitude,landmark,directions);

@override
String toString() {
  return 'TempleLocation(address: $address, city: $city, state: $state, country: $country, pincode: $pincode, latitude: $latitude, longitude: $longitude, landmark: $landmark, directions: $directions)';
}


}

/// @nodoc
abstract mixin class $TempleLocationCopyWith<$Res>  {
  factory $TempleLocationCopyWith(TempleLocation value, $Res Function(TempleLocation) _then) = _$TempleLocationCopyWithImpl;
@useResult
$Res call({
 String address, String city, String state, String country, String pincode, double latitude, double longitude, String? landmark, String? directions
});




}
/// @nodoc
class _$TempleLocationCopyWithImpl<$Res>
    implements $TempleLocationCopyWith<$Res> {
  _$TempleLocationCopyWithImpl(this._self, this._then);

  final TempleLocation _self;
  final $Res Function(TempleLocation) _then;

/// Create a copy of TempleLocation
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? address = null,Object? city = null,Object? state = null,Object? country = null,Object? pincode = null,Object? latitude = null,Object? longitude = null,Object? landmark = freezed,Object? directions = freezed,}) {
  return _then(_self.copyWith(
address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,pincode: null == pincode ? _self.pincode : pincode // ignore: cast_nullable_to_non_nullable
as String,latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,landmark: freezed == landmark ? _self.landmark : landmark // ignore: cast_nullable_to_non_nullable
as String?,directions: freezed == directions ? _self.directions : directions // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [TempleLocation].
extension TempleLocationPatterns on TempleLocation {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TempleLocation value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TempleLocation() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TempleLocation value)  $default,){
final _that = this;
switch (_that) {
case _TempleLocation():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TempleLocation value)?  $default,){
final _that = this;
switch (_that) {
case _TempleLocation() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String address,  String city,  String state,  String country,  String pincode,  double latitude,  double longitude,  String? landmark,  String? directions)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TempleLocation() when $default != null:
return $default(_that.address,_that.city,_that.state,_that.country,_that.pincode,_that.latitude,_that.longitude,_that.landmark,_that.directions);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String address,  String city,  String state,  String country,  String pincode,  double latitude,  double longitude,  String? landmark,  String? directions)  $default,) {final _that = this;
switch (_that) {
case _TempleLocation():
return $default(_that.address,_that.city,_that.state,_that.country,_that.pincode,_that.latitude,_that.longitude,_that.landmark,_that.directions);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String address,  String city,  String state,  String country,  String pincode,  double latitude,  double longitude,  String? landmark,  String? directions)?  $default,) {final _that = this;
switch (_that) {
case _TempleLocation() when $default != null:
return $default(_that.address,_that.city,_that.state,_that.country,_that.pincode,_that.latitude,_that.longitude,_that.landmark,_that.directions);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TempleLocation implements TempleLocation {
  const _TempleLocation({required this.address, required this.city, required this.state, required this.country, required this.pincode, required this.latitude, required this.longitude, this.landmark, this.directions});
  factory _TempleLocation.fromJson(Map<String, dynamic> json) => _$TempleLocationFromJson(json);

@override final  String address;
@override final  String city;
@override final  String state;
@override final  String country;
@override final  String pincode;
@override final  double latitude;
@override final  double longitude;
@override final  String? landmark;
@override final  String? directions;

/// Create a copy of TempleLocation
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TempleLocationCopyWith<_TempleLocation> get copyWith => __$TempleLocationCopyWithImpl<_TempleLocation>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TempleLocationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TempleLocation&&(identical(other.address, address) || other.address == address)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.pincode, pincode) || other.pincode == pincode)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.landmark, landmark) || other.landmark == landmark)&&(identical(other.directions, directions) || other.directions == directions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,address,city,state,country,pincode,latitude,longitude,landmark,directions);

@override
String toString() {
  return 'TempleLocation(address: $address, city: $city, state: $state, country: $country, pincode: $pincode, latitude: $latitude, longitude: $longitude, landmark: $landmark, directions: $directions)';
}


}

/// @nodoc
abstract mixin class _$TempleLocationCopyWith<$Res> implements $TempleLocationCopyWith<$Res> {
  factory _$TempleLocationCopyWith(_TempleLocation value, $Res Function(_TempleLocation) _then) = __$TempleLocationCopyWithImpl;
@override @useResult
$Res call({
 String address, String city, String state, String country, String pincode, double latitude, double longitude, String? landmark, String? directions
});




}
/// @nodoc
class __$TempleLocationCopyWithImpl<$Res>
    implements _$TempleLocationCopyWith<$Res> {
  __$TempleLocationCopyWithImpl(this._self, this._then);

  final _TempleLocation _self;
  final $Res Function(_TempleLocation) _then;

/// Create a copy of TempleLocation
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? address = null,Object? city = null,Object? state = null,Object? country = null,Object? pincode = null,Object? latitude = null,Object? longitude = null,Object? landmark = freezed,Object? directions = freezed,}) {
  return _then(_TempleLocation(
address: null == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,pincode: null == pincode ? _self.pincode : pincode // ignore: cast_nullable_to_non_nullable
as String,latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,landmark: freezed == landmark ? _self.landmark : landmark // ignore: cast_nullable_to_non_nullable
as String?,directions: freezed == directions ? _self.directions : directions // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$TempleContact {

 String get phone; String? get email; String? get website; String? get whatsapp; Map<String, String>? get socialMedia;
/// Create a copy of TempleContact
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TempleContactCopyWith<TempleContact> get copyWith => _$TempleContactCopyWithImpl<TempleContact>(this as TempleContact, _$identity);

  /// Serializes this TempleContact to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TempleContact&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email)&&(identical(other.website, website) || other.website == website)&&(identical(other.whatsapp, whatsapp) || other.whatsapp == whatsapp)&&const DeepCollectionEquality().equals(other.socialMedia, socialMedia));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phone,email,website,whatsapp,const DeepCollectionEquality().hash(socialMedia));

@override
String toString() {
  return 'TempleContact(phone: $phone, email: $email, website: $website, whatsapp: $whatsapp, socialMedia: $socialMedia)';
}


}

/// @nodoc
abstract mixin class $TempleContactCopyWith<$Res>  {
  factory $TempleContactCopyWith(TempleContact value, $Res Function(TempleContact) _then) = _$TempleContactCopyWithImpl;
@useResult
$Res call({
 String phone, String? email, String? website, String? whatsapp, Map<String, String>? socialMedia
});




}
/// @nodoc
class _$TempleContactCopyWithImpl<$Res>
    implements $TempleContactCopyWith<$Res> {
  _$TempleContactCopyWithImpl(this._self, this._then);

  final TempleContact _self;
  final $Res Function(TempleContact) _then;

/// Create a copy of TempleContact
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? phone = null,Object? email = freezed,Object? website = freezed,Object? whatsapp = freezed,Object? socialMedia = freezed,}) {
  return _then(_self.copyWith(
phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,whatsapp: freezed == whatsapp ? _self.whatsapp : whatsapp // ignore: cast_nullable_to_non_nullable
as String?,socialMedia: freezed == socialMedia ? _self.socialMedia : socialMedia // ignore: cast_nullable_to_non_nullable
as Map<String, String>?,
  ));
}

}


/// Adds pattern-matching-related methods to [TempleContact].
extension TempleContactPatterns on TempleContact {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TempleContact value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TempleContact() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TempleContact value)  $default,){
final _that = this;
switch (_that) {
case _TempleContact():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TempleContact value)?  $default,){
final _that = this;
switch (_that) {
case _TempleContact() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String phone,  String? email,  String? website,  String? whatsapp,  Map<String, String>? socialMedia)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TempleContact() when $default != null:
return $default(_that.phone,_that.email,_that.website,_that.whatsapp,_that.socialMedia);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String phone,  String? email,  String? website,  String? whatsapp,  Map<String, String>? socialMedia)  $default,) {final _that = this;
switch (_that) {
case _TempleContact():
return $default(_that.phone,_that.email,_that.website,_that.whatsapp,_that.socialMedia);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String phone,  String? email,  String? website,  String? whatsapp,  Map<String, String>? socialMedia)?  $default,) {final _that = this;
switch (_that) {
case _TempleContact() when $default != null:
return $default(_that.phone,_that.email,_that.website,_that.whatsapp,_that.socialMedia);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TempleContact implements TempleContact {
  const _TempleContact({required this.phone, this.email, this.website, this.whatsapp, final  Map<String, String>? socialMedia}): _socialMedia = socialMedia;
  factory _TempleContact.fromJson(Map<String, dynamic> json) => _$TempleContactFromJson(json);

@override final  String phone;
@override final  String? email;
@override final  String? website;
@override final  String? whatsapp;
 final  Map<String, String>? _socialMedia;
@override Map<String, String>? get socialMedia {
  final value = _socialMedia;
  if (value == null) return null;
  if (_socialMedia is EqualUnmodifiableMapView) return _socialMedia;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of TempleContact
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TempleContactCopyWith<_TempleContact> get copyWith => __$TempleContactCopyWithImpl<_TempleContact>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TempleContactToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TempleContact&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email)&&(identical(other.website, website) || other.website == website)&&(identical(other.whatsapp, whatsapp) || other.whatsapp == whatsapp)&&const DeepCollectionEquality().equals(other._socialMedia, _socialMedia));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,phone,email,website,whatsapp,const DeepCollectionEquality().hash(_socialMedia));

@override
String toString() {
  return 'TempleContact(phone: $phone, email: $email, website: $website, whatsapp: $whatsapp, socialMedia: $socialMedia)';
}


}

/// @nodoc
abstract mixin class _$TempleContactCopyWith<$Res> implements $TempleContactCopyWith<$Res> {
  factory _$TempleContactCopyWith(_TempleContact value, $Res Function(_TempleContact) _then) = __$TempleContactCopyWithImpl;
@override @useResult
$Res call({
 String phone, String? email, String? website, String? whatsapp, Map<String, String>? socialMedia
});




}
/// @nodoc
class __$TempleContactCopyWithImpl<$Res>
    implements _$TempleContactCopyWith<$Res> {
  __$TempleContactCopyWithImpl(this._self, this._then);

  final _TempleContact _self;
  final $Res Function(_TempleContact) _then;

/// Create a copy of TempleContact
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? phone = null,Object? email = freezed,Object? website = freezed,Object? whatsapp = freezed,Object? socialMedia = freezed,}) {
  return _then(_TempleContact(
phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,whatsapp: freezed == whatsapp ? _self.whatsapp : whatsapp // ignore: cast_nullable_to_non_nullable
as String?,socialMedia: freezed == socialMedia ? _self._socialMedia : socialMedia // ignore: cast_nullable_to_non_nullable
as Map<String, String>?,
  ));
}


}


/// @nodoc
mixin _$TempleTimings {

 String get openTime; String get closeTime; String? get breakStartTime; String? get breakEndTime; List<String> get specialTimings; bool get isOpen24Hours; List<String> get closedDays;
/// Create a copy of TempleTimings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TempleTimingsCopyWith<TempleTimings> get copyWith => _$TempleTimingsCopyWithImpl<TempleTimings>(this as TempleTimings, _$identity);

  /// Serializes this TempleTimings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TempleTimings&&(identical(other.openTime, openTime) || other.openTime == openTime)&&(identical(other.closeTime, closeTime) || other.closeTime == closeTime)&&(identical(other.breakStartTime, breakStartTime) || other.breakStartTime == breakStartTime)&&(identical(other.breakEndTime, breakEndTime) || other.breakEndTime == breakEndTime)&&const DeepCollectionEquality().equals(other.specialTimings, specialTimings)&&(identical(other.isOpen24Hours, isOpen24Hours) || other.isOpen24Hours == isOpen24Hours)&&const DeepCollectionEquality().equals(other.closedDays, closedDays));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,openTime,closeTime,breakStartTime,breakEndTime,const DeepCollectionEquality().hash(specialTimings),isOpen24Hours,const DeepCollectionEquality().hash(closedDays));

@override
String toString() {
  return 'TempleTimings(openTime: $openTime, closeTime: $closeTime, breakStartTime: $breakStartTime, breakEndTime: $breakEndTime, specialTimings: $specialTimings, isOpen24Hours: $isOpen24Hours, closedDays: $closedDays)';
}


}

/// @nodoc
abstract mixin class $TempleTimingsCopyWith<$Res>  {
  factory $TempleTimingsCopyWith(TempleTimings value, $Res Function(TempleTimings) _then) = _$TempleTimingsCopyWithImpl;
@useResult
$Res call({
 String openTime, String closeTime, String? breakStartTime, String? breakEndTime, List<String> specialTimings, bool isOpen24Hours, List<String> closedDays
});




}
/// @nodoc
class _$TempleTimingsCopyWithImpl<$Res>
    implements $TempleTimingsCopyWith<$Res> {
  _$TempleTimingsCopyWithImpl(this._self, this._then);

  final TempleTimings _self;
  final $Res Function(TempleTimings) _then;

/// Create a copy of TempleTimings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? openTime = null,Object? closeTime = null,Object? breakStartTime = freezed,Object? breakEndTime = freezed,Object? specialTimings = null,Object? isOpen24Hours = null,Object? closedDays = null,}) {
  return _then(_self.copyWith(
openTime: null == openTime ? _self.openTime : openTime // ignore: cast_nullable_to_non_nullable
as String,closeTime: null == closeTime ? _self.closeTime : closeTime // ignore: cast_nullable_to_non_nullable
as String,breakStartTime: freezed == breakStartTime ? _self.breakStartTime : breakStartTime // ignore: cast_nullable_to_non_nullable
as String?,breakEndTime: freezed == breakEndTime ? _self.breakEndTime : breakEndTime // ignore: cast_nullable_to_non_nullable
as String?,specialTimings: null == specialTimings ? _self.specialTimings : specialTimings // ignore: cast_nullable_to_non_nullable
as List<String>,isOpen24Hours: null == isOpen24Hours ? _self.isOpen24Hours : isOpen24Hours // ignore: cast_nullable_to_non_nullable
as bool,closedDays: null == closedDays ? _self.closedDays : closedDays // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [TempleTimings].
extension TempleTimingsPatterns on TempleTimings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TempleTimings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TempleTimings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TempleTimings value)  $default,){
final _that = this;
switch (_that) {
case _TempleTimings():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TempleTimings value)?  $default,){
final _that = this;
switch (_that) {
case _TempleTimings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String openTime,  String closeTime,  String? breakStartTime,  String? breakEndTime,  List<String> specialTimings,  bool isOpen24Hours,  List<String> closedDays)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TempleTimings() when $default != null:
return $default(_that.openTime,_that.closeTime,_that.breakStartTime,_that.breakEndTime,_that.specialTimings,_that.isOpen24Hours,_that.closedDays);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String openTime,  String closeTime,  String? breakStartTime,  String? breakEndTime,  List<String> specialTimings,  bool isOpen24Hours,  List<String> closedDays)  $default,) {final _that = this;
switch (_that) {
case _TempleTimings():
return $default(_that.openTime,_that.closeTime,_that.breakStartTime,_that.breakEndTime,_that.specialTimings,_that.isOpen24Hours,_that.closedDays);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String openTime,  String closeTime,  String? breakStartTime,  String? breakEndTime,  List<String> specialTimings,  bool isOpen24Hours,  List<String> closedDays)?  $default,) {final _that = this;
switch (_that) {
case _TempleTimings() when $default != null:
return $default(_that.openTime,_that.closeTime,_that.breakStartTime,_that.breakEndTime,_that.specialTimings,_that.isOpen24Hours,_that.closedDays);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TempleTimings implements TempleTimings {
  const _TempleTimings({required this.openTime, required this.closeTime, this.breakStartTime, this.breakEndTime, final  List<String> specialTimings = const [], this.isOpen24Hours = false, final  List<String> closedDays = const []}): _specialTimings = specialTimings,_closedDays = closedDays;
  factory _TempleTimings.fromJson(Map<String, dynamic> json) => _$TempleTimingsFromJson(json);

@override final  String openTime;
@override final  String closeTime;
@override final  String? breakStartTime;
@override final  String? breakEndTime;
 final  List<String> _specialTimings;
@override@JsonKey() List<String> get specialTimings {
  if (_specialTimings is EqualUnmodifiableListView) return _specialTimings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_specialTimings);
}

@override@JsonKey() final  bool isOpen24Hours;
 final  List<String> _closedDays;
@override@JsonKey() List<String> get closedDays {
  if (_closedDays is EqualUnmodifiableListView) return _closedDays;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_closedDays);
}


/// Create a copy of TempleTimings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TempleTimingsCopyWith<_TempleTimings> get copyWith => __$TempleTimingsCopyWithImpl<_TempleTimings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TempleTimingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TempleTimings&&(identical(other.openTime, openTime) || other.openTime == openTime)&&(identical(other.closeTime, closeTime) || other.closeTime == closeTime)&&(identical(other.breakStartTime, breakStartTime) || other.breakStartTime == breakStartTime)&&(identical(other.breakEndTime, breakEndTime) || other.breakEndTime == breakEndTime)&&const DeepCollectionEquality().equals(other._specialTimings, _specialTimings)&&(identical(other.isOpen24Hours, isOpen24Hours) || other.isOpen24Hours == isOpen24Hours)&&const DeepCollectionEquality().equals(other._closedDays, _closedDays));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,openTime,closeTime,breakStartTime,breakEndTime,const DeepCollectionEquality().hash(_specialTimings),isOpen24Hours,const DeepCollectionEquality().hash(_closedDays));

@override
String toString() {
  return 'TempleTimings(openTime: $openTime, closeTime: $closeTime, breakStartTime: $breakStartTime, breakEndTime: $breakEndTime, specialTimings: $specialTimings, isOpen24Hours: $isOpen24Hours, closedDays: $closedDays)';
}


}

/// @nodoc
abstract mixin class _$TempleTimingsCopyWith<$Res> implements $TempleTimingsCopyWith<$Res> {
  factory _$TempleTimingsCopyWith(_TempleTimings value, $Res Function(_TempleTimings) _then) = __$TempleTimingsCopyWithImpl;
@override @useResult
$Res call({
 String openTime, String closeTime, String? breakStartTime, String? breakEndTime, List<String> specialTimings, bool isOpen24Hours, List<String> closedDays
});




}
/// @nodoc
class __$TempleTimingsCopyWithImpl<$Res>
    implements _$TempleTimingsCopyWith<$Res> {
  __$TempleTimingsCopyWithImpl(this._self, this._then);

  final _TempleTimings _self;
  final $Res Function(_TempleTimings) _then;

/// Create a copy of TempleTimings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? openTime = null,Object? closeTime = null,Object? breakStartTime = freezed,Object? breakEndTime = freezed,Object? specialTimings = null,Object? isOpen24Hours = null,Object? closedDays = null,}) {
  return _then(_TempleTimings(
openTime: null == openTime ? _self.openTime : openTime // ignore: cast_nullable_to_non_nullable
as String,closeTime: null == closeTime ? _self.closeTime : closeTime // ignore: cast_nullable_to_non_nullable
as String,breakStartTime: freezed == breakStartTime ? _self.breakStartTime : breakStartTime // ignore: cast_nullable_to_non_nullable
as String?,breakEndTime: freezed == breakEndTime ? _self.breakEndTime : breakEndTime // ignore: cast_nullable_to_non_nullable
as String?,specialTimings: null == specialTimings ? _self._specialTimings : specialTimings // ignore: cast_nullable_to_non_nullable
as List<String>,isOpen24Hours: null == isOpen24Hours ? _self.isOpen24Hours : isOpen24Hours // ignore: cast_nullable_to_non_nullable
as bool,closedDays: null == closedDays ? _self._closedDays : closedDays // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}


/// @nodoc
mixin _$Darshan {

 String get id; String get name; DarshanType get type; String get description; double get price; int get duration;// in minutes
 List<String> get timeSlots; Map<String, dynamic> get requirements; List<String> get rules; bool get isActive; int get maxBookingsPerSlot; String? get specialInstructions;
/// Create a copy of Darshan
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DarshanCopyWith<Darshan> get copyWith => _$DarshanCopyWithImpl<Darshan>(this as Darshan, _$identity);

  /// Serializes this Darshan to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Darshan&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.description, description) || other.description == description)&&(identical(other.price, price) || other.price == price)&&(identical(other.duration, duration) || other.duration == duration)&&const DeepCollectionEquality().equals(other.timeSlots, timeSlots)&&const DeepCollectionEquality().equals(other.requirements, requirements)&&const DeepCollectionEquality().equals(other.rules, rules)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.maxBookingsPerSlot, maxBookingsPerSlot) || other.maxBookingsPerSlot == maxBookingsPerSlot)&&(identical(other.specialInstructions, specialInstructions) || other.specialInstructions == specialInstructions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,description,price,duration,const DeepCollectionEquality().hash(timeSlots),const DeepCollectionEquality().hash(requirements),const DeepCollectionEquality().hash(rules),isActive,maxBookingsPerSlot,specialInstructions);

@override
String toString() {
  return 'Darshan(id: $id, name: $name, type: $type, description: $description, price: $price, duration: $duration, timeSlots: $timeSlots, requirements: $requirements, rules: $rules, isActive: $isActive, maxBookingsPerSlot: $maxBookingsPerSlot, specialInstructions: $specialInstructions)';
}


}

/// @nodoc
abstract mixin class $DarshanCopyWith<$Res>  {
  factory $DarshanCopyWith(Darshan value, $Res Function(Darshan) _then) = _$DarshanCopyWithImpl;
@useResult
$Res call({
 String id, String name, DarshanType type, String description, double price, int duration, List<String> timeSlots, Map<String, dynamic> requirements, List<String> rules, bool isActive, int maxBookingsPerSlot, String? specialInstructions
});




}
/// @nodoc
class _$DarshanCopyWithImpl<$Res>
    implements $DarshanCopyWith<$Res> {
  _$DarshanCopyWithImpl(this._self, this._then);

  final Darshan _self;
  final $Res Function(Darshan) _then;

/// Create a copy of Darshan
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? type = null,Object? description = null,Object? price = null,Object? duration = null,Object? timeSlots = null,Object? requirements = null,Object? rules = null,Object? isActive = null,Object? maxBookingsPerSlot = null,Object? specialInstructions = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as DarshanType,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int,timeSlots: null == timeSlots ? _self.timeSlots : timeSlots // ignore: cast_nullable_to_non_nullable
as List<String>,requirements: null == requirements ? _self.requirements : requirements // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,rules: null == rules ? _self.rules : rules // ignore: cast_nullable_to_non_nullable
as List<String>,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,maxBookingsPerSlot: null == maxBookingsPerSlot ? _self.maxBookingsPerSlot : maxBookingsPerSlot // ignore: cast_nullable_to_non_nullable
as int,specialInstructions: freezed == specialInstructions ? _self.specialInstructions : specialInstructions // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [Darshan].
extension DarshanPatterns on Darshan {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Darshan value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Darshan() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Darshan value)  $default,){
final _that = this;
switch (_that) {
case _Darshan():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Darshan value)?  $default,){
final _that = this;
switch (_that) {
case _Darshan() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  DarshanType type,  String description,  double price,  int duration,  List<String> timeSlots,  Map<String, dynamic> requirements,  List<String> rules,  bool isActive,  int maxBookingsPerSlot,  String? specialInstructions)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Darshan() when $default != null:
return $default(_that.id,_that.name,_that.type,_that.description,_that.price,_that.duration,_that.timeSlots,_that.requirements,_that.rules,_that.isActive,_that.maxBookingsPerSlot,_that.specialInstructions);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  DarshanType type,  String description,  double price,  int duration,  List<String> timeSlots,  Map<String, dynamic> requirements,  List<String> rules,  bool isActive,  int maxBookingsPerSlot,  String? specialInstructions)  $default,) {final _that = this;
switch (_that) {
case _Darshan():
return $default(_that.id,_that.name,_that.type,_that.description,_that.price,_that.duration,_that.timeSlots,_that.requirements,_that.rules,_that.isActive,_that.maxBookingsPerSlot,_that.specialInstructions);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  DarshanType type,  String description,  double price,  int duration,  List<String> timeSlots,  Map<String, dynamic> requirements,  List<String> rules,  bool isActive,  int maxBookingsPerSlot,  String? specialInstructions)?  $default,) {final _that = this;
switch (_that) {
case _Darshan() when $default != null:
return $default(_that.id,_that.name,_that.type,_that.description,_that.price,_that.duration,_that.timeSlots,_that.requirements,_that.rules,_that.isActive,_that.maxBookingsPerSlot,_that.specialInstructions);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Darshan implements Darshan {
  const _Darshan({required this.id, required this.name, required this.type, required this.description, required this.price, required this.duration, required final  List<String> timeSlots, required final  Map<String, dynamic> requirements, required final  List<String> rules, this.isActive = true, this.maxBookingsPerSlot = 0, this.specialInstructions}): _timeSlots = timeSlots,_requirements = requirements,_rules = rules;
  factory _Darshan.fromJson(Map<String, dynamic> json) => _$DarshanFromJson(json);

@override final  String id;
@override final  String name;
@override final  DarshanType type;
@override final  String description;
@override final  double price;
@override final  int duration;
// in minutes
 final  List<String> _timeSlots;
// in minutes
@override List<String> get timeSlots {
  if (_timeSlots is EqualUnmodifiableListView) return _timeSlots;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_timeSlots);
}

 final  Map<String, dynamic> _requirements;
@override Map<String, dynamic> get requirements {
  if (_requirements is EqualUnmodifiableMapView) return _requirements;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_requirements);
}

 final  List<String> _rules;
@override List<String> get rules {
  if (_rules is EqualUnmodifiableListView) return _rules;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_rules);
}

@override@JsonKey() final  bool isActive;
@override@JsonKey() final  int maxBookingsPerSlot;
@override final  String? specialInstructions;

/// Create a copy of Darshan
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DarshanCopyWith<_Darshan> get copyWith => __$DarshanCopyWithImpl<_Darshan>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DarshanToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Darshan&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.description, description) || other.description == description)&&(identical(other.price, price) || other.price == price)&&(identical(other.duration, duration) || other.duration == duration)&&const DeepCollectionEquality().equals(other._timeSlots, _timeSlots)&&const DeepCollectionEquality().equals(other._requirements, _requirements)&&const DeepCollectionEquality().equals(other._rules, _rules)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.maxBookingsPerSlot, maxBookingsPerSlot) || other.maxBookingsPerSlot == maxBookingsPerSlot)&&(identical(other.specialInstructions, specialInstructions) || other.specialInstructions == specialInstructions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,description,price,duration,const DeepCollectionEquality().hash(_timeSlots),const DeepCollectionEquality().hash(_requirements),const DeepCollectionEquality().hash(_rules),isActive,maxBookingsPerSlot,specialInstructions);

@override
String toString() {
  return 'Darshan(id: $id, name: $name, type: $type, description: $description, price: $price, duration: $duration, timeSlots: $timeSlots, requirements: $requirements, rules: $rules, isActive: $isActive, maxBookingsPerSlot: $maxBookingsPerSlot, specialInstructions: $specialInstructions)';
}


}

/// @nodoc
abstract mixin class _$DarshanCopyWith<$Res> implements $DarshanCopyWith<$Res> {
  factory _$DarshanCopyWith(_Darshan value, $Res Function(_Darshan) _then) = __$DarshanCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, DarshanType type, String description, double price, int duration, List<String> timeSlots, Map<String, dynamic> requirements, List<String> rules, bool isActive, int maxBookingsPerSlot, String? specialInstructions
});




}
/// @nodoc
class __$DarshanCopyWithImpl<$Res>
    implements _$DarshanCopyWith<$Res> {
  __$DarshanCopyWithImpl(this._self, this._then);

  final _Darshan _self;
  final $Res Function(_Darshan) _then;

/// Create a copy of Darshan
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? type = null,Object? description = null,Object? price = null,Object? duration = null,Object? timeSlots = null,Object? requirements = null,Object? rules = null,Object? isActive = null,Object? maxBookingsPerSlot = null,Object? specialInstructions = freezed,}) {
  return _then(_Darshan(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as DarshanType,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int,timeSlots: null == timeSlots ? _self._timeSlots : timeSlots // ignore: cast_nullable_to_non_nullable
as List<String>,requirements: null == requirements ? _self._requirements : requirements // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,rules: null == rules ? _self._rules : rules // ignore: cast_nullable_to_non_nullable
as List<String>,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,maxBookingsPerSlot: null == maxBookingsPerSlot ? _self.maxBookingsPerSlot : maxBookingsPerSlot // ignore: cast_nullable_to_non_nullable
as int,specialInstructions: freezed == specialInstructions ? _self.specialInstructions : specialInstructions // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$Seva {

 String get id; String get name; SevaType get type; String get description; double get price; int get duration;// in minutes
 List<String> get timeSlots; Map<String, dynamic> get requirements; List<String> get rules; bool get isActive; int get maxBookingsPerSlot; String? get specialInstructions; List<String> get includedItems;
/// Create a copy of Seva
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SevaCopyWith<Seva> get copyWith => _$SevaCopyWithImpl<Seva>(this as Seva, _$identity);

  /// Serializes this Seva to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Seva&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.description, description) || other.description == description)&&(identical(other.price, price) || other.price == price)&&(identical(other.duration, duration) || other.duration == duration)&&const DeepCollectionEquality().equals(other.timeSlots, timeSlots)&&const DeepCollectionEquality().equals(other.requirements, requirements)&&const DeepCollectionEquality().equals(other.rules, rules)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.maxBookingsPerSlot, maxBookingsPerSlot) || other.maxBookingsPerSlot == maxBookingsPerSlot)&&(identical(other.specialInstructions, specialInstructions) || other.specialInstructions == specialInstructions)&&const DeepCollectionEquality().equals(other.includedItems, includedItems));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,description,price,duration,const DeepCollectionEquality().hash(timeSlots),const DeepCollectionEquality().hash(requirements),const DeepCollectionEquality().hash(rules),isActive,maxBookingsPerSlot,specialInstructions,const DeepCollectionEquality().hash(includedItems));

@override
String toString() {
  return 'Seva(id: $id, name: $name, type: $type, description: $description, price: $price, duration: $duration, timeSlots: $timeSlots, requirements: $requirements, rules: $rules, isActive: $isActive, maxBookingsPerSlot: $maxBookingsPerSlot, specialInstructions: $specialInstructions, includedItems: $includedItems)';
}


}

/// @nodoc
abstract mixin class $SevaCopyWith<$Res>  {
  factory $SevaCopyWith(Seva value, $Res Function(Seva) _then) = _$SevaCopyWithImpl;
@useResult
$Res call({
 String id, String name, SevaType type, String description, double price, int duration, List<String> timeSlots, Map<String, dynamic> requirements, List<String> rules, bool isActive, int maxBookingsPerSlot, String? specialInstructions, List<String> includedItems
});




}
/// @nodoc
class _$SevaCopyWithImpl<$Res>
    implements $SevaCopyWith<$Res> {
  _$SevaCopyWithImpl(this._self, this._then);

  final Seva _self;
  final $Res Function(Seva) _then;

/// Create a copy of Seva
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? type = null,Object? description = null,Object? price = null,Object? duration = null,Object? timeSlots = null,Object? requirements = null,Object? rules = null,Object? isActive = null,Object? maxBookingsPerSlot = null,Object? specialInstructions = freezed,Object? includedItems = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as SevaType,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int,timeSlots: null == timeSlots ? _self.timeSlots : timeSlots // ignore: cast_nullable_to_non_nullable
as List<String>,requirements: null == requirements ? _self.requirements : requirements // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,rules: null == rules ? _self.rules : rules // ignore: cast_nullable_to_non_nullable
as List<String>,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,maxBookingsPerSlot: null == maxBookingsPerSlot ? _self.maxBookingsPerSlot : maxBookingsPerSlot // ignore: cast_nullable_to_non_nullable
as int,specialInstructions: freezed == specialInstructions ? _self.specialInstructions : specialInstructions // ignore: cast_nullable_to_non_nullable
as String?,includedItems: null == includedItems ? _self.includedItems : includedItems // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [Seva].
extension SevaPatterns on Seva {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Seva value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Seva() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Seva value)  $default,){
final _that = this;
switch (_that) {
case _Seva():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Seva value)?  $default,){
final _that = this;
switch (_that) {
case _Seva() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  SevaType type,  String description,  double price,  int duration,  List<String> timeSlots,  Map<String, dynamic> requirements,  List<String> rules,  bool isActive,  int maxBookingsPerSlot,  String? specialInstructions,  List<String> includedItems)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Seva() when $default != null:
return $default(_that.id,_that.name,_that.type,_that.description,_that.price,_that.duration,_that.timeSlots,_that.requirements,_that.rules,_that.isActive,_that.maxBookingsPerSlot,_that.specialInstructions,_that.includedItems);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  SevaType type,  String description,  double price,  int duration,  List<String> timeSlots,  Map<String, dynamic> requirements,  List<String> rules,  bool isActive,  int maxBookingsPerSlot,  String? specialInstructions,  List<String> includedItems)  $default,) {final _that = this;
switch (_that) {
case _Seva():
return $default(_that.id,_that.name,_that.type,_that.description,_that.price,_that.duration,_that.timeSlots,_that.requirements,_that.rules,_that.isActive,_that.maxBookingsPerSlot,_that.specialInstructions,_that.includedItems);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  SevaType type,  String description,  double price,  int duration,  List<String> timeSlots,  Map<String, dynamic> requirements,  List<String> rules,  bool isActive,  int maxBookingsPerSlot,  String? specialInstructions,  List<String> includedItems)?  $default,) {final _that = this;
switch (_that) {
case _Seva() when $default != null:
return $default(_that.id,_that.name,_that.type,_that.description,_that.price,_that.duration,_that.timeSlots,_that.requirements,_that.rules,_that.isActive,_that.maxBookingsPerSlot,_that.specialInstructions,_that.includedItems);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Seva implements Seva {
  const _Seva({required this.id, required this.name, required this.type, required this.description, required this.price, required this.duration, required final  List<String> timeSlots, required final  Map<String, dynamic> requirements, required final  List<String> rules, this.isActive = true, this.maxBookingsPerSlot = 0, this.specialInstructions, final  List<String> includedItems = const []}): _timeSlots = timeSlots,_requirements = requirements,_rules = rules,_includedItems = includedItems;
  factory _Seva.fromJson(Map<String, dynamic> json) => _$SevaFromJson(json);

@override final  String id;
@override final  String name;
@override final  SevaType type;
@override final  String description;
@override final  double price;
@override final  int duration;
// in minutes
 final  List<String> _timeSlots;
// in minutes
@override List<String> get timeSlots {
  if (_timeSlots is EqualUnmodifiableListView) return _timeSlots;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_timeSlots);
}

 final  Map<String, dynamic> _requirements;
@override Map<String, dynamic> get requirements {
  if (_requirements is EqualUnmodifiableMapView) return _requirements;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_requirements);
}

 final  List<String> _rules;
@override List<String> get rules {
  if (_rules is EqualUnmodifiableListView) return _rules;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_rules);
}

@override@JsonKey() final  bool isActive;
@override@JsonKey() final  int maxBookingsPerSlot;
@override final  String? specialInstructions;
 final  List<String> _includedItems;
@override@JsonKey() List<String> get includedItems {
  if (_includedItems is EqualUnmodifiableListView) return _includedItems;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_includedItems);
}


/// Create a copy of Seva
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SevaCopyWith<_Seva> get copyWith => __$SevaCopyWithImpl<_Seva>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SevaToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Seva&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.description, description) || other.description == description)&&(identical(other.price, price) || other.price == price)&&(identical(other.duration, duration) || other.duration == duration)&&const DeepCollectionEquality().equals(other._timeSlots, _timeSlots)&&const DeepCollectionEquality().equals(other._requirements, _requirements)&&const DeepCollectionEquality().equals(other._rules, _rules)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.maxBookingsPerSlot, maxBookingsPerSlot) || other.maxBookingsPerSlot == maxBookingsPerSlot)&&(identical(other.specialInstructions, specialInstructions) || other.specialInstructions == specialInstructions)&&const DeepCollectionEquality().equals(other._includedItems, _includedItems));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,description,price,duration,const DeepCollectionEquality().hash(_timeSlots),const DeepCollectionEquality().hash(_requirements),const DeepCollectionEquality().hash(_rules),isActive,maxBookingsPerSlot,specialInstructions,const DeepCollectionEquality().hash(_includedItems));

@override
String toString() {
  return 'Seva(id: $id, name: $name, type: $type, description: $description, price: $price, duration: $duration, timeSlots: $timeSlots, requirements: $requirements, rules: $rules, isActive: $isActive, maxBookingsPerSlot: $maxBookingsPerSlot, specialInstructions: $specialInstructions, includedItems: $includedItems)';
}


}

/// @nodoc
abstract mixin class _$SevaCopyWith<$Res> implements $SevaCopyWith<$Res> {
  factory _$SevaCopyWith(_Seva value, $Res Function(_Seva) _then) = __$SevaCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, SevaType type, String description, double price, int duration, List<String> timeSlots, Map<String, dynamic> requirements, List<String> rules, bool isActive, int maxBookingsPerSlot, String? specialInstructions, List<String> includedItems
});




}
/// @nodoc
class __$SevaCopyWithImpl<$Res>
    implements _$SevaCopyWith<$Res> {
  __$SevaCopyWithImpl(this._self, this._then);

  final _Seva _self;
  final $Res Function(_Seva) _then;

/// Create a copy of Seva
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? type = null,Object? description = null,Object? price = null,Object? duration = null,Object? timeSlots = null,Object? requirements = null,Object? rules = null,Object? isActive = null,Object? maxBookingsPerSlot = null,Object? specialInstructions = freezed,Object? includedItems = null,}) {
  return _then(_Seva(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as SevaType,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int,timeSlots: null == timeSlots ? _self._timeSlots : timeSlots // ignore: cast_nullable_to_non_nullable
as List<String>,requirements: null == requirements ? _self._requirements : requirements // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,rules: null == rules ? _self._rules : rules // ignore: cast_nullable_to_non_nullable
as List<String>,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,maxBookingsPerSlot: null == maxBookingsPerSlot ? _self.maxBookingsPerSlot : maxBookingsPerSlot // ignore: cast_nullable_to_non_nullable
as int,specialInstructions: freezed == specialInstructions ? _self.specialInstructions : specialInstructions // ignore: cast_nullable_to_non_nullable
as String?,includedItems: null == includedItems ? _self._includedItems : includedItems // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}


/// @nodoc
mixin _$Temple {

 String get id; String get name; String get description; TempleType get type; TempleStatus get status; TempleLocation get location; TempleContact get contact; Map<String, TempleTimings> get weeklyTimings; List<String> get deities; List<String> get festivals; List<Darshan> get darshans; List<Seva> get sevas; List<String> get images; List<String> get facilities; List<String> get languages; double get rating; int get totalReviews; DateTime get createdAt; DateTime get updatedAt; bool get isVerified; bool get isFamous; bool get isPilgrimage; bool get isDeleted; String? get coverImage; String? get history; String? get significance; String? get architecture; String? get managedBy; String? get managerName; String? get registrationNumber; String? get verificationNotes; String? get rejectionReason; Map<String, dynamic>? get additionalInfo;
/// Create a copy of Temple
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TempleCopyWith<Temple> get copyWith => _$TempleCopyWithImpl<Temple>(this as Temple, _$identity);

  /// Serializes this Temple to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Temple&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.location, location) || other.location == location)&&(identical(other.contact, contact) || other.contact == contact)&&const DeepCollectionEquality().equals(other.weeklyTimings, weeklyTimings)&&const DeepCollectionEquality().equals(other.deities, deities)&&const DeepCollectionEquality().equals(other.festivals, festivals)&&const DeepCollectionEquality().equals(other.darshans, darshans)&&const DeepCollectionEquality().equals(other.sevas, sevas)&&const DeepCollectionEquality().equals(other.images, images)&&const DeepCollectionEquality().equals(other.facilities, facilities)&&const DeepCollectionEquality().equals(other.languages, languages)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isFamous, isFamous) || other.isFamous == isFamous)&&(identical(other.isPilgrimage, isPilgrimage) || other.isPilgrimage == isPilgrimage)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.coverImage, coverImage) || other.coverImage == coverImage)&&(identical(other.history, history) || other.history == history)&&(identical(other.significance, significance) || other.significance == significance)&&(identical(other.architecture, architecture) || other.architecture == architecture)&&(identical(other.managedBy, managedBy) || other.managedBy == managedBy)&&(identical(other.managerName, managerName) || other.managerName == managerName)&&(identical(other.registrationNumber, registrationNumber) || other.registrationNumber == registrationNumber)&&(identical(other.verificationNotes, verificationNotes) || other.verificationNotes == verificationNotes)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&const DeepCollectionEquality().equals(other.additionalInfo, additionalInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,description,type,status,location,contact,const DeepCollectionEquality().hash(weeklyTimings),const DeepCollectionEquality().hash(deities),const DeepCollectionEquality().hash(festivals),const DeepCollectionEquality().hash(darshans),const DeepCollectionEquality().hash(sevas),const DeepCollectionEquality().hash(images),const DeepCollectionEquality().hash(facilities),const DeepCollectionEquality().hash(languages),rating,totalReviews,createdAt,updatedAt,isVerified,isFamous,isPilgrimage,isDeleted,coverImage,history,significance,architecture,managedBy,managerName,registrationNumber,verificationNotes,rejectionReason,const DeepCollectionEquality().hash(additionalInfo)]);

@override
String toString() {
  return 'Temple(id: $id, name: $name, description: $description, type: $type, status: $status, location: $location, contact: $contact, weeklyTimings: $weeklyTimings, deities: $deities, festivals: $festivals, darshans: $darshans, sevas: $sevas, images: $images, facilities: $facilities, languages: $languages, rating: $rating, totalReviews: $totalReviews, createdAt: $createdAt, updatedAt: $updatedAt, isVerified: $isVerified, isFamous: $isFamous, isPilgrimage: $isPilgrimage, isDeleted: $isDeleted, coverImage: $coverImage, history: $history, significance: $significance, architecture: $architecture, managedBy: $managedBy, managerName: $managerName, registrationNumber: $registrationNumber, verificationNotes: $verificationNotes, rejectionReason: $rejectionReason, additionalInfo: $additionalInfo)';
}


}

/// @nodoc
abstract mixin class $TempleCopyWith<$Res>  {
  factory $TempleCopyWith(Temple value, $Res Function(Temple) _then) = _$TempleCopyWithImpl;
@useResult
$Res call({
 String id, String name, String description, TempleType type, TempleStatus status, TempleLocation location, TempleContact contact, Map<String, TempleTimings> weeklyTimings, List<String> deities, List<String> festivals, List<Darshan> darshans, List<Seva> sevas, List<String> images, List<String> facilities, List<String> languages, double rating, int totalReviews, DateTime createdAt, DateTime updatedAt, bool isVerified, bool isFamous, bool isPilgrimage, bool isDeleted, String? coverImage, String? history, String? significance, String? architecture, String? managedBy, String? managerName, String? registrationNumber, String? verificationNotes, String? rejectionReason, Map<String, dynamic>? additionalInfo
});


$TempleLocationCopyWith<$Res> get location;$TempleContactCopyWith<$Res> get contact;

}
/// @nodoc
class _$TempleCopyWithImpl<$Res>
    implements $TempleCopyWith<$Res> {
  _$TempleCopyWithImpl(this._self, this._then);

  final Temple _self;
  final $Res Function(Temple) _then;

/// Create a copy of Temple
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? description = null,Object? type = null,Object? status = null,Object? location = null,Object? contact = null,Object? weeklyTimings = null,Object? deities = null,Object? festivals = null,Object? darshans = null,Object? sevas = null,Object? images = null,Object? facilities = null,Object? languages = null,Object? rating = null,Object? totalReviews = null,Object? createdAt = null,Object? updatedAt = null,Object? isVerified = null,Object? isFamous = null,Object? isPilgrimage = null,Object? isDeleted = null,Object? coverImage = freezed,Object? history = freezed,Object? significance = freezed,Object? architecture = freezed,Object? managedBy = freezed,Object? managerName = freezed,Object? registrationNumber = freezed,Object? verificationNotes = freezed,Object? rejectionReason = freezed,Object? additionalInfo = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as TempleType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as TempleStatus,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as TempleLocation,contact: null == contact ? _self.contact : contact // ignore: cast_nullable_to_non_nullable
as TempleContact,weeklyTimings: null == weeklyTimings ? _self.weeklyTimings : weeklyTimings // ignore: cast_nullable_to_non_nullable
as Map<String, TempleTimings>,deities: null == deities ? _self.deities : deities // ignore: cast_nullable_to_non_nullable
as List<String>,festivals: null == festivals ? _self.festivals : festivals // ignore: cast_nullable_to_non_nullable
as List<String>,darshans: null == darshans ? _self.darshans : darshans // ignore: cast_nullable_to_non_nullable
as List<Darshan>,sevas: null == sevas ? _self.sevas : sevas // ignore: cast_nullable_to_non_nullable
as List<Seva>,images: null == images ? _self.images : images // ignore: cast_nullable_to_non_nullable
as List<String>,facilities: null == facilities ? _self.facilities : facilities // ignore: cast_nullable_to_non_nullable
as List<String>,languages: null == languages ? _self.languages : languages // ignore: cast_nullable_to_non_nullable
as List<String>,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isFamous: null == isFamous ? _self.isFamous : isFamous // ignore: cast_nullable_to_non_nullable
as bool,isPilgrimage: null == isPilgrimage ? _self.isPilgrimage : isPilgrimage // ignore: cast_nullable_to_non_nullable
as bool,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,coverImage: freezed == coverImage ? _self.coverImage : coverImage // ignore: cast_nullable_to_non_nullable
as String?,history: freezed == history ? _self.history : history // ignore: cast_nullable_to_non_nullable
as String?,significance: freezed == significance ? _self.significance : significance // ignore: cast_nullable_to_non_nullable
as String?,architecture: freezed == architecture ? _self.architecture : architecture // ignore: cast_nullable_to_non_nullable
as String?,managedBy: freezed == managedBy ? _self.managedBy : managedBy // ignore: cast_nullable_to_non_nullable
as String?,managerName: freezed == managerName ? _self.managerName : managerName // ignore: cast_nullable_to_non_nullable
as String?,registrationNumber: freezed == registrationNumber ? _self.registrationNumber : registrationNumber // ignore: cast_nullable_to_non_nullable
as String?,verificationNotes: freezed == verificationNotes ? _self.verificationNotes : verificationNotes // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,additionalInfo: freezed == additionalInfo ? _self.additionalInfo : additionalInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}
/// Create a copy of Temple
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TempleLocationCopyWith<$Res> get location {
  
  return $TempleLocationCopyWith<$Res>(_self.location, (value) {
    return _then(_self.copyWith(location: value));
  });
}/// Create a copy of Temple
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TempleContactCopyWith<$Res> get contact {
  
  return $TempleContactCopyWith<$Res>(_self.contact, (value) {
    return _then(_self.copyWith(contact: value));
  });
}
}


/// Adds pattern-matching-related methods to [Temple].
extension TemplePatterns on Temple {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Temple value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Temple() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Temple value)  $default,){
final _that = this;
switch (_that) {
case _Temple():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Temple value)?  $default,){
final _that = this;
switch (_that) {
case _Temple() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String description,  TempleType type,  TempleStatus status,  TempleLocation location,  TempleContact contact,  Map<String, TempleTimings> weeklyTimings,  List<String> deities,  List<String> festivals,  List<Darshan> darshans,  List<Seva> sevas,  List<String> images,  List<String> facilities,  List<String> languages,  double rating,  int totalReviews,  DateTime createdAt,  DateTime updatedAt,  bool isVerified,  bool isFamous,  bool isPilgrimage,  bool isDeleted,  String? coverImage,  String? history,  String? significance,  String? architecture,  String? managedBy,  String? managerName,  String? registrationNumber,  String? verificationNotes,  String? rejectionReason,  Map<String, dynamic>? additionalInfo)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Temple() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.type,_that.status,_that.location,_that.contact,_that.weeklyTimings,_that.deities,_that.festivals,_that.darshans,_that.sevas,_that.images,_that.facilities,_that.languages,_that.rating,_that.totalReviews,_that.createdAt,_that.updatedAt,_that.isVerified,_that.isFamous,_that.isPilgrimage,_that.isDeleted,_that.coverImage,_that.history,_that.significance,_that.architecture,_that.managedBy,_that.managerName,_that.registrationNumber,_that.verificationNotes,_that.rejectionReason,_that.additionalInfo);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String description,  TempleType type,  TempleStatus status,  TempleLocation location,  TempleContact contact,  Map<String, TempleTimings> weeklyTimings,  List<String> deities,  List<String> festivals,  List<Darshan> darshans,  List<Seva> sevas,  List<String> images,  List<String> facilities,  List<String> languages,  double rating,  int totalReviews,  DateTime createdAt,  DateTime updatedAt,  bool isVerified,  bool isFamous,  bool isPilgrimage,  bool isDeleted,  String? coverImage,  String? history,  String? significance,  String? architecture,  String? managedBy,  String? managerName,  String? registrationNumber,  String? verificationNotes,  String? rejectionReason,  Map<String, dynamic>? additionalInfo)  $default,) {final _that = this;
switch (_that) {
case _Temple():
return $default(_that.id,_that.name,_that.description,_that.type,_that.status,_that.location,_that.contact,_that.weeklyTimings,_that.deities,_that.festivals,_that.darshans,_that.sevas,_that.images,_that.facilities,_that.languages,_that.rating,_that.totalReviews,_that.createdAt,_that.updatedAt,_that.isVerified,_that.isFamous,_that.isPilgrimage,_that.isDeleted,_that.coverImage,_that.history,_that.significance,_that.architecture,_that.managedBy,_that.managerName,_that.registrationNumber,_that.verificationNotes,_that.rejectionReason,_that.additionalInfo);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String description,  TempleType type,  TempleStatus status,  TempleLocation location,  TempleContact contact,  Map<String, TempleTimings> weeklyTimings,  List<String> deities,  List<String> festivals,  List<Darshan> darshans,  List<Seva> sevas,  List<String> images,  List<String> facilities,  List<String> languages,  double rating,  int totalReviews,  DateTime createdAt,  DateTime updatedAt,  bool isVerified,  bool isFamous,  bool isPilgrimage,  bool isDeleted,  String? coverImage,  String? history,  String? significance,  String? architecture,  String? managedBy,  String? managerName,  String? registrationNumber,  String? verificationNotes,  String? rejectionReason,  Map<String, dynamic>? additionalInfo)?  $default,) {final _that = this;
switch (_that) {
case _Temple() when $default != null:
return $default(_that.id,_that.name,_that.description,_that.type,_that.status,_that.location,_that.contact,_that.weeklyTimings,_that.deities,_that.festivals,_that.darshans,_that.sevas,_that.images,_that.facilities,_that.languages,_that.rating,_that.totalReviews,_that.createdAt,_that.updatedAt,_that.isVerified,_that.isFamous,_that.isPilgrimage,_that.isDeleted,_that.coverImage,_that.history,_that.significance,_that.architecture,_that.managedBy,_that.managerName,_that.registrationNumber,_that.verificationNotes,_that.rejectionReason,_that.additionalInfo);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Temple implements Temple {
  const _Temple({required this.id, required this.name, required this.description, required this.type, required this.status, required this.location, required this.contact, required final  Map<String, TempleTimings> weeklyTimings, required final  List<String> deities, required final  List<String> festivals, required final  List<Darshan> darshans, required final  List<Seva> sevas, required final  List<String> images, required final  List<String> facilities, required final  List<String> languages, required this.rating, required this.totalReviews, required this.createdAt, required this.updatedAt, this.isVerified = false, this.isFamous = false, this.isPilgrimage = false, this.isDeleted = false, this.coverImage, this.history, this.significance, this.architecture, this.managedBy, this.managerName, this.registrationNumber, this.verificationNotes, this.rejectionReason, final  Map<String, dynamic>? additionalInfo}): _weeklyTimings = weeklyTimings,_deities = deities,_festivals = festivals,_darshans = darshans,_sevas = sevas,_images = images,_facilities = facilities,_languages = languages,_additionalInfo = additionalInfo;
  factory _Temple.fromJson(Map<String, dynamic> json) => _$TempleFromJson(json);

@override final  String id;
@override final  String name;
@override final  String description;
@override final  TempleType type;
@override final  TempleStatus status;
@override final  TempleLocation location;
@override final  TempleContact contact;
 final  Map<String, TempleTimings> _weeklyTimings;
@override Map<String, TempleTimings> get weeklyTimings {
  if (_weeklyTimings is EqualUnmodifiableMapView) return _weeklyTimings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_weeklyTimings);
}

 final  List<String> _deities;
@override List<String> get deities {
  if (_deities is EqualUnmodifiableListView) return _deities;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_deities);
}

 final  List<String> _festivals;
@override List<String> get festivals {
  if (_festivals is EqualUnmodifiableListView) return _festivals;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_festivals);
}

 final  List<Darshan> _darshans;
@override List<Darshan> get darshans {
  if (_darshans is EqualUnmodifiableListView) return _darshans;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_darshans);
}

 final  List<Seva> _sevas;
@override List<Seva> get sevas {
  if (_sevas is EqualUnmodifiableListView) return _sevas;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_sevas);
}

 final  List<String> _images;
@override List<String> get images {
  if (_images is EqualUnmodifiableListView) return _images;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_images);
}

 final  List<String> _facilities;
@override List<String> get facilities {
  if (_facilities is EqualUnmodifiableListView) return _facilities;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_facilities);
}

 final  List<String> _languages;
@override List<String> get languages {
  if (_languages is EqualUnmodifiableListView) return _languages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_languages);
}

@override final  double rating;
@override final  int totalReviews;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool isVerified;
@override@JsonKey() final  bool isFamous;
@override@JsonKey() final  bool isPilgrimage;
@override@JsonKey() final  bool isDeleted;
@override final  String? coverImage;
@override final  String? history;
@override final  String? significance;
@override final  String? architecture;
@override final  String? managedBy;
@override final  String? managerName;
@override final  String? registrationNumber;
@override final  String? verificationNotes;
@override final  String? rejectionReason;
 final  Map<String, dynamic>? _additionalInfo;
@override Map<String, dynamic>? get additionalInfo {
  final value = _additionalInfo;
  if (value == null) return null;
  if (_additionalInfo is EqualUnmodifiableMapView) return _additionalInfo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of Temple
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TempleCopyWith<_Temple> get copyWith => __$TempleCopyWithImpl<_Temple>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TempleToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Temple&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.location, location) || other.location == location)&&(identical(other.contact, contact) || other.contact == contact)&&const DeepCollectionEquality().equals(other._weeklyTimings, _weeklyTimings)&&const DeepCollectionEquality().equals(other._deities, _deities)&&const DeepCollectionEquality().equals(other._festivals, _festivals)&&const DeepCollectionEquality().equals(other._darshans, _darshans)&&const DeepCollectionEquality().equals(other._sevas, _sevas)&&const DeepCollectionEquality().equals(other._images, _images)&&const DeepCollectionEquality().equals(other._facilities, _facilities)&&const DeepCollectionEquality().equals(other._languages, _languages)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.totalReviews, totalReviews) || other.totalReviews == totalReviews)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isFamous, isFamous) || other.isFamous == isFamous)&&(identical(other.isPilgrimage, isPilgrimage) || other.isPilgrimage == isPilgrimage)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.coverImage, coverImage) || other.coverImage == coverImage)&&(identical(other.history, history) || other.history == history)&&(identical(other.significance, significance) || other.significance == significance)&&(identical(other.architecture, architecture) || other.architecture == architecture)&&(identical(other.managedBy, managedBy) || other.managedBy == managedBy)&&(identical(other.managerName, managerName) || other.managerName == managerName)&&(identical(other.registrationNumber, registrationNumber) || other.registrationNumber == registrationNumber)&&(identical(other.verificationNotes, verificationNotes) || other.verificationNotes == verificationNotes)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&const DeepCollectionEquality().equals(other._additionalInfo, _additionalInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,description,type,status,location,contact,const DeepCollectionEquality().hash(_weeklyTimings),const DeepCollectionEquality().hash(_deities),const DeepCollectionEquality().hash(_festivals),const DeepCollectionEquality().hash(_darshans),const DeepCollectionEquality().hash(_sevas),const DeepCollectionEquality().hash(_images),const DeepCollectionEquality().hash(_facilities),const DeepCollectionEquality().hash(_languages),rating,totalReviews,createdAt,updatedAt,isVerified,isFamous,isPilgrimage,isDeleted,coverImage,history,significance,architecture,managedBy,managerName,registrationNumber,verificationNotes,rejectionReason,const DeepCollectionEquality().hash(_additionalInfo)]);

@override
String toString() {
  return 'Temple(id: $id, name: $name, description: $description, type: $type, status: $status, location: $location, contact: $contact, weeklyTimings: $weeklyTimings, deities: $deities, festivals: $festivals, darshans: $darshans, sevas: $sevas, images: $images, facilities: $facilities, languages: $languages, rating: $rating, totalReviews: $totalReviews, createdAt: $createdAt, updatedAt: $updatedAt, isVerified: $isVerified, isFamous: $isFamous, isPilgrimage: $isPilgrimage, isDeleted: $isDeleted, coverImage: $coverImage, history: $history, significance: $significance, architecture: $architecture, managedBy: $managedBy, managerName: $managerName, registrationNumber: $registrationNumber, verificationNotes: $verificationNotes, rejectionReason: $rejectionReason, additionalInfo: $additionalInfo)';
}


}

/// @nodoc
abstract mixin class _$TempleCopyWith<$Res> implements $TempleCopyWith<$Res> {
  factory _$TempleCopyWith(_Temple value, $Res Function(_Temple) _then) = __$TempleCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String description, TempleType type, TempleStatus status, TempleLocation location, TempleContact contact, Map<String, TempleTimings> weeklyTimings, List<String> deities, List<String> festivals, List<Darshan> darshans, List<Seva> sevas, List<String> images, List<String> facilities, List<String> languages, double rating, int totalReviews, DateTime createdAt, DateTime updatedAt, bool isVerified, bool isFamous, bool isPilgrimage, bool isDeleted, String? coverImage, String? history, String? significance, String? architecture, String? managedBy, String? managerName, String? registrationNumber, String? verificationNotes, String? rejectionReason, Map<String, dynamic>? additionalInfo
});


@override $TempleLocationCopyWith<$Res> get location;@override $TempleContactCopyWith<$Res> get contact;

}
/// @nodoc
class __$TempleCopyWithImpl<$Res>
    implements _$TempleCopyWith<$Res> {
  __$TempleCopyWithImpl(this._self, this._then);

  final _Temple _self;
  final $Res Function(_Temple) _then;

/// Create a copy of Temple
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? description = null,Object? type = null,Object? status = null,Object? location = null,Object? contact = null,Object? weeklyTimings = null,Object? deities = null,Object? festivals = null,Object? darshans = null,Object? sevas = null,Object? images = null,Object? facilities = null,Object? languages = null,Object? rating = null,Object? totalReviews = null,Object? createdAt = null,Object? updatedAt = null,Object? isVerified = null,Object? isFamous = null,Object? isPilgrimage = null,Object? isDeleted = null,Object? coverImage = freezed,Object? history = freezed,Object? significance = freezed,Object? architecture = freezed,Object? managedBy = freezed,Object? managerName = freezed,Object? registrationNumber = freezed,Object? verificationNotes = freezed,Object? rejectionReason = freezed,Object? additionalInfo = freezed,}) {
  return _then(_Temple(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as TempleType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as TempleStatus,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as TempleLocation,contact: null == contact ? _self.contact : contact // ignore: cast_nullable_to_non_nullable
as TempleContact,weeklyTimings: null == weeklyTimings ? _self._weeklyTimings : weeklyTimings // ignore: cast_nullable_to_non_nullable
as Map<String, TempleTimings>,deities: null == deities ? _self._deities : deities // ignore: cast_nullable_to_non_nullable
as List<String>,festivals: null == festivals ? _self._festivals : festivals // ignore: cast_nullable_to_non_nullable
as List<String>,darshans: null == darshans ? _self._darshans : darshans // ignore: cast_nullable_to_non_nullable
as List<Darshan>,sevas: null == sevas ? _self._sevas : sevas // ignore: cast_nullable_to_non_nullable
as List<Seva>,images: null == images ? _self._images : images // ignore: cast_nullable_to_non_nullable
as List<String>,facilities: null == facilities ? _self._facilities : facilities // ignore: cast_nullable_to_non_nullable
as List<String>,languages: null == languages ? _self._languages : languages // ignore: cast_nullable_to_non_nullable
as List<String>,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,totalReviews: null == totalReviews ? _self.totalReviews : totalReviews // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isFamous: null == isFamous ? _self.isFamous : isFamous // ignore: cast_nullable_to_non_nullable
as bool,isPilgrimage: null == isPilgrimage ? _self.isPilgrimage : isPilgrimage // ignore: cast_nullable_to_non_nullable
as bool,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,coverImage: freezed == coverImage ? _self.coverImage : coverImage // ignore: cast_nullable_to_non_nullable
as String?,history: freezed == history ? _self.history : history // ignore: cast_nullable_to_non_nullable
as String?,significance: freezed == significance ? _self.significance : significance // ignore: cast_nullable_to_non_nullable
as String?,architecture: freezed == architecture ? _self.architecture : architecture // ignore: cast_nullable_to_non_nullable
as String?,managedBy: freezed == managedBy ? _self.managedBy : managedBy // ignore: cast_nullable_to_non_nullable
as String?,managerName: freezed == managerName ? _self.managerName : managerName // ignore: cast_nullable_to_non_nullable
as String?,registrationNumber: freezed == registrationNumber ? _self.registrationNumber : registrationNumber // ignore: cast_nullable_to_non_nullable
as String?,verificationNotes: freezed == verificationNotes ? _self.verificationNotes : verificationNotes // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,additionalInfo: freezed == additionalInfo ? _self._additionalInfo : additionalInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

/// Create a copy of Temple
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TempleLocationCopyWith<$Res> get location {
  
  return $TempleLocationCopyWith<$Res>(_self.location, (value) {
    return _then(_self.copyWith(location: value));
  });
}/// Create a copy of Temple
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TempleContactCopyWith<$Res> get contact {
  
  return $TempleContactCopyWith<$Res>(_self.contact, (value) {
    return _then(_self.copyWith(contact: value));
  });
}
}

// dart format on
