import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../shared/providers/chat_provider.dart';
import 'widgets/chat_input_widget.dart';
import 'widgets/chat_message_widget.dart';

class ChatScreen extends ConsumerStatefulWidget {
  const ChatScreen({super.key});

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      // Load more messages if needed
    }
  }

  @override
  Widget build(BuildContext context) {
    final chatState = ref.watch(chatStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Chat'),
      ),
      body: Column(
        children: [
          Expanded(
            child: chatState.isLoading
                ? const Center(child: CircularProgressIndicator())
                : chatState.error != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              chatState.error!,
                              style: const TextStyle(color: Colors.red),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () {
                                ref
                                    .read(chatStateProvider.notifier)
                                    .clearError();
                              },
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        controller: _scrollController,
                        reverse: true,
                        padding: const EdgeInsets.all(16),
                        itemCount: chatState.messages.length,
                        itemBuilder: (context, index) {
                          final message = chatState.messages[index];
                          return ChatMessageWidget(
                            message: message,
                            isPlaying:
                                message.id == chatState.currentlyPlayingId,
                            onPlayVoice: () {
                              ref
                                  .read(chatStateProvider.notifier)
                                  .startPlaying(message.id);
                            },
                            onStopVoice: () {
                              ref
                                  .read(chatStateProvider.notifier)
                                  .stopPlaying();
                            },
                          );
                        },
                      ),
          ),
          if (chatState.isRecording)
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.red.withValues(alpha: 0.1),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.mic, color: Colors.red),
                  SizedBox(width: 8),
                  Text(
                    'Recording...',
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
            ),
          ChatInputWidget(
            onSendMessage: (text) {
              ref.read(chatStateProvider.notifier).sendTextMessage(text);
            },
            onStartRecording: () {
              ref.read(chatStateProvider.notifier).startRecording();
            },
            onStopRecording: (path) {
              ref.read(chatStateProvider.notifier).stopRecording();
              ref.read(chatStateProvider.notifier).sendVoiceMessage(path);
            },
          ),
        ],
      ),
    );
  }
}
