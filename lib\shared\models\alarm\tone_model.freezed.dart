// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tone_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ToneModel {

 String get id; String get name; String get category; String get filePath; int get duration; bool get isDefault; DateTime get createdAt; DateTime get updatedAt; String get uploadedBy;// 'admin' or 'executor'
 ToneStatus get status; String? get rejectionReason; String? get approvedBy; DateTime? get approvedAt; String get language;// Language the tone is for (e.g., 'en', 'hi', 'te')
 bool get isMultiple;// Whether this is a single or multiple tone
 List<String> get daysOfWeek;
/// Create a copy of ToneModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ToneModelCopyWith<ToneModel> get copyWith => _$ToneModelCopyWithImpl<ToneModel>(this as ToneModel, _$identity);

  /// Serializes this ToneModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ToneModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.category, category) || other.category == category)&&(identical(other.filePath, filePath) || other.filePath == filePath)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.uploadedBy, uploadedBy) || other.uploadedBy == uploadedBy)&&(identical(other.status, status) || other.status == status)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.approvedBy, approvedBy) || other.approvedBy == approvedBy)&&(identical(other.approvedAt, approvedAt) || other.approvedAt == approvedAt)&&(identical(other.language, language) || other.language == language)&&(identical(other.isMultiple, isMultiple) || other.isMultiple == isMultiple)&&const DeepCollectionEquality().equals(other.daysOfWeek, daysOfWeek));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,category,filePath,duration,isDefault,createdAt,updatedAt,uploadedBy,status,rejectionReason,approvedBy,approvedAt,language,isMultiple,const DeepCollectionEquality().hash(daysOfWeek));

@override
String toString() {
  return 'ToneModel(id: $id, name: $name, category: $category, filePath: $filePath, duration: $duration, isDefault: $isDefault, createdAt: $createdAt, updatedAt: $updatedAt, uploadedBy: $uploadedBy, status: $status, rejectionReason: $rejectionReason, approvedBy: $approvedBy, approvedAt: $approvedAt, language: $language, isMultiple: $isMultiple, daysOfWeek: $daysOfWeek)';
}


}

/// @nodoc
abstract mixin class $ToneModelCopyWith<$Res>  {
  factory $ToneModelCopyWith(ToneModel value, $Res Function(ToneModel) _then) = _$ToneModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String category, String filePath, int duration, bool isDefault, DateTime createdAt, DateTime updatedAt, String uploadedBy, ToneStatus status, String? rejectionReason, String? approvedBy, DateTime? approvedAt, String language, bool isMultiple, List<String> daysOfWeek
});




}
/// @nodoc
class _$ToneModelCopyWithImpl<$Res>
    implements $ToneModelCopyWith<$Res> {
  _$ToneModelCopyWithImpl(this._self, this._then);

  final ToneModel _self;
  final $Res Function(ToneModel) _then;

/// Create a copy of ToneModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? category = null,Object? filePath = null,Object? duration = null,Object? isDefault = null,Object? createdAt = null,Object? updatedAt = null,Object? uploadedBy = null,Object? status = null,Object? rejectionReason = freezed,Object? approvedBy = freezed,Object? approvedAt = freezed,Object? language = null,Object? isMultiple = null,Object? daysOfWeek = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String,filePath: null == filePath ? _self.filePath : filePath // ignore: cast_nullable_to_non_nullable
as String,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int,isDefault: null == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,uploadedBy: null == uploadedBy ? _self.uploadedBy : uploadedBy // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ToneStatus,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,approvedBy: freezed == approvedBy ? _self.approvedBy : approvedBy // ignore: cast_nullable_to_non_nullable
as String?,approvedAt: freezed == approvedAt ? _self.approvedAt : approvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,isMultiple: null == isMultiple ? _self.isMultiple : isMultiple // ignore: cast_nullable_to_non_nullable
as bool,daysOfWeek: null == daysOfWeek ? _self.daysOfWeek : daysOfWeek // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}

}


/// Adds pattern-matching-related methods to [ToneModel].
extension ToneModelPatterns on ToneModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ToneModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ToneModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ToneModel value)  $default,){
final _that = this;
switch (_that) {
case _ToneModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ToneModel value)?  $default,){
final _that = this;
switch (_that) {
case _ToneModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String category,  String filePath,  int duration,  bool isDefault,  DateTime createdAt,  DateTime updatedAt,  String uploadedBy,  ToneStatus status,  String? rejectionReason,  String? approvedBy,  DateTime? approvedAt,  String language,  bool isMultiple,  List<String> daysOfWeek)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ToneModel() when $default != null:
return $default(_that.id,_that.name,_that.category,_that.filePath,_that.duration,_that.isDefault,_that.createdAt,_that.updatedAt,_that.uploadedBy,_that.status,_that.rejectionReason,_that.approvedBy,_that.approvedAt,_that.language,_that.isMultiple,_that.daysOfWeek);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String category,  String filePath,  int duration,  bool isDefault,  DateTime createdAt,  DateTime updatedAt,  String uploadedBy,  ToneStatus status,  String? rejectionReason,  String? approvedBy,  DateTime? approvedAt,  String language,  bool isMultiple,  List<String> daysOfWeek)  $default,) {final _that = this;
switch (_that) {
case _ToneModel():
return $default(_that.id,_that.name,_that.category,_that.filePath,_that.duration,_that.isDefault,_that.createdAt,_that.updatedAt,_that.uploadedBy,_that.status,_that.rejectionReason,_that.approvedBy,_that.approvedAt,_that.language,_that.isMultiple,_that.daysOfWeek);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String category,  String filePath,  int duration,  bool isDefault,  DateTime createdAt,  DateTime updatedAt,  String uploadedBy,  ToneStatus status,  String? rejectionReason,  String? approvedBy,  DateTime? approvedAt,  String language,  bool isMultiple,  List<String> daysOfWeek)?  $default,) {final _that = this;
switch (_that) {
case _ToneModel() when $default != null:
return $default(_that.id,_that.name,_that.category,_that.filePath,_that.duration,_that.isDefault,_that.createdAt,_that.updatedAt,_that.uploadedBy,_that.status,_that.rejectionReason,_that.approvedBy,_that.approvedAt,_that.language,_that.isMultiple,_that.daysOfWeek);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ToneModel implements ToneModel {
  const _ToneModel({required this.id, required this.name, required this.category, required this.filePath, required this.duration, required this.isDefault, required this.createdAt, required this.updatedAt, required this.uploadedBy, this.status = ToneStatus.pending, this.rejectionReason, this.approvedBy, this.approvedAt, this.language = 'en', this.isMultiple = false, final  List<String> daysOfWeek = const []}): _daysOfWeek = daysOfWeek;
  factory _ToneModel.fromJson(Map<String, dynamic> json) => _$ToneModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String category;
@override final  String filePath;
@override final  int duration;
@override final  bool isDefault;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override final  String uploadedBy;
// 'admin' or 'executor'
@override@JsonKey() final  ToneStatus status;
@override final  String? rejectionReason;
@override final  String? approvedBy;
@override final  DateTime? approvedAt;
@override@JsonKey() final  String language;
// Language the tone is for (e.g., 'en', 'hi', 'te')
@override@JsonKey() final  bool isMultiple;
// Whether this is a single or multiple tone
 final  List<String> _daysOfWeek;
// Whether this is a single or multiple tone
@override@JsonKey() List<String> get daysOfWeek {
  if (_daysOfWeek is EqualUnmodifiableListView) return _daysOfWeek;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_daysOfWeek);
}


/// Create a copy of ToneModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ToneModelCopyWith<_ToneModel> get copyWith => __$ToneModelCopyWithImpl<_ToneModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ToneModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ToneModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.category, category) || other.category == category)&&(identical(other.filePath, filePath) || other.filePath == filePath)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.uploadedBy, uploadedBy) || other.uploadedBy == uploadedBy)&&(identical(other.status, status) || other.status == status)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.approvedBy, approvedBy) || other.approvedBy == approvedBy)&&(identical(other.approvedAt, approvedAt) || other.approvedAt == approvedAt)&&(identical(other.language, language) || other.language == language)&&(identical(other.isMultiple, isMultiple) || other.isMultiple == isMultiple)&&const DeepCollectionEquality().equals(other._daysOfWeek, _daysOfWeek));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,category,filePath,duration,isDefault,createdAt,updatedAt,uploadedBy,status,rejectionReason,approvedBy,approvedAt,language,isMultiple,const DeepCollectionEquality().hash(_daysOfWeek));

@override
String toString() {
  return 'ToneModel(id: $id, name: $name, category: $category, filePath: $filePath, duration: $duration, isDefault: $isDefault, createdAt: $createdAt, updatedAt: $updatedAt, uploadedBy: $uploadedBy, status: $status, rejectionReason: $rejectionReason, approvedBy: $approvedBy, approvedAt: $approvedAt, language: $language, isMultiple: $isMultiple, daysOfWeek: $daysOfWeek)';
}


}

/// @nodoc
abstract mixin class _$ToneModelCopyWith<$Res> implements $ToneModelCopyWith<$Res> {
  factory _$ToneModelCopyWith(_ToneModel value, $Res Function(_ToneModel) _then) = __$ToneModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String category, String filePath, int duration, bool isDefault, DateTime createdAt, DateTime updatedAt, String uploadedBy, ToneStatus status, String? rejectionReason, String? approvedBy, DateTime? approvedAt, String language, bool isMultiple, List<String> daysOfWeek
});




}
/// @nodoc
class __$ToneModelCopyWithImpl<$Res>
    implements _$ToneModelCopyWith<$Res> {
  __$ToneModelCopyWithImpl(this._self, this._then);

  final _ToneModel _self;
  final $Res Function(_ToneModel) _then;

/// Create a copy of ToneModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? category = null,Object? filePath = null,Object? duration = null,Object? isDefault = null,Object? createdAt = null,Object? updatedAt = null,Object? uploadedBy = null,Object? status = null,Object? rejectionReason = freezed,Object? approvedBy = freezed,Object? approvedAt = freezed,Object? language = null,Object? isMultiple = null,Object? daysOfWeek = null,}) {
  return _then(_ToneModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as String,filePath: null == filePath ? _self.filePath : filePath // ignore: cast_nullable_to_non_nullable
as String,duration: null == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as int,isDefault: null == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,uploadedBy: null == uploadedBy ? _self.uploadedBy : uploadedBy // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ToneStatus,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,approvedBy: freezed == approvedBy ? _self.approvedBy : approvedBy // ignore: cast_nullable_to_non_nullable
as String?,approvedAt: freezed == approvedAt ? _self.approvedAt : approvedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,isMultiple: null == isMultiple ? _self.isMultiple : isMultiple // ignore: cast_nullable_to_non_nullable
as bool,daysOfWeek: null == daysOfWeek ? _self._daysOfWeek : daysOfWeek // ignore: cast_nullable_to_non_nullable
as List<String>,
  ));
}


}

// dart format on
