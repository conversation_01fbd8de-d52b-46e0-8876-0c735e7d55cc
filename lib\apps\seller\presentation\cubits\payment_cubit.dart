import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/apps/seller/domain/models/payment_model.dart';
import 'package:shivish/apps/seller/domain/repositories/payment_repository.dart';

part 'payment_cubit.freezed.dart';
part 'payment_cubit.g.dart';

@freezed
sealed class PaymentState with _$PaymentState {
  const factory PaymentState({
    @Default(false) bool isLoading,
    @Default(false) bool hasData,
    @Default(false) bool hasError,
    String? errorMessage,
    PaymentSummary? summary,
    @Default([]) List<TransactionModel> transactions,
    @Default([]) List<SettlementModel> settlements,
    @Default([]) List<BankAccountModel> bankAccounts,
  }) = _PaymentState;

  factory PaymentState.fromJson(Map<String, dynamic> json) =>
      _$PaymentStateFromJson(json);
}

class PaymentCubit extends Cubit<PaymentState> {
  final PaymentRepository _repository;

  PaymentCubit(this._repository) : super(const PaymentState());

  Future<void> loadPaymentSummary() async {
    try {
      emit(state.copyWith(isLoading: true, hasError: false));
      final summary = await _repository.getPaymentSummary();
      emit(state.copyWith(
        isLoading: false,
        hasData: true,
        summary: summary,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> loadTransactions() async {
    try {
      emit(state.copyWith(isLoading: true, hasError: false));
      final transactions = await _repository.getTransactions();
      emit(state.copyWith(
        isLoading: false,
        hasData: true,
        transactions: transactions,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> loadSettlements() async {
    try {
      emit(state.copyWith(isLoading: true, hasError: false));
      final settlements = await _repository.getSettlements();
      emit(state.copyWith(
        isLoading: false,
        hasData: true,
        settlements: settlements,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> loadBankAccounts() async {
    try {
      emit(state.copyWith(isLoading: true, hasError: false));
      final accounts = await _repository.getBankAccounts();
      emit(state.copyWith(
        isLoading: false,
        hasData: true,
        bankAccounts: accounts,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> addBankAccount(BankAccountModel account) async {
    try {
      emit(state.copyWith(isLoading: true, hasError: false));
      await _repository.addBankAccount(account);
      await loadBankAccounts();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> updateBankAccount(BankAccountModel account) async {
    try {
      emit(state.copyWith(isLoading: true, hasError: false));
      await _repository.updateBankAccount(account);
      await loadBankAccounts();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> deleteBankAccount(String accountId) async {
    try {
      emit(state.copyWith(isLoading: true, hasError: false));
      await _repository.deleteBankAccount(accountId);
      await loadBankAccounts();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> setPrimaryBankAccount(String accountId) async {
    try {
      emit(state.copyWith(isLoading: true, hasError: false));
      await _repository.setPrimaryBankAccount(accountId);
      await loadBankAccounts();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> requestSettlement({
    required double amount,
    required String bankAccountId,
  }) async {
    try {
      emit(state.copyWith(isLoading: true, hasError: false));
      await _repository.requestSettlement(
        amount: amount,
        bankAccountId: bankAccountId,
      );
      await Future.wait([
        loadPaymentSummary(),
        loadSettlements(),
      ]);
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }
}
