// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_management_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$UserManagementEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserManagementEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'UserManagementEvent()';
}


}

/// @nodoc
class $UserManagementEventCopyWith<$Res>  {
$UserManagementEventCopyWith(UserManagementEvent _, $Res Function(UserManagementEvent) __);
}


/// Adds pattern-matching-related methods to [UserManagementEvent].
extension UserManagementEventPatterns on UserManagementEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( LoadUsers value)?  loadUsers,TResult Function( AddUser value)?  addUser,TResult Function( UpdateUser value)?  updateUser,TResult Function( DeleteUser value)?  deleteUser,TResult Function( FilterUsers value)?  filterUsers,required TResult orElse(),}){
final _that = this;
switch (_that) {
case LoadUsers() when loadUsers != null:
return loadUsers(_that);case AddUser() when addUser != null:
return addUser(_that);case UpdateUser() when updateUser != null:
return updateUser(_that);case DeleteUser() when deleteUser != null:
return deleteUser(_that);case FilterUsers() when filterUsers != null:
return filterUsers(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( LoadUsers value)  loadUsers,required TResult Function( AddUser value)  addUser,required TResult Function( UpdateUser value)  updateUser,required TResult Function( DeleteUser value)  deleteUser,required TResult Function( FilterUsers value)  filterUsers,}){
final _that = this;
switch (_that) {
case LoadUsers():
return loadUsers(_that);case AddUser():
return addUser(_that);case UpdateUser():
return updateUser(_that);case DeleteUser():
return deleteUser(_that);case FilterUsers():
return filterUsers(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( LoadUsers value)?  loadUsers,TResult? Function( AddUser value)?  addUser,TResult? Function( UpdateUser value)?  updateUser,TResult? Function( DeleteUser value)?  deleteUser,TResult? Function( FilterUsers value)?  filterUsers,}){
final _that = this;
switch (_that) {
case LoadUsers() when loadUsers != null:
return loadUsers(_that);case AddUser() when addUser != null:
return addUser(_that);case UpdateUser() when updateUser != null:
return updateUser(_that);case DeleteUser() when deleteUser != null:
return deleteUser(_that);case FilterUsers() when filterUsers != null:
return filterUsers(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  loadUsers,TResult Function( User user)?  addUser,TResult Function( User user)?  updateUser,TResult Function( String userId)?  deleteUser,TResult Function( UserRole? role,  UserStatus? status,  bool? isActive)?  filterUsers,required TResult orElse(),}) {final _that = this;
switch (_that) {
case LoadUsers() when loadUsers != null:
return loadUsers();case AddUser() when addUser != null:
return addUser(_that.user);case UpdateUser() when updateUser != null:
return updateUser(_that.user);case DeleteUser() when deleteUser != null:
return deleteUser(_that.userId);case FilterUsers() when filterUsers != null:
return filterUsers(_that.role,_that.status,_that.isActive);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  loadUsers,required TResult Function( User user)  addUser,required TResult Function( User user)  updateUser,required TResult Function( String userId)  deleteUser,required TResult Function( UserRole? role,  UserStatus? status,  bool? isActive)  filterUsers,}) {final _that = this;
switch (_that) {
case LoadUsers():
return loadUsers();case AddUser():
return addUser(_that.user);case UpdateUser():
return updateUser(_that.user);case DeleteUser():
return deleteUser(_that.userId);case FilterUsers():
return filterUsers(_that.role,_that.status,_that.isActive);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  loadUsers,TResult? Function( User user)?  addUser,TResult? Function( User user)?  updateUser,TResult? Function( String userId)?  deleteUser,TResult? Function( UserRole? role,  UserStatus? status,  bool? isActive)?  filterUsers,}) {final _that = this;
switch (_that) {
case LoadUsers() when loadUsers != null:
return loadUsers();case AddUser() when addUser != null:
return addUser(_that.user);case UpdateUser() when updateUser != null:
return updateUser(_that.user);case DeleteUser() when deleteUser != null:
return deleteUser(_that.userId);case FilterUsers() when filterUsers != null:
return filterUsers(_that.role,_that.status,_that.isActive);case _:
  return null;

}
}

}

/// @nodoc


class LoadUsers implements UserManagementEvent {
  const LoadUsers();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadUsers);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'UserManagementEvent.loadUsers()';
}


}




/// @nodoc


class AddUser implements UserManagementEvent {
  const AddUser(this.user);
  

 final  User user;

/// Create a copy of UserManagementEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AddUserCopyWith<AddUser> get copyWith => _$AddUserCopyWithImpl<AddUser>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AddUser&&(identical(other.user, user) || other.user == user));
}


@override
int get hashCode => Object.hash(runtimeType,user);

@override
String toString() {
  return 'UserManagementEvent.addUser(user: $user)';
}


}

/// @nodoc
abstract mixin class $AddUserCopyWith<$Res> implements $UserManagementEventCopyWith<$Res> {
  factory $AddUserCopyWith(AddUser value, $Res Function(AddUser) _then) = _$AddUserCopyWithImpl;
@useResult
$Res call({
 User user
});


$UserCopyWith<$Res> get user;

}
/// @nodoc
class _$AddUserCopyWithImpl<$Res>
    implements $AddUserCopyWith<$Res> {
  _$AddUserCopyWithImpl(this._self, this._then);

  final AddUser _self;
  final $Res Function(AddUser) _then;

/// Create a copy of UserManagementEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? user = null,}) {
  return _then(AddUser(
null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User,
  ));
}

/// Create a copy of UserManagementEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get user {
  
  return $UserCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}

/// @nodoc


class UpdateUser implements UserManagementEvent {
  const UpdateUser(this.user);
  

 final  User user;

/// Create a copy of UserManagementEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateUserCopyWith<UpdateUser> get copyWith => _$UpdateUserCopyWithImpl<UpdateUser>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateUser&&(identical(other.user, user) || other.user == user));
}


@override
int get hashCode => Object.hash(runtimeType,user);

@override
String toString() {
  return 'UserManagementEvent.updateUser(user: $user)';
}


}

/// @nodoc
abstract mixin class $UpdateUserCopyWith<$Res> implements $UserManagementEventCopyWith<$Res> {
  factory $UpdateUserCopyWith(UpdateUser value, $Res Function(UpdateUser) _then) = _$UpdateUserCopyWithImpl;
@useResult
$Res call({
 User user
});


$UserCopyWith<$Res> get user;

}
/// @nodoc
class _$UpdateUserCopyWithImpl<$Res>
    implements $UpdateUserCopyWith<$Res> {
  _$UpdateUserCopyWithImpl(this._self, this._then);

  final UpdateUser _self;
  final $Res Function(UpdateUser) _then;

/// Create a copy of UserManagementEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? user = null,}) {
  return _then(UpdateUser(
null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User,
  ));
}

/// Create a copy of UserManagementEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get user {
  
  return $UserCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}
}

/// @nodoc


class DeleteUser implements UserManagementEvent {
  const DeleteUser(this.userId);
  

 final  String userId;

/// Create a copy of UserManagementEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DeleteUserCopyWith<DeleteUser> get copyWith => _$DeleteUserCopyWithImpl<DeleteUser>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DeleteUser&&(identical(other.userId, userId) || other.userId == userId));
}


@override
int get hashCode => Object.hash(runtimeType,userId);

@override
String toString() {
  return 'UserManagementEvent.deleteUser(userId: $userId)';
}


}

/// @nodoc
abstract mixin class $DeleteUserCopyWith<$Res> implements $UserManagementEventCopyWith<$Res> {
  factory $DeleteUserCopyWith(DeleteUser value, $Res Function(DeleteUser) _then) = _$DeleteUserCopyWithImpl;
@useResult
$Res call({
 String userId
});




}
/// @nodoc
class _$DeleteUserCopyWithImpl<$Res>
    implements $DeleteUserCopyWith<$Res> {
  _$DeleteUserCopyWithImpl(this._self, this._then);

  final DeleteUser _self;
  final $Res Function(DeleteUser) _then;

/// Create a copy of UserManagementEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? userId = null,}) {
  return _then(DeleteUser(
null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class FilterUsers implements UserManagementEvent {
  const FilterUsers({this.role, this.status, this.isActive});
  

 final  UserRole? role;
 final  UserStatus? status;
 final  bool? isActive;

/// Create a copy of UserManagementEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FilterUsersCopyWith<FilterUsers> get copyWith => _$FilterUsersCopyWithImpl<FilterUsers>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FilterUsers&&(identical(other.role, role) || other.role == role)&&(identical(other.status, status) || other.status == status)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}


@override
int get hashCode => Object.hash(runtimeType,role,status,isActive);

@override
String toString() {
  return 'UserManagementEvent.filterUsers(role: $role, status: $status, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class $FilterUsersCopyWith<$Res> implements $UserManagementEventCopyWith<$Res> {
  factory $FilterUsersCopyWith(FilterUsers value, $Res Function(FilterUsers) _then) = _$FilterUsersCopyWithImpl;
@useResult
$Res call({
 UserRole? role, UserStatus? status, bool? isActive
});




}
/// @nodoc
class _$FilterUsersCopyWithImpl<$Res>
    implements $FilterUsersCopyWith<$Res> {
  _$FilterUsersCopyWithImpl(this._self, this._then);

  final FilterUsers _self;
  final $Res Function(FilterUsers) _then;

/// Create a copy of UserManagementEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? role = freezed,Object? status = freezed,Object? isActive = freezed,}) {
  return _then(FilterUsers(
role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as UserRole?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as UserStatus?,isActive: freezed == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}

// dart format on
