import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/core/service_locator.dart';

class ShoppingListSettings {
  final bool showNotifications;
  final bool showReminders;
  final bool showLowStockAlerts;
  final bool showPriceAlerts;
  final bool showNewItemAlerts;
  final String sortBy;
  final bool sortAscending;
  final bool showGrid;
  final bool showPrices;
  final bool showQuantities;
  final bool showNotes;
  final bool allowSharing;
  final bool allowEditing;
  final bool allowDeleting;

  const ShoppingListSettings({
    this.showNotifications = true,
    this.showReminders = true,
    this.showLowStockAlerts = true,
    this.showPriceAlerts = true,
    this.showNewItemAlerts = true,
    this.sortBy = 'name',
    this.sortAscending = true,
    this.showGrid = false,
    this.showPrices = true,
    this.showQuantities = true,
    this.showNotes = true,
    this.allowSharing = true,
    this.allowEditing = true,
    this.allowDeleting = true,
  });

  ShoppingListSettings copyWith({
    bool? showNotifications,
    bool? showReminders,
    bool? showLowStockAlerts,
    bool? showPriceAlerts,
    bool? showNewItemAlerts,
    String? sortBy,
    bool? sortAscending,
    bool? showGrid,
    bool? showPrices,
    bool? showQuantities,
    bool? showNotes,
    bool? allowSharing,
    bool? allowEditing,
    bool? allowDeleting,
  }) {
    return ShoppingListSettings(
      showNotifications: showNotifications ?? this.showNotifications,
      showReminders: showReminders ?? this.showReminders,
      showLowStockAlerts: showLowStockAlerts ?? this.showLowStockAlerts,
      showPriceAlerts: showPriceAlerts ?? this.showPriceAlerts,
      showNewItemAlerts: showNewItemAlerts ?? this.showNewItemAlerts,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
      showGrid: showGrid ?? this.showGrid,
      showPrices: showPrices ?? this.showPrices,
      showQuantities: showQuantities ?? this.showQuantities,
      showNotes: showNotes ?? this.showNotes,
      allowSharing: allowSharing ?? this.allowSharing,
      allowEditing: allowEditing ?? this.allowEditing,
      allowDeleting: allowDeleting ?? this.allowDeleting,
    );
  }
}

class ShoppingListSettingsNotifier extends StateNotifier<ShoppingListSettings> {
  final SharedPreferences _prefs;
  final String _userId;

  ShoppingListSettingsNotifier(this._prefs, this._userId)
      : super(const ShoppingListSettings()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    state = ShoppingListSettings(
      showNotifications:
          _prefs.getBool('${_userId}_show_notifications') ?? true,
      showReminders: _prefs.getBool('${_userId}_show_reminders') ?? true,
      showLowStockAlerts:
          _prefs.getBool('${_userId}_show_low_stock_alerts') ?? true,
      showPriceAlerts: _prefs.getBool('${_userId}_show_price_alerts') ?? true,
      showNewItemAlerts:
          _prefs.getBool('${_userId}_show_new_item_alerts') ?? true,
      sortBy: _prefs.getString('${_userId}_sort_by') ?? 'name',
      sortAscending: _prefs.getBool('${_userId}_sort_ascending') ?? true,
      showGrid: _prefs.getBool('${_userId}_show_grid') ?? false,
      showPrices: _prefs.getBool('${_userId}_show_prices') ?? true,
      showQuantities: _prefs.getBool('${_userId}_show_quantities') ?? true,
      showNotes: _prefs.getBool('${_userId}_show_notes') ?? true,
      allowSharing: _prefs.getBool('${_userId}_allow_sharing') ?? true,
      allowEditing: _prefs.getBool('${_userId}_allow_editing') ?? true,
      allowDeleting: _prefs.getBool('${_userId}_allow_deleting') ?? true,
    );
  }

  Future<void> _saveSettings() async {
    await Future.wait([
      _prefs.setBool('${_userId}_show_notifications', state.showNotifications),
      _prefs.setBool('${_userId}_show_reminders', state.showReminders),
      _prefs.setBool(
          '${_userId}_show_low_stock_alerts', state.showLowStockAlerts),
      _prefs.setBool('${_userId}_show_price_alerts', state.showPriceAlerts),
      _prefs.setBool(
          '${_userId}_show_new_item_alerts', state.showNewItemAlerts),
      _prefs.setString('${_userId}_sort_by', state.sortBy),
      _prefs.setBool('${_userId}_sort_ascending', state.sortAscending),
      _prefs.setBool('${_userId}_show_grid', state.showGrid),
      _prefs.setBool('${_userId}_show_prices', state.showPrices),
      _prefs.setBool('${_userId}_show_quantities', state.showQuantities),
      _prefs.setBool('${_userId}_show_notes', state.showNotes),
      _prefs.setBool('${_userId}_allow_sharing', state.allowSharing),
      _prefs.setBool('${_userId}_allow_editing', state.allowEditing),
      _prefs.setBool('${_userId}_allow_deleting', state.allowDeleting),
    ]);
  }

  Future<void> updateSettings(ShoppingListSettings newSettings) async {
    state = newSettings;
    await _saveSettings();
  }

  Future<void> updateNotificationSetting(String setting, bool value) async {
    state = state.copyWith(
      showNotifications:
          setting == 'notifications' ? value : state.showNotifications,
      showReminders: setting == 'reminders' ? value : state.showReminders,
      showLowStockAlerts:
          setting == 'low_stock' ? value : state.showLowStockAlerts,
      showPriceAlerts: setting == 'price' ? value : state.showPriceAlerts,
      showNewItemAlerts:
          setting == 'new_item' ? value : state.showNewItemAlerts,
    );
    await _saveSettings();
  }

  Future<void> updateSortSetting(String setting, String value) async {
    state = state.copyWith(
      sortBy: setting == 'sort_by' ? value : state.sortBy,
      sortAscending: setting == 'sort_ascending'
          ? value == 'ascending'
          : state.sortAscending,
    );
    await _saveSettings();
  }

  Future<void> updateDisplaySetting(String setting, bool value) async {
    state = state.copyWith(
      showGrid: setting == 'grid' ? value : state.showGrid,
      showPrices: setting == 'prices' ? value : state.showPrices,
      showQuantities: setting == 'quantities' ? value : state.showQuantities,
      showNotes: setting == 'notes' ? value : state.showNotes,
    );
    await _saveSettings();
  }

  Future<void> updateSharingSetting(String setting, bool value) async {
    state = state.copyWith(
      allowSharing: setting == 'sharing' ? value : state.allowSharing,
      allowEditing: setting == 'editing' ? value : state.allowEditing,
      allowDeleting: setting == 'deleting' ? value : state.allowDeleting,
    );
    await _saveSettings();
  }
}

final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  return serviceLocator<SharedPreferences>();
});

final shoppingListSettingsProvider =
    StateNotifierProvider<ShoppingListSettingsNotifier, ShoppingListSettings>(
  (ref) {
    final prefs = ref.watch(sharedPreferencesProvider);
    final user = ref.watch(currentUserProvider).value;
    if (user == null) throw Exception('User not authenticated');
    return ShoppingListSettingsNotifier(prefs, user.id);
  },
);
