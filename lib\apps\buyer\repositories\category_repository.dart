import '../domain/entities/category/category_model.dart';

/// Repository interface for managing categories
abstract class CategoryRepository {
  /// Get all active categories
  Future<List<CategoryModel>> getActiveCategories();

  /// Get category by ID
  Future<CategoryModel?> getCategoryById(String id);

  /// Get root categories (categories without parent)
  Future<List<CategoryModel>> getRootCategories();

  /// Get child categories of a parent category
  Future<List<CategoryModel>> getChildCategories(String parentId);

  /// Get categories by priority range
  Future<List<CategoryModel>> getCategoriesByPriorityRange({
    required int minPriority,
    required int maxPriority,
  });
}
