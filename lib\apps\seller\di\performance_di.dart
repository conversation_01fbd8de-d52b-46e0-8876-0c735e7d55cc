import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:shivish/apps/seller/data/repositories/analytics_repository_impl.dart';
import 'package:shivish/apps/seller/domain/repositories/analytics_repository.dart';
import 'package:shivish/apps/seller/presentation/cubits/performance/store_performance_cubit.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

class PerformanceDI {
  static void init() {
    final getIt = GetIt.instance;

    // Services
    final databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
    final authService = AuthService();

    // Repository
    final analyticsRepository = AnalyticsRepositoryImpl(
      databaseService,
      authService,
    );

    // Register Repository
    getIt.registerLazySingleton<AnalyticsRepository>(
      () => analyticsRepository,
    );

    // Cubit
    final storePerformanceCubit = StorePerformanceCubit(analyticsRepository);

    // Register Cubit
    BlocProvider<StorePerformanceCubit>.value(value: storePerformanceCubit);
  }
}
