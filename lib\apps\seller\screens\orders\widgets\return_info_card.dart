import 'package:flutter/material.dart';
import 'package:shivish/shared/models/order/order_model.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';
import 'package:shivish/shared/utils/date_formatter.dart';

class ReturnInfoCard extends StatelessWidget {
  final OrderModel order;

  const ReturnInfoCard({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    if (order.status != OrderStatus.refunded) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.assignment_return, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Return Information',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              context,
              'Status',
              'Refunded',
              color: Colors.red,
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              context,
              'Reason',
              order.cancellationReason ?? 'Not specified',
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              context,
              'Refund Amount',
              CurrencyFormatter.format(order.total),
            ),
            if (order.paymentDetails.paymentDate != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow(
                context,
                'Refund Date',
                DateFormatter.formatDate(order.paymentDetails.paymentDate!),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value,
      {Color? color}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style:
                Theme.of(context).textTheme.bodyMedium?.copyWith(color: color),
          ),
        ),
      ],
    );
  }
}
