import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/services/payment/payment_service_provider.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

class SettlementsScreen extends ConsumerStatefulWidget {
  const SettlementsScreen({super.key});

  @override
  ConsumerState<SettlementsScreen> createState() => _SettlementsScreenState();
}

class _SettlementsScreenState extends ConsumerState<SettlementsScreen> {
  bool _isLoading = true;
  String? _error;
  List<Map<String, dynamic>> _settlements = [];
  final _amountController = TextEditingController();
  String? _selectedBankAccountId;

  @override
  void initState() {
    super.initState();
    _loadSettlements();
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _loadSettlements() async {
    try {
      final paymentService = ref.read(paymentServiceProvider);
      final settlements = await paymentService.getSettlements();

      if (mounted) {
        setState(() {
          _settlements = settlements;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _requestSettlement() async {
    if (_selectedBankAccountId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a bank account')),
      );
      return;
    }

    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid amount')),
      );
      return;
    }

    try {
      final paymentService = ref.read(paymentServiceProvider);
      await paymentService.requestSettlement(
        bankAccountId: _selectedBankAccountId!,
        amount: amount,
      );

      _amountController.clear();
      _selectedBankAccountId = null;

      if (mounted) {
        Navigator.pop(context);
        _loadSettlements();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to request settlement: $e')),
        );
      }
    }
  }

  void _showRequestSettlementDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Request Settlement'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'Amount',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            FutureBuilder<List<Map<String, dynamic>>>(
              future: ref.read(paymentServiceProvider).getBankAccounts(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator();
                }

                if (snapshot.hasError) {
                  return Text('Error: ${snapshot.error}');
                }

                final accounts = snapshot.data ?? [];
                if (accounts.isEmpty) {
                  return const Text('No bank accounts found');
                }

                return DropdownButtonFormField<String>(
                  value: _selectedBankAccountId,
                  decoration: const InputDecoration(
                    labelText: 'Bank Account',
                    border: OutlineInputBorder(),
                  ),
                  items: accounts.map((account) {
                    return DropdownMenuItem<String>(
                      value: account['id'] as String,
                      child: Text(account['accountName'] as String),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedBankAccountId = value;
                    });
                  },
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: _requestSettlement,
            child: const Text('Request'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: LoadingIndicator(),
      );
    }

    if (_error != null) {
      return Scaffold(
        body: ErrorMessage(
          message: _error!,
          onRetry: _loadSettlements,
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settlements'),
      ),
      body: RefreshIndicator(
        onRefresh: _loadSettlements,
        child: _settlements.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.payment,
                      size: 64,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No settlements yet',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Request a settlement to receive your earnings',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 24),
                    FilledButton.icon(
                      onPressed: _showRequestSettlementDialog,
                      icon: const Icon(Icons.add),
                      label: const Text('Request Settlement'),
                    ),
                  ],
                ),
              )
            : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _settlements.length,
                itemBuilder: (context, index) {
                  final settlement = _settlements[index];
                  return Card(
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: _getStatusColor(settlement['status'])
                            .withValues(alpha: 0.1),
                        child: Icon(
                          _getStatusIcon(settlement['status']),
                          color: _getStatusColor(settlement['status']),
                        ),
                      ),
                      title: Text(
                        '₹${settlement['amount'].toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(settlement['bankAccountName']),
                          Text(settlement['date']),
                        ],
                      ),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(settlement['status'])
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          settlement['status'].toUpperCase(),
                          style: TextStyle(
                            color: _getStatusColor(settlement['status']),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showRequestSettlementDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.pending;
      case 'processing':
        return Icons.sync;
      case 'completed':
        return Icons.check_circle;
      case 'failed':
        return Icons.error;
      default:
        return Icons.help;
    }
  }
}
