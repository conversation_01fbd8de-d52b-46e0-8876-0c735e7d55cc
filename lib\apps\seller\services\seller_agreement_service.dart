import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../widgets/dialogs/seller_legal_agreement_dialog.dart';

/// Service to handle the seller legal agreement
class SellerAgreementService {
  static const String _agreementKey = 'seller_legal_agreement_accepted';
  static const String _agreementDateKey = 'seller_legal_agreement_date';
  static const int _agreementValidityDays = 90; // Reconfirm every 90 days

  /// Checks if the seller has already accepted the agreement
  static Future<bool> hasAcceptedAgreement() async {
    final prefs = await SharedPreferences.getInstance();
    final isAccepted = prefs.getBool(_agreementKey) ?? false;

    debugPrint('SellerAgreementService: hasAcceptedAgreement() = $isAccepted');

    if (!isAccepted) {
      debugPrint('SellerAgreementService: Agreement not accepted');
      return false;
    }

    // Check if the agreement acceptance has expired
    final agreementDateStr = prefs.getString(_agreementDateKey);
    if (agreementDateStr == null) {
      debugPrint('SellerAgreementService: No agreement date found');
      return false;
    }

    try {
      final agreementDate = DateTime.parse(agreementDateStr);
      final now = DateTime.now();
      final difference = now.difference(agreementDate).inDays;

      // If the agreement is older than the validity period, it's expired
      if (difference > _agreementValidityDays) {
        return false;
      }

      return true;
    } catch (e) {
      // If there's an error parsing the date, assume not accepted
      return false;
    }
  }

  /// Saves the seller's agreement acceptance
  static Future<bool> saveAgreementAcceptance(bool accepted) async {
    debugPrint('SellerAgreementService: saveAgreementAcceptance($accepted)');
    try {
      final prefs = await SharedPreferences.getInstance();
      final boolResult = await prefs.setBool(_agreementKey, accepted);
      debugPrint('SellerAgreementService: setBool result: $boolResult');

      if (accepted) {
        // Save the agreement date
        final now = DateTime.now().toIso8601String();
        final dateResult = await prefs.setString(_agreementDateKey, now);
        debugPrint(
            'SellerAgreementService: Saved agreement date: $now, result: $dateResult');
      }

      // Force a small delay to ensure the data is written
      await Future.delayed(const Duration(milliseconds: 100));

      // Verify the save was successful
      final savedValue = prefs.getBool(_agreementKey);
      debugPrint(
          'SellerAgreementService: Verification - saved value is $savedValue');

      return savedValue == accepted;
    } catch (e) {
      debugPrint('SellerAgreementService: Error saving agreement: $e');
      return false;
    }
  }

  /// Shows the agreement dialog if needed
  /// Returns true if the seller has accepted, false otherwise
  static Future<bool> checkAgreement(BuildContext context) async {
    debugPrint('SellerAgreementService: checkAgreement() called');

    // Check if the seller has already accepted
    final hasAlreadyAccepted = await hasAcceptedAgreement();
    if (hasAlreadyAccepted) {
      debugPrint('SellerAgreementService: Agreement already accepted');
      return true;
    }

    try {
      debugPrint('SellerAgreementService: Showing agreement dialog');
      // Show the agreement dialog directly using a safer approach
      final accepted = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => const SellerLegalAgreementDialog(),
      );
      debugPrint('SellerAgreementService: Dialog result: $accepted');

      // If the seller accepted, save the acceptance
      if (accepted == true) {
        debugPrint('SellerAgreementService: Agreement accepted, saving');

        // Save with retry mechanism
        bool saveSuccess = false;
        for (int i = 0; i < 3; i++) {
          try {
            final saveResult = await saveAgreementAcceptance(true);
            debugPrint(
                'SellerAgreementService: Save attempt ${i + 1} result: $saveResult');

            if (saveResult) {
              saveSuccess = true;
              break;
            }

            // Wait before retrying
            await Future.delayed(const Duration(milliseconds: 300));
          } catch (e) {
            debugPrint(
                'SellerAgreementService: Error in save attempt ${i + 1}: $e');
          }
        }

        if (saveSuccess) {
          debugPrint('SellerAgreementService: Agreement saved successfully');

          // Verify the agreement was saved
          final verifyAccepted = await hasAcceptedAgreement();
          debugPrint(
              'SellerAgreementService: Final verification - hasAcceptedAgreement() = $verifyAccepted');

          return true;
        } else {
          debugPrint(
              'SellerAgreementService: Failed to save agreement after multiple attempts');
          return false;
        }
      } else {
        debugPrint('SellerAgreementService: Agreement rejected');
        return false;
      }
    } catch (e) {
      debugPrint('SellerAgreementService: Error showing dialog: $e');
      return false;
    }
  }
}
