import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/domain/models/promotion_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/marketing_cubit.dart';
import 'package:shivish/apps/seller/screens/marketing/promotion_form.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';
import 'package:shivish/shared/utils/date_formatter.dart';

class PromotionsTab extends StatelessWidget {
  const PromotionsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MarketingCubit, MarketingState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.hasError) {
          return Center(
            child: Text(
              state.errorMessage ?? 'An error occurred',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          );
        }

        if (state.promotions.isEmpty) {
          return const Center(
            child: Text('No promotions found. Create one to get started!'),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: state.promotions.length,
          itemBuilder: (context, index) {
            final promotion = state.promotions[index];
            return _PromotionCard(promotion: promotion);
          },
        );
      },
    );
  }
}

class _PromotionCard extends StatelessWidget {
  final PromotionModel promotion;

  const _PromotionCard({required this.promotion});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    promotion.name,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                Switch(
                  value: promotion.isActive,
                  onChanged: (value) {
                    context
                        .read<MarketingCubit>()
                        .togglePromotionStatus(promotion.id);
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              promotion.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildDiscountInfo(context),
                const Spacer(),
                _buildActions(context),
              ],
            ),
            const SizedBox(height: 8),
            _buildDateRange(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscountInfo(BuildContext context) {
    String discountText;
    switch (promotion.type) {
      case PromotionType.percentage:
        discountText = '${promotion.value}% off';
        break;
      case PromotionType.fixedAmount:
        discountText = CurrencyFormatter.format(promotion.value);
        break;
      case PromotionType.buyOneGetOne:
        discountText = 'Buy One Get One Free';
        break;
      case PromotionType.bundleDiscount:
        discountText = 'Bundle Discount';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        discountText,
        style: TextStyle(
          color: Theme.of(context).primaryColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDateRange(BuildContext context) {
    return Row(
      children: [
        const Icon(Icons.calendar_today, size: 16),
        const SizedBox(width: 8),
        Text(
          '${DateFormatter.formatDate(promotion.startDate)} - ${DateFormatter.formatDate(promotion.endDate)}',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        TextButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => PromotionForm(promotionId: promotion.id),
            );
          },
          child: const Text('Edit'),
        ),
        TextButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Delete Promotion'),
                content: const Text(
                    'Are you sure you want to delete this promotion?'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () {
                      context
                          .read<MarketingCubit>()
                          .deletePromotion(promotion.id);
                      Navigator.of(context).pop();
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).colorScheme.error,
                    ),
                    child: const Text('Delete'),
                  ),
                ],
              ),
            );
          },
          style: TextButton.styleFrom(
            foregroundColor: Theme.of(context).colorScheme.error,
          ),
          child: const Text('Delete'),
        ),
      ],
    );
  }
}
