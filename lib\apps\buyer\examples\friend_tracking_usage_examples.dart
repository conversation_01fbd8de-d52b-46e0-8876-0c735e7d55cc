import 'package:flutter/material.dart';
import '../utils/friend_tracking_navigation.dart';

/// Examples of how to use the friend tracking navigation throughout the app
class FriendTrackingUsageExamples {
  
  /// Example 1: Add friend tracking button to any app bar
  static AppBar buildAppBarWithFriendTracking(BuildContext context, String title) {
    return AppBar(
      title: Text(title),
      actions: [
        // Add friend tracking button to app bar
        FriendTrackingNavigation.buildTrackingButton(context),
        // Other action buttons...
        IconButton(
          icon: const Icon(Icons.notifications),
          onPressed: () {
            // Handle notifications
          },
        ),
      ],
    );
  }

  /// Example 2: Add friend tracking to a settings menu
  static Widget buildSettingsMenuWithFriendTracking(BuildContext context) {
    return Column(
      children: [
        // Other settings items...
        ListTile(
          leading: const Icon(Icons.settings),
          title: const Text('General Settings'),
          onTap: () {
            // Navigate to general settings
          },
        ),
        
        // Friend tracking menu item
        FriendTrackingNavigation.buildTrackingListTile(context),
        
        // More settings items...
        ListTile(
          leading: const Icon(Icons.privacy_tip),
          title: const Text('Privacy Settings'),
          onTap: () {
            // Navigate to privacy settings
          },
        ),
      ],
    );
  }

  /// Example 3: Add floating action button for friend tracking
  static Widget buildScreenWithFriendTrackingFAB(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('My Screen')),
      body: const Center(
        child: Text('Screen content here'),
      ),
      // Add friend tracking floating action button
      floatingActionButton: FriendTrackingNavigation.buildTrackingFAB(context),
    );
  }

  /// Example 4: Navigate programmatically to friend tracking
  static void navigateToFriendTrackingExample(BuildContext context) {
    // Navigate to friend tracking list
    context.showFriendTrackingList();
    
    // Or navigate to specific tracking session
    context.showFriendTracking(
      rideId: 'ride_123',
      friendPhone: '+1234567890',
      userName: 'John Doe',
    );
  }

  /// Example 5: Show friend tracking invitation
  static void showInvitationExample(BuildContext context) {
    // Show as bottom sheet
    context.showTrackingInvitation(
      rideId: 'ride_123',
      userName: 'John Doe',
      userPhone: '+1234567890',
      role: 'start_point_friend',
    );
    
    // Or show as dialog
    FriendTrackingNavigation.showTrackingNotification(
      context,
      rideId: 'ride_123',
      userName: 'John Doe',
      userPhone: '+1234567890',
      role: 'destination_friend',
      onAccept: () {
        print('User accepted tracking invitation');
      },
      onDecline: () {
        print('User declined tracking invitation');
      },
    );
  }

  /// Example 6: Integration with ride booking flow
  static Widget buildRideBookingWithFriendTracking(BuildContext context) {
    return Column(
      children: [
        // Ride booking form...
        const Text('Ride Booking Form'),
        
        // Add friend tracking option
        Card(
          child: ListTile(
            leading: const Icon(Icons.people_outline),
            title: const Text('Enable Friend Tracking'),
            subtitle: const Text('Let friends track your ride for safety'),
            trailing: Switch(
              value: true, // This would be controlled by state
              onChanged: (value) {
                // Handle friend tracking toggle
              },
            ),
          ),
        ),
        
        // Book ride button
        ElevatedButton(
          onPressed: () {
            // After booking ride, show friend tracking setup
            _showFriendTrackingSetup(context);
          },
          child: const Text('Book Ride'),
        ),
      ],
    );
  }

  static void _showFriendTrackingSetup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Set Up Friend Tracking'),
        content: const Text(
          'Would you like to invite friends to track your ride for safety?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Skip'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to friend tracking setup
              context.showFriendTrackingList();
            },
            child: const Text('Set Up'),
          ),
        ],
      ),
    );
  }

  /// Example 7: Integration with safety features
  static Widget buildSafetyMenuWithFriendTracking(BuildContext context) {
    return Column(
      children: [
        // Safety header
        const Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'Safety Features',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ),
        
        // Emergency contacts
        ListTile(
          leading: const Icon(Icons.emergency, color: Colors.red),
          title: const Text('Emergency Contacts'),
          subtitle: const Text('Manage your emergency contacts'),
          onTap: () {
            // Navigate to emergency contacts
          },
        ),
        
        // Friend tracking
        FriendTrackingNavigation.buildTrackingListTile(context),
        
        // SOS button
        ListTile(
          leading: const Icon(Icons.warning, color: Colors.orange),
          title: const Text('SOS Alert'),
          subtitle: const Text('Send emergency alert'),
          onTap: () {
            // Handle SOS alert
          },
        ),
        
        // Safety settings
        ListTile(
          leading: const Icon(Icons.security, color: Colors.blue),
          title: const Text('Safety Settings'),
          subtitle: const Text('Configure safety preferences'),
          onTap: () {
            // Navigate to safety settings
          },
        ),
      ],
    );
  }

  /// Example 8: Quick access methods
  static void quickAccessExamples(BuildContext context) {
    // Quick navigation methods using extension
    context.showFriendTrackingList();
    
    context.showFriendTracking(
      rideId: 'ride_123',
      friendPhone: '+1234567890',
      userName: 'John Doe',
    );
    
    context.showTrackingInvitation(
      rideId: 'ride_123',
      userName: 'John Doe',
      userPhone: '+1234567890',
      role: 'start_point_friend',
    );
  }
}

/// Example widget showing complete integration
class FriendTrackingIntegrationExample extends StatelessWidget {
  const FriendTrackingIntegrationExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Friend Tracking Example'),
        actions: [
          // Friend tracking button in app bar
          FriendTrackingNavigation.buildTrackingButton(context),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          children: [
            const DrawerHeader(
              child: Text('Menu'),
            ),
            // Friend tracking in drawer
            FriendTrackingNavigation.buildTrackingListTile(context),
            // Other menu items...
          ],
        ),
      ),
      body: const Center(
        child: Text('Your app content here'),
      ),
      // Friend tracking FAB
      floatingActionButton: FriendTrackingNavigation.buildTrackingFAB(context),
    );
  }
}
