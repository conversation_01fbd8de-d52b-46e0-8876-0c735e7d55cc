import 'dart:io' show Platform;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/models/device/connected_device_model.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';
import 'package:device_info_plus/device_info_plus.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final connectedDevicesProvider =
    AsyncNotifierProvider<ConnectedDevicesNotifier, List<ConnectedDeviceModel>>(
  ConnectedDevicesNotifier.new,
);

class ConnectedDevicesNotifier
    extends AsyncNotifier<List<ConnectedDeviceModel>> {
  late final DatabaseService _databaseService;
  late final DeviceInfoPlugin _deviceInfo;
  static const String _devicesCollection = 'user_devices';

  @override
  Future<List<ConnectedDeviceModel>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    _deviceInfo = DeviceInfoPlugin();
    return _loadDevices();
  }

  Future<List<ConnectedDeviceModel>> _loadDevices() async {
    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) return [];

      final devices = await _databaseService.getAll(
        _devicesCollection,
        where: 'user_id = ? AND is_deleted = ?',
        whereParams: [userId, false],
        orderBy: 'last_active DESC',
      );

      return devices
          .map((data) => ConnectedDeviceModel.fromJson(data))
          .toList();
    } catch (e) {
      debugPrint('Error loading devices: $e');
      return [];
    }
  }

  Future<void> removeDevice(String deviceId) async {
    state = const AsyncValue.loading();
    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      await _databaseService.update(_devicesCollection, deviceId, {
        'is_deleted': true,
        'deleted_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncValue.data(
        state.value?.where((device) => device.id != deviceId).toList() ?? [],
      );
    } catch (e, st) {
      debugPrint('Error removing device: $e');
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> addNewDevice() async {
    state = const AsyncValue.loading();
    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      final deviceInfo = await _getDeviceInfo();
      final deviceId = DateTime.now().millisecondsSinceEpoch.toString();

      final deviceData = {
        'id': deviceId,
        'user_id': userId,
        'name': deviceInfo['name'] ?? 'Unknown Device',
        'type': deviceInfo['type'] ?? 'Unknown',
        'last_active': DateTime.now().toIso8601String(),
        'device_info': deviceInfo['info'] ?? '',
        'is_current_device': true,
        'is_authorized': true,
        'is_deleted': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _databaseService.create(_devicesCollection, deviceData);

      final newDevice = ConnectedDeviceModel.fromJson(deviceData);
      state = AsyncValue.data([...state.value ?? [], newDevice]);
    } catch (e, st) {
      debugPrint('Error adding new device: $e');
      state = AsyncValue.error(e, st);
    }
  }

  Future<Map<String, String>> _getDeviceInfo() async {
    if (Platform.isAndroid) {
      final androidInfo = await _deviceInfo.androidInfo;
      return {
        'name': androidInfo.model,
        'type': 'Android',
        'info': '${androidInfo.brand} ${androidInfo.model}',
      };
    } else if (Platform.isIOS) {
      final iosInfo = await _deviceInfo.iosInfo;
      return {
        'name': iosInfo.name,
        'type': 'iOS',
        'info': iosInfo.model,
      };
    }
    return {
      'name': 'Unknown Device',
      'type': 'Unknown',
      'info': 'Unknown',
    };
  }

  Future<void> updateDeviceSecurity(String deviceId, bool isAuthorized) async {
    state = const AsyncValue.loading();
    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      final updatedDevices = state.value?.map((device) {
        if (device.id == deviceId) {
          return device.copyWith(isAuthorized: isAuthorized);
        }
        return device;
      }).toList();

      await _databaseService.update(_devicesCollection, deviceId, {
        'is_authorized': isAuthorized,
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncValue.data(updatedDevices ?? []);
    } catch (e, st) {
      debugPrint('Error updating device security: $e');
      state = AsyncValue.error(e, st);
    }
  }
}
