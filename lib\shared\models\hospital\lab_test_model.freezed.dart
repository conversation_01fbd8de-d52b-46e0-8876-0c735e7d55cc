// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lab_test_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LabTestModel {

 String get id; String get testNumber; String get name; String get description; String get hospitalId; String get hospitalName; String get patientId; String get patientName; String get patientPhone; String? get patientEmail; String? get doctorId; String? get doctorName; DateTime get testDate; String? get timeSlot; LabTestStatus get status; LabTestCategory get category; double get price; bool get isPaid; String? get paymentId; String? get reportUrl; String? get notes; String? get sampleType; int? get turnaroundTime; String? get instructions; bool? get isAvailable; DateTime get createdAt; DateTime get updatedAt; bool get isDeleted; DateTime? get deletedAt;
/// Create a copy of LabTestModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LabTestModelCopyWith<LabTestModel> get copyWith => _$LabTestModelCopyWithImpl<LabTestModel>(this as LabTestModel, _$identity);

  /// Serializes this LabTestModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LabTestModel&&(identical(other.id, id) || other.id == id)&&(identical(other.testNumber, testNumber) || other.testNumber == testNumber)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.hospitalId, hospitalId) || other.hospitalId == hospitalId)&&(identical(other.hospitalName, hospitalName) || other.hospitalName == hospitalName)&&(identical(other.patientId, patientId) || other.patientId == patientId)&&(identical(other.patientName, patientName) || other.patientName == patientName)&&(identical(other.patientPhone, patientPhone) || other.patientPhone == patientPhone)&&(identical(other.patientEmail, patientEmail) || other.patientEmail == patientEmail)&&(identical(other.doctorId, doctorId) || other.doctorId == doctorId)&&(identical(other.doctorName, doctorName) || other.doctorName == doctorName)&&(identical(other.testDate, testDate) || other.testDate == testDate)&&(identical(other.timeSlot, timeSlot) || other.timeSlot == timeSlot)&&(identical(other.status, status) || other.status == status)&&(identical(other.category, category) || other.category == category)&&(identical(other.price, price) || other.price == price)&&(identical(other.isPaid, isPaid) || other.isPaid == isPaid)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.reportUrl, reportUrl) || other.reportUrl == reportUrl)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.sampleType, sampleType) || other.sampleType == sampleType)&&(identical(other.turnaroundTime, turnaroundTime) || other.turnaroundTime == turnaroundTime)&&(identical(other.instructions, instructions) || other.instructions == instructions)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,testNumber,name,description,hospitalId,hospitalName,patientId,patientName,patientPhone,patientEmail,doctorId,doctorName,testDate,timeSlot,status,category,price,isPaid,paymentId,reportUrl,notes,sampleType,turnaroundTime,instructions,isAvailable,createdAt,updatedAt,isDeleted,deletedAt]);

@override
String toString() {
  return 'LabTestModel(id: $id, testNumber: $testNumber, name: $name, description: $description, hospitalId: $hospitalId, hospitalName: $hospitalName, patientId: $patientId, patientName: $patientName, patientPhone: $patientPhone, patientEmail: $patientEmail, doctorId: $doctorId, doctorName: $doctorName, testDate: $testDate, timeSlot: $timeSlot, status: $status, category: $category, price: $price, isPaid: $isPaid, paymentId: $paymentId, reportUrl: $reportUrl, notes: $notes, sampleType: $sampleType, turnaroundTime: $turnaroundTime, instructions: $instructions, isAvailable: $isAvailable, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, deletedAt: $deletedAt)';
}


}

/// @nodoc
abstract mixin class $LabTestModelCopyWith<$Res>  {
  factory $LabTestModelCopyWith(LabTestModel value, $Res Function(LabTestModel) _then) = _$LabTestModelCopyWithImpl;
@useResult
$Res call({
 String id, String testNumber, String name, String description, String hospitalId, String hospitalName, String patientId, String patientName, String patientPhone, String? patientEmail, String? doctorId, String? doctorName, DateTime testDate, String? timeSlot, LabTestStatus status, LabTestCategory category, double price, bool isPaid, String? paymentId, String? reportUrl, String? notes, String? sampleType, int? turnaroundTime, String? instructions, bool? isAvailable, DateTime createdAt, DateTime updatedAt, bool isDeleted, DateTime? deletedAt
});




}
/// @nodoc
class _$LabTestModelCopyWithImpl<$Res>
    implements $LabTestModelCopyWith<$Res> {
  _$LabTestModelCopyWithImpl(this._self, this._then);

  final LabTestModel _self;
  final $Res Function(LabTestModel) _then;

/// Create a copy of LabTestModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? testNumber = null,Object? name = null,Object? description = null,Object? hospitalId = null,Object? hospitalName = null,Object? patientId = null,Object? patientName = null,Object? patientPhone = null,Object? patientEmail = freezed,Object? doctorId = freezed,Object? doctorName = freezed,Object? testDate = null,Object? timeSlot = freezed,Object? status = null,Object? category = null,Object? price = null,Object? isPaid = null,Object? paymentId = freezed,Object? reportUrl = freezed,Object? notes = freezed,Object? sampleType = freezed,Object? turnaroundTime = freezed,Object? instructions = freezed,Object? isAvailable = freezed,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,Object? deletedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,testNumber: null == testNumber ? _self.testNumber : testNumber // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,hospitalId: null == hospitalId ? _self.hospitalId : hospitalId // ignore: cast_nullable_to_non_nullable
as String,hospitalName: null == hospitalName ? _self.hospitalName : hospitalName // ignore: cast_nullable_to_non_nullable
as String,patientId: null == patientId ? _self.patientId : patientId // ignore: cast_nullable_to_non_nullable
as String,patientName: null == patientName ? _self.patientName : patientName // ignore: cast_nullable_to_non_nullable
as String,patientPhone: null == patientPhone ? _self.patientPhone : patientPhone // ignore: cast_nullable_to_non_nullable
as String,patientEmail: freezed == patientEmail ? _self.patientEmail : patientEmail // ignore: cast_nullable_to_non_nullable
as String?,doctorId: freezed == doctorId ? _self.doctorId : doctorId // ignore: cast_nullable_to_non_nullable
as String?,doctorName: freezed == doctorName ? _self.doctorName : doctorName // ignore: cast_nullable_to_non_nullable
as String?,testDate: null == testDate ? _self.testDate : testDate // ignore: cast_nullable_to_non_nullable
as DateTime,timeSlot: freezed == timeSlot ? _self.timeSlot : timeSlot // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as LabTestStatus,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as LabTestCategory,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,isPaid: null == isPaid ? _self.isPaid : isPaid // ignore: cast_nullable_to_non_nullable
as bool,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,reportUrl: freezed == reportUrl ? _self.reportUrl : reportUrl // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,sampleType: freezed == sampleType ? _self.sampleType : sampleType // ignore: cast_nullable_to_non_nullable
as String?,turnaroundTime: freezed == turnaroundTime ? _self.turnaroundTime : turnaroundTime // ignore: cast_nullable_to_non_nullable
as int?,instructions: freezed == instructions ? _self.instructions : instructions // ignore: cast_nullable_to_non_nullable
as String?,isAvailable: freezed == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [LabTestModel].
extension LabTestModelPatterns on LabTestModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _LabTestModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LabTestModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _LabTestModel value)  $default,){
final _that = this;
switch (_that) {
case _LabTestModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _LabTestModel value)?  $default,){
final _that = this;
switch (_that) {
case _LabTestModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String testNumber,  String name,  String description,  String hospitalId,  String hospitalName,  String patientId,  String patientName,  String patientPhone,  String? patientEmail,  String? doctorId,  String? doctorName,  DateTime testDate,  String? timeSlot,  LabTestStatus status,  LabTestCategory category,  double price,  bool isPaid,  String? paymentId,  String? reportUrl,  String? notes,  String? sampleType,  int? turnaroundTime,  String? instructions,  bool? isAvailable,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  DateTime? deletedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LabTestModel() when $default != null:
return $default(_that.id,_that.testNumber,_that.name,_that.description,_that.hospitalId,_that.hospitalName,_that.patientId,_that.patientName,_that.patientPhone,_that.patientEmail,_that.doctorId,_that.doctorName,_that.testDate,_that.timeSlot,_that.status,_that.category,_that.price,_that.isPaid,_that.paymentId,_that.reportUrl,_that.notes,_that.sampleType,_that.turnaroundTime,_that.instructions,_that.isAvailable,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.deletedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String testNumber,  String name,  String description,  String hospitalId,  String hospitalName,  String patientId,  String patientName,  String patientPhone,  String? patientEmail,  String? doctorId,  String? doctorName,  DateTime testDate,  String? timeSlot,  LabTestStatus status,  LabTestCategory category,  double price,  bool isPaid,  String? paymentId,  String? reportUrl,  String? notes,  String? sampleType,  int? turnaroundTime,  String? instructions,  bool? isAvailable,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  DateTime? deletedAt)  $default,) {final _that = this;
switch (_that) {
case _LabTestModel():
return $default(_that.id,_that.testNumber,_that.name,_that.description,_that.hospitalId,_that.hospitalName,_that.patientId,_that.patientName,_that.patientPhone,_that.patientEmail,_that.doctorId,_that.doctorName,_that.testDate,_that.timeSlot,_that.status,_that.category,_that.price,_that.isPaid,_that.paymentId,_that.reportUrl,_that.notes,_that.sampleType,_that.turnaroundTime,_that.instructions,_that.isAvailable,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.deletedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String testNumber,  String name,  String description,  String hospitalId,  String hospitalName,  String patientId,  String patientName,  String patientPhone,  String? patientEmail,  String? doctorId,  String? doctorName,  DateTime testDate,  String? timeSlot,  LabTestStatus status,  LabTestCategory category,  double price,  bool isPaid,  String? paymentId,  String? reportUrl,  String? notes,  String? sampleType,  int? turnaroundTime,  String? instructions,  bool? isAvailable,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted,  DateTime? deletedAt)?  $default,) {final _that = this;
switch (_that) {
case _LabTestModel() when $default != null:
return $default(_that.id,_that.testNumber,_that.name,_that.description,_that.hospitalId,_that.hospitalName,_that.patientId,_that.patientName,_that.patientPhone,_that.patientEmail,_that.doctorId,_that.doctorName,_that.testDate,_that.timeSlot,_that.status,_that.category,_that.price,_that.isPaid,_that.paymentId,_that.reportUrl,_that.notes,_that.sampleType,_that.turnaroundTime,_that.instructions,_that.isAvailable,_that.createdAt,_that.updatedAt,_that.isDeleted,_that.deletedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _LabTestModel implements LabTestModel {
  const _LabTestModel({required this.id, required this.testNumber, required this.name, required this.description, required this.hospitalId, required this.hospitalName, required this.patientId, required this.patientName, required this.patientPhone, this.patientEmail, this.doctorId, this.doctorName, required this.testDate, this.timeSlot, required this.status, required this.category, required this.price, required this.isPaid, this.paymentId, this.reportUrl, this.notes, this.sampleType, this.turnaroundTime, this.instructions, this.isAvailable, required this.createdAt, required this.updatedAt, this.isDeleted = false, this.deletedAt});
  factory _LabTestModel.fromJson(Map<String, dynamic> json) => _$LabTestModelFromJson(json);

@override final  String id;
@override final  String testNumber;
@override final  String name;
@override final  String description;
@override final  String hospitalId;
@override final  String hospitalName;
@override final  String patientId;
@override final  String patientName;
@override final  String patientPhone;
@override final  String? patientEmail;
@override final  String? doctorId;
@override final  String? doctorName;
@override final  DateTime testDate;
@override final  String? timeSlot;
@override final  LabTestStatus status;
@override final  LabTestCategory category;
@override final  double price;
@override final  bool isPaid;
@override final  String? paymentId;
@override final  String? reportUrl;
@override final  String? notes;
@override final  String? sampleType;
@override final  int? turnaroundTime;
@override final  String? instructions;
@override final  bool? isAvailable;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool isDeleted;
@override final  DateTime? deletedAt;

/// Create a copy of LabTestModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LabTestModelCopyWith<_LabTestModel> get copyWith => __$LabTestModelCopyWithImpl<_LabTestModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LabTestModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LabTestModel&&(identical(other.id, id) || other.id == id)&&(identical(other.testNumber, testNumber) || other.testNumber == testNumber)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.hospitalId, hospitalId) || other.hospitalId == hospitalId)&&(identical(other.hospitalName, hospitalName) || other.hospitalName == hospitalName)&&(identical(other.patientId, patientId) || other.patientId == patientId)&&(identical(other.patientName, patientName) || other.patientName == patientName)&&(identical(other.patientPhone, patientPhone) || other.patientPhone == patientPhone)&&(identical(other.patientEmail, patientEmail) || other.patientEmail == patientEmail)&&(identical(other.doctorId, doctorId) || other.doctorId == doctorId)&&(identical(other.doctorName, doctorName) || other.doctorName == doctorName)&&(identical(other.testDate, testDate) || other.testDate == testDate)&&(identical(other.timeSlot, timeSlot) || other.timeSlot == timeSlot)&&(identical(other.status, status) || other.status == status)&&(identical(other.category, category) || other.category == category)&&(identical(other.price, price) || other.price == price)&&(identical(other.isPaid, isPaid) || other.isPaid == isPaid)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.reportUrl, reportUrl) || other.reportUrl == reportUrl)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.sampleType, sampleType) || other.sampleType == sampleType)&&(identical(other.turnaroundTime, turnaroundTime) || other.turnaroundTime == turnaroundTime)&&(identical(other.instructions, instructions) || other.instructions == instructions)&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,testNumber,name,description,hospitalId,hospitalName,patientId,patientName,patientPhone,patientEmail,doctorId,doctorName,testDate,timeSlot,status,category,price,isPaid,paymentId,reportUrl,notes,sampleType,turnaroundTime,instructions,isAvailable,createdAt,updatedAt,isDeleted,deletedAt]);

@override
String toString() {
  return 'LabTestModel(id: $id, testNumber: $testNumber, name: $name, description: $description, hospitalId: $hospitalId, hospitalName: $hospitalName, patientId: $patientId, patientName: $patientName, patientPhone: $patientPhone, patientEmail: $patientEmail, doctorId: $doctorId, doctorName: $doctorName, testDate: $testDate, timeSlot: $timeSlot, status: $status, category: $category, price: $price, isPaid: $isPaid, paymentId: $paymentId, reportUrl: $reportUrl, notes: $notes, sampleType: $sampleType, turnaroundTime: $turnaroundTime, instructions: $instructions, isAvailable: $isAvailable, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted, deletedAt: $deletedAt)';
}


}

/// @nodoc
abstract mixin class _$LabTestModelCopyWith<$Res> implements $LabTestModelCopyWith<$Res> {
  factory _$LabTestModelCopyWith(_LabTestModel value, $Res Function(_LabTestModel) _then) = __$LabTestModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String testNumber, String name, String description, String hospitalId, String hospitalName, String patientId, String patientName, String patientPhone, String? patientEmail, String? doctorId, String? doctorName, DateTime testDate, String? timeSlot, LabTestStatus status, LabTestCategory category, double price, bool isPaid, String? paymentId, String? reportUrl, String? notes, String? sampleType, int? turnaroundTime, String? instructions, bool? isAvailable, DateTime createdAt, DateTime updatedAt, bool isDeleted, DateTime? deletedAt
});




}
/// @nodoc
class __$LabTestModelCopyWithImpl<$Res>
    implements _$LabTestModelCopyWith<$Res> {
  __$LabTestModelCopyWithImpl(this._self, this._then);

  final _LabTestModel _self;
  final $Res Function(_LabTestModel) _then;

/// Create a copy of LabTestModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? testNumber = null,Object? name = null,Object? description = null,Object? hospitalId = null,Object? hospitalName = null,Object? patientId = null,Object? patientName = null,Object? patientPhone = null,Object? patientEmail = freezed,Object? doctorId = freezed,Object? doctorName = freezed,Object? testDate = null,Object? timeSlot = freezed,Object? status = null,Object? category = null,Object? price = null,Object? isPaid = null,Object? paymentId = freezed,Object? reportUrl = freezed,Object? notes = freezed,Object? sampleType = freezed,Object? turnaroundTime = freezed,Object? instructions = freezed,Object? isAvailable = freezed,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,Object? deletedAt = freezed,}) {
  return _then(_LabTestModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,testNumber: null == testNumber ? _self.testNumber : testNumber // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,hospitalId: null == hospitalId ? _self.hospitalId : hospitalId // ignore: cast_nullable_to_non_nullable
as String,hospitalName: null == hospitalName ? _self.hospitalName : hospitalName // ignore: cast_nullable_to_non_nullable
as String,patientId: null == patientId ? _self.patientId : patientId // ignore: cast_nullable_to_non_nullable
as String,patientName: null == patientName ? _self.patientName : patientName // ignore: cast_nullable_to_non_nullable
as String,patientPhone: null == patientPhone ? _self.patientPhone : patientPhone // ignore: cast_nullable_to_non_nullable
as String,patientEmail: freezed == patientEmail ? _self.patientEmail : patientEmail // ignore: cast_nullable_to_non_nullable
as String?,doctorId: freezed == doctorId ? _self.doctorId : doctorId // ignore: cast_nullable_to_non_nullable
as String?,doctorName: freezed == doctorName ? _self.doctorName : doctorName // ignore: cast_nullable_to_non_nullable
as String?,testDate: null == testDate ? _self.testDate : testDate // ignore: cast_nullable_to_non_nullable
as DateTime,timeSlot: freezed == timeSlot ? _self.timeSlot : timeSlot // ignore: cast_nullable_to_non_nullable
as String?,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as LabTestStatus,category: null == category ? _self.category : category // ignore: cast_nullable_to_non_nullable
as LabTestCategory,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,isPaid: null == isPaid ? _self.isPaid : isPaid // ignore: cast_nullable_to_non_nullable
as bool,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,reportUrl: freezed == reportUrl ? _self.reportUrl : reportUrl // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,sampleType: freezed == sampleType ? _self.sampleType : sampleType // ignore: cast_nullable_to_non_nullable
as String?,turnaroundTime: freezed == turnaroundTime ? _self.turnaroundTime : turnaroundTime // ignore: cast_nullable_to_non_nullable
as int?,instructions: freezed == instructions ? _self.instructions : instructions // ignore: cast_nullable_to_non_nullable
as String?,isAvailable: freezed == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
