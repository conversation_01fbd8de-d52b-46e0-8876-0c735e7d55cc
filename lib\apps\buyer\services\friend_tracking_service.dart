import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';

/// Service for managing friend tracking during rides
class FriendTrackingService extends ChangeNotifier {
  final DatabaseService _databaseService;

  // Collections
  static const String _friendTrackingCollection = 'friend_tracking';
  static const String _usersCollection = 'users';

  // Active tracking sessions
  final Map<String, FriendTrackingSession> _activeSessions = {};

  FriendTrackingService()
    : _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());

  // Getters
  Map<String, FriendTrackingSession> get activeSessions =>
      Map.unmodifiable(_activeSessions);
  bool get hasActiveSessions => _activeSessions.isNotEmpty;

  /// Setup friend tracking for a ride
  Future<bool> setupFriendTracking({
    required String rideId,
    required String userId,
    String? startFriendPhone,
    String? endFriendPhone,
  }) async {
    try {
      if (startFriendPhone == null && endFriendPhone == null) {
        return false; // No friends to track
      }

      final session = FriendTrackingSession(
        rideId: rideId,
        userId: userId,
        startFriendPhone: startFriendPhone,
        endFriendPhone: endFriendPhone,
        createdAt: DateTime.now(),
        isActive: true,
      );

      // Save to database
      await _databaseService.create(_friendTrackingCollection, {
        'id': rideId,
        ...session.toJson(),
      });

      // Check if friends have the app and send invitations
      await _sendTrackingInvitations(session);

      _activeSessions[rideId] = session;
      notifyListeners();

      return true;
    } catch (e) {
      print('Error setting up friend tracking: $e');
      return false;
    }
  }

  /// Send tracking invitations to friends
  Future<void> _sendTrackingInvitations(FriendTrackingSession session) async {
    try {
      final invitations = <Map<String, dynamic>>[];

      if (session.startFriendPhone != null) {
        final hasApp = await _checkIfUserHasApp(session.startFriendPhone!);
        invitations.add({
          'phone': session.startFriendPhone,
          'role': 'start_point_friend',
          'hasApp': hasApp,
          'invitationSent': true,
          'timestamp': DateTime.now().toIso8601String(),
        });
      }

      if (session.endFriendPhone != null) {
        final hasApp = await _checkIfUserHasApp(session.endFriendPhone!);
        invitations.add({
          'phone': session.endFriendPhone,
          'role': 'destination_friend',
          'hasApp': hasApp,
          'invitationSent': true,
          'timestamp': DateTime.now().toIso8601String(),
        });
      }

      // Store invitations
      for (final invitation in invitations) {
        await _databaseService.create('friend_tracking_invitations', {
          'id':
              '${session.rideId}_${invitation['phone']}_${DateTime.now().millisecondsSinceEpoch}',
          'rideId': session.rideId,
          'userId': session.userId,
          ...invitation,
        });
      }
    } catch (e) {
      print('Error sending tracking invitations: $e');
    }
  }

  /// Check if a phone number has the app installed
  Future<bool> _checkIfUserHasApp(String phone) async {
    try {
      final users = await _databaseService.getAll(
        _usersCollection,
        where: 'phone = ?',
        whereParams: [phone],
      );
      return users.isNotEmpty;
    } catch (e) {
      print('Error checking if user has app: $e');
      return false;
    }
  }

  /// Update live location for tracking
  Future<void> updateLiveLocation({
    required String rideId,
    required Position location,
  }) async {
    try {
      final session = _activeSessions[rideId];
      if (session == null || !session.isActive) return;

      await _databaseService.update(_friendTrackingCollection, rideId, {
        'lastLocation': {
          'latitude': location.latitude,
          'longitude': location.longitude,
          'accuracy': location.accuracy,
          'timestamp': location.timestamp.toIso8601String(),
        },
        'lastUpdated': DateTime.now().toIso8601String(),
      });

      // Notify friends with app about location update
      await _notifyFriendsLocationUpdate(session, location);
    } catch (e) {
      print('Error updating live location: $e');
    }
  }

  /// Notify friends about location updates
  Future<void> _notifyFriendsLocationUpdate(
    FriendTrackingSession session,
    Position location,
  ) async {
    try {
      // This would typically send push notifications or real-time updates
      // to friends who have the app installed
      print(
        'Notifying friends about location update for ride: ${session.rideId}',
      );
    } catch (e) {
      print('Error notifying friends: $e');
    }
  }

  /// Stop friend tracking for a ride
  Future<void> stopFriendTracking(String rideId) async {
    try {
      final session = _activeSessions[rideId];
      if (session == null) return;

      // Update session as inactive
      await _databaseService.update(_friendTrackingCollection, rideId, {
        'isActive': false,
        'endedAt': DateTime.now().toIso8601String(),
      });

      _activeSessions.remove(rideId);
      notifyListeners();
    } catch (e) {
      print('Error stopping friend tracking: $e');
    }
  }

  /// Get friends tracking a specific ride
  Future<List<Map<String, dynamic>>> getFriendsTrackingRide(
    String rideId,
  ) async {
    try {
      return await _databaseService.getAll(
        'friend_tracking_invitations',
        where: 'rideId = ? AND hasApp = ?',
        whereParams: [rideId, true],
      );
    } catch (e) {
      print('Error getting friends tracking ride: $e');
      return [];
    }
  }

  /// Get user's tracking invitations (rides where user is invited to track)
  Future<List<Map<String, dynamic>>> getUserTrackingInvitations(
    String userId,
  ) async {
    try {
      // Get all invitations where the user's phone matches and they have the app
      final invitations = await _databaseService.getAll(
        'friend_tracking_invitations',
        where: 'hasApp = ?',
        whereParams: [true],
      );

      // Filter invitations for the current user by checking if their phone matches
      // First, get the user's phone number from the users collection
      final users = await _databaseService.getAll(
        _usersCollection,
        where: 'id = ?',
        whereParams: [userId],
      );

      if (users.isEmpty) {
        print('User not found in database');
        return [];
      }

      final userPhone = users.first['phone'] as String?;
      if (userPhone == null || userPhone.isEmpty) {
        print('User phone not found');
        return [];
      }

      // Filter invitations for this user's phone
      final userInvitations = invitations.where((invitation) {
        return invitation['phone'] == userPhone;
      }).toList();

      // Transform the data to match the expected format for the UI
      final transformedInvitations = <Map<String, dynamic>>[];

      for (final invitation in userInvitations) {
        // Get the ride details and user who sent the invitation
        final rideId = invitation['rideId'] as String;
        final senderId = invitation['userId'] as String;

        // Get sender's details
        final senderUsers = await _databaseService.getAll(
          _usersCollection,
          where: 'id = ?',
          whereParams: [senderId],
        );

        if (senderUsers.isNotEmpty) {
          final sender = senderUsers.first;

          // Get the tracking session to check if it's still active
          final sessions = await _databaseService.getAll(
            _friendTrackingCollection,
            where: 'id = ?',
            whereParams: [rideId],
          );

          final isActive = sessions.isNotEmpty
              ? (sessions.first['isActive'] ?? false)
              : false;

          transformedInvitations.add({
            'rideId': rideId,
            'userName': sender['name'] ?? sender['email'] ?? 'Unknown User',
            'userPhone': sender['phone'] ?? '',
            'role': invitation['role'] ?? 'friend',
            'createdAt':
                DateTime.tryParse(invitation['timestamp'] ?? '') ??
                DateTime.now(),
            'isActive': isActive,
          });
        }
      }

      // Sort by creation time, most recent first
      transformedInvitations.sort((a, b) {
        final aTime = a['createdAt'] as DateTime;
        final bTime = b['createdAt'] as DateTime;
        return bTime.compareTo(aTime);
      });

      return transformedInvitations;
    } catch (e) {
      print('Error getting user tracking invitations: $e');
      return [];
    }
  }

  @override
  void dispose() {
    _activeSessions.clear();
    super.dispose();
  }
}

/// Friend tracking session model
class FriendTrackingSession {
  final String rideId;
  final String userId;
  final String? startFriendPhone;
  final String? endFriendPhone;
  final DateTime createdAt;
  final bool isActive;

  const FriendTrackingSession({
    required this.rideId,
    required this.userId,
    this.startFriendPhone,
    this.endFriendPhone,
    required this.createdAt,
    required this.isActive,
  });

  Map<String, dynamic> toJson() => {
    'rideId': rideId,
    'userId': userId,
    'startFriendPhone': startFriendPhone,
    'endFriendPhone': endFriendPhone,
    'createdAt': createdAt.toIso8601String(),
    'isActive': isActive,
  };

  factory FriendTrackingSession.fromJson(Map<String, dynamic> json) {
    return FriendTrackingSession(
      rideId: json['rideId'],
      userId: json['userId'],
      startFriendPhone: json['startFriendPhone'],
      endFriendPhone: json['endFriendPhone'],
      createdAt: DateTime.parse(json['createdAt']),
      isActive: json['isActive'] ?? false,
    );
  }
}
