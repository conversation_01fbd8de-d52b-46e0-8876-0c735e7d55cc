﻿package com.shivish.utils

import android.content.Context
import android.util.Log
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability

/**
 * Utility class for Google Services operations
 */
object GoogleServicesUtils {
    private const val TAG = "GoogleServicesUtils"

    /**
     * Check if Google Play Services is available
     */
    fun isGooglePlayServicesAvailable(context: Context): Boolean {
        return try {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)
            resultCode == ConnectionResult.SUCCESS
        } catch (e: Exception) {
            Log.e(TAG, "Error checking Google Play Services availability: ${e.message}")
            false
        }
    }

    /**
     * Get Google Play Services availability status
     */
    fun getGooglePlayServicesStatus(context: Context): Int {
        return try {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            googleApiAvailability.isGooglePlayServicesAvailable(context)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting Google Play Services status: ${e.message}")
            ConnectionResult.SERVICE_MISSING
        }
    }

    /**
     * Check if Google Play Services is updatable
     */
    fun isGooglePlayServicesUpdatable(context: Context): Boolean {
        return try {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)
            googleApiAvailability.isUserResolvableError(resultCode)
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if Google Play Services is updatable: ${e.message}")
            false
        }
    }

    /**
     * Get error string for Google Play Services status
     */
    fun getErrorString(context: Context, errorCode: Int): String {
        return try {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            googleApiAvailability.getErrorString(errorCode)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting error string: ${e.message}")
            "Unknown error"
        }
    }
}
