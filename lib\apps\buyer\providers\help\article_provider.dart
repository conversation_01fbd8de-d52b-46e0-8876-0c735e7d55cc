import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/help/article_model.dart';
import '../../../../shared/services/help/article_service.dart';

final articleServiceProvider = Provider<ArticleService>((ref) {
  return ArticleService();
});

final articleCategoriesProvider = FutureProvider<List<ArticleCategoryModel>>((
  ref,
) {
  return ref.watch(articleServiceProvider).getCategories();
});

final articleCategoryProvider =
    FutureProvider.family<ArticleCategoryModel?, String>((ref, categoryId) {
      return ref.watch(articleServiceProvider).getCategory(categoryId);
    });

final articleProvider = FutureProvider.family<ArticleModel?, String>((
  ref,
  articleId,
) {
  return ref.watch(articleServiceProvider).getArticle(articleId);
});

final bookmarkedArticlesProvider = FutureProvider<List<ArticleModel>>((ref) {
  return ref.watch(articleServiceProvider).getBookmarkedArticles();
});

final articleStateProvider =
    StateNotifierProvider<ArticleStateNotifier, AsyncValue<void>>((ref) {
      return ArticleStateNotifier(ref.watch(articleServiceProvider));
    });

class ArticleStateNotifier extends StateNotifier<AsyncValue<void>> {
  final ArticleService _service;

  ArticleStateNotifier(this._service) : super(const AsyncValue.data(null));

  Future<void> bookmarkArticle(String articleId) async {
    try {
      state = const AsyncValue.loading();
      await _service.bookmarkArticle(articleId);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> unbookmarkArticle(String articleId) async {
    try {
      state = const AsyncValue.loading();
      await _service.unbookmarkArticle(articleId);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> shareArticle(String articleId) async {
    try {
      state = const AsyncValue.loading();
      await _service.shareArticle(articleId);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
