import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/apps/technician/providers/pricing_settings_provider.dart';

class PricingSettingsScreen extends ConsumerStatefulWidget {
  const PricingSettingsScreen({super.key});

  @override
  ConsumerState<PricingSettingsScreen> createState() =>
      _PricingSettingsScreenState();
}

class _PricingSettingsScreenState extends ConsumerState<PricingSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _basePriceController = TextEditingController();
  final _hourlyRateController = TextEditingController();
  final _minimumPriceController = TextEditingController();
  final _maximumPriceController = TextEditingController();
  final _travelFeeController = TextEditingController();
  final _emergencyFeeController = TextEditingController();
  bool _includeTravelFee = true;
  bool _includeEmergencyFee = true;
  bool _showPriceRange = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadPricingSettings();
  }

  @override
  void dispose() {
    _basePriceController.dispose();
    _hourlyRateController.dispose();
    _minimumPriceController.dispose();
    _maximumPriceController.dispose();
    _travelFeeController.dispose();
    _emergencyFeeController.dispose();
    super.dispose();
  }

  Future<void> _loadPricingSettings() async {
    final settings = await ref.read(pricingSettingsProvider.future);
    if (mounted) {
      setState(() {
        _basePriceController.text = settings['basePrice'].toString();
        _hourlyRateController.text = settings['hourlyRate'].toString();
        _minimumPriceController.text = settings['minimumPrice'].toString();
        _maximumPriceController.text = settings['maximumPrice'].toString();
        _travelFeeController.text = settings['travelFee'].toString();
        _emergencyFeeController.text = settings['emergencyFee'].toString();
        _includeTravelFee = settings['includeTravelFee'];
        _includeEmergencyFee = settings['includeEmergencyFee'];
        _showPriceRange = settings['showPriceRange'];
      });
    }
  }

  Future<void> _savePricingSettings() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      final settings = {
        'basePrice': double.parse(_basePriceController.text),
        'hourlyRate': double.parse(_hourlyRateController.text),
        'minimumPrice': double.parse(_minimumPriceController.text),
        'maximumPrice': double.parse(_maximumPriceController.text),
        'includeTravelFee': _includeTravelFee,
        'travelFee': double.parse(_travelFeeController.text),
        'includeEmergencyFee': _includeEmergencyFee,
        'emergencyFee': double.parse(_emergencyFeeController.text),
        'showPriceRange': _showPriceRange,
      };

      await ref
          .read(pricingSettingsProvider.notifier)
          .savePricingSettings(settings);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Pricing settings updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update pricing settings: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(pricingSettingsProvider, (previous, next) {
      next.whenOrNull(
        error: (error, _) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error.toString()),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
        },
      );
    });

    final pricingState = ref.watch(pricingSettingsProvider);
    _isLoading = pricingState is AsyncLoading;

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Pricing Settings',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Base Pricing',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      AppTextField(
                        controller: _basePriceController,
                        label: 'Base Price',
                        keyboardType: TextInputType.number,
                        prefixIcon: const Icon(Icons.attach_money),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter base price';
                          }
                          if (double.tryParse(value) == null) {
                            return 'Please enter a valid price';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      AppTextField(
                        controller: _hourlyRateController,
                        label: 'Hourly Rate',
                        keyboardType: TextInputType.number,
                        prefixIcon: const Icon(Icons.access_time),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter hourly rate';
                          }
                          if (double.tryParse(value) == null) {
                            return 'Please enter a valid rate';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Price Range',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Switch(
                            value: _showPriceRange,
                            onChanged: (value) {
                              setState(() {
                                _showPriceRange = value;
                              });
                            },
                          ),
                        ],
                      ),
                      if (_showPriceRange) ...[
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: AppTextField(
                                controller: _minimumPriceController,
                                label: 'Minimum Price',
                                keyboardType: TextInputType.number,
                                prefixIcon: const Icon(Icons.attach_money),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter minimum price';
                                  }
                                  if (double.tryParse(value) == null) {
                                    return 'Please enter a valid price';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: AppTextField(
                                controller: _maximumPriceController,
                                label: 'Maximum Price',
                                keyboardType: TextInputType.number,
                                prefixIcon: const Icon(Icons.attach_money),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter maximum price';
                                  }
                                  if (double.tryParse(value) == null) {
                                    return 'Please enter a valid price';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Additional Fees',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Travel Fee'),
                          Switch(
                            value: _includeTravelFee,
                            onChanged: (value) {
                              setState(() {
                                _includeTravelFee = value;
                              });
                            },
                          ),
                        ],
                      ),
                      if (_includeTravelFee) ...[
                        const SizedBox(height: 16),
                        AppTextField(
                          controller: _travelFeeController,
                          label: 'Travel Fee Amount',
                          keyboardType: TextInputType.number,
                          prefixIcon: const Icon(Icons.directions_car),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter travel fee';
                            }
                            if (double.tryParse(value) == null) {
                              return 'Please enter a valid amount';
                            }
                            return null;
                          },
                        ),
                      ],
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Emergency Fee'),
                          Switch(
                            value: _includeEmergencyFee,
                            onChanged: (value) {
                              setState(() {
                                _includeEmergencyFee = value;
                              });
                            },
                          ),
                        ],
                      ),
                      if (_includeEmergencyFee) ...[
                        const SizedBox(height: 16),
                        AppTextField(
                          controller: _emergencyFeeController,
                          label: 'Emergency Fee Amount',
                          keyboardType: TextInputType.number,
                          prefixIcon: const Icon(Icons.warning),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter emergency fee';
                            }
                            if (double.tryParse(value) == null) {
                              return 'Please enter a valid amount';
                            }
                            return null;
                          },
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              AppButton(
                onPressed: _isLoading ? null : _savePricingSettings,
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Text('Save Changes'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
