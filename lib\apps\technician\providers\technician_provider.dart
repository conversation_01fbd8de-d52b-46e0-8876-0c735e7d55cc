import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/models/technician/technician.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final technicianProvider =
    AsyncNotifierProvider<TechnicianNotifier, Technician?>(() {
  return TechnicianNotifier();
});

class TechnicianNotifier extends AsyncNotifier<Technician?> {
  @override
  Future<Technician?> build() async {
    try {
      final authService = ref.read(authServiceProvider);
      final userId = authService.currentUser?.id;
      if (userId == null) return null;

      final databaseService = ref.read(databaseServiceProvider);
      final data = await databaseService.find('technicians', userId);

      if (data == null) return null;

      return Technician.fromJson(data);
    } catch (e) {
      debugPrint('Error loading technician: $e');
      return null;
    }
  }

  Future<void> updateTechnician(Technician technician) async {
    state = const AsyncValue.loading();
    try {
      final databaseService = ref.read(databaseServiceProvider);
      final technicianData = technician.toJson();
      technicianData['updated_at'] = DateTime.now().toIso8601String();

      await databaseService.update(
        'technicians',
        technician.id,
        technicianData,
      );

      state = AsyncValue.data(technician);
    } catch (e, st) {
      debugPrint('Error updating technician: $e');
      state = AsyncValue.error(e, st);
    }
  }


}
