// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OrderItem implements DiagnosticableTreeMixin {

 String get id; String get productId; String get name; double get price; int get quantity; String? get imageUrl; String? get description; String? get productName; String? get productImage;
/// Create a copy of OrderItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderItemCopyWith<OrderItem> get copyWith => _$OrderItemCopyWithImpl<OrderItem>(this as OrderItem, _$identity);

  /// Serializes this OrderItem to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderItem'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('productId', productId))..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('price', price))..add(DiagnosticsProperty('quantity', quantity))..add(DiagnosticsProperty('imageUrl', imageUrl))..add(DiagnosticsProperty('description', description))..add(DiagnosticsProperty('productName', productName))..add(DiagnosticsProperty('productImage', productImage));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderItem&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.name, name) || other.name == name)&&(identical(other.price, price) || other.price == price)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.description, description) || other.description == description)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productImage, productImage) || other.productImage == productImage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,name,price,quantity,imageUrl,description,productName,productImage);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderItem(id: $id, productId: $productId, name: $name, price: $price, quantity: $quantity, imageUrl: $imageUrl, description: $description, productName: $productName, productImage: $productImage)';
}


}

/// @nodoc
abstract mixin class $OrderItemCopyWith<$Res>  {
  factory $OrderItemCopyWith(OrderItem value, $Res Function(OrderItem) _then) = _$OrderItemCopyWithImpl;
@useResult
$Res call({
 String id, String productId, String name, double price, int quantity, String? imageUrl, String? description, String? productName, String? productImage
});




}
/// @nodoc
class _$OrderItemCopyWithImpl<$Res>
    implements $OrderItemCopyWith<$Res> {
  _$OrderItemCopyWithImpl(this._self, this._then);

  final OrderItem _self;
  final $Res Function(OrderItem) _then;

/// Create a copy of OrderItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? productId = null,Object? name = null,Object? price = null,Object? quantity = null,Object? imageUrl = freezed,Object? description = freezed,Object? productName = freezed,Object? productImage = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,productName: freezed == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String?,productImage: freezed == productImage ? _self.productImage : productImage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [OrderItem].
extension OrderItemPatterns on OrderItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OrderItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OrderItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OrderItem value)  $default,){
final _that = this;
switch (_that) {
case _OrderItem():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OrderItem value)?  $default,){
final _that = this;
switch (_that) {
case _OrderItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String productId,  String name,  double price,  int quantity,  String? imageUrl,  String? description,  String? productName,  String? productImage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OrderItem() when $default != null:
return $default(_that.id,_that.productId,_that.name,_that.price,_that.quantity,_that.imageUrl,_that.description,_that.productName,_that.productImage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String productId,  String name,  double price,  int quantity,  String? imageUrl,  String? description,  String? productName,  String? productImage)  $default,) {final _that = this;
switch (_that) {
case _OrderItem():
return $default(_that.id,_that.productId,_that.name,_that.price,_that.quantity,_that.imageUrl,_that.description,_that.productName,_that.productImage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String productId,  String name,  double price,  int quantity,  String? imageUrl,  String? description,  String? productName,  String? productImage)?  $default,) {final _that = this;
switch (_that) {
case _OrderItem() when $default != null:
return $default(_that.id,_that.productId,_that.name,_that.price,_that.quantity,_that.imageUrl,_that.description,_that.productName,_that.productImage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _OrderItem with DiagnosticableTreeMixin implements OrderItem {
  const _OrderItem({required this.id, required this.productId, required this.name, required this.price, required this.quantity, this.imageUrl, this.description, this.productName, this.productImage});
  factory _OrderItem.fromJson(Map<String, dynamic> json) => _$OrderItemFromJson(json);

@override final  String id;
@override final  String productId;
@override final  String name;
@override final  double price;
@override final  int quantity;
@override final  String? imageUrl;
@override final  String? description;
@override final  String? productName;
@override final  String? productImage;

/// Create a copy of OrderItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderItemCopyWith<_OrderItem> get copyWith => __$OrderItemCopyWithImpl<_OrderItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OrderItemToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderItem'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('productId', productId))..add(DiagnosticsProperty('name', name))..add(DiagnosticsProperty('price', price))..add(DiagnosticsProperty('quantity', quantity))..add(DiagnosticsProperty('imageUrl', imageUrl))..add(DiagnosticsProperty('description', description))..add(DiagnosticsProperty('productName', productName))..add(DiagnosticsProperty('productImage', productImage));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderItem&&(identical(other.id, id) || other.id == id)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.name, name) || other.name == name)&&(identical(other.price, price) || other.price == price)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.description, description) || other.description == description)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.productImage, productImage) || other.productImage == productImage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,productId,name,price,quantity,imageUrl,description,productName,productImage);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderItem(id: $id, productId: $productId, name: $name, price: $price, quantity: $quantity, imageUrl: $imageUrl, description: $description, productName: $productName, productImage: $productImage)';
}


}

/// @nodoc
abstract mixin class _$OrderItemCopyWith<$Res> implements $OrderItemCopyWith<$Res> {
  factory _$OrderItemCopyWith(_OrderItem value, $Res Function(_OrderItem) _then) = __$OrderItemCopyWithImpl;
@override @useResult
$Res call({
 String id, String productId, String name, double price, int quantity, String? imageUrl, String? description, String? productName, String? productImage
});




}
/// @nodoc
class __$OrderItemCopyWithImpl<$Res>
    implements _$OrderItemCopyWith<$Res> {
  __$OrderItemCopyWithImpl(this._self, this._then);

  final _OrderItem _self;
  final $Res Function(_OrderItem) _then;

/// Create a copy of OrderItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? productId = null,Object? name = null,Object? price = null,Object? quantity = null,Object? imageUrl = freezed,Object? description = freezed,Object? productName = freezed,Object? productImage = freezed,}) {
  return _then(_OrderItem(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,productName: freezed == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String?,productImage: freezed == productImage ? _self.productImage : productImage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$OrderAddress implements DiagnosticableTreeMixin {

 String get street; String get city; String get state; String get country; String get postalCode; String? get landmark; String get contactName; String get contactPhone; String? get contactEmail; bool get isDefault; double? get latitude; double? get longitude;
/// Create a copy of OrderAddress
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderAddressCopyWith<OrderAddress> get copyWith => _$OrderAddressCopyWithImpl<OrderAddress>(this as OrderAddress, _$identity);

  /// Serializes this OrderAddress to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderAddress'))
    ..add(DiagnosticsProperty('street', street))..add(DiagnosticsProperty('city', city))..add(DiagnosticsProperty('state', state))..add(DiagnosticsProperty('country', country))..add(DiagnosticsProperty('postalCode', postalCode))..add(DiagnosticsProperty('landmark', landmark))..add(DiagnosticsProperty('contactName', contactName))..add(DiagnosticsProperty('contactPhone', contactPhone))..add(DiagnosticsProperty('contactEmail', contactEmail))..add(DiagnosticsProperty('isDefault', isDefault))..add(DiagnosticsProperty('latitude', latitude))..add(DiagnosticsProperty('longitude', longitude));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderAddress&&(identical(other.street, street) || other.street == street)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.postalCode, postalCode) || other.postalCode == postalCode)&&(identical(other.landmark, landmark) || other.landmark == landmark)&&(identical(other.contactName, contactName) || other.contactName == contactName)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,street,city,state,country,postalCode,landmark,contactName,contactPhone,contactEmail,isDefault,latitude,longitude);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderAddress(street: $street, city: $city, state: $state, country: $country, postalCode: $postalCode, landmark: $landmark, contactName: $contactName, contactPhone: $contactPhone, contactEmail: $contactEmail, isDefault: $isDefault, latitude: $latitude, longitude: $longitude)';
}


}

/// @nodoc
abstract mixin class $OrderAddressCopyWith<$Res>  {
  factory $OrderAddressCopyWith(OrderAddress value, $Res Function(OrderAddress) _then) = _$OrderAddressCopyWithImpl;
@useResult
$Res call({
 String street, String city, String state, String country, String postalCode, String? landmark, String contactName, String contactPhone, String? contactEmail, bool isDefault, double? latitude, double? longitude
});




}
/// @nodoc
class _$OrderAddressCopyWithImpl<$Res>
    implements $OrderAddressCopyWith<$Res> {
  _$OrderAddressCopyWithImpl(this._self, this._then);

  final OrderAddress _self;
  final $Res Function(OrderAddress) _then;

/// Create a copy of OrderAddress
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? street = null,Object? city = null,Object? state = null,Object? country = null,Object? postalCode = null,Object? landmark = freezed,Object? contactName = null,Object? contactPhone = null,Object? contactEmail = freezed,Object? isDefault = null,Object? latitude = freezed,Object? longitude = freezed,}) {
  return _then(_self.copyWith(
street: null == street ? _self.street : street // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,postalCode: null == postalCode ? _self.postalCode : postalCode // ignore: cast_nullable_to_non_nullable
as String,landmark: freezed == landmark ? _self.landmark : landmark // ignore: cast_nullable_to_non_nullable
as String?,contactName: null == contactName ? _self.contactName : contactName // ignore: cast_nullable_to_non_nullable
as String,contactPhone: null == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,isDefault: null == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}

}


/// Adds pattern-matching-related methods to [OrderAddress].
extension OrderAddressPatterns on OrderAddress {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OrderAddress value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OrderAddress() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OrderAddress value)  $default,){
final _that = this;
switch (_that) {
case _OrderAddress():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OrderAddress value)?  $default,){
final _that = this;
switch (_that) {
case _OrderAddress() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String street,  String city,  String state,  String country,  String postalCode,  String? landmark,  String contactName,  String contactPhone,  String? contactEmail,  bool isDefault,  double? latitude,  double? longitude)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OrderAddress() when $default != null:
return $default(_that.street,_that.city,_that.state,_that.country,_that.postalCode,_that.landmark,_that.contactName,_that.contactPhone,_that.contactEmail,_that.isDefault,_that.latitude,_that.longitude);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String street,  String city,  String state,  String country,  String postalCode,  String? landmark,  String contactName,  String contactPhone,  String? contactEmail,  bool isDefault,  double? latitude,  double? longitude)  $default,) {final _that = this;
switch (_that) {
case _OrderAddress():
return $default(_that.street,_that.city,_that.state,_that.country,_that.postalCode,_that.landmark,_that.contactName,_that.contactPhone,_that.contactEmail,_that.isDefault,_that.latitude,_that.longitude);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String street,  String city,  String state,  String country,  String postalCode,  String? landmark,  String contactName,  String contactPhone,  String? contactEmail,  bool isDefault,  double? latitude,  double? longitude)?  $default,) {final _that = this;
switch (_that) {
case _OrderAddress() when $default != null:
return $default(_that.street,_that.city,_that.state,_that.country,_that.postalCode,_that.landmark,_that.contactName,_that.contactPhone,_that.contactEmail,_that.isDefault,_that.latitude,_that.longitude);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _OrderAddress with DiagnosticableTreeMixin implements OrderAddress {
  const _OrderAddress({required this.street, required this.city, required this.state, required this.country, required this.postalCode, this.landmark, required this.contactName, required this.contactPhone, this.contactEmail, this.isDefault = false, this.latitude, this.longitude});
  factory _OrderAddress.fromJson(Map<String, dynamic> json) => _$OrderAddressFromJson(json);

@override final  String street;
@override final  String city;
@override final  String state;
@override final  String country;
@override final  String postalCode;
@override final  String? landmark;
@override final  String contactName;
@override final  String contactPhone;
@override final  String? contactEmail;
@override@JsonKey() final  bool isDefault;
@override final  double? latitude;
@override final  double? longitude;

/// Create a copy of OrderAddress
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderAddressCopyWith<_OrderAddress> get copyWith => __$OrderAddressCopyWithImpl<_OrderAddress>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OrderAddressToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderAddress'))
    ..add(DiagnosticsProperty('street', street))..add(DiagnosticsProperty('city', city))..add(DiagnosticsProperty('state', state))..add(DiagnosticsProperty('country', country))..add(DiagnosticsProperty('postalCode', postalCode))..add(DiagnosticsProperty('landmark', landmark))..add(DiagnosticsProperty('contactName', contactName))..add(DiagnosticsProperty('contactPhone', contactPhone))..add(DiagnosticsProperty('contactEmail', contactEmail))..add(DiagnosticsProperty('isDefault', isDefault))..add(DiagnosticsProperty('latitude', latitude))..add(DiagnosticsProperty('longitude', longitude));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderAddress&&(identical(other.street, street) || other.street == street)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.country, country) || other.country == country)&&(identical(other.postalCode, postalCode) || other.postalCode == postalCode)&&(identical(other.landmark, landmark) || other.landmark == landmark)&&(identical(other.contactName, contactName) || other.contactName == contactName)&&(identical(other.contactPhone, contactPhone) || other.contactPhone == contactPhone)&&(identical(other.contactEmail, contactEmail) || other.contactEmail == contactEmail)&&(identical(other.isDefault, isDefault) || other.isDefault == isDefault)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,street,city,state,country,postalCode,landmark,contactName,contactPhone,contactEmail,isDefault,latitude,longitude);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderAddress(street: $street, city: $city, state: $state, country: $country, postalCode: $postalCode, landmark: $landmark, contactName: $contactName, contactPhone: $contactPhone, contactEmail: $contactEmail, isDefault: $isDefault, latitude: $latitude, longitude: $longitude)';
}


}

/// @nodoc
abstract mixin class _$OrderAddressCopyWith<$Res> implements $OrderAddressCopyWith<$Res> {
  factory _$OrderAddressCopyWith(_OrderAddress value, $Res Function(_OrderAddress) _then) = __$OrderAddressCopyWithImpl;
@override @useResult
$Res call({
 String street, String city, String state, String country, String postalCode, String? landmark, String contactName, String contactPhone, String? contactEmail, bool isDefault, double? latitude, double? longitude
});




}
/// @nodoc
class __$OrderAddressCopyWithImpl<$Res>
    implements _$OrderAddressCopyWith<$Res> {
  __$OrderAddressCopyWithImpl(this._self, this._then);

  final _OrderAddress _self;
  final $Res Function(_OrderAddress) _then;

/// Create a copy of OrderAddress
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? street = null,Object? city = null,Object? state = null,Object? country = null,Object? postalCode = null,Object? landmark = freezed,Object? contactName = null,Object? contactPhone = null,Object? contactEmail = freezed,Object? isDefault = null,Object? latitude = freezed,Object? longitude = freezed,}) {
  return _then(_OrderAddress(
street: null == street ? _self.street : street // ignore: cast_nullable_to_non_nullable
as String,city: null == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String,state: null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,postalCode: null == postalCode ? _self.postalCode : postalCode // ignore: cast_nullable_to_non_nullable
as String,landmark: freezed == landmark ? _self.landmark : landmark // ignore: cast_nullable_to_non_nullable
as String?,contactName: null == contactName ? _self.contactName : contactName // ignore: cast_nullable_to_non_nullable
as String,contactPhone: null == contactPhone ? _self.contactPhone : contactPhone // ignore: cast_nullable_to_non_nullable
as String,contactEmail: freezed == contactEmail ? _self.contactEmail : contactEmail // ignore: cast_nullable_to_non_nullable
as String?,isDefault: null == isDefault ? _self.isDefault : isDefault // ignore: cast_nullable_to_non_nullable
as bool,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}


}


/// @nodoc
mixin _$PaymentDetails implements DiagnosticableTreeMixin {

 String get method; PaymentStatus get status; double get amount; String? get transactionId; DateTime? get paidAt; String? get cardLast4; String? get cardBrand; String? get paymentId; DateTime? get paymentDate;
/// Create a copy of PaymentDetails
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentDetailsCopyWith<PaymentDetails> get copyWith => _$PaymentDetailsCopyWithImpl<PaymentDetails>(this as PaymentDetails, _$identity);

  /// Serializes this PaymentDetails to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'PaymentDetails'))
    ..add(DiagnosticsProperty('method', method))..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('amount', amount))..add(DiagnosticsProperty('transactionId', transactionId))..add(DiagnosticsProperty('paidAt', paidAt))..add(DiagnosticsProperty('cardLast4', cardLast4))..add(DiagnosticsProperty('cardBrand', cardBrand))..add(DiagnosticsProperty('paymentId', paymentId))..add(DiagnosticsProperty('paymentDate', paymentDate));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentDetails&&(identical(other.method, method) || other.method == method)&&(identical(other.status, status) || other.status == status)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&(identical(other.cardLast4, cardLast4) || other.cardLast4 == cardLast4)&&(identical(other.cardBrand, cardBrand) || other.cardBrand == cardBrand)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.paymentDate, paymentDate) || other.paymentDate == paymentDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,method,status,amount,transactionId,paidAt,cardLast4,cardBrand,paymentId,paymentDate);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'PaymentDetails(method: $method, status: $status, amount: $amount, transactionId: $transactionId, paidAt: $paidAt, cardLast4: $cardLast4, cardBrand: $cardBrand, paymentId: $paymentId, paymentDate: $paymentDate)';
}


}

/// @nodoc
abstract mixin class $PaymentDetailsCopyWith<$Res>  {
  factory $PaymentDetailsCopyWith(PaymentDetails value, $Res Function(PaymentDetails) _then) = _$PaymentDetailsCopyWithImpl;
@useResult
$Res call({
 String method, PaymentStatus status, double amount, String? transactionId, DateTime? paidAt, String? cardLast4, String? cardBrand, String? paymentId, DateTime? paymentDate
});




}
/// @nodoc
class _$PaymentDetailsCopyWithImpl<$Res>
    implements $PaymentDetailsCopyWith<$Res> {
  _$PaymentDetailsCopyWithImpl(this._self, this._then);

  final PaymentDetails _self;
  final $Res Function(PaymentDetails) _then;

/// Create a copy of PaymentDetails
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? method = null,Object? status = null,Object? amount = null,Object? transactionId = freezed,Object? paidAt = freezed,Object? cardLast4 = freezed,Object? cardBrand = freezed,Object? paymentId = freezed,Object? paymentDate = freezed,}) {
  return _then(_self.copyWith(
method: null == method ? _self.method : method // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PaymentStatus,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,cardLast4: freezed == cardLast4 ? _self.cardLast4 : cardLast4 // ignore: cast_nullable_to_non_nullable
as String?,cardBrand: freezed == cardBrand ? _self.cardBrand : cardBrand // ignore: cast_nullable_to_non_nullable
as String?,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,paymentDate: freezed == paymentDate ? _self.paymentDate : paymentDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [PaymentDetails].
extension PaymentDetailsPatterns on PaymentDetails {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PaymentDetails value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PaymentDetails() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PaymentDetails value)  $default,){
final _that = this;
switch (_that) {
case _PaymentDetails():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PaymentDetails value)?  $default,){
final _that = this;
switch (_that) {
case _PaymentDetails() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String method,  PaymentStatus status,  double amount,  String? transactionId,  DateTime? paidAt,  String? cardLast4,  String? cardBrand,  String? paymentId,  DateTime? paymentDate)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PaymentDetails() when $default != null:
return $default(_that.method,_that.status,_that.amount,_that.transactionId,_that.paidAt,_that.cardLast4,_that.cardBrand,_that.paymentId,_that.paymentDate);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String method,  PaymentStatus status,  double amount,  String? transactionId,  DateTime? paidAt,  String? cardLast4,  String? cardBrand,  String? paymentId,  DateTime? paymentDate)  $default,) {final _that = this;
switch (_that) {
case _PaymentDetails():
return $default(_that.method,_that.status,_that.amount,_that.transactionId,_that.paidAt,_that.cardLast4,_that.cardBrand,_that.paymentId,_that.paymentDate);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String method,  PaymentStatus status,  double amount,  String? transactionId,  DateTime? paidAt,  String? cardLast4,  String? cardBrand,  String? paymentId,  DateTime? paymentDate)?  $default,) {final _that = this;
switch (_that) {
case _PaymentDetails() when $default != null:
return $default(_that.method,_that.status,_that.amount,_that.transactionId,_that.paidAt,_that.cardLast4,_that.cardBrand,_that.paymentId,_that.paymentDate);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PaymentDetails with DiagnosticableTreeMixin implements PaymentDetails {
  const _PaymentDetails({required this.method, required this.status, required this.amount, this.transactionId, this.paidAt, this.cardLast4, this.cardBrand, this.paymentId, this.paymentDate});
  factory _PaymentDetails.fromJson(Map<String, dynamic> json) => _$PaymentDetailsFromJson(json);

@override final  String method;
@override final  PaymentStatus status;
@override final  double amount;
@override final  String? transactionId;
@override final  DateTime? paidAt;
@override final  String? cardLast4;
@override final  String? cardBrand;
@override final  String? paymentId;
@override final  DateTime? paymentDate;

/// Create a copy of PaymentDetails
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentDetailsCopyWith<_PaymentDetails> get copyWith => __$PaymentDetailsCopyWithImpl<_PaymentDetails>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentDetailsToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'PaymentDetails'))
    ..add(DiagnosticsProperty('method', method))..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('amount', amount))..add(DiagnosticsProperty('transactionId', transactionId))..add(DiagnosticsProperty('paidAt', paidAt))..add(DiagnosticsProperty('cardLast4', cardLast4))..add(DiagnosticsProperty('cardBrand', cardBrand))..add(DiagnosticsProperty('paymentId', paymentId))..add(DiagnosticsProperty('paymentDate', paymentDate));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentDetails&&(identical(other.method, method) || other.method == method)&&(identical(other.status, status) || other.status == status)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.paidAt, paidAt) || other.paidAt == paidAt)&&(identical(other.cardLast4, cardLast4) || other.cardLast4 == cardLast4)&&(identical(other.cardBrand, cardBrand) || other.cardBrand == cardBrand)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.paymentDate, paymentDate) || other.paymentDate == paymentDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,method,status,amount,transactionId,paidAt,cardLast4,cardBrand,paymentId,paymentDate);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'PaymentDetails(method: $method, status: $status, amount: $amount, transactionId: $transactionId, paidAt: $paidAt, cardLast4: $cardLast4, cardBrand: $cardBrand, paymentId: $paymentId, paymentDate: $paymentDate)';
}


}

/// @nodoc
abstract mixin class _$PaymentDetailsCopyWith<$Res> implements $PaymentDetailsCopyWith<$Res> {
  factory _$PaymentDetailsCopyWith(_PaymentDetails value, $Res Function(_PaymentDetails) _then) = __$PaymentDetailsCopyWithImpl;
@override @useResult
$Res call({
 String method, PaymentStatus status, double amount, String? transactionId, DateTime? paidAt, String? cardLast4, String? cardBrand, String? paymentId, DateTime? paymentDate
});




}
/// @nodoc
class __$PaymentDetailsCopyWithImpl<$Res>
    implements _$PaymentDetailsCopyWith<$Res> {
  __$PaymentDetailsCopyWithImpl(this._self, this._then);

  final _PaymentDetails _self;
  final $Res Function(_PaymentDetails) _then;

/// Create a copy of PaymentDetails
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? method = null,Object? status = null,Object? amount = null,Object? transactionId = freezed,Object? paidAt = freezed,Object? cardLast4 = freezed,Object? cardBrand = freezed,Object? paymentId = freezed,Object? paymentDate = freezed,}) {
  return _then(_PaymentDetails(
method: null == method ? _self.method : method // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PaymentStatus,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,paidAt: freezed == paidAt ? _self.paidAt : paidAt // ignore: cast_nullable_to_non_nullable
as DateTime?,cardLast4: freezed == cardLast4 ? _self.cardLast4 : cardLast4 // ignore: cast_nullable_to_non_nullable
as String?,cardBrand: freezed == cardBrand ? _self.cardBrand : cardBrand // ignore: cast_nullable_to_non_nullable
as String?,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,paymentDate: freezed == paymentDate ? _self.paymentDate : paymentDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$ShippingDetails implements DiagnosticableTreeMixin {

 String get trackingNumber; String get provider; DeliveryProviderType get providerType; DateTime? get estimatedDeliveryDate; String? get notes; String? get deliveryPersonId; double? get deliveryCharge; String? get deliveryRequestId; Map<String, dynamic> get metadata;
/// Create a copy of ShippingDetails
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ShippingDetailsCopyWith<ShippingDetails> get copyWith => _$ShippingDetailsCopyWithImpl<ShippingDetails>(this as ShippingDetails, _$identity);

  /// Serializes this ShippingDetails to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ShippingDetails'))
    ..add(DiagnosticsProperty('trackingNumber', trackingNumber))..add(DiagnosticsProperty('provider', provider))..add(DiagnosticsProperty('providerType', providerType))..add(DiagnosticsProperty('estimatedDeliveryDate', estimatedDeliveryDate))..add(DiagnosticsProperty('notes', notes))..add(DiagnosticsProperty('deliveryPersonId', deliveryPersonId))..add(DiagnosticsProperty('deliveryCharge', deliveryCharge))..add(DiagnosticsProperty('deliveryRequestId', deliveryRequestId))..add(DiagnosticsProperty('metadata', metadata));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ShippingDetails&&(identical(other.trackingNumber, trackingNumber) || other.trackingNumber == trackingNumber)&&(identical(other.provider, provider) || other.provider == provider)&&(identical(other.providerType, providerType) || other.providerType == providerType)&&(identical(other.estimatedDeliveryDate, estimatedDeliveryDate) || other.estimatedDeliveryDate == estimatedDeliveryDate)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.deliveryPersonId, deliveryPersonId) || other.deliveryPersonId == deliveryPersonId)&&(identical(other.deliveryCharge, deliveryCharge) || other.deliveryCharge == deliveryCharge)&&(identical(other.deliveryRequestId, deliveryRequestId) || other.deliveryRequestId == deliveryRequestId)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,trackingNumber,provider,providerType,estimatedDeliveryDate,notes,deliveryPersonId,deliveryCharge,deliveryRequestId,const DeepCollectionEquality().hash(metadata));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ShippingDetails(trackingNumber: $trackingNumber, provider: $provider, providerType: $providerType, estimatedDeliveryDate: $estimatedDeliveryDate, notes: $notes, deliveryPersonId: $deliveryPersonId, deliveryCharge: $deliveryCharge, deliveryRequestId: $deliveryRequestId, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $ShippingDetailsCopyWith<$Res>  {
  factory $ShippingDetailsCopyWith(ShippingDetails value, $Res Function(ShippingDetails) _then) = _$ShippingDetailsCopyWithImpl;
@useResult
$Res call({
 String trackingNumber, String provider, DeliveryProviderType providerType, DateTime? estimatedDeliveryDate, String? notes, String? deliveryPersonId, double? deliveryCharge, String? deliveryRequestId, Map<String, dynamic> metadata
});




}
/// @nodoc
class _$ShippingDetailsCopyWithImpl<$Res>
    implements $ShippingDetailsCopyWith<$Res> {
  _$ShippingDetailsCopyWithImpl(this._self, this._then);

  final ShippingDetails _self;
  final $Res Function(ShippingDetails) _then;

/// Create a copy of ShippingDetails
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? trackingNumber = null,Object? provider = null,Object? providerType = null,Object? estimatedDeliveryDate = freezed,Object? notes = freezed,Object? deliveryPersonId = freezed,Object? deliveryCharge = freezed,Object? deliveryRequestId = freezed,Object? metadata = null,}) {
  return _then(_self.copyWith(
trackingNumber: null == trackingNumber ? _self.trackingNumber : trackingNumber // ignore: cast_nullable_to_non_nullable
as String,provider: null == provider ? _self.provider : provider // ignore: cast_nullable_to_non_nullable
as String,providerType: null == providerType ? _self.providerType : providerType // ignore: cast_nullable_to_non_nullable
as DeliveryProviderType,estimatedDeliveryDate: freezed == estimatedDeliveryDate ? _self.estimatedDeliveryDate : estimatedDeliveryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,deliveryPersonId: freezed == deliveryPersonId ? _self.deliveryPersonId : deliveryPersonId // ignore: cast_nullable_to_non_nullable
as String?,deliveryCharge: freezed == deliveryCharge ? _self.deliveryCharge : deliveryCharge // ignore: cast_nullable_to_non_nullable
as double?,deliveryRequestId: freezed == deliveryRequestId ? _self.deliveryRequestId : deliveryRequestId // ignore: cast_nullable_to_non_nullable
as String?,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [ShippingDetails].
extension ShippingDetailsPatterns on ShippingDetails {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ShippingDetails value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ShippingDetails() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ShippingDetails value)  $default,){
final _that = this;
switch (_that) {
case _ShippingDetails():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ShippingDetails value)?  $default,){
final _that = this;
switch (_that) {
case _ShippingDetails() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String trackingNumber,  String provider,  DeliveryProviderType providerType,  DateTime? estimatedDeliveryDate,  String? notes,  String? deliveryPersonId,  double? deliveryCharge,  String? deliveryRequestId,  Map<String, dynamic> metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ShippingDetails() when $default != null:
return $default(_that.trackingNumber,_that.provider,_that.providerType,_that.estimatedDeliveryDate,_that.notes,_that.deliveryPersonId,_that.deliveryCharge,_that.deliveryRequestId,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String trackingNumber,  String provider,  DeliveryProviderType providerType,  DateTime? estimatedDeliveryDate,  String? notes,  String? deliveryPersonId,  double? deliveryCharge,  String? deliveryRequestId,  Map<String, dynamic> metadata)  $default,) {final _that = this;
switch (_that) {
case _ShippingDetails():
return $default(_that.trackingNumber,_that.provider,_that.providerType,_that.estimatedDeliveryDate,_that.notes,_that.deliveryPersonId,_that.deliveryCharge,_that.deliveryRequestId,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String trackingNumber,  String provider,  DeliveryProviderType providerType,  DateTime? estimatedDeliveryDate,  String? notes,  String? deliveryPersonId,  double? deliveryCharge,  String? deliveryRequestId,  Map<String, dynamic> metadata)?  $default,) {final _that = this;
switch (_that) {
case _ShippingDetails() when $default != null:
return $default(_that.trackingNumber,_that.provider,_that.providerType,_that.estimatedDeliveryDate,_that.notes,_that.deliveryPersonId,_that.deliveryCharge,_that.deliveryRequestId,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ShippingDetails with DiagnosticableTreeMixin implements ShippingDetails {
  const _ShippingDetails({required this.trackingNumber, required this.provider, required this.providerType, this.estimatedDeliveryDate, this.notes, this.deliveryPersonId, this.deliveryCharge, this.deliveryRequestId, final  Map<String, dynamic> metadata = const {}}): _metadata = metadata;
  factory _ShippingDetails.fromJson(Map<String, dynamic> json) => _$ShippingDetailsFromJson(json);

@override final  String trackingNumber;
@override final  String provider;
@override final  DeliveryProviderType providerType;
@override final  DateTime? estimatedDeliveryDate;
@override final  String? notes;
@override final  String? deliveryPersonId;
@override final  double? deliveryCharge;
@override final  String? deliveryRequestId;
 final  Map<String, dynamic> _metadata;
@override@JsonKey() Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}


/// Create a copy of ShippingDetails
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ShippingDetailsCopyWith<_ShippingDetails> get copyWith => __$ShippingDetailsCopyWithImpl<_ShippingDetails>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ShippingDetailsToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ShippingDetails'))
    ..add(DiagnosticsProperty('trackingNumber', trackingNumber))..add(DiagnosticsProperty('provider', provider))..add(DiagnosticsProperty('providerType', providerType))..add(DiagnosticsProperty('estimatedDeliveryDate', estimatedDeliveryDate))..add(DiagnosticsProperty('notes', notes))..add(DiagnosticsProperty('deliveryPersonId', deliveryPersonId))..add(DiagnosticsProperty('deliveryCharge', deliveryCharge))..add(DiagnosticsProperty('deliveryRequestId', deliveryRequestId))..add(DiagnosticsProperty('metadata', metadata));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ShippingDetails&&(identical(other.trackingNumber, trackingNumber) || other.trackingNumber == trackingNumber)&&(identical(other.provider, provider) || other.provider == provider)&&(identical(other.providerType, providerType) || other.providerType == providerType)&&(identical(other.estimatedDeliveryDate, estimatedDeliveryDate) || other.estimatedDeliveryDate == estimatedDeliveryDate)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.deliveryPersonId, deliveryPersonId) || other.deliveryPersonId == deliveryPersonId)&&(identical(other.deliveryCharge, deliveryCharge) || other.deliveryCharge == deliveryCharge)&&(identical(other.deliveryRequestId, deliveryRequestId) || other.deliveryRequestId == deliveryRequestId)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,trackingNumber,provider,providerType,estimatedDeliveryDate,notes,deliveryPersonId,deliveryCharge,deliveryRequestId,const DeepCollectionEquality().hash(_metadata));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ShippingDetails(trackingNumber: $trackingNumber, provider: $provider, providerType: $providerType, estimatedDeliveryDate: $estimatedDeliveryDate, notes: $notes, deliveryPersonId: $deliveryPersonId, deliveryCharge: $deliveryCharge, deliveryRequestId: $deliveryRequestId, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$ShippingDetailsCopyWith<$Res> implements $ShippingDetailsCopyWith<$Res> {
  factory _$ShippingDetailsCopyWith(_ShippingDetails value, $Res Function(_ShippingDetails) _then) = __$ShippingDetailsCopyWithImpl;
@override @useResult
$Res call({
 String trackingNumber, String provider, DeliveryProviderType providerType, DateTime? estimatedDeliveryDate, String? notes, String? deliveryPersonId, double? deliveryCharge, String? deliveryRequestId, Map<String, dynamic> metadata
});




}
/// @nodoc
class __$ShippingDetailsCopyWithImpl<$Res>
    implements _$ShippingDetailsCopyWith<$Res> {
  __$ShippingDetailsCopyWithImpl(this._self, this._then);

  final _ShippingDetails _self;
  final $Res Function(_ShippingDetails) _then;

/// Create a copy of ShippingDetails
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? trackingNumber = null,Object? provider = null,Object? providerType = null,Object? estimatedDeliveryDate = freezed,Object? notes = freezed,Object? deliveryPersonId = freezed,Object? deliveryCharge = freezed,Object? deliveryRequestId = freezed,Object? metadata = null,}) {
  return _then(_ShippingDetails(
trackingNumber: null == trackingNumber ? _self.trackingNumber : trackingNumber // ignore: cast_nullable_to_non_nullable
as String,provider: null == provider ? _self.provider : provider // ignore: cast_nullable_to_non_nullable
as String,providerType: null == providerType ? _self.providerType : providerType // ignore: cast_nullable_to_non_nullable
as DeliveryProviderType,estimatedDeliveryDate: freezed == estimatedDeliveryDate ? _self.estimatedDeliveryDate : estimatedDeliveryDate // ignore: cast_nullable_to_non_nullable
as DateTime?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,deliveryPersonId: freezed == deliveryPersonId ? _self.deliveryPersonId : deliveryPersonId // ignore: cast_nullable_to_non_nullable
as String?,deliveryCharge: freezed == deliveryCharge ? _self.deliveryCharge : deliveryCharge // ignore: cast_nullable_to_non_nullable
as double?,deliveryRequestId: freezed == deliveryRequestId ? _self.deliveryRequestId : deliveryRequestId // ignore: cast_nullable_to_non_nullable
as String?,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

/// @nodoc
mixin _$OrderModel implements DiagnosticableTreeMixin {

 String get id; String get buyerId; String get sellerId; List<OrderItem> get items; double get total; OrderStatus get status; DateTime get createdAt; DateTime get updatedAt;// Customer details
 String get customerName; String get customerPhone; String? get customerEmail;// Address details
 OrderAddress get deliveryAddress;// Payment details
 PaymentMethod get paymentMethod; PaymentDetails get paymentDetails;// Shipping details
 ShippingDetails? get shippingDetails;// Additional details
 String? get notes; String? get cancellationReason; List<String> get orderNotes; bool get isDeleted;// Shopping list related fields
 String? get shoppingListId; bool get isPricingRequested; bool get isPriced; String? get orderType;
/// Create a copy of OrderModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderModelCopyWith<OrderModel> get copyWith => _$OrderModelCopyWithImpl<OrderModel>(this as OrderModel, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderModel'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('buyerId', buyerId))..add(DiagnosticsProperty('sellerId', sellerId))..add(DiagnosticsProperty('items', items))..add(DiagnosticsProperty('total', total))..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('updatedAt', updatedAt))..add(DiagnosticsProperty('customerName', customerName))..add(DiagnosticsProperty('customerPhone', customerPhone))..add(DiagnosticsProperty('customerEmail', customerEmail))..add(DiagnosticsProperty('deliveryAddress', deliveryAddress))..add(DiagnosticsProperty('paymentMethod', paymentMethod))..add(DiagnosticsProperty('paymentDetails', paymentDetails))..add(DiagnosticsProperty('shippingDetails', shippingDetails))..add(DiagnosticsProperty('notes', notes))..add(DiagnosticsProperty('cancellationReason', cancellationReason))..add(DiagnosticsProperty('orderNotes', orderNotes))..add(DiagnosticsProperty('isDeleted', isDeleted))..add(DiagnosticsProperty('shoppingListId', shoppingListId))..add(DiagnosticsProperty('isPricingRequested', isPricingRequested))..add(DiagnosticsProperty('isPriced', isPriced))..add(DiagnosticsProperty('orderType', orderType));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderModel&&(identical(other.id, id) || other.id == id)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&const DeepCollectionEquality().equals(other.items, items)&&(identical(other.total, total) || other.total == total)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.customerName, customerName) || other.customerName == customerName)&&(identical(other.customerPhone, customerPhone) || other.customerPhone == customerPhone)&&(identical(other.customerEmail, customerEmail) || other.customerEmail == customerEmail)&&(identical(other.deliveryAddress, deliveryAddress) || other.deliveryAddress == deliveryAddress)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.paymentDetails, paymentDetails) || other.paymentDetails == paymentDetails)&&(identical(other.shippingDetails, shippingDetails) || other.shippingDetails == shippingDetails)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.cancellationReason, cancellationReason) || other.cancellationReason == cancellationReason)&&const DeepCollectionEquality().equals(other.orderNotes, orderNotes)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.shoppingListId, shoppingListId) || other.shoppingListId == shoppingListId)&&(identical(other.isPricingRequested, isPricingRequested) || other.isPricingRequested == isPricingRequested)&&(identical(other.isPriced, isPriced) || other.isPriced == isPriced)&&(identical(other.orderType, orderType) || other.orderType == orderType));
}


@override
int get hashCode => Object.hashAll([runtimeType,id,buyerId,sellerId,const DeepCollectionEquality().hash(items),total,status,createdAt,updatedAt,customerName,customerPhone,customerEmail,deliveryAddress,paymentMethod,paymentDetails,shippingDetails,notes,cancellationReason,const DeepCollectionEquality().hash(orderNotes),isDeleted,shoppingListId,isPricingRequested,isPriced,orderType]);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderModel(id: $id, buyerId: $buyerId, sellerId: $sellerId, items: $items, total: $total, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, customerName: $customerName, customerPhone: $customerPhone, customerEmail: $customerEmail, deliveryAddress: $deliveryAddress, paymentMethod: $paymentMethod, paymentDetails: $paymentDetails, shippingDetails: $shippingDetails, notes: $notes, cancellationReason: $cancellationReason, orderNotes: $orderNotes, isDeleted: $isDeleted, shoppingListId: $shoppingListId, isPricingRequested: $isPricingRequested, isPriced: $isPriced, orderType: $orderType)';
}


}

/// @nodoc
abstract mixin class $OrderModelCopyWith<$Res>  {
  factory $OrderModelCopyWith(OrderModel value, $Res Function(OrderModel) _then) = _$OrderModelCopyWithImpl;
@useResult
$Res call({
 String id, String buyerId, String sellerId, List<OrderItem> items, double total, OrderStatus status, DateTime createdAt, DateTime updatedAt, String customerName, String customerPhone, String? customerEmail, OrderAddress deliveryAddress, PaymentMethod paymentMethod, PaymentDetails paymentDetails, ShippingDetails? shippingDetails, String? notes, String? cancellationReason, List<String> orderNotes, bool isDeleted, String? shoppingListId, bool isPricingRequested, bool isPriced, String? orderType
});


$OrderAddressCopyWith<$Res> get deliveryAddress;$PaymentDetailsCopyWith<$Res> get paymentDetails;$ShippingDetailsCopyWith<$Res>? get shippingDetails;

}
/// @nodoc
class _$OrderModelCopyWithImpl<$Res>
    implements $OrderModelCopyWith<$Res> {
  _$OrderModelCopyWithImpl(this._self, this._then);

  final OrderModel _self;
  final $Res Function(OrderModel) _then;

/// Create a copy of OrderModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? buyerId = null,Object? sellerId = null,Object? items = null,Object? total = null,Object? status = null,Object? createdAt = null,Object? updatedAt = null,Object? customerName = null,Object? customerPhone = null,Object? customerEmail = freezed,Object? deliveryAddress = null,Object? paymentMethod = null,Object? paymentDetails = null,Object? shippingDetails = freezed,Object? notes = freezed,Object? cancellationReason = freezed,Object? orderNotes = null,Object? isDeleted = null,Object? shoppingListId = freezed,Object? isPricingRequested = null,Object? isPriced = null,Object? orderType = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,buyerId: null == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,items: null == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<OrderItem>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,customerName: null == customerName ? _self.customerName : customerName // ignore: cast_nullable_to_non_nullable
as String,customerPhone: null == customerPhone ? _self.customerPhone : customerPhone // ignore: cast_nullable_to_non_nullable
as String,customerEmail: freezed == customerEmail ? _self.customerEmail : customerEmail // ignore: cast_nullable_to_non_nullable
as String?,deliveryAddress: null == deliveryAddress ? _self.deliveryAddress : deliveryAddress // ignore: cast_nullable_to_non_nullable
as OrderAddress,paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as PaymentMethod,paymentDetails: null == paymentDetails ? _self.paymentDetails : paymentDetails // ignore: cast_nullable_to_non_nullable
as PaymentDetails,shippingDetails: freezed == shippingDetails ? _self.shippingDetails : shippingDetails // ignore: cast_nullable_to_non_nullable
as ShippingDetails?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,cancellationReason: freezed == cancellationReason ? _self.cancellationReason : cancellationReason // ignore: cast_nullable_to_non_nullable
as String?,orderNotes: null == orderNotes ? _self.orderNotes : orderNotes // ignore: cast_nullable_to_non_nullable
as List<String>,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,shoppingListId: freezed == shoppingListId ? _self.shoppingListId : shoppingListId // ignore: cast_nullable_to_non_nullable
as String?,isPricingRequested: null == isPricingRequested ? _self.isPricingRequested : isPricingRequested // ignore: cast_nullable_to_non_nullable
as bool,isPriced: null == isPriced ? _self.isPriced : isPriced // ignore: cast_nullable_to_non_nullable
as bool,orderType: freezed == orderType ? _self.orderType : orderType // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of OrderModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OrderAddressCopyWith<$Res> get deliveryAddress {
  
  return $OrderAddressCopyWith<$Res>(_self.deliveryAddress, (value) {
    return _then(_self.copyWith(deliveryAddress: value));
  });
}/// Create a copy of OrderModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentDetailsCopyWith<$Res> get paymentDetails {
  
  return $PaymentDetailsCopyWith<$Res>(_self.paymentDetails, (value) {
    return _then(_self.copyWith(paymentDetails: value));
  });
}/// Create a copy of OrderModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ShippingDetailsCopyWith<$Res>? get shippingDetails {
    if (_self.shippingDetails == null) {
    return null;
  }

  return $ShippingDetailsCopyWith<$Res>(_self.shippingDetails!, (value) {
    return _then(_self.copyWith(shippingDetails: value));
  });
}
}


/// Adds pattern-matching-related methods to [OrderModel].
extension OrderModelPatterns on OrderModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OrderModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OrderModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OrderModel value)  $default,){
final _that = this;
switch (_that) {
case _OrderModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OrderModel value)?  $default,){
final _that = this;
switch (_that) {
case _OrderModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String buyerId,  String sellerId,  List<OrderItem> items,  double total,  OrderStatus status,  DateTime createdAt,  DateTime updatedAt,  String customerName,  String customerPhone,  String? customerEmail,  OrderAddress deliveryAddress,  PaymentMethod paymentMethod,  PaymentDetails paymentDetails,  ShippingDetails? shippingDetails,  String? notes,  String? cancellationReason,  List<String> orderNotes,  bool isDeleted,  String? shoppingListId,  bool isPricingRequested,  bool isPriced,  String? orderType)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OrderModel() when $default != null:
return $default(_that.id,_that.buyerId,_that.sellerId,_that.items,_that.total,_that.status,_that.createdAt,_that.updatedAt,_that.customerName,_that.customerPhone,_that.customerEmail,_that.deliveryAddress,_that.paymentMethod,_that.paymentDetails,_that.shippingDetails,_that.notes,_that.cancellationReason,_that.orderNotes,_that.isDeleted,_that.shoppingListId,_that.isPricingRequested,_that.isPriced,_that.orderType);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String buyerId,  String sellerId,  List<OrderItem> items,  double total,  OrderStatus status,  DateTime createdAt,  DateTime updatedAt,  String customerName,  String customerPhone,  String? customerEmail,  OrderAddress deliveryAddress,  PaymentMethod paymentMethod,  PaymentDetails paymentDetails,  ShippingDetails? shippingDetails,  String? notes,  String? cancellationReason,  List<String> orderNotes,  bool isDeleted,  String? shoppingListId,  bool isPricingRequested,  bool isPriced,  String? orderType)  $default,) {final _that = this;
switch (_that) {
case _OrderModel():
return $default(_that.id,_that.buyerId,_that.sellerId,_that.items,_that.total,_that.status,_that.createdAt,_that.updatedAt,_that.customerName,_that.customerPhone,_that.customerEmail,_that.deliveryAddress,_that.paymentMethod,_that.paymentDetails,_that.shippingDetails,_that.notes,_that.cancellationReason,_that.orderNotes,_that.isDeleted,_that.shoppingListId,_that.isPricingRequested,_that.isPriced,_that.orderType);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String buyerId,  String sellerId,  List<OrderItem> items,  double total,  OrderStatus status,  DateTime createdAt,  DateTime updatedAt,  String customerName,  String customerPhone,  String? customerEmail,  OrderAddress deliveryAddress,  PaymentMethod paymentMethod,  PaymentDetails paymentDetails,  ShippingDetails? shippingDetails,  String? notes,  String? cancellationReason,  List<String> orderNotes,  bool isDeleted,  String? shoppingListId,  bool isPricingRequested,  bool isPriced,  String? orderType)?  $default,) {final _that = this;
switch (_that) {
case _OrderModel() when $default != null:
return $default(_that.id,_that.buyerId,_that.sellerId,_that.items,_that.total,_that.status,_that.createdAt,_that.updatedAt,_that.customerName,_that.customerPhone,_that.customerEmail,_that.deliveryAddress,_that.paymentMethod,_that.paymentDetails,_that.shippingDetails,_that.notes,_that.cancellationReason,_that.orderNotes,_that.isDeleted,_that.shoppingListId,_that.isPricingRequested,_that.isPriced,_that.orderType);case _:
  return null;

}
}

}

/// @nodoc


class _OrderModel extends OrderModel with DiagnosticableTreeMixin {
  const _OrderModel({required this.id, required this.buyerId, required this.sellerId, required final  List<OrderItem> items, required this.total, required this.status, required this.createdAt, required this.updatedAt, required this.customerName, required this.customerPhone, this.customerEmail, required this.deliveryAddress, required this.paymentMethod, required this.paymentDetails, this.shippingDetails, this.notes, this.cancellationReason, final  List<String> orderNotes = const [], this.isDeleted = false, this.shoppingListId, this.isPricingRequested = false, this.isPriced = false, this.orderType}): _items = items,_orderNotes = orderNotes,super._();
  

@override final  String id;
@override final  String buyerId;
@override final  String sellerId;
 final  List<OrderItem> _items;
@override List<OrderItem> get items {
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_items);
}

@override final  double total;
@override final  OrderStatus status;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
// Customer details
@override final  String customerName;
@override final  String customerPhone;
@override final  String? customerEmail;
// Address details
@override final  OrderAddress deliveryAddress;
// Payment details
@override final  PaymentMethod paymentMethod;
@override final  PaymentDetails paymentDetails;
// Shipping details
@override final  ShippingDetails? shippingDetails;
// Additional details
@override final  String? notes;
@override final  String? cancellationReason;
 final  List<String> _orderNotes;
@override@JsonKey() List<String> get orderNotes {
  if (_orderNotes is EqualUnmodifiableListView) return _orderNotes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_orderNotes);
}

@override@JsonKey() final  bool isDeleted;
// Shopping list related fields
@override final  String? shoppingListId;
@override@JsonKey() final  bool isPricingRequested;
@override@JsonKey() final  bool isPriced;
@override final  String? orderType;

/// Create a copy of OrderModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderModelCopyWith<_OrderModel> get copyWith => __$OrderModelCopyWithImpl<_OrderModel>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'OrderModel'))
    ..add(DiagnosticsProperty('id', id))..add(DiagnosticsProperty('buyerId', buyerId))..add(DiagnosticsProperty('sellerId', sellerId))..add(DiagnosticsProperty('items', items))..add(DiagnosticsProperty('total', total))..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('createdAt', createdAt))..add(DiagnosticsProperty('updatedAt', updatedAt))..add(DiagnosticsProperty('customerName', customerName))..add(DiagnosticsProperty('customerPhone', customerPhone))..add(DiagnosticsProperty('customerEmail', customerEmail))..add(DiagnosticsProperty('deliveryAddress', deliveryAddress))..add(DiagnosticsProperty('paymentMethod', paymentMethod))..add(DiagnosticsProperty('paymentDetails', paymentDetails))..add(DiagnosticsProperty('shippingDetails', shippingDetails))..add(DiagnosticsProperty('notes', notes))..add(DiagnosticsProperty('cancellationReason', cancellationReason))..add(DiagnosticsProperty('orderNotes', orderNotes))..add(DiagnosticsProperty('isDeleted', isDeleted))..add(DiagnosticsProperty('shoppingListId', shoppingListId))..add(DiagnosticsProperty('isPricingRequested', isPricingRequested))..add(DiagnosticsProperty('isPriced', isPriced))..add(DiagnosticsProperty('orderType', orderType));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderModel&&(identical(other.id, id) || other.id == id)&&(identical(other.buyerId, buyerId) || other.buyerId == buyerId)&&(identical(other.sellerId, sellerId) || other.sellerId == sellerId)&&const DeepCollectionEquality().equals(other._items, _items)&&(identical(other.total, total) || other.total == total)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.customerName, customerName) || other.customerName == customerName)&&(identical(other.customerPhone, customerPhone) || other.customerPhone == customerPhone)&&(identical(other.customerEmail, customerEmail) || other.customerEmail == customerEmail)&&(identical(other.deliveryAddress, deliveryAddress) || other.deliveryAddress == deliveryAddress)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.paymentDetails, paymentDetails) || other.paymentDetails == paymentDetails)&&(identical(other.shippingDetails, shippingDetails) || other.shippingDetails == shippingDetails)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.cancellationReason, cancellationReason) || other.cancellationReason == cancellationReason)&&const DeepCollectionEquality().equals(other._orderNotes, _orderNotes)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.shoppingListId, shoppingListId) || other.shoppingListId == shoppingListId)&&(identical(other.isPricingRequested, isPricingRequested) || other.isPricingRequested == isPricingRequested)&&(identical(other.isPriced, isPriced) || other.isPriced == isPriced)&&(identical(other.orderType, orderType) || other.orderType == orderType));
}


@override
int get hashCode => Object.hashAll([runtimeType,id,buyerId,sellerId,const DeepCollectionEquality().hash(_items),total,status,createdAt,updatedAt,customerName,customerPhone,customerEmail,deliveryAddress,paymentMethod,paymentDetails,shippingDetails,notes,cancellationReason,const DeepCollectionEquality().hash(_orderNotes),isDeleted,shoppingListId,isPricingRequested,isPriced,orderType]);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'OrderModel(id: $id, buyerId: $buyerId, sellerId: $sellerId, items: $items, total: $total, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, customerName: $customerName, customerPhone: $customerPhone, customerEmail: $customerEmail, deliveryAddress: $deliveryAddress, paymentMethod: $paymentMethod, paymentDetails: $paymentDetails, shippingDetails: $shippingDetails, notes: $notes, cancellationReason: $cancellationReason, orderNotes: $orderNotes, isDeleted: $isDeleted, shoppingListId: $shoppingListId, isPricingRequested: $isPricingRequested, isPriced: $isPriced, orderType: $orderType)';
}


}

/// @nodoc
abstract mixin class _$OrderModelCopyWith<$Res> implements $OrderModelCopyWith<$Res> {
  factory _$OrderModelCopyWith(_OrderModel value, $Res Function(_OrderModel) _then) = __$OrderModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String buyerId, String sellerId, List<OrderItem> items, double total, OrderStatus status, DateTime createdAt, DateTime updatedAt, String customerName, String customerPhone, String? customerEmail, OrderAddress deliveryAddress, PaymentMethod paymentMethod, PaymentDetails paymentDetails, ShippingDetails? shippingDetails, String? notes, String? cancellationReason, List<String> orderNotes, bool isDeleted, String? shoppingListId, bool isPricingRequested, bool isPriced, String? orderType
});


@override $OrderAddressCopyWith<$Res> get deliveryAddress;@override $PaymentDetailsCopyWith<$Res> get paymentDetails;@override $ShippingDetailsCopyWith<$Res>? get shippingDetails;

}
/// @nodoc
class __$OrderModelCopyWithImpl<$Res>
    implements _$OrderModelCopyWith<$Res> {
  __$OrderModelCopyWithImpl(this._self, this._then);

  final _OrderModel _self;
  final $Res Function(_OrderModel) _then;

/// Create a copy of OrderModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? buyerId = null,Object? sellerId = null,Object? items = null,Object? total = null,Object? status = null,Object? createdAt = null,Object? updatedAt = null,Object? customerName = null,Object? customerPhone = null,Object? customerEmail = freezed,Object? deliveryAddress = null,Object? paymentMethod = null,Object? paymentDetails = null,Object? shippingDetails = freezed,Object? notes = freezed,Object? cancellationReason = freezed,Object? orderNotes = null,Object? isDeleted = null,Object? shoppingListId = freezed,Object? isPricingRequested = null,Object? isPriced = null,Object? orderType = freezed,}) {
  return _then(_OrderModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,buyerId: null == buyerId ? _self.buyerId : buyerId // ignore: cast_nullable_to_non_nullable
as String,sellerId: null == sellerId ? _self.sellerId : sellerId // ignore: cast_nullable_to_non_nullable
as String,items: null == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<OrderItem>,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,customerName: null == customerName ? _self.customerName : customerName // ignore: cast_nullable_to_non_nullable
as String,customerPhone: null == customerPhone ? _self.customerPhone : customerPhone // ignore: cast_nullable_to_non_nullable
as String,customerEmail: freezed == customerEmail ? _self.customerEmail : customerEmail // ignore: cast_nullable_to_non_nullable
as String?,deliveryAddress: null == deliveryAddress ? _self.deliveryAddress : deliveryAddress // ignore: cast_nullable_to_non_nullable
as OrderAddress,paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as PaymentMethod,paymentDetails: null == paymentDetails ? _self.paymentDetails : paymentDetails // ignore: cast_nullable_to_non_nullable
as PaymentDetails,shippingDetails: freezed == shippingDetails ? _self.shippingDetails : shippingDetails // ignore: cast_nullable_to_non_nullable
as ShippingDetails?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,cancellationReason: freezed == cancellationReason ? _self.cancellationReason : cancellationReason // ignore: cast_nullable_to_non_nullable
as String?,orderNotes: null == orderNotes ? _self._orderNotes : orderNotes // ignore: cast_nullable_to_non_nullable
as List<String>,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,shoppingListId: freezed == shoppingListId ? _self.shoppingListId : shoppingListId // ignore: cast_nullable_to_non_nullable
as String?,isPricingRequested: null == isPricingRequested ? _self.isPricingRequested : isPricingRequested // ignore: cast_nullable_to_non_nullable
as bool,isPriced: null == isPriced ? _self.isPriced : isPriced // ignore: cast_nullable_to_non_nullable
as bool,orderType: freezed == orderType ? _self.orderType : orderType // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of OrderModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$OrderAddressCopyWith<$Res> get deliveryAddress {
  
  return $OrderAddressCopyWith<$Res>(_self.deliveryAddress, (value) {
    return _then(_self.copyWith(deliveryAddress: value));
  });
}/// Create a copy of OrderModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentDetailsCopyWith<$Res> get paymentDetails {
  
  return $PaymentDetailsCopyWith<$Res>(_self.paymentDetails, (value) {
    return _then(_self.copyWith(paymentDetails: value));
  });
}/// Create a copy of OrderModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ShippingDetailsCopyWith<$Res>? get shippingDetails {
    if (_self.shippingDetails == null) {
    return null;
  }

  return $ShippingDetailsCopyWith<$Res>(_self.shippingDetails!, (value) {
    return _then(_self.copyWith(shippingDetails: value));
  });
}
}

// dart format on
