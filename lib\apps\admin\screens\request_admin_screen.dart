import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/auth_provider.dart';

class RequestAdminScreen extends ConsumerStatefulWidget {
  const RequestAdminScreen({super.key});

  @override
  ConsumerState<RequestAdminScreen> createState() => _RequestAdminScreenState();
}

class _RequestAdminScreenState extends ConsumerState<RequestAdminScreen> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(adminAuthStateProvider);
    final authNotifier = ref.read(adminAuthStateProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Request Admin Access'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Request Admin Access',
                style: theme.textTheme.headlineSmall,
              ),
              const SizedBox(height: 16),
              Text(
                'Please provide a reason for requesting admin access. Your request will be reviewed by an existing admin.',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(179),
                ),
              ),
              const SizedBox(height: 24),
              TextFormField(
                controller: _reasonController,
                decoration: const InputDecoration(
                  labelText: 'Reason for Request',
                  hintText: 'Please explain why you need admin access',
                  border: OutlineInputBorder(),
                ),
                maxLines: 5,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please provide a reason';
                  }
                  if (value.length < 20) {
                    return 'Please provide a more detailed reason';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: authState.isLoading
                      ? null
                      : () {
                          if (_formKey.currentState?.validate() ?? false) {
                            authNotifier.requestAdminAccess(
                              email: authState.user?.email ?? '',
                              password:
                                  '', // Password is not needed for request
                              displayName: authState.user?.userMetadata?['display_name'] ??
                                          authState.user?.userMetadata?['full_name'] ?? '',
                              reason: _reasonController.text,
                            );
                          }
                        },
                  child: authState.isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                          ),
                        )
                      : const Text('Submit Request'),
                ),
              ),
              if (authState.error != null) ...[
                const SizedBox(height: 16),
                Text(
                  authState.error!,
                  style: TextStyle(
                    color: theme.colorScheme.error,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
