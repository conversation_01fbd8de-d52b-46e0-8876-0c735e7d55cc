import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/presentation/cubits/price_management/price_management_cubit.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'widgets/bulk_price_update.dart';
import 'widgets/price_history.dart';
import 'widgets/price_list.dart';

class PriceManagementScreen extends StatefulWidget {
  const PriceManagementScreen({super.key});

  @override
  State<PriceManagementScreen> createState() => _PriceManagementScreenState();
}

class _PriceManagementScreenState extends State<PriceManagementScreen> {
  List<String> _selectedProductIds = [];

  @override
  void initState() {
    super.initState();
    context.read<PriceManagementCubit>().loadProducts();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Price Management'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Prices'),
              Tab(text: 'Bulk Update'),
              Tab(text: 'History'),
            ],
          ),
        ),
        body: BlocBuilder<PriceManagementCubit, PriceManagementState>(
          builder: (context, state) {
            if (state.isLoading) {
              return const LoadingIndicator();
            }

            if (state.error != null) {
              return ErrorMessage(
                message: state.error!,
                onRetry: () {
                  context.read<PriceManagementCubit>().loadProducts();
                },
              );
            }

            return TabBarView(
              children: [
                PriceList(
                  products: state.products,
                  onPriceUpdate: (productId, price) {
                    context
                        .read<PriceManagementCubit>()
                        .updatePrice(productId, price);
                  },
                  onDiscountUpdate: (productId, discount) {
                    context
                        .read<PriceManagementCubit>()
                        .updateDiscount(productId, discount);
                  },
                  onSelectionChange: (selectedIds) {
                    setState(() {
                      _selectedProductIds = selectedIds;
                    });
                  },
                ),
                BulkPriceUpdate(
                  selectedProductIds: _selectedProductIds,
                  products: state.products,
                  onBulkUpdate: (type, value, productIds) {
                    context
                        .read<PriceManagementCubit>()
                        .bulkUpdatePrices(type, value, productIds);
                  },
                ),
                PriceHistory(
                  history: state.priceHistory,
                  onFilterChange: (filter) {
                    context
                        .read<PriceManagementCubit>()
                        .loadPriceHistory(filter);
                  },
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
