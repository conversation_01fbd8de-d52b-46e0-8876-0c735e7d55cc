import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_model.freezed.dart';
part 'payment_model.g.dart';

@freezed
sealed class PaymentSummary with _$PaymentSummary {
  const factory PaymentSummary({
    required double totalBalance,
    required double pendingAmount,
    required double availableAmount,
  }) = _PaymentSummary;

  factory PaymentSummary.fromJson(Map<String, dynamic> json) =>
      _$PaymentSummaryFromJson(json);
}

@freezed
sealed class TransactionModel with _$TransactionModel {
  const factory TransactionModel({
    required String id,
    required String description,
    required double amount,
    required DateTime date,
    required bool isCredit,
    String? orderId,
    String? settlementId,
  }) = _TransactionModel;

  factory TransactionModel.fromJson(Map<String, dynamic> json) =>
      _$TransactionModelFromJson(json);
}

@freezed
sealed class SettlementModel with _$SettlementModel {
  const factory SettlementModel({
    required String id,
    required double amount,
    required DateTime date,
    required String status,
    String? bankAccountId,
    String? transactionId,
    String? failureReason,
  }) = _SettlementModel;

  factory SettlementModel.fromJson(Map<String, dynamic> json) =>
      _$SettlementModelFromJson(json);
}

@freezed
sealed class BankAccountModel with _$BankAccountModel {
  const factory BankAccountModel({
    required String id,
    required String accountNumber,
    required String bankName,
    required String accountHolderName,
    required String ifscCode,
    @Default(false) bool isVerified,
    @Default(false) bool isPrimary,
    DateTime? verifiedAt,
  }) = _BankAccountModel;

  factory BankAccountModel.fromJson(Map<String, dynamic> json) =>
      _$BankAccountModelFromJson(json);
}
