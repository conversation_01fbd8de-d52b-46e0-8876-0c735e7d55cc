import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'server_config.freezed.dart';
part 'server_config.g.dart';

/// Server configuration model
@freezed
sealed class ServerConfig with _$ServerConfig {
  const factory ServerConfig({
    required String environment,
    required String awsRegion,
    required String datacenterIp,
    required String databaseUrl,
    required String redisUrl,
    required int maxConnections,
    required int timeout,
    required ServerState awsStatus,
    required ServerState datacenterStatus,
    required bool autoScaling,
    required bool loadBalancing,
    required bool cachingEnabled,
    required bool sslEnabled,
    required bool rateLimitingEnabled,
    required bool ddosProtection,
    required DateTime lastUpdated,
    @Default({}) Map<String, dynamic> customSettings,
    @Default([]) List<ServerInstance> awsInstances,
    @Default([]) List<ServerInstance> datacenterInstances,
  }) = _ServerConfig;

  factory ServerConfig.fromJson(Map<String, dynamic> json) =>
      _$ServerConfigFromJson(json);
}

/// Server state enum
enum ServerState {
  online,
  offline,
  maintenance;

  String get displayName {
    switch (this) {
      case ServerState.online:
        return 'Online';
      case ServerState.offline:
        return 'Offline';
      case ServerState.maintenance:
        return 'Maintenance';
    }
  }

  Color get color {
    switch (this) {
      case ServerState.online:
        return const Color(0xFF4CAF50); // Green
      case ServerState.offline:
        return const Color(0xFFF44336); // Red
      case ServerState.maintenance:
        return const Color(0xFFFF9800); // Orange
    }
  }
}

/// Individual server instance
@freezed
sealed class ServerInstance with _$ServerInstance {
  const factory ServerInstance({
    required String id,
    required String name,
    required String ipAddress,
    required int port,
    required ServerInstanceType type,
    required ServerState status,
    required ServerMetrics metrics,
    required DateTime lastHealthCheck,
    @Default('Unknown') String region,
    @Default({}) Map<String, String> tags,
  }) = _ServerInstance;

  factory ServerInstance.fromJson(Map<String, dynamic> json) =>
      _$ServerInstanceFromJson(json);
}

/// Server instance types
enum ServerInstanceType {
  apiGateway,
  authService,
  businessLogic,
  database,
  fileStorage,
  paymentProcessor,
  analytics,
  backgroundJobs;

  String get displayName {
    switch (this) {
      case ServerInstanceType.apiGateway:
        return 'API Gateway';
      case ServerInstanceType.authService:
        return 'Authentication Service';
      case ServerInstanceType.businessLogic:
        return 'Business Logic';
      case ServerInstanceType.database:
        return 'Database';
      case ServerInstanceType.fileStorage:
        return 'File Storage';
      case ServerInstanceType.paymentProcessor:
        return 'Payment Processor';
      case ServerInstanceType.analytics:
        return 'Analytics';
      case ServerInstanceType.backgroundJobs:
        return 'Background Jobs';
    }
  }

  String get description {
    switch (this) {
      case ServerInstanceType.apiGateway:
        return 'Single entry point, load balancing, rate limiting';
      case ServerInstanceType.authService:
        return 'Supabase backend, user management, JWT tokens';
      case ServerInstanceType.businessLogic:
        return 'Main app logic, business rules';
      case ServerInstanceType.database:
        return 'PostgreSQL, hybrid database';
      case ServerInstanceType.fileStorage:
        return 'AWS S3, local storage';
      case ServerInstanceType.paymentProcessor:
        return 'UPI, payment gateway integrations';
      case ServerInstanceType.analytics:
        return 'Hybrid analytics, app monitoring';
      case ServerInstanceType.backgroundJobs:
        return 'Notifications, scheduled tasks';
    }
  }
}

/// Server performance metrics
@freezed
sealed class ServerMetrics with _$ServerMetrics {
  const factory ServerMetrics({
    required double cpuUsage,
    required double memoryUsage,
    required double diskUsage,
    required double networkIn,
    required double networkOut,
    required int activeConnections,
    required int requestsPerSecond,
    required double averageResponseTime,
    required double errorRate,
    required DateTime timestamp,
  }) = _ServerMetrics;

  factory ServerMetrics.fromJson(Map<String, dynamic> json) =>
      _$ServerMetricsFromJson(json);
}

/// Auto scaling configuration
@freezed
sealed class AutoScalingConfig with _$AutoScalingConfig {
  const factory AutoScalingConfig({
    required bool enabled,
    required int minInstances,
    required int maxInstances,
    required double cpuThresholdUp,
    required double cpuThresholdDown,
    required double memoryThresholdUp,
    required double memoryThresholdDown,
    required int scaleUpCooldown,
    required int scaleDownCooldown,
    @Default([]) List<ScalingPolicy> customPolicies,
  }) = _AutoScalingConfig;

  factory AutoScalingConfig.fromJson(Map<String, dynamic> json) =>
      _$AutoScalingConfigFromJson(json);
}

/// Scaling policy
@freezed
sealed class ScalingPolicy with _$ScalingPolicy {
  const factory ScalingPolicy({
    required String name,
    required ScalingMetric metric,
    required ScalingComparison comparison,
    required double threshold,
    required ScalingAction action,
    required int cooldownSeconds,
  }) = _ScalingPolicy;

  factory ScalingPolicy.fromJson(Map<String, dynamic> json) =>
      _$ScalingPolicyFromJson(json);
}

/// Scaling metrics
enum ScalingMetric {
  cpuUsage,
  memoryUsage,
  diskUsage,
  networkIn,
  networkOut,
  activeConnections,
  requestsPerSecond,
  responseTime,
  errorRate;

  String get displayName {
    switch (this) {
      case ScalingMetric.cpuUsage:
        return 'CPU Usage';
      case ScalingMetric.memoryUsage:
        return 'Memory Usage';
      case ScalingMetric.diskUsage:
        return 'Disk Usage';
      case ScalingMetric.networkIn:
        return 'Network In';
      case ScalingMetric.networkOut:
        return 'Network Out';
      case ScalingMetric.activeConnections:
        return 'Active Connections';
      case ScalingMetric.requestsPerSecond:
        return 'Requests Per Second';
      case ScalingMetric.responseTime:
        return 'Response Time';
      case ScalingMetric.errorRate:
        return 'Error Rate';
    }
  }
}

/// Scaling comparison operators
enum ScalingComparison {
  greaterThan,
  lessThan,
  greaterThanOrEqual,
  lessThanOrEqual;

  String get displayName {
    switch (this) {
      case ScalingComparison.greaterThan:
        return 'Greater Than';
      case ScalingComparison.lessThan:
        return 'Less Than';
      case ScalingComparison.greaterThanOrEqual:
        return 'Greater Than or Equal';
      case ScalingComparison.lessThanOrEqual:
        return 'Less Than or Equal';
    }
  }

  String get symbol {
    switch (this) {
      case ScalingComparison.greaterThan:
        return '>';
      case ScalingComparison.lessThan:
        return '<';
      case ScalingComparison.greaterThanOrEqual:
        return '>=';
      case ScalingComparison.lessThanOrEqual:
        return '<=';
    }
  }
}

/// Scaling actions
enum ScalingAction {
  scaleUp,
  scaleDown,
  sendAlert,
  runScript;

  String get displayName {
    switch (this) {
      case ScalingAction.scaleUp:
        return 'Scale Up';
      case ScalingAction.scaleDown:
        return 'Scale Down';
      case ScalingAction.sendAlert:
        return 'Send Alert';
      case ScalingAction.runScript:
        return 'Run Script';
    }
  }
}

/// Configuration test result
@freezed
sealed class ConfigTestResult with _$ConfigTestResult {
  const factory ConfigTestResult({
    required bool success,
    String? error,
    @Default({}) Map<String, dynamic> details,
    DateTime? timestamp,
  }) = _ConfigTestResult;

  factory ConfigTestResult.fromJson(Map<String, dynamic> json) =>
      _$ConfigTestResultFromJson(json);
}

/// Database configuration
@freezed
sealed class DatabaseConfig with _$DatabaseConfig {
  const factory DatabaseConfig({
    required String host,
    required int port,
    required String database,
    required String username,
    required String password,
    required int maxConnections,
    required int connectionTimeout,
    required bool sslEnabled,
    @Default('postgresql') String type,
    @Default({}) Map<String, dynamic> additionalParams,
  }) = _DatabaseConfig;

  factory DatabaseConfig.fromJson(Map<String, dynamic> json) =>
      _$DatabaseConfigFromJson(json);
}

/// Redis configuration
@freezed
sealed class RedisConfig with _$RedisConfig {
  const factory RedisConfig({
    required String host,
    required int port,
    required String password,
    required int database,
    required int maxConnections,
    required int connectionTimeout,
    @Default(false) bool clusterMode,
    @Default([]) List<String> clusterNodes,
  }) = _RedisConfig;

  factory RedisConfig.fromJson(Map<String, dynamic> json) =>
      _$RedisConfigFromJson(json);
}
