import 'package:freezed_annotation/freezed_annotation.dart';
import 'ai_config_model.dart';
import 'chatbot_config_model.dart';
import 'refund_config_model.dart';
import 'security_config_model.dart';
import 'voice_command_config_model.dart';

part 'system_config_model.freezed.dart';
part 'system_config_model.g.dart';

@freezed
sealed class SystemConfigModel with _$SystemConfigModel {
  const factory SystemConfigModel({
    required SecurityConfigModel security,
    required RefundConfigModel refund,
    required AIConfigModel ai,
    required VoiceCommandConfigModel voiceCommand,
    required ChatbotConfigModel chatbot,
    required Map<String, dynamic> additionalSettings,
  }) = _SystemConfigModel;

  factory SystemConfigModel.fromJson(Map<String, dynamic> json) =>
      _$SystemConfigModelFromJson(json);
}
