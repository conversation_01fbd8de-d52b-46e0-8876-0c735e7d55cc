import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/shared/models/shopping_list/list_submission_model.dart';
import 'package:shivish/shared/models/shopping_list/shopping_list_model.dart';
import 'package:shivish/shared/providers/list_submission_provider.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

class ListSubmissionScreen extends ConsumerStatefulWidget {
  final String submissionId;

  const ListSubmissionScreen({
    super.key,
    required this.submissionId,
  });

  @override
  ConsumerState<ListSubmissionScreen> createState() =>
      _ListSubmissionScreenState();
}

class _ListSubmissionScreenState extends ConsumerState<ListSubmissionScreen> {
  @override
  void initState() {
    super.initState();
    ref
        .read(listSubmissionProvider.notifier)
        .watchSubmission(widget.submissionId);
  }

  void _handleAcceptedSubmission(ListSubmissionModel submission) {
    final items = submission.shoppingList.items
        .map((item) => ListSubmissionOrderItem(
              id: item.id,
              name: item.name,
              quantity: item.quantity,
              notes: item.notes,
              productId: item.productId,
            ))
        .toList();

    context.push(
      '/buyer/list-submissions/${widget.submissionId}/convert',
      extra: {
        'buyerId': submission.buyerId,
        'sellerId': submission.sellerId,
        'items': items,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(listSubmissionProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('List Submission'),
      ),
      body: Stack(
        children: [
          if (state.error != null)
            Center(
              child: ErrorMessage(message: state.error!),
            )
          else if (state.submission != null)
            _buildContent(state.submission!)
          else
            const SizedBox.shrink(),
          if (state.isLoading) const LoadingIndicator(),
        ],
      ),
    );
  }

  Widget _buildContent(ListSubmissionModel submission) {
    final shoppingList = submission.shoppingList;
    if (shoppingList.status == ShoppingListStatus.accepted) {
      _handleAcceptedSubmission(submission);
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSubmissionHeader(submission),
        const SizedBox(height: 16),
        _buildItemsList(submission),
      ],
    );
  }

  Widget _buildSubmissionHeader(ListSubmissionModel submission) {
    final shoppingList = submission.shoppingList;
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              shoppingList.name,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Status: ${shoppingList.status.name}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            if (shoppingList.notes != null) ...[
              const SizedBox(height: 8),
              Text(
                'Notes: ${shoppingList.notes}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildItemsList(ListSubmissionModel submission) {
    final shoppingList = submission.shoppingList;
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Items',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: shoppingList.items.length,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              final item = shoppingList.items[index];
              return ListTile(
                title: Text(item.name),
                subtitle: item.notes != null ? Text(item.notes!) : null,
                trailing: Text('Qty: ${item.quantity}'),
              );
            },
          ),
        ],
      ),
    );
  }
}

class ListSubmissionOrderItem {
  final String id;
  final String name;
  final int quantity;
  final String? notes;
  final String? productId;

  const ListSubmissionOrderItem({
    required this.id,
    required this.name,
    required this.quantity,
    this.notes,
    this.productId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'quantity': quantity,
      'notes': notes,
      'productId': productId,
    };
  }
}
