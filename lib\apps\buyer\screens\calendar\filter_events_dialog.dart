import 'package:flutter/material.dart';
import '../../../../shared/models/calendar/calendar_event_type.dart';

/// Dialog for filtering calendar events
class FilterEventsDialog extends StatefulWidget {
  /// Creates a [FilterEventsDialog]
  const FilterEventsDialog({
    super.key,
    this.selectedEventType,
    required this.onFilterSelected,
  });

  /// The currently selected event type
  final CalendarEventType? selectedEventType;

  /// Callback when a filter is selected
  final Function(CalendarEventType?) onFilterSelected;

  @override
  State<FilterEventsDialog> createState() => _FilterEventsDialogState();
}

class _FilterEventsDialogState extends State<FilterEventsDialog> {
  late CalendarEventType? _selectedType;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.selectedEventType;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Filter Events',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            _buildFilterOption(context, null, 'All Events'),
            _buildFilterOption(context, CalendarEventType.user, 'My Event'),
            _buildFilterOption(
                context, CalendarEventType.public, 'Public Event'),
            _buildFilterOption(
                context, CalendarEventType.productList, 'Product List'),
            _buildFilterOption(context, CalendarEventType.birthday, 'Birthday'),
            _buildFilterOption(
                context, CalendarEventType.anniversary, 'Anniversary'),
            _buildFilterOption(context, CalendarEventType.other, 'Other'),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(_selectedType);
                  },
                  child: const Text('Apply'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterOption(
      BuildContext context, CalendarEventType? type, String label) {
    return RadioListTile<CalendarEventType?>(
      title: Text(label),
      value: type,
      groupValue: _selectedType,
      onChanged: (value) {
        setState(() {
          _selectedType = value;
        });
      },
      activeColor: Theme.of(context).colorScheme.primary,
    );
  }
}
