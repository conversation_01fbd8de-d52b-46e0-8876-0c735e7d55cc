import 'package:shivish/apps/seller/domain/models/campaign_model.dart';
import 'package:shivish/apps/seller/domain/models/promotion_model.dart';

abstract class MarketingRepository {
  Future<List<PromotionModel>> getPromotions();
  Future<void> createPromotion(PromotionModel promotion);
  Future<void> updatePromotion(PromotionModel promotion);
  Future<void> deletePromotion(String id);

  Future<List<CampaignModel>> getCampaigns();
  Future<void> createCampaign(CampaignModel campaign);
  Future<void> updateCampaign(CampaignModel campaign);
  Future<void> deleteCampaign(String id);

  Future<void> updateCampaignMetrics(
    String id, {
    int? impressions,
    int? clicks,
    int? conversions,
    double? spent,
  });
}
