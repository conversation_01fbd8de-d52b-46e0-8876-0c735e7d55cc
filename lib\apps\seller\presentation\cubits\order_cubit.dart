import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/apps/seller/domain/use_cases/order_use_cases.dart';
import 'package:shivish/shared/models/order/order_model.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';

part 'order_cubit.freezed.dart';

@freezed
sealed class OrderState with _$OrderState {
  const factory OrderState({
    @Default([]) List<OrderModel> orders,
    @Default(false) bool isLoading,
    @Default(false) bool hasError,
    String? errorMessage,
    OrderStatus? selectedStatus,
    DateTime? startDate,
    DateTime? endDate,
    String? lastOrderId,
    @Default(true) bool hasMoreOrders,
  }) = _OrderState;
}

class OrderCubit extends Cubit<OrderState> {
  final GetOrdersUseCase _getOrdersUseCase;
  final GetOrderByIdUseCase _getOrderByIdUseCase;
  final UpdateOrderStatusUseCase _updateOrderStatusUseCase;
  final UpdatePaymentStatusUseCase _updatePaymentStatusUseCase;
  final UpdateShippingDetailsUseCase _updateShippingDetailsUseCase;
  final CancelOrderUseCase _cancelOrderUseCase;
  final ProcessRefundUseCase _processRefundUseCase;
  final AddOrderNoteUseCase _addOrderNoteUseCase;
  final WatchOrdersUseCase _watchOrdersUseCase;
  final AuthService _authService;

  OrderCubit({
    required GetOrdersUseCase getOrdersUseCase,
    required GetOrderByIdUseCase getOrderByIdUseCase,
    required UpdateOrderStatusUseCase updateOrderStatusUseCase,
    required UpdatePaymentStatusUseCase updatePaymentStatusUseCase,
    required UpdateShippingDetailsUseCase updateShippingDetailsUseCase,
    required CancelOrderUseCase cancelOrderUseCase,
    required ProcessRefundUseCase processRefundUseCase,
    required AddOrderNoteUseCase addOrderNoteUseCase,
    required WatchOrdersUseCase watchOrdersUseCase,
    required AuthService authService,
  })  : _getOrdersUseCase = getOrdersUseCase,
        _getOrderByIdUseCase = getOrderByIdUseCase,
        _updateOrderStatusUseCase = updateOrderStatusUseCase,
        _updatePaymentStatusUseCase = updatePaymentStatusUseCase,
        _updateShippingDetailsUseCase = updateShippingDetailsUseCase,
        _cancelOrderUseCase = cancelOrderUseCase,
        _processRefundUseCase = processRefundUseCase,
        _addOrderNoteUseCase = addOrderNoteUseCase,
        _watchOrdersUseCase = watchOrdersUseCase,
        _authService = authService,
        super(const OrderState());

  Future<void> loadOrders({
    bool refresh = false,
  }) async {
    if (refresh) {
      emit(state.copyWith(
        orders: [],
        lastOrderId: null,
        hasMoreOrders: true,
      ));
    }

    if (!state.hasMoreOrders) return;

    emit(state.copyWith(isLoading: true, hasError: false));

    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final orders = await _getOrdersUseCase(currentUser.id);

      if (orders.isEmpty) {
        emit(state.copyWith(
          isLoading: false,
          hasMoreOrders: false,
        ));
        return;
      }

      emit(state.copyWith(
        orders: [...state.orders, ...orders],
        lastOrderId: orders.last.id,
        isLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> refreshOrder(String orderId) async {
    try {
      final order = await _getOrderByIdUseCase(orderId);
      if (order == null) {
        throw Exception('Order not found');
      }
      final updatedOrders = state.orders.map((o) {
        if (o.id == orderId) return order;
        return o;
      }).toList();

      emit(state.copyWith(orders: updatedOrders));
    } catch (e) {
      emit(state.copyWith(
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  void setStatusFilter(OrderStatus? status) {
    emit(state.copyWith(selectedStatus: status));
    loadOrders(refresh: true);
  }

  void setDateRange(DateTime? startDate, DateTime? endDate) {
    emit(state.copyWith(
      startDate: startDate,
      endDate: endDate,
    ));
    loadOrders(refresh: true);
  }

  void clearFilters() {
    emit(state.copyWith(
      selectedStatus: null,
      startDate: null,
      endDate: null,
    ));
    loadOrders(refresh: true);
  }

  Future<void> updateOrderStatus({
    required String orderId,
    required OrderStatus status,
    String? notes,
  }) async {
    try {
      await _updateOrderStatusUseCase(orderId, status);
      await refreshOrder(orderId);
    } catch (e) {
      emit(state.copyWith(
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> updatePaymentStatus({
    required String orderId,
    required PaymentStatus status,
    String? notes,
  }) async {
    try {
      await _updatePaymentStatusUseCase(orderId, status);
      await refreshOrder(orderId);
    } catch (e) {
      emit(state.copyWith(
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> updateShippingDetails({
    required String orderId,
    required String trackingNumber,
    required String shippingProvider,
    DateTime? estimatedDeliveryDate,
  }) async {
    try {
      await _updateShippingDetailsUseCase(
        orderId,
        trackingNumber: trackingNumber,
        shippingProvider: shippingProvider,
        estimatedDeliveryDate: estimatedDeliveryDate,
      );
      await refreshOrder(orderId);
    } catch (e) {
      emit(state.copyWith(
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> cancelOrder({
    required String orderId,
    required String reason,
  }) async {
    try {
      await _cancelOrderUseCase(orderId, reason: reason);
      await refreshOrder(orderId);
    } catch (e) {
      emit(state.copyWith(
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> processRefund({
    required String orderId,
    required double amount,
    required String reason,
  }) async {
    try {
      await _processRefundUseCase(orderId, reason: reason);
      await refreshOrder(orderId);
    } catch (e) {
      emit(state.copyWith(
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> addOrderNote({
    required String orderId,
    required String note,
  }) async {
    try {
      await _addOrderNoteUseCase(
        orderId: orderId,
        note: note,
      );
      await refreshOrder(orderId);
    } catch (e) {
      emit(state.copyWith(
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  void startWatchingOrders() {
    _authService.getCurrentUser().then((user) {
      if (user == null) {
        emit(state.copyWith(
          hasError: true,
          errorMessage: 'User not authenticated',
        ));
        return;
      }

      _watchOrdersUseCase(
        sellerId: user.id,
        status: state.selectedStatus,
      ).listen(
        (orders) {
          emit(state.copyWith(orders: orders));
        },
        onError: (e) {
          emit(state.copyWith(
            hasError: true,
            errorMessage: e.toString(),
          ));
        },
      );
    });
  }
}
