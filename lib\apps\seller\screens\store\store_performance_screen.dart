import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/presentation/cubits/analytics_cubit.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';
import 'package:shivish/shared/utils/date_formatter.dart';
import 'package:fl_chart/fl_chart.dart';

class StorePerformanceScreen extends StatefulWidget {
  const StorePerformanceScreen({super.key});

  @override
  State<StorePerformanceScreen> createState() => _StorePerformanceScreenState();
}

class _StorePerformanceScreenState extends State<StorePerformanceScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    final cubit = context.read<AnalyticsCubit>();
    cubit.loadSalesAnalytics();
    cubit.loadProductAnalytics();
    cubit.loadCustomerAnalytics();
    cubit.loadPerformanceMetrics();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Store Performance'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Sales'),
            Tab(text: 'Products'),
            Tab(text: 'Customers'),
            Tab(text: 'Metrics'),
          ],
        ),
      ),
      body: BlocBuilder<AnalyticsCubit, AnalyticsState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    state.errorMessage ?? 'An error occurred',
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadData,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: const [
              _SalesTab(),
              _ProductsTab(),
              _CustomersTab(),
              _MetricsTab(),
            ],
          );
        },
      ),
    );
  }
}

class _SalesTab extends StatelessWidget {
  const _SalesTab();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSalesChart(context),
          const SizedBox(height: 24),
          _buildSalesMetrics(),
        ],
      ),
    );
  }

  Widget _buildSalesChart(BuildContext context) {
    return BlocBuilder<AnalyticsCubit, AnalyticsState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.hasError) {
          return Center(
            child: Text(
              state.errorMessage ?? 'Something went wrong',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          );
        }

        final salesAnalytics = state.salesAnalytics;
        if (salesAnalytics == null) {
          return const Center(child: Text('No sales data available'));
        }

        final spots = salesAnalytics.dailySales.map((point) {
          return FlSpot(
            point.date.millisecondsSinceEpoch.toDouble(),
            point.amount,
          );
        }).toList();

        return LineChart(
          LineChartData(
            gridData: const FlGridData(show: true),
            titlesData: const FlTitlesData(
              leftTitles: AxisTitles(
                sideTitles: SideTitles(showTitles: true),
              ),
              bottomTitles: AxisTitles(
                sideTitles: SideTitles(showTitles: true),
              ),
              rightTitles: AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
              topTitles: AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
            ),
            borderData: FlBorderData(show: true),
            lineBarsData: [
              LineChartBarData(
                spots: spots,
                isCurved: true,
                color: Theme.of(context).colorScheme.primary,
                barWidth: 3,
                dotData: const FlDotData(show: true),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSalesMetrics() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _MetricCard(
          title: 'Total Sales',
          value: CurrencyFormatter.format(10000),
          icon: Icons.attach_money,
          trend: 12.5,
        ),
        const _MetricCard(
          title: 'Orders',
          value: '156',
          icon: Icons.shopping_cart,
          trend: 8.3,
        ),
        _MetricCard(
          title: 'Average Order Value',
          value: CurrencyFormatter.format(64.10),
          icon: Icons.analytics,
          trend: -2.1,
        ),
        const _MetricCard(
          title: 'Conversion Rate',
          value: '3.2%',
          icon: Icons.trending_up,
          trend: 5.7,
        ),
      ],
    );
  }
}

class _ProductsTab extends StatelessWidget {
  const _ProductsTab();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTopProducts(context),
          const SizedBox(height: 24),
          _buildProductMetrics(),
        ],
      ),
    );
  }

  Widget _buildTopProducts(BuildContext context) {
    return BlocBuilder<AnalyticsCubit, AnalyticsState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.hasError) {
          return Center(child: Text(state.errorMessage ?? 'An error occurred'));
        }

        final productAnalytics = state.productAnalytics;
        if (productAnalytics == null || productAnalytics.topProducts.isEmpty) {
          return const Center(child: Text('No product data available'));
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'Top Products',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ...productAnalytics.topProducts.map((product) => ListTile(
                  leading: product.imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            product.imageUrl!,
                            width: 48,
                            height: 48,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: 48,
                                height: 48,
                                color: Colors.grey[200],
                                child: const Icon(Icons.image_not_supported),
                              );
                            },
                          ),
                        )
                      : Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(Icons.inventory_2_outlined),
                        ),
                  title: Text(product.name),
                  subtitle: Text('${product.soldCount} sold'),
                  trailing: Text(
                    CurrencyFormatter.format(product.revenue),
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                )),
          ],
        );
      },
    );
  }

  Widget _buildProductMetrics() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: const [
        _MetricCard(
          title: 'Total Products',
          value: '234',
          icon: Icons.inventory_2,
          trend: null,
        ),
        _MetricCard(
          title: 'Low Stock',
          value: '12',
          icon: Icons.warning,
          trend: null,
          trendColor: Colors.orange,
        ),
        _MetricCard(
          title: 'Out of Stock',
          value: '5',
          icon: Icons.remove_shopping_cart,
          trend: null,
          trendColor: Colors.red,
        ),
        _MetricCard(
          title: 'Categories',
          value: '8',
          icon: Icons.category,
          trend: null,
        ),
      ],
    );
  }
}

class _CustomersTab extends StatelessWidget {
  const _CustomersTab();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCustomerChart(context),
          const SizedBox(height: 24),
          _buildCustomerMetrics(),
        ],
      ),
    );
  }

  Widget _buildCustomerChart(BuildContext context) {
    return BlocBuilder<AnalyticsCubit, AnalyticsState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.hasError) {
          return Center(child: Text(state.errorMessage ?? 'An error occurred'));
        }

        final customerAnalytics = state.customerAnalytics;
        if (customerAnalytics == null || customerAnalytics.growthData.isEmpty) {
          return const Center(child: Text('No customer data available'));
        }

        final barGroups =
            customerAnalytics.growthData.asMap().entries.map((entry) {
          return BarChartGroupData(
            x: entry.key,
            barRods: [
              BarChartRodData(
                toY: entry.value.count.toDouble(),
                color: Theme.of(context).primaryColor,
                width: 16,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(4),
                ),
              ),
            ],
          );
        }).toList();

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Customer Growth',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                height: 300,
                child: BarChart(
                  BarChartData(
                    alignment: BarChartAlignment.spaceAround,
                    maxY: customerAnalytics.growthData
                            .map((e) => e.count)
                            .reduce((a, b) => a > b ? a : b) *
                        1.2,
                    gridData: FlGridData(
                      show: true,
                      drawVerticalLine: false,
                      horizontalInterval: 20,
                      getDrawingHorizontalLine: (value) {
                        return FlLine(
                          color: Colors.grey[300],
                          strokeWidth: 1,
                        );
                      },
                    ),
                    borderData: FlBorderData(
                      show: true,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey[300]!),
                        left: BorderSide(color: Colors.grey[300]!),
                      ),
                    ),
                    titlesData: FlTitlesData(
                      show: true,
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, meta) {
                            if (value.toInt() >=
                                customerAnalytics.growthData.length) {
                              return const SizedBox.shrink();
                            }
                            return Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                DateFormatter.formatMonth(customerAnalytics
                                    .growthData[value.toInt()].date),
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            );
                          },
                        ),
                      ),
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 40,
                          getTitlesWidget: (value, meta) {
                            return Text(
                              value.toInt().toString(),
                              style: Theme.of(context).textTheme.bodySmall,
                            );
                          },
                        ),
                      ),
                      rightTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      topTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                    ),
                    barGroups: barGroups,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCustomerMetrics() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: const [
        _MetricCard(
          title: 'Total Customers',
          value: '1,234',
          icon: Icons.people,
          trend: 15.8,
        ),
        _MetricCard(
          title: 'New Customers',
          value: '123',
          icon: Icons.person_add,
          trend: 22.4,
        ),
        _MetricCard(
          title: 'Repeat Customers',
          value: '45%',
          icon: Icons.repeat,
          trend: 5.2,
        ),
        _MetricCard(
          title: 'Average Rating',
          value: '4.5',
          icon: Icons.star,
          trend: 0.3,
        ),
      ],
    );
  }
}

class _MetricsTab extends StatelessWidget {
  const _MetricsTab();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPerformanceMetrics(),
          const SizedBox(height: 24),
          _buildEfficiencyMetrics(),
        ],
      ),
    );
  }

  Widget _buildPerformanceMetrics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Performance Overview',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            BlocBuilder<AnalyticsCubit, AnalyticsState>(
              builder: (context, state) {
                if (state.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state.hasError) {
                  return Center(
                    child: Text(
                      state.errorMessage ?? 'An error occurred',
                      style:
                          TextStyle(color: Theme.of(context).colorScheme.error),
                    ),
                  );
                }

                final performanceMetrics = state.performanceMetrics;
                if (performanceMetrics == null) {
                  return const Center(
                      child: Text('No performance data available'));
                }

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildMetricRow(
                      context,
                      'Conversion Rate',
                      '${performanceMetrics.conversionRate.toStringAsFixed(1)}%',
                      Icons.trending_up,
                    ),
                    const SizedBox(height: 16),
                    _buildMetricRow(
                      context,
                      'Return Rate',
                      '${performanceMetrics.returnRate.toStringAsFixed(1)}%',
                      Icons.assignment_return,
                    ),
                    const SizedBox(height: 16),
                    _buildMetricRow(
                      context,
                      'Avg Processing Time',
                      '${performanceMetrics.averageProcessingTime.toStringAsFixed(1)}h',
                      Icons.timer,
                    ),
                    const SizedBox(height: 16),
                    _buildMetricRow(
                      context,
                      'Avg Delivery Time',
                      '${performanceMetrics.averageDeliveryTime.toStringAsFixed(1)}h',
                      Icons.local_shipping,
                    ),
                    if (performanceMetrics.improvements.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      const Text(
                        'Suggested Improvements',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...performanceMetrics.improvements
                          .map((improvement) => Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: Row(
                                  children: [
                                    const Icon(Icons.lightbulb_outline,
                                        size: 16),
                                    const SizedBox(width: 8),
                                    Expanded(child: Text(improvement)),
                                  ],
                                ),
                              )),
                    ],
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEfficiencyMetrics() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: const [
        _MetricCard(
          title: 'Order Processing Time',
          value: '2.3h',
          icon: Icons.timer,
          trend: -12.5,
          trendColor: Colors.green,
        ),
        _MetricCard(
          title: 'Delivery Success Rate',
          value: '98.5%',
          icon: Icons.local_shipping,
          trend: 1.2,
        ),
        _MetricCard(
          title: 'Return Rate',
          value: '3.2%',
          icon: Icons.assignment_return,
          trend: -0.8,
          trendColor: Colors.green,
        ),
        _MetricCard(
          title: 'Customer Support',
          value: '4.8',
          icon: Icons.support_agent,
          trend: 0.2,
        ),
      ],
    );
  }

  Widget _buildMetricRow(
      BuildContext context, String title, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 20),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleSmall,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall,
        ),
      ],
    );
  }
}

class _MetricCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final double? trend;
  final Color? trendColor;

  const _MetricCard({
    required this.title,
    required this.value,
    required this.icon,
    this.trend,
    this.trendColor,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleSmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            if (trend != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    trend! >= 0 ? Icons.arrow_upward : Icons.arrow_downward,
                    size: 16,
                    color:
                        trendColor ?? (trend! >= 0 ? Colors.green : Colors.red),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${trend!.abs().toStringAsFixed(1)}%',
                    style: TextStyle(
                      color: trendColor ??
                          (trend! >= 0 ? Colors.green : Colors.red),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
