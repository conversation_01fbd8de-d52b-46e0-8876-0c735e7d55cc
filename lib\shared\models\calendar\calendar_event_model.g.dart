// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'calendar_event_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CalendarEventModel _$CalendarEventModelFromJson(Map<String, dynamic> json) =>
    _CalendarEventModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      type: $enumDecode(_$CalendarEventTypeEnumMap, json['type']),
      visibility: $enumDecode(
        _$CalendarEventVisibilityEnumMap,
        json['visibility'],
      ),
      status: $enumDecode(_$CalendarEventStatusEnumMap, json['status']),
      isRecurring: json['isRecurring'] as bool,
      recurrenceRule: $enumDecodeNullable(
        _$CalendarEventRecurrenceEnumMap,
        json['recurrenceRule'],
      ),
      hasReminder: json['hasReminder'] as bool,
      reminder: json['reminder'] == null
          ? null
          : CalendarEventReminder.fromJson(
              json['reminder'] as Map<String, dynamic>,
            ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      userId: json['userId'] as String?,
      products: (json['products'] as List<dynamic>?)
          ?.map((e) => EventProduct.fromJson(e as Map<String, dynamic>))
          .toList(),
      contactName: json['contactName'] as String?,
      contactPhone: json['contactPhone'] as String?,
      relationship: json['relationship'] as String?,
      sendGreeting: json['sendGreeting'] as bool?,
      customGreetingMessage: json['customGreetingMessage'] as String?,
    );

Map<String, dynamic> _$CalendarEventModelToJson(
  _CalendarEventModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate.toIso8601String(),
  'type': _$CalendarEventTypeEnumMap[instance.type]!,
  'visibility': _$CalendarEventVisibilityEnumMap[instance.visibility]!,
  'status': _$CalendarEventStatusEnumMap[instance.status]!,
  'isRecurring': instance.isRecurring,
  'recurrenceRule': _$CalendarEventRecurrenceEnumMap[instance.recurrenceRule],
  'hasReminder': instance.hasReminder,
  'reminder': instance.reminder?.toJson(),
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'userId': instance.userId,
  'products': instance.products?.map((e) => e.toJson()).toList(),
  'contactName': instance.contactName,
  'contactPhone': instance.contactPhone,
  'relationship': instance.relationship,
  'sendGreeting': instance.sendGreeting,
  'customGreetingMessage': instance.customGreetingMessage,
};

const _$CalendarEventTypeEnumMap = {
  CalendarEventType.user: 'user',
  CalendarEventType.public: 'public',
  CalendarEventType.productList: 'productList',
  CalendarEventType.birthday: 'birthday',
  CalendarEventType.anniversary: 'anniversary',
  CalendarEventType.other: 'other',
};

const _$CalendarEventVisibilityEnumMap = {
  CalendarEventVisibility.private: 'private',
  CalendarEventVisibility.public: 'public',
  CalendarEventVisibility.shared: 'shared',
};

const _$CalendarEventStatusEnumMap = {
  CalendarEventStatus.scheduled: 'scheduled',
  CalendarEventStatus.completed: 'completed',
  CalendarEventStatus.cancelled: 'cancelled',
  CalendarEventStatus.postponed: 'postponed',
};

const _$CalendarEventRecurrenceEnumMap = {
  CalendarEventRecurrence.none: 'none',
  CalendarEventRecurrence.daily: 'daily',
  CalendarEventRecurrence.weekly: 'weekly',
  CalendarEventRecurrence.monthly: 'monthly',
  CalendarEventRecurrence.yearly: 'yearly',
  CalendarEventRecurrence.custom: 'custom',
};
