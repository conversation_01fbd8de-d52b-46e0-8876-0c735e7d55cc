import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/notification/notification_model.dart';
import '../../../shared/models/notification/notification_type.dart';
import '../admin_routes.dart';

/// Widget for displaying popup notifications in the admin app
class AdminNotificationPopup extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onDismiss;
  final VoidCallback? onTap;

  const AdminNotificationPopup({
    super.key,
    required this.notification,
    this.onDismiss,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: InkWell(
          onTap: () {
            onTap?.call();
            _handleNotificationTap(context);
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getNotificationColor(notification.type),
                  child: Icon(
                    _getNotificationIcon(notification.type),
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        notification.title,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        notification.body,
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  onPressed: onDismiss,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleNotificationTap(BuildContext context) {
    switch (notification.type) {
      case NotificationType.order:
        // Navigate to analytics for order management
        context.go(AdminRoutes.analytics);
        break;
      case NotificationType.payment:
        // Navigate to commission management for payment-related issues
        context.go(AdminRoutes.commission);
        break;
      case NotificationType.booking:
        // Navigate to analytics for booking management
        context.go(AdminRoutes.analytics);
        break;
      case NotificationType.event:
        if (notification.data['eventId'] != null) {
          context.go('${AdminRoutes.events}/${notification.data['eventId']}');
        } else {
          context.go(AdminRoutes.events);
        }
        break;
      case NotificationType.verification:
        if (notification.data['requestType'] != null) {
          _handleVerificationRequest(context, notification.data['requestType']);
        } else {
          context.go(AdminRoutes.executorRequests);
        }
        break;
      case NotificationType.system:
        if (notification.data['action'] != null) {
          _handleSystemAction(context, notification.data['action']);
        } else {
          context.go(AdminRoutes.settings);
        }
        break;
      case NotificationType.chat:
        // Navigate to notifications for chat-related issues
        context.go(AdminRoutes.notifications);
        break;
      case NotificationType.deliveryRequest:
        if (notification.data['requestId'] != null) {
          context.go(
            '${AdminRoutes.deliveryPartnerRequests}/${notification.data['requestId']}',
          );
        } else {
          context.go(AdminRoutes.deliveryPartnerRequests);
        }
        break;
      case NotificationType.statusUpdate:
        context.go(AdminRoutes.analytics);
        break;
      case NotificationType.earnings:
        context.go(AdminRoutes.commission);
        break;
      default:
        // Navigate to notifications screen for other types
        context.go(AdminRoutes.notifications);
        break;
    }
  }

  void _handleVerificationRequest(BuildContext context, String requestType) {
    switch (requestType) {
      case 'executor':
        context.go(AdminRoutes.executorRequests);
        break;
      case 'seller':
        context.go(AdminRoutes.sellerRequests);
        break;
      case 'priest':
        context.go(AdminRoutes.priestRequests);
        break;
      case 'technician':
        context.go(AdminRoutes.technicianRequests);
        break;
      case 'delivery_partner':
        context.go(AdminRoutes.deliveryPartnerRequests);
        break;
      default:
        context.go(AdminRoutes.executorRequests);
        break;
    }
  }

  void _handleSystemAction(BuildContext context, String action) {
    switch (action) {
      case 'server_status':
        context.go(AdminRoutes.infrastructure);
        break;
      case 'api_keys':
        context.go(AdminRoutes.apiKeys);
        break;
      case 'ai_settings':
        context.go(AdminRoutes.aiSettings);
        break;
      case 'refund_request':
        context.go(AdminRoutes.refunds);
        break;
      default:
        context.go(AdminRoutes.settings);
        break;
    }
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.order:
        return Icons.shopping_cart;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.system:
        return Icons.info;
      case NotificationType.booking:
        return Icons.calendar_today;
      case NotificationType.event:
        return Icons.event;
      case NotificationType.chat:
        return Icons.chat;
      case NotificationType.verification:
        return Icons.verified_user;
      case NotificationType.general:
        return Icons.notifications;
      case NotificationType.deliveryRequest:
        return Icons.local_shipping;
      case NotificationType.statusUpdate:
        return Icons.update;
      case NotificationType.earnings:
        return Icons.monetization_on;
      default:
        return Icons.notifications;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.order:
        return Colors.blue;
      case NotificationType.payment:
        return Colors.green;
      case NotificationType.system:
        return Colors.red;
      case NotificationType.booking:
        return Colors.orange;
      case NotificationType.event:
        return Colors.purple;
      case NotificationType.chat:
        return Colors.teal;
      case NotificationType.verification:
        return Colors.indigo;
      case NotificationType.general:
        return Colors.grey;
      case NotificationType.deliveryRequest:
        return Colors.brown;
      case NotificationType.statusUpdate:
        return Colors.cyan;
      case NotificationType.earnings:
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }
}

/// Service for showing notification popups
class AdminNotificationPopupService {
  static OverlayEntry? _currentOverlay;

  /// Show a notification popup
  static void showNotificationPopup(
    BuildContext context,
    NotificationModel notification, {
    Duration duration = const Duration(seconds: 4),
  }) {
    // Remove any existing popup
    hideNotificationPopup();

    final overlay = Overlay.of(context);

    _currentOverlay = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 16,
        left: 0,
        right: 0,
        child: AdminNotificationPopup(
          notification: notification,
          onDismiss: hideNotificationPopup,
          onTap: hideNotificationPopup,
        ),
      ),
    );

    overlay.insert(_currentOverlay!);

    // Auto-hide after duration
    Future.delayed(duration, () {
      hideNotificationPopup();
    });
  }

  /// Hide the current notification popup
  static void hideNotificationPopup() {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }
}
