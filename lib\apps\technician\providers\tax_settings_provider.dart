import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final taxSettingsProvider =
    AsyncNotifierProvider<TaxSettingsNotifier, Map<String, dynamic>>(() {
  return TaxSettingsNotifier();
});

class TaxSettingsNotifier extends AsyncNotifier<Map<String, dynamic>> {
  late final DatabaseService _databaseService;

  @override
  Future<Map<String, dynamic>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadTaxSettings();
  }

  Future<Map<String, dynamic>> _loadTaxSettings() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('users', userId);
      return userData?['tax_settings'] as Map<String, dynamic>? ??
          {
            'gst_number': '',
            'pan_number': '',
            'tax_rate': 0.0,
            'tax_type': 'GST',
            'include_tax_in_price': false,
          };
    } catch (e) {
      debugPrint('Failed to load tax settings: $e');
      throw Exception('Failed to load tax settings: $e');
    }
  }

  Future<void> updateTaxSettings(Map<String, dynamic> settings) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Validate tax settings
      final taxRate = settings['tax_rate'] as double? ?? 0.0;
      if (taxRate < 0 || taxRate > 100) {
        throw Exception('Tax rate must be between 0 and 100');
      }

      await _databaseService.update('users', userId, {
        'tax_settings': settings,
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData(settings);
    } catch (e) {
      debugPrint('Failed to update tax settings: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}
