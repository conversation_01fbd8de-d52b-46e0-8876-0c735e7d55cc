import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/apps/admin/models/user.dart';

part 'user_management_state.freezed.dart';

@freezed
sealed class UserManagementState with _$UserManagementState {
  const factory UserManagementState.initial() = Initial;
  const factory UserManagementState.loading() = Loading;
  const factory UserManagementState.loaded(List<User> users) = Loaded;
  const factory UserManagementState.error(String message) = Error;
}
