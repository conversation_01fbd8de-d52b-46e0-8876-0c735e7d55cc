import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/screens/language_selection_screen.dart';
import 'package:shivish/shared/core/navigation/shells/seller_shell.dart';
import 'package:shivish/shared/services/app_initialization_service.dart';
import 'package:shivish/apps/seller/application/seller_provider.dart';
import 'package:shivish/shared/screens/banner_submission_screen.dart';
import 'package:shivish/shared/providers/auth_provider.dart' as auth;
import 'services/seller_agreement_service.dart';

// Auth Screens
import 'screens/login_screen.dart';
import 'screens/register_screen.dart';
import 'screens/agreement_screen.dart';

// Main Screens
import 'screens/home_screen.dart';
import 'screens/notifications_screen.dart';
import 'screens/settings_screen.dart';

// Profile Screens
import 'screens/profile/profile_screen.dart';
import 'screens/profile/business_profile_setup_screen.dart';
import 'screens/profile/business_information_screen.dart';

// Payment Screens
import 'screens/payments/payment_settings_screen.dart';
import 'screens/payments/bank_account_settings_screen.dart';

// Settings Screens
import 'screens/settings/theme_settings_screen.dart';
import 'screens/settings/language_settings_screen.dart';
import 'screens/settings/privacy_policy_screen.dart';
import 'screens/settings/terms_of_service_screen.dart';

// Product Screens
import 'screens/products/product_list_screen.dart';
import 'screens/products/product_details_screen.dart';
import 'screens/products/add_product_screen.dart';
import 'screens/products/edit_product_screen.dart';
import 'screens/products/bulk_upload_screen.dart';
import 'screens/products/product_approval_status_screen.dart';
import 'screens/products/recent_products_screen.dart';

// Order Screens
import 'screens/orders/orders_screen.dart';
import 'screens/orders/order_details_screen.dart';
import 'screens/orders/order_processing_screen.dart';
import 'screens/orders/order_list_screen.dart';
import 'screens/orders/recent_orders_screen.dart';
import 'screens/orders/returns_screen.dart';

// Analytics Screens
import 'screens/analytics/analytics_dashboard_screen.dart';
import 'screens/analytics/store_performance_screen.dart';
import 'screens/analytics/customer_analytics_screen.dart';

// New screens
import 'screens/pending_approval_screen.dart';

class SellerRoutes {
  static const String initialLocation = '/seller/language-selection';

  // Auth Routes
  static const String login = '/seller/login';
  static const String register = '/seller/register';
  static const String languageSelection = '/seller/language-selection';
  static const String agreement = '/seller/agreement';

  // Main Routes
  static const String home = '/seller/home';
  static const String notifications = '/seller/notifications';
  static const String settings = '/seller/settings';

  // Profile Routes
  static const String profile = '/seller/profile';
  static const String businessProfileSetup = '/seller/profile/business-setup';
  static const String businessInfo = '/seller/business-info';

  // Payment Routes
  static const String paymentSettings = '/seller/payment-settings';
  static const String bankDetails = '/seller/bank-details';

  // Settings Routes
  static const String themeSettings = '/seller/settings/theme';
  static const String languageSettings = '/seller/settings/language';
  static const String privacyPolicy = '/seller/settings/privacy-policy';
  static const String termsOfService = '/seller/settings/terms-of-service';

  // Product Routes
  static const String products = '/seller/products';
  static const String productDetails = '/seller/products/:id';
  static const String addProduct = '/seller/products/add';
  static const String editProduct = '/seller/products/:id/edit';
  static const String bulkUpload = '/seller/products/bulk-upload';
  static const String productApproval = '/seller/products/approval';
  static const String recentProducts = '/seller/products/recent';

  // Order Routes
  static const String orders = '/seller/orders';
  static const String orderDetails = '/seller/orders/:id';
  static const String orderProcessing = '/seller/orders/processing';
  static const String orderList = '/seller/orders/list';
  static const String recentOrders = '/seller/orders/recent';
  static const String returns = '/seller/orders/returns';

  // Event Service Routes
  static const String eventServicePackages = '/seller/event-services/packages';
  static const String eventServiceBookings = '/seller/event-services/bookings';
  static const String eventServiceCalendar = '/seller/event-services/calendar';

  // Analytics Routes
  static const String analyticsDashboard = '/seller/analytics/dashboard';
  static const String storePerformance = '/seller/analytics/store-performance';
  static const String customerAnalytics = '/seller/analytics/customers';

  // Payment Routes
  static const String payments = '/seller/payments';

  // Store Routes
  static const String store = '/seller/store';

  // New route
  static const String pendingApproval = '/pending-approval';

  static final router = GoRouter(
    initialLocation: initialLocation,
    // Enable history state restoration for proper back button handling
    restorationScopeId: 'seller_app',
    redirect: (context, state) async {
      debugPrint('Redirect called for path: ${state.matchedLocation}');

      // Define route checks
      final isLanguageSelectionRoute =
          state.matchedLocation == languageSelection;
      final isLoginRoute = state.matchedLocation == login;
      final isRegisterRoute = state.matchedLocation == register;
      final isAgreementRoute = state.matchedLocation == agreement;
      final isAuthRoute = isLoginRoute || isRegisterRoute || isAgreementRoute;
      final isHomeRoute = state.matchedLocation == home;

      // Get auth state
      final container = ProviderContainer();
      final authService = container.read(auth.authServiceProvider);
      final isAuthenticated = authService.currentUser != null;
      debugPrint('Is authenticated: $isAuthenticated');

      // If we're authenticated and on the home route, ensure seller data is loaded
      if (isAuthenticated && isHomeRoute) {
        final userId = authService.currentUser?.id;
        if (userId != null) {
          final sellerState = container.read(sellerProvider);
          if (!sellerState.isLoading && sellerState.value == null) {
            // Load seller data if not already loaded
            await container.read(sellerProvider.notifier).getSeller(userId);
          }
        }
        debugPrint('On home route and authenticated, no redirect');
        return null;
      }

      // Don't redirect if we're on the language selection screen
      if (isLanguageSelectionRoute) {
        debugPrint('On language selection screen, no redirect');
        return null;
      }

      // Check if language has been selected
      final languageSelected =
          await AppInitializationService.hasLanguageBeenSelected();
      if (!languageSelected && !isLanguageSelectionRoute) {
        debugPrint('Language not selected, redirecting to language selection');
        return languageSelection;
      }

      // Check if confirmation has been completed - using shared confirmation service
      final hasConfirmed =
          await AppInitializationService.hasConfirmationBeenGiven();
      debugPrint('Has confirmed: $hasConfirmed');

      // If confirmation is not completed, redirect to language selection
      if (!hasConfirmed && !isLanguageSelectionRoute) {
        debugPrint('Not confirmed, redirecting to language selection');
        return languageSelection;
      }

      // Check if the user has accepted the legal agreement
      // We only check this for non-auth routes to avoid redirect loops
      if (!isAgreementRoute && !isAuthRoute && !isHomeRoute) {
        final hasAcceptedAgreement =
            await SellerAgreementService.hasAcceptedAgreement();
        debugPrint('Has accepted agreement: $hasAcceptedAgreement');

        // If the user hasn't accepted the agreement and is not on the agreement screen,
        // redirect to the agreement screen
        if (!hasAcceptedAgreement) {
          debugPrint('Not accepted agreement, redirecting to agreement screen');
          return agreement;
        }
      }

      // Handle authentication redirects
      if (!isAuthenticated && !isAuthRoute && !isAgreementRoute) {
        debugPrint(
          'Not authenticated and not on auth route, redirecting to login',
        );
        return login;
      }

      if (isAuthenticated && isAuthRoute) {
        debugPrint('Authenticated and on auth route, redirecting to home');
        return home;
      }

      // Check for pending approval
      final seller = container.read(sellerProvider).value;
      if (seller != null &&
          !seller.isApproved &&
          state.uri.path != pendingApproval) {
        debugPrint('Seller not approved, redirecting to pending approval');
        return pendingApproval;
      }

      if (seller != null &&
          seller.isApproved &&
          state.uri.path == pendingApproval) {
        debugPrint('Seller approved, redirecting to home');
        return home;
      }

      debugPrint('No redirect needed');
      return null;
    },
    routes: getRoutes(),
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Text(
          'Error: ${state.error}',
          style: Theme.of(context).textTheme.titleLarge,
        ),
      ),
    ),
  );

  // Navigation Methods
  static void navigateToLogin(BuildContext context) => context.go(login);
  static void navigateToRegister(BuildContext context) => context.go(register);
  static void navigateToAgreement(BuildContext context) =>
      context.go(agreement);
  static void navigateToHome(BuildContext context) => context.go(home);
  static void navigateToNotifications(BuildContext context) =>
      context.go(notifications);
  static void navigateToSettings(BuildContext context) => context.go(settings);
  static void navigateToProfile(BuildContext context) =>
      context.goNamed('seller_profile');
  static void navigateToBusinessProfileSetup(BuildContext context) =>
      context.go(businessProfileSetup);
  static void navigateToProducts(BuildContext context) => context.go(products);
  static void navigateToProductDetails(
    BuildContext context,
    String productId,
  ) => context.go(productDetails.replaceAll(':id', productId));
  static void navigateToAddProduct(BuildContext context) =>
      context.go(addProduct);
  static void navigateToEditProduct(BuildContext context, String productId) =>
      context.go(editProduct.replaceAll(':id', productId));
  static void navigateToBulkUpload(BuildContext context) =>
      context.go(bulkUpload);
  static void navigateToProductApproval(BuildContext context) =>
      context.go(productApproval);
  static void navigateToRecentProducts(BuildContext context) =>
      context.go(recentProducts);
  static void navigateToOrders(BuildContext context) => context.go(orders);
  static void navigateToOrderDetails(BuildContext context, String orderId) =>
      context.go(orderDetails.replaceAll(':id', orderId));
  static void navigateToOrderProcessing(BuildContext context) =>
      context.go(orderProcessing);
  static void navigateToOrderList(BuildContext context) =>
      context.go(orderList);
  static void navigateToRecentOrders(BuildContext context) =>
      context.go(recentOrders);
  static void navigateToReturns(BuildContext context) => context.go(returns);
  static void navigateToAnalyticsDashboard(BuildContext context) =>
      context.go(analyticsDashboard);
  static void navigateToStorePerformance(BuildContext context) =>
      context.go(storePerformance);
  static void navigateToCustomerAnalytics(BuildContext context) =>
      context.go(customerAnalytics);
  static void navigateToPendingApproval(BuildContext context) {
    context.go(pendingApproval);
  }

  // Get routes for centralized router
  static List<RouteBase> getRoutes() {
    return [
      // Banner submission route
      GoRoute(
        path: '/banner-submission',
        builder: (context, state) {
          debugPrint('Seller routes: Building BannerSubmissionScreen');
          return const BannerSubmissionScreen();
        },
      ),

      // Seller-specific routes
      // Language selection screen - must be first
      GoRoute(
        path: languageSelection,
        name: 'language_selection',
        builder: (context, state) {
          debugPrint('Seller routes: Building LanguageSelectionScreen');
          return LanguageSelectionScreen(
            onComplete: (confirmed) async {
              debugPrint(
                'Seller routes: Language selection onComplete called with: $confirmed',
              );
              if (confirmed) {
                debugPrint(
                  'Seller routes: Language selection completed, navigating to agreement screen',
                );

                // Navigate directly to agreement screen without showing confirmation dialog again
                try {
                  // Add a small delay before navigation to ensure the UI is ready
                  await Future.delayed(const Duration(milliseconds: 300));

                  if (context.mounted) {
                    // Use direct context.go instead of the helper method
                    context.go(agreement);
                    debugPrint(
                      'Seller routes: Direct navigation to agreement screen initiated',
                    );
                  } else {
                    debugPrint(
                      'Seller routes: Context is no longer mounted after delay',
                    );
                  }
                } catch (e) {
                  debugPrint(
                    'Seller routes: Error navigating to agreement screen: $e',
                  );
                }
              }
            },
          );
        },
      ),

      // Auth Routes - must be before ShellRoute
      GoRoute(
        path: agreement,
        name: 'agreement',
        builder: (context, state) {
          debugPrint('Seller routes: Building AgreementScreen');
          return const AgreementScreen();
        },
      ),
      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) {
          debugPrint('Seller routes: Building LoginScreen');
          return const LoginScreen();
        },
      ),
      GoRoute(
        path: register,
        name: 'register',
        builder: (context, state) {
          debugPrint('Seller routes: Building RegisterScreen');
          return RegisterScreen(
            onRegisterSuccess: () {
              debugPrint(
                'Seller routes: Registration successful, navigating to pending approval',
              );
              context.go(pendingApproval);
            },
          );
        },
      ),
      GoRoute(
        path: pendingApproval,
        name: 'pending_approval',
        builder: (context, state) {
          debugPrint('Seller routes: Building PendingApprovalScreen');
          return const PendingApprovalScreen();
        },
      ),

      // Other standalone routes that should be outside the shell
      GoRoute(
        path: notifications,
        name: 'notifications',
        builder: (context, state) {
          debugPrint('Seller routes: Building NotificationsScreen');
          return const NotificationsScreen();
        },
      ),
      GoRoute(
        path: settings,
        name: 'settings',
        builder: (context, state) {
          debugPrint('Seller routes: Building SettingsScreen');
          return const SettingsScreen();
        },
      ),

      // Profile routes
      GoRoute(
        path: profile,
        name: 'seller_profile',
        builder: (context, state) {
          debugPrint('Seller routes: Building ProfileScreen');
          return const SellerProfileScreen();
        },
      ),
      GoRoute(
        path: businessInfo,
        name: 'business_info',
        builder: (context, state) {
          debugPrint('Seller routes: Building BusinessInformationScreen');
          return const BusinessInformationScreen();
        },
      ),

      // Payment routes
      GoRoute(
        path: paymentSettings,
        name: 'payment_settings',
        builder: (context, state) {
          debugPrint('Seller routes: Building PaymentSettingsScreen');
          return const PaymentSettingsScreen();
        },
      ),
      GoRoute(
        path: bankDetails,
        name: 'bank_details',
        builder: (context, state) {
          debugPrint('Seller routes: Building BankAccountSettingsScreen');
          return const BankAccountSettingsScreen();
        },
      ),

      // Settings routes
      GoRoute(
        path: themeSettings,
        name: 'theme_settings',
        builder: (context, state) {
          debugPrint('Seller routes: Building ThemeSettingsScreen');
          return const ThemeSettingsScreen();
        },
      ),
      GoRoute(
        path: languageSettings,
        name: 'language_settings',
        builder: (context, state) {
          debugPrint('Seller routes: Building LanguageSettingsScreen');
          return const LanguageSettingsScreen();
        },
      ),
      GoRoute(
        path: privacyPolicy,
        name: 'privacy_policy',
        builder: (context, state) {
          debugPrint('Seller routes: Building PrivacyPolicyScreen');
          return const PrivacyPolicyScreen();
        },
      ),
      GoRoute(
        path: termsOfService,
        name: 'terms_of_service',
        builder: (context, state) {
          debugPrint('Seller routes: Building TermsOfServiceScreen');
          return const TermsOfServiceScreen();
        },
      ),

      // Analytics Routes (outside of main tab)
      GoRoute(
        path: storePerformance,
        name: 'store_performance',
        builder: (context, state) {
          debugPrint('Seller routes: Building StorePerformanceScreen');
          return const StorePerformanceScreen();
        },
      ),
      GoRoute(
        path: customerAnalytics,
        name: 'customer_analytics',
        builder: (context, state) {
          debugPrint('Seller routes: Building CustomerAnalyticsScreen');
          return const CustomerAnalyticsScreen();
        },
      ),

      // ShellRoute for main navigation with bottom nav bar - must be last
      ShellRoute(
        builder: (context, state, child) {
          debugPrint('Seller routes: Building SellerShell');
          return SellerShell(child: child);
        },
        routes: [
          // Main tab routes
          GoRoute(
            path: home,
            name: 'home',
            builder: (context, state) {
              debugPrint('Seller routes: Building HomeScreen');
              return const HomeScreen();
            },
          ),
          GoRoute(
            path: products,
            name: 'products',
            builder: (context, state) {
              debugPrint('Seller routes: Building ProductListScreen');
              return const ProductListScreen();
            },
          ),
          GoRoute(
            path: orders,
            name: 'orders',
            builder: (context, state) {
              debugPrint('Seller routes: Building OrdersScreen');
              return const OrdersScreen();
            },
          ),
          GoRoute(
            path: analyticsDashboard,
            name: 'analytics_dashboard',
            builder: (context, state) {
              debugPrint('Seller routes: Building AnalyticsDashboardScreen');
              return const AnalyticsDashboardScreen();
            },
          ),
          GoRoute(
            path: profile,
            name: 'profile',
            builder: (context, state) {
              debugPrint('Seller routes: Building SellerProfileScreen');
              return const SellerProfileScreen();
            },
          ),

          // Product related routes
          GoRoute(
            path: productDetails,
            name: 'product_details',
            builder: (context, state) {
              debugPrint('Seller routes: Building ProductDetailsScreen');
              return ProductDetailsScreen(
                productId: state.pathParameters['id']!,
              );
            },
          ),
          GoRoute(
            path: addProduct,
            name: 'add_product',
            builder: (context, state) {
              debugPrint('Seller routes: Building AddProductScreen');
              return const AddProductScreen();
            },
          ),
          GoRoute(
            path: editProduct,
            name: 'edit_product',
            builder: (context, state) {
              debugPrint('Seller routes: Building EditProductScreen');
              return EditProductScreen(productId: state.pathParameters['id']!);
            },
          ),
          GoRoute(
            path: bulkUpload,
            name: 'bulk_upload',
            builder: (context, state) {
              debugPrint('Seller routes: Building BulkUploadScreen');
              return const BulkUploadScreen();
            },
          ),
          GoRoute(
            path: productApproval,
            name: 'product_approval',
            builder: (context, state) {
              debugPrint('Seller routes: Building ProductApprovalStatusScreen');
              return const ProductApprovalStatusScreen();
            },
          ),
          GoRoute(
            path: recentProducts,
            name: 'recent_products',
            builder: (context, state) {
              debugPrint('Seller routes: Building RecentProductsScreen');
              return const RecentProductsScreen();
            },
          ),

          // Order related routes
          GoRoute(
            path: orderDetails,
            name: 'order_details',
            builder: (context, state) {
              debugPrint('Seller routes: Building OrderDetailsScreen');
              return OrderDetailsScreen(orderId: state.pathParameters['id']!);
            },
          ),
          GoRoute(
            path: orderProcessing,
            name: 'order_processing',
            builder: (context, state) {
              debugPrint('Seller routes: Building OrderProcessingScreen');
              return const OrderProcessingScreen();
            },
          ),
          GoRoute(
            path: orderList,
            name: 'order_list',
            builder: (context, state) {
              debugPrint('Seller routes: Building OrderListScreen');
              return const OrderListScreen();
            },
          ),
          GoRoute(
            path: recentOrders,
            name: 'recent_orders',
            builder: (context, state) {
              debugPrint('Seller routes: Building RecentOrdersScreen');
              return const RecentOrdersScreen();
            },
          ),
          GoRoute(
            path: returns,
            name: 'returns',
            builder: (context, state) {
              debugPrint('Seller routes: Building ReturnsScreen');
              return const ReturnsScreen();
            },
          ),

          // Profile related routes
          GoRoute(
            path: businessProfileSetup,
            name: 'business_profile_setup',
            builder: (context, state) {
              debugPrint('Seller routes: Building BusinessProfileSetupScreen');
              return const BusinessProfileSetupScreen();
            },
          ),

          // Payment related routes
          GoRoute(
            path: paymentSettings,
            name: 'shell_payment_settings',
            builder: (context, state) {
              debugPrint(
                'Seller routes: Building PaymentSettingsScreen (Shell)',
              );
              return const PaymentSettingsScreen();
            },
          ),
        ],
      ),
    ];
  }
}
