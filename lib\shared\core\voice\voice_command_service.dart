import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter/foundation.dart';
import '../analytics/analytics_service.dart';

class VoiceCommandService {
  final stt.SpeechToText _speech = stt.SpeechToText();
  final AnalyticsService _analytics;
  bool _isInitialized = false;

  VoiceCommandService(this._analytics);

  Future<bool> initialize() async {
    if (!_isInitialized) {
      _isInitialized = await _speech.initialize(
        onError: (error) => _handleError(error.errorMsg),
        debugLogging: kDebugMode,
      );
    }
    return _isInitialized;
  }

  Future<void> startListening({
    required Function(String) onResult,
    String? locale,
  }) async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) {
        _handleError('Failed to initialize speech recognition');
        return;
      }
    }

    try {
      await _speech.listen(
        onResult: (result) {
          final recognizedWords = result.recognizedWords;
          if (recognizedWords.isNotEmpty) {
            onResult(recognizedWords);
            _logVoiceCommand(recognizedWords);
          }
        },
        localeId: locale,
        listenOptions: stt.SpeechListenOptions(
          listenMode: stt.ListenMode.confirmation,
          cancelOnError: true,
          partialResults: false,
        ),
      );
    } catch (e) {
      _handleError('Error starting voice recognition: $e');
    }
  }

  Future<void> stopListening() async {
    try {
      await _speech.stop();
    } catch (e) {
      _handleError('Error stopping voice recognition: $e');
    }
  }

  bool get isListening => _speech.isListening;

  Future<bool> get hasPermission async {
    if (!_isInitialized) {
      return false;
    }
    return _speech.hasPermission;
  }

  Future<List<String>> get locales async {
    final localesList = await _speech.locales();
    return localesList.map((locale) => locale.localeId).toList();
  }

  void _handleError(String error) {
    debugPrint('VoiceCommandService Error: $error');
    _analytics.logError(
      error: error,
      parameters: {'service': 'voice_command'},
    );
  }

  void _logVoiceCommand(String command) {
    _analytics.logEvent(
      name: 'voice_command',
      parameters: {
        'command': command,
        'locale': _speech.lastRecognizedWords,
      },
    );
  }

  void dispose() {
    _speech.cancel();
  }
}
