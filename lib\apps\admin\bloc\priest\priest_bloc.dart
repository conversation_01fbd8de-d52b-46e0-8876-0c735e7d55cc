import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import '../../../../shared/models/priest.dart';
import '../../../../shared/services/priest_service.dart';
import '../../../../shared/utils/logger.dart';

part 'priest_event.dart';
part 'priest_state.dart';
part 'priest_bloc.freezed.dart';

@injectable
class PriestBloc extends Bloc<PriestEvent, PriestState> {
  final PriestService _priestService;
  final _logger = getLogger('AdminPriestBloc');
  int _currentOffset = 0;
  static const int _pageSize = 20;

  PriestBloc(this._priestService) : super(const PriestState.initial()) {
    on<PriestEvent>((event, emit) async {
      await event.map(
        loadPriests: (e) => _onLoadPriests(e, emit),
        loadMorePriests: (e) => _onLoadMorePriests(e, emit),
        updatePriestStatus: (e) => _onUpdatePriestStatus(e, emit),
        updatePriestVerification: (e) => _onUpdatePriestVerification(e, emit),
        deletePriest: (e) => _onDeletePriest(e, emit),
        startRealtimeUpdates: (e) => _onStartRealtimeUpdates(e, emit),
        stopRealtimeUpdates: (e) => _onStopRealtimeUpdates(e, emit),
      );
    });
  }

  Future<void> _onLoadPriests(
      _LoadPriests event, Emitter<PriestState> emit) async {
    try {
      _logger.info('Loading priests');
      emit(const PriestState.loading());
      _currentOffset = 0; // Reset offset for fresh load
      final priests = await _priestService.getPriests(limit: _pageSize);
      _currentOffset = priests.length;
      _logger.info('Successfully loaded ${priests.length} priests');
      emit(PriestState.loaded(priests));
    } catch (e) {
      _logger.severe('Failed to load priests: $e');
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onLoadMorePriests(
      _LoadMorePriests event, Emitter<PriestState> emit) async {
    try {
      final currentState = state;
      if (currentState is _Loaded) {
        _logger.info('Loading more priests from offset: $_currentOffset');

        // For now, we'll reload all priests with a larger limit
        // This is not optimal but works until we add offset support to PriestService
        final allPriests = await _priestService.getPriests(
          limit: _currentOffset + _pageSize,
        );

        if (allPriests.length > currentState.priests.length) {
          _currentOffset = allPriests.length;
          _logger.info('Successfully loaded ${allPriests.length} total priests');
          emit(PriestState.loaded(allPriests));
        } else {
          _logger.info('No more priests to load');
        }
      }
    } catch (e) {
      _logger.severe('Failed to load more priests: $e');
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onUpdatePriestStatus(
      _UpdatePriestStatus event, Emitter<PriestState> emit) async {
    try {
      final currentState = state;
      if (currentState is _Loaded) {
        _logger.info('Updating priest status: ${event.id} -> active: ${event.isActive}');
        await _priestService.updatePriestStatus(
          id: event.id,
          isActive: event.isActive,
        );
        final updatedPriests = currentState.priests.map((priest) {
          if (priest.id == event.id) {
            return priest.copyWith(isActive: event.isActive);
          }
          return priest;
        }).toList();
        _logger.info('Successfully updated priest status: ${event.id}');
        emit(PriestState.loaded(updatedPriests));
      }
    } catch (e) {
      _logger.severe('Failed to update priest status: $e');
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onUpdatePriestVerification(
      _UpdatePriestVerification event, Emitter<PriestState> emit) async {
    try {
      final currentState = state;
      if (currentState is _Loaded) {
        _logger.info('Updating priest verification: ${event.id} -> verified: ${event.isVerified}');
        await _priestService.updatePriestVerification(
          id: event.id,
          isVerified: event.isVerified,
          verificationStatus: event.verificationStatus,
          verificationNotes: event.verificationNotes ?? '',
        );
        final updatedPriests = currentState.priests.map((priest) {
          if (priest.id == event.id) {
            return priest.copyWith(
              isVerified: event.isVerified,
              verificationStatus: event.verificationStatus,
              verificationNotes: event.verificationNotes ?? '',
            );
          }
          return priest;
        }).toList();
        _logger.info('Successfully updated priest verification: ${event.id}');
        emit(PriestState.loaded(updatedPriests));
      }
    } catch (e) {
      _logger.severe('Failed to update priest verification: $e');
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onDeletePriest(
      _DeletePriest event, Emitter<PriestState> emit) async {
    try {
      final currentState = state;
      if (currentState is _Loaded) {
        _logger.info('Deleting priest: ${event.id}');
        await _priestService.deletePriest(id: event.id);
        final updatedPriests = currentState.priests
            .where((priest) => priest.id != event.id)
            .toList();
        _logger.info('Successfully deleted priest: ${event.id}');
        emit(PriestState.loaded(updatedPriests));
      }
    } catch (e) {
      _logger.severe('Failed to delete priest: $e');
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onStartRealtimeUpdates(
      _StartRealtimeUpdates event, Emitter<PriestState> emit) async {
    try {
      _logger.info('Starting realtime updates for priests');
      await _priestService.startRealtimeUpdates((priests) {
        _logger.info('Received realtime update: ${priests.length} priests');
        emit(PriestState.loaded(priests));
      });
    } catch (e) {
      _logger.severe('Failed to start realtime updates: $e');
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onStopRealtimeUpdates(
      _StopRealtimeUpdates event, Emitter<PriestState> emit) async {
    try {
      _logger.info('Stopping realtime updates for priests');
      await _priestService.stopRealtimeUpdates();
    } catch (e) {
      _logger.severe('Failed to stop realtime updates: $e');
      emit(PriestState.error(e.toString()));
    }
  }
}
