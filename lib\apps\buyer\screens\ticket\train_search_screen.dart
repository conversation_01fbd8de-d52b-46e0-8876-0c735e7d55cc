import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../models/ticket_booking_models.dart';
import '../../providers/ticket_booking_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import 'train_passenger_details_screen.dart';

class TrainSearchScreen extends ConsumerStatefulWidget {
  final TrainSearchRequest searchRequest;

  const TrainSearchScreen({super.key, required this.searchRequest});

  @override
  ConsumerState<TrainSearchScreen> createState() => _TrainSearchScreenState();
}

class _TrainSearchScreenState extends ConsumerState<TrainSearchScreen> {
  String _timeFilter = 'all'; // all, morning, afternoon, evening
  String _trainTypeFilter = 'all'; // all, express, superfast, local
  String _sortBy = 'departure'; // departure, duration, price

  @override
  void initState() {
    super.initState();
    // Trigger search when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(trainSearchProvider.notifier).searchTrains(widget.searchRequest);
    });
  }

  @override
  Widget build(BuildContext context) {
    final searchState = ref.watch(trainSearchProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '${widget.searchRequest.origin} → ${widget.searchRequest.destination}',
        ),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search summary card
          _buildSearchSummary(),

          // Search results
          Expanded(
            child: searchState.when(
              data: (response) {
                if (response == null) {
                  return const Center(child: Text('No search performed yet'));
                }

                if (response.trains.isEmpty) {
                  return _buildNoResultsView();
                }

                return _buildTrainsList(response.trains);
              },
              loading: () => const LoadingIndicator(),
              error: (error, stack) => ErrorView(
                message: 'Failed to search trains: $error',
                onRetry: () => ref
                    .read(trainSearchProvider.notifier)
                    .searchTrains(widget.searchRequest),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSummary() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.train, color: Colors.purple.shade700),
              const SizedBox(width: 8),
              Text(
                '${widget.searchRequest.origin} → ${widget.searchRequest.destination}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16,
                color: Colors.purple.shade600,
              ),
              const SizedBox(width: 4),
              Text(
                _formatDate(widget.searchRequest.journeyDate),
                style: TextStyle(color: Colors.purple.shade600),
              ),
              const SizedBox(width: 16),
              Icon(
                Icons.airline_seat_recline_normal,
                size: 16,
                color: Colors.purple.shade600,
              ),
              const SizedBox(width: 4),
              Text(
                widget.searchRequest.classType,
                style: TextStyle(color: Colors.purple.shade600),
              ),
              const Spacer(),
              Text(
                '${widget.searchRequest.passengers} Passenger${widget.searchRequest.passengers > 1 ? 's' : ''}',
                style: TextStyle(color: Colors.purple.shade600),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTrainsList(List<Train> trains) {
    final filteredTrains = _filterAndSortTrains(trains);

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredTrains.length,
      itemBuilder: (context, index) {
        final train = filteredTrains[index];
        return _buildTrainCard(train);
      },
    );
  }

  Widget _buildTrainCard(Train train) {
    final selectedClassPrice =
        train.classPrices[widget.searchRequest.classType] ?? 0.0;
    final selectedClassSeats =
        train.availableSeats[widget.searchRequest.classType] ?? 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _selectTrain(train),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Train name and number
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.purple.shade100,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      train.trainNumber,
                      style: TextStyle(
                        color: Colors.purple.shade700,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      train.trainName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Text(
                    'INR ${selectedClassPrice.toStringAsFixed(0)}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Journey times and route
              Row(
                children: [
                  // Departure
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _formatTime(train.departureTime),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        train.origin,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(width: 16),

                  // Journey path
                  Expanded(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.purple,
                                shape: BoxShape.circle,
                              ),
                            ),
                            Expanded(
                              child: Container(height: 2, color: Colors.purple),
                            ),
                            const Icon(
                              Icons.train,
                              color: Colors.purple,
                              size: 16,
                            ),
                            Expanded(
                              child: Container(height: 2, color: Colors.purple),
                            ),
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.purple,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          train.duration,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Arrival
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        _formatTime(train.arrivalTime),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        train.destination,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Class availability
              Row(
                children: [
                  Icon(
                    Icons.airline_seat_recline_normal,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${widget.searchRequest.classType}: $selectedClassSeats seats',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                  const Spacer(),
                  _buildAvailabilityStatus(selectedClassSeats),
                ],
              ),

              const SizedBox(height: 8),

              // Other class options
              if (train.classPrices.length > 1) ...[
                const Divider(height: 16),
                Text(
                  'Other Classes:',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                Wrap(
                  spacing: 8,
                  children: train.classPrices.entries
                      .where(
                        (entry) => entry.key != widget.searchRequest.classType,
                      )
                      .map(
                        (entry) => Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.purple.shade50,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: Colors.purple.shade200),
                          ),
                          child: Text(
                            '${entry.key}: INR ${entry.value.toStringAsFixed(0)}',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.purple.shade700,
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvailabilityStatus(int seats) {
    Color color;
    String text;

    if (seats > 50) {
      color = Colors.green;
      text = 'Available';
    } else if (seats > 10) {
      color = Colors.orange;
      text = 'Limited';
    } else if (seats > 0) {
      color = Colors.red;
      text = 'Few Left';
    } else {
      color = Colors.grey;
      text = 'Waitlist';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildNoResultsView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.train, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'No trains found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try searching for a different date or route',
            style: TextStyle(color: Colors.grey.shade500),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Modify Search'),
          ),
        ],
      ),
    );
  }

  void _selectTrain(Train train) {
    // Navigate to booking screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(train.trainName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Train: ${train.trainNumber}'),
            Text('Class: ${widget.searchRequest.classType}'),
            Text(
              'Price: INR ${train.classPrices[widget.searchRequest.classType]?.toStringAsFixed(0)}',
            ),
            Text(
              'Available Seats: ${train.availableSeats[widget.searchRequest.classType]}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to passenger details screen
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => TrainPassengerDetailsScreen(
                    train: train,
                    searchRequest: widget.searchRequest,
                  ),
                ),
              );
            },
            child: const Text('Book Now'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Trains'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Departure Time'),
              subtitle: const Text('Morning, Afternoon, Evening'),
              trailing: _timeFilter != 'all'
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                Navigator.of(context).pop();
                _showTimeFilterDialog();
              },
            ),
            ListTile(
              title: const Text('Train Type'),
              subtitle: const Text('Express, Superfast, Local'),
              trailing: _trainTypeFilter != 'all'
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                Navigator.of(context).pop();
                _showTrainTypeFilterDialog();
              },
            ),
            ListTile(
              title: const Text('Sort by Duration'),
              subtitle: const Text('Sort by journey time'),
              trailing: _sortBy == 'duration'
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                Navigator.of(context).pop();
                _setSortBy('duration');
              },
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  void _showTimeFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter by Departure Time'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('All Times'),
              value: 'all',
              groupValue: _timeFilter,
              onChanged: (value) {
                Navigator.of(context).pop();
                _setTimeFilter(value!);
              },
            ),
            RadioListTile<String>(
              title: const Text('Morning (6:00 - 12:00)'),
              value: 'morning',
              groupValue: _timeFilter,
              onChanged: (value) {
                Navigator.of(context).pop();
                _setTimeFilter(value!);
              },
            ),
            RadioListTile<String>(
              title: const Text('Afternoon (12:00 - 18:00)'),
              value: 'afternoon',
              groupValue: _timeFilter,
              onChanged: (value) {
                Navigator.of(context).pop();
                _setTimeFilter(value!);
              },
            ),
            RadioListTile<String>(
              title: const Text('Evening (18:00 - 24:00)'),
              value: 'evening',
              groupValue: _timeFilter,
              onChanged: (value) {
                Navigator.of(context).pop();
                _setTimeFilter(value!);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showTrainTypeFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter by Train Type'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('All Types'),
              value: 'all',
              groupValue: _trainTypeFilter,
              onChanged: (value) {
                Navigator.of(context).pop();
                _setTrainTypeFilter(value!);
              },
            ),
            RadioListTile<String>(
              title: const Text('Express'),
              value: 'express',
              groupValue: _trainTypeFilter,
              onChanged: (value) {
                Navigator.of(context).pop();
                _setTrainTypeFilter(value!);
              },
            ),
            RadioListTile<String>(
              title: const Text('Superfast'),
              value: 'superfast',
              groupValue: _trainTypeFilter,
              onChanged: (value) {
                Navigator.of(context).pop();
                _setTrainTypeFilter(value!);
              },
            ),
            RadioListTile<String>(
              title: const Text('Local'),
              value: 'local',
              groupValue: _trainTypeFilter,
              onChanged: (value) {
                Navigator.of(context).pop();
                _setTrainTypeFilter(value!);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _setTimeFilter(String filter) {
    setState(() {
      _timeFilter = filter;
    });
  }

  void _setTrainTypeFilter(String filter) {
    setState(() {
      _trainTypeFilter = filter;
    });
  }

  void _setSortBy(String sortBy) {
    setState(() {
      _sortBy = sortBy;
    });
  }

  List<Train> _filterAndSortTrains(List<Train> trains) {
    List<Train> filteredTrains = trains;

    // Apply time filter
    if (_timeFilter != 'all') {
      filteredTrains = filteredTrains.where((train) {
        final hour = train.departureTime.hour;
        switch (_timeFilter) {
          case 'morning':
            return hour >= 6 && hour < 12;
          case 'afternoon':
            return hour >= 12 && hour < 18;
          case 'evening':
            return hour >= 18 && hour < 24;
          default:
            return true;
        }
      }).toList();
    }

    // Apply train type filter
    if (_trainTypeFilter != 'all') {
      filteredTrains = filteredTrains.where((train) {
        final trainName = train.trainName.toLowerCase();
        switch (_trainTypeFilter) {
          case 'express':
            return trainName.contains('express');
          case 'superfast':
            return trainName.contains('superfast');
          case 'local':
            return trainName.contains('local');
          default:
            return true;
        }
      }).toList();
    }

    // Apply sorting
    switch (_sortBy) {
      case 'departure':
        filteredTrains.sort(
          (a, b) => a.departureTime.compareTo(b.departureTime),
        );
        break;
      case 'duration':
        filteredTrains.sort(
          (a, b) =>
              _parseDuration(a.duration).compareTo(_parseDuration(b.duration)),
        );
        break;
      case 'price':
        filteredTrains.sort(
          (a, b) => _getTrainPrice(a).compareTo(_getTrainPrice(b)),
        );
        break;
    }

    return filteredTrains;
  }

  Duration _parseDuration(String duration) {
    // Parse duration string like "5h 30m" to Duration
    final parts = duration.split(' ');
    int hours = 0;
    int minutes = 0;

    for (final part in parts) {
      if (part.endsWith('h')) {
        hours = int.tryParse(part.replaceAll('h', '')) ?? 0;
      } else if (part.endsWith('m')) {
        minutes = int.tryParse(part.replaceAll('m', '')) ?? 0;
      }
    }

    return Duration(hours: hours, minutes: minutes);
  }

  double _getTrainPrice(Train train) {
    // Get price for the selected class type, fallback to first available price
    final classType = widget.searchRequest.classType;
    return train.classPrices[classType] ?? train.classPrices.values.first;
  }
}
