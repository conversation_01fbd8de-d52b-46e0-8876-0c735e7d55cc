import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../shared/models/booking/booking_model.dart';
import '../../../../shared/models/user/user_model.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/booking_provider.dart';
import '../../../../shared/providers/priest_provider.dart';
import '../../../../shared/providers/user_provider.dart';
import '../../../../shared/services/booking/priest_booking_notification_service.dart';

class PriestBookingFormScreen extends ConsumerStatefulWidget {
  final String priestId;

  const PriestBookingFormScreen({super.key, required this.priestId});

  @override
  ConsumerState<PriestBookingFormScreen> createState() =>
      _PriestBookingFormScreenState();
}

class _PriestBookingFormScreenState
    extends ConsumerState<PriestBookingFormScreen> {
  String? selectedService;
  DateTime? selectedDate;
  TimeOfDay? selectedTime;
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  UserAddress? selectedAddress;

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: selectedTime ?? TimeOfDay.now(),
    );
    if (picked != null && picked != selectedTime) {
      setState(() {
        selectedTime = picked;
      });
    }
  }

  Future<void> _selectAddress(BuildContext context) async {
    final user = ref.read(userProvider).value;
    if (user == null || user.addresses.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add an address to your profile first'),
        ),
      );
      return;
    }

    final addresses = user.addresses;
    final selectedIndex = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Address'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: addresses.length,
            itemBuilder: (context, index) {
              final address = addresses[index];
              return ListTile(
                title: Text(address.street),
                subtitle: Text('${address.city}, ${address.state}'),
                onTap: () => Navigator.pop(context, index),
              );
            },
          ),
        ),
      ),
    );

    if (selectedIndex != null) {
      setState(() {
        selectedAddress = addresses[selectedIndex];
      });
    }
  }

  Future<void> _submitBooking() async {
    if (!_formKey.currentState!.validate()) return;

    final user = ref.read(authProvider);
    if (user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please login to book a priest')),
      );
      return;
    }

    final priest = ref.read(priestByIdProvider(widget.priestId)).value;
    if (priest == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Priest not found')));
      return;
    }

    final bookingDate = DateTime(
      selectedDate!.year,
      selectedDate!.month,
      selectedDate!.day,
      selectedTime!.hour,
      selectedTime!.minute,
    );

    final booking = BookingModel(
      id: '', // Will be set by the repository
      bookingNumber: '', // Will be set by the repository
      customerId: user.id,
      providerId: priest.id,
      type: BookingType.priest,
      status: BookingStatus.pending,
      paymentStatus: PaymentStatus.pending,
      bookingDate: bookingDate,
      startTime: bookingDate,
      endTime: bookingDate.add(const Duration(hours: 1)),
      serviceLocation: BookingAddress(
        street: selectedAddress?.street ?? '',
        city: selectedAddress?.city ?? '',
        state: selectedAddress?.state ?? '',
        country: selectedAddress?.country ?? '',
        postalCode: selectedAddress?.postalCode ?? '',
        contactName:
            user.userMetadata?['display_name'] ??
            user.userMetadata?['full_name'] ??
            '',
        contactPhone: user.phone ?? '',
        contactEmail: user.email,
      ),
      services: [selectedService!],
      subtotalAmount: priest.pricing[selectedService] ?? 0.0,
      taxAmount: 0.0,
      discountAmount: 0.0,
      totalAmount: priest.pricing[selectedService] ?? 0.0,
      notes: _notesController.text,
      customerName:
          user.userMetadata?['display_name'] ?? user.userMetadata?['full_name'],
      customerEmail: user.email,
      providerName: priest.name,
      providerPhone: priest.contactInfo['phone'] as String?,
      providerEmail: priest.contactInfo['email'] as String?,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    try {
      // First create the booking
      await ref
          .read(bookingStateNotifierProvider(user.id).notifier)
          .createBooking(booking);

      // Send notification to priest about new booking
      final notificationService = PriestBookingNotificationService();
      await notificationService.notifyPriestOfNewBooking(
        booking: booking,
        priest: priest,
      );

      if (mounted) {
        // Show payment options dialog
        final shouldProceedWithPayment = await _showPaymentDialog(
          context,
          booking.totalAmount,
        );

        if (shouldProceedWithPayment == true) {
          // Process payment
          final paymentSuccess = await _processPayment(booking);

          if (paymentSuccess) {
            // Update booking status to confirmed after successful payment
            final updatedBooking = booking.copyWith(
              paymentStatus: PaymentStatus.completed,
              status: BookingStatus.confirmed,
            );

            await ref
                .read(bookingStateNotifierProvider(user.id).notifier)
                .updateBooking(updatedBooking);

            // Send confirmation notifications
            await notificationService.notifyBuyerOfBookingConfirmation(
              booking: updatedBooking,
              priest: priest,
            );

            await notificationService.notifyPaymentConfirmation(
              booking: updatedBooking,
              amount: booking.totalAmount,
            );

            // Schedule reminder notifications
            await notificationService.scheduleBookingReminders(
              booking: updatedBooking,
              priest: priest,
            );

            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Booking confirmed and payment successful!'),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Payment failed. Booking saved but not confirmed.',
                ),
              ),
            );
          }
        } else {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Booking saved. Complete payment to confirm.'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error creating booking: $e')));
      }
    }
  }

  Future<bool?> _showPaymentDialog(BuildContext context, double amount) async {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment Required'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Total Amount: ₹${amount.toStringAsFixed(2)}'),
            const SizedBox(height: 16),
            const Text('Choose payment method:'),
            const SizedBox(height: 8),
            const Text('• UPI Payment'),
            const Text('• Card Payment'),
            const Text('• Pay Later (booking will be pending)'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Pay Later'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Pay Now'),
          ),
        ],
      ),
    );
  }

  Future<bool> _processPayment(BookingModel booking) async {
    try {
      // For now, simulate payment processing
      // In a real implementation, this would integrate with payment gateway

      // Show payment processing dialog
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Processing payment...'),
              ],
            ),
          ),
        );
      }

      // Simulate payment processing delay
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        Navigator.pop(context); // Close processing dialog
      }

      // For demo purposes, return true (successful payment)
      // In production, this would call actual payment service
      return true;
    } catch (e) {
      debugPrint('Payment processing error: $e');
      if (mounted) {
        Navigator.pop(context); // Close processing dialog if open
      }
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final priestAsync = ref.watch(priestByIdProvider(widget.priestId));

    return Scaffold(
      appBar: AppBar(title: const Text('Book a Priest')),
      body: priestAsync.when(
        data: (priest) {
          if (priest == null) {
            return const Center(child: Text('Priest not found'));
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Service',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: selectedService,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: 'Choose a service',
                    ),
                    items: priest.services.map((service) {
                      final price = priest.pricing[service] ?? 0.0;
                      return DropdownMenuItem(
                        value: service,
                        child: Text('$service - ₹${price.toStringAsFixed(2)}'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedService = value;
                      });
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'Please select a service';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Select Date & Time',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _selectDate(context),
                          icon: const Icon(Icons.calendar_today),
                          label: Text(
                            selectedDate == null
                                ? 'Select Date'
                                : DateFormat(
                                    'MMM dd, yyyy',
                                  ).format(selectedDate!),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _selectTime(context),
                          icon: const Icon(Icons.access_time),
                          label: Text(
                            selectedTime == null
                                ? 'Select Time'
                                : selectedTime!.format(context),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Service Location',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  OutlinedButton.icon(
                    onPressed: () => _selectAddress(context),
                    icon: const Icon(Icons.location_on),
                    label: Text(
                      selectedAddress == null
                          ? 'Select Address'
                          : '${selectedAddress!.street}, ${selectedAddress!.city}',
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Additional Notes',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _notesController,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: 'Enter any special requirements or notes',
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _submitBooking,
                      child: const Text('Book Now'),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text(
            'Error: $error',
            style: TextStyle(color: Theme.of(context).colorScheme.error),
          ),
        ),
      ),
    );
  }
}
