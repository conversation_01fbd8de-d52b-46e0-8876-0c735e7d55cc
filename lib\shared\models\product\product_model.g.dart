// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProductVariant _$ProductVariantFromJson(Map<String, dynamic> json) =>
    _ProductVariant(
      id: json['id'] as String,
      name: json['name'] as String,
      sku: json['sku'] as String,
      price: (json['price'] as num).toDouble(),
      salePrice: (json['salePrice'] as num?)?.toDouble(),
      compareAtPrice: (json['compareAtPrice'] as num?)?.toDouble(),
      quantity: (json['quantity'] as num).toInt(),
      barcode: json['barcode'] as String?,
      imageUrl: json['imageUrl'] as String?,
      options: json['options'] as Map<String, dynamic>?,
      isDefault: json['isDefault'] as bool? ?? false,
      isAvailable: json['isAvailable'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$ProductVariantToJson(_ProductVariant instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'sku': instance.sku,
      'price': instance.price,
      'salePrice': instance.salePrice,
      'compareAtPrice': instance.compareAtPrice,
      'quantity': instance.quantity,
      'barcode': instance.barcode,
      'imageUrl': instance.imageUrl,
      'options': instance.options,
      'isDefault': instance.isDefault,
      'isAvailable': instance.isAvailable,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
    };

_ProductCategory _$ProductCategoryFromJson(Map<String, dynamic> json) =>
    _ProductCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      imageUrl: json['imageUrl'] as String?,
      parentId: json['parentId'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$ProductCategoryToJson(_ProductCategory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'imageUrl': instance.imageUrl,
      'parentId': instance.parentId,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
    };

_ProductModel _$ProductModelFromJson(Map<String, dynamic> json) =>
    _ProductModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      originalPrice: (json['originalPrice'] as num?)?.toDouble(),
      discountPercentage: (json['discountPercentage'] as num?)?.toDouble(),
      salePrice: (json['salePrice'] as num?)?.toDouble(),
      compareAtPrice: (json['compareAtPrice'] as num?)?.toDouble(),
      quantity: (json['quantity'] as num).toInt(),
      categoryId: json['categoryId'] as String,
      sellerId: json['sellerId'] as String,
      images: (json['images'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      isFeatured: json['isFeatured'] as bool? ?? false,
      isApproved: json['isApproved'] as bool? ?? false,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isActive: json['isActive'] as bool? ?? true,
      isSuspended: json['isSuspended'] as bool? ?? false,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      brand: json['brand'] as String?,
      unit: json['unit'] as String?,
      weight: (json['weight'] as num?)?.toDouble(),
      rating: (json['rating'] as num?)?.toDouble(),
      reviewCount: (json['reviewCount'] as num?)?.toInt(),
      highlights:
          (json['highlights'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      specifications: json['specifications'] as Map<String, dynamic>?,
      variants: (json['variants'] as List<dynamic>?)
          ?.map((e) => ProductVariant.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      productType: $enumDecode(_$ProductTypeEnumMap, json['productType']),
      productStatus: $enumDecode(_$ProductStatusEnumMap, json['productStatus']),
      verificationNotes: json['verificationNotes'] as String?,
      sellerName: json['sellerName'] as String?,
      sellerEmail: json['sellerEmail'] as String?,
      sellerPhone: json['sellerPhone'] as String?,
      sellerAddress: json['sellerAddress'] as String?,
      sellerRating: (json['sellerRating'] as num?)?.toDouble(),
      sellerTotalSales: (json['sellerTotalSales'] as num?)?.toInt(),
      sellerTotalProducts: (json['sellerTotalProducts'] as num?)?.toInt(),
      sellerIsVerified: json['sellerIsVerified'] as bool?,
      sellerIsActive: json['sellerIsActive'] as bool?,
      sellerCreatedAt: json['sellerCreatedAt'] == null
          ? null
          : DateTime.parse(json['sellerCreatedAt'] as String),
      sellerUpdatedAt: json['sellerUpdatedAt'] == null
          ? null
          : DateTime.parse(json['sellerUpdatedAt'] as String),
    );

Map<String, dynamic> _$ProductModelToJson(_ProductModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'price': instance.price,
      'originalPrice': instance.originalPrice,
      'discountPercentage': instance.discountPercentage,
      'salePrice': instance.salePrice,
      'compareAtPrice': instance.compareAtPrice,
      'quantity': instance.quantity,
      'categoryId': instance.categoryId,
      'sellerId': instance.sellerId,
      'images': instance.images,
      'isFeatured': instance.isFeatured,
      'isApproved': instance.isApproved,
      'isDeleted': instance.isDeleted,
      'isActive': instance.isActive,
      'isSuspended': instance.isSuspended,
      'tags': instance.tags,
      'brand': instance.brand,
      'unit': instance.unit,
      'weight': instance.weight,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'highlights': instance.highlights,
      'specifications': instance.specifications,
      'variants': instance.variants?.map((e) => e.toJson()).toList(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'productType': _$ProductTypeEnumMap[instance.productType]!,
      'productStatus': _$ProductStatusEnumMap[instance.productStatus]!,
      'verificationNotes': instance.verificationNotes,
      'sellerName': instance.sellerName,
      'sellerEmail': instance.sellerEmail,
      'sellerPhone': instance.sellerPhone,
      'sellerAddress': instance.sellerAddress,
      'sellerRating': instance.sellerRating,
      'sellerTotalSales': instance.sellerTotalSales,
      'sellerTotalProducts': instance.sellerTotalProducts,
      'sellerIsVerified': instance.sellerIsVerified,
      'sellerIsActive': instance.sellerIsActive,
      'sellerCreatedAt': instance.sellerCreatedAt?.toIso8601String(),
      'sellerUpdatedAt': instance.sellerUpdatedAt?.toIso8601String(),
    };

const _$ProductTypeEnumMap = {
  ProductType.physical: 0,
  ProductType.digital: 1,
  ProductType.service: 2,
};

const _$ProductStatusEnumMap = {
  ProductStatus.draft: 0,
  ProductStatus.pending: 1,
  ProductStatus.approved: 2,
  ProductStatus.rejected: 3,
  ProductStatus.archived: 4,
};
