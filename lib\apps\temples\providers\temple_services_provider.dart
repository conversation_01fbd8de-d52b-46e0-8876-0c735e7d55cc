import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../shared/models/temple/temple_model.dart';
import '../../../shared/services/temple/temple_service.dart';
import '../../../shared/core/service_locator.dart';

/// Temple services state
@immutable
class TempleServicesState {
  final List<Darshan> darshans;
  final List<Seva> sevas;
  final bool isLoading;
  final String? error;

  const TempleServicesState({
    this.darshans = const [],
    this.sevas = const [],
    this.isLoading = false,
    this.error,
  });

  TempleServicesState copyWith({
    List<Darshan>? darshans,
    List<Seva>? sevas,
    bool? isLoading,
    String? error,
  }) {
    return TempleServicesState(
      darshans: darshans ?? this.darshans,
      sevas: sevas ?? this.sevas,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Temple services notifier
class TempleServicesNotifier extends StateNotifier<TempleServicesState> {
  final TempleService _templeService;

  TempleServicesNotifier(this._templeService)
    : super(const TempleServicesState());

  /// Load services for temple
  Future<void> loadServices(String templeId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temple = await _templeService.getTempleById(templeId);

      if (temple != null) {
        state = state.copyWith(
          darshans: temple.darshans,
          sevas: temple.sevas,
          isLoading: false,
        );
      } else {
        state = state.copyWith(error: 'Temple not found', isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Add darshan service
  Future<bool> addDarshan(String templeId, Darshan darshan) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temple = await _templeService.getTempleById(templeId);

      if (temple != null) {
        final updatedDarshans = [...temple.darshans, darshan];
        final updatedTemple = temple.copyWith(darshans: updatedDarshans);

        final updatedResult = await _templeService.updateTemple(
          templeId,
          updatedTemple,
        );

        if (updatedResult != null) {
          state = state.copyWith(darshans: updatedDarshans, isLoading: false);
          return true;
        } else {
          state = state.copyWith(
            error: 'Failed to add darshan',
            isLoading: false,
          );
          return false;
        }
      } else {
        state = state.copyWith(error: 'Temple not found', isLoading: false);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Add seva service
  Future<bool> addSeva(String templeId, Seva seva) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temple = await _templeService.getTempleById(templeId);

      if (temple != null) {
        final updatedSevas = [...temple.sevas, seva];
        final updatedTemple = temple.copyWith(sevas: updatedSevas);

        final updatedResult = await _templeService.updateTemple(
          templeId,
          updatedTemple,
        );

        if (updatedResult != null) {
          state = state.copyWith(sevas: updatedSevas, isLoading: false);
          return true;
        } else {
          state = state.copyWith(error: 'Failed to add seva', isLoading: false);
          return false;
        }
      } else {
        state = state.copyWith(error: 'Temple not found', isLoading: false);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Update darshan service
  Future<bool> updateDarshan(String templeId, Darshan updatedDarshan) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temple = await _templeService.getTempleById(templeId);

      if (temple != null) {
        final updatedDarshans = temple.darshans.map((darshan) {
          return darshan.id == updatedDarshan.id ? updatedDarshan : darshan;
        }).toList();

        final updatedTemple = temple.copyWith(darshans: updatedDarshans);

        final updatedResult = await _templeService.updateTemple(
          templeId,
          updatedTemple,
        );

        if (updatedResult != null) {
          state = state.copyWith(darshans: updatedDarshans, isLoading: false);
          return true;
        } else {
          state = state.copyWith(
            error: 'Failed to update darshan',
            isLoading: false,
          );
          return false;
        }
      } else {
        state = state.copyWith(error: 'Temple not found', isLoading: false);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Update seva service
  Future<bool> updateSeva(String templeId, Seva updatedSeva) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temple = await _templeService.getTempleById(templeId);

      if (temple != null) {
        final updatedSevas = temple.sevas.map((seva) {
          return seva.id == updatedSeva.id ? updatedSeva : seva;
        }).toList();

        final updatedTemple = temple.copyWith(sevas: updatedSevas);

        final updatedResult = await _templeService.updateTemple(
          templeId,
          updatedTemple,
        );

        if (updatedResult != null) {
          state = state.copyWith(sevas: updatedSevas, isLoading: false);
          return true;
        } else {
          state = state.copyWith(
            error: 'Failed to update seva',
            isLoading: false,
          );
          return false;
        }
      } else {
        state = state.copyWith(error: 'Temple not found', isLoading: false);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Delete darshan service
  Future<bool> deleteDarshan(String templeId, String darshanId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temple = await _templeService.getTempleById(templeId);

      if (temple != null) {
        final updatedDarshans = temple.darshans
            .where((darshan) => darshan.id != darshanId)
            .toList();

        final updatedTemple = temple.copyWith(darshans: updatedDarshans);

        final updatedResult = await _templeService.updateTemple(
          templeId,
          updatedTemple,
        );

        if (updatedResult != null) {
          state = state.copyWith(darshans: updatedDarshans, isLoading: false);
          return true;
        } else {
          state = state.copyWith(
            error: 'Failed to delete darshan',
            isLoading: false,
          );
          return false;
        }
      } else {
        state = state.copyWith(error: 'Temple not found', isLoading: false);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Delete seva service
  Future<bool> deleteSeva(String templeId, String sevaId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final temple = await _templeService.getTempleById(templeId);

      if (temple != null) {
        final updatedSevas = temple.sevas
            .where((seva) => seva.id != sevaId)
            .toList();

        final updatedTemple = temple.copyWith(sevas: updatedSevas);

        final updatedResult = await _templeService.updateTemple(
          templeId,
          updatedTemple,
        );

        if (updatedResult != null) {
          state = state.copyWith(sevas: updatedSevas, isLoading: false);
          return true;
        } else {
          state = state.copyWith(
            error: 'Failed to delete seva',
            isLoading: false,
          );
          return false;
        }
      } else {
        state = state.copyWith(error: 'Temple not found', isLoading: false);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Temple services provider
final templeServicesProvider =
    StateNotifierProvider<TempleServicesNotifier, TempleServicesState>((ref) {
      final templeService = ref.watch(templeServiceProvider);
      return TempleServicesNotifier(templeService);
    });

/// Temple service provider
final templeServiceProvider = Provider<TempleService>((ref) {
  return serviceLocator<TempleService>();
});
