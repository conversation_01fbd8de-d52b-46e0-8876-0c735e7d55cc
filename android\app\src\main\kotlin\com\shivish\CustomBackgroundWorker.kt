package com.shivish

import android.content.Context
import androidx.work.Worker
import androidx.work.WorkerParameters
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel

class CustomBackgroundWorker(
    private val context: Context,
    workerParams: WorkerParameters
) : Worker(context, workerParams) {

    companion object {
        const val CHANNEL_NAME = "be.tramckrijte.workmanager/background_channel_work_manager"
        const val DART_TASK_KEY = "be.tramckrijte.workmanager.DART_TASK"
        const val PAYLOAD_KEY = "be.tramckrijte.workmanager.PAYLOAD"
    }

    override fun doWork(): Result {
        val taskName = inputData.getString(DART_TASK_KEY) ?: return Result.failure()
        val payload = inputData.getString(PAYLOAD_KEY)

        // Create a new FlutterEngine if not cached
        val engineId = "background_worker_engine"
        var flutterEngine = FlutterEngineCache.getInstance().get(engineId)
        
        if (flutterEngine == null) {
            flutterEngine = FlutterEngine(context)
            flutterEngine.dartExecutor.executeDartEntrypoint(
                DartExecutor.DartEntrypoint.createDefault()
            )
            FlutterEngineCache.getInstance().put(engineId, flutterEngine)
        }

        // Set up the method channel
        val methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_NAME)
        
        // Call the Dart method
        val args = HashMap<String, Any?>()
        args["task"] = taskName
        args["payload"] = payload
        
        try {
            methodChannel.invokeMethod("onBackgroundTask", args)
            return Result.success()
        } catch (e: Exception) {
            return Result.failure()
        }
    }
}
