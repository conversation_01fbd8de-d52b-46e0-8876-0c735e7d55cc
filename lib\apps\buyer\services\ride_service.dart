import 'dart:async';
import 'dart:math';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:latlong2/latlong.dart' as latlong2;
import 'package:uuid/uuid.dart';
import '../../../shared/models/ride/ride_request_model.dart';
import '../../../shared/models/common/geo_point.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/providers/auth_provider.dart';
import '../../../shared/utils/logger.dart';

final _logger = getLogger('RideService');

/// Provider for the ride service
final rideServiceProvider = Provider<RideService>((ref) {
  // Get the current user from Supabase Auth directly
  final currentUser = Supabase.instance.client.auth.currentUser;
  final userId = currentUser?.id;

  // Also check the auth provider
  final authState = ref.watch(authProvider);
  final authProviderId = authState?.id;

  // Log the user IDs for debugging
  _logger.info('RideServiceProvider - Supabase Auth userId: $userId');
  _logger.info('RideServiceProvider - Auth Provider userId: $authProviderId');

  // Use the Supabase Auth user ID if available, otherwise fall back to the auth provider
  final finalUserId = userId ?? authProviderId;

  return RideService(
    databaseService: DatabaseService(DatabaseConfig.fromEnvironment()),
    userId: finalUserId,
  );
});

/// Provider for active ride
final activeRideProvider = StreamProvider<RideRequestModel?>((ref) {
  final rideService = ref.watch(rideServiceProvider);
  return rideService.getActiveRide();
});

/// Provider for ride history
final rideHistoryProvider = StreamProvider<List<RideRequestModel>>((ref) {
  final rideService = ref.watch(rideServiceProvider);
  return rideService.getRideHistory();
});

/// Provider for ride details
final rideDetailsProvider = StreamProvider.family<RideRequestModel?, String>((
  ref,
  rideId,
) {
  final rideService = ref.watch(rideServiceProvider);
  return rideService.getRideById(rideId);
});

/// Provider for driver location
final driverLocationProvider = StreamProvider.family<latlong2.LatLng?, String>((
  ref,
  rideId,
) {
  final rideService = ref.watch(rideServiceProvider);
  return rideService.getDriverLocation(rideId);
});

/// Service for managing ride requests
class RideService {
  final DatabaseService _databaseService;
  final String? _userId;

  // Database collections
  static const String _rideRequestsCollection = 'ride_requests';
  static const String _deliveryPersonsCollection = 'delivery_persons';

  RideService({
    required DatabaseService databaseService,
    required String? userId,
  }) : _databaseService = databaseService,
       _userId = userId;

  /// Create a new ride request
  Future<RideRequestModel> createRideRequest({
    required String pickupAddress,
    required String dropAddress,
    required Map<String, double> pickupLocation,
    required Map<String, double> dropLocation,
    required String userName,
    required String userPhone,
    required double estimatedFare,
    required double distance,
    required int estimatedDuration,
    required RidePaymentMethod paymentMethod,
    String? vehicleType,
  }) async {
    _logger.info('Creating ride request with userId: $_userId');

    if (_userId == null) {
      _logger.severe('User not authenticated - userId is null');
      throw Exception('User not authenticated');
    }

    // Double check with Supabase Auth
    final currentUser = Supabase.instance.client.auth.currentUser;
    if (currentUser == null) {
      _logger.severe(
        'User not authenticated - Supabase Auth currentUser is null',
      );
      throw Exception('User not authenticated');
    }

    _logger.info('Supabase Auth currentUser: ${currentUser.id}');

    try {
      _logger.info('Creating ride request with vehicle type: $vehicleType');

      // Generate a unique ID for the ride
      final rideId = const Uuid().v4();

      // Generate a random 4-digit verification PIN
      final pin = (1000 + (DateTime.now().millisecondsSinceEpoch % 9000))
          .toString();

      // Create a minimal map for hybrid storage with only essential fields
      final Map<String, dynamic> rideData = {
        'id': rideId,
        'userId': _userId,
        'status': 'pending',
        'pickupAddress': pickupAddress,
        'dropAddress': dropAddress,
        'pickupLocation': pickupLocation,
        'dropLocation': dropLocation,
        'userName': userName,
        'userPhone': userPhone,
        'estimatedFare': estimatedFare,
        'distance': distance,
        'estimatedDuration': estimatedDuration,
        'paymentMethod': paymentMethod.name,
        'isPaid': false,
        'verificationPin': pin,
        'vehicleType': vehicleType,
        'createdAt': DateTime.now().toIso8601String(),
      };

      _logger.info(
        'Ride data prepared for hybrid storage: ${rideData.keys.join(', ')}',
      );
      _logger.info('Saving ride request to hybrid storage with ID: $rideId');

      try {
        _logger.info('Attempting to save ride request to hybrid storage...');

        // Print the full ride data for debugging
        _logger.info('Full ride data: $rideData');

        // Convert GeoPoint objects to maps
        final Map<String, dynamic> safeRideData = Map<String, dynamic>.from(
          rideData,
        );

        // Log the safe data
        _logger.info('Safe ride data: $safeRideData');

        // Save to hybrid storage
        await _databaseService.create(_rideRequestsCollection, safeRideData);

        _logger.info('Successfully saved ride request to hybrid storage');

        // Create a simple RideRequestModel for the return value
        final rideRequest = RideRequestModel.create(
          userId: _userId,
          pickupAddress: pickupAddress,
          dropAddress: dropAddress,
          pickupLocation: GeoPoint(
            pickupLocation['latitude']!,
            pickupLocation['longitude']!,
          ),
          dropLocation: GeoPoint(
            dropLocation['latitude']!,
            dropLocation['longitude']!,
          ),
          userName: userName,
          userPhone: userPhone,
          estimatedFare: estimatedFare,
          distance: distance,
          estimatedDuration: estimatedDuration,
          paymentMethod: paymentMethod,
          vehicleType: vehicleType,
        );

        // Override the ID to match the one we used in hybrid storage
        final rideRequestWithId = rideRequest.copyWith(id: rideId);

        _logger.info('Ride request created: $rideId');

        // Check for nearby available Saviours (delivery persons)
        _checkForNearbySaviours(rideRequestWithId);

        return rideRequestWithId;
      } catch (e, stackTrace) {
        _logger.severe(
          'Error saving ride request to hybrid storage: $e',
          e,
          stackTrace,
        );

        // Try to provide more specific error information
        if (e.toString().contains('permission-denied')) {
          throw Exception(
            'Permission denied: You do not have permission to create ride requests',
          );
        } else if (e.toString().contains('network')) {
          throw Exception(
            'Network error: Please check your internet connection',
          );
        } else if (e.toString().contains('invalid-argument')) {
          throw Exception('Invalid data: Some ride information is invalid');
        } else {
          throw Exception('Server error: ${e.toString()}');
        }
      }
    } catch (e) {
      _logger.severe('Error creating ride request: $e');
      rethrow;
    }
  }

  /// Check for nearby Saviours and notify them about the ride request
  Future<void> _checkForNearbySaviours(RideRequestModel rideRequest) async {
    try {
      _logger.info('Checking for nearby Saviours for ride: ${rideRequest.id}');

      // Get all active delivery persons (Saviours) from hybrid storage
      final allSaviours = await _databaseService.getAll(
        _deliveryPersonsCollection,
      );

      final availableSaviours = allSaviours
          .where(
            (saviourData) =>
                saviourData['isOnline'] == true &&
                saviourData['status'] == 'available',
          )
          .toList();

      if (availableSaviours.isEmpty) {
        _logger.info('No available Saviours found');
        return;
      }

      _logger.info('Found ${availableSaviours.length} potential Saviours');

      // Filter by vehicle type if specified
      final filteredSaviours = availableSaviours.where((saviourData) {
        if (rideRequest.vehicleType == null ||
            rideRequest.vehicleType!.isEmpty) {
          return true; // No vehicle type filter
        }

        final vehicleTypes = saviourData['vehicleTypes'] as List<dynamic>?;
        if (vehicleTypes == null || vehicleTypes.isEmpty) {
          return false;
        }

        return vehicleTypes.contains(rideRequest.vehicleType);
      }).toList();

      _logger.info(
        'Found ${filteredSaviours.length} Saviours with matching vehicle type',
      );

      // Create notifications for each nearby Saviour
      for (final saviourData in filteredSaviours) {
        final saviourId = saviourData['id'] as String? ?? '';

        // Check if Saviour has location data
        if (!saviourData.containsKey('currentLocation')) {
          continue;
        }

        final saviourLocation =
            saviourData['currentLocation'] as Map<String, dynamic>?;
        if (saviourLocation == null) {
          _logger.warning('Saviour $saviourId has invalid current location');
          continue;
        }

        final saviourLat = saviourLocation['latitude'] as double? ?? 0.0;
        final saviourLng = saviourLocation['longitude'] as double? ?? 0.0;
        final pickupLat = rideRequest.pickupLocation.latitude;
        final pickupLng = rideRequest.pickupLocation.longitude;

        // Calculate distance between Saviour and pickup location
        final distance = _calculateDistance(
          saviourLat,
          saviourLng,
          pickupLat,
          pickupLng,
        );

        // Only notify Saviours within 10km radius
        if (distance <= 10000) {
          // 10km in meters
          _logger.info(
            'Notifying Saviour $saviourId (${distance.toStringAsFixed(0)}m away)',
          );

          try {
            // Create a notification for this Saviour in hybrid storage
            final notificationData = {
              'id': const Uuid().v4(),
              'saviourId': saviourId,
              'type': 'ride_request',
              'rideId': rideRequest.id,
              'createdAt': DateTime.now().toIso8601String(),
              'read': false,
              'distance': distance,
              'estimatedFare': rideRequest.estimatedFare,
              'pickupAddress': rideRequest.pickupAddress,
              'dropAddress': rideRequest.dropAddress,
            };

            await _databaseService.create(
              'saviour_notifications',
              notificationData,
            );

            _logger.info('Notification created for Saviour: $saviourId');
          } catch (e) {
            _logger.warning(
              'Error creating notification for Saviour: $saviourId - $e',
            );
            // Continue with the next Saviour
          }
        }
      }
    } catch (e, stackTrace) {
      _logger.severe('Error checking for nearby Saviours: $e', e, stackTrace);
      // Don't rethrow - this is a background process
    }
  }

  /// Calculate distance between two coordinates in meters
  double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const R = 6371000; // Earth radius in meters
    final phi1 = lat1 * pi / 180;
    final phi2 = lat2 * pi / 180;
    final deltaPhi = (lat2 - lat1) * pi / 180;
    final deltaLambda = (lon2 - lon1) * pi / 180;

    final a =
        sin(deltaPhi / 2) * sin(deltaPhi / 2) +
        cos(phi1) * cos(phi2) * sin(deltaLambda / 2) * sin(deltaLambda / 2);
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return R * c; // Distance in meters
  }

  /// Get active ride for the current user
  Stream<RideRequestModel?> getActiveRide() {
    if (_userId == null) {
      return Stream.value(null);
    }

    // Convert to polling-based stream for hybrid storage
    late StreamController<RideRequestModel?> controller;
    Timer? timer;

    controller = StreamController<RideRequestModel?>(
      onListen: () {
        // Immediate fetch
        _getActiveRideOnce().then((ride) {
          if (!controller.isClosed) controller.add(ride);
        });

        // Periodic updates every 5 seconds
        timer = Timer.periodic(const Duration(seconds: 5), (_) async {
          try {
            final ride = await _getActiveRideOnce();
            if (!controller.isClosed) controller.add(ride);
          } catch (e) {
            _logger.severe('Error in active ride stream: $e');
            if (!controller.isClosed) controller.add(null);
          }
        });
      },
      onCancel: () {
        timer?.cancel();
      },
    );

    return controller.stream;
  }

  Future<RideRequestModel?> _getActiveRideOnce() async {
    try {
      final allRides = await _databaseService.getAll(_rideRequestsCollection);

      final activeRides = allRides
          .where(
            (rideData) =>
                rideData['userId'] == _userId &&
                [
                  RideStatus.pending.name,
                  RideStatus.accepted.name,
                  RideStatus.arrived.name,
                  RideStatus.inProgress.name,
                ].contains(rideData['status']),
          )
          .toList();

      if (activeRides.isEmpty) return null;

      // Sort by createdAt (descending) and get the first one
      activeRides.sort((a, b) {
        final dateA =
            DateTime.tryParse(a['createdAt'] as String? ?? '') ??
            DateTime.now();
        final dateB =
            DateTime.tryParse(b['createdAt'] as String? ?? '') ??
            DateTime.now();
        return dateB.compareTo(dateA);
      });

      return RideRequestModel.fromJson(activeRides.first);
    } catch (e) {
      _logger.severe('Error getting active ride: $e');
      return null;
    }
  }

  /// Get ride history for the current user
  Stream<List<RideRequestModel>> getRideHistory() {
    if (_userId == null) {
      return Stream.value([]);
    }

    // Convert to polling-based stream for hybrid storage
    late StreamController<List<RideRequestModel>> controller;
    Timer? timer;

    controller = StreamController<List<RideRequestModel>>(
      onListen: () {
        // Immediate fetch
        _getRideHistoryOnce().then((rides) {
          if (!controller.isClosed) controller.add(rides);
        });

        // Periodic updates every 30 seconds (less frequent for history)
        timer = Timer.periodic(const Duration(seconds: 30), (_) async {
          try {
            final rides = await _getRideHistoryOnce();
            if (!controller.isClosed) controller.add(rides);
          } catch (e) {
            _logger.severe('Error in ride history stream: $e');
            if (!controller.isClosed) controller.add([]);
          }
        });
      },
      onCancel: () {
        timer?.cancel();
      },
    );

    return controller.stream;
  }

  Future<List<RideRequestModel>> _getRideHistoryOnce() async {
    try {
      final allRides = await _databaseService.getAll(_rideRequestsCollection);

      final historyRides = allRides
          .where(
            (rideData) =>
                rideData['userId'] == _userId &&
                [
                  RideStatus.completed.name,
                  RideStatus.cancelled.name,
                ].contains(rideData['status']),
          )
          .toList();

      // Sort by createdAt (descending) and limit to 20
      historyRides.sort((a, b) {
        final dateA =
            DateTime.tryParse(a['createdAt'] as String? ?? '') ??
            DateTime.now();
        final dateB =
            DateTime.tryParse(b['createdAt'] as String? ?? '') ??
            DateTime.now();
        return dateB.compareTo(dateA);
      });

      final limitedRides = historyRides.take(20).toList();
      return limitedRides
          .map((rideData) => RideRequestModel.fromJson(rideData))
          .toList();
    } catch (e) {
      _logger.severe('Error getting ride history: $e');
      return [];
    }
  }

  /// Get ride by ID
  Stream<RideRequestModel?> getRideById(String rideId) {
    // Convert to polling-based stream for hybrid storage
    late StreamController<RideRequestModel?> controller;
    Timer? timer;

    controller = StreamController<RideRequestModel?>(
      onListen: () {
        // Immediate fetch
        _getRideByIdOnce(rideId).then((ride) {
          if (!controller.isClosed) controller.add(ride);
        });

        // Periodic updates every 5 seconds
        timer = Timer.periodic(const Duration(seconds: 5), (_) async {
          try {
            final ride = await _getRideByIdOnce(rideId);
            if (!controller.isClosed) controller.add(ride);
          } catch (e) {
            _logger.severe('Error in ride by ID stream: $e');
            if (!controller.isClosed) controller.add(null);
          }
        });
      },
      onCancel: () {
        timer?.cancel();
      },
    );

    return controller.stream;
  }

  Future<RideRequestModel?> _getRideByIdOnce(String rideId) async {
    try {
      final rideData = await _databaseService.find(
        _rideRequestsCollection,
        rideId,
      );
      if (rideData == null) return null;
      return RideRequestModel.fromJson(rideData);
    } catch (e) {
      _logger.severe('Error getting ride by ID: $e');
      return null;
    }
  }

  /// Cancel a ride request
  Future<void> cancelRide(String rideId, String reason) async {
    try {
      _logger.info('Attempting to cancel ride: $rideId');

      // Skip fetching the ride document and just update the status directly
      // This avoids the GeoPoint conversion issue

      // Update ride status in hybrid storage
      final existingRide = await _databaseService.find(
        _rideRequestsCollection,
        rideId,
      );
      if (existingRide == null) {
        throw Exception('Ride not found');
      }

      final statusUpdates = List<Map<String, dynamic>>.from(
        existingRide['statusUpdates'] ?? [],
      );
      statusUpdates.add({
        'status': RideStatus.cancelled.name,
        'timestamp': DateTime.now().toIso8601String(),
        'note': 'Cancelled by user: $reason',
      });

      await _databaseService.update(_rideRequestsCollection, rideId, {
        ...existingRide,
        'status': RideStatus.cancelled.name,
        'cancelledAt': DateTime.now().toIso8601String(),
        'cancelReason': reason,
        'statusUpdates': statusUpdates,
      });

      _logger.info('Ride cancelled successfully: $rideId');
    } catch (e, stackTrace) {
      _logger.severe('Error cancelling ride: $e', e, stackTrace);
      rethrow;
    }
  }

  /// Rate a completed ride
  Future<void> rateRide(String rideId, double rating, String? review) async {
    try {
      final rideData = await _databaseService.find(
        _rideRequestsCollection,
        rideId,
      );

      if (rideData == null) {
        throw Exception('Ride not found');
      }

      final ride = RideRequestModel.fromJson(rideData);

      // Only allow rating of completed rides
      if (ride.status != RideStatus.completed) {
        throw Exception('Cannot rate ride in ${ride.status.name} status');
      }

      // Update ride
      await _databaseService.update(_rideRequestsCollection, rideId, {
        ...rideData,
        'rating': rating,
        'review': review,
      });

      // Update driver rating if available
      if (ride.driverId != null) {
        // Get all completed rides for this driver
        final allRides = await _databaseService.getAll(_rideRequestsCollection);
        final driverRides = allRides
            .where(
              (rideData) =>
                  rideData['driverId'] == ride.driverId &&
                  rideData['status'] == RideStatus.completed.name &&
                  (rideData['rating'] as num?)?.toDouble() != null &&
                  (rideData['rating'] as num?)!.toDouble() > 0,
            )
            .toList();

        if (driverRides.isNotEmpty) {
          // Calculate average rating
          double totalRating = 0;
          for (final rideData in driverRides) {
            totalRating += (rideData['rating'] as num).toDouble();
          }

          final averageRating = totalRating / driverRides.length;

          // Update driver rating
          final driverData = await _databaseService.find(
            _deliveryPersonsCollection,
            ride.driverId!,
          );
          if (driverData != null) {
            await _databaseService.update(
              _deliveryPersonsCollection,
              ride.driverId!,
              {...driverData, 'rating': averageRating},
            );
          }
        }
      }

      _logger.info('Ride rated: $rideId');
    } catch (e) {
      _logger.severe('Error rating ride: $e');
      rethrow;
    }
  }

  /// Get driver location for a ride
  Stream<latlong2.LatLng?> getDriverLocation(String rideId) {
    // Convert to polling-based stream for hybrid storage
    late StreamController<latlong2.LatLng?> controller;
    Timer? timer;

    controller = StreamController<latlong2.LatLng?>(
      onListen: () {
        // Immediate fetch
        _getDriverLocationOnce(rideId).then((location) {
          if (!controller.isClosed) controller.add(location);
        });

        // Periodic updates every 5 seconds for real-time tracking
        timer = Timer.periodic(const Duration(seconds: 5), (_) async {
          try {
            final location = await _getDriverLocationOnce(rideId);
            if (!controller.isClosed) controller.add(location);
          } catch (e) {
            _logger.severe('Error in driver location stream: $e');
            if (!controller.isClosed) controller.add(null);
          }
        });
      },
      onCancel: () {
        timer?.cancel();
      },
    );

    return controller.stream;
  }

  Future<latlong2.LatLng?> _getDriverLocationOnce(String rideId) async {
    try {
      final rideData = await _databaseService.find(
        _rideRequestsCollection,
        rideId,
      );
      if (rideData == null) return null;

      final ride = RideRequestModel.fromJson(rideData);
      if (ride.driverId == null) return null;

      // Get driver location from delivery_persons collection
      final driverData = await _databaseService.find(
        _deliveryPersonsCollection,
        ride.driverId!,
      );
      if (driverData == null) return null;

      if (driverData['currentLocation'] == null) return null;

      final location = driverData['currentLocation'] as Map<String, dynamic>?;
      if (location == null) return null;

      final lat = location['latitude'] as double? ?? 0.0;
      final lng = location['longitude'] as double? ?? 0.0;

      return latlong2.LatLng(lat, lng);
    } catch (e) {
      _logger.severe('Error getting driver location: $e');
      return null;
    }
  }
}
