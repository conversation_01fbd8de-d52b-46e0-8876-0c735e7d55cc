import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final servicesProvider =
    AsyncNotifierProvider<ServicesNotifier, List<Map<String, dynamic>>>(() {
  return ServicesNotifier();
});

class ServicesNotifier extends AsyncNotifier<List<Map<String, dynamic>>> {
  late final DatabaseService _databaseService;

  @override
  Future<List<Map<String, dynamic>>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadServices();
  }

  Future<List<Map<String, dynamic>>> _loadServices() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('technicians', userId);
      return List<Map<String, dynamic>>.from(
        userData?['services'] ?? [],
      );
    } catch (e) {
      debugPrint('Failed to load services: $e');
      throw Exception('Failed to load services: $e');
    }
  }

  Future<void> saveServices(List<Map<String, dynamic>> services) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      // Validate services
      for (final service in services) {
        if (service['name']?.toString().isEmpty ?? true) {
          throw Exception('Service name is required');
        }
        if (service['price'] == null || service['price'] < 0) {
          throw Exception('Valid service price is required');
        }
      }

      await _databaseService.update('technicians', userId, {
        'services': services,
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData(services);
    } catch (e) {
      debugPrint('Failed to save services: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> addService(Map<String, dynamic> service) async {
    try {
      final currentServices = await _loadServices();
      final updatedServices = [...currentServices, service];
      await saveServices(updatedServices);
    } catch (e) {
      debugPrint('Failed to add service: $e');
      rethrow;
    }
  }

  Future<void> removeService(int index) async {
    try {
      final currentServices = await _loadServices();
      if (index >= 0 && index < currentServices.length) {
        currentServices.removeAt(index);
        await saveServices(currentServices);
      }
    } catch (e) {
      debugPrint('Failed to remove service: $e');
      rethrow;
    }
  }
}
