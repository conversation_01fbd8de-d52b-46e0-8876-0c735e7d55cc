import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shivish/apps/seller/presentation/cubits/settings_cubit.dart';
import 'package:shivish/shared/services/settings/settings_service.dart';
import 'settings_notifier.dart';

// Provider for SharedPreferences
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError(
      'SharedPreferences must be initialized and overridden at the app level');
});

// Provider for SettingsService
final settingsServiceProvider = Provider<SettingsService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return SettingsService(prefs);
});

// New StateNotifierProvider for settings
final settingsProvider =
    StateNotifierProvider<SettingsNotifier, SettingsState>((ref) {
  final settingsService = ref.watch(settingsServiceProvider);
  return SettingsNotifier(settingsService);
});

// Legacy provider for backward compatibility
// This will be used by existing code that depends on the cubit
final settingsCubitProvider = Provider<SettingsCubit>((ref) {
  // Watch the settings provider to ensure this rebuilds when settings change
  ref.watch(settingsProvider);

  // Get the settings service
  final settingsService = ref.watch(settingsServiceProvider);

  // Create a new cubit with the same service
  return SettingsCubit(settingsService);
});
