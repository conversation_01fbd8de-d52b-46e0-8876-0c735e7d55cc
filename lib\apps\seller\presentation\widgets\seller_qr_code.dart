import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:shivish/shared/models/seller.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SellerQRCode extends ConsumerWidget {
  final Seller seller;
  final double size;
  final GlobalKey? repaintBoundaryKey;

  const SellerQRCode({
    super.key,
    required this.seller,
    this.size = 200,
    this.repaintBoundaryKey,
  });

  String _getUpiId() {
    final paymentSettings = seller.paymentSettings;
    if (paymentSettings.containsKey('upiSettings')) {
      final upiId = paymentSettings['upiSettings']?['upiId'] as String?;
      return upiId ?? '';
    }
    return '';
  }

  String _generateUpiPaymentUrl() {
    final upiId = _getUpiId();
    if (upiId.isEmpty) {
      return '';
    }

    // Format: upi://pay?pa=UPI_ID&pn=NAME&mc=0000&tr=TRANSACTION_REF&tn=DESCRIPTION
    // This format is compatible with all UPI apps
    final transactionRef = 'SHIVISH${DateTime.now().millisecondsSinceEpoch}';
    final description = 'Payment to ${seller.businessName}';

    return "upi://pay?pa=${Uri.encodeComponent(upiId)}"
        "&pn=${Uri.encodeComponent(seller.businessName)}"
        "&mc=0000" // Merchant category code (optional)
        "&tr=${Uri.encodeComponent(transactionRef)}" // Transaction reference
        "&tn=${Uri.encodeComponent(description)}" // Transaction note/description
        "&cu=INR"; // Currency (INR for Indian Rupee)
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final upiId = _getUpiId();
    final qrData = upiId.isNotEmpty
        ? _generateUpiPaymentUrl()
        : Uri.encodeFull({
            'id': seller.id,
            'businessName': seller.businessName,
            'category': seller.category.name,
            'email': seller.email,
            'phoneNumber': seller.phoneNumber,
          }.toString());

    final qrCodeWidget = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // QR Code
        Stack(
          alignment: Alignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white,
                    Color(0xFFF5F5F5), // Very light gray
                    Color(0xFFE8F5E9), // Very light green
                  ],
                  stops: [0.0, 0.7, 1.0],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: QrImageView(
                data: qrData,
                version: QrVersions.auto,
                size: size,
                backgroundColor: Colors.transparent,
                padding: EdgeInsets.all(size * 0.1),
                embeddedImage: AssetImage('assets/images/flavors/shivish.png'),
                embeddedImageStyle: QrEmbeddedImageStyle(
                  size: Size(size * 0.2, size * 0.2),
                ),
              ),
            ),
          ],
        ),

        // UPI ID information (only shown if UPI ID is available)
        if (upiId.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 12.0),
            child: Column(
              children: [
                const Text(
                  'UPI ID',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  upiId,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'Scan with any UPI app to pay',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
          ),
      ],
    );

    // If a repaint boundary key is provided, wrap the QR code in a RepaintBoundary
    if (repaintBoundaryKey != null) {
      return RepaintBoundary(
        key: repaintBoundaryKey,
        child: qrCodeWidget,
      );
    }

    return qrCodeWidget;
  }
}
