import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';
import 'package:shivish/shared/providers/settings_provider.dart';
import 'package:shivish/shared/providers/notification_provider.dart';
import 'package:shivish/apps/technician/screens/settings/phone_number_settings_screen.dart';
import 'package:shivish/apps/technician/screens/settings/email_settings_screen.dart';
import 'package:shivish/apps/technician/screens/settings/recovery_codes_screen.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

class TwoFactorSettingsScreen extends ConsumerStatefulWidget {
  const TwoFactorSettingsScreen({super.key});

  @override
  ConsumerState<TwoFactorSettingsScreen> createState() =>
      _TwoFactorSettingsScreenState();
}

class _TwoFactorSettingsScreenState
    extends ConsumerState<TwoFactorSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _verificationCodeController = TextEditingController();
  bool _showVerificationInput = false;
  bool _isLoading = false;
  String? _currentPhoneNumber;
  String? _verificationCode;

  @override
  void initState() {
    super.initState();
    _showVerificationInput = ref.read(twoFactorEnabledProvider);
    _loadCurrentPhoneNumber();
  }

  @override
  void dispose() {
    _verificationCodeController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentPhoneNumber() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user != null) {
        setState(() => _currentPhoneNumber = user.phone);
      }
    } catch (e) {
      debugPrint('Error loading phone number: $e');
    }
  }

  Future<void> _resendVerificationCode() async {
    if (_currentPhoneNumber == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please set up your phone number first'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    setState(() => _isLoading = true);
    try {
      final notificationService = ref.read(notificationServiceProvider);

      // Generate a random 6-digit code
      final verificationCode =
          DateTime.now().millisecondsSinceEpoch.toString().substring(0, 6);
      setState(() => _verificationCode = verificationCode);

      // Send verification code via SMS
      await notificationService.sendSms(
        phoneNumber: _currentPhoneNumber!,
        message: 'Your verification code is: $verificationCode',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Verification code sent successfully'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send code: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _verifyAndEnable2FA() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      final enteredCode = _verificationCodeController.text;

      if (_verificationCode == null) {
        throw Exception('Please request a verification code first');
      }

      if (enteredCode != _verificationCode) {
        throw Exception('Invalid verification code');
      }

      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      // Enable 2FA in the backend by updating user document in hybrid storage
      final databaseService = ref.read(databaseServiceProvider);
      await databaseService.update('technicians', user.id, {
        'security_settings': {
          'two_factor_enabled': true,
          'sms_verification': true,
          'phone_number': _currentPhoneNumber,
          'last_updated': DateTime.now().toIso8601String(),
        },
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Update local state
      await ref
          .read(twoFactorEnabledProvider.notifier)
          .setTwoFactorEnabled(true);
      await ref.read(smsVerificationProvider.notifier).setSmsVerification(true);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Two-factor authentication enabled successfully'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to enable 2FA: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isTwoFactorEnabled = ref.watch(twoFactorEnabledProvider);
    final setTwoFactorEnabled =
        ref.read(twoFactorEnabledProvider.notifier).setTwoFactorEnabled;

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Two-Factor Authentication',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Two-Factor Authentication',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Switch(
                          value: isTwoFactorEnabled,
                          onChanged: (value) async {
                            if (!value) {
                              await setTwoFactorEnabled(false);
                              if (mounted) {
                                setState(() => _showVerificationInput = false);
                              }
                            } else {
                              setState(() => _showVerificationInput = true);
                            }
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Add an extra layer of security to your account by enabling two-factor authentication.',
                      style: TextStyle(color: Colors.grey),
                    ),
                    if (_currentPhoneNumber != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Current phone number: $_currentPhoneNumber',
                        style: const TextStyle(
                          color: Colors.grey,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            if (_showVerificationInput) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Verification',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Enter the verification code sent to your phone number.',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 16),
                        AppTextField(
                          controller: _verificationCodeController,
                          label: 'Verification Code',
                          keyboardType: TextInputType.number,
                          prefixIcon: const Icon(Icons.lock),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter the verification code';
                            }
                            if (value.length != 6) {
                              return 'Code must be 6 digits';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextButton(
                              onPressed:
                                  _isLoading ? null : _resendVerificationCode,
                              child: _isLoading
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : const Text('Resend Code'),
                            ),
                            AppButton.primary(
                              label: 'Verify',
                              onPressed:
                                  _isLoading ? null : _verifyAndEnable2FA,
                              isLoading: _isLoading,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
            if (isTwoFactorEnabled) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Recovery Options',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ListTile(
                        leading: const Icon(Icons.phone),
                        title: const Text('Phone Number'),
                        subtitle: Text(
                          _currentPhoneNumber ?? 'Not set',
                          style: const TextStyle(color: Colors.grey),
                        ),
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const PhoneNumberSettingsScreen(),
                            ),
                          );
                        },
                      ),
                      const Divider(),
                      ListTile(
                        leading: const Icon(Icons.email),
                        title: const Text('Email'),
                        subtitle: const Text('Use your email for verification'),
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const EmailSettingsScreen(),
                            ),
                          );
                        },
                      ),
                      const Divider(),
                      ListTile(
                        leading: const Icon(Icons.security),
                        title: const Text('Recovery Codes'),
                        subtitle: const Text('Generate backup recovery codes'),
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const RecoveryCodesScreen(),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
