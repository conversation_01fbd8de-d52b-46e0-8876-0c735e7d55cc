import 'package:freezed_annotation/freezed_annotation.dart';

part 'analytics_model.freezed.dart';
part 'analytics_model.g.dart';

@freezed
sealed class AnalyticsData with _$AnalyticsData {
  const factory AnalyticsData({
    required double totalSales,
    required int totalOrders,
    required double averageOrderValue,
    required List<SalesDataPoint> salesData,
    required List<TopProduct> topProducts,
    required CustomerMetrics customerMetrics,
  }) = _AnalyticsData;

  factory AnalyticsData.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsDataFromJson(json);
}

@freezed
sealed class SalesDataPoint with _$SalesDataPoint {
  const factory SalesDataPoint({
    required String date,
    required double amount,
  }) = _SalesDataPoint;

  factory SalesDataPoint.fromJson(Map<String, dynamic> json) =>
      _$SalesDataPointFromJson(json);
}

@freezed
sealed class TopProduct with _$TopProduct {
  const factory TopProduct({
    required String id,
    required String name,
    required String imageUrl,
    required int quantity,
    required double revenue,
  }) = _TopProduct;

  factory TopProduct.fromJson(Map<String, dynamic> json) =>
      _$TopProductFromJson(json);
}

@freezed
sealed class CustomerMetrics with _$CustomerMetrics {
  const factory CustomerMetrics({
    required int newCustomers,
    required int repeatCustomers,
    required double customerRetentionRate,
    required double customerSatisfaction,
  }) = _CustomerMetrics;

  factory CustomerMetrics.fromJson(Map<String, dynamic> json) =>
      _$CustomerMetricsFromJson(json);
}
