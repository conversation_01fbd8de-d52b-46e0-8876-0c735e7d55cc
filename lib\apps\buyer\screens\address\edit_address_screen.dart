import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/address/address_model.dart';
import '../../../../shared/utils/india_data.dart';
import '../../providers/address_provider.dart';
import '../../buyer_routes.dart';
import '../../../../shared/ui_components/navigation/bottom_nav_back_handler.dart';

class EditAddressScreen extends ConsumerStatefulWidget {
  final String addressId;

  const EditAddressScreen({
    super.key,
    required this.addressId,
  });

  @override
  ConsumerState<EditAddressScreen> createState() => _EditAddressScreenState();
}

class _EditAddressScreenState extends ConsumerState<EditAddressScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _streetController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _postalCodeController = TextEditingController();
  final _countryController = TextEditingController();
  final _phoneController = TextEditingController();
  bool _isDefault = false;
  AddressModel? _originalAddress;

  @override
  void initState() {
    super.initState();
    _loadAddress();
  }

  Future<void> _loadAddress() async {
    final addresses = await ref.read(userAddressesProvider.future);
    final address = addresses.firstWhere(
      (a) => a.id == widget.addressId,
      orElse: () => throw Exception('Address not found'),
    );

    setState(() {
      _originalAddress = address;
      _nameController.text = address.name;
      _streetController.text = address.street;
      _cityController.text = address.city;
      _stateController.text = address.state;
      _postalCodeController.text = address.postalCode;
      _countryController.text = address.country;
      _phoneController.text = address.phone ?? '';
      _isDefault = address.isDefault;
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _streetController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _postalCodeController.dispose();
    _countryController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final addressState = ref.watch(addressProvider);
    final theme = Theme.of(context);

    if (_originalAddress == null) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return BottomNavBackHandler(
      fallbackRoute: BuyerRoutes.addresses,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Edit Address'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              } else {
                context.go(BuyerRoutes.addresses);
              }
            },
          ),
        ),
        body: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Full Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _streetController,
                decoration: const InputDecoration(
                  labelText: 'Street Address',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your street address';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _cityController,
                decoration: const InputDecoration(
                  labelText: 'City',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your city';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // State dropdown for Indian states
              DropdownButtonFormField<String>(
                value: _stateController.text.isEmpty ? null : _stateController.text,
                decoration: const InputDecoration(
                  labelText: 'State',
                  border: OutlineInputBorder(),
                ),
                items: IndiaData.states.map((state) {
                  return DropdownMenuItem<String>(
                    value: state,
                    child: Text(state),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _stateController.text = value;
                    });
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select your state';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // PIN code with validation for Indian PIN codes (6 digits)
              TextFormField(
                controller: _postalCodeController,
                decoration: const InputDecoration(
                  labelText: 'PIN Code',
                  border: OutlineInputBorder(),
                  hintText: '6-digit PIN code',
                ),
                keyboardType: TextInputType.number,
                maxLength: 6,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your PIN code';
                  }
                  if (value.length != 6 || !RegExp(r'^[0-9]{6}$').hasMatch(value)) {
                    return 'Please enter a valid 6-digit PIN code';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 8),
              // Country field (read-only, set to India)
              TextFormField(
                controller: _countryController,
                decoration: const InputDecoration(
                  labelText: 'Country',
                  border: OutlineInputBorder(),
                ),
                readOnly: true, // Make it read-only
                enabled: false, // Disable the field
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'Phone Number (Optional)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('Set as Default Address'),
                value: _isDefault,
                onChanged: (value) {
                  setState(() {
                    _isDefault = value;
                  });
                },
              ),
              const SizedBox(height: 24),
              addressState.when(
                data: (_) => ElevatedButton(
                  onPressed: _saveAddress,
                  child: const Text('Save Changes'),
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => Column(
                  children: [
                    Text(
                      'Error: $error',
                      style: TextStyle(color: theme.colorScheme.error),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _saveAddress,
                      child: const Text('Try Again'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveAddress() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        final updatedAddress = _originalAddress!.copyWith(
          name: _nameController.text,
          street: _streetController.text,
          city: _cityController.text,
          state: _stateController.text,
          postalCode: _postalCodeController.text,
          country: _countryController.text,
          phone: _phoneController.text.isEmpty ? null : _phoneController.text,
          isDefault: _isDefault,
          updatedAt: DateTime.now(),
        );

        await ref.read(addressProvider.notifier).updateAddress(updatedAddress);

        if (_isDefault && mounted) {
          await ref
              .read(addressProvider.notifier)
              .setDefaultAddress(updatedAddress.id);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Address updated successfully')),
          );
          BuyerRoutes.navigateToAddresses(context);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error updating address: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }
}
