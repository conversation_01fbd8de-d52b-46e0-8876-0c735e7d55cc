import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/domain/models/campaign_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/marketing_cubit.dart';
import 'package:shivish/shared/utils/date_formatter.dart';
import 'package:uuid/uuid.dart';

class CampaignForm extends StatefulWidget {
  final String? campaignId;

  const CampaignForm({super.key, this.campaignId});

  @override
  State<CampaignForm> createState() => _CampaignFormState();
}

class _CampaignFormState extends State<CampaignForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _budgetController = TextEditingController();
  DateTime? _startDate;
  DateTime? _endDate;
  CampaignType _selectedType = CampaignType.productPromotion;

  @override
  void initState() {
    super.initState();
    if (widget.campaignId != null) {
      _loadCampaign();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _budgetController.dispose();
    super.dispose();
  }

  void _loadCampaign() {
    final cubit = context.read<MarketingCubit>();
    final campaign = cubit.state.campaigns.firstWhere(
      (c) => c.id == widget.campaignId,
      orElse: () => throw Exception('Campaign not found'),
    );

    _nameController.text = campaign.name;
    _descriptionController.text = campaign.description;
    _budgetController.text = campaign.budget.toString();
    _selectedType = campaign.type;
    _startDate = campaign.startDate;
    _endDate = campaign.endDate;
  }

  Future<void> _selectDateRange() async {
    final initialDateRange = DateTimeRange(
      start: _startDate ?? DateTime.now(),
      end: _endDate ?? DateTime.now().add(const Duration(days: 30)),
    );

    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: initialDateRange,
    );

    if (dateRange != null) {
      setState(() {
        _startDate = dateRange.start;
        _endDate = dateRange.end;
      });
    }
  }

  void _submit() {
    if (_formKey.currentState!.validate() &&
        _startDate != null &&
        _endDate != null) {
      final cubit = context.read<MarketingCubit>();
      final now = DateTime.now();

      final campaign = CampaignModel(
        id: widget.campaignId ?? const Uuid().v4(),
        name: _nameController.text,
        description: _descriptionController.text,
        type: _selectedType,
        status: widget.campaignId != null
            ? cubit.state.campaigns
                .firstWhere((c) => c.id == widget.campaignId)
                .status
            : CampaignStatus.draft,
        budget: double.parse(_budgetController.text),
        spent: widget.campaignId != null
            ? cubit.state.campaigns
                .firstWhere((c) => c.id == widget.campaignId)
                .spent
            : 0,
        startDate: _startDate!,
        endDate: _endDate!,
        impressions: widget.campaignId != null
            ? cubit.state.campaigns
                .firstWhere((c) => c.id == widget.campaignId)
                .impressions
            : 0,
        clicks: widget.campaignId != null
            ? cubit.state.campaigns
                .firstWhere((c) => c.id == widget.campaignId)
                .clicks
            : 0,
        conversions: widget.campaignId != null
            ? cubit.state.campaigns
                .firstWhere((c) => c.id == widget.campaignId)
                .conversions
            : 0,
        createdAt: widget.campaignId != null
            ? cubit.state.campaigns
                .firstWhere((c) => c.id == widget.campaignId)
                .createdAt
            : now,
        updatedAt: now,
      );

      if (widget.campaignId != null) {
        cubit.updateCampaign(campaign);
      } else {
        cubit.createCampaign(campaign);
      }

      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                widget.campaignId != null ? 'Edit Campaign' : 'Create Campaign',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Campaign Name',
                  hintText: 'Enter campaign name',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Enter campaign description',
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<CampaignType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Campaign Type',
                ),
                items: CampaignType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(_formatCampaignType(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _budgetController,
                decoration: const InputDecoration(
                  labelText: 'Budget',
                  hintText: 'Enter campaign budget',
                  prefixText: '₹',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a budget';
                  }
                  final number = double.tryParse(value);
                  if (number == null) {
                    return 'Please enter a valid number';
                  }
                  if (number <= 0) {
                    return 'Budget must be greater than 0';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              ListTile(
                title: Text(
                  _startDate != null && _endDate != null
                      ? '${DateFormatter.formatDate(_startDate!)} - ${DateFormatter.formatDate(_endDate!)}'
                      : 'Select Date Range',
                ),
                trailing: const Icon(Icons.calendar_today),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Theme.of(context).dividerColor),
                ),
                onTap: _selectDateRange,
              ),
              if (_startDate == null || _endDate == null) ...[
                const SizedBox(height: 8),
                Text(
                  'Please select a date range',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.error,
                    fontSize: 12,
                  ),
                ),
              ],
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  FilledButton(
                    onPressed: _submit,
                    child: Text(
                      widget.campaignId != null ? 'Update' : 'Create',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatCampaignType(CampaignType type) {
    switch (type) {
      case CampaignType.productPromotion:
        return 'Product Promotion';
      case CampaignType.storePromotion:
        return 'Store Promotion';
      case CampaignType.seasonalSale:
        return 'Seasonal Sale';
      case CampaignType.flashSale:
        return 'Flash Sale';
    }
  }
}
