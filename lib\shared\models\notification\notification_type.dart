import 'package:json_annotation/json_annotation.dart';

/// Enum representing different types of notifications
enum NotificationType {
  @JsonValue('general')
  general,
  @JsonValue('order')
  order,
  @JsonValue('payment')
  payment,
  @JsonValue('booking')
  booking,
  @JsonValue('event')
  event,
  @JsonValue('chat')
  chat,
  @JsonValue('system')
  system,
  @JsonValue('verification')
  verification,
  @JsonValue('delivery_request')
  deliveryRequest,
  @JsonValue('status_update')
  statusUpdate,
  @JsonValue('earnings')
  earnings,
  // Buyer-specific notification types
  @JsonValue('healthcare_reminder')
  healthcareReminder,
  @JsonValue('daily_horoscope')
  dailyHoroscope,
  @JsonValue('panchangam')
  panchangam,
  @JsonValue('shopping_list_alert')
  shoppingListAlert,
  @JsonValue('safety_tracking')
  safetyTracking,
  @JsonValue('friend_tracking')
  friendTracking,
  @JsonValue('alarm_notification')
  alarmNotification,
  @JsonValue('calendar_reminder')
  calendarReminder,
  @JsonValue('promotions')
  promotions,
  @JsonValue('festival_reminder')
  festivalReminder,
  @JsonValue('price_alert')
  priceAlert,
  @JsonValue('low_stock_alert')
  lowStockAlert,
  @JsonValue('ride_booking')
  rideBooking,
  @JsonValue('ticket_booking')
  ticketBooking,
  @JsonValue('medicine_order')
  medicineOrder,
  @JsonValue('doctor_consultation')
  doctorConsultation,
  @JsonValue('ai_assistant')
  aiAssistant,
  @JsonValue('technician_booking')
  technicianBooking,
  @JsonValue('priest_booking')
  priestBooking,
}
