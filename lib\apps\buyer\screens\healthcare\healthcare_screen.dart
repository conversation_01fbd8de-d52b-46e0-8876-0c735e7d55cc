import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/buyer/buyer_routes.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import '../../widgets/section_title.dart';
import 'providers/healthcare_providers.dart';
import 'healthcare_back_handler.dart';

class HealthcareScreen extends ConsumerStatefulWidget {
  const HealthcareScreen({super.key});

  @override
  ConsumerState<HealthcareScreen> createState() => _HealthcareScreenState();
}

class _HealthcareScreenState extends ConsumerState<HealthcareScreen> {
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return HealthcareBackHandler(
      child: Scaffold(
      appBar: AppBar(
        title: const Text('Healthcare'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we can pop to previous screen
            if (context.canPop()) {
              context.pop();
            } else {
              // If we can't pop, navigate to home
              context.go(BuyerRoutes.home);
            }
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search bar
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search doctors, specialties, hospitals...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 16),
                ),
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    context.push('/buyer/healthcare/search?query=$value');
                  }
                },
              ),
            ),

            // Quick actions
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SectionTitle(
                title: 'Quick Actions',
                actionText: 'See All',
                onActionTap: () {
                  context.push('/buyer/healthcare/services');
                },
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 120,
              child: ListView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                children: [
                  _buildQuickActionCard(
                    title: 'Find Doctor',
                    icon: Icons.medical_services,
                    color: Colors.blue,
                    onTap: () {
                      context.push('/buyer/healthcare/doctors');
                    },
                  ),
                  _buildQuickActionCard(
                    title: 'Book Lab Test',
                    icon: Icons.science,
                    color: Colors.orange,
                    onTap: () {
                      context.push('/buyer/healthcare/lab-tests');
                    },
                  ),
                  _buildQuickActionCard(
                    title: 'Buy Medicine',
                    icon: Icons.medication,
                    color: Colors.green,
                    onTap: () {
                      context.push('/buyer/healthcare/medicines');
                    },
                  ),
                  _buildQuickActionCard(
                    title: 'My Appointments',
                    icon: Icons.calendar_today,
                    color: Colors.purple,
                    onTap: () {
                      context.push(BuyerRoutes.healthcareAppointments);
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Top specialties
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SectionTitle(
                title: 'Top Specialties',
                actionText: 'View All',
                onActionTap: () {
                  context.push('/buyer/healthcare/specialties');
                },
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 120,
              child: ListView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                children: [
                  _buildSpecialtyCard(
                    title: 'Cardiology',
                    icon: Icons.favorite,
                    color: Colors.red,
                    onTap: () {
                      context.push('${BuyerRoutes.healthcareDoctors}?specialty=cardiologist');
                    },
                  ),
                  _buildSpecialtyCard(
                    title: 'Dermatology',
                    icon: Icons.face,
                    color: Colors.amber,
                    onTap: () {
                      context.push('${BuyerRoutes.healthcareDoctors}?specialty=dermatologist');
                    },
                  ),
                  _buildSpecialtyCard(
                    title: 'Orthopedics',
                    icon: Icons.accessibility_new,
                    color: Colors.blue,
                    onTap: () {
                      context.push('${BuyerRoutes.healthcareDoctors}?specialty=orthopedic');
                    },
                  ),
                  _buildSpecialtyCard(
                    title: 'Pediatrics',
                    icon: Icons.child_care,
                    color: Colors.green,
                    onTap: () {
                      context.push('${BuyerRoutes.healthcareDoctors}?specialty=pediatrician');
                    },
                  ),
                  _buildSpecialtyCard(
                    title: 'Neurology',
                    icon: Icons.psychology,
                    color: Colors.purple,
                    onTap: () {
                      context.push('${BuyerRoutes.healthcareDoctors}?specialty=neurologist');
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Top doctors
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SectionTitle(
                title: 'Top Doctors',
                actionText: 'View All',
                onActionTap: () {
                  context.push(BuyerRoutes.healthcareDoctors);
                },
              ),
            ),
            const SizedBox(height: 16),
            _buildTopDoctorsList(),
            const SizedBox(height: 24),

            // Nearby hospitals
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SectionTitle(
                title: 'Nearby Hospitals',
                actionText: 'View All',
                onActionTap: () {
                  context.push('/buyer/healthcare/hospitals');
                },
              ),
            ),
            const SizedBox(height: 16),
            _buildNearbyHospitalsList(),
            const SizedBox(height: 24),

            // Health packages
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SectionTitle(
                title: 'Health Packages',
                actionText: 'View All',
                onActionTap: () {
                  context.push('/buyer/healthcare/packages');
                },
              ),
            ),
            const SizedBox(height: 16),
            _buildHealthPackagesList(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    ),
    );
  }

  Widget _buildQuickActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: 100,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withAlpha(25), // 0.1 opacity
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withAlpha(76)), // 0.3 opacity
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: color,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  color: color.withAlpha(204), // 0.8 opacity
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSpecialtyCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: 100,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withAlpha(25), // 0.1 opacity
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withAlpha(25), // 0.1 opacity
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopDoctorsList() {
    final topDoctors = ref.watch(topDoctorsProvider);

    return topDoctors.when(
      data: (doctors) {
        if (doctors.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 24.0),
              child: Text(
                'No doctors found',
                style: TextStyle(
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          );
        }

        // Show only 3 doctors on home screen
        final displayDoctors = doctors.take(3).toList();

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: displayDoctors.length,
          itemBuilder: (context, index) {
            final doctor = displayDoctors[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: InkWell(
                onTap: () {
                  context.push('${BuyerRoutes.healthcareDoctors}/${doctor.id}');
                },
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundImage: doctor.profileImage.isNotEmpty
                            ? NetworkImage(doctor.profileImage)
                            : const AssetImage('assets/images/default_doctor.png') as ImageProvider,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Dr. ${doctor.name}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              doctor.specialties.isNotEmpty
                                ? doctor.specialties.first.displayName
                                : 'General Physician',
                              style: TextStyle(
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.star,
                                  size: 16,
                                  color: Colors.amber[700],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${doctor.rating} (${doctor.totalReviews})',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Icon(
                                  Icons.location_on,
                                  size: 16,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${2 + index} km away', // Placeholder distance
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          'Book',
                          style: TextStyle(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
      loading: () => const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 24.0),
          child: LoadingIndicator(size: 32),
        ),
      ),
      error: (error, stackTrace) => Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24.0),
          child: ErrorMessage(
            message: 'Failed to load doctors',
            onRetry: () => ref.refresh(topDoctorsProvider),
          ),
        ),
      ),
    );
  }

  Widget _buildNearbyHospitalsList() {
    final nearbyHospitals = ref.watch(nearbyHospitalsProvider);

    return nearbyHospitals.when(
      data: (hospitals) {
        if (hospitals.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 24.0),
              child: Text(
                'No nearby hospitals found',
                style: TextStyle(
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          );
        }

        return SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            itemCount: hospitals.length,
            itemBuilder: (context, index) {
              final hospital = hospitals[index];
              return Container(
                width: 200,
                margin: const EdgeInsets.symmetric(horizontal: 4),
                child: Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: InkWell(
                    onTap: () {
                      context.push('/buyer/healthcare/hospitals/${hospital.id}');
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ClipRRect(
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(12),
                          ),
                          child: Image.network(
                            hospital.images.isNotEmpty
                                ? hospital.images.first
                                : 'https://via.placeholder.com/300x200?text=Hospital',
                            height: 100,
                            width: double.infinity,
                            fit: BoxFit.cover,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                hospital.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    size: 14,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      '${2 + index} km away', // Placeholder distance
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 24.0),
          child: LoadingIndicator(size: 32),
        ),
      ),
      error: (error, stackTrace) => Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24.0),
          child: ErrorMessage(
            message: 'Failed to load nearby hospitals',
            onRetry: () => ref.refresh(nearbyHospitalsProvider),
          ),
        ),
      ),
    );
  }

  Widget _buildHealthPackagesList() {
    final healthPackages = ref.watch(healthPackagesProvider);

    return healthPackages.when(
      data: (packages) {
        if (packages.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 24.0),
              child: Text(
                'No health packages found',
                style: TextStyle(
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          );
        }

        return SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            itemCount: packages.length,
            itemBuilder: (context, index) {
              final package = packages[index];
              // Extract values from the Map
              final String id = package['id'] ?? '';
              final String title = package['name'] ?? 'Health Package';
              final int tests = package['testCount'] ?? 0;
              final double price = (package['price'] ?? 0.0).toDouble();
              final double discount = (package['originalPrice'] ?? 0.0).toDouble();
              final Color packageColor = Colors.blue;

              return Container(
                width: 220,
                margin: const EdgeInsets.symmetric(horizontal: 4),
                child: Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: InkWell(
                    onTap: () {
                      context.push('/buyer/healthcare/packages/$id');
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: packageColor.withAlpha(25), // 0.1 opacity
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.medical_services,
                              color: packageColor,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            title,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '$tests Tests',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          const Spacer(),
                          Row(
                            children: [
                              Text(
                                '₹$price',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '₹$discount',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                  decoration: TextDecoration.lineThrough,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 24.0),
          child: LoadingIndicator(size: 32),
        ),
      ),
      error: (error, stackTrace) => Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24.0),
          child: ErrorMessage(
            message: 'Failed to load health packages',
            onRetry: () => ref.refresh(healthPackagesProvider),
          ),
        ),
      ),
    );
  }
}

