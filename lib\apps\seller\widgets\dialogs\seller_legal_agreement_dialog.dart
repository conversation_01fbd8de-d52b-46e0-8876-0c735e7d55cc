import 'package:flutter/material.dart';

/// A dialog that presents the legal agreement for sellers
class SellerLegalAgreementDialog extends StatefulWidget {
  /// Creates a [SellerLegalAgreementDialog]
  const SellerLegalAgreementDialog({super.key});

  @override
  State<SellerLegalAgreementDialog> createState() =>
      _SellerLegalAgreementDialogState();

  /// Shows the legal agreement dialog and returns whether the seller accepted
  static Future<bool?> show(BuildContext context) {
    debugPrint('SellerLegalAgreementDialog: Showing dialog');
    return showDialog<bool>(
      context: context,
      barrierDismissible: false, // User must respond to the dialog
      builder: (context) => const SellerLegalAgreementDialog(),
    ).then((result) {
      debugPrint('SellerLegalAgreementDialog: Dialog result: $result');
      return result;
    }).catchError((error) {
      debugPrint('SellerLegalAgreementDialog: Error showing dialog: $error');
      return false;
    });
  }
}

class _SellerLegalAgreementDialogState
    extends State<SellerLegalAgreementDialog> {
  bool _hasReadTerms = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      title: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.gavel,
            size: 40,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 8),
          Text(
            'Seller Legal Agreement',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'By using the SHIVISH platform as a seller, you agree to the following legally binding terms and conditions:',
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSection(
              title: '1. PRODUCT LEGALITY AND COMPLIANCE',
              content:
                  'Seller warrants and represents that all goods listed for sale on the SHIVISH platform ("Products") are legal under all applicable local, state, and national laws and regulations. Seller shall not list, advertise, or sell any Products that are prohibited, restricted, or regulated without proper authorization. Seller assumes full responsibility for ensuring compliance with all relevant laws, including but not limited to consumer protection laws, product safety regulations, and food and drug regulations where applicable.',
            ),
            _buildSection(
              title: '2. PRODUCT DESCRIPTION AND REPRESENTATION',
              content:
                  'Seller agrees to provide accurate, complete, and truthful descriptions of all Products, including their specifications, ingredients, usage instructions, and any potential risks or warnings. Misrepresentation of Products, including false claims about their benefits, origin, or composition, is strictly prohibited and may result in immediate termination of Seller\'s account and potential legal action.',
            ),
            _buildSection(
              title: '3. DELIVERY OBLIGATIONS',
              content:
                  'Seller hereby commits to fulfill all orders placed by buyers in accordance with the delivery timeframes specified at the time of purchase. Seller acknowledges that the delivery timeframe constitutes a material term of the sales contract with the buyer. In the event that Seller anticipates any delay in delivery, Seller shall promptly notify both the buyer and SHIVISH platform administrators, providing a revised estimated delivery time. Repeated failure to meet delivery commitments may result in penalties, including but not limited to account suspension, negative impact on Seller ratings, and financial penalties as outlined in the Seller Penalty Schedule.',
            ),
            _buildSection(
              title: '4. PRODUCT QUALITY AND CONDITION',
              content:
                  'Seller guarantees that all Products delivered to buyers will be of merchantable quality, fit for their intended purpose, and in the condition as represented in the Product listing. Products must be properly packaged to prevent damage during transit. Seller shall not deliver Products that are damaged, defective, expired, or otherwise not in conformity with the Product listing unless such conditions were clearly disclosed to and accepted by the buyer prior to purchase.',
            ),
            _buildSection(
              title: '5. COMPLETE ORDER FULFILLMENT OBLIGATION',
              content:
                  'Seller expressly agrees to provide ALL legal goods requested by the buyer at the time of delivery, even if such items were not available in Seller\'s inventory at the time the order was placed. Seller acknowledges that this is a material obligation and commits to making all reasonable efforts to source and procure any missing items to ensure complete order fulfillment. If any requested item cannot be provided despite best efforts, Seller must notify the buyer at least 24 hours before the scheduled delivery time, providing a detailed explanation and offering suitable alternatives or compensation. Repeated failure to fulfill complete orders may result in penalties including account suspension, financial penalties, or termination of Seller privileges on the SHIVISH platform.',
            ),
            _buildSection(
              title: '6. CANCELLATION AND REFUND POLICY',
              content:
                  'Seller agrees to comply with the SHIVISH platform\'s cancellation and refund policies. In cases where Products cannot be delivered as promised, Seller shall promptly process cancellations and refunds in accordance with platform policies. Seller acknowledges that failure to process legitimate refunds may result in SHIVISH processing the refund directly and deducting the amount from Seller\'s account, plus applicable administrative fees.',
            ),
            _buildSection(
              title: '7. RECORD KEEPING',
              content:
                  'Seller shall maintain accurate records of all transactions, including order details, payment information, shipping documentation, and communication with buyers, for a minimum period of three (3) years. Such records shall be made available to SHIVISH upon request for the purpose of resolving disputes or addressing regulatory inquiries.',
            ),
            _buildSection(
              title: '8. DISPUTE RESOLUTION',
              content:
                  'In the event of any dispute with a buyer regarding Product quality, delivery, or other aspects of the transaction, Seller agrees to make good faith efforts to resolve such disputes promptly and amicably. If the dispute cannot be resolved directly with the buyer, Seller agrees to participate in SHIVISH\'s dispute resolution process and to be bound by its outcome.',
            ),
            _buildSection(
              title: '9. INDEMNIFICATION',
              content:
                  'Seller agrees to indemnify, defend, and hold harmless SHIVISH, its affiliates, officers, directors, employees, and agents from and against any and all claims, liabilities, damages, losses, costs, expenses, or fees (including reasonable attorneys\' fees) arising from or relating to: (a) Seller\'s violation of this Agreement; (b) Seller\'s failure to comply with applicable laws and regulations; (c) the Products sold by Seller; (d) Seller\'s misrepresentation of Products; or (e) Seller\'s violation of any rights of a third party.',
            ),
            _buildSection(
              title: '10. PENALTIES FOR NON-COMPLIANCE',
              content:
                  'Seller acknowledges that failure to comply with this Agreement may result in penalties, including but not limited to: (a) temporary or permanent suspension of Seller\'s account; (b) forfeiture of pending payments; (c) removal of Product listings; (d) negative impact on Seller ratings; (e) financial penalties; and (f) legal action. The application of any such penalties shall be at SHIVISH\'s sole discretion based on the nature and severity of the non-compliance.',
            ),
            _buildSection(
              title: '11. GOVERNING LAW',
              content:
                  'This Agreement shall be governed by and construed in accordance with the laws of India, without regard to its conflict of law provisions. Any disputes arising under or in connection with this Agreement shall be subject to the exclusive jurisdiction of the courts located within the jurisdiction of the registered office of SHIVISH.',
            ),
            const SizedBox(height: 16),
            Text(
              'By clicking "I Accept" below, you acknowledge that you have read, understood, and agree to be bound by all the terms and conditions of this Agreement. This Agreement constitutes a legally binding contract between you and SHIVISH.',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: _hasReadTerms,
                  onChanged: (value) {
                    setState(() {
                      _hasReadTerms = value ?? false;
                    });
                  },
                  activeColor: theme.colorScheme.primary,
                ),
                Expanded(
                  child: Text(
                    'I have read, understood, and agree to be bound by all the terms and conditions of this Agreement.',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            debugPrint('SellerLegalAgreementDialog: Decline button pressed');
            // Return false to indicate rejection
            Navigator.of(context).pop(false);
            debugPrint(
                'SellerLegalAgreementDialog: Dialog closed with rejection');
          },
          style: TextButton.styleFrom(
            foregroundColor: Colors.red,
          ),
          child: const Text('Decline'),
        ),
        ElevatedButton(
          onPressed: _hasReadTerms
              ? () {
                  debugPrint(
                      'SellerLegalAgreementDialog: Accept button pressed');
                  // Return true to indicate acceptance
                  Navigator.of(context).pop(true);
                  debugPrint(
                      'SellerLegalAgreementDialog: Dialog closed with acceptance');
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: Colors.white,
            disabledBackgroundColor: Colors.grey.shade300,
          ),
          child: const Text('I Accept'),
        ),
      ],
    );
  }

  Widget _buildSection({required String title, required String content}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          content,
          style: const TextStyle(fontSize: 13),
        ),
      ],
    );
  }
}
