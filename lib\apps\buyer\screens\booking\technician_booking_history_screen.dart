import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/booking/booking_model.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/booking_provider.dart';
import 'technician_booking_details_screen.dart';

class TechnicianBookingHistoryScreen extends ConsumerWidget {
  const TechnicianBookingHistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    final user = authState;
    // User will always be signed in when accessing this screen
    final bookingsAsync =
        ref.watch(bookingStateNotifierProvider(user?.id ?? ''));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking History'),
      ),
      body: bookingsAsync.when(
        data: (bookings) {
          final technicianBookings = bookings
              .where((booking) => booking.type == BookingType.technician)
              .toList();

          if (technicianBookings.isEmpty) {
            return const Center(
              child: Text('No booking history found'),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: technicianBookings.length,
            itemBuilder: (context, index) {
              final booking = technicianBookings[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: InkWell(
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => TechnicianBookingDetailsScreen(
                          bookingId: booking.id,
                        ),
                      ),
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Booking #${booking.bookingNumber}',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            _buildStatusChip(context, booking.status),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Service: ${booking.services.join(", ")}',
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Provider: ${booking.providerName}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Date: ${_formatDate(booking.bookingDate)}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Time: ${_formatTime(booking.startTime)} - ${_formatTime(booking.endTime)}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Total: ₹${booking.totalAmount.toStringAsFixed(2)}',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            _buildPaymentStatusChip(
                                context, booking.paymentStatus),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, BookingStatus status) {
    Color color;
    String text;

    switch (status) {
      case BookingStatus.pending:
        color = Colors.orange;
        text = 'Pending';
        break;
      case BookingStatus.confirmed:
        color = Colors.blue;
        text = 'Confirmed';
        break;
      case BookingStatus.inProgress:
        color = Colors.purple;
        text = 'In Progress';
        break;
      case BookingStatus.completed:
        color = Colors.green;
        text = 'Completed';
        break;
      case BookingStatus.cancelled:
        color = Colors.red;
        text = 'Cancelled';
        break;
      case BookingStatus.rejected:
        color = Colors.red;
        text = 'Rejected';
        break;
      case BookingStatus.rescheduled:
        color = Colors.orange;
        text = 'Rescheduled';
        break;
      case BookingStatus.noShow:
        color = Colors.red;
        text = 'No Show';
        break;
    }

    return Chip(
      label: Text(
        text,
        style: const TextStyle(color: Colors.white),
      ),
      backgroundColor: color,
    );
  }

  Widget _buildPaymentStatusChip(BuildContext context, PaymentStatus status) {
    Color color;
    String text;

    switch (status) {
      case PaymentStatus.pending:
        color = Colors.orange;
        text = 'Pending';
        break;
      case PaymentStatus.completed:
        color = Colors.green;
        text = 'Completed';
        break;
      case PaymentStatus.failed:
        color = Colors.red;
        text = 'Failed';
        break;
      case PaymentStatus.refunded:
        color = Colors.blue;
        text = 'Refunded';
        break;
      case PaymentStatus.partiallyRefunded:
        color = Colors.blue;
        text = 'Partially Refunded';
        break;
    }

    return Chip(
      label: Text(
        text,
        style: const TextStyle(color: Colors.white),
      ),
      backgroundColor: color,
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
