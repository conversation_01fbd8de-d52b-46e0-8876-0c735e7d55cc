// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'analytics_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AnalyticsData {

 double get totalSales; int get totalOrders; double get averageOrderValue; List<SalesDataPoint> get salesData; List<TopProduct> get topProducts; CustomerMetrics get customerMetrics;
/// Create a copy of AnalyticsData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AnalyticsDataCopyWith<AnalyticsData> get copyWith => _$AnalyticsDataCopyWithImpl<AnalyticsData>(this as AnalyticsData, _$identity);

  /// Serializes this AnalyticsData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AnalyticsData&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.averageOrderValue, averageOrderValue) || other.averageOrderValue == averageOrderValue)&&const DeepCollectionEquality().equals(other.salesData, salesData)&&const DeepCollectionEquality().equals(other.topProducts, topProducts)&&(identical(other.customerMetrics, customerMetrics) || other.customerMetrics == customerMetrics));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalSales,totalOrders,averageOrderValue,const DeepCollectionEquality().hash(salesData),const DeepCollectionEquality().hash(topProducts),customerMetrics);

@override
String toString() {
  return 'AnalyticsData(totalSales: $totalSales, totalOrders: $totalOrders, averageOrderValue: $averageOrderValue, salesData: $salesData, topProducts: $topProducts, customerMetrics: $customerMetrics)';
}


}

/// @nodoc
abstract mixin class $AnalyticsDataCopyWith<$Res>  {
  factory $AnalyticsDataCopyWith(AnalyticsData value, $Res Function(AnalyticsData) _then) = _$AnalyticsDataCopyWithImpl;
@useResult
$Res call({
 double totalSales, int totalOrders, double averageOrderValue, List<SalesDataPoint> salesData, List<TopProduct> topProducts, CustomerMetrics customerMetrics
});


$CustomerMetricsCopyWith<$Res> get customerMetrics;

}
/// @nodoc
class _$AnalyticsDataCopyWithImpl<$Res>
    implements $AnalyticsDataCopyWith<$Res> {
  _$AnalyticsDataCopyWithImpl(this._self, this._then);

  final AnalyticsData _self;
  final $Res Function(AnalyticsData) _then;

/// Create a copy of AnalyticsData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalSales = null,Object? totalOrders = null,Object? averageOrderValue = null,Object? salesData = null,Object? topProducts = null,Object? customerMetrics = null,}) {
  return _then(_self.copyWith(
totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,averageOrderValue: null == averageOrderValue ? _self.averageOrderValue : averageOrderValue // ignore: cast_nullable_to_non_nullable
as double,salesData: null == salesData ? _self.salesData : salesData // ignore: cast_nullable_to_non_nullable
as List<SalesDataPoint>,topProducts: null == topProducts ? _self.topProducts : topProducts // ignore: cast_nullable_to_non_nullable
as List<TopProduct>,customerMetrics: null == customerMetrics ? _self.customerMetrics : customerMetrics // ignore: cast_nullable_to_non_nullable
as CustomerMetrics,
  ));
}
/// Create a copy of AnalyticsData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CustomerMetricsCopyWith<$Res> get customerMetrics {
  
  return $CustomerMetricsCopyWith<$Res>(_self.customerMetrics, (value) {
    return _then(_self.copyWith(customerMetrics: value));
  });
}
}


/// Adds pattern-matching-related methods to [AnalyticsData].
extension AnalyticsDataPatterns on AnalyticsData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AnalyticsData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AnalyticsData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AnalyticsData value)  $default,){
final _that = this;
switch (_that) {
case _AnalyticsData():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AnalyticsData value)?  $default,){
final _that = this;
switch (_that) {
case _AnalyticsData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double totalSales,  int totalOrders,  double averageOrderValue,  List<SalesDataPoint> salesData,  List<TopProduct> topProducts,  CustomerMetrics customerMetrics)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AnalyticsData() when $default != null:
return $default(_that.totalSales,_that.totalOrders,_that.averageOrderValue,_that.salesData,_that.topProducts,_that.customerMetrics);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double totalSales,  int totalOrders,  double averageOrderValue,  List<SalesDataPoint> salesData,  List<TopProduct> topProducts,  CustomerMetrics customerMetrics)  $default,) {final _that = this;
switch (_that) {
case _AnalyticsData():
return $default(_that.totalSales,_that.totalOrders,_that.averageOrderValue,_that.salesData,_that.topProducts,_that.customerMetrics);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double totalSales,  int totalOrders,  double averageOrderValue,  List<SalesDataPoint> salesData,  List<TopProduct> topProducts,  CustomerMetrics customerMetrics)?  $default,) {final _that = this;
switch (_that) {
case _AnalyticsData() when $default != null:
return $default(_that.totalSales,_that.totalOrders,_that.averageOrderValue,_that.salesData,_that.topProducts,_that.customerMetrics);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AnalyticsData implements AnalyticsData {
  const _AnalyticsData({required this.totalSales, required this.totalOrders, required this.averageOrderValue, required final  List<SalesDataPoint> salesData, required final  List<TopProduct> topProducts, required this.customerMetrics}): _salesData = salesData,_topProducts = topProducts;
  factory _AnalyticsData.fromJson(Map<String, dynamic> json) => _$AnalyticsDataFromJson(json);

@override final  double totalSales;
@override final  int totalOrders;
@override final  double averageOrderValue;
 final  List<SalesDataPoint> _salesData;
@override List<SalesDataPoint> get salesData {
  if (_salesData is EqualUnmodifiableListView) return _salesData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_salesData);
}

 final  List<TopProduct> _topProducts;
@override List<TopProduct> get topProducts {
  if (_topProducts is EqualUnmodifiableListView) return _topProducts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_topProducts);
}

@override final  CustomerMetrics customerMetrics;

/// Create a copy of AnalyticsData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AnalyticsDataCopyWith<_AnalyticsData> get copyWith => __$AnalyticsDataCopyWithImpl<_AnalyticsData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AnalyticsDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AnalyticsData&&(identical(other.totalSales, totalSales) || other.totalSales == totalSales)&&(identical(other.totalOrders, totalOrders) || other.totalOrders == totalOrders)&&(identical(other.averageOrderValue, averageOrderValue) || other.averageOrderValue == averageOrderValue)&&const DeepCollectionEquality().equals(other._salesData, _salesData)&&const DeepCollectionEquality().equals(other._topProducts, _topProducts)&&(identical(other.customerMetrics, customerMetrics) || other.customerMetrics == customerMetrics));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalSales,totalOrders,averageOrderValue,const DeepCollectionEquality().hash(_salesData),const DeepCollectionEquality().hash(_topProducts),customerMetrics);

@override
String toString() {
  return 'AnalyticsData(totalSales: $totalSales, totalOrders: $totalOrders, averageOrderValue: $averageOrderValue, salesData: $salesData, topProducts: $topProducts, customerMetrics: $customerMetrics)';
}


}

/// @nodoc
abstract mixin class _$AnalyticsDataCopyWith<$Res> implements $AnalyticsDataCopyWith<$Res> {
  factory _$AnalyticsDataCopyWith(_AnalyticsData value, $Res Function(_AnalyticsData) _then) = __$AnalyticsDataCopyWithImpl;
@override @useResult
$Res call({
 double totalSales, int totalOrders, double averageOrderValue, List<SalesDataPoint> salesData, List<TopProduct> topProducts, CustomerMetrics customerMetrics
});


@override $CustomerMetricsCopyWith<$Res> get customerMetrics;

}
/// @nodoc
class __$AnalyticsDataCopyWithImpl<$Res>
    implements _$AnalyticsDataCopyWith<$Res> {
  __$AnalyticsDataCopyWithImpl(this._self, this._then);

  final _AnalyticsData _self;
  final $Res Function(_AnalyticsData) _then;

/// Create a copy of AnalyticsData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalSales = null,Object? totalOrders = null,Object? averageOrderValue = null,Object? salesData = null,Object? topProducts = null,Object? customerMetrics = null,}) {
  return _then(_AnalyticsData(
totalSales: null == totalSales ? _self.totalSales : totalSales // ignore: cast_nullable_to_non_nullable
as double,totalOrders: null == totalOrders ? _self.totalOrders : totalOrders // ignore: cast_nullable_to_non_nullable
as int,averageOrderValue: null == averageOrderValue ? _self.averageOrderValue : averageOrderValue // ignore: cast_nullable_to_non_nullable
as double,salesData: null == salesData ? _self._salesData : salesData // ignore: cast_nullable_to_non_nullable
as List<SalesDataPoint>,topProducts: null == topProducts ? _self._topProducts : topProducts // ignore: cast_nullable_to_non_nullable
as List<TopProduct>,customerMetrics: null == customerMetrics ? _self.customerMetrics : customerMetrics // ignore: cast_nullable_to_non_nullable
as CustomerMetrics,
  ));
}

/// Create a copy of AnalyticsData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CustomerMetricsCopyWith<$Res> get customerMetrics {
  
  return $CustomerMetricsCopyWith<$Res>(_self.customerMetrics, (value) {
    return _then(_self.copyWith(customerMetrics: value));
  });
}
}


/// @nodoc
mixin _$SalesDataPoint {

 String get date; double get amount;
/// Create a copy of SalesDataPoint
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SalesDataPointCopyWith<SalesDataPoint> get copyWith => _$SalesDataPointCopyWithImpl<SalesDataPoint>(this as SalesDataPoint, _$identity);

  /// Serializes this SalesDataPoint to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SalesDataPoint&&(identical(other.date, date) || other.date == date)&&(identical(other.amount, amount) || other.amount == amount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,amount);

@override
String toString() {
  return 'SalesDataPoint(date: $date, amount: $amount)';
}


}

/// @nodoc
abstract mixin class $SalesDataPointCopyWith<$Res>  {
  factory $SalesDataPointCopyWith(SalesDataPoint value, $Res Function(SalesDataPoint) _then) = _$SalesDataPointCopyWithImpl;
@useResult
$Res call({
 String date, double amount
});




}
/// @nodoc
class _$SalesDataPointCopyWithImpl<$Res>
    implements $SalesDataPointCopyWith<$Res> {
  _$SalesDataPointCopyWithImpl(this._self, this._then);

  final SalesDataPoint _self;
  final $Res Function(SalesDataPoint) _then;

/// Create a copy of SalesDataPoint
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? date = null,Object? amount = null,}) {
  return _then(_self.copyWith(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [SalesDataPoint].
extension SalesDataPointPatterns on SalesDataPoint {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SalesDataPoint value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SalesDataPoint() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SalesDataPoint value)  $default,){
final _that = this;
switch (_that) {
case _SalesDataPoint():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SalesDataPoint value)?  $default,){
final _that = this;
switch (_that) {
case _SalesDataPoint() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String date,  double amount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SalesDataPoint() when $default != null:
return $default(_that.date,_that.amount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String date,  double amount)  $default,) {final _that = this;
switch (_that) {
case _SalesDataPoint():
return $default(_that.date,_that.amount);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String date,  double amount)?  $default,) {final _that = this;
switch (_that) {
case _SalesDataPoint() when $default != null:
return $default(_that.date,_that.amount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SalesDataPoint implements SalesDataPoint {
  const _SalesDataPoint({required this.date, required this.amount});
  factory _SalesDataPoint.fromJson(Map<String, dynamic> json) => _$SalesDataPointFromJson(json);

@override final  String date;
@override final  double amount;

/// Create a copy of SalesDataPoint
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SalesDataPointCopyWith<_SalesDataPoint> get copyWith => __$SalesDataPointCopyWithImpl<_SalesDataPoint>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SalesDataPointToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SalesDataPoint&&(identical(other.date, date) || other.date == date)&&(identical(other.amount, amount) || other.amount == amount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,date,amount);

@override
String toString() {
  return 'SalesDataPoint(date: $date, amount: $amount)';
}


}

/// @nodoc
abstract mixin class _$SalesDataPointCopyWith<$Res> implements $SalesDataPointCopyWith<$Res> {
  factory _$SalesDataPointCopyWith(_SalesDataPoint value, $Res Function(_SalesDataPoint) _then) = __$SalesDataPointCopyWithImpl;
@override @useResult
$Res call({
 String date, double amount
});




}
/// @nodoc
class __$SalesDataPointCopyWithImpl<$Res>
    implements _$SalesDataPointCopyWith<$Res> {
  __$SalesDataPointCopyWithImpl(this._self, this._then);

  final _SalesDataPoint _self;
  final $Res Function(_SalesDataPoint) _then;

/// Create a copy of SalesDataPoint
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? date = null,Object? amount = null,}) {
  return _then(_SalesDataPoint(
date: null == date ? _self.date : date // ignore: cast_nullable_to_non_nullable
as String,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$TopProduct {

 String get id; String get name; String get imageUrl; int get quantity; double get revenue;
/// Create a copy of TopProduct
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TopProductCopyWith<TopProduct> get copyWith => _$TopProductCopyWithImpl<TopProduct>(this as TopProduct, _$identity);

  /// Serializes this TopProduct to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TopProduct&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.revenue, revenue) || other.revenue == revenue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,imageUrl,quantity,revenue);

@override
String toString() {
  return 'TopProduct(id: $id, name: $name, imageUrl: $imageUrl, quantity: $quantity, revenue: $revenue)';
}


}

/// @nodoc
abstract mixin class $TopProductCopyWith<$Res>  {
  factory $TopProductCopyWith(TopProduct value, $Res Function(TopProduct) _then) = _$TopProductCopyWithImpl;
@useResult
$Res call({
 String id, String name, String imageUrl, int quantity, double revenue
});




}
/// @nodoc
class _$TopProductCopyWithImpl<$Res>
    implements $TopProductCopyWith<$Res> {
  _$TopProductCopyWithImpl(this._self, this._then);

  final TopProduct _self;
  final $Res Function(TopProduct) _then;

/// Create a copy of TopProduct
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? imageUrl = null,Object? quantity = null,Object? revenue = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,revenue: null == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [TopProduct].
extension TopProductPatterns on TopProduct {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TopProduct value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TopProduct() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TopProduct value)  $default,){
final _that = this;
switch (_that) {
case _TopProduct():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TopProduct value)?  $default,){
final _that = this;
switch (_that) {
case _TopProduct() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String imageUrl,  int quantity,  double revenue)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TopProduct() when $default != null:
return $default(_that.id,_that.name,_that.imageUrl,_that.quantity,_that.revenue);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String imageUrl,  int quantity,  double revenue)  $default,) {final _that = this;
switch (_that) {
case _TopProduct():
return $default(_that.id,_that.name,_that.imageUrl,_that.quantity,_that.revenue);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String imageUrl,  int quantity,  double revenue)?  $default,) {final _that = this;
switch (_that) {
case _TopProduct() when $default != null:
return $default(_that.id,_that.name,_that.imageUrl,_that.quantity,_that.revenue);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TopProduct implements TopProduct {
  const _TopProduct({required this.id, required this.name, required this.imageUrl, required this.quantity, required this.revenue});
  factory _TopProduct.fromJson(Map<String, dynamic> json) => _$TopProductFromJson(json);

@override final  String id;
@override final  String name;
@override final  String imageUrl;
@override final  int quantity;
@override final  double revenue;

/// Create a copy of TopProduct
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TopProductCopyWith<_TopProduct> get copyWith => __$TopProductCopyWithImpl<_TopProduct>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TopProductToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TopProduct&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&(identical(other.revenue, revenue) || other.revenue == revenue));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,imageUrl,quantity,revenue);

@override
String toString() {
  return 'TopProduct(id: $id, name: $name, imageUrl: $imageUrl, quantity: $quantity, revenue: $revenue)';
}


}

/// @nodoc
abstract mixin class _$TopProductCopyWith<$Res> implements $TopProductCopyWith<$Res> {
  factory _$TopProductCopyWith(_TopProduct value, $Res Function(_TopProduct) _then) = __$TopProductCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String imageUrl, int quantity, double revenue
});




}
/// @nodoc
class __$TopProductCopyWithImpl<$Res>
    implements _$TopProductCopyWith<$Res> {
  __$TopProductCopyWithImpl(this._self, this._then);

  final _TopProduct _self;
  final $Res Function(_TopProduct) _then;

/// Create a copy of TopProduct
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? imageUrl = null,Object? quantity = null,Object? revenue = null,}) {
  return _then(_TopProduct(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,revenue: null == revenue ? _self.revenue : revenue // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$CustomerMetrics {

 int get newCustomers; int get repeatCustomers; double get customerRetentionRate; double get customerSatisfaction;
/// Create a copy of CustomerMetrics
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CustomerMetricsCopyWith<CustomerMetrics> get copyWith => _$CustomerMetricsCopyWithImpl<CustomerMetrics>(this as CustomerMetrics, _$identity);

  /// Serializes this CustomerMetrics to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CustomerMetrics&&(identical(other.newCustomers, newCustomers) || other.newCustomers == newCustomers)&&(identical(other.repeatCustomers, repeatCustomers) || other.repeatCustomers == repeatCustomers)&&(identical(other.customerRetentionRate, customerRetentionRate) || other.customerRetentionRate == customerRetentionRate)&&(identical(other.customerSatisfaction, customerSatisfaction) || other.customerSatisfaction == customerSatisfaction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,newCustomers,repeatCustomers,customerRetentionRate,customerSatisfaction);

@override
String toString() {
  return 'CustomerMetrics(newCustomers: $newCustomers, repeatCustomers: $repeatCustomers, customerRetentionRate: $customerRetentionRate, customerSatisfaction: $customerSatisfaction)';
}


}

/// @nodoc
abstract mixin class $CustomerMetricsCopyWith<$Res>  {
  factory $CustomerMetricsCopyWith(CustomerMetrics value, $Res Function(CustomerMetrics) _then) = _$CustomerMetricsCopyWithImpl;
@useResult
$Res call({
 int newCustomers, int repeatCustomers, double customerRetentionRate, double customerSatisfaction
});




}
/// @nodoc
class _$CustomerMetricsCopyWithImpl<$Res>
    implements $CustomerMetricsCopyWith<$Res> {
  _$CustomerMetricsCopyWithImpl(this._self, this._then);

  final CustomerMetrics _self;
  final $Res Function(CustomerMetrics) _then;

/// Create a copy of CustomerMetrics
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? newCustomers = null,Object? repeatCustomers = null,Object? customerRetentionRate = null,Object? customerSatisfaction = null,}) {
  return _then(_self.copyWith(
newCustomers: null == newCustomers ? _self.newCustomers : newCustomers // ignore: cast_nullable_to_non_nullable
as int,repeatCustomers: null == repeatCustomers ? _self.repeatCustomers : repeatCustomers // ignore: cast_nullable_to_non_nullable
as int,customerRetentionRate: null == customerRetentionRate ? _self.customerRetentionRate : customerRetentionRate // ignore: cast_nullable_to_non_nullable
as double,customerSatisfaction: null == customerSatisfaction ? _self.customerSatisfaction : customerSatisfaction // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [CustomerMetrics].
extension CustomerMetricsPatterns on CustomerMetrics {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _CustomerMetrics value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _CustomerMetrics() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _CustomerMetrics value)  $default,){
final _that = this;
switch (_that) {
case _CustomerMetrics():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _CustomerMetrics value)?  $default,){
final _that = this;
switch (_that) {
case _CustomerMetrics() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int newCustomers,  int repeatCustomers,  double customerRetentionRate,  double customerSatisfaction)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _CustomerMetrics() when $default != null:
return $default(_that.newCustomers,_that.repeatCustomers,_that.customerRetentionRate,_that.customerSatisfaction);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int newCustomers,  int repeatCustomers,  double customerRetentionRate,  double customerSatisfaction)  $default,) {final _that = this;
switch (_that) {
case _CustomerMetrics():
return $default(_that.newCustomers,_that.repeatCustomers,_that.customerRetentionRate,_that.customerSatisfaction);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int newCustomers,  int repeatCustomers,  double customerRetentionRate,  double customerSatisfaction)?  $default,) {final _that = this;
switch (_that) {
case _CustomerMetrics() when $default != null:
return $default(_that.newCustomers,_that.repeatCustomers,_that.customerRetentionRate,_that.customerSatisfaction);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _CustomerMetrics implements CustomerMetrics {
  const _CustomerMetrics({required this.newCustomers, required this.repeatCustomers, required this.customerRetentionRate, required this.customerSatisfaction});
  factory _CustomerMetrics.fromJson(Map<String, dynamic> json) => _$CustomerMetricsFromJson(json);

@override final  int newCustomers;
@override final  int repeatCustomers;
@override final  double customerRetentionRate;
@override final  double customerSatisfaction;

/// Create a copy of CustomerMetrics
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CustomerMetricsCopyWith<_CustomerMetrics> get copyWith => __$CustomerMetricsCopyWithImpl<_CustomerMetrics>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CustomerMetricsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CustomerMetrics&&(identical(other.newCustomers, newCustomers) || other.newCustomers == newCustomers)&&(identical(other.repeatCustomers, repeatCustomers) || other.repeatCustomers == repeatCustomers)&&(identical(other.customerRetentionRate, customerRetentionRate) || other.customerRetentionRate == customerRetentionRate)&&(identical(other.customerSatisfaction, customerSatisfaction) || other.customerSatisfaction == customerSatisfaction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,newCustomers,repeatCustomers,customerRetentionRate,customerSatisfaction);

@override
String toString() {
  return 'CustomerMetrics(newCustomers: $newCustomers, repeatCustomers: $repeatCustomers, customerRetentionRate: $customerRetentionRate, customerSatisfaction: $customerSatisfaction)';
}


}

/// @nodoc
abstract mixin class _$CustomerMetricsCopyWith<$Res> implements $CustomerMetricsCopyWith<$Res> {
  factory _$CustomerMetricsCopyWith(_CustomerMetrics value, $Res Function(_CustomerMetrics) _then) = __$CustomerMetricsCopyWithImpl;
@override @useResult
$Res call({
 int newCustomers, int repeatCustomers, double customerRetentionRate, double customerSatisfaction
});




}
/// @nodoc
class __$CustomerMetricsCopyWithImpl<$Res>
    implements _$CustomerMetricsCopyWith<$Res> {
  __$CustomerMetricsCopyWithImpl(this._self, this._then);

  final _CustomerMetrics _self;
  final $Res Function(_CustomerMetrics) _then;

/// Create a copy of CustomerMetrics
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? newCustomers = null,Object? repeatCustomers = null,Object? customerRetentionRate = null,Object? customerSatisfaction = null,}) {
  return _then(_CustomerMetrics(
newCustomers: null == newCustomers ? _self.newCustomers : newCustomers // ignore: cast_nullable_to_non_nullable
as int,repeatCustomers: null == repeatCustomers ? _self.repeatCustomers : repeatCustomers // ignore: cast_nullable_to_non_nullable
as int,customerRetentionRate: null == customerRetentionRate ? _self.customerRetentionRate : customerRetentionRate // ignore: cast_nullable_to_non_nullable
as double,customerSatisfaction: null == customerSatisfaction ? _self.customerSatisfaction : customerSatisfaction // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on
