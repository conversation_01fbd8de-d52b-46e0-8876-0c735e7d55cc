import '../../../shared/models/order/order_model.dart';

/// Order repository interface
abstract class OrderRepository {
  /// Create order
  Future<OrderModel> createOrder(OrderModel order);

  /// Get order by ID
  Future<OrderModel?> getOrderById(String id);

  /// Get orders by user ID
  Future<List<OrderModel>> getOrdersByUserId(String userId);

  /// Update order status
  Future<OrderModel> updateOrderStatus(String id, OrderStatus status);

  /// Cancel order
  Future<OrderModel> cancelOrder(String id);
}
