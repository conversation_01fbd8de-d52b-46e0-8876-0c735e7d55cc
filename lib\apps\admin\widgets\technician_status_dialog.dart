import 'package:flutter/material.dart';
import 'package:shivish/shared/models/technician/technician.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/ui_components/text/text.dart';

class TechnicianStatusDialog extends StatefulWidget {
  final Technician technician;
  final Function(Technician, String, String?, bool) onStatusUpdate;

  const TechnicianStatusDialog({
    super.key,
    required this.technician,
    required this.onStatusUpdate,
  });

  @override
  State<TechnicianStatusDialog> createState() => _TechnicianStatusDialogState();
}

class _TechnicianStatusDialogState extends State<TechnicianStatusDialog> {
  late String _selectedStatus;
  late bool _isActive;
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.technician.verificationStatus;
    _isActive = widget.technician.isActive;
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const AppText(
        'Update Technician Status',
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const AppText(
              'Status',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _selectedStatus,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: const [
                DropdownMenuItem(
                  value: 'pending',
                  child: AppText('Pending'),
                ),
                DropdownMenuItem(
                  value: 'approved',
                  child: AppText('Approved'),
                ),
                DropdownMenuItem(
                  value: 'rejected',
                  child: AppText('Rejected'),
                ),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedStatus = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            const AppText(
              'Notes',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _notesController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Enter notes (optional)',
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: _isActive,
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _isActive = value;
                      });
                    }
                  },
                ),
                const AppText('Active'),
              ],
            ),
          ],
        ),
      ),
      actions: [
        AppButton(
          onPressed: () => Navigator.of(context).pop(),
          label: 'Cancel',
          variant: AppButtonVariant.text,
        ),
        AppButton(
          onPressed: () {
            widget.onStatusUpdate(
              widget.technician,
              _selectedStatus,
              _notesController.text.isEmpty ? null : _notesController.text,
              _isActive,
            );
            Navigator.of(context).pop();
          },
          label: 'Update',
          variant: AppButtonVariant.primary,
        ),
      ],
    );
  }
}
