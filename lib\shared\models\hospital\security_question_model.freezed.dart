// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'security_question_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SecurityQuestionModel {

 String get id; String get userId; String get question; String get answer; DateTime get createdAt; DateTime get updatedAt;
/// Create a copy of SecurityQuestionModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SecurityQuestionModelCopyWith<SecurityQuestionModel> get copyWith => _$SecurityQuestionModelCopyWithImpl<SecurityQuestionModel>(this as SecurityQuestionModel, _$identity);

  /// Serializes this SecurityQuestionModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SecurityQuestionModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.question, question) || other.question == question)&&(identical(other.answer, answer) || other.answer == answer)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,question,answer,createdAt,updatedAt);

@override
String toString() {
  return 'SecurityQuestionModel(id: $id, userId: $userId, question: $question, answer: $answer, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $SecurityQuestionModelCopyWith<$Res>  {
  factory $SecurityQuestionModelCopyWith(SecurityQuestionModel value, $Res Function(SecurityQuestionModel) _then) = _$SecurityQuestionModelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String question, String answer, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class _$SecurityQuestionModelCopyWithImpl<$Res>
    implements $SecurityQuestionModelCopyWith<$Res> {
  _$SecurityQuestionModelCopyWithImpl(this._self, this._then);

  final SecurityQuestionModel _self;
  final $Res Function(SecurityQuestionModel) _then;

/// Create a copy of SecurityQuestionModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? question = null,Object? answer = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,question: null == question ? _self.question : question // ignore: cast_nullable_to_non_nullable
as String,answer: null == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [SecurityQuestionModel].
extension SecurityQuestionModelPatterns on SecurityQuestionModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SecurityQuestionModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SecurityQuestionModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SecurityQuestionModel value)  $default,){
final _that = this;
switch (_that) {
case _SecurityQuestionModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SecurityQuestionModel value)?  $default,){
final _that = this;
switch (_that) {
case _SecurityQuestionModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String question,  String answer,  DateTime createdAt,  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SecurityQuestionModel() when $default != null:
return $default(_that.id,_that.userId,_that.question,_that.answer,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String question,  String answer,  DateTime createdAt,  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _SecurityQuestionModel():
return $default(_that.id,_that.userId,_that.question,_that.answer,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String question,  String answer,  DateTime createdAt,  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _SecurityQuestionModel() when $default != null:
return $default(_that.id,_that.userId,_that.question,_that.answer,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SecurityQuestionModel implements SecurityQuestionModel {
  const _SecurityQuestionModel({required this.id, required this.userId, required this.question, required this.answer, required this.createdAt, required this.updatedAt});
  factory _SecurityQuestionModel.fromJson(Map<String, dynamic> json) => _$SecurityQuestionModelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String question;
@override final  String answer;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;

/// Create a copy of SecurityQuestionModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SecurityQuestionModelCopyWith<_SecurityQuestionModel> get copyWith => __$SecurityQuestionModelCopyWithImpl<_SecurityQuestionModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SecurityQuestionModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SecurityQuestionModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.question, question) || other.question == question)&&(identical(other.answer, answer) || other.answer == answer)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userId,question,answer,createdAt,updatedAt);

@override
String toString() {
  return 'SecurityQuestionModel(id: $id, userId: $userId, question: $question, answer: $answer, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$SecurityQuestionModelCopyWith<$Res> implements $SecurityQuestionModelCopyWith<$Res> {
  factory _$SecurityQuestionModelCopyWith(_SecurityQuestionModel value, $Res Function(_SecurityQuestionModel) _then) = __$SecurityQuestionModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String question, String answer, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class __$SecurityQuestionModelCopyWithImpl<$Res>
    implements _$SecurityQuestionModelCopyWith<$Res> {
  __$SecurityQuestionModelCopyWithImpl(this._self, this._then);

  final _SecurityQuestionModel _self;
  final $Res Function(_SecurityQuestionModel) _then;

/// Create a copy of SecurityQuestionModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? question = null,Object? answer = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_SecurityQuestionModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,question: null == question ? _self.question : question // ignore: cast_nullable_to_non_nullable
as String,answer: null == answer ? _self.answer : answer // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
