import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'app_state_provider.dart';

class StateRestoration extends ConsumerStatefulWidget {
  final Widget child;

  const StateRestoration({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<StateRestoration> createState() => _StateRestorationState();
}

class _StateRestorationState extends ConsumerState<StateRestoration>
    with RestorationMixin {
  @override
  String? get restorationId => 'app_state_restoration';

  final RestorableDateTime _lastRestoreTime =
      RestorableDateTime(DateTime.now());

  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {
    registerForRestoration(_lastRestoreTime, 'last_restore_time');

    if (initialRestore) {
      _restoreAppState();
    }
  }

  Future<void> _restoreAppState() async {
    final notifier = ref.read(appStateProvider.notifier);
    await notifier.loadState();
    _lastRestoreTime.value = DateTime.now();
  }

  @override
  void dispose() {
    _lastRestoreTime.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
