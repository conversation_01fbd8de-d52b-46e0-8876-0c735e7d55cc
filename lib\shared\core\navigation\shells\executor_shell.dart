import 'package:flutter/material.dart';
import '../widgets/executor_drawer.dart';

// This class is deprecated and should not be used.
// Use the ExecutorShell from apps/executor/executor_shell.dart instead.
@Deprecated('Use the ExecutorShell from apps/executor/executor_shell.dart instead')
class ExecutorShell extends StatelessWidget {
  final Widget child;

  const ExecutorShell({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Shivish Executor'),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // Handle notifications
            },
          ),
        ],
      ),
      body: Safe<PERSON>rea(
        child: child,
      ),
      drawer: const ExecutorDrawer(),
      // Bottom navigation bar is now handled in the HomeScreen
    );
  }
}
