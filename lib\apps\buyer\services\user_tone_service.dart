import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../shared/models/alarm/tone_model.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';

final userToneServiceProvider = Provider<UserToneService>((ref) {
  return UserToneService();
});

/// Service for managing user's own tones
class UserToneService {
  static const String _userTonesKey = 'user_tones';

  /// Get all user tones
  Future<List<ToneModel>> getUserTones() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tonesJson = prefs.getStringList(_userTonesKey) ?? [];

      return tonesJson.map((json) {
        final Map<String, dynamic> data = jsonDecode(json);
        return ToneModel.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('Error getting user tones: $e');
      return [];
    }
  }

  /// Add a new user tone
  Future<ToneModel?> addUserTone({
    required String language,
  }) async {
    try {
      // Let user pick an audio file
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
        dialogTitle: 'Select Alarm Tone',
      );

      if (result == null || result.files.isEmpty) {
        return null;
      }

      final file = result.files.first;
      final fileName = file.name;

      // Use the file name without extension as the tone name
      final name = fileName.split('.').first;

      // Copy file to app documents directory
      final directory = await getApplicationDocumentsDirectory();
      final toneDirectory = Directory('${directory.path}/user_tones');

      if (!await toneDirectory.exists()) {
        await toneDirectory.create(recursive: true);
      }

      final filePath = '${toneDirectory.path}/$fileName';

      // For web, we can't access the file system directly
      if (!kIsWeb && file.path != null) {
        final sourceFile = File(file.path!);
        await sourceFile.copy(filePath);
      }

      // Create tone model
      final tone = ToneModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        category: 'user',
        filePath: filePath,
        duration: 0, // We don't know the duration yet
        isDefault: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uploadedBy: 'user',
        status: ToneStatus.approved, // User tones are auto-approved
        language: language,
        isMultiple: false,
        daysOfWeek: [],
      );

      // Save to SharedPreferences
      await _saveUserTone(tone);

      return tone;
    } catch (e) {
      debugPrint('Error adding user tone: $e');
      return null;
    }
  }

  /// Save a user tone to SharedPreferences
  Future<void> _saveUserTone(ToneModel tone) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tones = await getUserTones();

      // Add the new tone
      tones.add(tone);

      // Convert to JSON strings
      final tonesJson = tones.map((t) => jsonEncode(t.toJson())).toList();

      // Save to SharedPreferences
      await prefs.setStringList(_userTonesKey, tonesJson);
    } catch (e) {
      debugPrint('Error saving user tone: $e');
    }
  }

  /// Delete a user tone
  Future<bool> deleteUserTone(String toneId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tones = await getUserTones();

      // Find the tone to delete
      final toneIndex = tones.indexWhere((t) => t.id == toneId);
      if (toneIndex == -1) {
        return false;
      }

      final tone = tones[toneIndex];

      // Delete the file if it exists
      if (!kIsWeb) {
        final file = File(tone.filePath);
        if (await file.exists()) {
          await file.delete();
        }
      }

      // Remove from the list
      tones.removeAt(toneIndex);

      // Convert to JSON strings
      final tonesJson = tones.map((t) => jsonEncode(t.toJson())).toList();

      // Save to SharedPreferences
      await prefs.setStringList(_userTonesKey, tonesJson);

      return true;
    } catch (e) {
      debugPrint('Error deleting user tone: $e');
      return false;
    }
  }
}
