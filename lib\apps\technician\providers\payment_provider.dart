import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/technician/services/payment_service.dart';

final technicianPaymentsProvider =
    StreamProvider.family<List<Map<String, dynamic>>, String>(
  (ref, technicianId) {
    final paymentService = ref.watch(paymentServiceProvider);
    return paymentService.streamTechnicianPayments(technicianId);
  },
);

final technicianSettlementsProvider =
    StreamProvider.family<List<Map<String, dynamic>>, String>(
  (ref, technicianId) {
    final paymentService = ref.watch(paymentServiceProvider);
    return paymentService.streamTechnicianSettlements(technicianId);
  },
);

final technicianPaymentSummaryProvider =
    FutureProvider.family<Map<String, dynamic>, String>(
  (ref, technicianId) {
    final paymentService = ref.watch(paymentServiceProvider);
    return paymentService.getTechnicianPaymentSummary(technicianId);
  },
);
