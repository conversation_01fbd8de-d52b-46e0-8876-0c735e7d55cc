import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';
import '../services/friend_tracking_service.dart';
import '../providers/friend_tracking_provider.dart';
import '../widgets/friend_tracking_map.dart';

/// Screen for friends to view live tracking of a user during ride
class FriendTrackingScreen extends ConsumerStatefulWidget {
  final String rideId;
  final String friendPhone;
  final String userName;

  const FriendTrackingScreen({
    super.key,
    required this.rideId,
    required this.friendPhone,
    required this.userName,
  });

  @override
  ConsumerState<FriendTrackingScreen> createState() =>
      _FriendTrackingScreenState();
}

class _FriendTrackingScreenState extends ConsumerState<FriendTrackingScreen> {
  Timer? _locationUpdateTimer;
  Position? _currentLocation;
  DateTime? _lastLocationUpdate;
  bool _isLoading = true;
  String? _errorMessage;
  FriendTrackingSession? _trackingSession;

  @override
  void initState() {
    super.initState();
    _loadTrackingSession();
    _startLocationUpdates();
  }

  @override
  void dispose() {
    _locationUpdateTimer?.cancel();
    super.dispose();
  }

  /// Load the tracking session details
  Future<void> _loadTrackingSession() async {
    try {
      final friendActions = ref.read(friendTrackingActionsProvider);
      final sessions = friendActions.activeSessions;

      setState(() {
        _trackingSession = sessions[widget.rideId];
        _isLoading = false;
      });

      if (_trackingSession == null) {
        setState(() {
          _errorMessage = 'Tracking session not found or has ended';
        });
      }
    } catch (e) {
      print('Error loading tracking session: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load tracking session';
      });
    }
  }

  /// Start periodic location updates
  void _startLocationUpdates() {
    _locationUpdateTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      _fetchLatestLocation();
    });

    // Initial load
    _fetchLatestLocation();
  }

  /// Fetch the latest location from the tracking session
  Future<void> _fetchLatestLocation() async {
    try {
      // In a real implementation, you would fetch from your database
      // For now, we'll simulate location updates

      // This would typically be:
      // final locationData = await _databaseService.getLatestLocation(widget.rideId);

      setState(() {
        _lastLocationUpdate = DateTime.now();
        // Simulate location update - replace with actual data
        _currentLocation = Position(
          latitude:
              37.7749 + (DateTime.now().millisecondsSinceEpoch % 1000) / 100000,
          longitude:
              -122.4194 +
              (DateTime.now().millisecondsSinceEpoch % 1000) / 100000,
          timestamp: DateTime.now(),
          accuracy: 5.0,
          altitude: 0.0,
          altitudeAccuracy: 0.0,
          heading: 0.0,
          headingAccuracy: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
        );
      });
    } catch (e) {
      print('Error fetching location: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Tracking ${widget.userName}'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchLatestLocation,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading tracking session...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(fontSize: 16, color: Colors.red.shade600),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadTrackingSession,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatusCard(),
          const SizedBox(height: 16),
          _buildMapCard(),
          const SizedBox(height: 16),
          _buildLocationCard(),
          const SizedBox(height: 16),
          _buildRideInfoCard(),
          const SizedBox(height: 16),
          _buildActionsCard(),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    final isActive = _trackingSession?.isActive ?? false;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: isActive ? Colors.green : Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isActive ? 'Live Tracking Active' : 'Tracking Ended',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    isActive
                        ? 'You are currently tracking ${widget.userName}\'s ride'
                        : 'The ride has ended or tracking was stopped',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
            if (isActive)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'LIVE',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMapCard() {
    final isActive = _trackingSession?.isActive ?? false;

    return FriendTrackingMap(
      currentLocation: _currentLocation,
      userName: widget.userName,
      isLive: isActive,
    );
  }

  Widget _buildLocationCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.location_on, color: Colors.blue.shade600, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'Current Location',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_currentLocation != null) ...[
              _buildLocationInfo(
                'Latitude',
                _currentLocation!.latitude.toStringAsFixed(6),
              ),
              const SizedBox(height: 8),
              _buildLocationInfo(
                'Longitude',
                _currentLocation!.longitude.toStringAsFixed(6),
              ),
              const SizedBox(height: 8),
              _buildLocationInfo(
                'Accuracy',
                '${_currentLocation!.accuracy.toStringAsFixed(1)}m',
              ),
              const SizedBox(height: 12),
              if (_lastLocationUpdate != null)
                Text(
                  'Last updated: ${_formatTime(_lastLocationUpdate!)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
            ] else
              Text(
                'Location not available',
                style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationInfo(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
        ),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Widget _buildRideInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.directions_car,
                  color: Colors.orange.shade600,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Ride Information',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildRideInfo('Ride ID', widget.rideId),
            const SizedBox(height: 8),
            _buildRideInfo('Passenger', widget.userName),
            const SizedBox(height: 8),
            if (_trackingSession != null)
              _buildRideInfo(
                'Started',
                _formatTime(_trackingSession!.createdAt),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRideInfo(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
        ),
      ],
    );
  }

  Widget _buildActionsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Actions',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _fetchLatestLocation,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Refresh Location'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _showEmergencyOptions,
                    icon: const Icon(Icons.emergency),
                    label: const Text('Emergency'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red.shade600,
                      side: BorderSide(color: Colors.red.shade600),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showEmergencyOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Emergency Options',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.phone, color: Colors.green),
              title: const Text('Call Emergency Services'),
              subtitle: const Text('Call 911 or local emergency number'),
              onTap: () {
                Navigator.pop(context);
                // Implement emergency call
              },
            ),
            ListTile(
              leading: const Icon(Icons.message, color: Colors.blue),
              title: const Text('Contact Passenger'),
              subtitle: Text('Try to contact ${widget.userName}'),
              onTap: () {
                Navigator.pop(context);
                // Implement contact passenger
              },
            ),
            ListTile(
              leading: const Icon(Icons.report, color: Colors.red),
              title: const Text('Report Concern'),
              subtitle: const Text('Report safety concern to authorities'),
              onTap: () {
                Navigator.pop(context);
                // Implement report concern
              },
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}
