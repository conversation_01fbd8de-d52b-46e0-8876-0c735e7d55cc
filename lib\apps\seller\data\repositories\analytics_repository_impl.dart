import 'package:shivish/apps/seller/domain/models/analytics_model.dart';
import 'package:shivish/apps/seller/domain/repositories/analytics_repository.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:flutter/foundation.dart';

class AnalyticsRepositoryImpl implements AnalyticsRepository {
  final DatabaseService _databaseService;
  final AuthService _authService;

  AnalyticsRepositoryImpl(this._databaseService, this._authService);

  @override
  Future<SalesAnalytics> getSalesAnalytics() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final analytics = await _databaseService.getAll(
        'seller_analytics',
        where: 'seller_id = @param0 AND analytics_type = @param1',
        whereParams: [userId, 'sales'],
        limit: 1,
      );

      if (analytics.isEmpty) {
        // Return default analytics if none found
        return const SalesAnalytics(
          totalRevenue: 0.0,
          totalOrders: 0,
          averageOrderValue: 0.0,
          growthRate: 0.0,
          dailySales: [],
          salesByCategory: {},
        );
      }

      return SalesAnalytics.fromJson(analytics.first);
    } catch (e) {
      debugPrint('Error getting sales analytics: $e');
      rethrow;
    }
  }

  @override
  Future<ProductAnalytics> getProductAnalytics() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final analytics = await _databaseService.getAll(
        'seller_analytics',
        where: 'seller_id = @param0 AND analytics_type = @param1',
        whereParams: [userId, 'products'],
        limit: 1,
      );

      if (analytics.isEmpty) {
        // Return default analytics if none found
        return const ProductAnalytics(
          totalProducts: 0,
          outOfStockProducts: 0,
          averageRating: 0.0,
          topProducts: [],
          lowStockProducts: [],
          productsByCategory: {},
        );
      }

      return ProductAnalytics.fromJson(analytics.first);
    } catch (e) {
      debugPrint('Error getting product analytics: $e');
      rethrow;
    }
  }

  @override
  Future<CustomerAnalytics> getCustomerAnalytics() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final analytics = await _databaseService.getAll(
        'seller_analytics',
        where: 'seller_id = @param0 AND analytics_type = @param1',
        whereParams: [userId, 'customers'],
        limit: 1,
      );

      if (analytics.isEmpty) {
        // Return default analytics if none found
        return const CustomerAnalytics(
          totalCustomers: 0,
          newCustomers: 0,
          customerRetentionRate: 0.0,
          averageCustomerLifetimeValue: 0.0,
          segments: [],
          customersByLocation: {},
          recentActivity: [],
          growthData: [],
        );
      }

      return CustomerAnalytics.fromJson(analytics.first);
    } catch (e) {
      debugPrint('Error getting customer analytics: $e');
      rethrow;
    }
  }

  @override
  Future<PerformanceMetrics> getPerformanceMetrics() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final analytics = await _databaseService.getAll(
        'seller_analytics',
        where: 'seller_id = @param0 AND analytics_type = @param1',
        whereParams: [userId, 'performance'],
        limit: 1,
      );

      if (analytics.isEmpty) {
        // Return default analytics if none found
        return const PerformanceMetrics(
          conversionRate: 0.0,
          returnRate: 0.0,
          averageProcessingTime: 0.0,
          averageDeliveryTime: 0.0,
          performanceByDay: {},
          improvements: [],
        );
      }

      return PerformanceMetrics.fromJson(analytics.first);
    } catch (e) {
      debugPrint('Error getting performance metrics: $e');
      rethrow;
    }
  }
}
