import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';

class AnalyticsService {
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Event tracking
  Future<void> logEvent({
    required String name,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      await _analytics.logEvent(
        name: name,
        parameters:
            parameters?.map((key, value) => MapEntry(key, value as Object)),
      );
    } catch (e) {
      debugPrint('Error logging event: $e');
    }
  }

  // User behavior tracking
  Future<void> setUserProperty({
    required String name,
    required String? value,
  }) async {
    try {
      await _analytics.setUserProperty(name: name, value: value);
    } catch (e) {
      debugPrint('Error setting user property: $e');
    }
  }

  // Screen tracking
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
  }) async {
    try {
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
      );
    } catch (e) {
      debugPrint('Error logging screen view: $e');
    }
  }

  // Performance monitoring
  Future<void> logNetworkLatency({
    required String endpoint,
    required int latencyMillis,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'network_latency',
        parameters: {
          'endpoint': endpoint,
          'latency_ms': latencyMillis,
        },
      );
    } catch (e) {
      debugPrint('Error logging network latency: $e');
    }
  }

  // Crash reporting
  Future<void> logError({
    required String error,
    StackTrace? stackTrace,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'app_error',
        parameters: {
          'error': error,
          'stack_trace': stackTrace?.toString() ?? '',
          ...?parameters,
        },
      );
    } catch (e) {
      debugPrint('Error logging error event: $e');
    }
  }

  // User engagement tracking
  Future<void> logUserEngagement({
    required String action,
    required Duration duration,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'user_engagement',
        parameters: {
          'action': action,
          'duration_seconds': duration.inSeconds,
          ...?parameters,
        },
      );
    } catch (e) {
      debugPrint('Error logging user engagement: $e');
    }
  }

  // E-commerce tracking
  Future<void> logPurchase({
    required String productId,
    required double value,
    String? currency,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'purchase',
        parameters: {
          'productId': productId,
          'value': value,
          'currency': currency ?? 'USD',
          ...?parameters,
        },
      );
    } catch (e) {
      debugPrint('Error logging purchase: $e');
    }
  }
}
