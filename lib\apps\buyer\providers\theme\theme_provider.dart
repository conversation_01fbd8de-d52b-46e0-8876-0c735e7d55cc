import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>(
  (ref) => ThemeModeNotifier(),
);

final colorSchemeProvider =
    StateNotifierProvider<ColorSchemeNotifier, ColorScheme>(
  (ref) => ColorSchemeNotifier(),
);

final textScaleProvider = StateNotifierProvider<TextScaleNotifier, double>(
  (ref) => TextScaleNotifier(),
);

class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.system) {
    _loadThemeMode();
  }

  Future<void> _loadThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final themeModeString = prefs.getString('theme_mode') ?? 'system';
    state = ThemeMode.values.firstWhere(
      (mode) => mode.toString() == 'ThemeMode.$themeModeString',
    );
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('theme_mode', mode.toString().split('.').last);
    state = mode;
  }
}

class ColorSchemeNotifier extends StateNotifier<ColorScheme> {
  ColorSchemeNotifier() : super(const ColorScheme.light()) {
    _loadColorScheme();
  }

  Future<void> _loadColorScheme() async {
    final prefs = await SharedPreferences.getInstance();
    final colorSchemeString = prefs.getString('color_scheme') ?? 'light';
    state = _getColorScheme(colorSchemeString);
  }

  Future<void> setColorScheme(String scheme) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('color_scheme', scheme);
    state = _getColorScheme(scheme);
  }

  ColorScheme _getColorScheme(String scheme) {
    switch (scheme) {
      case 'light':
        return const ColorScheme.light();
      case 'dark':
        return const ColorScheme.dark();
      case 'highContrast':
        return const ColorScheme.highContrastLight();
      default:
        return const ColorScheme.light();
    }
  }
}

class TextScaleNotifier extends StateNotifier<double> {
  TextScaleNotifier() : super(1.0) {
    _loadTextScale();
  }

  Future<void> _loadTextScale() async {
    final prefs = await SharedPreferences.getInstance();
    state = prefs.getDouble('text_scale') ?? 1.0;
  }

  Future<void> setTextScale(double scale) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('text_scale', scale);
    state = scale;
  }
}
