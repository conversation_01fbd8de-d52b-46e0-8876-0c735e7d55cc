// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_command_training_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$VoiceCommandTrainingEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VoiceCommandTrainingEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'VoiceCommandTrainingEvent()';
}


}

/// @nodoc
class $VoiceCommandTrainingEventCopyWith<$Res>  {
$VoiceCommandTrainingEventCopyWith(VoiceCommandTrainingEvent _, $Res Function(VoiceCommandTrainingEvent) __);
}


/// Adds pattern-matching-related methods to [VoiceCommandTrainingEvent].
extension VoiceCommandTrainingEventPatterns on VoiceCommandTrainingEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( LoadVoiceCommandTrainings value)?  loadVoiceCommandTrainings,TResult Function( CreateVoiceCommandTraining value)?  createVoiceCommandTraining,TResult Function( UpdateVoiceCommandTraining value)?  updateVoiceCommandTraining,TResult Function( DeleteVoiceCommandTraining value)?  deleteVoiceCommandTraining,TResult Function( UpdateVoiceCommandTrainingStatus value)?  updateVoiceCommandTrainingStatus,required TResult orElse(),}){
final _that = this;
switch (_that) {
case LoadVoiceCommandTrainings() when loadVoiceCommandTrainings != null:
return loadVoiceCommandTrainings(_that);case CreateVoiceCommandTraining() when createVoiceCommandTraining != null:
return createVoiceCommandTraining(_that);case UpdateVoiceCommandTraining() when updateVoiceCommandTraining != null:
return updateVoiceCommandTraining(_that);case DeleteVoiceCommandTraining() when deleteVoiceCommandTraining != null:
return deleteVoiceCommandTraining(_that);case UpdateVoiceCommandTrainingStatus() when updateVoiceCommandTrainingStatus != null:
return updateVoiceCommandTrainingStatus(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( LoadVoiceCommandTrainings value)  loadVoiceCommandTrainings,required TResult Function( CreateVoiceCommandTraining value)  createVoiceCommandTraining,required TResult Function( UpdateVoiceCommandTraining value)  updateVoiceCommandTraining,required TResult Function( DeleteVoiceCommandTraining value)  deleteVoiceCommandTraining,required TResult Function( UpdateVoiceCommandTrainingStatus value)  updateVoiceCommandTrainingStatus,}){
final _that = this;
switch (_that) {
case LoadVoiceCommandTrainings():
return loadVoiceCommandTrainings(_that);case CreateVoiceCommandTraining():
return createVoiceCommandTraining(_that);case UpdateVoiceCommandTraining():
return updateVoiceCommandTraining(_that);case DeleteVoiceCommandTraining():
return deleteVoiceCommandTraining(_that);case UpdateVoiceCommandTrainingStatus():
return updateVoiceCommandTrainingStatus(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( LoadVoiceCommandTrainings value)?  loadVoiceCommandTrainings,TResult? Function( CreateVoiceCommandTraining value)?  createVoiceCommandTraining,TResult? Function( UpdateVoiceCommandTraining value)?  updateVoiceCommandTraining,TResult? Function( DeleteVoiceCommandTraining value)?  deleteVoiceCommandTraining,TResult? Function( UpdateVoiceCommandTrainingStatus value)?  updateVoiceCommandTrainingStatus,}){
final _that = this;
switch (_that) {
case LoadVoiceCommandTrainings() when loadVoiceCommandTrainings != null:
return loadVoiceCommandTrainings(_that);case CreateVoiceCommandTraining() when createVoiceCommandTraining != null:
return createVoiceCommandTraining(_that);case UpdateVoiceCommandTraining() when updateVoiceCommandTraining != null:
return updateVoiceCommandTraining(_that);case DeleteVoiceCommandTraining() when deleteVoiceCommandTraining != null:
return deleteVoiceCommandTraining(_that);case UpdateVoiceCommandTrainingStatus() when updateVoiceCommandTrainingStatus != null:
return updateVoiceCommandTrainingStatus(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  loadVoiceCommandTrainings,TResult Function( VoiceCommandTraining training)?  createVoiceCommandTraining,TResult Function( VoiceCommandTraining training)?  updateVoiceCommandTraining,TResult Function( String id)?  deleteVoiceCommandTraining,TResult Function( String id,  bool isEnabled)?  updateVoiceCommandTrainingStatus,required TResult orElse(),}) {final _that = this;
switch (_that) {
case LoadVoiceCommandTrainings() when loadVoiceCommandTrainings != null:
return loadVoiceCommandTrainings();case CreateVoiceCommandTraining() when createVoiceCommandTraining != null:
return createVoiceCommandTraining(_that.training);case UpdateVoiceCommandTraining() when updateVoiceCommandTraining != null:
return updateVoiceCommandTraining(_that.training);case DeleteVoiceCommandTraining() when deleteVoiceCommandTraining != null:
return deleteVoiceCommandTraining(_that.id);case UpdateVoiceCommandTrainingStatus() when updateVoiceCommandTrainingStatus != null:
return updateVoiceCommandTrainingStatus(_that.id,_that.isEnabled);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  loadVoiceCommandTrainings,required TResult Function( VoiceCommandTraining training)  createVoiceCommandTraining,required TResult Function( VoiceCommandTraining training)  updateVoiceCommandTraining,required TResult Function( String id)  deleteVoiceCommandTraining,required TResult Function( String id,  bool isEnabled)  updateVoiceCommandTrainingStatus,}) {final _that = this;
switch (_that) {
case LoadVoiceCommandTrainings():
return loadVoiceCommandTrainings();case CreateVoiceCommandTraining():
return createVoiceCommandTraining(_that.training);case UpdateVoiceCommandTraining():
return updateVoiceCommandTraining(_that.training);case DeleteVoiceCommandTraining():
return deleteVoiceCommandTraining(_that.id);case UpdateVoiceCommandTrainingStatus():
return updateVoiceCommandTrainingStatus(_that.id,_that.isEnabled);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  loadVoiceCommandTrainings,TResult? Function( VoiceCommandTraining training)?  createVoiceCommandTraining,TResult? Function( VoiceCommandTraining training)?  updateVoiceCommandTraining,TResult? Function( String id)?  deleteVoiceCommandTraining,TResult? Function( String id,  bool isEnabled)?  updateVoiceCommandTrainingStatus,}) {final _that = this;
switch (_that) {
case LoadVoiceCommandTrainings() when loadVoiceCommandTrainings != null:
return loadVoiceCommandTrainings();case CreateVoiceCommandTraining() when createVoiceCommandTraining != null:
return createVoiceCommandTraining(_that.training);case UpdateVoiceCommandTraining() when updateVoiceCommandTraining != null:
return updateVoiceCommandTraining(_that.training);case DeleteVoiceCommandTraining() when deleteVoiceCommandTraining != null:
return deleteVoiceCommandTraining(_that.id);case UpdateVoiceCommandTrainingStatus() when updateVoiceCommandTrainingStatus != null:
return updateVoiceCommandTrainingStatus(_that.id,_that.isEnabled);case _:
  return null;

}
}

}

/// @nodoc


class LoadVoiceCommandTrainings implements VoiceCommandTrainingEvent {
  const LoadVoiceCommandTrainings();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadVoiceCommandTrainings);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'VoiceCommandTrainingEvent.loadVoiceCommandTrainings()';
}


}




/// @nodoc


class CreateVoiceCommandTraining implements VoiceCommandTrainingEvent {
  const CreateVoiceCommandTraining(this.training);
  

 final  VoiceCommandTraining training;

/// Create a copy of VoiceCommandTrainingEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateVoiceCommandTrainingCopyWith<CreateVoiceCommandTraining> get copyWith => _$CreateVoiceCommandTrainingCopyWithImpl<CreateVoiceCommandTraining>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateVoiceCommandTraining&&(identical(other.training, training) || other.training == training));
}


@override
int get hashCode => Object.hash(runtimeType,training);

@override
String toString() {
  return 'VoiceCommandTrainingEvent.createVoiceCommandTraining(training: $training)';
}


}

/// @nodoc
abstract mixin class $CreateVoiceCommandTrainingCopyWith<$Res> implements $VoiceCommandTrainingEventCopyWith<$Res> {
  factory $CreateVoiceCommandTrainingCopyWith(CreateVoiceCommandTraining value, $Res Function(CreateVoiceCommandTraining) _then) = _$CreateVoiceCommandTrainingCopyWithImpl;
@useResult
$Res call({
 VoiceCommandTraining training
});




}
/// @nodoc
class _$CreateVoiceCommandTrainingCopyWithImpl<$Res>
    implements $CreateVoiceCommandTrainingCopyWith<$Res> {
  _$CreateVoiceCommandTrainingCopyWithImpl(this._self, this._then);

  final CreateVoiceCommandTraining _self;
  final $Res Function(CreateVoiceCommandTraining) _then;

/// Create a copy of VoiceCommandTrainingEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? training = null,}) {
  return _then(CreateVoiceCommandTraining(
null == training ? _self.training : training // ignore: cast_nullable_to_non_nullable
as VoiceCommandTraining,
  ));
}


}

/// @nodoc


class UpdateVoiceCommandTraining implements VoiceCommandTrainingEvent {
  const UpdateVoiceCommandTraining(this.training);
  

 final  VoiceCommandTraining training;

/// Create a copy of VoiceCommandTrainingEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateVoiceCommandTrainingCopyWith<UpdateVoiceCommandTraining> get copyWith => _$UpdateVoiceCommandTrainingCopyWithImpl<UpdateVoiceCommandTraining>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateVoiceCommandTraining&&(identical(other.training, training) || other.training == training));
}


@override
int get hashCode => Object.hash(runtimeType,training);

@override
String toString() {
  return 'VoiceCommandTrainingEvent.updateVoiceCommandTraining(training: $training)';
}


}

/// @nodoc
abstract mixin class $UpdateVoiceCommandTrainingCopyWith<$Res> implements $VoiceCommandTrainingEventCopyWith<$Res> {
  factory $UpdateVoiceCommandTrainingCopyWith(UpdateVoiceCommandTraining value, $Res Function(UpdateVoiceCommandTraining) _then) = _$UpdateVoiceCommandTrainingCopyWithImpl;
@useResult
$Res call({
 VoiceCommandTraining training
});




}
/// @nodoc
class _$UpdateVoiceCommandTrainingCopyWithImpl<$Res>
    implements $UpdateVoiceCommandTrainingCopyWith<$Res> {
  _$UpdateVoiceCommandTrainingCopyWithImpl(this._self, this._then);

  final UpdateVoiceCommandTraining _self;
  final $Res Function(UpdateVoiceCommandTraining) _then;

/// Create a copy of VoiceCommandTrainingEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? training = null,}) {
  return _then(UpdateVoiceCommandTraining(
null == training ? _self.training : training // ignore: cast_nullable_to_non_nullable
as VoiceCommandTraining,
  ));
}


}

/// @nodoc


class DeleteVoiceCommandTraining implements VoiceCommandTrainingEvent {
  const DeleteVoiceCommandTraining(this.id);
  

 final  String id;

/// Create a copy of VoiceCommandTrainingEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DeleteVoiceCommandTrainingCopyWith<DeleteVoiceCommandTraining> get copyWith => _$DeleteVoiceCommandTrainingCopyWithImpl<DeleteVoiceCommandTraining>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DeleteVoiceCommandTraining&&(identical(other.id, id) || other.id == id));
}


@override
int get hashCode => Object.hash(runtimeType,id);

@override
String toString() {
  return 'VoiceCommandTrainingEvent.deleteVoiceCommandTraining(id: $id)';
}


}

/// @nodoc
abstract mixin class $DeleteVoiceCommandTrainingCopyWith<$Res> implements $VoiceCommandTrainingEventCopyWith<$Res> {
  factory $DeleteVoiceCommandTrainingCopyWith(DeleteVoiceCommandTraining value, $Res Function(DeleteVoiceCommandTraining) _then) = _$DeleteVoiceCommandTrainingCopyWithImpl;
@useResult
$Res call({
 String id
});




}
/// @nodoc
class _$DeleteVoiceCommandTrainingCopyWithImpl<$Res>
    implements $DeleteVoiceCommandTrainingCopyWith<$Res> {
  _$DeleteVoiceCommandTrainingCopyWithImpl(this._self, this._then);

  final DeleteVoiceCommandTraining _self;
  final $Res Function(DeleteVoiceCommandTraining) _then;

/// Create a copy of VoiceCommandTrainingEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,}) {
  return _then(DeleteVoiceCommandTraining(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class UpdateVoiceCommandTrainingStatus implements VoiceCommandTrainingEvent {
  const UpdateVoiceCommandTrainingStatus(this.id, this.isEnabled);
  

 final  String id;
 final  bool isEnabled;

/// Create a copy of VoiceCommandTrainingEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateVoiceCommandTrainingStatusCopyWith<UpdateVoiceCommandTrainingStatus> get copyWith => _$UpdateVoiceCommandTrainingStatusCopyWithImpl<UpdateVoiceCommandTrainingStatus>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateVoiceCommandTrainingStatus&&(identical(other.id, id) || other.id == id)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled));
}


@override
int get hashCode => Object.hash(runtimeType,id,isEnabled);

@override
String toString() {
  return 'VoiceCommandTrainingEvent.updateVoiceCommandTrainingStatus(id: $id, isEnabled: $isEnabled)';
}


}

/// @nodoc
abstract mixin class $UpdateVoiceCommandTrainingStatusCopyWith<$Res> implements $VoiceCommandTrainingEventCopyWith<$Res> {
  factory $UpdateVoiceCommandTrainingStatusCopyWith(UpdateVoiceCommandTrainingStatus value, $Res Function(UpdateVoiceCommandTrainingStatus) _then) = _$UpdateVoiceCommandTrainingStatusCopyWithImpl;
@useResult
$Res call({
 String id, bool isEnabled
});




}
/// @nodoc
class _$UpdateVoiceCommandTrainingStatusCopyWithImpl<$Res>
    implements $UpdateVoiceCommandTrainingStatusCopyWith<$Res> {
  _$UpdateVoiceCommandTrainingStatusCopyWithImpl(this._self, this._then);

  final UpdateVoiceCommandTrainingStatus _self;
  final $Res Function(UpdateVoiceCommandTrainingStatus) _then;

/// Create a copy of VoiceCommandTrainingEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? isEnabled = null,}) {
  return _then(UpdateVoiceCommandTrainingStatus(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
