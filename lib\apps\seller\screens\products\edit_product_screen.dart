import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shivish/shared/models/product/product_model.dart';
import 'package:shivish/shared/services/product/product_service.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'dart:io';
import 'package:shivish/apps/seller/presentation/providers/product_provider.dart';
import 'package:shivish/shared/constants/product_constants.dart';
import 'package:shivish/shared/services/storage/adaptive_storage_service.dart';

class EditProductScreen extends ConsumerStatefulWidget {
  final String productId;

  const EditProductScreen({
    super.key,
    required this.productId,
  });

  @override
  ConsumerState<EditProductScreen> createState() => _EditProductScreenState();
}

class _EditProductScreenState extends ConsumerState<EditProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _originalPriceController = TextEditingController();
  final _quantityController = TextEditingController();
  final _brandController = TextEditingController();
  final _unitController = TextEditingController();
  final _weightController = TextEditingController();
  final _highlightsController = TextEditingController();
  final _warrantyController = TextEditingController();
  final _manufacturerController = TextEditingController();
  final _countryOfOriginController = TextEditingController();
  final _tagsController = TextEditingController();

  // Specifications
  final List<Map<String, TextEditingController>> _specifications = [];

  // Day-wise availability for food products
  final Map<String, Map<String, TextEditingController>> _dayWiseAvailability = {
    'Monday': {
      'from': TextEditingController(),
      'to': TextEditingController(),
    },
    'Tuesday': {
      'from': TextEditingController(),
      'to': TextEditingController(),
    },
    'Wednesday': {
      'from': TextEditingController(),
      'to': TextEditingController(),
    },
    'Thursday': {
      'from': TextEditingController(),
      'to': TextEditingController(),
    },
    'Friday': {
      'from': TextEditingController(),
      'to': TextEditingController(),
    },
    'Saturday': {
      'from': TextEditingController(),
      'to': TextEditingController(),
    },
    'Sunday': {
      'from': TextEditingController(),
      'to': TextEditingController(),
    },
  };

  String? _selectedCategory;
  List<String> _images = [];
  bool _isLoading = true;
  String? _error;
  bool _isApproved = false;
  ProductStatus _productStatus = ProductStatus.pending;

  // Food type (veg/non-veg)
  String _foodType = 'veg'; // Default to vegetarian

  // Storage service for file uploads
  late final AdaptiveStorageService _storageService;

  @override
  void initState() {
    super.initState();
    _storageService = AdaptiveStorageService();
    _loadProduct();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _originalPriceController.dispose();
    _quantityController.dispose();
    _brandController.dispose();
    _unitController.dispose();
    _weightController.dispose();
    _highlightsController.dispose();
    _warrantyController.dispose();
    _manufacturerController.dispose();
    _countryOfOriginController.dispose();
    _tagsController.dispose();

    // Dispose specification controllers
    for (final spec in _specifications) {
      spec['key']?.dispose();
      spec['value']?.dispose();
    }

    // Dispose day-wise availability controllers
    for (final day in _dayWiseAvailability.values) {
      day['from']?.dispose();
      day['to']?.dispose();
    }

    super.dispose();
  }

  Future<void> _loadProduct() async {
    try {
      final productStream =
          ref.read(productServiceProvider).getProduct(widget.productId);
      final product = await productStream.first;
      if (mounted && product != null) {
        setState(() {
          // Basic information
          _nameController.text = product.name;
          _descriptionController.text = product.description;
          _priceController.text = product.price.toString();
          _originalPriceController.text =
              product.originalPrice?.toString() ?? '';
          _quantityController.text = product.quantity.toString();
          _selectedCategory = product.categoryId;

          // Product details
          _brandController.text = product.brand ?? '';
          _unitController.text = product.unit ?? '';
          _weightController.text = product.weight?.toString() ?? '';
          _manufacturerController.text = ''; // Not stored in the model yet
          _countryOfOriginController.text = ''; // Not stored in the model yet
          _warrantyController.text = ''; // Not stored in the model yet

          // Set highlights if available
          if (product.highlights.isNotEmpty) {
            _highlightsController.text = product.highlights.join('\n');
          } else {
            _highlightsController.text = '';
          }

          // Tags
          _tagsController.text = product.tags.join(', ');

          // Specifications (for non-food products)
          _specifications.clear();
          if (product.categoryId != 'food' &&
              product.specifications != null &&
              product.specifications!.isNotEmpty) {
            product.specifications!.forEach((key, value) {
              _specifications.add({
                'key': TextEditingController(text: key),
                'value': TextEditingController(text: value.toString()),
              });
            });
          }

          // If no specifications and not food category, add an empty one
          if (_specifications.isEmpty && product.categoryId != 'food') {
            _specifications.add({
              'key': TextEditingController(),
              'value': TextEditingController(),
            });
          }

          // Day-wise availability and food type (for food products)
          if (product.categoryId == 'food') {
            // Try to get day-wise availability from the product data
            final dayWiseAvailability = product.toJson()['dayWiseAvailability'];
            if (dayWiseAvailability != null && dayWiseAvailability is Map) {
              dayWiseAvailability.forEach((day, times) {
                if (times is Map && _dayWiseAvailability.containsKey(day)) {
                  if (times['from'] != null) {
                    _dayWiseAvailability[day]!['from']!.text =
                        times['from'].toString();
                  }
                  if (times['to'] != null) {
                    _dayWiseAvailability[day]!['to']!.text =
                        times['to'].toString();
                  }
                }
              });
            }

            // Get food type (veg/non-veg)
            final foodType = product.toJson()['foodType'];
            if (foodType != null &&
                (foodType == 'veg' || foodType == 'non-veg')) {
              _foodType = foodType;
            }
          }

          // Images and status
          _images = List.from(product.images);
          _isApproved = product.isApproved;
          _productStatus = product.productStatus;

          _originalProduct = product; // Store original product for comparison
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final file = File(pickedFile.path);
        final fileName =
            '${DateTime.now().millisecondsSinceEpoch}_${pickedFile.name}';
        final fileBytes = await file.readAsBytes();

        final downloadUrl = await _storageService.uploadData(
          data: fileBytes,
          fileName: fileName,
          storagePath: 'products',
        );

        setState(() {
          _images.add(downloadUrl);
        });
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to upload image: $e')),
          );
        }
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _removeImage(int index) async {
    setState(() {
      _images.removeAt(index);
    });
  }

  // Store original product data for comparison
  ProductModel? _originalProduct;

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = ref.read(authServiceProvider);
      final currentUser = await authService.getCurrentUser();

      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Parse tags from comma-separated string
      final tags = _tagsController.text.isEmpty
          ? <String>[]
          : _tagsController.text.split(',').map((tag) => tag.trim()).toList();

      // Build specifications map (only for non-food products)
      final specifications = <String, dynamic>{};
      if (_selectedCategory != 'food') {
        for (final spec in _specifications) {
          final key = spec['key']?.text;
          final value = spec['value']?.text;
          if (key != null &&
              key.isNotEmpty &&
              value != null &&
              value.isNotEmpty) {
            specifications[key] = value;
          }
        }
      }

      // Build day-wise availability map (only for food products)
      final dayWiseAvailability = <String, Map<String, String>>{};
      if (_selectedCategory == 'food') {
        for (final entry in _dayWiseAvailability.entries) {
          final day = entry.key;
          final times = entry.value;
          final fromTime = times['from']?.text;
          final toTime = times['to']?.text;

          if (fromTime != null &&
              fromTime.isNotEmpty &&
              toTime != null &&
              toTime.isNotEmpty) {
            dayWiseAvailability[day] = {
              'from': fromTime,
              'to': toTime,
            };
          }
        }
      }

      // Parse numeric values safely
      double? originalPrice;
      if (_originalPriceController.text.isNotEmpty) {
        originalPrice = double.tryParse(_originalPriceController.text);
      }

      double? weight;
      if (_weightController.text.isNotEmpty) {
        weight = double.tryParse(_weightController.text);
      }

      // Calculate discount percentage if original price is provided
      double? discountPercentage;
      if (originalPrice != null) {
        final price = double.parse(_priceController.text);
        if (originalPrice > price) {
          discountPercentage = ((originalPrice - price) / originalPrice) * 100;
        }
      }

      // Check if only price has changed
      bool onlyPriceChanged = false;
      if (_originalProduct != null) {
        final nameChanged = _nameController.text != _originalProduct!.name;
        final descriptionChanged =
            _descriptionController.text != _originalProduct!.description;
        final categoryChanged =
            _selectedCategory != _originalProduct!.categoryId;
        final imagesChanged =
            !_areListsEqual(_images, _originalProduct!.images);
        final brandChanged =
            (_brandController.text.isEmpty ? null : _brandController.text) !=
                _originalProduct!.brand;
        final unitChanged =
            (_unitController.text.isEmpty ? null : _unitController.text) !=
                _originalProduct!.unit;
        final weightChanged = weight != _originalProduct!.weight;
        final originalPriceChanged =
            originalPrice != _originalProduct!.originalPrice;
        final tagsChanged = !_areListsEqual(tags, _originalProduct!.tags);
        // Product type is always physical for sellers
        final productTypeChanged = false;
        final quantityChanged =
            int.parse(_quantityController.text) != _originalProduct!.quantity;

        // Compare specifications
        bool specificationsChanged = false;
        if (_originalProduct!.specifications != null &&
            specifications.isNotEmpty) {
          if (_originalProduct!.specifications!.length !=
              specifications.length) {
            specificationsChanged = true;
          } else {
            for (final key in specifications.keys) {
              if (!_originalProduct!.specifications!.containsKey(key) ||
                  _originalProduct!.specifications![key].toString() !=
                      specifications[key].toString()) {
                specificationsChanged = true;
                break;
              }
            }
          }
        } else if ((_originalProduct!.specifications == null) !=
            (specifications.isEmpty)) {
          specificationsChanged = true;
        }

        // Check if only the price has changed (not quantity or any other field)
        final priceChanged =
            double.parse(_priceController.text) != _originalProduct!.price;

        onlyPriceChanged = priceChanged &&
            !nameChanged &&
            !descriptionChanged &&
            !categoryChanged &&
            !imagesChanged &&
            !brandChanged &&
            !unitChanged &&
            !weightChanged &&
            !originalPriceChanged &&
            !tagsChanged &&
            !productTypeChanged &&
            !specificationsChanged &&
            !quantityChanged;

        debugPrint('Only price changed: $onlyPriceChanged');
        if (priceChanged) {
          debugPrint(
              'Price changed from ${_originalProduct!.price} to ${double.parse(_priceController.text)}');
        }
        if (quantityChanged) {
          debugPrint(
              'Quantity changed from ${_originalProduct!.quantity} to ${int.parse(_quantityController.text)}');
        }
      }

      // Determine approval status and product status based on changes
      // Only price changes don't require approval
      bool newIsApproved = onlyPriceChanged ? _isApproved : false;
      ProductStatus newProductStatus =
          onlyPriceChanged ? _productStatus : ProductStatus.pending;

      // Create the base product model as JSON
      final productJson = {
        'id': widget.productId,
        'name': _nameController.text,
        'description': _descriptionController.text,
        'price': double.parse(_priceController.text),
        'quantity': int.parse(_quantityController.text),
        'categoryId': _selectedCategory!,
        'sellerId': currentUser.id,
        'images': _images,
        'isApproved': newIsApproved,
        'tags': tags,
        'productType': ProductType.physical.index,
        'productStatus': newProductStatus.index,
        'createdAt': _originalProduct?.createdAt ?? DateTime.now(),
        'updatedAt': DateTime.now(),
      };

      // Add optional fields based on category
      if (originalPrice != null) {
        productJson['originalPrice'] = originalPrice;
        if (discountPercentage != null) {
          productJson['discountPercentage'] = discountPercentage;
        }
      }

      if (_brandController.text.isNotEmpty) {
        productJson['brand'] = _brandController.text;
      }

      if (_unitController.text.isNotEmpty) {
        productJson['unit'] = _unitController.text;
      }

      if (weight != null) {
        productJson['weight'] = weight;
      }

      // Add specifications for non-food products
      if (_selectedCategory != 'food' && specifications.isNotEmpty) {
        productJson['specifications'] = specifications;
      }

      // Add day-wise availability and food type for food products
      if (_selectedCategory == 'food') {
        if (dayWiseAvailability.isNotEmpty) {
          productJson['dayWiseAvailability'] = dayWiseAvailability;
        }
        // Add food type (veg/non-veg)
        productJson['foodType'] = _foodType;
      }

      final product = ProductModel.fromJson(productJson);

      await ref.read(productServiceProvider).createOrUpdateProduct(product);

      // Refresh the products list
      await ref.read(productsProvider.notifier).refreshProducts();

      if (mounted) {
        // Show appropriate message based on whether approval is needed
        if (!onlyPriceChanged) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Product updated and sent for approval')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Price updated successfully')),
          );
        }
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update product: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Helper method to compare two lists
  bool _areListsEqual(List<String> list1, List<String> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }

  void _addSpecification() {
    setState(() {
      _specifications.add({
        'key': TextEditingController(),
        'value': TextEditingController(),
      });
    });
  }

  void _removeSpecification(int index) {
    setState(() {
      final spec = _specifications.removeAt(index);
      spec['key']?.dispose();
      spec['value']?.dispose();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: LoadingIndicator(),
      );
    }

    if (_error != null) {
      return Scaffold(
        body: ErrorMessage(
          message: _error!,
          onRetry: _loadProduct,
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Product'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              if (_images.isNotEmpty)
                SizedBox(
                  height: 200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _images.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Image.network(
                                _images[index],
                                width: 200,
                                height: 200,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 200,
                                    height: 200,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .surfaceContainerHighest,
                                    child: Icon(
                                      Icons.error_outline,
                                      size: 48,
                                      color:
                                          Theme.of(context).colorScheme.error,
                                    ),
                                  );
                                },
                              ),
                            ),
                            Positioned(
                              top: 8,
                              right: 8,
                              child: IconButton(
                                icon: const Icon(Icons.close),
                                onPressed: () => _removeImage(index),
                                style: IconButton.styleFrom(
                                  backgroundColor:
                                      Theme.of(context).colorScheme.surface,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              const SizedBox(height: 16),
              FilledButton.icon(
                onPressed: _pickImage,
                icon: const Icon(Icons.add_photo_alternate),
                label: const Text('Add Image'),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Product Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter product name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter product description';
                  }
                  return null;
                },
              ),
              // Basic Information Section
              const SizedBox(height: 24),
              Text(
                'Basic Information',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _priceController,
                      decoration: const InputDecoration(
                        labelText: 'Selling Price',
                        border: OutlineInputBorder(),
                        prefixText: '₹',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter product price';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid price';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _originalPriceController,
                      decoration: const InputDecoration(
                        labelText: 'MRP/Original Price',
                        border: OutlineInputBorder(),
                        prefixText: '₹',
                        helperText: 'Leave empty if no discount',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final originalPrice = double.tryParse(value);
                          if (originalPrice == null) {
                            return 'Please enter a valid price';
                          }

                          final sellingPrice =
                              double.tryParse(_priceController.text);
                          if (sellingPrice != null &&
                              originalPrice < sellingPrice) {
                            return 'MRP should be >= selling price';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _quantityController,
                      decoration: const InputDecoration(
                        labelText: 'Stock Quantity',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter stock quantity';
                        }
                        if (int.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedCategory,
                      decoration: const InputDecoration(
                        labelText: 'Category',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        // Basic categories
                        DropdownMenuItem(
                          value: 'electronics',
                          child: Text('Electronics'),
                        ),
                        DropdownMenuItem(
                          value: 'clothing',
                          child: Text('Clothing'),
                        ),
                        DropdownMenuItem(
                          value: 'food',
                          child: Text('Food'),
                        ),
                        DropdownMenuItem(
                          value: 'health',
                          child: Text('Health'),
                        ),
                        DropdownMenuItem(
                          value: 'beauty',
                          child: Text('Beauty'),
                        ),
                        DropdownMenuItem(
                          value: 'home',
                          child: Text('Home'),
                        ),
                        DropdownMenuItem(
                          value: 'books',
                          child: Text('Books'),
                        ),
                        DropdownMenuItem(
                          value: 'toys',
                          child: Text('Toys'),
                        ),

                        // Food subcategories
                        DropdownMenuItem(
                          value: 'vegetables',
                          child: Text('Vegetables'),
                        ),
                        DropdownMenuItem(
                          value: 'fruits',
                          child: Text('Fruits'),
                        ),
                        DropdownMenuItem(
                          value: 'grocery',
                          child: Text('Grocery'),
                        ),
                        DropdownMenuItem(
                          value: 'bakery',
                          child: Text('Bakery'),
                        ),
                        DropdownMenuItem(
                          value: 'dairy',
                          child: Text('Dairy'),
                        ),
                        DropdownMenuItem(
                          value: 'meat',
                          child: Text('Meat'),
                        ),

                        // Additional categories
                        DropdownMenuItem(
                          value: 'sports',
                          child: Text('Sports'),
                        ),
                        DropdownMenuItem(
                          value: 'furniture',
                          child: Text('Furniture'),
                        ),
                        DropdownMenuItem(
                          value: 'garden',
                          child: Text('Garden'),
                        ),
                        DropdownMenuItem(
                          value: 'pets',
                          child: Text('Pets'),
                        ),
                        DropdownMenuItem(
                          value: 'auto',
                          child: Text('Auto'),
                        ),
                        DropdownMenuItem(
                          value: 'education',
                          child: Text('Education'),
                        ),
                        DropdownMenuItem(
                          value: 'gaming',
                          child: Text('Gaming'),
                        ),
                        DropdownMenuItem(
                          value: 'other',
                          child: Text('Other'),
                        ),
                      ],
                      onChanged: (value) {
                        final oldCategory = _selectedCategory;
                        setState(() {
                          _selectedCategory = value;
                        });

                        // Show a snackbar when changing to or from food category
                        if (value == 'food' && oldCategory != 'food') {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                  'Food products only require availability timings. Product details section has been hidden.'),
                              duration: Duration(seconds: 4),
                            ),
                          );
                        } else if (value != 'food' && oldCategory == 'food') {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                  'Product details section is now visible for non-food products.'),
                              duration: Duration(seconds: 4),
                            ),
                          );
                        }
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Please select a category';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              // Product Details Section (only for non-food products)
              if (_selectedCategory != 'food') ...[
                const SizedBox(height: 24),
                Text(
                  'Product Details',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _brandController,
                        decoration: const InputDecoration(
                          labelText: 'Brand',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _manufacturerController,
                        decoration: const InputDecoration(
                          labelText: 'Manufacturer',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _unitController.text.isEmpty
                            ? null
                            : _unitController.text,
                        decoration: const InputDecoration(
                          labelText: 'Unit',
                          border: OutlineInputBorder(),
                        ),
                        items: ProductConstants.units
                            .map((unit) => DropdownMenuItem<String>(
                                  value: unit,
                                  child: Text(unit),
                                ))
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _unitController.text = value;
                            });
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _weightController,
                        decoration: const InputDecoration(
                          labelText: 'Weight',
                          border: OutlineInputBorder(),
                          helperText: 'In kg',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _countryOfOriginController.text.isEmpty
                      ? null
                      : _countryOfOriginController.text,
                  decoration: const InputDecoration(
                    labelText: 'Country of Origin',
                    border: OutlineInputBorder(),
                  ),
                  items: ProductConstants.countries
                      .map((country) => DropdownMenuItem<String>(
                            value: country,
                            child: Text(country),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _countryOfOriginController.text = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _warrantyController,
                  decoration: const InputDecoration(
                    labelText: 'Warranty Information',
                    border: OutlineInputBorder(),
                    hintText: 'E.g., 1 Year Manufacturer Warranty',
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _highlightsController,
                  decoration: const InputDecoration(
                    labelText: 'Product Highlights',
                    border: OutlineInputBorder(),
                    hintText: 'Key features of your product',
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _tagsController,
                  decoration: const InputDecoration(
                    labelText: 'Tags (comma separated)',
                    border: OutlineInputBorder(),
                    hintText: 'E.g., organic, fresh, premium',
                  ),
                ),

                // Specifications Section
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Specifications',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    IconButton(
                      onPressed: _addSpecification,
                      icon: const Icon(Icons.add_circle),
                      tooltip: 'Add Specification',
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ...List.generate(
                  _specifications.length,
                  (index) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _specifications[index]['key'],
                            decoration: const InputDecoration(
                              labelText: 'Specification',
                              border: OutlineInputBorder(),
                              hintText: 'E.g., Material',
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: TextFormField(
                            controller: _specifications[index]['value'],
                            decoration: const InputDecoration(
                              labelText: 'Value',
                              border: OutlineInputBorder(),
                              hintText: 'E.g., Cotton',
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: index == 0 && _specifications.length == 1
                              ? null
                              : () => _removeSpecification(index),
                          icon: const Icon(Icons.remove_circle),
                          tooltip: 'Remove Specification',
                        ),
                      ],
                    ),
                  ),
                ),
              ],

              // Day-wise Availability Section (only for food products)
              if (_selectedCategory == 'food') ...[
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Day-wise Availability Timings',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ),
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerLowest,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          Theme.of(context).colorScheme.outline.withAlpha(40),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'For food products, only availability timings are required. Product details are not needed.',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                // Vegetarian/Non-vegetarian selector
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Food Type',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _foodType,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: 'veg',
                            child: Row(
                              children: [
                                Icon(Icons.circle,
                                    color: Colors.green, size: 16),
                                SizedBox(width: 8),
                                Text('Vegetarian'),
                              ],
                            ),
                          ),
                          DropdownMenuItem(
                            value: 'non-veg',
                            child: Row(
                              children: [
                                Icon(Icons.circle, color: Colors.red, size: 16),
                                SizedBox(width: 8),
                                Text('Non-Vegetarian'),
                              ],
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _foodType = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                ..._dayWiseAvailability.entries.map((entry) {
                  final day = entry.key;
                  final times = entry.value;
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          day,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: times['from'],
                                decoration: const InputDecoration(
                                  labelText: 'From',
                                  border: OutlineInputBorder(),
                                  hintText: 'E.g., 9:00 AM',
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: TextFormField(
                                controller: times['to'],
                                decoration: const InputDecoration(
                                  labelText: 'To',
                                  border: OutlineInputBorder(),
                                  hintText: 'E.g., 5:00 PM',
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                }),
              ],

              // Approval Note
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                      color:
                          Theme.of(context).colorScheme.outline.withAlpha(40)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Any changes to the product (except price changes) will require approval before becoming visible to buyers.',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              ),

              // Submit Button
              const SizedBox(height: 32),
              FilledButton.icon(
                onPressed: _submitForm,
                icon: const Icon(Icons.save),
                label: const Text('Update Product'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
