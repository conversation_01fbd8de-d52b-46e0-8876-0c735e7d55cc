// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'temple_booking_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DevoteeInfo {

 String get name; String get phone; String? get email; String? get address; String? get gotram; String? get nakshatram; String? get rashi; Map<String, dynamic>? get additionalInfo;
/// Create a copy of DevoteeInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DevoteeInfoCopyWith<DevoteeInfo> get copyWith => _$DevoteeInfoCopyWithImpl<DevoteeInfo>(this as DevoteeInfo, _$identity);

  /// Serializes this DevoteeInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DevoteeInfo&&(identical(other.name, name) || other.name == name)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email)&&(identical(other.address, address) || other.address == address)&&(identical(other.gotram, gotram) || other.gotram == gotram)&&(identical(other.nakshatram, nakshatram) || other.nakshatram == nakshatram)&&(identical(other.rashi, rashi) || other.rashi == rashi)&&const DeepCollectionEquality().equals(other.additionalInfo, additionalInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,phone,email,address,gotram,nakshatram,rashi,const DeepCollectionEquality().hash(additionalInfo));

@override
String toString() {
  return 'DevoteeInfo(name: $name, phone: $phone, email: $email, address: $address, gotram: $gotram, nakshatram: $nakshatram, rashi: $rashi, additionalInfo: $additionalInfo)';
}


}

/// @nodoc
abstract mixin class $DevoteeInfoCopyWith<$Res>  {
  factory $DevoteeInfoCopyWith(DevoteeInfo value, $Res Function(DevoteeInfo) _then) = _$DevoteeInfoCopyWithImpl;
@useResult
$Res call({
 String name, String phone, String? email, String? address, String? gotram, String? nakshatram, String? rashi, Map<String, dynamic>? additionalInfo
});




}
/// @nodoc
class _$DevoteeInfoCopyWithImpl<$Res>
    implements $DevoteeInfoCopyWith<$Res> {
  _$DevoteeInfoCopyWithImpl(this._self, this._then);

  final DevoteeInfo _self;
  final $Res Function(DevoteeInfo) _then;

/// Create a copy of DevoteeInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? phone = null,Object? email = freezed,Object? address = freezed,Object? gotram = freezed,Object? nakshatram = freezed,Object? rashi = freezed,Object? additionalInfo = freezed,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,gotram: freezed == gotram ? _self.gotram : gotram // ignore: cast_nullable_to_non_nullable
as String?,nakshatram: freezed == nakshatram ? _self.nakshatram : nakshatram // ignore: cast_nullable_to_non_nullable
as String?,rashi: freezed == rashi ? _self.rashi : rashi // ignore: cast_nullable_to_non_nullable
as String?,additionalInfo: freezed == additionalInfo ? _self.additionalInfo : additionalInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [DevoteeInfo].
extension DevoteeInfoPatterns on DevoteeInfo {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DevoteeInfo value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DevoteeInfo() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DevoteeInfo value)  $default,){
final _that = this;
switch (_that) {
case _DevoteeInfo():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DevoteeInfo value)?  $default,){
final _that = this;
switch (_that) {
case _DevoteeInfo() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String name,  String phone,  String? email,  String? address,  String? gotram,  String? nakshatram,  String? rashi,  Map<String, dynamic>? additionalInfo)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DevoteeInfo() when $default != null:
return $default(_that.name,_that.phone,_that.email,_that.address,_that.gotram,_that.nakshatram,_that.rashi,_that.additionalInfo);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String name,  String phone,  String? email,  String? address,  String? gotram,  String? nakshatram,  String? rashi,  Map<String, dynamic>? additionalInfo)  $default,) {final _that = this;
switch (_that) {
case _DevoteeInfo():
return $default(_that.name,_that.phone,_that.email,_that.address,_that.gotram,_that.nakshatram,_that.rashi,_that.additionalInfo);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String name,  String phone,  String? email,  String? address,  String? gotram,  String? nakshatram,  String? rashi,  Map<String, dynamic>? additionalInfo)?  $default,) {final _that = this;
switch (_that) {
case _DevoteeInfo() when $default != null:
return $default(_that.name,_that.phone,_that.email,_that.address,_that.gotram,_that.nakshatram,_that.rashi,_that.additionalInfo);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DevoteeInfo implements DevoteeInfo {
  const _DevoteeInfo({required this.name, required this.phone, this.email, this.address, this.gotram, this.nakshatram, this.rashi, final  Map<String, dynamic>? additionalInfo}): _additionalInfo = additionalInfo;
  factory _DevoteeInfo.fromJson(Map<String, dynamic> json) => _$DevoteeInfoFromJson(json);

@override final  String name;
@override final  String phone;
@override final  String? email;
@override final  String? address;
@override final  String? gotram;
@override final  String? nakshatram;
@override final  String? rashi;
 final  Map<String, dynamic>? _additionalInfo;
@override Map<String, dynamic>? get additionalInfo {
  final value = _additionalInfo;
  if (value == null) return null;
  if (_additionalInfo is EqualUnmodifiableMapView) return _additionalInfo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of DevoteeInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DevoteeInfoCopyWith<_DevoteeInfo> get copyWith => __$DevoteeInfoCopyWithImpl<_DevoteeInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DevoteeInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DevoteeInfo&&(identical(other.name, name) || other.name == name)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.email, email) || other.email == email)&&(identical(other.address, address) || other.address == address)&&(identical(other.gotram, gotram) || other.gotram == gotram)&&(identical(other.nakshatram, nakshatram) || other.nakshatram == nakshatram)&&(identical(other.rashi, rashi) || other.rashi == rashi)&&const DeepCollectionEquality().equals(other._additionalInfo, _additionalInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,phone,email,address,gotram,nakshatram,rashi,const DeepCollectionEquality().hash(_additionalInfo));

@override
String toString() {
  return 'DevoteeInfo(name: $name, phone: $phone, email: $email, address: $address, gotram: $gotram, nakshatram: $nakshatram, rashi: $rashi, additionalInfo: $additionalInfo)';
}


}

/// @nodoc
abstract mixin class _$DevoteeInfoCopyWith<$Res> implements $DevoteeInfoCopyWith<$Res> {
  factory _$DevoteeInfoCopyWith(_DevoteeInfo value, $Res Function(_DevoteeInfo) _then) = __$DevoteeInfoCopyWithImpl;
@override @useResult
$Res call({
 String name, String phone, String? email, String? address, String? gotram, String? nakshatram, String? rashi, Map<String, dynamic>? additionalInfo
});




}
/// @nodoc
class __$DevoteeInfoCopyWithImpl<$Res>
    implements _$DevoteeInfoCopyWith<$Res> {
  __$DevoteeInfoCopyWithImpl(this._self, this._then);

  final _DevoteeInfo _self;
  final $Res Function(_DevoteeInfo) _then;

/// Create a copy of DevoteeInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? phone = null,Object? email = freezed,Object? address = freezed,Object? gotram = freezed,Object? nakshatram = freezed,Object? rashi = freezed,Object? additionalInfo = freezed,}) {
  return _then(_DevoteeInfo(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,gotram: freezed == gotram ? _self.gotram : gotram // ignore: cast_nullable_to_non_nullable
as String?,nakshatram: freezed == nakshatram ? _self.nakshatram : nakshatram // ignore: cast_nullable_to_non_nullable
as String?,rashi: freezed == rashi ? _self.rashi : rashi // ignore: cast_nullable_to_non_nullable
as String?,additionalInfo: freezed == additionalInfo ? _self._additionalInfo : additionalInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$TempleBookingItem {

 String get id; String get name; TempleBookingType get type; String get timeSlot; double get price; int get quantity; Map<String, dynamic> get requirements; String? get specialInstructions; Map<String, dynamic>? get additionalDetails;
/// Create a copy of TempleBookingItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TempleBookingItemCopyWith<TempleBookingItem> get copyWith => _$TempleBookingItemCopyWithImpl<TempleBookingItem>(this as TempleBookingItem, _$identity);

  /// Serializes this TempleBookingItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TempleBookingItem&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.timeSlot, timeSlot) || other.timeSlot == timeSlot)&&(identical(other.price, price) || other.price == price)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&const DeepCollectionEquality().equals(other.requirements, requirements)&&(identical(other.specialInstructions, specialInstructions) || other.specialInstructions == specialInstructions)&&const DeepCollectionEquality().equals(other.additionalDetails, additionalDetails));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,timeSlot,price,quantity,const DeepCollectionEquality().hash(requirements),specialInstructions,const DeepCollectionEquality().hash(additionalDetails));

@override
String toString() {
  return 'TempleBookingItem(id: $id, name: $name, type: $type, timeSlot: $timeSlot, price: $price, quantity: $quantity, requirements: $requirements, specialInstructions: $specialInstructions, additionalDetails: $additionalDetails)';
}


}

/// @nodoc
abstract mixin class $TempleBookingItemCopyWith<$Res>  {
  factory $TempleBookingItemCopyWith(TempleBookingItem value, $Res Function(TempleBookingItem) _then) = _$TempleBookingItemCopyWithImpl;
@useResult
$Res call({
 String id, String name, TempleBookingType type, String timeSlot, double price, int quantity, Map<String, dynamic> requirements, String? specialInstructions, Map<String, dynamic>? additionalDetails
});




}
/// @nodoc
class _$TempleBookingItemCopyWithImpl<$Res>
    implements $TempleBookingItemCopyWith<$Res> {
  _$TempleBookingItemCopyWithImpl(this._self, this._then);

  final TempleBookingItem _self;
  final $Res Function(TempleBookingItem) _then;

/// Create a copy of TempleBookingItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? type = null,Object? timeSlot = null,Object? price = null,Object? quantity = null,Object? requirements = null,Object? specialInstructions = freezed,Object? additionalDetails = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as TempleBookingType,timeSlot: null == timeSlot ? _self.timeSlot : timeSlot // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,requirements: null == requirements ? _self.requirements : requirements // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,specialInstructions: freezed == specialInstructions ? _self.specialInstructions : specialInstructions // ignore: cast_nullable_to_non_nullable
as String?,additionalDetails: freezed == additionalDetails ? _self.additionalDetails : additionalDetails // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [TempleBookingItem].
extension TempleBookingItemPatterns on TempleBookingItem {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TempleBookingItem value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TempleBookingItem() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TempleBookingItem value)  $default,){
final _that = this;
switch (_that) {
case _TempleBookingItem():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TempleBookingItem value)?  $default,){
final _that = this;
switch (_that) {
case _TempleBookingItem() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  TempleBookingType type,  String timeSlot,  double price,  int quantity,  Map<String, dynamic> requirements,  String? specialInstructions,  Map<String, dynamic>? additionalDetails)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TempleBookingItem() when $default != null:
return $default(_that.id,_that.name,_that.type,_that.timeSlot,_that.price,_that.quantity,_that.requirements,_that.specialInstructions,_that.additionalDetails);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  TempleBookingType type,  String timeSlot,  double price,  int quantity,  Map<String, dynamic> requirements,  String? specialInstructions,  Map<String, dynamic>? additionalDetails)  $default,) {final _that = this;
switch (_that) {
case _TempleBookingItem():
return $default(_that.id,_that.name,_that.type,_that.timeSlot,_that.price,_that.quantity,_that.requirements,_that.specialInstructions,_that.additionalDetails);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  TempleBookingType type,  String timeSlot,  double price,  int quantity,  Map<String, dynamic> requirements,  String? specialInstructions,  Map<String, dynamic>? additionalDetails)?  $default,) {final _that = this;
switch (_that) {
case _TempleBookingItem() when $default != null:
return $default(_that.id,_that.name,_that.type,_that.timeSlot,_that.price,_that.quantity,_that.requirements,_that.specialInstructions,_that.additionalDetails);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TempleBookingItem implements TempleBookingItem {
  const _TempleBookingItem({required this.id, required this.name, required this.type, required this.timeSlot, required this.price, required this.quantity, required final  Map<String, dynamic> requirements, this.specialInstructions, final  Map<String, dynamic>? additionalDetails}): _requirements = requirements,_additionalDetails = additionalDetails;
  factory _TempleBookingItem.fromJson(Map<String, dynamic> json) => _$TempleBookingItemFromJson(json);

@override final  String id;
@override final  String name;
@override final  TempleBookingType type;
@override final  String timeSlot;
@override final  double price;
@override final  int quantity;
 final  Map<String, dynamic> _requirements;
@override Map<String, dynamic> get requirements {
  if (_requirements is EqualUnmodifiableMapView) return _requirements;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_requirements);
}

@override final  String? specialInstructions;
 final  Map<String, dynamic>? _additionalDetails;
@override Map<String, dynamic>? get additionalDetails {
  final value = _additionalDetails;
  if (value == null) return null;
  if (_additionalDetails is EqualUnmodifiableMapView) return _additionalDetails;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of TempleBookingItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TempleBookingItemCopyWith<_TempleBookingItem> get copyWith => __$TempleBookingItemCopyWithImpl<_TempleBookingItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TempleBookingItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TempleBookingItem&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.type, type) || other.type == type)&&(identical(other.timeSlot, timeSlot) || other.timeSlot == timeSlot)&&(identical(other.price, price) || other.price == price)&&(identical(other.quantity, quantity) || other.quantity == quantity)&&const DeepCollectionEquality().equals(other._requirements, _requirements)&&(identical(other.specialInstructions, specialInstructions) || other.specialInstructions == specialInstructions)&&const DeepCollectionEquality().equals(other._additionalDetails, _additionalDetails));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,type,timeSlot,price,quantity,const DeepCollectionEquality().hash(_requirements),specialInstructions,const DeepCollectionEquality().hash(_additionalDetails));

@override
String toString() {
  return 'TempleBookingItem(id: $id, name: $name, type: $type, timeSlot: $timeSlot, price: $price, quantity: $quantity, requirements: $requirements, specialInstructions: $specialInstructions, additionalDetails: $additionalDetails)';
}


}

/// @nodoc
abstract mixin class _$TempleBookingItemCopyWith<$Res> implements $TempleBookingItemCopyWith<$Res> {
  factory _$TempleBookingItemCopyWith(_TempleBookingItem value, $Res Function(_TempleBookingItem) _then) = __$TempleBookingItemCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, TempleBookingType type, String timeSlot, double price, int quantity, Map<String, dynamic> requirements, String? specialInstructions, Map<String, dynamic>? additionalDetails
});




}
/// @nodoc
class __$TempleBookingItemCopyWithImpl<$Res>
    implements _$TempleBookingItemCopyWith<$Res> {
  __$TempleBookingItemCopyWithImpl(this._self, this._then);

  final _TempleBookingItem _self;
  final $Res Function(_TempleBookingItem) _then;

/// Create a copy of TempleBookingItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? type = null,Object? timeSlot = null,Object? price = null,Object? quantity = null,Object? requirements = null,Object? specialInstructions = freezed,Object? additionalDetails = freezed,}) {
  return _then(_TempleBookingItem(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as TempleBookingType,timeSlot: null == timeSlot ? _self.timeSlot : timeSlot // ignore: cast_nullable_to_non_nullable
as String,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,quantity: null == quantity ? _self.quantity : quantity // ignore: cast_nullable_to_non_nullable
as int,requirements: null == requirements ? _self._requirements : requirements // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,specialInstructions: freezed == specialInstructions ? _self.specialInstructions : specialInstructions // ignore: cast_nullable_to_non_nullable
as String?,additionalDetails: freezed == additionalDetails ? _self._additionalDetails : additionalDetails // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$TempleBooking {

 String get id; String get bookingNumber; String get templeId; String get templeName; String get userId; DevoteeInfo get devoteeInfo; List<TempleBookingItem> get items; DateTime get bookingDate; DateTime get visitDate; TempleBookingStatus get status; TemplePaymentStatus get paymentStatus; double get subtotalAmount; double get taxAmount; double get discountAmount; double get totalAmount; DateTime get createdAt; DateTime get updatedAt; String? get paymentMethod; String? get paymentId; String? get transactionId; String? get refundId; String? get qrCode; String? get notes; String? get cancellationReason; String? get refundReason; DateTime? get checkedInAt; DateTime? get completedAt; DateTime? get cancelledAt; DateTime? get refundedAt; bool get isDeleted; bool get reminderSent; bool get confirmationSent; Map<String, dynamic>? get metadata;
/// Create a copy of TempleBooking
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TempleBookingCopyWith<TempleBooking> get copyWith => _$TempleBookingCopyWithImpl<TempleBooking>(this as TempleBooking, _$identity);

  /// Serializes this TempleBooking to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TempleBooking&&(identical(other.id, id) || other.id == id)&&(identical(other.bookingNumber, bookingNumber) || other.bookingNumber == bookingNumber)&&(identical(other.templeId, templeId) || other.templeId == templeId)&&(identical(other.templeName, templeName) || other.templeName == templeName)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.devoteeInfo, devoteeInfo) || other.devoteeInfo == devoteeInfo)&&const DeepCollectionEquality().equals(other.items, items)&&(identical(other.bookingDate, bookingDate) || other.bookingDate == bookingDate)&&(identical(other.visitDate, visitDate) || other.visitDate == visitDate)&&(identical(other.status, status) || other.status == status)&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.subtotalAmount, subtotalAmount) || other.subtotalAmount == subtotalAmount)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.refundId, refundId) || other.refundId == refundId)&&(identical(other.qrCode, qrCode) || other.qrCode == qrCode)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.cancellationReason, cancellationReason) || other.cancellationReason == cancellationReason)&&(identical(other.refundReason, refundReason) || other.refundReason == refundReason)&&(identical(other.checkedInAt, checkedInAt) || other.checkedInAt == checkedInAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.cancelledAt, cancelledAt) || other.cancelledAt == cancelledAt)&&(identical(other.refundedAt, refundedAt) || other.refundedAt == refundedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.reminderSent, reminderSent) || other.reminderSent == reminderSent)&&(identical(other.confirmationSent, confirmationSent) || other.confirmationSent == confirmationSent)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,bookingNumber,templeId,templeName,userId,devoteeInfo,const DeepCollectionEquality().hash(items),bookingDate,visitDate,status,paymentStatus,subtotalAmount,taxAmount,discountAmount,totalAmount,createdAt,updatedAt,paymentMethod,paymentId,transactionId,refundId,qrCode,notes,cancellationReason,refundReason,checkedInAt,completedAt,cancelledAt,refundedAt,isDeleted,reminderSent,confirmationSent,const DeepCollectionEquality().hash(metadata)]);

@override
String toString() {
  return 'TempleBooking(id: $id, bookingNumber: $bookingNumber, templeId: $templeId, templeName: $templeName, userId: $userId, devoteeInfo: $devoteeInfo, items: $items, bookingDate: $bookingDate, visitDate: $visitDate, status: $status, paymentStatus: $paymentStatus, subtotalAmount: $subtotalAmount, taxAmount: $taxAmount, discountAmount: $discountAmount, totalAmount: $totalAmount, createdAt: $createdAt, updatedAt: $updatedAt, paymentMethod: $paymentMethod, paymentId: $paymentId, transactionId: $transactionId, refundId: $refundId, qrCode: $qrCode, notes: $notes, cancellationReason: $cancellationReason, refundReason: $refundReason, checkedInAt: $checkedInAt, completedAt: $completedAt, cancelledAt: $cancelledAt, refundedAt: $refundedAt, isDeleted: $isDeleted, reminderSent: $reminderSent, confirmationSent: $confirmationSent, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $TempleBookingCopyWith<$Res>  {
  factory $TempleBookingCopyWith(TempleBooking value, $Res Function(TempleBooking) _then) = _$TempleBookingCopyWithImpl;
@useResult
$Res call({
 String id, String bookingNumber, String templeId, String templeName, String userId, DevoteeInfo devoteeInfo, List<TempleBookingItem> items, DateTime bookingDate, DateTime visitDate, TempleBookingStatus status, TemplePaymentStatus paymentStatus, double subtotalAmount, double taxAmount, double discountAmount, double totalAmount, DateTime createdAt, DateTime updatedAt, String? paymentMethod, String? paymentId, String? transactionId, String? refundId, String? qrCode, String? notes, String? cancellationReason, String? refundReason, DateTime? checkedInAt, DateTime? completedAt, DateTime? cancelledAt, DateTime? refundedAt, bool isDeleted, bool reminderSent, bool confirmationSent, Map<String, dynamic>? metadata
});


$DevoteeInfoCopyWith<$Res> get devoteeInfo;

}
/// @nodoc
class _$TempleBookingCopyWithImpl<$Res>
    implements $TempleBookingCopyWith<$Res> {
  _$TempleBookingCopyWithImpl(this._self, this._then);

  final TempleBooking _self;
  final $Res Function(TempleBooking) _then;

/// Create a copy of TempleBooking
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? bookingNumber = null,Object? templeId = null,Object? templeName = null,Object? userId = null,Object? devoteeInfo = null,Object? items = null,Object? bookingDate = null,Object? visitDate = null,Object? status = null,Object? paymentStatus = null,Object? subtotalAmount = null,Object? taxAmount = null,Object? discountAmount = null,Object? totalAmount = null,Object? createdAt = null,Object? updatedAt = null,Object? paymentMethod = freezed,Object? paymentId = freezed,Object? transactionId = freezed,Object? refundId = freezed,Object? qrCode = freezed,Object? notes = freezed,Object? cancellationReason = freezed,Object? refundReason = freezed,Object? checkedInAt = freezed,Object? completedAt = freezed,Object? cancelledAt = freezed,Object? refundedAt = freezed,Object? isDeleted = null,Object? reminderSent = null,Object? confirmationSent = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,bookingNumber: null == bookingNumber ? _self.bookingNumber : bookingNumber // ignore: cast_nullable_to_non_nullable
as String,templeId: null == templeId ? _self.templeId : templeId // ignore: cast_nullable_to_non_nullable
as String,templeName: null == templeName ? _self.templeName : templeName // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,devoteeInfo: null == devoteeInfo ? _self.devoteeInfo : devoteeInfo // ignore: cast_nullable_to_non_nullable
as DevoteeInfo,items: null == items ? _self.items : items // ignore: cast_nullable_to_non_nullable
as List<TempleBookingItem>,bookingDate: null == bookingDate ? _self.bookingDate : bookingDate // ignore: cast_nullable_to_non_nullable
as DateTime,visitDate: null == visitDate ? _self.visitDate : visitDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as TempleBookingStatus,paymentStatus: null == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as TemplePaymentStatus,subtotalAmount: null == subtotalAmount ? _self.subtotalAmount : subtotalAmount // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as double,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,paymentMethod: freezed == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as String?,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,refundId: freezed == refundId ? _self.refundId : refundId // ignore: cast_nullable_to_non_nullable
as String?,qrCode: freezed == qrCode ? _self.qrCode : qrCode // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,cancellationReason: freezed == cancellationReason ? _self.cancellationReason : cancellationReason // ignore: cast_nullable_to_non_nullable
as String?,refundReason: freezed == refundReason ? _self.refundReason : refundReason // ignore: cast_nullable_to_non_nullable
as String?,checkedInAt: freezed == checkedInAt ? _self.checkedInAt : checkedInAt // ignore: cast_nullable_to_non_nullable
as DateTime?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,cancelledAt: freezed == cancelledAt ? _self.cancelledAt : cancelledAt // ignore: cast_nullable_to_non_nullable
as DateTime?,refundedAt: freezed == refundedAt ? _self.refundedAt : refundedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,reminderSent: null == reminderSent ? _self.reminderSent : reminderSent // ignore: cast_nullable_to_non_nullable
as bool,confirmationSent: null == confirmationSent ? _self.confirmationSent : confirmationSent // ignore: cast_nullable_to_non_nullable
as bool,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}
/// Create a copy of TempleBooking
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DevoteeInfoCopyWith<$Res> get devoteeInfo {
  
  return $DevoteeInfoCopyWith<$Res>(_self.devoteeInfo, (value) {
    return _then(_self.copyWith(devoteeInfo: value));
  });
}
}


/// Adds pattern-matching-related methods to [TempleBooking].
extension TempleBookingPatterns on TempleBooking {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TempleBooking value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TempleBooking() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TempleBooking value)  $default,){
final _that = this;
switch (_that) {
case _TempleBooking():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TempleBooking value)?  $default,){
final _that = this;
switch (_that) {
case _TempleBooking() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String bookingNumber,  String templeId,  String templeName,  String userId,  DevoteeInfo devoteeInfo,  List<TempleBookingItem> items,  DateTime bookingDate,  DateTime visitDate,  TempleBookingStatus status,  TemplePaymentStatus paymentStatus,  double subtotalAmount,  double taxAmount,  double discountAmount,  double totalAmount,  DateTime createdAt,  DateTime updatedAt,  String? paymentMethod,  String? paymentId,  String? transactionId,  String? refundId,  String? qrCode,  String? notes,  String? cancellationReason,  String? refundReason,  DateTime? checkedInAt,  DateTime? completedAt,  DateTime? cancelledAt,  DateTime? refundedAt,  bool isDeleted,  bool reminderSent,  bool confirmationSent,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TempleBooking() when $default != null:
return $default(_that.id,_that.bookingNumber,_that.templeId,_that.templeName,_that.userId,_that.devoteeInfo,_that.items,_that.bookingDate,_that.visitDate,_that.status,_that.paymentStatus,_that.subtotalAmount,_that.taxAmount,_that.discountAmount,_that.totalAmount,_that.createdAt,_that.updatedAt,_that.paymentMethod,_that.paymentId,_that.transactionId,_that.refundId,_that.qrCode,_that.notes,_that.cancellationReason,_that.refundReason,_that.checkedInAt,_that.completedAt,_that.cancelledAt,_that.refundedAt,_that.isDeleted,_that.reminderSent,_that.confirmationSent,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String bookingNumber,  String templeId,  String templeName,  String userId,  DevoteeInfo devoteeInfo,  List<TempleBookingItem> items,  DateTime bookingDate,  DateTime visitDate,  TempleBookingStatus status,  TemplePaymentStatus paymentStatus,  double subtotalAmount,  double taxAmount,  double discountAmount,  double totalAmount,  DateTime createdAt,  DateTime updatedAt,  String? paymentMethod,  String? paymentId,  String? transactionId,  String? refundId,  String? qrCode,  String? notes,  String? cancellationReason,  String? refundReason,  DateTime? checkedInAt,  DateTime? completedAt,  DateTime? cancelledAt,  DateTime? refundedAt,  bool isDeleted,  bool reminderSent,  bool confirmationSent,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _TempleBooking():
return $default(_that.id,_that.bookingNumber,_that.templeId,_that.templeName,_that.userId,_that.devoteeInfo,_that.items,_that.bookingDate,_that.visitDate,_that.status,_that.paymentStatus,_that.subtotalAmount,_that.taxAmount,_that.discountAmount,_that.totalAmount,_that.createdAt,_that.updatedAt,_that.paymentMethod,_that.paymentId,_that.transactionId,_that.refundId,_that.qrCode,_that.notes,_that.cancellationReason,_that.refundReason,_that.checkedInAt,_that.completedAt,_that.cancelledAt,_that.refundedAt,_that.isDeleted,_that.reminderSent,_that.confirmationSent,_that.metadata);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String bookingNumber,  String templeId,  String templeName,  String userId,  DevoteeInfo devoteeInfo,  List<TempleBookingItem> items,  DateTime bookingDate,  DateTime visitDate,  TempleBookingStatus status,  TemplePaymentStatus paymentStatus,  double subtotalAmount,  double taxAmount,  double discountAmount,  double totalAmount,  DateTime createdAt,  DateTime updatedAt,  String? paymentMethod,  String? paymentId,  String? transactionId,  String? refundId,  String? qrCode,  String? notes,  String? cancellationReason,  String? refundReason,  DateTime? checkedInAt,  DateTime? completedAt,  DateTime? cancelledAt,  DateTime? refundedAt,  bool isDeleted,  bool reminderSent,  bool confirmationSent,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _TempleBooking() when $default != null:
return $default(_that.id,_that.bookingNumber,_that.templeId,_that.templeName,_that.userId,_that.devoteeInfo,_that.items,_that.bookingDate,_that.visitDate,_that.status,_that.paymentStatus,_that.subtotalAmount,_that.taxAmount,_that.discountAmount,_that.totalAmount,_that.createdAt,_that.updatedAt,_that.paymentMethod,_that.paymentId,_that.transactionId,_that.refundId,_that.qrCode,_that.notes,_that.cancellationReason,_that.refundReason,_that.checkedInAt,_that.completedAt,_that.cancelledAt,_that.refundedAt,_that.isDeleted,_that.reminderSent,_that.confirmationSent,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TempleBooking implements TempleBooking {
  const _TempleBooking({required this.id, required this.bookingNumber, required this.templeId, required this.templeName, required this.userId, required this.devoteeInfo, required final  List<TempleBookingItem> items, required this.bookingDate, required this.visitDate, required this.status, required this.paymentStatus, required this.subtotalAmount, required this.taxAmount, required this.discountAmount, required this.totalAmount, required this.createdAt, required this.updatedAt, this.paymentMethod, this.paymentId, this.transactionId, this.refundId, this.qrCode, this.notes, this.cancellationReason, this.refundReason, this.checkedInAt, this.completedAt, this.cancelledAt, this.refundedAt, this.isDeleted = false, this.reminderSent = false, this.confirmationSent = false, final  Map<String, dynamic>? metadata}): _items = items,_metadata = metadata;
  factory _TempleBooking.fromJson(Map<String, dynamic> json) => _$TempleBookingFromJson(json);

@override final  String id;
@override final  String bookingNumber;
@override final  String templeId;
@override final  String templeName;
@override final  String userId;
@override final  DevoteeInfo devoteeInfo;
 final  List<TempleBookingItem> _items;
@override List<TempleBookingItem> get items {
  if (_items is EqualUnmodifiableListView) return _items;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_items);
}

@override final  DateTime bookingDate;
@override final  DateTime visitDate;
@override final  TempleBookingStatus status;
@override final  TemplePaymentStatus paymentStatus;
@override final  double subtotalAmount;
@override final  double taxAmount;
@override final  double discountAmount;
@override final  double totalAmount;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override final  String? paymentMethod;
@override final  String? paymentId;
@override final  String? transactionId;
@override final  String? refundId;
@override final  String? qrCode;
@override final  String? notes;
@override final  String? cancellationReason;
@override final  String? refundReason;
@override final  DateTime? checkedInAt;
@override final  DateTime? completedAt;
@override final  DateTime? cancelledAt;
@override final  DateTime? refundedAt;
@override@JsonKey() final  bool isDeleted;
@override@JsonKey() final  bool reminderSent;
@override@JsonKey() final  bool confirmationSent;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of TempleBooking
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TempleBookingCopyWith<_TempleBooking> get copyWith => __$TempleBookingCopyWithImpl<_TempleBooking>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TempleBookingToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TempleBooking&&(identical(other.id, id) || other.id == id)&&(identical(other.bookingNumber, bookingNumber) || other.bookingNumber == bookingNumber)&&(identical(other.templeId, templeId) || other.templeId == templeId)&&(identical(other.templeName, templeName) || other.templeName == templeName)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.devoteeInfo, devoteeInfo) || other.devoteeInfo == devoteeInfo)&&const DeepCollectionEquality().equals(other._items, _items)&&(identical(other.bookingDate, bookingDate) || other.bookingDate == bookingDate)&&(identical(other.visitDate, visitDate) || other.visitDate == visitDate)&&(identical(other.status, status) || other.status == status)&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.subtotalAmount, subtotalAmount) || other.subtotalAmount == subtotalAmount)&&(identical(other.taxAmount, taxAmount) || other.taxAmount == taxAmount)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.totalAmount, totalAmount) || other.totalAmount == totalAmount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.paymentId, paymentId) || other.paymentId == paymentId)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.refundId, refundId) || other.refundId == refundId)&&(identical(other.qrCode, qrCode) || other.qrCode == qrCode)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.cancellationReason, cancellationReason) || other.cancellationReason == cancellationReason)&&(identical(other.refundReason, refundReason) || other.refundReason == refundReason)&&(identical(other.checkedInAt, checkedInAt) || other.checkedInAt == checkedInAt)&&(identical(other.completedAt, completedAt) || other.completedAt == completedAt)&&(identical(other.cancelledAt, cancelledAt) || other.cancelledAt == cancelledAt)&&(identical(other.refundedAt, refundedAt) || other.refundedAt == refundedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.reminderSent, reminderSent) || other.reminderSent == reminderSent)&&(identical(other.confirmationSent, confirmationSent) || other.confirmationSent == confirmationSent)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,bookingNumber,templeId,templeName,userId,devoteeInfo,const DeepCollectionEquality().hash(_items),bookingDate,visitDate,status,paymentStatus,subtotalAmount,taxAmount,discountAmount,totalAmount,createdAt,updatedAt,paymentMethod,paymentId,transactionId,refundId,qrCode,notes,cancellationReason,refundReason,checkedInAt,completedAt,cancelledAt,refundedAt,isDeleted,reminderSent,confirmationSent,const DeepCollectionEquality().hash(_metadata)]);

@override
String toString() {
  return 'TempleBooking(id: $id, bookingNumber: $bookingNumber, templeId: $templeId, templeName: $templeName, userId: $userId, devoteeInfo: $devoteeInfo, items: $items, bookingDate: $bookingDate, visitDate: $visitDate, status: $status, paymentStatus: $paymentStatus, subtotalAmount: $subtotalAmount, taxAmount: $taxAmount, discountAmount: $discountAmount, totalAmount: $totalAmount, createdAt: $createdAt, updatedAt: $updatedAt, paymentMethod: $paymentMethod, paymentId: $paymentId, transactionId: $transactionId, refundId: $refundId, qrCode: $qrCode, notes: $notes, cancellationReason: $cancellationReason, refundReason: $refundReason, checkedInAt: $checkedInAt, completedAt: $completedAt, cancelledAt: $cancelledAt, refundedAt: $refundedAt, isDeleted: $isDeleted, reminderSent: $reminderSent, confirmationSent: $confirmationSent, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$TempleBookingCopyWith<$Res> implements $TempleBookingCopyWith<$Res> {
  factory _$TempleBookingCopyWith(_TempleBooking value, $Res Function(_TempleBooking) _then) = __$TempleBookingCopyWithImpl;
@override @useResult
$Res call({
 String id, String bookingNumber, String templeId, String templeName, String userId, DevoteeInfo devoteeInfo, List<TempleBookingItem> items, DateTime bookingDate, DateTime visitDate, TempleBookingStatus status, TemplePaymentStatus paymentStatus, double subtotalAmount, double taxAmount, double discountAmount, double totalAmount, DateTime createdAt, DateTime updatedAt, String? paymentMethod, String? paymentId, String? transactionId, String? refundId, String? qrCode, String? notes, String? cancellationReason, String? refundReason, DateTime? checkedInAt, DateTime? completedAt, DateTime? cancelledAt, DateTime? refundedAt, bool isDeleted, bool reminderSent, bool confirmationSent, Map<String, dynamic>? metadata
});


@override $DevoteeInfoCopyWith<$Res> get devoteeInfo;

}
/// @nodoc
class __$TempleBookingCopyWithImpl<$Res>
    implements _$TempleBookingCopyWith<$Res> {
  __$TempleBookingCopyWithImpl(this._self, this._then);

  final _TempleBooking _self;
  final $Res Function(_TempleBooking) _then;

/// Create a copy of TempleBooking
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? bookingNumber = null,Object? templeId = null,Object? templeName = null,Object? userId = null,Object? devoteeInfo = null,Object? items = null,Object? bookingDate = null,Object? visitDate = null,Object? status = null,Object? paymentStatus = null,Object? subtotalAmount = null,Object? taxAmount = null,Object? discountAmount = null,Object? totalAmount = null,Object? createdAt = null,Object? updatedAt = null,Object? paymentMethod = freezed,Object? paymentId = freezed,Object? transactionId = freezed,Object? refundId = freezed,Object? qrCode = freezed,Object? notes = freezed,Object? cancellationReason = freezed,Object? refundReason = freezed,Object? checkedInAt = freezed,Object? completedAt = freezed,Object? cancelledAt = freezed,Object? refundedAt = freezed,Object? isDeleted = null,Object? reminderSent = null,Object? confirmationSent = null,Object? metadata = freezed,}) {
  return _then(_TempleBooking(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,bookingNumber: null == bookingNumber ? _self.bookingNumber : bookingNumber // ignore: cast_nullable_to_non_nullable
as String,templeId: null == templeId ? _self.templeId : templeId // ignore: cast_nullable_to_non_nullable
as String,templeName: null == templeName ? _self.templeName : templeName // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,devoteeInfo: null == devoteeInfo ? _self.devoteeInfo : devoteeInfo // ignore: cast_nullable_to_non_nullable
as DevoteeInfo,items: null == items ? _self._items : items // ignore: cast_nullable_to_non_nullable
as List<TempleBookingItem>,bookingDate: null == bookingDate ? _self.bookingDate : bookingDate // ignore: cast_nullable_to_non_nullable
as DateTime,visitDate: null == visitDate ? _self.visitDate : visitDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as TempleBookingStatus,paymentStatus: null == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as TemplePaymentStatus,subtotalAmount: null == subtotalAmount ? _self.subtotalAmount : subtotalAmount // ignore: cast_nullable_to_non_nullable
as double,taxAmount: null == taxAmount ? _self.taxAmount : taxAmount // ignore: cast_nullable_to_non_nullable
as double,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as double,totalAmount: null == totalAmount ? _self.totalAmount : totalAmount // ignore: cast_nullable_to_non_nullable
as double,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,paymentMethod: freezed == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as String?,paymentId: freezed == paymentId ? _self.paymentId : paymentId // ignore: cast_nullable_to_non_nullable
as String?,transactionId: freezed == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String?,refundId: freezed == refundId ? _self.refundId : refundId // ignore: cast_nullable_to_non_nullable
as String?,qrCode: freezed == qrCode ? _self.qrCode : qrCode // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,cancellationReason: freezed == cancellationReason ? _self.cancellationReason : cancellationReason // ignore: cast_nullable_to_non_nullable
as String?,refundReason: freezed == refundReason ? _self.refundReason : refundReason // ignore: cast_nullable_to_non_nullable
as String?,checkedInAt: freezed == checkedInAt ? _self.checkedInAt : checkedInAt // ignore: cast_nullable_to_non_nullable
as DateTime?,completedAt: freezed == completedAt ? _self.completedAt : completedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,cancelledAt: freezed == cancelledAt ? _self.cancelledAt : cancelledAt // ignore: cast_nullable_to_non_nullable
as DateTime?,refundedAt: freezed == refundedAt ? _self.refundedAt : refundedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,reminderSent: null == reminderSent ? _self.reminderSent : reminderSent // ignore: cast_nullable_to_non_nullable
as bool,confirmationSent: null == confirmationSent ? _self.confirmationSent : confirmationSent // ignore: cast_nullable_to_non_nullable
as bool,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

/// Create a copy of TempleBooking
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DevoteeInfoCopyWith<$Res> get devoteeInfo {
  
  return $DevoteeInfoCopyWith<$Res>(_self.devoteeInfo, (value) {
    return _then(_self.copyWith(devoteeInfo: value));
  });
}
}


/// @nodoc
mixin _$TempleReview {

 String get id; String get templeId; String get userId; String get userName; double get rating; String get comment; DateTime get createdAt; DateTime get updatedAt; bool get isVerified; bool get isDeleted; String? get bookingId; List<String>? get images; Map<String, dynamic>? get additionalInfo;
/// Create a copy of TempleReview
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TempleReviewCopyWith<TempleReview> get copyWith => _$TempleReviewCopyWithImpl<TempleReview>(this as TempleReview, _$identity);

  /// Serializes this TempleReview to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TempleReview&&(identical(other.id, id) || other.id == id)&&(identical(other.templeId, templeId) || other.templeId == templeId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.comment, comment) || other.comment == comment)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.bookingId, bookingId) || other.bookingId == bookingId)&&const DeepCollectionEquality().equals(other.images, images)&&const DeepCollectionEquality().equals(other.additionalInfo, additionalInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,templeId,userId,userName,rating,comment,createdAt,updatedAt,isVerified,isDeleted,bookingId,const DeepCollectionEquality().hash(images),const DeepCollectionEquality().hash(additionalInfo));

@override
String toString() {
  return 'TempleReview(id: $id, templeId: $templeId, userId: $userId, userName: $userName, rating: $rating, comment: $comment, createdAt: $createdAt, updatedAt: $updatedAt, isVerified: $isVerified, isDeleted: $isDeleted, bookingId: $bookingId, images: $images, additionalInfo: $additionalInfo)';
}


}

/// @nodoc
abstract mixin class $TempleReviewCopyWith<$Res>  {
  factory $TempleReviewCopyWith(TempleReview value, $Res Function(TempleReview) _then) = _$TempleReviewCopyWithImpl;
@useResult
$Res call({
 String id, String templeId, String userId, String userName, double rating, String comment, DateTime createdAt, DateTime updatedAt, bool isVerified, bool isDeleted, String? bookingId, List<String>? images, Map<String, dynamic>? additionalInfo
});




}
/// @nodoc
class _$TempleReviewCopyWithImpl<$Res>
    implements $TempleReviewCopyWith<$Res> {
  _$TempleReviewCopyWithImpl(this._self, this._then);

  final TempleReview _self;
  final $Res Function(TempleReview) _then;

/// Create a copy of TempleReview
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? templeId = null,Object? userId = null,Object? userName = null,Object? rating = null,Object? comment = null,Object? createdAt = null,Object? updatedAt = null,Object? isVerified = null,Object? isDeleted = null,Object? bookingId = freezed,Object? images = freezed,Object? additionalInfo = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,templeId: null == templeId ? _self.templeId : templeId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,comment: null == comment ? _self.comment : comment // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,bookingId: freezed == bookingId ? _self.bookingId : bookingId // ignore: cast_nullable_to_non_nullable
as String?,images: freezed == images ? _self.images : images // ignore: cast_nullable_to_non_nullable
as List<String>?,additionalInfo: freezed == additionalInfo ? _self.additionalInfo : additionalInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [TempleReview].
extension TempleReviewPatterns on TempleReview {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TempleReview value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TempleReview() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TempleReview value)  $default,){
final _that = this;
switch (_that) {
case _TempleReview():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TempleReview value)?  $default,){
final _that = this;
switch (_that) {
case _TempleReview() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String templeId,  String userId,  String userName,  double rating,  String comment,  DateTime createdAt,  DateTime updatedAt,  bool isVerified,  bool isDeleted,  String? bookingId,  List<String>? images,  Map<String, dynamic>? additionalInfo)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TempleReview() when $default != null:
return $default(_that.id,_that.templeId,_that.userId,_that.userName,_that.rating,_that.comment,_that.createdAt,_that.updatedAt,_that.isVerified,_that.isDeleted,_that.bookingId,_that.images,_that.additionalInfo);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String templeId,  String userId,  String userName,  double rating,  String comment,  DateTime createdAt,  DateTime updatedAt,  bool isVerified,  bool isDeleted,  String? bookingId,  List<String>? images,  Map<String, dynamic>? additionalInfo)  $default,) {final _that = this;
switch (_that) {
case _TempleReview():
return $default(_that.id,_that.templeId,_that.userId,_that.userName,_that.rating,_that.comment,_that.createdAt,_that.updatedAt,_that.isVerified,_that.isDeleted,_that.bookingId,_that.images,_that.additionalInfo);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String templeId,  String userId,  String userName,  double rating,  String comment,  DateTime createdAt,  DateTime updatedAt,  bool isVerified,  bool isDeleted,  String? bookingId,  List<String>? images,  Map<String, dynamic>? additionalInfo)?  $default,) {final _that = this;
switch (_that) {
case _TempleReview() when $default != null:
return $default(_that.id,_that.templeId,_that.userId,_that.userName,_that.rating,_that.comment,_that.createdAt,_that.updatedAt,_that.isVerified,_that.isDeleted,_that.bookingId,_that.images,_that.additionalInfo);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TempleReview implements TempleReview {
  const _TempleReview({required this.id, required this.templeId, required this.userId, required this.userName, required this.rating, required this.comment, required this.createdAt, required this.updatedAt, this.isVerified = false, this.isDeleted = false, this.bookingId, final  List<String>? images, final  Map<String, dynamic>? additionalInfo}): _images = images,_additionalInfo = additionalInfo;
  factory _TempleReview.fromJson(Map<String, dynamic> json) => _$TempleReviewFromJson(json);

@override final  String id;
@override final  String templeId;
@override final  String userId;
@override final  String userName;
@override final  double rating;
@override final  String comment;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool isVerified;
@override@JsonKey() final  bool isDeleted;
@override final  String? bookingId;
 final  List<String>? _images;
@override List<String>? get images {
  final value = _images;
  if (value == null) return null;
  if (_images is EqualUnmodifiableListView) return _images;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  Map<String, dynamic>? _additionalInfo;
@override Map<String, dynamic>? get additionalInfo {
  final value = _additionalInfo;
  if (value == null) return null;
  if (_additionalInfo is EqualUnmodifiableMapView) return _additionalInfo;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of TempleReview
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TempleReviewCopyWith<_TempleReview> get copyWith => __$TempleReviewCopyWithImpl<_TempleReview>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TempleReviewToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TempleReview&&(identical(other.id, id) || other.id == id)&&(identical(other.templeId, templeId) || other.templeId == templeId)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.rating, rating) || other.rating == rating)&&(identical(other.comment, comment) || other.comment == comment)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.bookingId, bookingId) || other.bookingId == bookingId)&&const DeepCollectionEquality().equals(other._images, _images)&&const DeepCollectionEquality().equals(other._additionalInfo, _additionalInfo));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,templeId,userId,userName,rating,comment,createdAt,updatedAt,isVerified,isDeleted,bookingId,const DeepCollectionEquality().hash(_images),const DeepCollectionEquality().hash(_additionalInfo));

@override
String toString() {
  return 'TempleReview(id: $id, templeId: $templeId, userId: $userId, userName: $userName, rating: $rating, comment: $comment, createdAt: $createdAt, updatedAt: $updatedAt, isVerified: $isVerified, isDeleted: $isDeleted, bookingId: $bookingId, images: $images, additionalInfo: $additionalInfo)';
}


}

/// @nodoc
abstract mixin class _$TempleReviewCopyWith<$Res> implements $TempleReviewCopyWith<$Res> {
  factory _$TempleReviewCopyWith(_TempleReview value, $Res Function(_TempleReview) _then) = __$TempleReviewCopyWithImpl;
@override @useResult
$Res call({
 String id, String templeId, String userId, String userName, double rating, String comment, DateTime createdAt, DateTime updatedAt, bool isVerified, bool isDeleted, String? bookingId, List<String>? images, Map<String, dynamic>? additionalInfo
});




}
/// @nodoc
class __$TempleReviewCopyWithImpl<$Res>
    implements _$TempleReviewCopyWith<$Res> {
  __$TempleReviewCopyWithImpl(this._self, this._then);

  final _TempleReview _self;
  final $Res Function(_TempleReview) _then;

/// Create a copy of TempleReview
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? templeId = null,Object? userId = null,Object? userName = null,Object? rating = null,Object? comment = null,Object? createdAt = null,Object? updatedAt = null,Object? isVerified = null,Object? isDeleted = null,Object? bookingId = freezed,Object? images = freezed,Object? additionalInfo = freezed,}) {
  return _then(_TempleReview(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,templeId: null == templeId ? _self.templeId : templeId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,rating: null == rating ? _self.rating : rating // ignore: cast_nullable_to_non_nullable
as double,comment: null == comment ? _self.comment : comment // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,bookingId: freezed == bookingId ? _self.bookingId : bookingId // ignore: cast_nullable_to_non_nullable
as String?,images: freezed == images ? _self._images : images // ignore: cast_nullable_to_non_nullable
as List<String>?,additionalInfo: freezed == additionalInfo ? _self._additionalInfo : additionalInfo // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$TempleNotification {

 String get id; String get templeId; String get templeName; String get title; String get message; String get type;// 'nearby', 'festival', 'special_event', 'booking_reminder'
 DateTime get createdAt; bool get isRead; bool get isDeleted; String? get userId; String? get bookingId; Map<String, dynamic>? get data;
/// Create a copy of TempleNotification
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TempleNotificationCopyWith<TempleNotification> get copyWith => _$TempleNotificationCopyWithImpl<TempleNotification>(this as TempleNotification, _$identity);

  /// Serializes this TempleNotification to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TempleNotification&&(identical(other.id, id) || other.id == id)&&(identical(other.templeId, templeId) || other.templeId == templeId)&&(identical(other.templeName, templeName) || other.templeName == templeName)&&(identical(other.title, title) || other.title == title)&&(identical(other.message, message) || other.message == message)&&(identical(other.type, type) || other.type == type)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.isRead, isRead) || other.isRead == isRead)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.bookingId, bookingId) || other.bookingId == bookingId)&&const DeepCollectionEquality().equals(other.data, data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,templeId,templeName,title,message,type,createdAt,isRead,isDeleted,userId,bookingId,const DeepCollectionEquality().hash(data));

@override
String toString() {
  return 'TempleNotification(id: $id, templeId: $templeId, templeName: $templeName, title: $title, message: $message, type: $type, createdAt: $createdAt, isRead: $isRead, isDeleted: $isDeleted, userId: $userId, bookingId: $bookingId, data: $data)';
}


}

/// @nodoc
abstract mixin class $TempleNotificationCopyWith<$Res>  {
  factory $TempleNotificationCopyWith(TempleNotification value, $Res Function(TempleNotification) _then) = _$TempleNotificationCopyWithImpl;
@useResult
$Res call({
 String id, String templeId, String templeName, String title, String message, String type, DateTime createdAt, bool isRead, bool isDeleted, String? userId, String? bookingId, Map<String, dynamic>? data
});




}
/// @nodoc
class _$TempleNotificationCopyWithImpl<$Res>
    implements $TempleNotificationCopyWith<$Res> {
  _$TempleNotificationCopyWithImpl(this._self, this._then);

  final TempleNotification _self;
  final $Res Function(TempleNotification) _then;

/// Create a copy of TempleNotification
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? templeId = null,Object? templeName = null,Object? title = null,Object? message = null,Object? type = null,Object? createdAt = null,Object? isRead = null,Object? isDeleted = null,Object? userId = freezed,Object? bookingId = freezed,Object? data = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,templeId: null == templeId ? _self.templeId : templeId // ignore: cast_nullable_to_non_nullable
as String,templeName: null == templeName ? _self.templeName : templeName // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,bookingId: freezed == bookingId ? _self.bookingId : bookingId // ignore: cast_nullable_to_non_nullable
as String?,data: freezed == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [TempleNotification].
extension TempleNotificationPatterns on TempleNotification {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TempleNotification value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TempleNotification() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TempleNotification value)  $default,){
final _that = this;
switch (_that) {
case _TempleNotification():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TempleNotification value)?  $default,){
final _that = this;
switch (_that) {
case _TempleNotification() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String templeId,  String templeName,  String title,  String message,  String type,  DateTime createdAt,  bool isRead,  bool isDeleted,  String? userId,  String? bookingId,  Map<String, dynamic>? data)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TempleNotification() when $default != null:
return $default(_that.id,_that.templeId,_that.templeName,_that.title,_that.message,_that.type,_that.createdAt,_that.isRead,_that.isDeleted,_that.userId,_that.bookingId,_that.data);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String templeId,  String templeName,  String title,  String message,  String type,  DateTime createdAt,  bool isRead,  bool isDeleted,  String? userId,  String? bookingId,  Map<String, dynamic>? data)  $default,) {final _that = this;
switch (_that) {
case _TempleNotification():
return $default(_that.id,_that.templeId,_that.templeName,_that.title,_that.message,_that.type,_that.createdAt,_that.isRead,_that.isDeleted,_that.userId,_that.bookingId,_that.data);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String templeId,  String templeName,  String title,  String message,  String type,  DateTime createdAt,  bool isRead,  bool isDeleted,  String? userId,  String? bookingId,  Map<String, dynamic>? data)?  $default,) {final _that = this;
switch (_that) {
case _TempleNotification() when $default != null:
return $default(_that.id,_that.templeId,_that.templeName,_that.title,_that.message,_that.type,_that.createdAt,_that.isRead,_that.isDeleted,_that.userId,_that.bookingId,_that.data);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TempleNotification implements TempleNotification {
  const _TempleNotification({required this.id, required this.templeId, required this.templeName, required this.title, required this.message, required this.type, required this.createdAt, this.isRead = false, this.isDeleted = false, this.userId, this.bookingId, final  Map<String, dynamic>? data}): _data = data;
  factory _TempleNotification.fromJson(Map<String, dynamic> json) => _$TempleNotificationFromJson(json);

@override final  String id;
@override final  String templeId;
@override final  String templeName;
@override final  String title;
@override final  String message;
@override final  String type;
// 'nearby', 'festival', 'special_event', 'booking_reminder'
@override final  DateTime createdAt;
@override@JsonKey() final  bool isRead;
@override@JsonKey() final  bool isDeleted;
@override final  String? userId;
@override final  String? bookingId;
 final  Map<String, dynamic>? _data;
@override Map<String, dynamic>? get data {
  final value = _data;
  if (value == null) return null;
  if (_data is EqualUnmodifiableMapView) return _data;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of TempleNotification
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TempleNotificationCopyWith<_TempleNotification> get copyWith => __$TempleNotificationCopyWithImpl<_TempleNotification>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TempleNotificationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TempleNotification&&(identical(other.id, id) || other.id == id)&&(identical(other.templeId, templeId) || other.templeId == templeId)&&(identical(other.templeName, templeName) || other.templeName == templeName)&&(identical(other.title, title) || other.title == title)&&(identical(other.message, message) || other.message == message)&&(identical(other.type, type) || other.type == type)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.isRead, isRead) || other.isRead == isRead)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.bookingId, bookingId) || other.bookingId == bookingId)&&const DeepCollectionEquality().equals(other._data, _data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,templeId,templeName,title,message,type,createdAt,isRead,isDeleted,userId,bookingId,const DeepCollectionEquality().hash(_data));

@override
String toString() {
  return 'TempleNotification(id: $id, templeId: $templeId, templeName: $templeName, title: $title, message: $message, type: $type, createdAt: $createdAt, isRead: $isRead, isDeleted: $isDeleted, userId: $userId, bookingId: $bookingId, data: $data)';
}


}

/// @nodoc
abstract mixin class _$TempleNotificationCopyWith<$Res> implements $TempleNotificationCopyWith<$Res> {
  factory _$TempleNotificationCopyWith(_TempleNotification value, $Res Function(_TempleNotification) _then) = __$TempleNotificationCopyWithImpl;
@override @useResult
$Res call({
 String id, String templeId, String templeName, String title, String message, String type, DateTime createdAt, bool isRead, bool isDeleted, String? userId, String? bookingId, Map<String, dynamic>? data
});




}
/// @nodoc
class __$TempleNotificationCopyWithImpl<$Res>
    implements _$TempleNotificationCopyWith<$Res> {
  __$TempleNotificationCopyWithImpl(this._self, this._then);

  final _TempleNotification _self;
  final $Res Function(_TempleNotification) _then;

/// Create a copy of TempleNotification
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? templeId = null,Object? templeName = null,Object? title = null,Object? message = null,Object? type = null,Object? createdAt = null,Object? isRead = null,Object? isDeleted = null,Object? userId = freezed,Object? bookingId = freezed,Object? data = freezed,}) {
  return _then(_TempleNotification(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,templeId: null == templeId ? _self.templeId : templeId // ignore: cast_nullable_to_non_nullable
as String,templeName: null == templeName ? _self.templeName : templeName // ignore: cast_nullable_to_non_nullable
as String,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,isRead: null == isRead ? _self.isRead : isRead // ignore: cast_nullable_to_non_nullable
as bool,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,userId: freezed == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String?,bookingId: freezed == bookingId ? _self.bookingId : bookingId // ignore: cast_nullable_to_non_nullable
as String?,data: freezed == data ? _self._data : data // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$TempleSearchFilters {

 String? get query; String? get city; String? get state; List<TempleType>? get types; List<String>? get deities; List<DarshanType>? get darshanTypes; List<SevaType>? get sevaTypes; double? get maxDistance; double? get minRating; bool? get isOpen; bool? get isFamous; bool? get isPilgrimage; double? get latitude; double? get longitude;
/// Create a copy of TempleSearchFilters
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TempleSearchFiltersCopyWith<TempleSearchFilters> get copyWith => _$TempleSearchFiltersCopyWithImpl<TempleSearchFilters>(this as TempleSearchFilters, _$identity);

  /// Serializes this TempleSearchFilters to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TempleSearchFilters&&(identical(other.query, query) || other.query == query)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&const DeepCollectionEquality().equals(other.types, types)&&const DeepCollectionEquality().equals(other.deities, deities)&&const DeepCollectionEquality().equals(other.darshanTypes, darshanTypes)&&const DeepCollectionEquality().equals(other.sevaTypes, sevaTypes)&&(identical(other.maxDistance, maxDistance) || other.maxDistance == maxDistance)&&(identical(other.minRating, minRating) || other.minRating == minRating)&&(identical(other.isOpen, isOpen) || other.isOpen == isOpen)&&(identical(other.isFamous, isFamous) || other.isFamous == isFamous)&&(identical(other.isPilgrimage, isPilgrimage) || other.isPilgrimage == isPilgrimage)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,city,state,const DeepCollectionEquality().hash(types),const DeepCollectionEquality().hash(deities),const DeepCollectionEquality().hash(darshanTypes),const DeepCollectionEquality().hash(sevaTypes),maxDistance,minRating,isOpen,isFamous,isPilgrimage,latitude,longitude);

@override
String toString() {
  return 'TempleSearchFilters(query: $query, city: $city, state: $state, types: $types, deities: $deities, darshanTypes: $darshanTypes, sevaTypes: $sevaTypes, maxDistance: $maxDistance, minRating: $minRating, isOpen: $isOpen, isFamous: $isFamous, isPilgrimage: $isPilgrimage, latitude: $latitude, longitude: $longitude)';
}


}

/// @nodoc
abstract mixin class $TempleSearchFiltersCopyWith<$Res>  {
  factory $TempleSearchFiltersCopyWith(TempleSearchFilters value, $Res Function(TempleSearchFilters) _then) = _$TempleSearchFiltersCopyWithImpl;
@useResult
$Res call({
 String? query, String? city, String? state, List<TempleType>? types, List<String>? deities, List<DarshanType>? darshanTypes, List<SevaType>? sevaTypes, double? maxDistance, double? minRating, bool? isOpen, bool? isFamous, bool? isPilgrimage, double? latitude, double? longitude
});




}
/// @nodoc
class _$TempleSearchFiltersCopyWithImpl<$Res>
    implements $TempleSearchFiltersCopyWith<$Res> {
  _$TempleSearchFiltersCopyWithImpl(this._self, this._then);

  final TempleSearchFilters _self;
  final $Res Function(TempleSearchFilters) _then;

/// Create a copy of TempleSearchFilters
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? query = freezed,Object? city = freezed,Object? state = freezed,Object? types = freezed,Object? deities = freezed,Object? darshanTypes = freezed,Object? sevaTypes = freezed,Object? maxDistance = freezed,Object? minRating = freezed,Object? isOpen = freezed,Object? isFamous = freezed,Object? isPilgrimage = freezed,Object? latitude = freezed,Object? longitude = freezed,}) {
  return _then(_self.copyWith(
query: freezed == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String?,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,state: freezed == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String?,types: freezed == types ? _self.types : types // ignore: cast_nullable_to_non_nullable
as List<TempleType>?,deities: freezed == deities ? _self.deities : deities // ignore: cast_nullable_to_non_nullable
as List<String>?,darshanTypes: freezed == darshanTypes ? _self.darshanTypes : darshanTypes // ignore: cast_nullable_to_non_nullable
as List<DarshanType>?,sevaTypes: freezed == sevaTypes ? _self.sevaTypes : sevaTypes // ignore: cast_nullable_to_non_nullable
as List<SevaType>?,maxDistance: freezed == maxDistance ? _self.maxDistance : maxDistance // ignore: cast_nullable_to_non_nullable
as double?,minRating: freezed == minRating ? _self.minRating : minRating // ignore: cast_nullable_to_non_nullable
as double?,isOpen: freezed == isOpen ? _self.isOpen : isOpen // ignore: cast_nullable_to_non_nullable
as bool?,isFamous: freezed == isFamous ? _self.isFamous : isFamous // ignore: cast_nullable_to_non_nullable
as bool?,isPilgrimage: freezed == isPilgrimage ? _self.isPilgrimage : isPilgrimage // ignore: cast_nullable_to_non_nullable
as bool?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}

}


/// Adds pattern-matching-related methods to [TempleSearchFilters].
extension TempleSearchFiltersPatterns on TempleSearchFilters {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TempleSearchFilters value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TempleSearchFilters() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TempleSearchFilters value)  $default,){
final _that = this;
switch (_that) {
case _TempleSearchFilters():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TempleSearchFilters value)?  $default,){
final _that = this;
switch (_that) {
case _TempleSearchFilters() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? query,  String? city,  String? state,  List<TempleType>? types,  List<String>? deities,  List<DarshanType>? darshanTypes,  List<SevaType>? sevaTypes,  double? maxDistance,  double? minRating,  bool? isOpen,  bool? isFamous,  bool? isPilgrimage,  double? latitude,  double? longitude)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TempleSearchFilters() when $default != null:
return $default(_that.query,_that.city,_that.state,_that.types,_that.deities,_that.darshanTypes,_that.sevaTypes,_that.maxDistance,_that.minRating,_that.isOpen,_that.isFamous,_that.isPilgrimage,_that.latitude,_that.longitude);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? query,  String? city,  String? state,  List<TempleType>? types,  List<String>? deities,  List<DarshanType>? darshanTypes,  List<SevaType>? sevaTypes,  double? maxDistance,  double? minRating,  bool? isOpen,  bool? isFamous,  bool? isPilgrimage,  double? latitude,  double? longitude)  $default,) {final _that = this;
switch (_that) {
case _TempleSearchFilters():
return $default(_that.query,_that.city,_that.state,_that.types,_that.deities,_that.darshanTypes,_that.sevaTypes,_that.maxDistance,_that.minRating,_that.isOpen,_that.isFamous,_that.isPilgrimage,_that.latitude,_that.longitude);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? query,  String? city,  String? state,  List<TempleType>? types,  List<String>? deities,  List<DarshanType>? darshanTypes,  List<SevaType>? sevaTypes,  double? maxDistance,  double? minRating,  bool? isOpen,  bool? isFamous,  bool? isPilgrimage,  double? latitude,  double? longitude)?  $default,) {final _that = this;
switch (_that) {
case _TempleSearchFilters() when $default != null:
return $default(_that.query,_that.city,_that.state,_that.types,_that.deities,_that.darshanTypes,_that.sevaTypes,_that.maxDistance,_that.minRating,_that.isOpen,_that.isFamous,_that.isPilgrimage,_that.latitude,_that.longitude);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TempleSearchFilters implements TempleSearchFilters {
  const _TempleSearchFilters({this.query, this.city, this.state, final  List<TempleType>? types, final  List<String>? deities, final  List<DarshanType>? darshanTypes, final  List<SevaType>? sevaTypes, this.maxDistance, this.minRating, this.isOpen, this.isFamous, this.isPilgrimage, this.latitude, this.longitude}): _types = types,_deities = deities,_darshanTypes = darshanTypes,_sevaTypes = sevaTypes;
  factory _TempleSearchFilters.fromJson(Map<String, dynamic> json) => _$TempleSearchFiltersFromJson(json);

@override final  String? query;
@override final  String? city;
@override final  String? state;
 final  List<TempleType>? _types;
@override List<TempleType>? get types {
  final value = _types;
  if (value == null) return null;
  if (_types is EqualUnmodifiableListView) return _types;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _deities;
@override List<String>? get deities {
  final value = _deities;
  if (value == null) return null;
  if (_deities is EqualUnmodifiableListView) return _deities;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<DarshanType>? _darshanTypes;
@override List<DarshanType>? get darshanTypes {
  final value = _darshanTypes;
  if (value == null) return null;
  if (_darshanTypes is EqualUnmodifiableListView) return _darshanTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<SevaType>? _sevaTypes;
@override List<SevaType>? get sevaTypes {
  final value = _sevaTypes;
  if (value == null) return null;
  if (_sevaTypes is EqualUnmodifiableListView) return _sevaTypes;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  double? maxDistance;
@override final  double? minRating;
@override final  bool? isOpen;
@override final  bool? isFamous;
@override final  bool? isPilgrimage;
@override final  double? latitude;
@override final  double? longitude;

/// Create a copy of TempleSearchFilters
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TempleSearchFiltersCopyWith<_TempleSearchFilters> get copyWith => __$TempleSearchFiltersCopyWithImpl<_TempleSearchFilters>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TempleSearchFiltersToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TempleSearchFilters&&(identical(other.query, query) || other.query == query)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&const DeepCollectionEquality().equals(other._types, _types)&&const DeepCollectionEquality().equals(other._deities, _deities)&&const DeepCollectionEquality().equals(other._darshanTypes, _darshanTypes)&&const DeepCollectionEquality().equals(other._sevaTypes, _sevaTypes)&&(identical(other.maxDistance, maxDistance) || other.maxDistance == maxDistance)&&(identical(other.minRating, minRating) || other.minRating == minRating)&&(identical(other.isOpen, isOpen) || other.isOpen == isOpen)&&(identical(other.isFamous, isFamous) || other.isFamous == isFamous)&&(identical(other.isPilgrimage, isPilgrimage) || other.isPilgrimage == isPilgrimage)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,query,city,state,const DeepCollectionEquality().hash(_types),const DeepCollectionEquality().hash(_deities),const DeepCollectionEquality().hash(_darshanTypes),const DeepCollectionEquality().hash(_sevaTypes),maxDistance,minRating,isOpen,isFamous,isPilgrimage,latitude,longitude);

@override
String toString() {
  return 'TempleSearchFilters(query: $query, city: $city, state: $state, types: $types, deities: $deities, darshanTypes: $darshanTypes, sevaTypes: $sevaTypes, maxDistance: $maxDistance, minRating: $minRating, isOpen: $isOpen, isFamous: $isFamous, isPilgrimage: $isPilgrimage, latitude: $latitude, longitude: $longitude)';
}


}

/// @nodoc
abstract mixin class _$TempleSearchFiltersCopyWith<$Res> implements $TempleSearchFiltersCopyWith<$Res> {
  factory _$TempleSearchFiltersCopyWith(_TempleSearchFilters value, $Res Function(_TempleSearchFilters) _then) = __$TempleSearchFiltersCopyWithImpl;
@override @useResult
$Res call({
 String? query, String? city, String? state, List<TempleType>? types, List<String>? deities, List<DarshanType>? darshanTypes, List<SevaType>? sevaTypes, double? maxDistance, double? minRating, bool? isOpen, bool? isFamous, bool? isPilgrimage, double? latitude, double? longitude
});




}
/// @nodoc
class __$TempleSearchFiltersCopyWithImpl<$Res>
    implements _$TempleSearchFiltersCopyWith<$Res> {
  __$TempleSearchFiltersCopyWithImpl(this._self, this._then);

  final _TempleSearchFilters _self;
  final $Res Function(_TempleSearchFilters) _then;

/// Create a copy of TempleSearchFilters
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? query = freezed,Object? city = freezed,Object? state = freezed,Object? types = freezed,Object? deities = freezed,Object? darshanTypes = freezed,Object? sevaTypes = freezed,Object? maxDistance = freezed,Object? minRating = freezed,Object? isOpen = freezed,Object? isFamous = freezed,Object? isPilgrimage = freezed,Object? latitude = freezed,Object? longitude = freezed,}) {
  return _then(_TempleSearchFilters(
query: freezed == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String?,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,state: freezed == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String?,types: freezed == types ? _self._types : types // ignore: cast_nullable_to_non_nullable
as List<TempleType>?,deities: freezed == deities ? _self._deities : deities // ignore: cast_nullable_to_non_nullable
as List<String>?,darshanTypes: freezed == darshanTypes ? _self._darshanTypes : darshanTypes // ignore: cast_nullable_to_non_nullable
as List<DarshanType>?,sevaTypes: freezed == sevaTypes ? _self._sevaTypes : sevaTypes // ignore: cast_nullable_to_non_nullable
as List<SevaType>?,maxDistance: freezed == maxDistance ? _self.maxDistance : maxDistance // ignore: cast_nullable_to_non_nullable
as double?,minRating: freezed == minRating ? _self.minRating : minRating // ignore: cast_nullable_to_non_nullable
as double?,isOpen: freezed == isOpen ? _self.isOpen : isOpen // ignore: cast_nullable_to_non_nullable
as bool?,isFamous: freezed == isFamous ? _self.isFamous : isFamous // ignore: cast_nullable_to_non_nullable
as bool?,isPilgrimage: freezed == isPilgrimage ? _self.isPilgrimage : isPilgrimage // ignore: cast_nullable_to_non_nullable
as bool?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}


}

// dart format on
