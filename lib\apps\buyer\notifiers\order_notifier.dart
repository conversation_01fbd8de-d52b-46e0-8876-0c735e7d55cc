import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../shared/models/order/order_model.dart';
import '../repositories/order_repository.dart';
import '../providers/order_provider.dart';
import 'order_state.dart';

/// Order provider
final orderProvider = StateNotifierProvider<OrderNotifier, OrderState>((ref) {
  final orderRepository = ref.watch(orderRepositoryProvider);
  return OrderNotifier(orderRepository);
});

/// Orders provider
final ordersProvider =
    StateNotifierProvider<OrdersNotifier, OrdersState>((ref) {
  final orderRepository = ref.watch(orderRepositoryProvider);
  return OrdersNotifier(orderRepository);
});

/// Order notifier
class OrderNotifier extends StateNotifier<OrderState> {
  /// Creates an [OrderNotifier]
  OrderNotifier(this._orderRepository) : super(const OrderState.initial());

  final OrderRepository _orderRepository;

  /// Create order
  Future<OrderModel> createOrder(OrderModel order) async {
    try {
      state = const OrderState.loading();
      final createdOrder = await _orderRepository.createOrder(order);
      state = OrderState.loaded(createdOrder);
      return createdOrder;
    } catch (e) {
      state = OrderState.error(e.toString());
      rethrow; // Re-throw to allow caller to handle the error
    }
  }

  /// Get order by ID
  Future<void> getOrderById(String id) async {
    try {
      state = const OrderState.loading();
      final order = await _orderRepository.getOrderById(id);
      if (order != null) {
        state = OrderState.loaded(order);
      } else {
        state = const OrderState.error('Order not found');
      }
    } catch (e) {
      state = OrderState.error(e.toString());
    }
  }

  /// Update order status
  Future<void> updateOrderStatus(String id, OrderStatus status) async {
    try {
      state = const OrderState.loading();
      final updatedOrder = await _orderRepository.updateOrderStatus(id, status);
      state = OrderState.loaded(updatedOrder);
    } catch (e) {
      state = OrderState.error(e.toString());
    }
  }

  /// Cancel order
  Future<void> cancelOrder(String id) async {
    try {
      state = const OrderState.loading();
      final cancelledOrder = await _orderRepository.cancelOrder(id);
      state = OrderState.loaded(cancelledOrder);
    } catch (e) {
      state = OrderState.error(e.toString());
    }
  }
}

/// Orders notifier
class OrdersNotifier extends StateNotifier<OrdersState> {
  /// Creates an [OrdersNotifier]
  OrdersNotifier(this._orderRepository) : super(const OrdersState.initial());

  final OrderRepository _orderRepository;

  /// Get orders by user ID
  Future<void> getOrdersByUserId(String userId) async {
    try {
      state = const OrdersState.loading();
      final orders = await _orderRepository.getOrdersByUserId(userId);
      state = OrdersState.loaded(orders);
    } catch (e) {
      state = OrdersState.error(e.toString());
    }
  }
}
