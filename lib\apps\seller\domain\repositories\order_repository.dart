import '../../../../shared/models/order/order_model.dart';

/// Repository interface for order-related operations
abstract class OrderRepository {
  /// Get all orders for a seller
  Future<List<OrderModel>> getOrders(String sellerId);

  /// Get a specific order by ID
  Future<OrderModel?> getOrderById(String id);

  /// Update order status
  Future<OrderModel> updateOrderStatus(String id, OrderStatus status);

  /// Update payment status
  Future<OrderModel> updatePaymentStatus(String id, PaymentStatus status);

  /// Add tracking information
  Future<OrderModel> addTrackingInfo(
    String id, {
    required String trackingNumber,
    required String shippingProvider,
    DateTime? estimatedDeliveryDate,
  });

  /// Cancel order
  Future<OrderModel> cancelOrder(String id, {String? reason});

  /// Refund order
  Future<OrderModel> refundOrder(String id, {String? reason});

  /// Add note to order
  Future<OrderModel> addOrderNote(String id, String note);
}
