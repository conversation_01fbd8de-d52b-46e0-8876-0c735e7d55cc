import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/apps/seller/domain/models/analytics_model.dart';

part 'store_performance_state.freezed.dart';

@freezed
sealed class StorePerformanceState with _$StorePerformanceState {
  const factory StorePerformanceState({
    @Default(false) bool isLoading,
    @Default(false) bool hasError,
    String? errorMessage,
    PerformanceMetrics? metrics,
    @Default('week') String timeRange,
  }) = _StorePerformanceState;
}
