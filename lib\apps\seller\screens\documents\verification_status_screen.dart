import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/utils/date_utils.dart' as date_utils;
import 'package:shivish/apps/seller/domain/models/document_model.dart';
import 'package:shivish/apps/seller/presentation/providers/document_providers.dart';

class VerificationStatusScreen extends ConsumerWidget {
  const VerificationStatusScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final documentsAsync = ref.watch(documentsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Verification Status'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(documentsProvider);
            },
          ),
        ],
      ),
      body: documentsAsync.when(
        data: (documents) {
          if (documents.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'No documents uploaded yet',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  AppButton(
                    onPressed: () {
                      // Navigate to document upload screen
                      Navigator.pushNamed(context, '/seller/documents/upload');
                    },
                    child: const Text('Upload Document'),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: documents.length,
            itemBuilder: (context, index) {
              final document = documents[index];
              return _DocumentStatusCard(document: document);
            },
          );
        },
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Error loading documents: $error',
                style: const TextStyle(color: Colors.red),
              ),
              const SizedBox(height: 16),
              AppButton(
                onPressed: () {
                  ref.invalidate(documentsProvider);
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _DocumentStatusCard extends StatelessWidget {
  final DocumentModel document;

  const _DocumentStatusCard({
    required this.document,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getStatusIcon(),
                  color: _getStatusColor(),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    document.type.name,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                _buildStatusChip(),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Uploaded on ${date_utils.formatDate(document.uploadedAt)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            if (document.verifiedAt != null) ...[
              const SizedBox(height: 4),
              Text(
                'Verified on ${date_utils.formatDate(document.verifiedAt!)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
            if (document.rejectionReason != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        document.rejectionReason!,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            if (document.status == DocumentStatus.rejected) ...[
              const SizedBox(height: 16),
              AppButton(
                onPressed: () {
                  // Navigate to document upload screen with pre-filled type
                  Navigator.pushNamed(
                    context,
                    '/seller/documents/upload',
                    arguments: document.type,
                  );
                },
                child: const Text('Upload Again'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getStatusIcon() {
    switch (document.status) {
      case DocumentStatus.pending:
        return Icons.pending;
      case DocumentStatus.approved:
        return Icons.check_circle;
      case DocumentStatus.rejected:
        return Icons.error;
      case DocumentStatus.expired:
        return Icons.warning;
    }
  }

  Color _getStatusColor() {
    switch (document.status) {
      case DocumentStatus.pending:
        return Colors.orange;
      case DocumentStatus.approved:
        return Colors.green;
      case DocumentStatus.rejected:
        return Colors.red;
      case DocumentStatus.expired:
        return Colors.orange;
    }
  }

  Widget _buildStatusChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        document.status.name.toUpperCase(),
        style: TextStyle(
          color: _getStatusColor(),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
