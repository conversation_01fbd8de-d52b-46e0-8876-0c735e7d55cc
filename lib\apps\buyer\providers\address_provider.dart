import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../shared/models/address/address_model.dart';
import '../../../shared/services/address/address_service.dart';
import '../../../shared/providers/auth_provider.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import 'profile_provider.dart';

/// Database service provider for hybrid storage
final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final addressServiceProvider = Provider<AddressService>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return AddressService(databaseService: databaseService);
});

final userAddressesProvider =
    StreamProvider.autoDispose<List<AddressModel>>((ref) {
  final addressService = ref.watch(addressServiceProvider);
  final userId = ref.watch(userIdProvider);
  return addressService.getUserAddresses(userId);
});

final addressProvider =
    StateNotifierProvider.autoDispose<AddressNotifier, AsyncValue<void>>((ref) {
  final addressService = ref.watch(addressServiceProvider);
  final databaseService = ref.watch(databaseServiceProvider);
  final userId = ref.watch(userIdProvider);
  return AddressNotifier(addressService, databaseService, userId, ref);
});

class AddressNotifier extends StateNotifier<AsyncValue<void>> {
  final AddressService _addressService;
  final DatabaseService _databaseService;
  final String _userId;
  final Ref _ref;

  AddressNotifier(this._addressService, this._databaseService, this._userId, this._ref)
      : super(const AsyncValue.data(null));

  Future<void> addAddress(AddressModel address) async {
    try {
      state = const AsyncValue.loading();

      // Add the address to the addresses collection
      final newAddress = await _addressService.addAddress(address);

      // Update the buyer profile to include the new address ID
      await _updateBuyerAddresses(newAddress.id);

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  // Helper method to update the buyer's addresses list
  Future<void> _updateBuyerAddresses(String addressId) async {
    try {
      final buyerData = await _databaseService.find('buyers', _userId);

      if (buyerData != null) {
        final addresses = List<String>.from(buyerData['addresses'] ?? []);

        // Add the new address ID if it's not already in the list
        if (!addresses.contains(addressId)) {
          addresses.add(addressId);

          // Update the buyer document
          await _databaseService.update('buyers', _userId, {
            'addresses': addresses,
            'updated_at': DateTime.now().toIso8601String(),
          });

          // Refresh the profile to reflect the updated addresses
          debugPrint('Refreshing profile after adding address: $addressId');
          _ref.read(profileProvider.notifier).refreshProfile();
        }
      }
    } catch (e) {
      debugPrint('Error updating buyer addresses: $e');
    }
  }

  Future<void> updateAddress(AddressModel address) async {
    try {
      state = const AsyncValue.loading();
      await _addressService.updateAddress(address);

      // Refresh the profile to reflect the updated address
      debugPrint('Refreshing profile after updating address: ${address.id}');
      _ref.read(profileProvider.notifier).refreshProfile();

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteAddress(String addressId) async {
    try {
      state = const AsyncValue.loading();

      // Delete the address from the addresses collection
      await _addressService.deleteAddress(addressId);

      // Remove the address ID from the buyer profile
      await _removeBuyerAddress(addressId);

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  // Helper method to remove an address ID from the buyer's addresses list
  Future<void> _removeBuyerAddress(String addressId) async {
    try {
      final buyerData = await _databaseService.find('buyers', _userId);

      if (buyerData != null) {
        final addresses = List<String>.from(buyerData['addresses'] ?? []);

        // Remove the address ID if it exists in the list
        if (addresses.contains(addressId)) {
          addresses.remove(addressId);

          // Update the buyer document
          await _databaseService.update('buyers', _userId, {
            'addresses': addresses,
            'updated_at': DateTime.now().toIso8601String(),
          });

          // Refresh the profile to reflect the updated addresses
          debugPrint('Refreshing profile after removing address: $addressId');
          _ref.read(profileProvider.notifier).refreshProfile();
        }
      }
    } catch (e) {
      debugPrint('Error removing buyer address: $e');
    }
  }

  Future<void> setDefaultAddress(String addressId) async {
    try {
      state = const AsyncValue.loading();
      await _addressService.setDefaultAddress(_userId, addressId);

      // Refresh the profile to reflect the updated default address
      debugPrint(
          'Refreshing profile after setting default address: $addressId');
      _ref.read(profileProvider.notifier).refreshProfile();

      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
