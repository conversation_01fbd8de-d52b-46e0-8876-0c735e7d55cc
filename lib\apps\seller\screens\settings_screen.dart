import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/seller/presentation/cubits/settings_cubit.dart';
import 'package:shivish/apps/seller/presentation/providers/settings_provider.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/apps/seller/seller_routes.dart';

// Profile Screens
import '../screens/profile/profile_screen.dart';
import '../screens/profile/business_information_screen.dart';

// Payment Screens
import '../screens/payments/payment_settings_screen.dart';
import '../screens/payments/bank_account_settings_screen.dart';

// Settings Screens
import '../screens/settings/theme_settings_screen.dart';
import '../screens/settings/language_settings_screen.dart';
import '../screens/settings/privacy_policy_screen.dart';
import '../screens/settings/terms_of_service_screen.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // Watch the settings provider to rebuild when it changes
    final settingsState = ref.watch(settingsProvider);

    return Scaffold(
      appBar: AppToolbar(
        title: 'Settings',
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(SellerRoutes.home),
        ),
      ),
      body: settingsState.maybeWhen(
        loading: () => const LoadingIndicator(),
        error: (message) => Center(
          child: Text(
            'Error: $message',
            style: const TextStyle(color: Colors.red),
          ),
        ),
        loaded: (settings) => _buildContent(context, settings),
        orElse: () => const Center(child: Text('Unknown State')),
      ),
    );
  }

  Widget _buildContent(BuildContext context, Settings settings) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSection(
          context,
          'Account Settings',
          [
            _buildListTile(
              context,
              'Profile',
              Icons.person,
              () => _navigateToProfile(context),
            ),
            _buildListTile(
              context,
              'Business Information',
              Icons.business,
              () => _navigateToBusinessInfo(context),
            ),
            _buildListTile(
              context,
              'Payment Settings',
              Icons.payment,
              () => _navigateToPaymentSettings(context),
            ),
            _buildListTile(
              context,
              'Bank Details',
              Icons.account_balance,
              () => _navigateToBankDetails(context),
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildSection(
          context,
          'Notification Settings',
          [
            _buildSwitchListTile(
              context,
              'Order Notifications',
              Icons.shopping_cart,
              settings.orderNotifications,
              (value) => _toggleOrderNotifications(value),
            ),
            _buildSwitchListTile(
              context,
              'Product Notifications',
              Icons.inventory,
              settings.productNotifications,
              (value) => _toggleProductNotifications(value),
            ),
            _buildSwitchListTile(
              context,
              'System Notifications',
              Icons.info,
              settings.systemNotifications,
              (value) => _toggleSystemNotifications(value),
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildSection(
          context,
          'App Settings',
          [
            _buildListTile(
              context,
              'Theme',
              Icons.palette,
              () => _navigateToThemeSettings(context),
            ),
            _buildListTile(
              context,
              'Language',
              Icons.language,
              () => _navigateToLanguageSettings(context),
            ),
            _buildListTile(
              context,
              'Privacy Policy',
              Icons.privacy_tip,
              () => _navigateToPrivacyPolicy(context),
            ),
            _buildListTile(
              context,
              'Terms of Service',
              Icons.description,
              () => _navigateToTermsOfService(context),
            ),
          ],
        ),
        ListTile(
          leading: const Icon(Icons.image),
          title: const Text('Submit Banner'),
          subtitle: const Text('Submit a banner for admin approval'),
          onTap: () => context.push('/banner-submission'),
        ),
      ],
    );
  }

  Widget _buildSection(
      BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        Card(
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildListTile(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildSwitchListTile(
    BuildContext context,
    String title,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      secondary: Icon(icon),
      title: Text(title),
      value: value,
      onChanged: onChanged,
    );
  }

  void _navigateToProfile(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SellerProfileScreen(),
      ),
    );
  }

  void _navigateToBusinessInfo(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BusinessInformationScreen(),
      ),
    );
  }

  void _navigateToBankDetails(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BankAccountSettingsScreen(),
      ),
    );
  }

  void _navigateToPaymentSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PaymentSettingsScreen(),
      ),
    );
  }

  void _toggleOrderNotifications(bool value) {
    ref.read(settingsProvider.notifier).toggleOrderNotifications(value);
  }

  void _toggleProductNotifications(bool value) {
    ref.read(settingsProvider.notifier).toggleProductNotifications(value);
  }

  void _toggleSystemNotifications(bool value) {
    ref.read(settingsProvider.notifier).toggleSystemNotifications(value);
  }

  void _navigateToThemeSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ThemeSettingsScreen(),
      ),
    );
  }

  void _navigateToLanguageSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LanguageSettingsScreen(),
      ),
    );
  }

  void _navigateToPrivacyPolicy(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PrivacyPolicyScreen(),
      ),
    );
  }

  void _navigateToTermsOfService(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const TermsOfServiceScreen(),
      ),
    );
  }
}
