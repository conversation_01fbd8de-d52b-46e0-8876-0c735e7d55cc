import 'package:flutter/foundation.dart';
import 'package:shivish/shared/models/seller.dart';
import 'package:shivish/shared/services/seller/seller_service.dart';

class SellerNotifier extends ChangeNotifier {
  final SellerService _sellerService;
  Seller? _seller;
  bool _isLoading = false;
  String? _error;

  SellerNotifier(this._sellerService);

  Seller? get seller => _seller;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> getSeller(String sellerId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      _seller = await _sellerService.getSellerById(sellerId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  Future<void> updateSeller(Seller seller) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      await _sellerService.updateSeller(seller);
      _seller = seller;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
