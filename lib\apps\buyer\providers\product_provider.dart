import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../repositories/product_repository_impl.dart';
import '../repositories/product_repository.dart';
import '../../../shared/models/product/product_model.dart';

/// Provider for [ProductRepository]
final productRepositoryProvider = Provider<ProductRepository>((ref) {
  return ProductRepositoryImpl();
});

/// Provider for list of products
final productsProvider =
    StateNotifierProvider<ProductsNotifier, AsyncValue<List<ProductModel>>>(
  (ref) => ProductsNotifier(ref.watch(productRepositoryProvider)),
);

/// Provider for single product
final productProvider =
    StateNotifierProvider<ProductNotifier, AsyncValue<ProductModel?>>(
  (ref) => ProductNotifier(ref.watch(productRepositoryProvider)),
);

/// Provider for featured products
final featuredProductsProvider = StateNotifierProvider<FeaturedProductsNotifier,
    AsyncValue<List<ProductModel>>>(
  (ref) => FeaturedProductsNotifier(ref.watch(productRepositoryProvider)),
);

/// Provider for latest products
final latestProductsProvider = StateNotifierProvider<LatestProductsNotifier,
    AsyncValue<List<ProductModel>>>(
  (ref) => LatestProductsNotifier(ref.watch(productRepositoryProvider)),
);

/// Provider for products by category
final productsByCategoryProvider = StateNotifierProvider.family<
    ProductsByCategoryNotifier, AsyncValue<List<ProductModel>>, String>(
  (ref, categoryId) => ProductsByCategoryNotifier(
      ref.watch(productRepositoryProvider), categoryId),
);

class ProductsNotifier extends StateNotifier<AsyncValue<List<ProductModel>>> {
  final ProductRepository _repository;

  ProductsNotifier(this._repository) : super(const AsyncValue.data([]));

  /// Get all products
  ///
  /// If [silent] is true, the state will not be set to loading before fetching products.
  /// This is useful for background refreshes where you don't want to show a loading indicator.
  Future<List<ProductModel>> getProducts({bool silent = false}) async {
    try {
      print('ProductsNotifier: Starting to fetch products (silent: $silent)');

      // Only set loading state if not silent
      if (!silent) {
        state = const AsyncValue.loading();
      }

      final products = await _repository.getProducts();
      print('ProductsNotifier: Fetched ${products.length} products');

      if (products.isEmpty) {
        print('ProductsNotifier: No products found');
      } else {
        print('ProductsNotifier: First product: ${products.first.toJson()}');
      }

      // Always update the state with the new data
      state = AsyncValue.data(products);
      return products;
    } catch (e, stackTrace) {
      print('ProductsNotifier: Error fetching products: $e');

      // Only update state with error if not silent
      if (!silent) {
        state = AsyncValue.error(e, stackTrace);
      }

      rethrow;
    }
  }

  Future<List<ProductModel>> getProductsWithFilters({
    String? categoryId,
    double? minPrice,
    double? maxPrice,
    bool? isFeatured,
    bool? isNew,
    String? searchQuery,
    int? limit,
  }) async {
    try {
      state = const AsyncValue.loading();
      final products = await _repository.getProductsWithFilters(
        categoryId: categoryId,
        minPrice: minPrice,
        maxPrice: maxPrice,
        isFeatured: isFeatured,
        isNew: isNew,
        searchQuery: searchQuery,
        limit: limit,
      );
      state = AsyncValue.data(products);
      return products;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  Future<List<ProductModel>> searchProducts(String query) async {
    try {
      state = const AsyncValue.loading();
      final products = await _repository.getProductsWithFilters(
        searchQuery: query,
      );
      state = AsyncValue.data(products);
      return products;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }
}

class ProductNotifier extends StateNotifier<AsyncValue<ProductModel?>> {
  final ProductRepository _repository;

  ProductNotifier(this._repository) : super(const AsyncValue.data(null));

  Future<ProductModel> getProduct(String id) async {
    try {
      state = const AsyncValue.loading();
      final product = await _repository.getProductWithSellerDetails(id);
      state = AsyncValue.data(product);
      return product;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  Future<List<ProductModel>> getRelatedProducts(String productId) async {
    try {
      final product = await getProduct(productId);
      return await _repository.getProductsWithFilters(
        categoryId: product.categoryId,
        limit: 5,
      );
    } catch (e) {
      return [];
    }
  }
}

class FeaturedProductsNotifier
    extends StateNotifier<AsyncValue<List<ProductModel>>> {
  final ProductRepository _repository;

  FeaturedProductsNotifier(this._repository) : super(const AsyncValue.data([]));

  Future<List<ProductModel>> getFeaturedProducts() async {
    try {
      state = const AsyncValue.loading();
      final products = await _repository.getProductsWithFilters(
        isFeatured: true,
        limit: 10,
      );
      state = AsyncValue.data(products);
      return products;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }
}

class LatestProductsNotifier
    extends StateNotifier<AsyncValue<List<ProductModel>>> {
  final ProductRepository _repository;

  LatestProductsNotifier(this._repository) : super(const AsyncValue.data([]));

  Future<List<ProductModel>> getLatestProducts() async {
    try {
      state = const AsyncValue.loading();
      final products = await _repository.getProductsWithFilters(
        isNew: true,
        limit: 10,
      );
      state = AsyncValue.data(products);
      return products;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }
}

class ProductsByCategoryNotifier
    extends StateNotifier<AsyncValue<List<ProductModel>>> {
  final ProductRepository _repository;
  final String categoryId;

  ProductsByCategoryNotifier(this._repository, this.categoryId)
      : super(const AsyncValue.data([]));

  Future<List<ProductModel>> getProductsByCategory() async {
    try {
      state = const AsyncValue.loading();
      final products = await _repository.getProductsWithFilters(
        categoryId: categoryId,
      );
      state = AsyncValue.data(products);
      return products;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }
}
