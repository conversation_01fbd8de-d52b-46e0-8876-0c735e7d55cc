import 'package:shivish/shared/models/user/user_model.dart';
import '../models/login_request.dart';
import '../models/register_request.dart';

abstract class AuthRepository {
  // Stream of authentication state changes
  Stream<UserModel?> get authStateChanges;

  // Get current user synchronously
  UserModel? get currentUser;

  // Get current user asynchronously
  Future<UserModel?> getCurrentUser();

  // Authentication methods
  Future<UserModel> signInWithEmailAndPassword(LoginRequest request);
  Future<UserModel> signInWithGoogle();
  Future<UserModel> signInWithApple();
  Future<UserModel> register(RegisterRequest request);
  Future<void> signOut({bool clearAllCredentials = true});
  Future<void> resetPassword(String email);

  // Profile management
  Future<UserModel> updateProfile({
    String? displayName,
    String? photoUrl,
    String? phoneNumber,
  });
  Future<void> deleteAccount();

  // Phone verification
  Future<void> verifyPhone(String phoneNumber);
  Future<UserModel> verifyPhoneCode(String code);

  // Email verification
  Future<bool> isEmailVerified();
  Future<void> sendEmailVerification();
}
