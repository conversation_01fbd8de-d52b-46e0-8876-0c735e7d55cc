import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../shared/models/temple/temple_model.dart';
import '../../providers/temple_provider.dart';
import '../../widgets/temple/temple_image_gallery.dart';
import '../../widgets/temple/temple_services_section.dart';
import '../../widgets/temple/temple_timings_section.dart';
import '../../widgets/temple/temple_reviews_section.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';

class TempleDetailsScreen extends ConsumerStatefulWidget {
  final String templeId;

  const TempleDetailsScreen({super.key, required this.templeId});

  @override
  ConsumerState<TempleDetailsScreen> createState() =>
      _TempleDetailsScreenState();
}

class _TempleDetailsScreenState extends ConsumerState<TempleDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;
  bool _isAppBarExpanded = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final isExpanded = _scrollController.offset < 200;
    if (isExpanded != _isAppBarExpanded) {
      setState(() {
        _isAppBarExpanded = isExpanded;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final templeAsync = ref.watch(templeDetailsProvider(widget.templeId));

    return Scaffold(
      body: templeAsync.when(
        data: (temple) {
          if (temple == null) {
            return const Center(child: Text('Temple not found'));
          }
          return _buildTempleDetails(context, temple);
        },
        loading: () => const LoadingIndicator(),
        error: (error, stack) => ErrorView(
          message: 'Failed to load temple details: $error',
          onRetry: () => ref.refresh(templeDetailsProvider(widget.templeId)),
        ),
      ),
    );
  }

  Widget _buildTempleDetails(BuildContext context, Temple temple) {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        _buildAppBar(context, temple),
        _buildTempleInfo(context, temple),
        _buildTabBar(context),
        _buildTabContent(context, temple),
      ],
    );
  }

  Widget _buildAppBar(BuildContext context, Temple temple) {
    return SliverAppBar(
      expandedHeight: 300,
      floating: false,
      pinned: true,
      backgroundColor: Colors.orange.shade800,
      flexibleSpace: FlexibleSpaceBar(
        title: _isAppBarExpanded
            ? null
            : Text(
                temple.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
        background: TempleImageGallery(
          images: temple.images,
          coverImage: temple.coverImage,
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.share, color: Colors.white),
          onPressed: () => _shareTemple(temple),
        ),
        IconButton(
          icon: const Icon(Icons.favorite_border, color: Colors.white),
          onPressed: () => _addToFavorites(temple),
        ),
      ],
    );
  }

  Widget _buildTempleInfo(BuildContext context, Temple temple) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Temple name and rating
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        temple.name,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        temple.description,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                _buildRatingCard(temple),
              ],
            ),

            const SizedBox(height: 20),

            // Location and contact
            _buildLocationSection(temple),

            const SizedBox(height: 20),

            // Deities
            if (temple.deities.isNotEmpty) ...[
              _buildDeitiesSection(temple),
              const SizedBox(height: 20),
            ],

            // Quick actions
            _buildQuickActions(context, temple),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingCard(Temple temple) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.shade200),
      ),
      child: Column(
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.star, color: Colors.amber, size: 20),
              const SizedBox(width: 4),
              Text(
                temple.rating.toStringAsFixed(1),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '${temple.totalReviews} reviews',
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSection(Temple temple) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.orange.shade600, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Location',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(temple.location.address, style: const TextStyle(fontSize: 14)),
          Text(
            '${temple.location.city}, ${temple.location.state} ${temple.location.pincode}',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _openMaps(temple),
                  icon: const Icon(Icons.directions, size: 18),
                  label: const Text('Directions'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange.shade600,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _callTemple(temple),
                  icon: const Icon(Icons.phone, size: 18),
                  label: const Text('Call'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.orange.shade600,
                    side: BorderSide(color: Colors.orange.shade600),
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDeitiesSection(Temple temple) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.temple_hindu, color: Colors.orange.shade600, size: 20),
            const SizedBox(width: 8),
            const Text(
              'Deities',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: temple.deities.map((deity) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Text(
                deity,
                style: TextStyle(
                  color: Colors.orange.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context, Temple temple) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () => _bookNow(context, temple),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Book Darshan/Seva',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return SliverToBoxAdapter(
      child: Container(
        color: Colors.white,
        child: TabBar(
          controller: _tabController,
          labelColor: Colors.orange.shade700,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.orange.shade700,
          tabs: const [
            Tab(text: 'Services'),
            Tab(text: 'Timings'),
            Tab(text: 'Reviews'),
            Tab(text: 'About'),
          ],
        ),
      ),
    );
  }

  Widget _buildTabContent(BuildContext context, Temple temple) {
    return SliverFillRemaining(
      child: TabBarView(
        controller: _tabController,
        children: [
          TempleServicesSection(temple: temple),
          TempleTimingsSection(temple: temple),
          TempleReviewsSection(temple: temple),
          _buildAboutSection(temple),
        ],
      ),
    );
  }

  Widget _buildAboutSection(Temple temple) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (temple.history != null) ...[
            _buildInfoCard('History', temple.history!),
            const SizedBox(height: 16),
          ],
          if (temple.significance != null) ...[
            _buildInfoCard('Significance', temple.significance!),
            const SizedBox(height: 16),
          ],
          if (temple.architecture != null) ...[
            _buildInfoCard('Architecture', temple.architecture!),
            const SizedBox(height: 16),
          ],
          if (temple.facilities.isNotEmpty) ...[
            _buildFacilitiesCard(temple.facilities),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, String content) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(content, style: const TextStyle(fontSize: 14, height: 1.5)),
          ],
        ),
      ),
    );
  }

  Widget _buildFacilitiesCard(List<String> facilities) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Facilities',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: facilities.map((facility) {
                return Chip(
                  label: Text(facility),
                  backgroundColor: Colors.orange.shade50,
                  labelStyle: TextStyle(color: Colors.orange.shade700),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  void _shareTemple(Temple temple) {
    // Implement share functionality
  }

  void _addToFavorites(Temple temple) {
    // Implement add to favorites
  }

  void _openMaps(Temple temple) async {
    final url =
        'https://maps.google.com/?q=${temple.location.latitude},${temple.location.longitude}';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  void _callTemple(Temple temple) async {
    final url = 'tel:${temple.contact.phone}';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  void _bookNow(BuildContext context, Temple temple) {
    context.push('/buyer/temples/${temple.id}/booking');
  }
}
