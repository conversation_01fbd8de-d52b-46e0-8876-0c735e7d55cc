import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final emailSettingsProvider =
    AsyncNotifierProvider<EmailSettingsNotifier, String?>(() {
  return EmailSettingsNotifier();
});

class EmailSettingsNotifier extends AsyncNotifier<String?> {
  late final DatabaseService _databaseService;

  @override
  Future<String?> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadCurrentEmail();
  }

  Future<String?> _loadCurrentEmail() async {
    final user = ref.read(authProvider);
    if (user == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('users', user.id);
      return userData?['email'] as String?;
    } catch (e) {
      debugPrint('Failed to load email: $e');
      throw Exception('Failed to load email: $e');
    }
  }

  Future<void> updateEmail(String newEmail) async {
    state = const AsyncLoading();

    try {
      final user = ref.read(authProvider);
      if (user == null) throw Exception('User not authenticated');

      // Update email in Supabase Auth (this will send a verification email)
      await Supabase.instance.client.auth.updateUser(
        UserAttributes(email: newEmail),
      );

      // Update email in database
      await _databaseService.update('users', user.id, {
        'email': newEmail,
        'email_verified': false,
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData(newEmail);
    } catch (e) {
      debugPrint('Failed to update email: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> setEmailVerification(bool enabled) async {
    final user = ref.read(authProvider);
    if (user == null) throw Exception('User not authenticated');

    try {
      await _databaseService.update('users', user.id, {
        'email_verification_enabled': enabled,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to update email verification settings: $e');
      throw Exception('Failed to update email verification settings: $e');
    }
  }
}
