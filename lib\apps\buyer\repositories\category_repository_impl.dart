import '../domain/entities/category/category_model.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';
import 'category_repository.dart';

/// Hybrid storage implementation of [CategoryRepository]
class CategoryRepositoryImpl implements CategoryRepository {
  /// Creates a [CategoryRepositoryImpl]
  CategoryRepositoryImpl({
    DatabaseService? databaseService,
  }) : _databaseService = databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment());

  final DatabaseService _databaseService;
  final _logger = getLogger('CategoryRepositoryImpl');
  final String _collection = 'categories';

  @override
  Future<List<CategoryModel>> getActiveCategories() async {
    try {
      // Get all categories and filter in memory
      final allCategories = await _databaseService.getAll(_collection);

      final activeCategories = allCategories
          .where((categoryData) => categoryData['isActive'] == true)
          .toList();

      // Sort by priority (ascending)
      activeCategories.sort((a, b) {
        final priorityA = a['priority'] as int? ?? 0;
        final priorityB = b['priority'] as int? ?? 0;
        return priorityA.compareTo(priorityB);
      });

      return activeCategories
          .map((categoryData) => CategoryModel.fromJson(categoryData))
          .toList();
    } catch (e) {
      _logger.severe('Error getting active categories: $e');
      return [];
    }
  }

  @override
  Future<CategoryModel?> getCategoryById(String id) async {
    try {
      final categoryData = await _databaseService.find(_collection, id);
      if (categoryData == null) return null;

      return CategoryModel.fromJson(categoryData);
    } catch (e) {
      _logger.severe('Error getting category by ID: $e');
      return null;
    }
  }

  @override
  Future<List<CategoryModel>> getRootCategories() async {
    try {
      // Get all active categories and filter for root categories
      final activeCategories = await getActiveCategories();

      return activeCategories
          .where((category) => category.parentId == null)
          .toList();
    } catch (e) {
      _logger.severe('Error getting root categories: $e');
      return [];
    }
  }

  @override
  Future<List<CategoryModel>> getChildCategories(String parentId) async {
    try {
      // Get all active categories and filter for child categories
      final activeCategories = await getActiveCategories();

      return activeCategories
          .where((category) => category.parentId == parentId)
          .toList();
    } catch (e) {
      _logger.severe('Error getting child categories: $e');
      return [];
    }
  }

  @override
  Future<List<CategoryModel>> getCategoriesByPriorityRange({
    required int minPriority,
    required int maxPriority,
  }) async {
    try {
      // Get all active categories and filter by priority range
      final activeCategories = await getActiveCategories();

      return activeCategories
          .where((category) =>
              category.priority >= minPriority &&
              category.priority <= maxPriority)
          .toList();
    } catch (e) {
      _logger.severe('Error getting categories by priority range: $e');
      return [];
    }
  }
}
