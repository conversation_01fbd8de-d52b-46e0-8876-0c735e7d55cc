import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/order/order_model.dart';
import '../../domain/repositories/orders_repository.dart';
import '../../data/repositories/orders_repository_impl.dart';

/// Provider for [OrdersRepository] - using the implementation from orders_repository_impl.dart
final ordersRepositoryProvider = ordersRepositoryImplProvider;

/// Provider for list of orders
final ordersProvider =
    StateNotifierProvider<OrdersNotifier, AsyncValue<List<OrderModel>>>(
  (ref) => OrdersNotifier(ref.watch(ordersRepositoryProvider)),
);

/// Provider for single order
final orderProvider = StateNotifierProvider.family<OrderNotifier,
    AsyncValue<OrderModel?>, String>(
  (ref, orderId) => OrderNotifier(ref.watch(ordersRepositoryProvider), orderId),
);

/// Provider for orders by status
final ordersByStatusProvider = StateNotifierProvider.family<
    OrdersByStatusNotifier, AsyncValue<List<OrderModel>>, OrderStatus>(
  (ref, status) =>
      OrdersByStatusNotifier(ref.watch(ordersRepositoryProvider), status),
);

/// Provider for orders by seller
final ordersBySellerProvider = StateNotifierProvider.family<
    OrdersBySellerNotifier, AsyncValue<List<OrderModel>>, String>(
  (ref, sellerId) =>
      OrdersBySellerNotifier(ref.watch(ordersRepositoryProvider), sellerId),
);

class OrdersNotifier extends StateNotifier<AsyncValue<List<OrderModel>>> {
  final OrdersRepository _repository;

  OrdersNotifier(this._repository) : super(const AsyncValue.data([]));

  Future<List<OrderModel>> getOrders() async {
    try {
      state = const AsyncValue.loading();
      final orders = await _repository.getOrders();
      state = AsyncValue.data(orders);
      return orders;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  Future<OrderModel> createOrder(OrderModel order) async {
    try {
      state = const AsyncValue.loading();
      final createdOrder = await _repository.createOrder(order);
      state = AsyncValue.data([...state.value ?? [], createdOrder]);
      return createdOrder;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  Future<OrderModel> updateOrder(OrderModel order) async {
    try {
      state = const AsyncValue.loading();
      final updatedOrder = await _repository.updateOrder(order);
      state = AsyncValue.data(
        (state.value ?? [])
            .map((o) => o.id == order.id ? updatedOrder : o)
            .toList(),
      );
      return updatedOrder;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  Future<void> deleteOrder(String id) async {
    try {
      state = const AsyncValue.loading();
      await _repository.deleteOrder(id);
      state = AsyncValue.data(
        (state.value ?? []).where((o) => o.id != id).toList(),
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }
}

class OrderNotifier extends StateNotifier<AsyncValue<OrderModel?>> {
  final OrdersRepository _repository;
  final String orderId;

  OrderNotifier(this._repository, this.orderId)
      : super(const AsyncValue.data(null)) {
    getOrder();
  }

  Future<OrderModel?> getOrder() async {
    try {
      state = const AsyncValue.loading();
      final order = await _repository.getOrder(orderId);
      state = AsyncValue.data(order);
      return order;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }
}

class OrdersByStatusNotifier
    extends StateNotifier<AsyncValue<List<OrderModel>>> {
  final OrdersRepository _repository;
  final OrderStatus status;

  OrdersByStatusNotifier(this._repository, this.status)
      : super(const AsyncValue.data([]));

  Future<List<OrderModel>> getOrdersByStatus() async {
    try {
      state = const AsyncValue.loading();
      final orders = await _repository.getOrdersByStatus(status);
      state = AsyncValue.data(orders);
      return orders;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }
}

class OrdersBySellerNotifier
    extends StateNotifier<AsyncValue<List<OrderModel>>> {
  final OrdersRepository _repository;
  final String sellerId;

  OrdersBySellerNotifier(this._repository, this.sellerId)
      : super(const AsyncValue.data([]));

  Future<List<OrderModel>> getOrdersBySeller() async {
    try {
      state = const AsyncValue.loading();
      final orders = await _repository.getOrdersBySeller(sellerId);
      state = AsyncValue.data(orders);
      return orders;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }
}
