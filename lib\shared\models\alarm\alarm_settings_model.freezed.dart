// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'alarm_settings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$NotificationSettings {

 bool get sound; bool get vibration; bool get led;
/// Create a copy of NotificationSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NotificationSettingsCopyWith<NotificationSettings> get copyWith => _$NotificationSettingsCopyWithImpl<NotificationSettings>(this as NotificationSettings, _$identity);

  /// Serializes this NotificationSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NotificationSettings&&(identical(other.sound, sound) || other.sound == sound)&&(identical(other.vibration, vibration) || other.vibration == vibration)&&(identical(other.led, led) || other.led == led));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sound,vibration,led);

@override
String toString() {
  return 'NotificationSettings(sound: $sound, vibration: $vibration, led: $led)';
}


}

/// @nodoc
abstract mixin class $NotificationSettingsCopyWith<$Res>  {
  factory $NotificationSettingsCopyWith(NotificationSettings value, $Res Function(NotificationSettings) _then) = _$NotificationSettingsCopyWithImpl;
@useResult
$Res call({
 bool sound, bool vibration, bool led
});




}
/// @nodoc
class _$NotificationSettingsCopyWithImpl<$Res>
    implements $NotificationSettingsCopyWith<$Res> {
  _$NotificationSettingsCopyWithImpl(this._self, this._then);

  final NotificationSettings _self;
  final $Res Function(NotificationSettings) _then;

/// Create a copy of NotificationSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sound = null,Object? vibration = null,Object? led = null,}) {
  return _then(_self.copyWith(
sound: null == sound ? _self.sound : sound // ignore: cast_nullable_to_non_nullable
as bool,vibration: null == vibration ? _self.vibration : vibration // ignore: cast_nullable_to_non_nullable
as bool,led: null == led ? _self.led : led // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [NotificationSettings].
extension NotificationSettingsPatterns on NotificationSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _NotificationSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _NotificationSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _NotificationSettings value)  $default,){
final _that = this;
switch (_that) {
case _NotificationSettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _NotificationSettings value)?  $default,){
final _that = this;
switch (_that) {
case _NotificationSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool sound,  bool vibration,  bool led)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _NotificationSettings() when $default != null:
return $default(_that.sound,_that.vibration,_that.led);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool sound,  bool vibration,  bool led)  $default,) {final _that = this;
switch (_that) {
case _NotificationSettings():
return $default(_that.sound,_that.vibration,_that.led);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool sound,  bool vibration,  bool led)?  $default,) {final _that = this;
switch (_that) {
case _NotificationSettings() when $default != null:
return $default(_that.sound,_that.vibration,_that.led);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _NotificationSettings implements NotificationSettings {
  const _NotificationSettings({this.sound = true, this.vibration = true, this.led = true});
  factory _NotificationSettings.fromJson(Map<String, dynamic> json) => _$NotificationSettingsFromJson(json);

@override@JsonKey() final  bool sound;
@override@JsonKey() final  bool vibration;
@override@JsonKey() final  bool led;

/// Create a copy of NotificationSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NotificationSettingsCopyWith<_NotificationSettings> get copyWith => __$NotificationSettingsCopyWithImpl<_NotificationSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$NotificationSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NotificationSettings&&(identical(other.sound, sound) || other.sound == sound)&&(identical(other.vibration, vibration) || other.vibration == vibration)&&(identical(other.led, led) || other.led == led));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sound,vibration,led);

@override
String toString() {
  return 'NotificationSettings(sound: $sound, vibration: $vibration, led: $led)';
}


}

/// @nodoc
abstract mixin class _$NotificationSettingsCopyWith<$Res> implements $NotificationSettingsCopyWith<$Res> {
  factory _$NotificationSettingsCopyWith(_NotificationSettings value, $Res Function(_NotificationSettings) _then) = __$NotificationSettingsCopyWithImpl;
@override @useResult
$Res call({
 bool sound, bool vibration, bool led
});




}
/// @nodoc
class __$NotificationSettingsCopyWithImpl<$Res>
    implements _$NotificationSettingsCopyWith<$Res> {
  __$NotificationSettingsCopyWithImpl(this._self, this._then);

  final _NotificationSettings _self;
  final $Res Function(_NotificationSettings) _then;

/// Create a copy of NotificationSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sound = null,Object? vibration = null,Object? led = null,}) {
  return _then(_NotificationSettings(
sound: null == sound ? _self.sound : sound // ignore: cast_nullable_to_non_nullable
as bool,vibration: null == vibration ? _self.vibration : vibration // ignore: cast_nullable_to_non_nullable
as bool,led: null == led ? _self.led : led // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$AlarmSettingsModel {

 String get defaultTimeString; int get snoozeDuration; double get volume; bool get vibrate; bool get aiEnabled; NotificationSettings get notificationSettings;
/// Create a copy of AlarmSettingsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AlarmSettingsModelCopyWith<AlarmSettingsModel> get copyWith => _$AlarmSettingsModelCopyWithImpl<AlarmSettingsModel>(this as AlarmSettingsModel, _$identity);

  /// Serializes this AlarmSettingsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AlarmSettingsModel&&(identical(other.defaultTimeString, defaultTimeString) || other.defaultTimeString == defaultTimeString)&&(identical(other.snoozeDuration, snoozeDuration) || other.snoozeDuration == snoozeDuration)&&(identical(other.volume, volume) || other.volume == volume)&&(identical(other.vibrate, vibrate) || other.vibrate == vibrate)&&(identical(other.aiEnabled, aiEnabled) || other.aiEnabled == aiEnabled)&&(identical(other.notificationSettings, notificationSettings) || other.notificationSettings == notificationSettings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,defaultTimeString,snoozeDuration,volume,vibrate,aiEnabled,notificationSettings);

@override
String toString() {
  return 'AlarmSettingsModel(defaultTimeString: $defaultTimeString, snoozeDuration: $snoozeDuration, volume: $volume, vibrate: $vibrate, aiEnabled: $aiEnabled, notificationSettings: $notificationSettings)';
}


}

/// @nodoc
abstract mixin class $AlarmSettingsModelCopyWith<$Res>  {
  factory $AlarmSettingsModelCopyWith(AlarmSettingsModel value, $Res Function(AlarmSettingsModel) _then) = _$AlarmSettingsModelCopyWithImpl;
@useResult
$Res call({
 String defaultTimeString, int snoozeDuration, double volume, bool vibrate, bool aiEnabled, NotificationSettings notificationSettings
});


$NotificationSettingsCopyWith<$Res> get notificationSettings;

}
/// @nodoc
class _$AlarmSettingsModelCopyWithImpl<$Res>
    implements $AlarmSettingsModelCopyWith<$Res> {
  _$AlarmSettingsModelCopyWithImpl(this._self, this._then);

  final AlarmSettingsModel _self;
  final $Res Function(AlarmSettingsModel) _then;

/// Create a copy of AlarmSettingsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? defaultTimeString = null,Object? snoozeDuration = null,Object? volume = null,Object? vibrate = null,Object? aiEnabled = null,Object? notificationSettings = null,}) {
  return _then(_self.copyWith(
defaultTimeString: null == defaultTimeString ? _self.defaultTimeString : defaultTimeString // ignore: cast_nullable_to_non_nullable
as String,snoozeDuration: null == snoozeDuration ? _self.snoozeDuration : snoozeDuration // ignore: cast_nullable_to_non_nullable
as int,volume: null == volume ? _self.volume : volume // ignore: cast_nullable_to_non_nullable
as double,vibrate: null == vibrate ? _self.vibrate : vibrate // ignore: cast_nullable_to_non_nullable
as bool,aiEnabled: null == aiEnabled ? _self.aiEnabled : aiEnabled // ignore: cast_nullable_to_non_nullable
as bool,notificationSettings: null == notificationSettings ? _self.notificationSettings : notificationSettings // ignore: cast_nullable_to_non_nullable
as NotificationSettings,
  ));
}
/// Create a copy of AlarmSettingsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$NotificationSettingsCopyWith<$Res> get notificationSettings {
  
  return $NotificationSettingsCopyWith<$Res>(_self.notificationSettings, (value) {
    return _then(_self.copyWith(notificationSettings: value));
  });
}
}


/// Adds pattern-matching-related methods to [AlarmSettingsModel].
extension AlarmSettingsModelPatterns on AlarmSettingsModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AlarmSettingsModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AlarmSettingsModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AlarmSettingsModel value)  $default,){
final _that = this;
switch (_that) {
case _AlarmSettingsModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AlarmSettingsModel value)?  $default,){
final _that = this;
switch (_that) {
case _AlarmSettingsModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String defaultTimeString,  int snoozeDuration,  double volume,  bool vibrate,  bool aiEnabled,  NotificationSettings notificationSettings)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AlarmSettingsModel() when $default != null:
return $default(_that.defaultTimeString,_that.snoozeDuration,_that.volume,_that.vibrate,_that.aiEnabled,_that.notificationSettings);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String defaultTimeString,  int snoozeDuration,  double volume,  bool vibrate,  bool aiEnabled,  NotificationSettings notificationSettings)  $default,) {final _that = this;
switch (_that) {
case _AlarmSettingsModel():
return $default(_that.defaultTimeString,_that.snoozeDuration,_that.volume,_that.vibrate,_that.aiEnabled,_that.notificationSettings);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String defaultTimeString,  int snoozeDuration,  double volume,  bool vibrate,  bool aiEnabled,  NotificationSettings notificationSettings)?  $default,) {final _that = this;
switch (_that) {
case _AlarmSettingsModel() when $default != null:
return $default(_that.defaultTimeString,_that.snoozeDuration,_that.volume,_that.vibrate,_that.aiEnabled,_that.notificationSettings);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AlarmSettingsModel implements AlarmSettingsModel {
  const _AlarmSettingsModel({this.defaultTimeString = '06:00', this.snoozeDuration = 5, this.volume = 1.0, this.vibrate = true, this.aiEnabled = false, this.notificationSettings = const NotificationSettings()});
  factory _AlarmSettingsModel.fromJson(Map<String, dynamic> json) => _$AlarmSettingsModelFromJson(json);

@override@JsonKey() final  String defaultTimeString;
@override@JsonKey() final  int snoozeDuration;
@override@JsonKey() final  double volume;
@override@JsonKey() final  bool vibrate;
@override@JsonKey() final  bool aiEnabled;
@override@JsonKey() final  NotificationSettings notificationSettings;

/// Create a copy of AlarmSettingsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AlarmSettingsModelCopyWith<_AlarmSettingsModel> get copyWith => __$AlarmSettingsModelCopyWithImpl<_AlarmSettingsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AlarmSettingsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AlarmSettingsModel&&(identical(other.defaultTimeString, defaultTimeString) || other.defaultTimeString == defaultTimeString)&&(identical(other.snoozeDuration, snoozeDuration) || other.snoozeDuration == snoozeDuration)&&(identical(other.volume, volume) || other.volume == volume)&&(identical(other.vibrate, vibrate) || other.vibrate == vibrate)&&(identical(other.aiEnabled, aiEnabled) || other.aiEnabled == aiEnabled)&&(identical(other.notificationSettings, notificationSettings) || other.notificationSettings == notificationSettings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,defaultTimeString,snoozeDuration,volume,vibrate,aiEnabled,notificationSettings);

@override
String toString() {
  return 'AlarmSettingsModel(defaultTimeString: $defaultTimeString, snoozeDuration: $snoozeDuration, volume: $volume, vibrate: $vibrate, aiEnabled: $aiEnabled, notificationSettings: $notificationSettings)';
}


}

/// @nodoc
abstract mixin class _$AlarmSettingsModelCopyWith<$Res> implements $AlarmSettingsModelCopyWith<$Res> {
  factory _$AlarmSettingsModelCopyWith(_AlarmSettingsModel value, $Res Function(_AlarmSettingsModel) _then) = __$AlarmSettingsModelCopyWithImpl;
@override @useResult
$Res call({
 String defaultTimeString, int snoozeDuration, double volume, bool vibrate, bool aiEnabled, NotificationSettings notificationSettings
});


@override $NotificationSettingsCopyWith<$Res> get notificationSettings;

}
/// @nodoc
class __$AlarmSettingsModelCopyWithImpl<$Res>
    implements _$AlarmSettingsModelCopyWith<$Res> {
  __$AlarmSettingsModelCopyWithImpl(this._self, this._then);

  final _AlarmSettingsModel _self;
  final $Res Function(_AlarmSettingsModel) _then;

/// Create a copy of AlarmSettingsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? defaultTimeString = null,Object? snoozeDuration = null,Object? volume = null,Object? vibrate = null,Object? aiEnabled = null,Object? notificationSettings = null,}) {
  return _then(_AlarmSettingsModel(
defaultTimeString: null == defaultTimeString ? _self.defaultTimeString : defaultTimeString // ignore: cast_nullable_to_non_nullable
as String,snoozeDuration: null == snoozeDuration ? _self.snoozeDuration : snoozeDuration // ignore: cast_nullable_to_non_nullable
as int,volume: null == volume ? _self.volume : volume // ignore: cast_nullable_to_non_nullable
as double,vibrate: null == vibrate ? _self.vibrate : vibrate // ignore: cast_nullable_to_non_nullable
as bool,aiEnabled: null == aiEnabled ? _self.aiEnabled : aiEnabled // ignore: cast_nullable_to_non_nullable
as bool,notificationSettings: null == notificationSettings ? _self.notificationSettings : notificationSettings // ignore: cast_nullable_to_non_nullable
as NotificationSettings,
  ));
}

/// Create a copy of AlarmSettingsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$NotificationSettingsCopyWith<$Res> get notificationSettings {
  
  return $NotificationSettingsCopyWith<$Res>(_self.notificationSettings, (value) {
    return _then(_self.copyWith(notificationSettings: value));
  });
}
}

// dart format on
