import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/product/product_model.dart';

part 'product_state.freezed.dart';

@freezed
sealed class ProductState with _$ProductState {
  const factory ProductState.initial() = _Initial;
  const factory ProductState.loading() = _Loading;
  const factory ProductState.loadingMore(List<ProductModel> products) =
      _LoadingMore;
  const factory ProductState.loaded(List<ProductModel> products) = _Loaded;
  const factory ProductState.error(String message) = _Error;
}
