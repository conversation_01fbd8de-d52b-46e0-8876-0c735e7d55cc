import 'package:flutter/material.dart';

/// Converts a string icon code to a constant IconData
/// This approach allows for tree-shaking of icon fonts
IconData getIconFromString(String? iconCode) {
  if (iconCode == null || iconCode.isEmpty) {
    return Icons.notifications; // Default icon
  }

  // Try to parse the icon code
  int? iconCodeInt = int.tryParse(iconCode);
  if (iconCodeInt == null) {
    return Icons.notifications; // Default icon if parsing fails
  }

  // Map common icon codes to predefined Icons
  // Add more mappings as needed based on your app's requirements
  switch (iconCodeInt) {
    case 0xe7f4: // notifications
      return Icons.notifications;
    case 0xe7f7: // notifications_active
      return Icons.notifications_active;
    case 0xe7f5: // notifications_none
      return Icons.notifications_none;
    case 0xe7f6: // notifications_off
      return Icons.notifications_off;
    case 0xe7f0: // message
      return Icons.message;
    case 0xe3c9: // email
      return Icons.email;
    case 0xe0b0: // phone
      return Icons.phone;
    case 0xe0cd: // sms
      return Icons.sms;
    case 0xe8f5: // warning
      return Icons.warning;
    case 0xe88e: // info
      return Icons.info;
    default:
      // For any other icon code, return a default icon
      return Icons.notifications;
  }
}
