import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/apps/technician/providers/payment_alert_settings_provider.dart';

class PaymentAlertSettingsScreen extends ConsumerStatefulWidget {
  const PaymentAlertSettingsScreen({super.key});

  @override
  ConsumerState<PaymentAlertSettingsScreen> createState() =>
      _PaymentAlertSettingsScreenState();
}

class _PaymentAlertSettingsScreenState
    extends ConsumerState<PaymentAlertSettingsScreen> {
  bool _enablePaymentAlerts = true;
  bool _enableEmailAlerts = true;
  bool _enablePushAlerts = true;
  bool _enableSMSAlerts = false;
  bool _enableLowBalanceAlerts = true;
  double _lowBalanceThreshold = 1000;
  bool _enablePaymentSuccessAlerts = true;
  bool _enablePaymentFailureAlerts = true;
  bool _enableRefundAlerts = true;
  bool _enableCommissionAlerts = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final settings = await ref.read(paymentAlertSettingsProvider.future);
    if (mounted) {
      setState(() {
        _enablePaymentAlerts = settings['enablePaymentAlerts'];
        _enableEmailAlerts = settings['enableEmailAlerts'];
        _enablePushAlerts = settings['enablePushAlerts'];
        _enableSMSAlerts = settings['enableSMSAlerts'];
        _enableLowBalanceAlerts = settings['enableLowBalanceAlerts'];
        _lowBalanceThreshold = settings['lowBalanceThreshold'];
        _enablePaymentSuccessAlerts = settings['enablePaymentSuccessAlerts'];
        _enablePaymentFailureAlerts = settings['enablePaymentFailureAlerts'];
        _enableRefundAlerts = settings['enableRefundAlerts'];
        _enableCommissionAlerts = settings['enableCommissionAlerts'];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(paymentAlertSettingsProvider, (previous, next) {
      next.whenOrNull(
        data: (_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Payment alert settings updated successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        error: (error, _) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error.toString()),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      );
    });

    final settingsState = ref.watch(paymentAlertSettingsProvider);
    _isLoading = settingsState is AsyncLoading;

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Payment Alerts',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SwitchListTile(
                      title: const Text('Enable Payment Alerts'),
                      subtitle: const Text(
                          'Receive notifications for payment-related events'),
                      value: _enablePaymentAlerts,
                      onChanged: (value) {
                        setState(() {
                          _enablePaymentAlerts = value;
                        });
                      },
                    ),
                    const Divider(),
                    SwitchListTile(
                      title: const Text('Email Alerts'),
                      subtitle: const Text('Receive payment alerts via email'),
                      value: _enableEmailAlerts,
                      onChanged: _enablePaymentAlerts
                          ? (value) {
                              setState(() {
                                _enableEmailAlerts = value;
                              });
                            }
                          : null,
                    ),
                    SwitchListTile(
                      title: const Text('Push Notifications'),
                      subtitle: const Text(
                          'Receive payment alerts via push notifications'),
                      value: _enablePushAlerts,
                      onChanged: _enablePaymentAlerts
                          ? (value) {
                              setState(() {
                                _enablePushAlerts = value;
                              });
                            }
                          : null,
                    ),
                    SwitchListTile(
                      title: const Text('SMS Alerts'),
                      subtitle: const Text('Receive payment alerts via SMS'),
                      value: _enableSMSAlerts,
                      onChanged: _enablePaymentAlerts
                          ? (value) {
                              setState(() {
                                _enableSMSAlerts = value;
                              });
                            }
                          : null,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Low Balance Alert',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SwitchListTile(
                      title: const Text('Enable Low Balance Alerts'),
                      subtitle:
                          const Text('Get notified when your balance is low'),
                      value: _enableLowBalanceAlerts,
                      onChanged: (value) {
                        setState(() {
                          _enableLowBalanceAlerts = value;
                        });
                      },
                    ),
                    if (_enableLowBalanceAlerts) ...[
                      const SizedBox(height: 8),
                      Slider(
                        value: _lowBalanceThreshold,
                        min: 100,
                        max: 10000,
                        divisions: 99,
                        label: '₹${_lowBalanceThreshold.toStringAsFixed(0)}',
                        onChanged: (value) {
                          setState(() {
                            _lowBalanceThreshold = value;
                          });
                        },
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Payment Event Alerts',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SwitchListTile(
                      title: const Text('Payment Success'),
                      subtitle: const Text(
                          'Get notified when payments are successful'),
                      value: _enablePaymentSuccessAlerts,
                      onChanged: _enablePaymentAlerts
                          ? (value) {
                              setState(() {
                                _enablePaymentSuccessAlerts = value;
                              });
                            }
                          : null,
                    ),
                    SwitchListTile(
                      title: const Text('Payment Failure'),
                      subtitle: const Text('Get notified when payments fail'),
                      value: _enablePaymentFailureAlerts,
                      onChanged: _enablePaymentAlerts
                          ? (value) {
                              setState(() {
                                _enablePaymentFailureAlerts = value;
                              });
                            }
                          : null,
                    ),
                    SwitchListTile(
                      title: const Text('Refunds'),
                      subtitle: const Text('Get notified about refunds'),
                      value: _enableRefundAlerts,
                      onChanged: _enablePaymentAlerts
                          ? (value) {
                              setState(() {
                                _enableRefundAlerts = value;
                              });
                            }
                          : null,
                    ),
                    SwitchListTile(
                      title: const Text('Commission'),
                      subtitle:
                          const Text('Get notified about commission updates'),
                      value: _enableCommissionAlerts,
                      onChanged: _enablePaymentAlerts
                          ? (value) {
                              setState(() {
                                _enableCommissionAlerts = value;
                              });
                            }
                          : null,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            AppButton(
              onPressed: _isLoading
                  ? null
                  : () async {
                      try {
                        await ref
                            .read(paymentAlertSettingsProvider.notifier)
                            .savePaymentAlertSettings({
                          'enablePaymentAlerts': _enablePaymentAlerts,
                          'enableEmailAlerts': _enableEmailAlerts,
                          'enablePushAlerts': _enablePushAlerts,
                          'enableSMSAlerts': _enableSMSAlerts,
                          'enableLowBalanceAlerts': _enableLowBalanceAlerts,
                          'lowBalanceThreshold': _lowBalanceThreshold,
                          'enablePaymentSuccessAlerts':
                              _enablePaymentSuccessAlerts,
                          'enablePaymentFailureAlerts':
                              _enablePaymentFailureAlerts,
                          'enableRefundAlerts': _enableRefundAlerts,
                          'enableCommissionAlerts': _enableCommissionAlerts,
                        });
                      } catch (e) {
                        // Error is handled by the provider listener
                      }
                    },
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('Save Settings'),
            ),
          ],
        ),
      ),
    );
  }
}
