import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/config/ai_config_model.dart';
import '../../../shared/providers/system_config_provider.dart';

class AIConfigSection extends ConsumerWidget {
  final AIConfigModel config;

  const AIConfigSection({
    super.key,
    required this.config,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'AI Configuration',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildTextField(
              context,
              ref,
              'Model Version',
              config.modelVersion,
              (value) => _updateConfig(
                ref,
                config.copyWith(modelVersion: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildTextField(
              context,
              ref,
              'Language Model',
              config.languageModel,
              (value) => _updateConfig(
                ref,
                config.copyWith(languageModel: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchField(
              context,
              ref,
              'Enable Voice Commands',
              config.enableVoiceCommands,
              (value) => _updateConfig(
                ref,
                config.copyWith(enableVoiceCommands: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchField(
              context,
              ref,
              'Enable Chatbot',
              config.enableChatbot,
              (value) => _updateConfig(
                ref,
                config.copyWith(enableChatbot: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchField(
              context,
              ref,
              'Enable Recommendations',
              config.enableRecommendations,
              (value) => _updateConfig(
                ref,
                config.copyWith(enableRecommendations: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildModelParametersField(
              context,
              ref,
              config.modelParameters,
              (value) => _updateConfig(
                ref,
                config.copyWith(modelParameters: value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
    BuildContext context,
    WidgetRef ref,
    String label,
    String value,
    Function(String) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        Expanded(
          child: TextFormField(
            initialValue: value,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchField(
    BuildContext context,
    WidgetRef ref,
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildModelParametersField(
    BuildContext context,
    WidgetRef ref,
    Map<String, dynamic> parameters,
    Function(Map<String, dynamic>) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Model Parameters'),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: parameters.length,
          itemBuilder: (context, index) {
            final key = parameters.keys.elementAt(index);
            final value = parameters[key];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: key,
                      decoration: const InputDecoration(
                        labelText: 'Parameter Name',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (newKey) {
                        final newParams = Map<String, dynamic>.from(parameters);
                        newParams.remove(key);
                        newParams[newKey] = value;
                        onChanged(newParams);
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextFormField(
                      initialValue: value.toString(),
                      decoration: const InputDecoration(
                        labelText: 'Value',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (newValue) {
                        final newParams = Map<String, dynamic>.from(parameters);
                        newParams[key] = newValue;
                        onChanged(newParams);
                      },
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      final newParams = Map<String, dynamic>.from(parameters);
                      newParams.remove(key);
                      onChanged(newParams);
                    },
                  ),
                ],
              ),
            );
          },
        ),
        ElevatedButton.icon(
          onPressed: () {
            final newParams = Map<String, dynamic>.from(parameters);
            newParams['new_parameter'] = '';
            onChanged(newParams);
          },
          icon: const Icon(Icons.add),
          label: const Text('Add Parameter'),
        ),
      ],
    );
  }

  Future<void> _updateConfig(
    WidgetRef ref,
    AIConfigModel newConfig,
  ) async {
    await ref
        .read(systemConfigStateProvider.notifier)
        .updateAIConfig(newConfig);
  }
}
