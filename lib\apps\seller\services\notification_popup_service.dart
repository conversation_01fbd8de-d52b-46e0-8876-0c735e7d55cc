import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/notification/notification_model.dart';
import '../../../shared/models/notification/notification_status.dart';
import '../../../shared/services/notification/notification_service.dart';
import '../widgets/notification_popup.dart' as popup_widget;
import '../../../shared/providers/auth_provider.dart';

/// Service for handling notification popups in the seller app
class SellerNotificationPopupService {
  static SellerNotificationPopupService? _instance;
  static SellerNotificationPopupService get instance {
    _instance ??= SellerNotificationPopupService._();
    return _instance!;
  }

  SellerNotificationPopupService._();

  StreamSubscription<List<NotificationModel>>? _notificationSubscription;
  List<NotificationModel> _lastNotifications = [];
  BuildContext? _context;
  WidgetRef? _ref;

  /// Initialize the popup service
  void initialize(BuildContext context, WidgetRef ref) {
    _context = context;
    _ref = ref;
    _startListening();
  }

  /// Start listening for new notifications
  void _startListening() {
    if (_ref == null) return;

    final authState = _ref!.read(authProvider);
    if (authState == null) return;

    final userId = authState.id;

    final notificationService = NotificationService();

    _notificationSubscription?.cancel();
    _notificationSubscription = notificationService
        .getNotifications(userId)
        .listen(_handleNotificationsUpdate);
  }

  /// Handle notifications update
  void _handleNotificationsUpdate(List<NotificationModel> notifications) {
    if (_context == null || !_context!.mounted) return;

    // Find new unread notifications
    final newNotifications = notifications
        .where(
          (notification) =>
              notification.status == NotificationStatus.unread &&
              !_lastNotifications.any((old) => old.id == notification.id),
        )
        .toList();

    // Show popup for new notifications
    for (final notification in newNotifications) {
      _showNotificationPopup(notification);
    }

    _lastNotifications = notifications;
  }

  /// Show notification popup
  void _showNotificationPopup(NotificationModel notification) {
    if (_context == null || !_context!.mounted) return;

    // Use the static method from the popup widget file
    popup_widget.SellerNotificationPopupService.showNotificationPopup(
      _context!,
      notification,
      duration: const Duration(seconds: 5),
    );
  }

  /// Dispose the service
  void dispose() {
    _notificationSubscription?.cancel();
    _notificationSubscription = null;
    _context = null;
    _ref = null;
    _lastNotifications.clear();
  }

  /// Restart listening (useful when user changes)
  void restart() {
    dispose();
    if (_context != null && _ref != null) {
      initialize(_context!, _ref!);
    }
  }
}

/// Provider for the notification popup service
final sellerNotificationPopupServiceProvider =
    Provider<SellerNotificationPopupService>((ref) {
      return SellerNotificationPopupService.instance;
    });

/// Widget that initializes notification popup service
class SellerNotificationPopupInitializer extends ConsumerStatefulWidget {
  final Widget child;

  const SellerNotificationPopupInitializer({super.key, required this.child});

  @override
  ConsumerState<SellerNotificationPopupInitializer> createState() =>
      _SellerNotificationPopupInitializerState();
}

class _SellerNotificationPopupInitializerState
    extends ConsumerState<SellerNotificationPopupInitializer> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final service = ref.read(sellerNotificationPopupServiceProvider);
      service.initialize(context, ref);
    });
  }

  @override
  void dispose() {
    final service = ref.read(sellerNotificationPopupServiceProvider);
    service.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes to restart the service
    ref.listen(authProvider, (previous, next) {
      if (previous?.id != next?.id) {
        final service = ref.read(sellerNotificationPopupServiceProvider);
        service.restart();
      }
    });

    return widget.child;
  }
}
