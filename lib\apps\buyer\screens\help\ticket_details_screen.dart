import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/help_provider.dart';
import '../../../../shared/models/help/help_article.dart';

class TicketDetailsScreen extends ConsumerStatefulWidget {
  final HelpArticle ticket;

  const TicketDetailsScreen({
    super.key,
    required this.ticket,
  });

  @override
  ConsumerState<TicketDetailsScreen> createState() =>
      _TicketDetailsScreenState();
}

class _TicketDetailsScreenState extends ConsumerState<TicketDetailsScreen> {
  final _updateController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _updateController.dispose();
    super.dispose();
  }

  Future<void> _addUpdate() async {
    if (_updateController.text.trim().isEmpty) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final updatedTicket = widget.ticket.copyWith(
        updates: [
          ...?widget.ticket.updates,
          {
            'date': DateTime.now().toString(),
            'message': _updateController.text.trim(),
            'type': 'user',
          },
        ],
      );

      await ref.read(helpProvider.notifier).updateTicket(updatedTicket);
      _updateController.clear();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding update: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final helpState = ref.watch(helpProvider);
    final isTicketClosed = widget.ticket.status.toLowerCase() == 'closed';

    return Scaffold(
      appBar: AppBar(
        title: const Text('Ticket Details'),
        actions: [
          if (!isTicketClosed)
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: helpState.isLoading
                  ? null
                  : () async {
                      await ref
                          .read(helpProvider.notifier)
                          .closeTicket(widget.ticket.id);
                      if (mounted) {
                        Navigator.pop(context);
                      }
                    },
            ),
        ],
      ),
      body: helpState.when(
        data: (_) => ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ticket #${widget.ticket.id}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.ticket.title,
                      style: theme.textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor(widget.ticket.status),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            widget.ticket.status,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          widget.ticket.date,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Description',
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.ticket.description ?? 'No description provided',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Updates',
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    if (widget.ticket.updates?.isEmpty ?? true)
                      Text(
                        'No updates yet',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      )
                    else
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: widget.ticket.updates!.length,
                        itemBuilder: (context, index) {
                          final update = widget.ticket.updates![index];
                          final isUserUpdate = update['type'] == 'user';
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      isUserUpdate
                                          ? Icons.person
                                          : Icons.support_agent,
                                      size: 16,
                                      color: theme.colorScheme.onSurfaceVariant,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      update['date'] as String,
                                      style:
                                          theme.textTheme.bodySmall?.copyWith(
                                        color:
                                            theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  update['message'] as String,
                                  style: theme.textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    if (!isTicketClosed) ...[
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _updateController,
                              decoration: const InputDecoration(
                                hintText: 'Add an update...',
                                border: OutlineInputBorder(),
                              ),
                              maxLines: 3,
                            ),
                          ),
                          const SizedBox(width: 8),
                          IconButton(
                            onPressed: _isSubmitting ? null : _addUpdate,
                            icon: _isSubmitting
                                ? const SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  )
                                : const Icon(Icons.send),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text(
            'Error: ${error.toString()}',
            style: TextStyle(color: theme.colorScheme.error),
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Colors.green;
      case 'in progress':
        return Colors.orange;
      case 'closed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
