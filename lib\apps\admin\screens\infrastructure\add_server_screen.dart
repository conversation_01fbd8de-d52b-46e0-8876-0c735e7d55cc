import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../models/server_config.dart';
import '../../services/infrastructure_service.dart';
import '../../../../shared/core/service_locator.dart';
import '../../../../shared/widgets/app_loading_indicator.dart';

/// Screen for adding new server configurations
class AddServerScreen extends ConsumerStatefulWidget {
  const AddServerScreen({super.key});

  @override
  ConsumerState<AddServerScreen> createState() => _AddServerScreenState();
}

class _AddServerScreenState extends ConsumerState<AddServerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _environmentController = TextEditingController();
  final _awsRegionController = TextEditingController();
  final _datacenterIpController = TextEditingController();
  final _databaseUrlController = TextEditingController();
  final _redisUrlController = TextEditingController();
  final _maxConnectionsController = TextEditingController(text: '100');
  final _timeoutController = TextEditingController(text: '30');

  bool _isLoading = false;
  String? _errorMessage;
  
  // Configuration toggles
  bool _autoScaling = true;
  bool _loadBalancing = true;
  bool _cachingEnabled = true;
  bool _sslEnabled = true;
  bool _rateLimitingEnabled = true;
  bool _ddosProtection = true;

  @override
  void dispose() {
    _environmentController.dispose();
    _awsRegionController.dispose();
    _datacenterIpController.dispose();
    _databaseUrlController.dispose();
    _redisUrlController.dispose();
    _maxConnectionsController.dispose();
    _timeoutController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Server Configuration'),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const AppLoadingIndicator()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_errorMessage != null) ...[
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          border: Border.all(color: Colors.red.shade200),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error, color: Colors.red.shade600),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(color: Colors.red.shade600),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                    
                    _buildBasicConfigSection(),
                    const SizedBox(height: 24),
                    _buildConnectionConfigSection(),
                    const SizedBox(height: 24),
                    _buildFeatureConfigSection(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicConfigSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Basic Configuration',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _environmentController,
              decoration: const InputDecoration(
                labelText: 'Environment Name',
                hintText: 'e.g., production, staging, development',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Environment name is required';
                }
                if (!RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(value.trim())) {
                  return 'Environment name can only contain letters, numbers, hyphens, and underscores';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _awsRegionController,
              decoration: const InputDecoration(
                labelText: 'AWS Region',
                hintText: 'e.g., us-east-1, eu-west-1',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'AWS region is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _datacenterIpController,
              decoration: const InputDecoration(
                labelText: 'Datacenter IP',
                hintText: 'e.g., *************',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Datacenter IP is required';
                }
                // Basic IP validation
                if (!RegExp(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$').hasMatch(value.trim())) {
                  return 'Please enter a valid IP address';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionConfigSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Connection Configuration',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _databaseUrlController,
              decoration: const InputDecoration(
                labelText: 'Database URL',
                hintText: '********************************/database',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Database URL is required';
                }
                if (!value.trim().startsWith('postgresql://')) {
                  return 'Database URL must start with postgresql://';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _redisUrlController,
              decoration: const InputDecoration(
                labelText: 'Redis URL',
                hintText: 'redis://host:6379',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Redis URL is required';
                }
                if (!value.trim().startsWith('redis://')) {
                  return 'Redis URL must start with redis://';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _maxConnectionsController,
                    decoration: const InputDecoration(
                      labelText: 'Max Connections',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Max connections is required';
                      }
                      final intValue = int.tryParse(value.trim());
                      if (intValue == null || intValue <= 0) {
                        return 'Must be a positive number';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _timeoutController,
                    decoration: const InputDecoration(
                      labelText: 'Timeout (seconds)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Timeout is required';
                      }
                      final intValue = int.tryParse(value.trim());
                      if (intValue == null || intValue <= 0) {
                        return 'Must be a positive number';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureConfigSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Feature Configuration',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Auto Scaling'),
              subtitle: const Text('Automatically scale servers based on load'),
              value: _autoScaling,
              onChanged: (value) => setState(() => _autoScaling = value),
            ),
            SwitchListTile(
              title: const Text('Load Balancing'),
              subtitle: const Text('Distribute traffic across multiple servers'),
              value: _loadBalancing,
              onChanged: (value) => setState(() => _loadBalancing = value),
            ),
            SwitchListTile(
              title: const Text('Caching'),
              subtitle: const Text('Enable Redis caching'),
              value: _cachingEnabled,
              onChanged: (value) => setState(() => _cachingEnabled = value),
            ),
            SwitchListTile(
              title: const Text('SSL/TLS'),
              subtitle: const Text('Enable secure connections'),
              value: _sslEnabled,
              onChanged: (value) => setState(() => _sslEnabled = value),
            ),
            SwitchListTile(
              title: const Text('Rate Limiting'),
              subtitle: const Text('Limit request rates per client'),
              value: _rateLimitingEnabled,
              onChanged: (value) => setState(() => _rateLimitingEnabled = value),
            ),
            SwitchListTile(
              title: const Text('DDoS Protection'),
              subtitle: const Text('Enable DDoS attack protection'),
              value: _ddosProtection,
              onChanged: (value) => setState(() => _ddosProtection = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => context.pop(),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveServerConfig,
            child: const Text('Add Server'),
          ),
        ),
      ],
    );
  }

  Future<void> _saveServerConfig() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final config = ServerConfig(
        environment: _environmentController.text.trim(),
        awsRegion: _awsRegionController.text.trim(),
        datacenterIp: _datacenterIpController.text.trim(),
        databaseUrl: _databaseUrlController.text.trim(),
        redisUrl: _redisUrlController.text.trim(),
        maxConnections: int.parse(_maxConnectionsController.text.trim()),
        timeout: int.parse(_timeoutController.text.trim()),
        awsStatus: ServerState.online, // Default to online, will be checked by health monitoring
        datacenterStatus: ServerState.online,
        autoScaling: _autoScaling,
        loadBalancing: _loadBalancing,
        cachingEnabled: _cachingEnabled,
        sslEnabled: _sslEnabled,
        rateLimitingEnabled: _rateLimitingEnabled,
        ddosProtection: _ddosProtection,
        lastUpdated: DateTime.now(),
      );

      final infrastructureService = serviceLocator<InfrastructureService>();
      await infrastructureService.addServerConfig(config);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Server configuration added for ${config.environment}'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
