import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../services/infrastructure_service.dart';
import '../models/traffic_config.dart';
import '../models/server_config.dart' as server_config;

/// Database service provider
final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

/// Infrastructure service provider
final infrastructureServiceProvider = Provider<InfrastructureService>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return InfrastructureService(
    databaseService,
    http.Client(),
  );
});

/// Traffic configuration provider
final trafficConfigProvider = FutureProvider<TrafficConfig>((ref) async {
  final service = ref.watch(infrastructureServiceProvider);
  return service.getTrafficConfig();
});

/// Server status provider
final serverStatusProvider = FutureProvider<ServerStatus>((ref) async {
  final service = ref.watch(infrastructureServiceProvider);
  return service.getServerStatus();
});

/// Server configurations provider
final serverConfigsProvider = FutureProvider<Map<String, server_config.ServerConfig>>((ref) async {
  final service = ref.watch(infrastructureServiceProvider);
  return service.getServerConfigs();
});

/// Real-time traffic analytics provider
final trafficAnalyticsProvider = StreamProvider<TrafficAnalytics>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return databaseService.watchRecord('infrastructure', 'traffic_analytics')
      .map((data) {
    if (data != null) {
      return TrafficAnalytics.fromJson(data);
    } else {
      return TrafficAnalytics(
        timestamp: DateTime.now(),
        totalRequests: 0,
        awsRequests: 0,
        datacenterRequests: 0,
        averageResponseTime: 0.0,
        errorRate: 0.0,
        requestsByCountry: {},
        requestsByEndpoint: {},
      );
    }
  });
});

/// Server metrics provider for real-time monitoring
final serverMetricsProvider = StreamProvider.family<List<ServerInfo>, String>((ref, serverType) {
  final databaseService = ref.watch(databaseServiceProvider);
  return databaseService.watchCollection(
    'server_metrics',
    where: 'server_type = ?',
    whereParams: [serverType],
  ).map((data) {
    return data
        .map((doc) => ServerInfo.fromJson(doc))
        .toList();
  });
});

/// Load balancer configuration provider
final loadBalancerConfigProvider = FutureProvider<List<LoadBalancerConfig>>((ref) async {
  final databaseService = ref.watch(databaseServiceProvider);
  final data = await databaseService.find('infrastructure', 'load_balancers');

  if (data != null) {
    return (data['configs'] as List)
        .map((config) => LoadBalancerConfig.fromJson(config))
        .toList();
  } else {
    return [];
  }
});

/// Routing rules provider
final routingRulesProvider = StreamProvider<List<RoutingRule>>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return databaseService.watchCollection(
    'routing_rules',
    orderBy: 'priority',
  ).map((data) {
    return data
        .map((doc) => RoutingRule.fromJson(doc))
        .toList();
  });
});

/// Auto scaling configuration provider
final autoScalingConfigProvider = FutureProvider.family<server_config.AutoScalingConfig, String>((ref, environment) async {
  final databaseService = ref.watch(databaseServiceProvider);
  final data = await databaseService.find('auto_scaling_environments', environment);

  if (data != null) {
    return server_config.AutoScalingConfig.fromJson(data);
  } else {
    // Return default auto scaling configuration
    return const server_config.AutoScalingConfig(
      enabled: false,
      minInstances: 1,
      maxInstances: 10,
      cpuThresholdUp: 80.0,
      cpuThresholdDown: 20.0,
      memoryThresholdUp: 80.0,
      memoryThresholdDown: 20.0,
      scaleUpCooldown: 300,
      scaleDownCooldown: 600,
    );
  }
});

/// Infrastructure alerts provider
final infrastructureAlertsProvider = StreamProvider<List<InfrastructureAlert>>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return databaseService.watchCollection(
    'infrastructure_alerts',
    orderBy: 'timestamp DESC',
    limit: 50,
  ).map((data) {
    return data
        .map((doc) => InfrastructureAlert.fromJson(doc))
        .toList();
  });
});

/// Infrastructure alert model
class InfrastructureAlert {
  final String id;
  final AlertSeverity severity;
  final String title;
  final String message;
  final String source;
  final DateTime timestamp;
  final bool acknowledged;
  final Map<String, dynamic> metadata;

  InfrastructureAlert({
    required this.id,
    required this.severity,
    required this.title,
    required this.message,
    required this.source,
    required this.timestamp,
    this.acknowledged = false,
    this.metadata = const {},
  });

  factory InfrastructureAlert.fromJson(Map<String, dynamic> json) {
    return InfrastructureAlert(
      id: json['id'] ?? '',
      severity: AlertSeverity.values.firstWhere(
        (e) => e.name == json['severity'],
        orElse: () => AlertSeverity.info,
      ),
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      source: json['source'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      acknowledged: json['acknowledged'] ?? false,
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'severity': severity.name,
      'title': title,
      'message': message,
      'source': source,
      'timestamp': timestamp.toIso8601String(),
      'acknowledged': acknowledged,
      'metadata': metadata,
    };
  }
}

/// Alert severity levels
enum AlertSeverity {
  critical,
  warning,
  info;

  String get displayName {
    switch (this) {
      case AlertSeverity.critical:
        return 'Critical';
      case AlertSeverity.warning:
        return 'Warning';
      case AlertSeverity.info:
        return 'Info';
    }
  }

  Color get color {
    switch (this) {
      case AlertSeverity.critical:
        return const Color(0xFFF44336); // Red
      case AlertSeverity.warning:
        return const Color(0xFFFF9800); // Orange
      case AlertSeverity.info:
        return const Color(0xFF2196F3); // Blue
    }
  }
}

/// Infrastructure dashboard state provider
final infrastructureDashboardProvider = Provider<InfrastructureDashboardState>((ref) {
  final trafficConfig = ref.watch(trafficConfigProvider);
  final serverStatus = ref.watch(serverStatusProvider);
  final trafficAnalytics = ref.watch(trafficAnalyticsProvider);
  final alerts = ref.watch(infrastructureAlertsProvider);

  return InfrastructureDashboardState(
    trafficConfig: trafficConfig,
    serverStatus: serverStatus,
    trafficAnalytics: trafficAnalytics,
    alerts: alerts,
  );
});

/// Infrastructure dashboard state
class InfrastructureDashboardState {
  final AsyncValue<TrafficConfig> trafficConfig;
  final AsyncValue<ServerStatus> serverStatus;
  final AsyncValue<TrafficAnalytics> trafficAnalytics;
  final AsyncValue<List<InfrastructureAlert>> alerts;

  InfrastructureDashboardState({
    required this.trafficConfig,
    required this.serverStatus,
    required this.trafficAnalytics,
    required this.alerts,
  });

  bool get isLoading {
    return trafficConfig.isLoading ||
           serverStatus.isLoading ||
           trafficAnalytics.isLoading ||
           alerts.isLoading;
  }

  bool get hasError {
    return trafficConfig.hasError ||
           serverStatus.hasError ||
           trafficAnalytics.hasError ||
           alerts.hasError;
  }

  String? get errorMessage {
    if (trafficConfig.hasError) return trafficConfig.error.toString();
    if (serverStatus.hasError) return serverStatus.error.toString();
    if (trafficAnalytics.hasError) return trafficAnalytics.error.toString();
    if (alerts.hasError) return alerts.error.toString();
    return null;
  }

  int get criticalAlertsCount {
    return alerts.value
        ?.where((alert) => alert.severity == AlertSeverity.critical && !alert.acknowledged)
        .length ?? 0;
  }

  bool get isHealthy {
    final status = serverStatus.value;
    if (status == null) return false;

    // Check if both AWS and datacenter have healthy status
    return status.awsStatus == ServerHealth.healthy &&
           status.datacenterStatus == ServerHealth.healthy;
  }
}
