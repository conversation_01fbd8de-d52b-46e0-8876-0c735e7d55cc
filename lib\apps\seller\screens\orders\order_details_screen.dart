import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/providers/order_provider.dart';
import 'package:shivish/shared/models/order/order_model.dart';
import 'package:shivish/shared/services/order/invoice_service.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

class OrderDetailsScreen extends ConsumerStatefulWidget {
  final String orderId;

  const OrderDetailsScreen({super.key, required this.orderId});

  @override
  ConsumerState<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends ConsumerState<OrderDetailsScreen> {
  bool _isLoading = true;
  bool _isUpdatingPrices = false;
  bool _isDistributingTotal = false;
  bool _isGeneratingInvoice = false;
  OrderModel? _order;
  String? _error;
  String? _invoicePath;
  final DatabaseService _databaseService = DatabaseService(
    DatabaseConfig.fromEnvironment(),
  );

  // List to store editable items
  late List<OrderItem> _editableItems = [];
  // Flag to track if we're in edit mode
  bool _isEditingPrices = false;

  // Controller for total price input
  final TextEditingController _totalPriceController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadOrder();
  }

  @override
  void dispose() {
    _totalPriceController.dispose();
    super.dispose();
  }

  Future<void> _loadOrder() async {
    try {
      final orderDoc = await ref
          .read(orderServiceProvider)
          .getOrder(widget.orderId);
      if (mounted) {
        try {
          final data = orderDoc.data() as Map<String, dynamic>;

          // Add the document ID to the data
          final orderData = {'id': orderDoc.id, ...data};

          // Ensure required fields have default values if they're null
          orderData['buyerId'] = orderData['buyerId'] ?? '';
          orderData['sellerId'] = orderData['sellerId'] ?? '';
          orderData['customerName'] =
              orderData['customerName'] ?? 'Unknown Customer';
          orderData['customerPhone'] = orderData['customerPhone'] ?? '';

          // Ensure createdAt and updatedAt are valid DateTime strings
          if (orderData['createdAt'] == null) {
            orderData['createdAt'] = DateTime.now().toIso8601String();
          } else if (orderData['createdAt'] is DateTime) {
            orderData['createdAt'] = (orderData['createdAt'] as DateTime)
                .toIso8601String();
          } else if (orderData['createdAt'] is String) {
            // Already a string, keep as is
          }

          if (orderData['updatedAt'] == null) {
            orderData['updatedAt'] = DateTime.now().toIso8601String();
          } else if (orderData['updatedAt'] is DateTime) {
            orderData['updatedAt'] = (orderData['updatedAt'] as DateTime)
                .toIso8601String();
          } else if (orderData['updatedAt'] is String) {
            // Already a string, keep as is
          }

          // Check if this is a shopping list order
          final bool isShoppingListOrder = orderData.containsKey(
            'shoppingListId',
          );

          if (isShoppingListOrder) {
            debugPrint(
              'Found shopping list order: ${orderDoc.id} with shoppingListId: ${orderData['shoppingListId']}',
            );

            // Set orderType to identify it as a shopping list order
            orderData['orderType'] = 'shopping_list';

            // Handle status - ensure it's not null and convert 'requested' to 'pending'
            if (orderData['status'] == null) {
              debugPrint(
                'Setting null status to "pending" for order ${orderDoc.id}',
              );
              orderData['status'] = 'pending';
            } else if (orderData['status'] == 'requested') {
              debugPrint(
                'Converting status "requested" to "pending" for order ${orderDoc.id}',
              );
              orderData['status'] = 'pending';
            }
          }

          // Ensure items is a valid list
          if (orderData['items'] == null) {
            orderData['items'] = [];
          } else if (isShoppingListOrder &&
              (orderData['items'] is List &&
                  (orderData['items'] as List).isEmpty)) {
            debugPrint(
              'Creating items array for shopping list order ${orderDoc.id}',
            );

            // Get name, price, and quantity with null safety
            String itemName = 'Unknown Item';
            double itemPrice = 0.0;
            int itemQuantity = 1;

            // Safely extract name
            try {
              if (orderData.containsKey('name') && orderData['name'] != null) {
                itemName = orderData['name'].toString();
              }
            } catch (e) {
              debugPrint('Error getting name: $e');
            }

            // Safely extract price
            try {
              if (orderData.containsKey('price') &&
                  orderData['price'] != null) {
                if (orderData['price'] is double) {
                  itemPrice = orderData['price'];
                } else if (orderData['price'] is int) {
                  itemPrice = (orderData['price'] as int).toDouble();
                } else if (orderData['price'] is String) {
                  itemPrice = double.tryParse(orderData['price']) ?? 0.0;
                }
              }
            } catch (e) {
              debugPrint('Error getting price: $e');
            }

            // Safely extract quantity
            try {
              if (orderData.containsKey('quantity') &&
                  orderData['quantity'] != null) {
                if (orderData['quantity'] is int) {
                  itemQuantity = orderData['quantity'];
                } else if (orderData['quantity'] is double) {
                  itemQuantity = (orderData['quantity'] as double).toInt();
                } else if (orderData['quantity'] is String) {
                  itemQuantity = int.tryParse(orderData['quantity']) ?? 1;
                }
              }
            } catch (e) {
              debugPrint('Error getting quantity: $e');
            }

            // Create a single item from the shopping list data
            orderData['items'] = [
              {
                'id': orderData['id'] ?? '',
                'productId': '',
                'name': itemName,
                'price': itemPrice,
                'quantity': itemQuantity,
              },
            ];
          } else if (orderData['items'] is List) {
            // Ensure each item in the items array has all required fields
            final List<dynamic> items = orderData['items'] as List<dynamic>;
            final List<Map<String, dynamic>> validItems = [];

            for (final item in items) {
              if (item is Map<String, dynamic>) {
                // Ensure required fields are present
                final Map<String, dynamic> validItem = {...item};

                validItem['id'] = validItem['id'] ?? '';
                validItem['productId'] = validItem['productId'] ?? '';
                validItem['name'] = validItem['name'] ?? 'Unknown Item';

                // Handle price
                if (!validItem.containsKey('price') ||
                    validItem['price'] == null) {
                  validItem['price'] = 0.0;
                } else if (validItem['price'] is int) {
                  validItem['price'] = (validItem['price'] as int).toDouble();
                } else if (validItem['price'] is String) {
                  validItem['price'] =
                      double.tryParse(validItem['price']) ?? 0.0;
                }

                // Handle quantity
                if (!validItem.containsKey('quantity') ||
                    validItem['quantity'] == null) {
                  validItem['quantity'] = 1;
                } else if (validItem['quantity'] is double) {
                  validItem['quantity'] = (validItem['quantity'] as double)
                      .toInt();
                } else if (validItem['quantity'] is String) {
                  validItem['quantity'] =
                      int.tryParse(validItem['quantity']) ?? 1;
                }

                validItems.add(validItem);
              }
            }

            // Replace the items array with the validated items
            orderData['items'] = validItems;
          }

          // Ensure deliveryAddress is valid
          if (orderData['deliveryAddress'] == null) {
            orderData['deliveryAddress'] = {
              'street': '',
              'city': '',
              'state': '',
              'country': 'India',
              'postalCode': '',
              'contactName': orderData['customerName'] ?? 'Unknown',
              'contactPhone': orderData['customerPhone'] ?? '',
            };
          } else {
            // Ensure all required fields in deliveryAddress have values
            var deliveryAddress =
                orderData['deliveryAddress'] as Map<String, dynamic>;

            // Ensure country is set
            if (!deliveryAddress.containsKey('country') ||
                deliveryAddress['country'] == null ||
                deliveryAddress['country'] == '') {
              debugPrint(
                'Setting default country in deliveryAddress for order ${orderDoc.id}',
              );
              deliveryAddress['country'] = 'India';
            }

            // Ensure other required fields have values
            deliveryAddress['street'] = deliveryAddress['street'] ?? '';
            deliveryAddress['city'] = deliveryAddress['city'] ?? '';
            deliveryAddress['state'] = deliveryAddress['state'] ?? '';
            deliveryAddress['postalCode'] = deliveryAddress['postalCode'] ?? '';
            deliveryAddress['contactName'] =
                deliveryAddress['contactName'] ??
                orderData['customerName'] ??
                'Unknown';
            deliveryAddress['contactPhone'] =
                deliveryAddress['contactPhone'] ??
                orderData['customerPhone'] ??
                '';

            // Update the orderData with the fixed deliveryAddress
            orderData['deliveryAddress'] = deliveryAddress;
          }

          // Ensure paymentDetails is valid and has all required fields
          if (orderData['paymentDetails'] == null) {
            orderData['paymentDetails'] = {
              'method': 'cod',
              'status': 'pending',
              'amount': orderData['total'] ?? 0.0,
            };
          } else {
            // Ensure all required fields in paymentDetails have values
            var paymentDetails =
                orderData['paymentDetails'] as Map<String, dynamic>;

            // Ensure required fields have values
            if (!paymentDetails.containsKey('method') ||
                paymentDetails['method'] == null ||
                paymentDetails['method'] == '') {
              debugPrint(
                'Setting default method in paymentDetails for order ${orderDoc.id}',
              );
              paymentDetails['method'] = 'cod';
            }

            if (!paymentDetails.containsKey('status') ||
                paymentDetails['status'] == null ||
                paymentDetails['status'] == '') {
              debugPrint(
                'Setting default status in paymentDetails for order ${orderDoc.id}',
              );
              paymentDetails['status'] = 'pending';
            }

            if (!paymentDetails.containsKey('amount') ||
                paymentDetails['amount'] == null) {
              debugPrint(
                'Setting default amount in paymentDetails for order ${orderDoc.id}',
              );
              paymentDetails['amount'] = orderData['total'] ?? 0.0;
            } else if (paymentDetails['amount'] is int) {
              paymentDetails['amount'] = (paymentDetails['amount'] as int)
                  .toDouble();
            } else if (paymentDetails['amount'] is String) {
              paymentDetails['amount'] =
                  double.tryParse(paymentDetails['amount']) ?? 0.0;
            }

            // Update the orderData with the fixed paymentDetails
            orderData['paymentDetails'] = paymentDetails;
          }

          // Ensure paymentMethod is valid
          if (orderData['paymentMethod'] == null) {
            orderData['paymentMethod'] = 'cod';
          }

          // Ensure total is present and is a double
          if (!orderData.containsKey('total') || orderData['total'] == null) {
            orderData['total'] = 0.0;
          } else if (orderData['total'] is int) {
            orderData['total'] = (orderData['total'] as int).toDouble();
          } else if (orderData['total'] is String) {
            orderData['total'] = double.tryParse(orderData['total']) ?? 0.0;
          }

          final orderModel = OrderModel.fromJson(orderData);
          setState(() {
            _order = orderModel;
            _editableItems = List.from(orderModel.items);
            _isLoading = false;
          });
        } catch (parseError) {
          debugPrint('Error parsing order data: $parseError');
          setState(() {
            _error = 'Error parsing order data: $parseError';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  // Helper method to get status color
  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.confirmed:
      case OrderStatus.processing:
        return Colors.blue;
      case OrderStatus.readyForPickup:
      case OrderStatus.outForDelivery:
      case OrderStatus.inTransit:
        return Colors.purple;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
      case OrderStatus.refunded:
        return Colors.red;
    }
  }

  Future<void> _updateOrderStatus(OrderStatus status) async {
    try {
      // Update the order in hybrid storage
      await _databaseService.update('orders', widget.orderId, {
        'status': status.name,
        'updated_at': DateTime.now().toIso8601String(),
      });

      await _loadOrder();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update order status: $e')),
        );
      }
    }
  }

  // Toggle edit mode for prices
  void _toggleEditPrices() {
    setState(() {
      _isEditingPrices = !_isEditingPrices;
      // Reset editable items when entering edit mode
      if (_isEditingPrices) {
        _editableItems = List.from(_order!.items);
      }
    });
  }

  // Update a specific item's price
  void _updateItemPrice(int index, double price) {
    setState(() {
      // Create a new item with the updated price
      final updatedItem = _editableItems[index].copyWith(price: price);
      // Replace the item in the list
      _editableItems[index] = updatedItem;
    });
  }

  // Calculate the total price from all items
  double _calculateTotal() {
    return _editableItems.fold(
      0.0,
      (total, item) => total + (item.price * item.quantity),
    );
  }

  // Show dialog to distribute total price
  void _showDistributeTotalDialog(double currentTotal) {
    // Set initial value to current total
    _totalPriceController.text = currentTotal.toString();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Distribute Total Price'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enter the total price to distribute evenly among all items:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _totalPriceController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Total Price',
                prefixText: '₹',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              _distributeTotalToItems();
            },
            child: _isDistributingTotal
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Distribute'),
          ),
        ],
      ),
    );
  }

  // Save the updated prices
  Future<void> _saveUpdatedPrices() async {
    if (_order == null) return;

    setState(() {
      _isUpdatingPrices = true;
    });

    try {
      final total = _calculateTotal();

      // Update the order in hybrid database
      await ref
          .read(orderServiceProvider)
          .updateOrderItemPrices(widget.orderId, _editableItems, total);

      // Reload the order to get the updated data
      await _loadOrder();

      // Exit edit mode
      setState(() {
        _isEditingPrices = false;
        _isUpdatingPrices = false;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Prices updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isUpdatingPrices = false;
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update prices: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Check if any item has zero price
  bool _hasZeroPriceItems() {
    if (_order == null) return false;
    return _order!.items.any((item) => item.price <= 0);
  }

  // Check if order has total price but zero item prices
  bool _hasTotalButZeroItemPrices() {
    if (_order == null) return false;
    return _order!.total > 0 && _order!.items.any((item) => item.price <= 0);
  }

  // Distribute total price to items
  Future<void> _distributeTotalToItems() async {
    if (_order == null) return;

    // Get the total price from the controller
    final totalText = _totalPriceController.text;
    if (totalText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid total price'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final total = double.tryParse(totalText);
    if (total == null || total <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid total price'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isDistributingTotal = true;
    });

    try {
      // Call the service to distribute the total
      await ref
          .read(orderServiceProvider)
          .distributeOrderTotalToItems(widget.orderId, total);

      // Reload the order
      await _loadOrder();

      setState(() {
        _isDistributingTotal = false;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Prices updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isDistributingTotal = false;
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update prices: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(body: LoadingIndicator());
    }

    if (_error != null) {
      return Scaffold(
        body: ErrorMessage(message: _error!, onRetry: _loadOrder),
      );
    }

    if (_order == null) {
      return Scaffold(
        body: ErrorMessage(message: 'Order not found', onRetry: _loadOrder),
      );
    }

    final order = _order!;

    return Scaffold(
      appBar: AppBar(title: Text('Order #${order.id}')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order Status',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(order.status).withAlpha(25),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        order.status.name.toUpperCase(),
                        style: TextStyle(
                          color: _getStatusColor(order.status),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (order.status == OrderStatus.pending)
                      FilledButton.icon(
                        onPressed: () =>
                            _updateOrderStatus(OrderStatus.processing),
                        icon: const Icon(Icons.check),
                        label: const Text('Accept Order'),
                      ),
                    if (order.status == OrderStatus.processing)
                      FilledButton.icon(
                        onPressed: () =>
                            _updateOrderStatus(OrderStatus.delivered),
                        icon: const Icon(Icons.local_shipping),
                        label: const Text('Mark as Shipped'),
                      ),
                    if (order.status == OrderStatus.delivered)
                      FilledButton.icon(
                        onPressed: () =>
                            _updateOrderStatus(OrderStatus.delivered),
                        icon: const Icon(Icons.check_circle),
                        label: const Text('Mark as Delivered'),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Order Items',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        // Show appropriate buttons based on order state
                        if (_hasZeroPriceItems() ||
                            order.shoppingListId != null)
                          Row(
                            children: [
                              // Show distribute button if total price exists but item prices are zero
                              if (_hasTotalButZeroItemPrices())
                                TextButton.icon(
                                  onPressed: () =>
                                      _showDistributeTotalDialog(order.total),
                                  icon: const Icon(Icons.calculate),
                                  label: const Text('Distribute Total'),
                                ),
                              // Show edit button for manual price editing
                              TextButton.icon(
                                onPressed: _toggleEditPrices,
                                icon: Icon(
                                  _isEditingPrices ? Icons.check : Icons.edit,
                                ),
                                label: Text(
                                  _isEditingPrices ? 'Done' : 'Edit Prices',
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // Show different UI based on edit mode
                    if (_isEditingPrices)
                      ..._buildEditableItemsList()
                    else
                      ...order.items.map(
                        (item) => ListTile(
                          leading: item.imageUrl != null
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    item.imageUrl!,
                                    width: 56,
                                    height: 56,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        width: 56,
                                        height: 56,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.surfaceContainerHighest,
                                        child: Icon(
                                          Icons.error_outline,
                                          size: 24,
                                          color: Theme.of(
                                            context,
                                          ).colorScheme.error,
                                        ),
                                      );
                                    },
                                  ),
                                )
                              : Container(
                                  width: 56,
                                  height: 56,
                                  decoration: BoxDecoration(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.surfaceContainerHighest,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.image_not_supported,
                                    size: 24,
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.primary,
                                  ),
                                ),
                          title: Text(item.name),
                          subtitle: Text('${item.quantity} x ₹${item.price}'),
                          trailing: Text(
                            '₹${(item.price * item.quantity).toStringAsFixed(2)}',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ),
                      ),

                    // Show save button when in edit mode
                    if (_isEditingPrices)
                      Padding(
                        padding: const EdgeInsets.only(top: 16.0),
                        child: FilledButton.icon(
                          onPressed: _isUpdatingPrices
                              ? null
                              : _saveUpdatedPrices,
                          icon: _isUpdatingPrices
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Icon(Icons.save),
                          label: Text(
                            _isUpdatingPrices ? 'Saving...' : 'Save Prices',
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Order Summary',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        ElevatedButton.icon(
                          onPressed: _isGeneratingInvoice
                              ? null
                              : () => _generateAndShareInvoice(order),
                          icon: _isGeneratingInvoice
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Icon(Icons.receipt_long, size: 16),
                          label: const Text('Invoice'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(
                              context,
                            ).colorScheme.primaryContainer,
                            foregroundColor: Theme.of(
                              context,
                            ).colorScheme.onPrimaryContainer,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    _buildSummaryRow(
                      'Subtotal',
                      _isEditingPrices ? _calculateTotal() : order.total,
                    ),
                    _buildSummaryRow('Tax', 0.0),
                    _buildSummaryRow('Shipping', 0.0),
                    const Divider(),
                    _buildSummaryRow(
                      'Total',
                      _isEditingPrices ? _calculateTotal() : order.total,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (order.notes != null && order.notes!.isNotEmpty) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notes',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(order.notes!),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, double amount, {TextStyle? style}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text('₹${amount.toStringAsFixed(2)}', style: style),
        ],
      ),
    );
  }

  // Build the list of editable items
  List<Widget> _buildEditableItemsList() {
    final List<Widget> widgets = [];

    for (int i = 0; i < _editableItems.length; i++) {
      final item = _editableItems[i];

      widgets.add(
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            children: [
              // Item image or placeholder
              item.imageUrl != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        item.imageUrl!,
                        width: 56,
                        height: 56,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 56,
                            height: 56,
                            color: Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                            child: Icon(
                              Icons.error_outline,
                              size: 24,
                              color: Theme.of(context).colorScheme.error,
                            ),
                          );
                        },
                      ),
                    )
                  : Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.image_not_supported,
                        size: 24,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
              const SizedBox(width: 16),

              // Item details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Text(
                      'Quantity: ${item.quantity}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),

              // Price input field
              SizedBox(
                width: 100,
                child: TextFormField(
                  initialValue: item.price.toString(),
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'Price',
                    prefixText: '₹',
                  ),
                  onChanged: (value) {
                    final price = double.tryParse(value);
                    if (price != null) {
                      _updateItemPrice(i, price);
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      );

      // Add a divider between items
      if (i < _editableItems.length - 1) {
        widgets.add(const Divider());
      }
    }

    return widgets;
  }

  // Generate and share invoice
  Future<void> _generateAndShareInvoice(OrderModel order) async {
    if (mounted) {
      setState(() {
        _isGeneratingInvoice = true;
      });
    }

    try {
      // Check if invoice already exists, if not generate it
      final existingPath = await InvoiceService.getInvoicePath(order.id);
      _invoicePath =
          existingPath ?? await InvoiceService.generateInvoice(order);

      // Download and open the invoice
      if (_invoicePath != null) {
        await InvoiceService.downloadAndOpenInvoice(order.id);

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Invoice downloaded successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to generate invoice: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingInvoice = false;
        });
      }
    }
  }
}
