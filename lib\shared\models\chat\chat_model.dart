import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_model.freezed.dart';
part 'chat_model.g.dart';

enum ChatSenderType {
  user,
  ai,
}

enum ChatMessageType {
  text,
  voice,
  product,
  order,
}

@freezed
abstract class ChatMessage with _$ChatMessage {
  const factory ChatMessage({
    required String id,
    required String content,
    required ChatMessageType type,
    required ChatSenderType sender,
    required DateTime timestamp,
    String? voiceUrl,
    String? productId,
    String? orderId,
  }) = _ChatMessage;

  factory ChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageFromJson(json);
}

@freezed
abstract class ChatState with _$ChatState {
  const factory ChatState({
    required List<ChatMessage> messages,
    required bool isLoading,
    required bool hasError,
    String? errorMessage,
    @Default(false) bool isVoiceRecording,
    @Default(false) bool isVoicePlaying,
    String? currentVoiceUrl,
    @Default(0) int currentVoicePosition,
    @Default(0) int currentVoiceDuration,
    ChatContext? chatContext,
    String? currentVoiceMessageId,
  }) = _ChatState;

  factory ChatState.fromJson(Map<String, dynamic> json) =>
      _$ChatStateFromJson(json);
}

@freezed
abstract class ChatContext with _$ChatContext {
  const factory ChatContext({
    required String sessionId,
    required String userId,
    required List<ChatMessage> messages,
    required DateTime lastUpdated,
  }) = _ChatContext;

  factory ChatContext.fromJson(Map<String, dynamic> json) =>
      _$ChatContextFromJson(json);
}
