// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ApiResponseImpl<T> _$$ApiResponseImplFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => _$ApiResponseImpl<T>(
  data: _$nullableGenericFromJson(json['data'], fromJsonT),
  isSuccess: json['isSuccess'] as bool? ?? false,
  error: json['error'] == null
      ? null
      : ApiError.fromJson(json['error'] as Map<String, dynamic>),
  metadata: json['metadata'] == null
      ? null
      : ApiMetadata.fromJson(json['metadata'] as Map<String, dynamic>),
  status:
      $enumDecodeNullable(_$ApiStatusEnumMap, json['status']) ??
      ApiStatus.unknown,
  paginationInfo: json['paginationInfo'] == null
      ? null
      : PaginationInfo.fromJson(json['paginationInfo'] as Map<String, dynamic>),
  requestInfo: json['requestInfo'] == null
      ? null
      : RequestInfo.fromJson(json['requestInfo'] as Map<String, dynamic>),
);

Map<String, dynamic> _$$ApiResponseImplToJson<T>(
  _$ApiResponseImpl<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'data': _$nullableGenericToJson(instance.data, toJsonT),
  'isSuccess': instance.isSuccess,
  'error': instance.error?.toJson(),
  'metadata': instance.metadata?.toJson(),
  'status': _$ApiStatusEnumMap[instance.status]!,
  'paginationInfo': instance.paginationInfo?.toJson(),
  'requestInfo': instance.requestInfo?.toJson(),
};

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) => input == null ? null : fromJson(input);

const _$ApiStatusEnumMap = {
  ApiStatus.success: 'success',
  ApiStatus.error: 'error',
  ApiStatus.loading: 'loading',
  ApiStatus.unknown: 'unknown',
};

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) => input == null ? null : toJson(input);

_$ApiMetadataImpl _$$ApiMetadataImplFromJson(Map<String, dynamic> json) =>
    _$ApiMetadataImpl(
      timestamp: json['timestamp'] as String? ?? '',
      version: json['version'] as String? ?? '',
      environment: json['environment'] as String? ?? '',
      additionalData: json['additionalData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$ApiMetadataImplToJson(_$ApiMetadataImpl instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp,
      'version': instance.version,
      'environment': instance.environment,
      'additionalData': instance.additionalData,
    };

_$PaginationInfoImpl _$$PaginationInfoImplFromJson(Map<String, dynamic> json) =>
    _$PaginationInfoImpl(
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 0,
      totalPages: (json['totalPages'] as num?)?.toInt() ?? 0,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 0,
      totalItems: (json['totalItems'] as num?)?.toInt() ?? 0,
      hasNextPage: json['hasNextPage'] as bool? ?? false,
      hasPreviousPage: json['hasPreviousPage'] as bool? ?? false,
    );

Map<String, dynamic> _$$PaginationInfoImplToJson(
  _$PaginationInfoImpl instance,
) => <String, dynamic>{
  'currentPage': instance.currentPage,
  'totalPages': instance.totalPages,
  'pageSize': instance.pageSize,
  'totalItems': instance.totalItems,
  'hasNextPage': instance.hasNextPage,
  'hasPreviousPage': instance.hasPreviousPage,
};

_$RequestInfoImpl _$$RequestInfoImplFromJson(Map<String, dynamic> json) =>
    _$RequestInfoImpl(
      requestId: json['requestId'] as String? ?? '',
      endpoint: json['endpoint'] as String? ?? '',
      method: json['method'] as String? ?? '',
      statusCode: (json['statusCode'] as num?)?.toInt() ?? 0,
      responseTime: (json['responseTime'] as num?)?.toInt() ?? 0,
      headers: json['headers'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$RequestInfoImplToJson(_$RequestInfoImpl instance) =>
    <String, dynamic>{
      'requestId': instance.requestId,
      'endpoint': instance.endpoint,
      'method': instance.method,
      'statusCode': instance.statusCode,
      'responseTime': instance.responseTime,
      'headers': instance.headers,
    };
