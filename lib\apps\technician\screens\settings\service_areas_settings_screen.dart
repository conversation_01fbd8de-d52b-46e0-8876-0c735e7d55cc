import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:latlong2/latlong.dart' as latlong2;
import 'package:flutter_map/flutter_map.dart';
import 'package:shivish/apps/technician/widgets/technician_app_toolbar.dart';
import 'package:shivish/apps/technician/technician_routes.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/apps/technician/providers/service_areas_provider.dart';
import 'package:shivish/shared/widgets/maps/flutter_map_view.dart';

class ServiceAreasSettingsScreen extends ConsumerStatefulWidget {
  const ServiceAreasSettingsScreen({super.key});

  @override
  ConsumerState<ServiceAreasSettingsScreen> createState() =>
      _ServiceAreasSettingsScreenState();
}

class _ServiceAreasSettingsScreenState
    extends ConsumerState<ServiceAreasSettingsScreen> {
  final List<List<latlong2.LatLng>> _polygons = [];
  final List<latlong2.LatLng> _selectedPoints = [];
  bool _isLoading = false;

  // Controller for the map
  final mapKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _loadServiceAreas();
  }

  Future<void> _loadServiceAreas() async {
    final areas = await ref.read(serviceAreasProvider.future);
    if (mounted) {
      setState(() {
        _polygons.clear();
        _polygons.addAll(areas);
      });
    }
  }

  void _onMapTap(latlong2.LatLng point) {
    setState(() {
      _selectedPoints.add(point);
      _updatePolygon();
    });
  }

  void _updatePolygon() {
    if (_selectedPoints.length >= 3) {
      setState(() {
        _polygons.clear();
        _polygons.add(_selectedPoints.toList());
      });
    }
  }

  void _clearSelection() {
    setState(() {
      _selectedPoints.clear();
      _polygons.clear();
    });
  }

  Future<void> _saveServiceArea() async {
    if (_selectedPoints.length < 3) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content:
              Text('Please select at least 3 points to create a service area'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);
    try {
      await ref
          .read(serviceAreasProvider.notifier)
          .saveServiceArea(_selectedPoints);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Service area saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
        _clearSelection();
        _loadServiceAreas();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save service area: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(serviceAreasProvider, (previous, next) {
      next.whenOrNull(
        error: (error, _) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error.toString()),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
        },
      );
    });

    final serviceAreasState = ref.watch(serviceAreasProvider);
    _isLoading = serviceAreasState is AsyncLoading;

    return Scaffold(
      appBar: TechnicianAppToolbar.simple(
        title: 'Service Areas',
        fallbackRoute: TechnicianRoutes.settings,
      ),
      body: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                // Custom FlutterMapView with basic parameters
                Stack(
                  children: [
                    FlutterMapView(
                      key: mapKey,
                      pickupLocation: latlong2.LatLng(20.5937, 78.9629), // India center as a reference point
                      initialZoom: 5,
                      showCurrentLocation: true,
                      customPolylines: _polygons.map((polygon) =>
                        Polyline(
                          points: polygon,
                          color: Colors.blue,
                          strokeWidth: 3.0,
                        )
                      ).toList(),
                      customMarkers: _selectedPoints.map((point) =>
                        Marker(
                          point: point,
                          width: 20,
                          height: 20,
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Colors.blue,
                              shape: BoxShape.circle,
                            ),
                          ),
                        )
                      ).toList(),
                    ),
                    // Add a transparent overlay to capture tap events
                    Positioned.fill(
                      child: GestureDetector(
                        onTapUp: (details) {
                          // Convert tap position to map coordinates
                          final RenderBox renderBox = mapKey.currentContext!.findRenderObject() as RenderBox;
                          final localPosition = renderBox.globalToLocal(details.globalPosition);

                          // This is a simplified approach to convert screen coordinates to LatLng
                          // In a real app, you'd need to use the MapController to convert more accurately
                          final point = latlong2.LatLng(
                            20.5937 + (localPosition.dy / renderBox.size.height - 0.5) * 10,
                            78.9629 + (localPosition.dx / renderBox.size.width - 0.5) * 20,
                          );

                          _onMapTap(point);
                        },
                        behavior: HitTestBehavior.translucent,
                      ),
                    ),
                  ],
                ),

                // Instructions overlay
                Positioned(
                  top: 16,
                  left: 16,
                  right: 16,
                  child: Card(
                    color: Colors.white.withValues(alpha: 204), // 0.8 * 255 = 204
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'Tap on the map to create a service area. Select at least 3 points.',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: AppButton(
                    onPressed: _isLoading ? null : _clearSelection,
                    child: const Text('Clear Selection'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: AppButton(
                    onPressed: _isLoading ? null : _saveServiceArea,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text('Save Area'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
