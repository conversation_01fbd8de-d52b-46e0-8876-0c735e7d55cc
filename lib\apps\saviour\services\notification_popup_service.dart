import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/notification/notification_model.dart';
import '../../../shared/models/notification/notification_status.dart';
import '../../../shared/services/notification/notification_service.dart';
import '../widgets/notification_popup.dart' as popup_widget;
import '../providers/saviour_auth_provider.dart';

/// Service for handling notification popups in the saviour app
class SaviourNotificationPopupService {
  static SaviourNotificationPopupService? _instance;
  static SaviourNotificationPopupService get instance {
    _instance ??= SaviourNotificationPopupService._();
    return _instance!;
  }

  SaviourNotificationPopupService._();

  StreamSubscription<List<NotificationModel>>? _notificationSubscription;
  List<NotificationModel> _lastNotifications = [];
  BuildContext? _context;
  WidgetRef? _ref;

  /// Initialize the popup service
  void initialize(BuildContext context, WidgetRef ref) {
    _context = context;
    _ref = ref;
    _startListening();
  }

  /// Start listening for new notifications
  void _startListening() {
    if (_ref == null) return;

    final authState = _ref!.read(saviourAuthStateProvider);
    if (!authState.isAuthenticated || authState.user == null) return;

    final userId = authState.user!.id;

    final notificationService = NotificationService();

    _notificationSubscription?.cancel();
    _notificationSubscription = notificationService
        .getNotifications(userId)
        .listen(_handleNotificationsUpdate);
  }

  /// Handle notifications update
  void _handleNotificationsUpdate(List<NotificationModel> notifications) {
    if (_context == null || !_context!.mounted) return;

    // Find new unread notifications
    final newNotifications = notifications
        .where(
          (notification) =>
              notification.status == NotificationStatus.unread &&
              !_lastNotifications.any((old) => old.id == notification.id),
        )
        .toList();

    // Show popup for new notifications
    for (final notification in newNotifications) {
      _showNotificationPopup(notification);
    }

    _lastNotifications = notifications;
  }

  /// Show notification popup
  void _showNotificationPopup(NotificationModel notification) {
    if (_context == null || !_context!.mounted) return;

    popup_widget.SaviourNotificationPopupService.showNotificationPopup(
      _context!,
      notification,
      duration: const Duration(seconds: 5),
    );
  }

  /// Dispose the service
  void dispose() {
    _notificationSubscription?.cancel();
    _notificationSubscription = null;
    _context = null;
    _ref = null;
    _lastNotifications.clear();
  }

  /// Restart listening (useful when user changes)
  void restart() {
    dispose();
    if (_context != null && _ref != null) {
      initialize(_context!, _ref!);
    }
  }
}

/// Provider for the notification popup service
final saviourNotificationPopupServiceProvider =
    Provider<SaviourNotificationPopupService>((ref) {
      return SaviourNotificationPopupService.instance;
    });

/// Widget that initializes notification popup service
class SaviourNotificationPopupInitializer extends ConsumerStatefulWidget {
  final Widget child;

  const SaviourNotificationPopupInitializer({super.key, required this.child});

  @override
  ConsumerState<SaviourNotificationPopupInitializer> createState() =>
      _SaviourNotificationPopupInitializerState();
}

class _SaviourNotificationPopupInitializerState
    extends ConsumerState<SaviourNotificationPopupInitializer> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final service = ref.read(saviourNotificationPopupServiceProvider);
      service.initialize(context, ref);
    });
  }

  @override
  void dispose() {
    final service = ref.read(saviourNotificationPopupServiceProvider);
    service.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes to restart the service
    ref.listen(saviourAuthStateProvider, (previous, next) {
      if (previous?.user?.id != next.user?.id) {
        final service = ref.read(saviourNotificationPopupServiceProvider);
        service.restart();
      }
    });

    return widget.child;
  }
}
