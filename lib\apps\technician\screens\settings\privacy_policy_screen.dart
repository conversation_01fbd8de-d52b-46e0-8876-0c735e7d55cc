import 'package:flutter/material.dart';
import 'package:shivish/apps/technician/widgets/technician_app_toolbar.dart';
import 'package:shivish/apps/technician/technician_routes.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TechnicianAppToolbar.simple(
        title: 'Privacy Policy',
        fallbackRoute: TechnicianRoutes.settings,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Privacy Policy',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            Text(
              'Last updated: ${DateTime.now().toString().split(' ')[0]}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const Sized<PERSON><PERSON>(height: 24),
            _buildSection(
              context,
              '1. Information We Collect',
              'We collect information that you provide directly to us, including:\n\n'
                  '• Personal information (name, email, phone number)\n'
                  '• Account credentials\n'
                  '• Payment information\n'
                  '• Location data\n'
                  '• Usage data and analytics',
            ),
            _buildSection(
              context,
              '2. How We Use Your Information',
              'We use the information we collect to:\n\n'
                  '• Provide and maintain our services\n'
                  '• Process your transactions\n'
                  '• Send you important updates\n'
                  '• Improve our services\n'
                  '• Comply with legal obligations',
            ),
            _buildSection(
              context,
              '3. Information Sharing',
              'We may share your information with:\n\n'
                  '• Service providers\n'
                  '• Business partners\n'
                  '• Legal authorities when required\n'
                  '• Other users (with your consent)',
            ),
            _buildSection(
              context,
              '4. Data Security',
              'We implement appropriate security measures to protect your personal information, including:\n\n'
                  '• Encryption of data in transit and at rest\n'
                  '• Regular security assessments\n'
                  '• Access controls and authentication\n'
                  '• Secure data storage',
            ),
            _buildSection(
              context,
              '5. Your Rights',
              'You have the right to:\n\n'
                  '• Access your personal data\n'
                  '• Correct inaccurate data\n'
                  '• Request deletion of your data\n'
                  '• Opt-out of marketing communications\n'
                  '• Export your data',
            ),
            _buildSection(
              context,
              '6. Contact Us',
              'If you have any questions about this Privacy Policy, please contact us at:\n\n'
                  'Email: <EMAIL>\n'
                  'Phone: +****************',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 8),
        Text(
          content,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }
}
