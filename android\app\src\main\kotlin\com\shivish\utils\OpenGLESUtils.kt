package com.shivish.utils

import android.app.ActivityManager
import android.content.Context
import android.content.pm.ConfigurationInfo
import android.util.Log

/**
 * Utility class for OpenGL ES operations and checks
 */
object OpenGLESUtils {
    private const val TAG = "OpenGLESUtils"

    /**
     * Check if OpenGL ES 2.0 is supported on this device
     */
    fun isOpenGLES2Supported(context: Context): <PERSON>olean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val configurationInfo = activityManager.deviceConfigurationInfo
            configurationInfo.reqGlEsVersion >= 0x20000
        } catch (e: Exception) {
            Log.e(TAG, "Error checking OpenGL ES 2.0 support: ${e.message}")
            false
        }
    }

    /**
     * Check if OpenGL ES 3.0 is supported on this device
     */
    fun isOpenGLES3Supported(context: Context): <PERSON><PERSON>an {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val configurationInfo = activityManager.deviceConfigurationInfo
            configurationInfo.reqGlEsVersion >= 0x30000
        } catch (e: Exception) {
            Log.e(TAG, "Error checking OpenGL ES 3.0 support: ${e.message}")
            false
        }
    }

    /**
     * Get the OpenGL ES version string
     */
    fun getOpenGLESVersion(context: Context): String {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val configurationInfo = activityManager.deviceConfigurationInfo
            configurationInfo.glEsVersion
        } catch (e: Exception) {
            Log.e(TAG, "Error getting OpenGL ES version: ${e.message}")
            "Unknown"
        }
    }

    /**
     * Check if a specific OpenGL ES feature is supported
     */
    fun isFeatureSupported(feature: String): Boolean {
        return try {
            when (feature.uppercase()) {
                "GLES20" -> true // OpenGL ES 2.0 is widely supported
                "GLES30" -> true // OpenGL ES 3.0 is also widely supported on modern devices
                else -> {
                    Log.w(TAG, "Unknown feature: $feature")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking feature support for $feature: ${e.message}")
            false
        }
    }

    /**
     * Get device configuration info
     */
    fun getDeviceConfigurationInfo(context: Context): ConfigurationInfo? {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            activityManager.deviceConfigurationInfo
        } catch (e: Exception) {
            Log.e(TAG, "Error getting device configuration info: ${e.message}")
            null
        }
    }
}
