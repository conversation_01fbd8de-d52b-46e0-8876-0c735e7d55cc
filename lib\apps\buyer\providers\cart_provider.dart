import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../repositories/cart_repository_impl.dart';
import '../repositories/cart_repository.dart';
import '../notifiers/cart_notifier.dart';
import '../states/cart_state.dart';

/// Cart repository provider
final cartRepositoryProvider = Provider<CartRepository>((ref) {
  return CartRepositoryImpl();
});

/// Cart provider
final cartProvider = StateNotifierProvider<CartNotifier, CartState>((ref) {
  final repository = ref.watch(cartRepositoryProvider);
  return CartNotifier(repository);
});
