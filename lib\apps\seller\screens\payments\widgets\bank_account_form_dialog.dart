import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/domain/models/payment_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/payment_cubit.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';

class BankAccountFormDialog extends StatefulWidget {
  final BankAccountModel? account;

  const BankAccountFormDialog({
    super.key,
    this.account,
  });

  @override
  State<BankAccountFormDialog> createState() => _BankAccountFormDialogState();
}

class _BankAccountFormDialogState extends State<BankAccountFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _accountNumberController;
  late final TextEditingController _bankNameController;
  late final TextEditingController _accountHolderNameController;
  late final TextEditingController _ifscCodeController;

  @override
  void initState() {
    super.initState();
    _accountNumberController = TextEditingController(
      text: widget.account?.accountNumber,
    );
    _bankNameController = TextEditingController(
      text: widget.account?.bankName,
    );
    _accountHolderNameController = TextEditingController(
      text: widget.account?.accountHolderName,
    );
    _ifscCodeController = TextEditingController(
      text: widget.account?.ifscCode,
    );
  }

  @override
  void dispose() {
    _accountNumberController.dispose();
    _bankNameController.dispose();
    _accountHolderNameController.dispose();
    _ifscCodeController.dispose();
    super.dispose();
  }

  void _submit() {
    if (!_formKey.currentState!.validate()) return;

    final account = BankAccountModel(
      id: widget.account?.id ??
          DateTime.now().millisecondsSinceEpoch.toString(),
      accountNumber: _accountNumberController.text,
      bankName: _bankNameController.text,
      accountHolderName: _accountHolderNameController.text,
      ifscCode: _ifscCodeController.text,
      isVerified: widget.account?.isVerified ?? false,
      isPrimary: widget.account?.isPrimary ?? false,
      verifiedAt: widget.account?.verifiedAt,
    );

    if (widget.account == null) {
      context.read<PaymentCubit>().addBankAccount(account);
    } else {
      context.read<PaymentCubit>().updateBankAccount(account);
    }

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                widget.account == null
                    ? 'Add Bank Account'
                    : 'Edit Bank Account',
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _accountNumberController,
                label: 'Account Number',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter account number';
                  }
                  if (value.length < 8) {
                    return 'Account number must be at least 8 digits';
                  }
                  return null;
                },
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _bankNameController,
                label: 'Bank Name',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter bank name';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.words,
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _accountHolderNameController,
                label: 'Account Holder Name',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter account holder name';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.words,
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _ifscCodeController,
                label: 'IFSC Code',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter IFSC code';
                  }
                  if (!RegExp(r'^[A-Z]{4}0[A-Z0-9]{6}$').hasMatch(value)) {
                    return 'Please enter a valid IFSC code';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.characters,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _submit,
                    child: Text(
                      widget.account == null ? 'Add Account' : 'Save Changes',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
