import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/apps/seller/domain/models/schedule_model.dart';

part 'store_model.freezed.dart';
part 'store_model.g.dart';

@freezed
sealed class StoreModel with _$StoreModel {
  const factory StoreModel({
    required String id,
    required String name,
    required String description,
    required String address,
    required String phone,
    required String email,
    String? website,
    String? logoUrl,
    @Default([]) List<String> categories,
    @Default(false) bool isVerified,
    DateTime? verifiedAt,
    @Default(false) bool isDeleted,
    required DateTime createdAt,
    required DateTime updatedAt,
    ScheduleModel? schedule,
  }) = _StoreModel;

  factory StoreModel.fromJson(Map<String, dynamic> json) =>
      _$StoreModelFromJson(json);
}
