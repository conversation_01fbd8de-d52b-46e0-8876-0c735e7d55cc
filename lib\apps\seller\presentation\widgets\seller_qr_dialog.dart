import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/application/seller_provider.dart';
import 'package:shivish/shared/models/seller.dart';
import 'package:shivish/shared/services/payments/upi_verification_service.dart';
import 'package:shivish/shared/utils/widget_to_image_utils.dart';
import 'seller_qr_code.dart';

class SellerQRDialog extends ConsumerStatefulWidget {
  final Seller seller;

  const SellerQRDialog({
    super.key,
    required this.seller,
  });

  @override
  ConsumerState<SellerQRDialog> createState() => _SellerQRDialogState();
}

class _SellerQRDialogState extends ConsumerState<SellerQRDialog> {
  final GlobalKey _qrKey = GlobalKey();
  bool _isDownloading = false;
  bool _isAddingUpiId = false;
  bool _isSavingUpiId = false;
  bool _isVerifyingUpiId = false;
  UpiVerificationResult? _verificationResult;
  final TextEditingController _upiIdController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _upiIdController.dispose();
    super.dispose();
  }

  Future<void> _downloadQRCode() async {
    if (_isDownloading) return;

    setState(() {
      _isDownloading = true;
    });

    try {
      final fileName =
          '${widget.seller.businessName.replaceAll(' ', '_')}_qr_code.png';
      final filePath =
          await WidgetToImageUtils.saveWidgetAsImage(_qrKey, fileName);

      if (filePath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('QR code saved to: $filePath'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'VIEW',
              onPressed: () {
                // Open the file with the default viewer
                // This would require a platform-specific implementation
                // For simplicity, we'll just show a message
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('File saved in app documents directory'),
                  ),
                );
              },
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save QR code'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving QR code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  Future<void> _shareQRCode() async {
    try {
      await WidgetToImageUtils.shareWidgetAsImage(
        _qrKey,
        '${widget.seller.businessName.replaceAll(' ', '_')}_qr_code.png',
        subject: 'QR Code for ${widget.seller.businessName}',
        text: 'Check out ${widget.seller.businessName} on our app!\n'
            'Business Category: ${widget.seller.category.displayName}\n'
            'Rating: ${widget.seller.rating} (${widget.seller.totalReviews} reviews)',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing QR code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  bool _hasUpiId(Seller seller) {
    final paymentSettings = seller.paymentSettings;
    if (paymentSettings.containsKey('upiSettings')) {
      final upiId = paymentSettings['upiSettings']?['upiId'] as String?;
      return upiId != null && upiId.isNotEmpty;
    }
    return false;
  }

  /// Verifies the UPI ID before saving
  Future<void> _verifyUpiId() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isVerifyingUpiId = true;
      _verificationResult = null;
    });

    try {
      final upiId = _upiIdController.text.trim();
      final result = await UpiVerificationService.verifyUpiId(upiId);

      if (mounted) {
        setState(() {
          _verificationResult = result;
          _isVerifyingUpiId = false;
        });

        // If verification is successful, proceed to save
        if (result.isValid) {
          await _saveUpiId();
        } else {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isVerifyingUpiId = false;
          _verificationResult = UpiVerificationResult(
            isValid: false,
            message: 'Verification failed: ${e.toString()}',
            status: UpiVerificationStatus.serviceError,
          );
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error verifying UPI ID: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveUpiId() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSavingUpiId = true;
    });

    try {
      // Use the widget.seller directly to avoid provider issues
      final seller = widget.seller;

      // Create a copy of the existing payment settings
      final paymentSettings = Map<String, dynamic>.from(seller.paymentSettings);

      // Update the UPI settings
      paymentSettings['upiSettings'] = {
        'upiId': _upiIdController.text.trim(),
        'isVerified': false,
        'isEnabled': true,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // Create an updated seller with the new payment settings
      final updatedSeller = seller.copyWith(
        paymentSettings: paymentSettings,
        updatedAt: DateTime.now(),
      );

      // Use Future.microtask to update the seller after the build is complete
      Future.microtask(() async {
        try {
          // Update the seller in the database
          await ref.read(sellerProvider.notifier).updateSeller(updatedSeller);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('UPI ID saved successfully'),
                backgroundColor: Colors.green,
              ),
            );

            // Close the dialog after successful save
            Navigator.of(context).pop();
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error saving UPI ID: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      });

      // Close the UPI ID input form
      if (mounted) {
        setState(() {
          _isAddingUpiId = false;
          _isSavingUpiId = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving UPI ID: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isSavingUpiId = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use the seller passed to the widget instead of watching the provider
    // This avoids modifying the provider during build
    final seller = widget.seller;

    // Check if UPI ID is available
    final hasUpiId = _hasUpiId(seller);

    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF4CAF50), // SHIVISH green
              Color(0xFF2E7D32), // Darker green
              Color(0xFF1B5E20), // Even darker green for double shade effect
            ],
            stops: [0.0, 0.6, 1.0], // Control the gradient distribution
          ),
          boxShadow: [
            const BoxShadow(
              color: Color(0x4D000000), // 30% opacity black
              blurRadius: 15,
              offset: Offset(0, 8),
            ),
          ],
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // SHIVISH Logo and Company Name
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/images/flavors/shivish.png',
                    width: 40,
                    height: 40,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'SHIVISH',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 1.5,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Title and Business Name
              Text(
                'Scan to Pay',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                widget.seller.businessName,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                    ),
              ),
              const SizedBox(height: 24),

              // QR Code or UPI ID Missing Message
              if (hasUpiId)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: const [
                      BoxShadow(
                        color: Color(0x33000000), // 20% opacity black
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                    border: Border.all(
                      color: const Color(0x4D4CAF50), // 30% opacity green
                      width: 2,
                    ),
                  ),
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      // Payment QR Code
                      SellerQRCode(
                        seller: seller,
                        repaintBoundaryKey: _qrKey,
                      ),

                      // Payment apps icons
                      const SizedBox(height: 16),
                      Column(
                        children: [
                          const Text(
                            'Supports',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.black54,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Google Pay
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Color(0x1A000000), // 10% opacity
                                      blurRadius: 4,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Container(
                                        color: Colors.white,
                                        width: 16,
                                        height: 16,
                                        child: const Center(
                                          child: Text(
                                            'G',
                                            style: TextStyle(
                                              color: Colors.blue,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    const Text(
                                      'Pay',
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 8),

                              // PhonePe
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.indigo.shade50,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Color(0x1A000000), // 10% opacity
                                      blurRadius: 4,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Container(
                                        color: Colors.indigo,
                                        width: 16,
                                        height: 16,
                                        child: const Center(
                                          child: Text(
                                            'P',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    const Text(
                                      'PhonePe',
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.indigo,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 8),

                              // CRED
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.black87,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Color(0x1A000000), // 10% opacity
                                      blurRadius: 4,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: const Text(
                                  'CRED',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'All UPI apps supported',
                            style: TextStyle(
                              fontSize: 10,
                              fontStyle: FontStyle.italic,
                              color: Colors.black45,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              else
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: _isAddingUpiId
                      ? Form(
                          key: _formKey,
                          child: Column(
                            children: [
                              const Text(
                                'Add UPI ID',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _upiIdController,
                                decoration: InputDecoration(
                                  labelText: 'UPI ID',
                                  hintText: 'yourname@upi',
                                  border: const OutlineInputBorder(),
                                  prefixIcon: const Icon(Icons.payment),
                                  suffixIcon: _verificationResult != null
                                      ? Icon(
                                          _verificationResult!.isValid
                                              ? Icons.check_circle
                                              : Icons.error,
                                          color: _verificationResult!.isValid
                                              ? Colors.green
                                              : Colors.red,
                                        )
                                      : null,
                                  helperText: _verificationResult != null
                                      ? _verificationResult!.message
                                      : 'Enter your UPI ID (e.g., yourname@upi)',
                                  helperStyle: TextStyle(
                                    color: _verificationResult != null
                                        ? (_verificationResult!.isValid
                                            ? Colors.green
                                            : Colors.red)
                                        : Colors.grey,
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter your UPI ID';
                                  }
                                  if (!value.contains('@')) {
                                    return 'Please enter a valid UPI ID';
                                  }
                                  return null;
                                },
                                onChanged: (_) {
                                  // Clear verification result when the user types
                                  if (_verificationResult != null) {
                                    setState(() {
                                      _verificationResult = null;
                                    });
                                  }
                                },
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  TextButton(
                                    onPressed: _isSavingUpiId
                                        ? null
                                        : () {
                                            setState(() {
                                              _isAddingUpiId = false;
                                            });
                                          },
                                    child: const Text('Cancel'),
                                  ),
                                  ElevatedButton(
                                    onPressed:
                                        (_isSavingUpiId || _isVerifyingUpiId)
                                            ? null
                                            : _verifyUpiId,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF4CAF50),
                                      foregroundColor: Colors.white,
                                    ),
                                    child: _isVerifyingUpiId
                                        ? Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: const [
                                              SizedBox(
                                                height: 16,
                                                width: 16,
                                                child:
                                                    CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                  valueColor:
                                                      AlwaysStoppedAnimation<
                                                          Color>(Colors.white),
                                                ),
                                              ),
                                              SizedBox(width: 8),
                                              Text('Verifying...'),
                                            ],
                                          )
                                        : _isSavingUpiId
                                            ? Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: const [
                                                  SizedBox(
                                                    height: 16,
                                                    width: 16,
                                                    child:
                                                        CircularProgressIndicator(
                                                      strokeWidth: 2,
                                                      valueColor:
                                                          AlwaysStoppedAnimation<
                                                                  Color>(
                                                              Colors.white),
                                                    ),
                                                  ),
                                                  SizedBox(width: 8),
                                                  Text('Saving...'),
                                                ],
                                              )
                                            : const Text(
                                                'Verify & Save UPI ID'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        )
                      : Column(
                          children: [
                            const Icon(
                              Icons.payment_outlined,
                              size: 64,
                              color: Colors.grey,
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'UPI ID Not Set Up',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Please add your UPI ID to generate a QR code for payments.',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () {
                                setState(() {
                                  _isAddingUpiId = true;
                                });
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF4CAF50),
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('Add UPI ID'),
                            ),
                          ],
                        ),
                ),
              const SizedBox(height: 24),

              // Action Buttons
              if (hasUpiId)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton.icon(
                      onPressed: _shareQRCode,
                      icon: const Icon(Icons.share, color: Colors.white),
                      label: const Text('Share',
                          style: TextStyle(color: Colors.white)),
                    ),
                    TextButton.icon(
                      onPressed: _downloadQRCode,
                      icon: _isDownloading
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(Icons.download, color: Colors.white),
                      label:
                          const Text('', style: TextStyle(color: Colors.white)),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      icon: const Icon(Icons.close, color: Colors.white),
                      label: const Text('Close',
                          style: TextStyle(color: Colors.white)),
                    ),
                  ],
                )
              else
                TextButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  icon: const Icon(Icons.close, color: Colors.white),
                  label: const Text('Close',
                      style: TextStyle(color: Colors.white)),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
