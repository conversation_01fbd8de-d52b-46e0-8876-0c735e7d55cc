import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:math' as math;
import '../bloc/auth/auth_bloc.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';

class TechnicianRequestsScreen extends StatefulWidget {
  const TechnicianRequestsScreen({super.key});

  @override
  State<TechnicianRequestsScreen> createState() => _TechnicianRequestsScreenState();
}

class _TechnicianRequestsScreenState extends State<TechnicianRequestsScreen> {
  final _logger = getLogger('TechnicianRequestsScreen');
  final _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
  bool _isLoading = false;
  List<Map<String, dynamic>> _technicianRequests = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeDatabase();
  }

  Future<void> _initializeDatabase() async {
    try {
      await _databaseService.initialize();
      await _loadTechnicianRequests();
    } catch (e) {
      _logger.severe('Error initializing database: $e');
      setState(() {
        _errorMessage = 'Error initializing database: $e';
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _databaseService.close();
    super.dispose();
  }

  Future<void> _loadTechnicianRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get all technicians to see what's in the collection
      final allTechnicians = await _databaseService.getAll('technicians');
      _logger.info('Found ${allTechnicians.length} total technicians');

      // Print the first few technicians to see their data structure
      for (int i = 0; i < math.min(5, allTechnicians.length); i++) {
        final technician = allTechnicians[i];
        _logger.info('Technician ${i+1}: ${technician['id']}');
        _logger.info('Data: $technician');
      }

      // Get pending technicians
      final pendingTechnicians = await _databaseService.getAll(
        'technicians',
        where: 'verificationStatus = ?',
        whereParams: ['pending'],
      );

      _logger.info('Found ${pendingTechnicians.length} technicians with verificationStatus=pending');

      setState(() {
        _technicianRequests = pendingTechnicians;
        _isLoading = false;
      });
    } catch (e) {
      _logger.severe('Error loading technician requests: $e');
      setState(() {
        _errorMessage = 'Error loading technician requests: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _approveTechnician(String requestId) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get request document
      final requestData = await _databaseService.find('technicians', requestId);
      if (requestData == null) {
        throw Exception('Technician request not found');
      }

      final userId = requestData['id'] as String; // Use id instead of uid

      // Get the current admin user ID
      String? adminId;
      if (mounted) {
        final authState = context.read<AuthBloc>().state;
        if (authState is AuthAuthenticatedState) {
          adminId = authState.user.id;
        }
      }

      final now = DateTime.now().toIso8601String();

      // Update user document to grant technician access
      await _databaseService.update('users', userId, {
        'isVerified': true,
        'isActive': true,
        'verificationStatus': 'approved',
        'updatedAt': now,
      });

      // Update request status
      await _databaseService.update('technicians', requestId, {
        'verificationStatus': 'approved',
        'isActive': true,
        'approvedAt': now,
        'approvedBy': adminId,
        'updatedAt': now,
      });

      // Reload the list
      await _loadTechnicianRequests();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Technician approved successfully')),
        );
      }
    } catch (e) {
      _logger.severe('Error approving technician: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Error approving technician: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _rejectTechnician(String requestId) async {
    if (!mounted) return;

    final reasonController = TextEditingController();

    final reason = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Technician'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejection:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(reasonController.text),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (reason == null || reason.isEmpty || !mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get the current admin user ID
      String? adminId;
      if (mounted) {
        final authState = context.read<AuthBloc>().state;
        if (authState is AuthAuthenticatedState) {
          adminId = authState.user.id;
        }
      }

      // Update request status
      final now = DateTime.now().toIso8601String();
      await _databaseService.update('technicians', requestId, {
        'verificationStatus': 'rejected',
        'rejectionReason': reason,
        'rejectedAt': now,
        'rejectedBy': adminId,
        'updatedAt': now,
      });

      // Reload the list
      await _loadTechnicianRequests();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Technician rejected successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error rejecting technician: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Technician Requests'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTechnicianRequests,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadTechnicianRequests,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _technicianRequests.isEmpty
                  ? const Center(
                      child: Text('No pending technician requests'),
                    )
                  : ListView.builder(
                      itemCount: _technicianRequests.length,
                      itemBuilder: (context, index) {
                        final request = _technicianRequests[index];
                        return Card(
                          margin: const EdgeInsets.all(8),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  request['name'] ?? 'Unknown',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text('Email: ${request['email'] ?? 'N/A'}'),
                                if (request['phone'] != null)
                                  Text('Phone: ${request['phone']}'),
                                const SizedBox(height: 8),
                                Text(
                                  'Requested: ${_formatTimestamp(request['createdAt'])}',
                                  style: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                                if (request['documents'] != null && (request['documents'] as List).isNotEmpty)
                                  _buildDocumentsList(request['documents'] as List),
                                const SizedBox(height: 16),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton(
                                      onPressed: () => _rejectTechnician(request['id']),
                                      child: const Text('Reject'),
                                    ),
                                    const SizedBox(width: 8),
                                    ElevatedButton(
                                      onPressed: () => _approveTechnician(request['id']),
                                      child: const Text('Approve'),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
    );
  }

  Widget _buildDocumentsList(List documents) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        const Text(
          'Documents:',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...documents.map((doc) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            children: [
              const Icon(Icons.description, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${doc['type']}: ${doc['name'] ?? 'Document'}',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'N/A';

    if (timestamp is String) {
      final date = DateTime.tryParse(timestamp);
      if (date != null) {
        return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
      }
    }

    return 'N/A';
  }
}
