import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:just_audio/just_audio.dart';
import 'package:video_player/video_player.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import '../../../../shared/services/storage/google_drive_service.dart';
import '../../../../shared/services/storage/storage_config_service.dart';
import '../../../../shared/utils/logger.dart';

final mediaProvider = StateNotifierProvider<MediaNotifier, MediaState>((ref) {
  return MediaNotifier();
});

class MediaState {
  final List<MediaItem> audioFiles;
  final List<MediaItem> videoFiles;
  final String searchQuery;
  final String filterType;
  final bool isLoading;
  final String? error;

  MediaState({
    this.audioFiles = const [],
    this.videoFiles = const [],
    this.searchQuery = '',
    this.filterType = 'all',
    this.isLoading = false,
    this.error,
  });

  MediaState copyWith({
    List<MediaItem>? audioFiles,
    List<MediaItem>? videoFiles,
    String? searchQuery,
    String? filterType,
    bool? isLoading,
    String? error,
  }) {
    return MediaState(
      audioFiles: audioFiles ?? this.audioFiles,
      videoFiles: videoFiles ?? this.videoFiles,
      searchQuery: searchQuery ?? this.searchQuery,
      filterType: filterType ?? this.filterType,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

class MediaItem {
  final String id;
  final String title;
  final String url;
  final String type;
  final Duration duration;
  final String thumbnailUrl;
  final DateTime createdAt;

  MediaItem({
    required this.id,
    required this.title,
    required this.url,
    required this.type,
    required this.duration,
    required this.thumbnailUrl,
    required this.createdAt,
  });
}

class MediaNotifier extends StateNotifier<MediaState> {
  final _audioPlayer = AudioPlayer();
  VideoPlayerController? _videoPlayer;
  final GoogleDriveService _driveService = GoogleDriveService();
  final StorageConfigService _configService = StorageConfigService();
  final _logger = getLogger('MediaNotifier');
  bool _isInitialized = false;

  MediaNotifier() : super(MediaState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final isConfigured = await _configService.isStorageConfigured();
      if (!isConfigured) {
        state = state.copyWith(
          isLoading: false,
          error: 'Media storage not configured. Please contact admin.',
        );
        return;
      }

      await _driveService.initialize();
      _isInitialized = true;
      await _loadMedia();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize media service: $e',
      );
    }
  }

  Future<String> _generateVideoThumbnail(
    String videoUrl,
    String videoId,
  ) async {
    try {
      // Download video to temporary file
      final tempDir = await getTemporaryDirectory();
      final videoFile = File('${tempDir.path}/temp_video.mp4');

      // Download the file from Google Drive
      final videoBytes = await _driveService.downloadFile(videoId);
      if (videoBytes == null) {
        throw Exception('Failed to download video');
      }

      await videoFile.writeAsBytes(videoBytes);

      // Generate thumbnail
      final thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: videoFile.path,
        imageFormat: ImageFormat.JPEG,
        quality: 75,
      );

      if (thumbnailPath == null) {
        throw Exception('Failed to generate thumbnail');
      }

      // Upload thumbnail to Google Drive
      final thumbnailFile = File(thumbnailPath);
      final thumbnailId = await _driveService.uploadFile(
        thumbnailFile.readAsBytesSync(),
        '${path.basenameWithoutExtension(videoUrl)}_thumbnail.jpg',
        'image/jpeg',
        parentFolderId:
            'thumbnails', // This should be a folder ID in Google Drive
      );

      final thumbnailUrl = await _driveService.getFileUrl(thumbnailId);

      // Clean up temporary files
      await videoFile.delete();
      await thumbnailFile.delete();

      return thumbnailUrl;
    } catch (e) {
      // If thumbnail generation fails, return a placeholder
      return 'https://via.placeholder.com/320x180.png?text=Video';
    }
  }

  Future<void> _loadMedia() async {
    if (!_isInitialized) {
      state = state.copyWith(
        isLoading: false,
        error: 'Media service not initialized',
      );
      return;
    }

    state = state.copyWith(isLoading: true, error: null);
    try {
      // Load media files from Google Drive
      final mediaFiles = await _driveService.listFiles(
        mimeTypes: ['audio/mpeg', 'video/mp4'],
      );

      final audioFiles = <MediaItem>[];
      final videoFiles = <MediaItem>[];

      for (var file in mediaFiles) {
        final fileId = file['id'] as String;
        final fileName = file['name'] as String;
        final mimeType = file['mimeType'] as String;
        final createdTime = DateTime.parse(file['createdTime'] as String);

        // Get file URL
        final downloadUrl = await _driveService.getFileUrl(fileId);
        final extension = path.extension(fileName).toLowerCase();

        // Get duration and thumbnail
        Duration duration = const Duration(seconds: 0);
        String thumbnailUrl = '';

        if (mimeType == 'audio/mpeg' || extension == '.mp3') {
          // For audio files, we'll set a default duration
          // In a real app, you'd need to download and analyze the file
          duration = const Duration(minutes: 3); // Default duration

          // Try to get actual duration if possible
          try {
            final audioPlayer = AudioPlayer();
            await audioPlayer.setUrl(downloadUrl);
            await Future.delayed(
              const Duration(seconds: 1),
            ); // Give it time to load
            final durationMs = audioPlayer.duration?.inMilliseconds ?? 0;
            if (durationMs > 0) {
              duration = Duration(milliseconds: durationMs);
            }
            await audioPlayer.dispose();
          } catch (e) {
            // If we can't get the duration, use the default
            _logger.warning('Could not get audio duration: $e');
          }
        } else if (mimeType == 'video/mp4' || extension == '.mp4') {
          // For video files, we'll set a default duration
          duration = const Duration(minutes: 1); // Default duration

          // Try to get actual duration if possible
          try {
            final videoPlayer = VideoPlayerController.networkUrl(
              Uri.parse(downloadUrl),
            );
            await videoPlayer.initialize();
            duration = videoPlayer.value.duration;
            await videoPlayer.dispose();
          } catch (e) {
            // If we can't get the duration, use the default
            _logger.warning('Could not get video duration: $e');
          }

          // Generate and upload thumbnail
          thumbnailUrl = await _generateVideoThumbnail(downloadUrl, fileId);
        }

        final mediaItem = MediaItem(
          id: fileId,
          title: path.basenameWithoutExtension(fileName),
          url: downloadUrl,
          type: mimeType.startsWith('audio/') ? 'audio' : 'video',
          duration: duration,
          thumbnailUrl: thumbnailUrl,
          createdAt: createdTime,
        );

        if (mediaItem.type == 'audio') {
          audioFiles.add(mediaItem);
        } else {
          videoFiles.add(mediaItem);
        }
      }

      state = state.copyWith(
        isLoading: false,
        audioFiles: audioFiles,
        videoFiles: videoFiles,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load media: $e',
      );
    }
  }

  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  void setFilterType(String type) {
    state = state.copyWith(filterType: type);
  }

  Future<void> uploadMedia(PlatformFile file) async {
    if (!_isInitialized) {
      state = state.copyWith(error: 'Media service not initialized');
      return;
    }

    state = state.copyWith(isLoading: true, error: null);
    try {
      // Determine MIME type
      String mimeType;
      if (file.extension?.toLowerCase() == 'mp3') {
        mimeType = 'audio/mpeg';
      } else if (file.extension?.toLowerCase() == 'mp4') {
        mimeType = 'video/mp4';
      } else {
        throw Exception('Unsupported file type: ${file.extension}');
      }

      // Upload file to Google Drive
      final fileId = await _driveService.uploadFile(
        file.bytes!,
        file.name,
        mimeType,
        parentFolderId: 'media', // This should be a folder ID in Google Drive
      );

      final downloadUrl = await _driveService.getFileUrl(fileId);

      // Get duration and thumbnail
      Duration duration = const Duration(seconds: 0);
      String thumbnailUrl = '';

      if (file.extension?.toLowerCase() == 'mp3') {
        // Set default duration for audio
        duration = const Duration(minutes: 3);

        // Try to get actual duration
        try {
          final audioPlayer = AudioPlayer();
          await audioPlayer.setUrl(downloadUrl);
          await Future.delayed(
            const Duration(seconds: 1),
          ); // Give it time to load
          final durationMs = audioPlayer.duration?.inMilliseconds ?? 0;
          if (durationMs > 0) {
            duration = Duration(milliseconds: durationMs);
          }
          await audioPlayer.dispose();
        } catch (e) {
          _logger.warning('Could not get audio duration: $e');
        }
      } else if (file.extension?.toLowerCase() == 'mp4') {
        // Set default duration for video
        duration = const Duration(minutes: 1);

        // Try to get actual duration
        try {
          final videoPlayer = VideoPlayerController.networkUrl(
            Uri.parse(downloadUrl),
          );
          await videoPlayer.initialize();
          duration = videoPlayer.value.duration;
          await videoPlayer.dispose();
        } catch (e) {
          _logger.warning('Could not get video duration: $e');
        }

        // Generate and upload thumbnail
        thumbnailUrl = await _generateVideoThumbnail(downloadUrl, fileId);
      }

      final newItem = MediaItem(
        id: fileId,
        title: file.name,
        url: downloadUrl,
        type: file.extension?.toLowerCase() == 'mp3' ? 'audio' : 'video',
        duration: duration,
        thumbnailUrl: thumbnailUrl,
        createdAt: DateTime.now(),
      );

      if (newItem.type == 'audio') {
        state = state.copyWith(
          audioFiles: [...state.audioFiles, newItem],
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          videoFiles: [...state.videoFiles, newItem],
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to upload media: $e',
      );
    }
  }

  Future<void> playAudio(String url) async {
    try {
      await _audioPlayer.setUrl(url);
      await _audioPlayer.play();
    } catch (e) {
      state = state.copyWith(error: 'Failed to play audio: $e');
    }
  }

  Future<void> playVideo(String url) async {
    try {
      // Dispose previous video player if exists
      await _videoPlayer?.dispose();

      // Create new video player for the URL
      _videoPlayer = VideoPlayerController.networkUrl(Uri.parse(url));
      await _videoPlayer!.initialize();
      await _videoPlayer!.play();
    } catch (e) {
      state = state.copyWith(error: 'Failed to play video: $e');
    }
  }

  Future<void> addToPlaylist(MediaItem item, String playlistId) async {
    if (!_isInitialized) {
      state = state.copyWith(error: 'Media service not initialized');
      return;
    }

    try {
      // In a real implementation, you would store playlists in a database or local storage
      // For now, we'll just show a success message
      state = state.copyWith(error: null);
    } catch (e) {
      state = state.copyWith(error: 'Failed to add to playlist: $e');
    }
  }

  Future<void> shareMedia(MediaItem item) async {
    try {
      // Share media URL
      await SharePlus.instance.share(
        ShareParams(
          text: 'Check out this ${item.type}: ${item.title}\n${item.url}',
        ),
      );
      state = state.copyWith(error: null);
    } catch (e) {
      state = state.copyWith(error: 'Failed to share media: $e');
    }
  }

  Future<void> deleteMedia(MediaItem item) async {
    if (!_isInitialized) {
      state = state.copyWith(error: 'Media service not initialized');
      return;
    }

    try {
      // Delete from Google Drive
      await _driveService.deleteFile(item.id);

      // Delete thumbnail if it's a video and has a thumbnail
      if (item.type == 'video' && item.thumbnailUrl.isNotEmpty) {
        // Extract thumbnail ID from URL or find it by name
        // This depends on how you've implemented the Google Drive service
        final thumbnailId = await _driveService.findFileByName(
          '${path.basenameWithoutExtension(item.title)}_thumbnail.jpg',
        );
        if (thumbnailId != null) {
          await _driveService.deleteFile(thumbnailId);
        }
      }

      // Update state
      if (item.type == 'audio') {
        state = state.copyWith(
          audioFiles: state.audioFiles
              .where((file) => file.id != item.id)
              .toList(),
          error: null,
        );
      } else {
        state = state.copyWith(
          videoFiles: state.videoFiles
              .where((file) => file.id != item.id)
              .toList(),
          error: null,
        );
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to delete media: $e');
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    _videoPlayer?.dispose();
    super.dispose();
  }
}
