import 'package:flutter/material.dart';
import '../../../../../shared/models/inventory_model.dart';

class InventoryList extends StatelessWidget {
  final List<InventoryModel> inventory;
  final Function(String, int) onStockUpdate;
  final Function(String, int) onAddStock;
  final Function(String, int) onRemoveStock;
  final Function(String, bool) onSetLowStockAlert;

  const InventoryList({
    super.key,
    required this.inventory,
    required this.onStockUpdate,
    required this.onAddStock,
    required this.onRemoveStock,
    required this.onSetLowStockAlert,
  });

  @override
  Widget build(BuildContext context) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final item = inventory[index];
          final isLowStock = item.currentStock <= item.minimumStock;
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              title: Text('Product ID: ${item.productId}'),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Stock: ${item.currentStock} / ${item.maximumStock}'),
                  Text('Min Stock: ${item.minimumStock}'),
                  Text('Unit Price: ${item.unitPrice} ${item.unit}'),
                ],
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.remove),
                    onPressed: () => onRemoveStock(item.id, 1),
                  ),
                  IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: () => onAddStock(item.id, 1),
                  ),
                  IconButton(
                    icon: Icon(
                      isLowStock
                          ? Icons.warning_amber
                          : Icons.warning_amber_outlined,
                      color: isLowStock ? Colors.orange : null,
                    ),
                    onPressed: () => onSetLowStockAlert(
                      item.id,
                      !isLowStock,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        childCount: inventory.length,
      ),
    );
  }
}
