// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'alarm_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AlarmModel {

 String get id; String get userId; AlarmType get type; DateTime get time; bool get isEnabled; String get toneId; double get volume; bool get vibrate; AlarmRepeat get repeat; List<int> get repeatDays;// 0-6 for Sunday-Saturday
 int get snoozeDuration;// in minutes
 int get maxSnoozeCount; String get label; DateTime get createdAt; DateTime get updatedAt; int get currentSnoozeCount; bool get isSnoozed; DateTime? get snoozeTime;// AI Alarm specific fields
 bool get isLearning; Map<String, dynamic> get usagePatterns; int get smartWakeUpOffset;// in minutes
// Custom Alarm specific fields
 bool get isCustom; Map<String, dynamic> get customSettings;
/// Create a copy of AlarmModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AlarmModelCopyWith<AlarmModel> get copyWith => _$AlarmModelCopyWithImpl<AlarmModel>(this as AlarmModel, _$identity);

  /// Serializes this AlarmModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AlarmModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.time, time) || other.time == time)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.toneId, toneId) || other.toneId == toneId)&&(identical(other.volume, volume) || other.volume == volume)&&(identical(other.vibrate, vibrate) || other.vibrate == vibrate)&&(identical(other.repeat, repeat) || other.repeat == repeat)&&const DeepCollectionEquality().equals(other.repeatDays, repeatDays)&&(identical(other.snoozeDuration, snoozeDuration) || other.snoozeDuration == snoozeDuration)&&(identical(other.maxSnoozeCount, maxSnoozeCount) || other.maxSnoozeCount == maxSnoozeCount)&&(identical(other.label, label) || other.label == label)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.currentSnoozeCount, currentSnoozeCount) || other.currentSnoozeCount == currentSnoozeCount)&&(identical(other.isSnoozed, isSnoozed) || other.isSnoozed == isSnoozed)&&(identical(other.snoozeTime, snoozeTime) || other.snoozeTime == snoozeTime)&&(identical(other.isLearning, isLearning) || other.isLearning == isLearning)&&const DeepCollectionEquality().equals(other.usagePatterns, usagePatterns)&&(identical(other.smartWakeUpOffset, smartWakeUpOffset) || other.smartWakeUpOffset == smartWakeUpOffset)&&(identical(other.isCustom, isCustom) || other.isCustom == isCustom)&&const DeepCollectionEquality().equals(other.customSettings, customSettings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,type,time,isEnabled,toneId,volume,vibrate,repeat,const DeepCollectionEquality().hash(repeatDays),snoozeDuration,maxSnoozeCount,label,createdAt,updatedAt,currentSnoozeCount,isSnoozed,snoozeTime,isLearning,const DeepCollectionEquality().hash(usagePatterns),smartWakeUpOffset,isCustom,const DeepCollectionEquality().hash(customSettings)]);

@override
String toString() {
  return 'AlarmModel(id: $id, userId: $userId, type: $type, time: $time, isEnabled: $isEnabled, toneId: $toneId, volume: $volume, vibrate: $vibrate, repeat: $repeat, repeatDays: $repeatDays, snoozeDuration: $snoozeDuration, maxSnoozeCount: $maxSnoozeCount, label: $label, createdAt: $createdAt, updatedAt: $updatedAt, currentSnoozeCount: $currentSnoozeCount, isSnoozed: $isSnoozed, snoozeTime: $snoozeTime, isLearning: $isLearning, usagePatterns: $usagePatterns, smartWakeUpOffset: $smartWakeUpOffset, isCustom: $isCustom, customSettings: $customSettings)';
}


}

/// @nodoc
abstract mixin class $AlarmModelCopyWith<$Res>  {
  factory $AlarmModelCopyWith(AlarmModel value, $Res Function(AlarmModel) _then) = _$AlarmModelCopyWithImpl;
@useResult
$Res call({
 String id, String userId, AlarmType type, DateTime time, bool isEnabled, String toneId, double volume, bool vibrate, AlarmRepeat repeat, List<int> repeatDays, int snoozeDuration, int maxSnoozeCount, String label, DateTime createdAt, DateTime updatedAt, int currentSnoozeCount, bool isSnoozed, DateTime? snoozeTime, bool isLearning, Map<String, dynamic> usagePatterns, int smartWakeUpOffset, bool isCustom, Map<String, dynamic> customSettings
});




}
/// @nodoc
class _$AlarmModelCopyWithImpl<$Res>
    implements $AlarmModelCopyWith<$Res> {
  _$AlarmModelCopyWithImpl(this._self, this._then);

  final AlarmModel _self;
  final $Res Function(AlarmModel) _then;

/// Create a copy of AlarmModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? type = null,Object? time = null,Object? isEnabled = null,Object? toneId = null,Object? volume = null,Object? vibrate = null,Object? repeat = null,Object? repeatDays = null,Object? snoozeDuration = null,Object? maxSnoozeCount = null,Object? label = null,Object? createdAt = null,Object? updatedAt = null,Object? currentSnoozeCount = null,Object? isSnoozed = null,Object? snoozeTime = freezed,Object? isLearning = null,Object? usagePatterns = null,Object? smartWakeUpOffset = null,Object? isCustom = null,Object? customSettings = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as AlarmType,time: null == time ? _self.time : time // ignore: cast_nullable_to_non_nullable
as DateTime,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,toneId: null == toneId ? _self.toneId : toneId // ignore: cast_nullable_to_non_nullable
as String,volume: null == volume ? _self.volume : volume // ignore: cast_nullable_to_non_nullable
as double,vibrate: null == vibrate ? _self.vibrate : vibrate // ignore: cast_nullable_to_non_nullable
as bool,repeat: null == repeat ? _self.repeat : repeat // ignore: cast_nullable_to_non_nullable
as AlarmRepeat,repeatDays: null == repeatDays ? _self.repeatDays : repeatDays // ignore: cast_nullable_to_non_nullable
as List<int>,snoozeDuration: null == snoozeDuration ? _self.snoozeDuration : snoozeDuration // ignore: cast_nullable_to_non_nullable
as int,maxSnoozeCount: null == maxSnoozeCount ? _self.maxSnoozeCount : maxSnoozeCount // ignore: cast_nullable_to_non_nullable
as int,label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,currentSnoozeCount: null == currentSnoozeCount ? _self.currentSnoozeCount : currentSnoozeCount // ignore: cast_nullable_to_non_nullable
as int,isSnoozed: null == isSnoozed ? _self.isSnoozed : isSnoozed // ignore: cast_nullable_to_non_nullable
as bool,snoozeTime: freezed == snoozeTime ? _self.snoozeTime : snoozeTime // ignore: cast_nullable_to_non_nullable
as DateTime?,isLearning: null == isLearning ? _self.isLearning : isLearning // ignore: cast_nullable_to_non_nullable
as bool,usagePatterns: null == usagePatterns ? _self.usagePatterns : usagePatterns // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,smartWakeUpOffset: null == smartWakeUpOffset ? _self.smartWakeUpOffset : smartWakeUpOffset // ignore: cast_nullable_to_non_nullable
as int,isCustom: null == isCustom ? _self.isCustom : isCustom // ignore: cast_nullable_to_non_nullable
as bool,customSettings: null == customSettings ? _self.customSettings : customSettings // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}

}


/// Adds pattern-matching-related methods to [AlarmModel].
extension AlarmModelPatterns on AlarmModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AlarmModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AlarmModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AlarmModel value)  $default,){
final _that = this;
switch (_that) {
case _AlarmModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AlarmModel value)?  $default,){
final _that = this;
switch (_that) {
case _AlarmModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  AlarmType type,  DateTime time,  bool isEnabled,  String toneId,  double volume,  bool vibrate,  AlarmRepeat repeat,  List<int> repeatDays,  int snoozeDuration,  int maxSnoozeCount,  String label,  DateTime createdAt,  DateTime updatedAt,  int currentSnoozeCount,  bool isSnoozed,  DateTime? snoozeTime,  bool isLearning,  Map<String, dynamic> usagePatterns,  int smartWakeUpOffset,  bool isCustom,  Map<String, dynamic> customSettings)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AlarmModel() when $default != null:
return $default(_that.id,_that.userId,_that.type,_that.time,_that.isEnabled,_that.toneId,_that.volume,_that.vibrate,_that.repeat,_that.repeatDays,_that.snoozeDuration,_that.maxSnoozeCount,_that.label,_that.createdAt,_that.updatedAt,_that.currentSnoozeCount,_that.isSnoozed,_that.snoozeTime,_that.isLearning,_that.usagePatterns,_that.smartWakeUpOffset,_that.isCustom,_that.customSettings);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  AlarmType type,  DateTime time,  bool isEnabled,  String toneId,  double volume,  bool vibrate,  AlarmRepeat repeat,  List<int> repeatDays,  int snoozeDuration,  int maxSnoozeCount,  String label,  DateTime createdAt,  DateTime updatedAt,  int currentSnoozeCount,  bool isSnoozed,  DateTime? snoozeTime,  bool isLearning,  Map<String, dynamic> usagePatterns,  int smartWakeUpOffset,  bool isCustom,  Map<String, dynamic> customSettings)  $default,) {final _that = this;
switch (_that) {
case _AlarmModel():
return $default(_that.id,_that.userId,_that.type,_that.time,_that.isEnabled,_that.toneId,_that.volume,_that.vibrate,_that.repeat,_that.repeatDays,_that.snoozeDuration,_that.maxSnoozeCount,_that.label,_that.createdAt,_that.updatedAt,_that.currentSnoozeCount,_that.isSnoozed,_that.snoozeTime,_that.isLearning,_that.usagePatterns,_that.smartWakeUpOffset,_that.isCustom,_that.customSettings);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  AlarmType type,  DateTime time,  bool isEnabled,  String toneId,  double volume,  bool vibrate,  AlarmRepeat repeat,  List<int> repeatDays,  int snoozeDuration,  int maxSnoozeCount,  String label,  DateTime createdAt,  DateTime updatedAt,  int currentSnoozeCount,  bool isSnoozed,  DateTime? snoozeTime,  bool isLearning,  Map<String, dynamic> usagePatterns,  int smartWakeUpOffset,  bool isCustom,  Map<String, dynamic> customSettings)?  $default,) {final _that = this;
switch (_that) {
case _AlarmModel() when $default != null:
return $default(_that.id,_that.userId,_that.type,_that.time,_that.isEnabled,_that.toneId,_that.volume,_that.vibrate,_that.repeat,_that.repeatDays,_that.snoozeDuration,_that.maxSnoozeCount,_that.label,_that.createdAt,_that.updatedAt,_that.currentSnoozeCount,_that.isSnoozed,_that.snoozeTime,_that.isLearning,_that.usagePatterns,_that.smartWakeUpOffset,_that.isCustom,_that.customSettings);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AlarmModel implements AlarmModel {
  const _AlarmModel({required this.id, required this.userId, required this.type, required this.time, required this.isEnabled, required this.toneId, required this.volume, required this.vibrate, required this.repeat, required final  List<int> repeatDays, required this.snoozeDuration, required this.maxSnoozeCount, required this.label, required this.createdAt, required this.updatedAt, this.currentSnoozeCount = 0, this.isSnoozed = false, this.snoozeTime, this.isLearning = false, final  Map<String, dynamic> usagePatterns = const {}, this.smartWakeUpOffset = 0, this.isCustom = false, final  Map<String, dynamic> customSettings = const {}}): _repeatDays = repeatDays,_usagePatterns = usagePatterns,_customSettings = customSettings;
  factory _AlarmModel.fromJson(Map<String, dynamic> json) => _$AlarmModelFromJson(json);

@override final  String id;
@override final  String userId;
@override final  AlarmType type;
@override final  DateTime time;
@override final  bool isEnabled;
@override final  String toneId;
@override final  double volume;
@override final  bool vibrate;
@override final  AlarmRepeat repeat;
 final  List<int> _repeatDays;
@override List<int> get repeatDays {
  if (_repeatDays is EqualUnmodifiableListView) return _repeatDays;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_repeatDays);
}

// 0-6 for Sunday-Saturday
@override final  int snoozeDuration;
// in minutes
@override final  int maxSnoozeCount;
@override final  String label;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  int currentSnoozeCount;
@override@JsonKey() final  bool isSnoozed;
@override final  DateTime? snoozeTime;
// AI Alarm specific fields
@override@JsonKey() final  bool isLearning;
 final  Map<String, dynamic> _usagePatterns;
@override@JsonKey() Map<String, dynamic> get usagePatterns {
  if (_usagePatterns is EqualUnmodifiableMapView) return _usagePatterns;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_usagePatterns);
}

@override@JsonKey() final  int smartWakeUpOffset;
// in minutes
// Custom Alarm specific fields
@override@JsonKey() final  bool isCustom;
 final  Map<String, dynamic> _customSettings;
@override@JsonKey() Map<String, dynamic> get customSettings {
  if (_customSettings is EqualUnmodifiableMapView) return _customSettings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_customSettings);
}


/// Create a copy of AlarmModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AlarmModelCopyWith<_AlarmModel> get copyWith => __$AlarmModelCopyWithImpl<_AlarmModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AlarmModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AlarmModel&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.time, time) || other.time == time)&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.toneId, toneId) || other.toneId == toneId)&&(identical(other.volume, volume) || other.volume == volume)&&(identical(other.vibrate, vibrate) || other.vibrate == vibrate)&&(identical(other.repeat, repeat) || other.repeat == repeat)&&const DeepCollectionEquality().equals(other._repeatDays, _repeatDays)&&(identical(other.snoozeDuration, snoozeDuration) || other.snoozeDuration == snoozeDuration)&&(identical(other.maxSnoozeCount, maxSnoozeCount) || other.maxSnoozeCount == maxSnoozeCount)&&(identical(other.label, label) || other.label == label)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.currentSnoozeCount, currentSnoozeCount) || other.currentSnoozeCount == currentSnoozeCount)&&(identical(other.isSnoozed, isSnoozed) || other.isSnoozed == isSnoozed)&&(identical(other.snoozeTime, snoozeTime) || other.snoozeTime == snoozeTime)&&(identical(other.isLearning, isLearning) || other.isLearning == isLearning)&&const DeepCollectionEquality().equals(other._usagePatterns, _usagePatterns)&&(identical(other.smartWakeUpOffset, smartWakeUpOffset) || other.smartWakeUpOffset == smartWakeUpOffset)&&(identical(other.isCustom, isCustom) || other.isCustom == isCustom)&&const DeepCollectionEquality().equals(other._customSettings, _customSettings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,type,time,isEnabled,toneId,volume,vibrate,repeat,const DeepCollectionEquality().hash(_repeatDays),snoozeDuration,maxSnoozeCount,label,createdAt,updatedAt,currentSnoozeCount,isSnoozed,snoozeTime,isLearning,const DeepCollectionEquality().hash(_usagePatterns),smartWakeUpOffset,isCustom,const DeepCollectionEquality().hash(_customSettings)]);

@override
String toString() {
  return 'AlarmModel(id: $id, userId: $userId, type: $type, time: $time, isEnabled: $isEnabled, toneId: $toneId, volume: $volume, vibrate: $vibrate, repeat: $repeat, repeatDays: $repeatDays, snoozeDuration: $snoozeDuration, maxSnoozeCount: $maxSnoozeCount, label: $label, createdAt: $createdAt, updatedAt: $updatedAt, currentSnoozeCount: $currentSnoozeCount, isSnoozed: $isSnoozed, snoozeTime: $snoozeTime, isLearning: $isLearning, usagePatterns: $usagePatterns, smartWakeUpOffset: $smartWakeUpOffset, isCustom: $isCustom, customSettings: $customSettings)';
}


}

/// @nodoc
abstract mixin class _$AlarmModelCopyWith<$Res> implements $AlarmModelCopyWith<$Res> {
  factory _$AlarmModelCopyWith(_AlarmModel value, $Res Function(_AlarmModel) _then) = __$AlarmModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, AlarmType type, DateTime time, bool isEnabled, String toneId, double volume, bool vibrate, AlarmRepeat repeat, List<int> repeatDays, int snoozeDuration, int maxSnoozeCount, String label, DateTime createdAt, DateTime updatedAt, int currentSnoozeCount, bool isSnoozed, DateTime? snoozeTime, bool isLearning, Map<String, dynamic> usagePatterns, int smartWakeUpOffset, bool isCustom, Map<String, dynamic> customSettings
});




}
/// @nodoc
class __$AlarmModelCopyWithImpl<$Res>
    implements _$AlarmModelCopyWith<$Res> {
  __$AlarmModelCopyWithImpl(this._self, this._then);

  final _AlarmModel _self;
  final $Res Function(_AlarmModel) _then;

/// Create a copy of AlarmModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? type = null,Object? time = null,Object? isEnabled = null,Object? toneId = null,Object? volume = null,Object? vibrate = null,Object? repeat = null,Object? repeatDays = null,Object? snoozeDuration = null,Object? maxSnoozeCount = null,Object? label = null,Object? createdAt = null,Object? updatedAt = null,Object? currentSnoozeCount = null,Object? isSnoozed = null,Object? snoozeTime = freezed,Object? isLearning = null,Object? usagePatterns = null,Object? smartWakeUpOffset = null,Object? isCustom = null,Object? customSettings = null,}) {
  return _then(_AlarmModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as AlarmType,time: null == time ? _self.time : time // ignore: cast_nullable_to_non_nullable
as DateTime,isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,toneId: null == toneId ? _self.toneId : toneId // ignore: cast_nullable_to_non_nullable
as String,volume: null == volume ? _self.volume : volume // ignore: cast_nullable_to_non_nullable
as double,vibrate: null == vibrate ? _self.vibrate : vibrate // ignore: cast_nullable_to_non_nullable
as bool,repeat: null == repeat ? _self.repeat : repeat // ignore: cast_nullable_to_non_nullable
as AlarmRepeat,repeatDays: null == repeatDays ? _self._repeatDays : repeatDays // ignore: cast_nullable_to_non_nullable
as List<int>,snoozeDuration: null == snoozeDuration ? _self.snoozeDuration : snoozeDuration // ignore: cast_nullable_to_non_nullable
as int,maxSnoozeCount: null == maxSnoozeCount ? _self.maxSnoozeCount : maxSnoozeCount // ignore: cast_nullable_to_non_nullable
as int,label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,currentSnoozeCount: null == currentSnoozeCount ? _self.currentSnoozeCount : currentSnoozeCount // ignore: cast_nullable_to_non_nullable
as int,isSnoozed: null == isSnoozed ? _self.isSnoozed : isSnoozed // ignore: cast_nullable_to_non_nullable
as bool,snoozeTime: freezed == snoozeTime ? _self.snoozeTime : snoozeTime // ignore: cast_nullable_to_non_nullable
as DateTime?,isLearning: null == isLearning ? _self.isLearning : isLearning // ignore: cast_nullable_to_non_nullable
as bool,usagePatterns: null == usagePatterns ? _self._usagePatterns : usagePatterns // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,smartWakeUpOffset: null == smartWakeUpOffset ? _self.smartWakeUpOffset : smartWakeUpOffset // ignore: cast_nullable_to_non_nullable
as int,isCustom: null == isCustom ? _self.isCustom : isCustom // ignore: cast_nullable_to_non_nullable
as bool,customSettings: null == customSettings ? _self._customSettings : customSettings // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

// dart format on
