import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/voice_command_training.dart';

part 'voice_command_training_state.freezed.dart';

@freezed
sealed class VoiceCommandTrainingState with _$VoiceCommandTrainingState {
  const factory VoiceCommandTrainingState.initial() = Initial;
  const factory VoiceCommandTrainingState.loading() = Loading;
  const factory VoiceCommandTrainingState.loaded(
      List<VoiceCommandTraining> trainings) = Loaded;
  const factory VoiceCommandTrainingState.error(String message) = Error;
}
