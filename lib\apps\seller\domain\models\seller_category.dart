import 'package:freezed_annotation/freezed_annotation.dart';

enum SellerCategory {
  @JsonValue('farmer')
  farmer,

  @JsonValue('wholesaler')
  wholesaler,

  @JsonValue('retailer')
  retailer,

  @JsonValue('manufacturer')
  manufacturer,

  @JsonValue('distributor')
  distributor,

  @JsonValue('importer')
  importer,

  @JsonValue('exporter')
  exporter,

  @JsonValue('trader')
  trader,

  // Event Service Categories
  @JsonValue('event_venues')
  eventVenues,

  @JsonValue('event_organizers')
  eventOrganizers,

  @JsonValue('event_photography')
  eventPhotography,

  @JsonValue('event_decorations')
  eventDecorations,

  @JsonValue('event_catering')
  eventCatering,

  @JsonValue('event_entertainment')
  eventEntertainment,

  @JsonValue('event_transportation')
  eventTransportation,

  @JsonValue('event_invitations')
  eventInvitations,

  @JsonValue('event_makeup')
  eventMakeup,

  @JsonValue('event_equipment')
  eventEquipment,

  @JsonValue('other')
  other;

  String get displayName {
    switch (this) {
      case SellerCategory.farmer:
        return 'Farmer';
      case SellerCategory.wholesaler:
        return 'Wholesaler';
      case SellerCategory.retailer:
        return 'Retailer';
      case SellerCategory.manufacturer:
        return 'Manufacturer';
      case SellerCategory.distributor:
        return 'Distributor';
      case SellerCategory.importer:
        return 'Importer';
      case SellerCategory.exporter:
        return 'Exporter';
      case SellerCategory.trader:
        return 'Trader';
      case SellerCategory.eventVenues:
        return 'Function Halls & Venues';
      case SellerCategory.eventOrganizers:
        return 'Event Organizers';
      case SellerCategory.eventPhotography:
        return 'Photography & Videography';
      case SellerCategory.eventDecorations:
        return 'Decorations & Themes';
      case SellerCategory.eventCatering:
        return 'Catering Services';
      case SellerCategory.eventEntertainment:
        return 'Entertainment';
      case SellerCategory.eventTransportation:
        return 'Transportation';
      case SellerCategory.eventInvitations:
        return 'Invitations & Printing';
      case SellerCategory.eventMakeup:
        return 'Makeup & Styling';
      case SellerCategory.eventEquipment:
        return 'Equipment Rental';
      case SellerCategory.other:
        return 'Other';
    }
  }

  /// Check if this category is an event service category
  bool get isEventService {
    return [
      SellerCategory.eventVenues,
      SellerCategory.eventOrganizers,
      SellerCategory.eventPhotography,
      SellerCategory.eventDecorations,
      SellerCategory.eventCatering,
      SellerCategory.eventEntertainment,
      SellerCategory.eventTransportation,
      SellerCategory.eventInvitations,
      SellerCategory.eventMakeup,
      SellerCategory.eventEquipment,
    ].contains(this);
  }

  /// Check if this category is a traditional product seller category
  bool get isProductSeller {
    return [
      SellerCategory.farmer,
      SellerCategory.wholesaler,
      SellerCategory.retailer,
      SellerCategory.manufacturer,
      SellerCategory.distributor,
      SellerCategory.importer,
      SellerCategory.exporter,
      SellerCategory.trader,
    ].contains(this);
  }

  /// Get the description for this category
  String get description {
    switch (this) {
      case SellerCategory.farmer:
        return 'Agricultural products and fresh produce';
      case SellerCategory.wholesaler:
        return 'Bulk products for retailers';
      case SellerCategory.retailer:
        return 'Direct sales to consumers';
      case SellerCategory.manufacturer:
        return 'Manufacturing and production';
      case SellerCategory.distributor:
        return 'Product distribution services';
      case SellerCategory.importer:
        return 'International product imports';
      case SellerCategory.exporter:
        return 'International product exports';
      case SellerCategory.trader:
        return 'Product trading services';
      case SellerCategory.eventVenues:
        return 'Function halls, banquet halls, and event venues';
      case SellerCategory.eventOrganizers:
        return 'Complete event planning and management';
      case SellerCategory.eventPhotography:
        return 'Photography, videography, and documentation';
      case SellerCategory.eventDecorations:
        return 'Event decoration and theme services';
      case SellerCategory.eventCatering:
        return 'Food and beverage services';
      case SellerCategory.eventEntertainment:
        return 'Music, dance, and entertainment services';
      case SellerCategory.eventTransportation:
        return 'Guest transport and logistics';
      case SellerCategory.eventInvitations:
        return 'Invitation cards and printing services';
      case SellerCategory.eventMakeup:
        return 'Makeup, styling, and beauty services';
      case SellerCategory.eventEquipment:
        return 'Equipment rental and technical services';
      case SellerCategory.other:
        return 'Other business categories';
    }
  }

  /// Get all event service categories
  static List<SellerCategory> get eventServiceCategories {
    return [
      SellerCategory.eventVenues,
      SellerCategory.eventOrganizers,
      SellerCategory.eventPhotography,
      SellerCategory.eventDecorations,
      SellerCategory.eventCatering,
      SellerCategory.eventEntertainment,
      SellerCategory.eventTransportation,
      SellerCategory.eventInvitations,
      SellerCategory.eventMakeup,
      SellerCategory.eventEquipment,
    ];
  }

  /// Get all product seller categories
  static List<SellerCategory> get productSellerCategories {
    return [
      SellerCategory.farmer,
      SellerCategory.wholesaler,
      SellerCategory.retailer,
      SellerCategory.manufacturer,
      SellerCategory.distributor,
      SellerCategory.importer,
      SellerCategory.exporter,
      SellerCategory.trader,
    ];
  }

  /// Get available service types for this event service category
  List<String> get availableServiceTypes {
    switch (this) {
      case SellerCategory.eventVenues:
        return [
          'Function Halls',
          'Banquet Halls',
          'Outdoor Venues',
          'Resorts',
          'Community Centers',
          'Marriage Gardens',
          'Convention Centers',
          'Farmhouses',
          'Beach Venues',
          'Heritage Venues',
        ];
      case SellerCategory.eventOrganizers:
        return [
          'Wedding Planners',
          'Corporate Events',
          'Birthday Parties',
          'Religious Events',
          'Cultural Events',
          'Anniversary Celebrations',
          'Baby Showers',
          'Engagement Ceremonies',
          'Reception Planning',
          'Destination Weddings',
        ];
      case SellerCategory.eventPhotography:
        return [
          'Wedding Photography',
          'Event Photography',
          'Videography',
          'Drone Services',
          'Photo Booths',
          'Candid Photography',
          'Traditional Photography',
          'Pre-wedding Shoots',
          'Maternity Shoots',
          'Baby Photography',
        ];
      case SellerCategory.eventDecorations:
        return [
          'Floral Decoration',
          'Stage Decoration',
          'Lighting',
          'Balloon Decoration',
          'Theme Decoration',
          'Mandap Decoration',
          'Car Decoration',
          'Entrance Decoration',
          'Table Decoration',
          'Backdrop Design',
        ];
      case SellerCategory.eventCatering:
        return [
          'Vegetarian Catering',
          'Non-Vegetarian Catering',
          'South Indian Cuisine',
          'North Indian Cuisine',
          'Chinese Cuisine',
          'Continental Cuisine',
          'Live Counters',
          'Dessert Catering',
          'Beverage Services',
          'Traditional Sweets',
        ];
      case SellerCategory.eventEntertainment:
        return [
          'DJs',
          'Live Bands',
          'Classical Music',
          'Dance Performers',
          'Anchors/MCs',
          'Comedians',
          'Magic Shows',
          'Folk Artists',
          'Orchestra',
          'Karaoke Services',
        ];
      case SellerCategory.eventTransportation:
        return [
          'Car Rental',
          'Bus Rental',
          'Decorated Cars',
          'Luxury Cars',
          'Guest Transport',
          'Vintage Cars',
          'Horse Carriages',
          'Bike Rentals',
          'Tempo Travellers',
          'Mini Buses',
        ];
      case SellerCategory.eventInvitations:
        return [
          'Wedding Cards',
          'Digital Invites',
          'Banners',
          'Flex Printing',
          'Photo Albums',
          'Save the Date Cards',
          'Thank You Cards',
          'Menu Cards',
          'Signage',
          'Custom Stationery',
        ];
      case SellerCategory.eventMakeup:
        return [
          'Bridal Makeup',
          'Hair Styling',
          'Mehendi Artists',
          'Nail Art',
          'Grooming Services',
          'Saree Draping',
          'Groom Makeup',
          'Party Makeup',
          'Traditional Styling',
          'Beauty Treatments',
        ];
      case SellerCategory.eventEquipment:
        return [
          'Sound Systems',
          'Projectors',
          'Furniture Rental',
          'Tents & Canopies',
          'Generators',
          'Lighting Equipment',
          'Stage Setup',
          'Crockery & Cutlery',
          'Air Conditioning',
          'Security Equipment',
        ];
      default:
        return [];
    }
  }
}
