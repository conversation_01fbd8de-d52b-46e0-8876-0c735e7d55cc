// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'priest_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$<PERSON>vent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PriestEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PriestEvent()';
}


}

/// @nodoc
class $PriestEventCopyWith<$Res>  {
$PriestEventCopyWith(PriestEvent _, $Res Function(PriestEvent) __);
}


/// Adds pattern-matching-related methods to [PriestEvent].
extension PriestEventPatterns on PriestEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _LoadPriests value)?  loadPriests,TResult Function( _LoadMorePriests value)?  loadMorePriests,TResult Function( _UpdatePriestStatus value)?  updatePriestStatus,TResult Function( _UpdatePriestVerification value)?  updatePriestVerification,TResult Function( _DeletePriest value)?  deletePriest,TResult Function( _StartRealtimeUpdates value)?  startRealtimeUpdates,TResult Function( _StopRealtimeUpdates value)?  stopRealtimeUpdates,required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LoadPriests() when loadPriests != null:
return loadPriests(_that);case _LoadMorePriests() when loadMorePriests != null:
return loadMorePriests(_that);case _UpdatePriestStatus() when updatePriestStatus != null:
return updatePriestStatus(_that);case _UpdatePriestVerification() when updatePriestVerification != null:
return updatePriestVerification(_that);case _DeletePriest() when deletePriest != null:
return deletePriest(_that);case _StartRealtimeUpdates() when startRealtimeUpdates != null:
return startRealtimeUpdates(_that);case _StopRealtimeUpdates() when stopRealtimeUpdates != null:
return stopRealtimeUpdates(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _LoadPriests value)  loadPriests,required TResult Function( _LoadMorePriests value)  loadMorePriests,required TResult Function( _UpdatePriestStatus value)  updatePriestStatus,required TResult Function( _UpdatePriestVerification value)  updatePriestVerification,required TResult Function( _DeletePriest value)  deletePriest,required TResult Function( _StartRealtimeUpdates value)  startRealtimeUpdates,required TResult Function( _StopRealtimeUpdates value)  stopRealtimeUpdates,}){
final _that = this;
switch (_that) {
case _LoadPriests():
return loadPriests(_that);case _LoadMorePriests():
return loadMorePriests(_that);case _UpdatePriestStatus():
return updatePriestStatus(_that);case _UpdatePriestVerification():
return updatePriestVerification(_that);case _DeletePriest():
return deletePriest(_that);case _StartRealtimeUpdates():
return startRealtimeUpdates(_that);case _StopRealtimeUpdates():
return stopRealtimeUpdates(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _LoadPriests value)?  loadPriests,TResult? Function( _LoadMorePriests value)?  loadMorePriests,TResult? Function( _UpdatePriestStatus value)?  updatePriestStatus,TResult? Function( _UpdatePriestVerification value)?  updatePriestVerification,TResult? Function( _DeletePriest value)?  deletePriest,TResult? Function( _StartRealtimeUpdates value)?  startRealtimeUpdates,TResult? Function( _StopRealtimeUpdates value)?  stopRealtimeUpdates,}){
final _that = this;
switch (_that) {
case _LoadPriests() when loadPriests != null:
return loadPriests(_that);case _LoadMorePriests() when loadMorePriests != null:
return loadMorePriests(_that);case _UpdatePriestStatus() when updatePriestStatus != null:
return updatePriestStatus(_that);case _UpdatePriestVerification() when updatePriestVerification != null:
return updatePriestVerification(_that);case _DeletePriest() when deletePriest != null:
return deletePriest(_that);case _StartRealtimeUpdates() when startRealtimeUpdates != null:
return startRealtimeUpdates(_that);case _StopRealtimeUpdates() when stopRealtimeUpdates != null:
return stopRealtimeUpdates(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  loadPriests,TResult Function()?  loadMorePriests,TResult Function( String id,  bool isActive)?  updatePriestStatus,TResult Function( String id,  bool isVerified,  String verificationStatus,  String? verificationNotes)?  updatePriestVerification,TResult Function( String id)?  deletePriest,TResult Function()?  startRealtimeUpdates,TResult Function()?  stopRealtimeUpdates,required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LoadPriests() when loadPriests != null:
return loadPriests();case _LoadMorePriests() when loadMorePriests != null:
return loadMorePriests();case _UpdatePriestStatus() when updatePriestStatus != null:
return updatePriestStatus(_that.id,_that.isActive);case _UpdatePriestVerification() when updatePriestVerification != null:
return updatePriestVerification(_that.id,_that.isVerified,_that.verificationStatus,_that.verificationNotes);case _DeletePriest() when deletePriest != null:
return deletePriest(_that.id);case _StartRealtimeUpdates() when startRealtimeUpdates != null:
return startRealtimeUpdates();case _StopRealtimeUpdates() when stopRealtimeUpdates != null:
return stopRealtimeUpdates();case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  loadPriests,required TResult Function()  loadMorePriests,required TResult Function( String id,  bool isActive)  updatePriestStatus,required TResult Function( String id,  bool isVerified,  String verificationStatus,  String? verificationNotes)  updatePriestVerification,required TResult Function( String id)  deletePriest,required TResult Function()  startRealtimeUpdates,required TResult Function()  stopRealtimeUpdates,}) {final _that = this;
switch (_that) {
case _LoadPriests():
return loadPriests();case _LoadMorePriests():
return loadMorePriests();case _UpdatePriestStatus():
return updatePriestStatus(_that.id,_that.isActive);case _UpdatePriestVerification():
return updatePriestVerification(_that.id,_that.isVerified,_that.verificationStatus,_that.verificationNotes);case _DeletePriest():
return deletePriest(_that.id);case _StartRealtimeUpdates():
return startRealtimeUpdates();case _StopRealtimeUpdates():
return stopRealtimeUpdates();}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  loadPriests,TResult? Function()?  loadMorePriests,TResult? Function( String id,  bool isActive)?  updatePriestStatus,TResult? Function( String id,  bool isVerified,  String verificationStatus,  String? verificationNotes)?  updatePriestVerification,TResult? Function( String id)?  deletePriest,TResult? Function()?  startRealtimeUpdates,TResult? Function()?  stopRealtimeUpdates,}) {final _that = this;
switch (_that) {
case _LoadPriests() when loadPriests != null:
return loadPriests();case _LoadMorePriests() when loadMorePriests != null:
return loadMorePriests();case _UpdatePriestStatus() when updatePriestStatus != null:
return updatePriestStatus(_that.id,_that.isActive);case _UpdatePriestVerification() when updatePriestVerification != null:
return updatePriestVerification(_that.id,_that.isVerified,_that.verificationStatus,_that.verificationNotes);case _DeletePriest() when deletePriest != null:
return deletePriest(_that.id);case _StartRealtimeUpdates() when startRealtimeUpdates != null:
return startRealtimeUpdates();case _StopRealtimeUpdates() when stopRealtimeUpdates != null:
return stopRealtimeUpdates();case _:
  return null;

}
}

}

/// @nodoc


class _LoadPriests implements PriestEvent {
  const _LoadPriests();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadPriests);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PriestEvent.loadPriests()';
}


}




/// @nodoc


class _LoadMorePriests implements PriestEvent {
  const _LoadMorePriests();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadMorePriests);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PriestEvent.loadMorePriests()';
}


}




/// @nodoc


class _UpdatePriestStatus implements PriestEvent {
  const _UpdatePriestStatus({required this.id, required this.isActive});
  

 final  String id;
 final  bool isActive;

/// Create a copy of PriestEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdatePriestStatusCopyWith<_UpdatePriestStatus> get copyWith => __$UpdatePriestStatusCopyWithImpl<_UpdatePriestStatus>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdatePriestStatus&&(identical(other.id, id) || other.id == id)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}


@override
int get hashCode => Object.hash(runtimeType,id,isActive);

@override
String toString() {
  return 'PriestEvent.updatePriestStatus(id: $id, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class _$UpdatePriestStatusCopyWith<$Res> implements $PriestEventCopyWith<$Res> {
  factory _$UpdatePriestStatusCopyWith(_UpdatePriestStatus value, $Res Function(_UpdatePriestStatus) _then) = __$UpdatePriestStatusCopyWithImpl;
@useResult
$Res call({
 String id, bool isActive
});




}
/// @nodoc
class __$UpdatePriestStatusCopyWithImpl<$Res>
    implements _$UpdatePriestStatusCopyWith<$Res> {
  __$UpdatePriestStatusCopyWithImpl(this._self, this._then);

  final _UpdatePriestStatus _self;
  final $Res Function(_UpdatePriestStatus) _then;

/// Create a copy of PriestEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? isActive = null,}) {
  return _then(_UpdatePriestStatus(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class _UpdatePriestVerification implements PriestEvent {
  const _UpdatePriestVerification({required this.id, required this.isVerified, required this.verificationStatus, this.verificationNotes});
  

 final  String id;
 final  bool isVerified;
 final  String verificationStatus;
 final  String? verificationNotes;

/// Create a copy of PriestEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdatePriestVerificationCopyWith<_UpdatePriestVerification> get copyWith => __$UpdatePriestVerificationCopyWithImpl<_UpdatePriestVerification>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdatePriestVerification&&(identical(other.id, id) || other.id == id)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.verificationStatus, verificationStatus) || other.verificationStatus == verificationStatus)&&(identical(other.verificationNotes, verificationNotes) || other.verificationNotes == verificationNotes));
}


@override
int get hashCode => Object.hash(runtimeType,id,isVerified,verificationStatus,verificationNotes);

@override
String toString() {
  return 'PriestEvent.updatePriestVerification(id: $id, isVerified: $isVerified, verificationStatus: $verificationStatus, verificationNotes: $verificationNotes)';
}


}

/// @nodoc
abstract mixin class _$UpdatePriestVerificationCopyWith<$Res> implements $PriestEventCopyWith<$Res> {
  factory _$UpdatePriestVerificationCopyWith(_UpdatePriestVerification value, $Res Function(_UpdatePriestVerification) _then) = __$UpdatePriestVerificationCopyWithImpl;
@useResult
$Res call({
 String id, bool isVerified, String verificationStatus, String? verificationNotes
});




}
/// @nodoc
class __$UpdatePriestVerificationCopyWithImpl<$Res>
    implements _$UpdatePriestVerificationCopyWith<$Res> {
  __$UpdatePriestVerificationCopyWithImpl(this._self, this._then);

  final _UpdatePriestVerification _self;
  final $Res Function(_UpdatePriestVerification) _then;

/// Create a copy of PriestEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? isVerified = null,Object? verificationStatus = null,Object? verificationNotes = freezed,}) {
  return _then(_UpdatePriestVerification(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,verificationStatus: null == verificationStatus ? _self.verificationStatus : verificationStatus // ignore: cast_nullable_to_non_nullable
as String,verificationNotes: freezed == verificationNotes ? _self.verificationNotes : verificationNotes // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _DeletePriest implements PriestEvent {
  const _DeletePriest({required this.id});
  

 final  String id;

/// Create a copy of PriestEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DeletePriestCopyWith<_DeletePriest> get copyWith => __$DeletePriestCopyWithImpl<_DeletePriest>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DeletePriest&&(identical(other.id, id) || other.id == id));
}


@override
int get hashCode => Object.hash(runtimeType,id);

@override
String toString() {
  return 'PriestEvent.deletePriest(id: $id)';
}


}

/// @nodoc
abstract mixin class _$DeletePriestCopyWith<$Res> implements $PriestEventCopyWith<$Res> {
  factory _$DeletePriestCopyWith(_DeletePriest value, $Res Function(_DeletePriest) _then) = __$DeletePriestCopyWithImpl;
@useResult
$Res call({
 String id
});




}
/// @nodoc
class __$DeletePriestCopyWithImpl<$Res>
    implements _$DeletePriestCopyWith<$Res> {
  __$DeletePriestCopyWithImpl(this._self, this._then);

  final _DeletePriest _self;
  final $Res Function(_DeletePriest) _then;

/// Create a copy of PriestEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,}) {
  return _then(_DeletePriest(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _StartRealtimeUpdates implements PriestEvent {
  const _StartRealtimeUpdates();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StartRealtimeUpdates);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PriestEvent.startRealtimeUpdates()';
}


}




/// @nodoc


class _StopRealtimeUpdates implements PriestEvent {
  const _StopRealtimeUpdates();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StopRealtimeUpdates);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PriestEvent.stopRealtimeUpdates()';
}


}




/// @nodoc
mixin _$PriestState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PriestState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PriestState()';
}


}

/// @nodoc
class $PriestStateCopyWith<$Res>  {
$PriestStateCopyWith(PriestState _, $Res Function(PriestState) __);
}


/// Adds pattern-matching-related methods to [PriestState].
extension PriestStatePatterns on PriestState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _Initial value)?  initial,TResult Function( _Loading value)?  loading,TResult Function( _Loaded value)?  loaded,TResult Function( _Error value)?  error,required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Initial() when initial != null:
return initial(_that);case _Loading() when loading != null:
return loading(_that);case _Loaded() when loaded != null:
return loaded(_that);case _Error() when error != null:
return error(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _Initial value)  initial,required TResult Function( _Loading value)  loading,required TResult Function( _Loaded value)  loaded,required TResult Function( _Error value)  error,}){
final _that = this;
switch (_that) {
case _Initial():
return initial(_that);case _Loading():
return loading(_that);case _Loaded():
return loaded(_that);case _Error():
return error(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _Initial value)?  initial,TResult? Function( _Loading value)?  loading,TResult? Function( _Loaded value)?  loaded,TResult? Function( _Error value)?  error,}){
final _that = this;
switch (_that) {
case _Initial() when initial != null:
return initial(_that);case _Loading() when loading != null:
return loading(_that);case _Loaded() when loaded != null:
return loaded(_that);case _Error() when error != null:
return error(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  initial,TResult Function()?  loading,TResult Function( List<Priest> priests)?  loaded,TResult Function( String message)?  error,required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Initial() when initial != null:
return initial();case _Loading() when loading != null:
return loading();case _Loaded() when loaded != null:
return loaded(_that.priests);case _Error() when error != null:
return error(_that.message);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  initial,required TResult Function()  loading,required TResult Function( List<Priest> priests)  loaded,required TResult Function( String message)  error,}) {final _that = this;
switch (_that) {
case _Initial():
return initial();case _Loading():
return loading();case _Loaded():
return loaded(_that.priests);case _Error():
return error(_that.message);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  initial,TResult? Function()?  loading,TResult? Function( List<Priest> priests)?  loaded,TResult? Function( String message)?  error,}) {final _that = this;
switch (_that) {
case _Initial() when initial != null:
return initial();case _Loading() when loading != null:
return loading();case _Loaded() when loaded != null:
return loaded(_that.priests);case _Error() when error != null:
return error(_that.message);case _:
  return null;

}
}

}

/// @nodoc


class _Initial implements PriestState {
  const _Initial();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Initial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PriestState.initial()';
}


}




/// @nodoc


class _Loading implements PriestState {
  const _Loading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Loading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PriestState.loading()';
}


}




/// @nodoc


class _Loaded implements PriestState {
  const _Loaded(final  List<Priest> priests): _priests = priests;
  

 final  List<Priest> _priests;
 List<Priest> get priests {
  if (_priests is EqualUnmodifiableListView) return _priests;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_priests);
}


/// Create a copy of PriestState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LoadedCopyWith<_Loaded> get copyWith => __$LoadedCopyWithImpl<_Loaded>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Loaded&&const DeepCollectionEquality().equals(other._priests, _priests));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_priests));

@override
String toString() {
  return 'PriestState.loaded(priests: $priests)';
}


}

/// @nodoc
abstract mixin class _$LoadedCopyWith<$Res> implements $PriestStateCopyWith<$Res> {
  factory _$LoadedCopyWith(_Loaded value, $Res Function(_Loaded) _then) = __$LoadedCopyWithImpl;
@useResult
$Res call({
 List<Priest> priests
});




}
/// @nodoc
class __$LoadedCopyWithImpl<$Res>
    implements _$LoadedCopyWith<$Res> {
  __$LoadedCopyWithImpl(this._self, this._then);

  final _Loaded _self;
  final $Res Function(_Loaded) _then;

/// Create a copy of PriestState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? priests = null,}) {
  return _then(_Loaded(
null == priests ? _self._priests : priests // ignore: cast_nullable_to_non_nullable
as List<Priest>,
  ));
}


}

/// @nodoc


class _Error implements PriestState {
  const _Error(this.message);
  

 final  String message;

/// Create a copy of PriestState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ErrorCopyWith<_Error> get copyWith => __$ErrorCopyWithImpl<_Error>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Error&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'PriestState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class _$ErrorCopyWith<$Res> implements $PriestStateCopyWith<$Res> {
  factory _$ErrorCopyWith(_Error value, $Res Function(_Error) _then) = __$ErrorCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class __$ErrorCopyWithImpl<$Res>
    implements _$ErrorCopyWith<$Res> {
  __$ErrorCopyWithImpl(this._self, this._then);

  final _Error _self;
  final $Res Function(_Error) _then;

/// Create a copy of PriestState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(_Error(
null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
