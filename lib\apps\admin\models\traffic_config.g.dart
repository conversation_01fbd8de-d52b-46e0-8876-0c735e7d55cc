// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'traffic_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TrafficConfig _$TrafficConfigFromJson(Map<String, dynamic> json) =>
    _TrafficConfig(
      routingMode: $enumDecode(
        _$TrafficRoutingModeEnumMap,
        json['routingMode'],
      ),
      awsTrafficPercentage: (json['awsTrafficPercentage'] as num).toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      maintenanceMode: json['maintenanceMode'] as bool? ?? false,
      autoFailoverEnabled: json['autoFailoverEnabled'] as bool? ?? true,
      customRules: json['customRules'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$TrafficConfigToJson(_TrafficConfig instance) =>
    <String, dynamic>{
      'routingMode': _$TrafficRoutingModeEnumMap[instance.routingMode]!,
      'awsTrafficPercentage': instance.awsTrafficPercentage,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'maintenanceMode': instance.maintenanceMode,
      'autoFailoverEnabled': instance.autoFailoverEnabled,
      'customRules': instance.customRules,
    };

const _$TrafficRoutingModeEnumMap = {
  TrafficRoutingMode.awsOnly: 'awsOnly',
  TrafficRoutingMode.datacenterOnly: 'datacenterOnly',
  TrafficRoutingMode.hybrid: 'hybrid',
  TrafficRoutingMode.geographic: 'geographic',
  TrafficRoutingMode.failover: 'failover',
  TrafficRoutingMode.maintenance: 'maintenance',
};

_ServerStatus _$ServerStatusFromJson(Map<String, dynamic> json) =>
    _ServerStatus(
      awsStatus: $enumDecode(_$ServerHealthEnumMap, json['awsStatus']),
      datacenterStatus: $enumDecode(
        _$ServerHealthEnumMap,
        json['datacenterStatus'],
      ),
      awsServers: (json['awsServers'] as List<dynamic>)
          .map((e) => ServerInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      datacenterServers: (json['datacenterServers'] as List<dynamic>)
          .map((e) => ServerInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      lastChecked: DateTime.parse(json['lastChecked'] as String),
      totalRequests: (json['totalRequests'] as num?)?.toInt() ?? 0,
      awsRequests: (json['awsRequests'] as num?)?.toInt() ?? 0,
      datacenterRequests: (json['datacenterRequests'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$ServerStatusToJson(_ServerStatus instance) =>
    <String, dynamic>{
      'awsStatus': _$ServerHealthEnumMap[instance.awsStatus]!,
      'datacenterStatus': _$ServerHealthEnumMap[instance.datacenterStatus]!,
      'awsServers': instance.awsServers.map((e) => e.toJson()).toList(),
      'datacenterServers': instance.datacenterServers
          .map((e) => e.toJson())
          .toList(),
      'lastChecked': instance.lastChecked.toIso8601String(),
      'totalRequests': instance.totalRequests,
      'awsRequests': instance.awsRequests,
      'datacenterRequests': instance.datacenterRequests,
    };

const _$ServerHealthEnumMap = {
  ServerHealth.healthy: 'healthy',
  ServerHealth.warning: 'warning',
  ServerHealth.critical: 'critical',
  ServerHealth.unknown: 'unknown',
};

_ServerInfo _$ServerInfoFromJson(Map<String, dynamic> json) => _ServerInfo(
  id: json['id'] as String,
  name: json['name'] as String,
  ipAddress: json['ipAddress'] as String,
  health: $enumDecode(_$ServerHealthEnumMap, json['health']),
  cpuUsage: (json['cpuUsage'] as num).toDouble(),
  memoryUsage: (json['memoryUsage'] as num).toDouble(),
  diskUsage: (json['diskUsage'] as num).toDouble(),
  responseTime: (json['responseTime'] as num).toInt(),
  activeConnections: (json['activeConnections'] as num).toInt(),
  lastHealthCheck: DateTime.parse(json['lastHealthCheck'] as String),
  region: json['region'] as String? ?? 'Unknown',
  serverType: json['serverType'] as String? ?? 'Unknown',
);

Map<String, dynamic> _$ServerInfoToJson(_ServerInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'ipAddress': instance.ipAddress,
      'health': _$ServerHealthEnumMap[instance.health]!,
      'cpuUsage': instance.cpuUsage,
      'memoryUsage': instance.memoryUsage,
      'diskUsage': instance.diskUsage,
      'responseTime': instance.responseTime,
      'activeConnections': instance.activeConnections,
      'lastHealthCheck': instance.lastHealthCheck.toIso8601String(),
      'region': instance.region,
      'serverType': instance.serverType,
    };

_RoutingRule _$RoutingRuleFromJson(Map<String, dynamic> json) => _RoutingRule(
  id: json['id'] as String,
  name: json['name'] as String,
  condition: $enumDecode(_$RoutingConditionEnumMap, json['condition']),
  action: $enumDecode(_$RoutingActionEnumMap, json['action']),
  priority: (json['priority'] as num).toInt(),
  isActive: json['isActive'] as bool,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$RoutingRuleToJson(_RoutingRule instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'condition': _$RoutingConditionEnumMap[instance.condition]!,
      'action': _$RoutingActionEnumMap[instance.action]!,
      'priority': instance.priority,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$RoutingConditionEnumMap = {
  RoutingCondition.geographic: 'geographic',
  RoutingCondition.userAgent: 'userAgent',
  RoutingCondition.ipRange: 'ipRange',
  RoutingCondition.timeOfDay: 'timeOfDay',
  RoutingCondition.serverLoad: 'serverLoad',
  RoutingCondition.customHeader: 'customHeader',
};

const _$RoutingActionEnumMap = {
  RoutingAction.routeToAws: 'routeToAws',
  RoutingAction.routeToDatacenter: 'routeToDatacenter',
  RoutingAction.routeToMaintenance: 'routeToMaintenance',
  RoutingAction.blockRequest: 'blockRequest',
  RoutingAction.rateLimitRequest: 'rateLimitRequest',
};

_TrafficAnalytics _$TrafficAnalyticsFromJson(
  Map<String, dynamic> json,
) => _TrafficAnalytics(
  timestamp: DateTime.parse(json['timestamp'] as String),
  totalRequests: (json['totalRequests'] as num).toInt(),
  awsRequests: (json['awsRequests'] as num).toInt(),
  datacenterRequests: (json['datacenterRequests'] as num).toInt(),
  averageResponseTime: (json['averageResponseTime'] as num).toDouble(),
  errorRate: (json['errorRate'] as num).toDouble(),
  requestsByCountry: Map<String, int>.from(json['requestsByCountry'] as Map),
  requestsByEndpoint: Map<String, int>.from(json['requestsByEndpoint'] as Map),
  blockedRequests: (json['blockedRequests'] as num?)?.toInt() ?? 0,
  rateLimitedRequests: (json['rateLimitedRequests'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$TrafficAnalyticsToJson(_TrafficAnalytics instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp.toIso8601String(),
      'totalRequests': instance.totalRequests,
      'awsRequests': instance.awsRequests,
      'datacenterRequests': instance.datacenterRequests,
      'averageResponseTime': instance.averageResponseTime,
      'errorRate': instance.errorRate,
      'requestsByCountry': instance.requestsByCountry,
      'requestsByEndpoint': instance.requestsByEndpoint,
      'blockedRequests': instance.blockedRequests,
      'rateLimitedRequests': instance.rateLimitedRequests,
    };

_LoadBalancerConfig _$LoadBalancerConfigFromJson(Map<String, dynamic> json) =>
    _LoadBalancerConfig(
      id: json['id'] as String,
      name: json['name'] as String,
      algorithm: $enumDecode(
        _$LoadBalancingAlgorithmEnumMap,
        json['algorithm'],
      ),
      endpoints: (json['endpoints'] as List<dynamic>)
          .map((e) => ServerEndpoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      healthCheck: HealthCheckConfig.fromJson(
        json['healthCheck'] as Map<String, dynamic>,
      ),
      stickySession: json['stickySession'] as bool? ?? true,
      sessionTimeout: (json['sessionTimeout'] as num?)?.toInt() ?? 30,
      maxRetries: (json['maxRetries'] as num?)?.toInt() ?? 5,
      timeoutMs: (json['timeoutMs'] as num?)?.toInt() ?? 1000,
    );

Map<String, dynamic> _$LoadBalancerConfigToJson(_LoadBalancerConfig instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'algorithm': _$LoadBalancingAlgorithmEnumMap[instance.algorithm]!,
      'endpoints': instance.endpoints.map((e) => e.toJson()).toList(),
      'healthCheck': instance.healthCheck.toJson(),
      'stickySession': instance.stickySession,
      'sessionTimeout': instance.sessionTimeout,
      'maxRetries': instance.maxRetries,
      'timeoutMs': instance.timeoutMs,
    };

const _$LoadBalancingAlgorithmEnumMap = {
  LoadBalancingAlgorithm.roundRobin: 'roundRobin',
  LoadBalancingAlgorithm.leastConnections: 'leastConnections',
  LoadBalancingAlgorithm.weightedRoundRobin: 'weightedRoundRobin',
  LoadBalancingAlgorithm.ipHash: 'ipHash',
  LoadBalancingAlgorithm.leastResponseTime: 'leastResponseTime',
};

_ServerEndpoint _$ServerEndpointFromJson(Map<String, dynamic> json) =>
    _ServerEndpoint(
      id: json['id'] as String,
      host: json['host'] as String,
      port: (json['port'] as num).toInt(),
      weight: (json['weight'] as num).toInt(),
      isActive: json['isActive'] as bool,
      protocol: json['protocol'] as String? ?? 'http',
      healthCheckPath: json['healthCheckPath'] as String? ?? '/health',
    );

Map<String, dynamic> _$ServerEndpointToJson(_ServerEndpoint instance) =>
    <String, dynamic>{
      'id': instance.id,
      'host': instance.host,
      'port': instance.port,
      'weight': instance.weight,
      'isActive': instance.isActive,
      'protocol': instance.protocol,
      'healthCheckPath': instance.healthCheckPath,
    };

_HealthCheckConfig _$HealthCheckConfigFromJson(Map<String, dynamic> json) =>
    _HealthCheckConfig(
      path: json['path'] as String,
      intervalSeconds: (json['intervalSeconds'] as num).toInt(),
      timeoutSeconds: (json['timeoutSeconds'] as num).toInt(),
      healthyThreshold: (json['healthyThreshold'] as num).toInt(),
      unhealthyThreshold: (json['unhealthyThreshold'] as num).toInt(),
      expectedStatusCode: (json['expectedStatusCode'] as num?)?.toInt() ?? 200,
      expectedResponse: json['expectedResponse'] as String?,
    );

Map<String, dynamic> _$HealthCheckConfigToJson(_HealthCheckConfig instance) =>
    <String, dynamic>{
      'path': instance.path,
      'intervalSeconds': instance.intervalSeconds,
      'timeoutSeconds': instance.timeoutSeconds,
      'healthyThreshold': instance.healthyThreshold,
      'unhealthyThreshold': instance.unhealthyThreshold,
      'expectedStatusCode': instance.expectedStatusCode,
      'expectedResponse': instance.expectedResponse,
    };
