import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:injectable/injectable.dart';

@injectable
class AuthService {
  final SupabaseClient _supabase = Supabase.instance.client;

  AuthService();

  Future<AuthResponse> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      return await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  Stream<User?> get authStateChanges => _supabase.auth.onAuthStateChange.map((state) => state.session?.user);

  String _handleAuthException(dynamic e) {
    if (e is AuthException) {
      switch (e.message) {
        case 'Invalid login credentials':
          return 'Invalid email or password.';
        case 'Email not confirmed':
          return 'Please verify your email address.';
        case 'Too many requests':
          return 'Too many attempts. Please try again later.';
        case 'User not found':
          return 'No user found with this email.';
        case 'Invalid email':
          return 'The email address is invalid.';
        case 'Weak password':
          return 'The password provided is too weak.';
        case 'Email already registered':
          return 'An account already exists with this email.';
        default:
          return e.message.isNotEmpty ? e.message : 'An authentication error occurred.';
      }
    }
    return 'An unknown error occurred: ${e.toString()}';
  }
}
