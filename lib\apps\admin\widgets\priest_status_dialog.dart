import 'package:flutter/material.dart';
import '../../../shared/models/priest.dart';

class PriestStatusDialog extends StatelessWidget {
  final Priest priest;

  const PriestStatusDialog({
    super.key,
    required this.priest,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Update Priest Status'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Current Status: ${priest.isActive ? 'Active' : 'Inactive'}'),
          const SizedBox(height: 16),
          const Text('Do you want to change the status?'),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, null),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context, !priest.isActive),
          child: Text(priest.isActive ? 'Deactivate' : 'Activate'),
        ),
      ],
    );
  }
}
