import 'dart:async';
import 'package:latlong2/latlong.dart' as latlong2;
import '../../../shared/models/delivery/delivery_person_model.dart';
import '../../../shared/utils/distance_calculator.dart';
import '../../../shared/utils/logger.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';

final _logger = getLogger('NearbyVehiclesService');

/// Service for finding nearby available vehicles/Saviours
class NearbyVehiclesService {
  final DatabaseService _databaseService;

  NearbyVehiclesService({DatabaseService? databaseService})
    : _databaseService =
          databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment());

  /// Maximum distance to search for nearby vehicles (in meters)
  static const double _maxSearchRadius = 15000; // 15km
  static const double _preferredRadius = 5000; // 5km preferred radius
  static const Duration _locationFreshnessThreshold = Duration(minutes: 5);

  /// Get nearby available vehicles based on location and vehicle type
  Future<List<DeliveryPersonModel>> getNearbyVehicles({
    required latlong2.LatLng userLocation,
    String? vehicleType,
    double radiusInMeters = _maxSearchRadius,
  }) async {
    try {
      _logger.info(
        'Searching for nearby vehicles at ${userLocation.latitude}, ${userLocation.longitude}',
      );

      // Get all delivery persons from hybrid storage
      final allDeliveryPersons = await _databaseService.getAll(
        'delivery_persons',
      );

      // Filter for available delivery persons
      final availablePersons = allDeliveryPersons
          .where(
            (data) =>
                data['isActive'] == true &&
                data['isVerified'] == true &&
                data['status'] == 'available' &&
                data['isOnline'] == true &&
                (vehicleType == null ||
                    vehicleType.isEmpty ||
                    data['vehicleType'] == vehicleType),
          )
          .toList();

      if (availablePersons.isEmpty) {
        _logger.info('No available vehicles found');
        return [];
      }

      final List<DeliveryPersonModel> nearbyVehicles = [];
      final now = DateTime.now();

      for (final data in availablePersons) {
        try {
          // Check location freshness
          final lastLocationUpdateStr = data['lastLocationUpdate'] as String?;
          if (lastLocationUpdateStr == null) {
            _logger.warning(
              'Saviour ${data['id']} has no location update timestamp',
            );
            continue;
          }

          final lastLocationUpdate = DateTime.tryParse(lastLocationUpdateStr);
          if (lastLocationUpdate == null) {
            _logger.warning(
              'Saviour ${data['id']} has invalid location update timestamp',
            );
            continue;
          }

          final locationAge = now.difference(lastLocationUpdate);
          if (locationAge > _locationFreshnessThreshold) {
            _logger.warning(
              'Saviour ${data['id']} location is stale (${locationAge.inMinutes} minutes old)',
            );
            continue;
          }

          final deliveryPerson = DeliveryPersonModel.fromJson(data);

          // Calculate distance from user location
          final vehicleLocation = latlong2.LatLng(
            deliveryPerson.location.latitude,
            deliveryPerson.location.longitude,
          );

          final distance = DistanceCalculator.calculateLatLngDistance(
            userLocation,
            vehicleLocation,
          );

          // Check if within radius
          if (distance <= radiusInMeters) {
            nearbyVehicles.add(deliveryPerson);
            _logger.info(
              'Found nearby vehicle: ${deliveryPerson.name} (${deliveryPerson.vehicleType}) at ${distance.toInt()}m (${locationAge.inMinutes}min old location)',
            );
          } else {
            _logger.info(
              'Saviour ${data['id']} is too far: ${distance.toInt()}m',
            );
          }
        } catch (e) {
          _logger.warning(
            'Error processing delivery person document ${data['id']}: $e',
          );
        }
      }

      // Sort by distance with preference for closer vehicles
      nearbyVehicles.sort((a, b) {
        final distanceA = DistanceCalculator.calculateLatLngDistance(
          userLocation,
          latlong2.LatLng(a.location.latitude, a.location.longitude),
        );
        final distanceB = DistanceCalculator.calculateLatLngDistance(
          userLocation,
          latlong2.LatLng(b.location.latitude, b.location.longitude),
        );

        // Prioritize vehicles within preferred radius
        final aInPreferred = distanceA <= _preferredRadius;
        final bInPreferred = distanceB <= _preferredRadius;

        if (aInPreferred && !bInPreferred) return -1;
        if (!aInPreferred && bInPreferred) return 1;

        // If both are in same category, sort by distance
        return distanceA.compareTo(distanceB);
      });

      _logger.info('Found ${nearbyVehicles.length} nearby vehicles');
      return nearbyVehicles;
    } catch (e) {
      _logger.severe('Error getting nearby vehicles: $e');
      return [];
    }
  }

  /// Stream of nearby vehicles that updates in real-time
  Stream<List<DeliveryPersonModel>> getNearbyVehiclesStream({
    required latlong2.LatLng userLocation,
    String? vehicleType,
    double radiusInMeters = _maxSearchRadius,
  }) {
    _logger.info('Starting polling-based stream for nearby vehicles');

    // Create a stream that polls the database every 10 seconds
    return Stream.periodic(const Duration(seconds: 10))
        .asyncMap((_) async {
          try {
            return await getNearbyVehicles(
              userLocation: userLocation,
              vehicleType: vehicleType,
              radiusInMeters: radiusInMeters,
            );
          } catch (e) {
            _logger.severe('Error in vehicle stream: $e');
            return <DeliveryPersonModel>[];
          }
        })
        .distinct((previous, next) {
          // Only emit if the list has actually changed
          if (previous.length != next.length) return false;
          for (int i = 0; i < previous.length; i++) {
            if (previous[i].id != next[i].id) return false;
          }
          return true;
        });
  }

  /// Get vehicle icon based on vehicle type and details
  String getVehicleIcon(DeliveryPersonModel deliveryPerson) {
    final vehicleType = deliveryPerson.vehicleType.toLowerCase();
    final brand = deliveryPerson.vehicleBrand?.toLowerCase() ?? '';
    final model = deliveryPerson.vehicleModel?.toLowerCase() ?? '';

    // Return appropriate icon based on vehicle type and brand/model
    switch (vehicleType) {
      case 'two-wheeler':
        if (brand.contains('honda') || model.contains('activa')) {
          return '🛵'; // Scooter
        } else if (brand.contains('bajaj') || model.contains('pulsar')) {
          return '🏍️'; // Motorcycle
        }
        return '🛵'; // Default scooter
      case 'auto':
        return '🛺'; // Auto rickshaw
      case 'standard':
        return '🚗'; // Car
      case 'premium':
        return '🚙'; // SUV
      case 'xl':
        return '🚐'; // Van
      default:
        return '🚗'; // Default car
    }
  }

  /// Get estimated arrival time for a vehicle
  Future<int> getEstimatedArrivalTime({
    required latlong2.LatLng userLocation,
    required DeliveryPersonModel deliveryPerson,
  }) async {
    final vehicleLocation = latlong2.LatLng(
      deliveryPerson.location.latitude,
      deliveryPerson.location.longitude,
    );

    final distance = DistanceCalculator.calculateLatLngDistance(
      userLocation,
      vehicleLocation,
    );

    // Estimate time based on vehicle type and distance
    double averageSpeed; // km/h
    switch (deliveryPerson.vehicleType.toLowerCase()) {
      case 'two-wheeler':
        averageSpeed = 25; // Faster in traffic
        break;
      case 'auto':
        averageSpeed = 20;
        break;
      case 'standard':
      case 'premium':
        averageSpeed = 15; // Slower in traffic
        break;
      case 'xl':
        averageSpeed = 12;
        break;
      default:
        averageSpeed = 20;
    }

    final timeInHours = (distance / 1000) / averageSpeed;
    final timeInMinutes = (timeInHours * 60).round();

    return timeInMinutes.clamp(1, 60); // Minimum 1 minute, maximum 60 minutes
  }

  /// Get diagnostic information about nearby vehicles
  Future<Map<String, dynamic>> getDiagnosticInfo({
    required latlong2.LatLng userLocation,
    String? vehicleType,
  }) async {
    try {
      final now = DateTime.now();

      // Get all potential saviours from hybrid storage
      final allDeliveryPersons = await _databaseService.getAll(
        'delivery_persons',
      );

      final potentialSaviours = allDeliveryPersons
          .where(
            (data) => data['isActive'] == true && data['isVerified'] == true,
          )
          .toList();

      int totalSaviours = potentialSaviours.length;
      int onlineSaviours = 0;
      int availableSaviours = 0;
      int savioursWithFreshLocation = 0;
      int savioursInRange = 0;
      int savioursWithVehicleType = 0;

      final List<Map<String, dynamic>> saviourDetails = [];

      for (final data in potentialSaviours) {
        try {
          final saviourInfo = <String, dynamic>{
            'id': data['id'] ?? 'Unknown',
            'name': data['name'] ?? 'Unknown',
            'isOnline': data['isOnline'] ?? false,
            'status': data['status'] ?? 'unknown',
            'issues': <String>[],
          };

          // Check online status
          if (data['isOnline'] == true) {
            onlineSaviours++;

            // Check availability
            if (data['status'] == 'available') {
              availableSaviours++;

              // Check location freshness
              final lastLocationUpdateStr =
                  data['lastLocationUpdate'] as String?;
              if (lastLocationUpdateStr != null) {
                final lastLocationUpdate = DateTime.tryParse(
                  lastLocationUpdateStr,
                );
                if (lastLocationUpdate == null) {
                  saviourInfo['issues'].add(
                    'Invalid location update timestamp',
                  );
                  continue;
                }
                final locationAge = now.difference(lastLocationUpdate);
                saviourInfo['locationAgeMinutes'] = locationAge.inMinutes;

                if (locationAge <= _locationFreshnessThreshold) {
                  savioursWithFreshLocation++;

                  // Check distance
                  final location = data['location'] as Map<String, dynamic>?;
                  if (location != null) {
                    final lat = location['latitude'] as double? ?? 0.0;
                    final lng = location['longitude'] as double? ?? 0.0;

                    final distance = DistanceCalculator.calculateLatLngDistance(
                      userLocation,
                      latlong2.LatLng(lat, lng),
                    );
                    saviourInfo['distanceMeters'] = distance.round();

                    if (distance <= _maxSearchRadius) {
                      savioursInRange++;
                    } else {
                      saviourInfo['issues'].add(
                        'Too far (${(distance / 1000).toStringAsFixed(1)}km)',
                      );
                    }
                  } else {
                    saviourInfo['issues'].add('No location coordinates');
                  }
                } else {
                  saviourInfo['issues'].add(
                    'Stale location (${locationAge.inMinutes}min old)',
                  );
                }
              } else {
                saviourInfo['issues'].add('No location update timestamp');
              }

              // Check vehicle type
              if (vehicleType != null && vehicleType.isNotEmpty) {
                final vehicleTypes = data['vehicleTypes'] as List<dynamic>?;
                if (vehicleTypes != null &&
                    vehicleTypes.contains(vehicleType)) {
                  savioursWithVehicleType++;
                } else {
                  saviourInfo['issues'].add('Does not support $vehicleType');
                }
              } else {
                savioursWithVehicleType++; // Count all if no specific type
              }
            } else {
              saviourInfo['issues'].add('Not available (${data['status']})');
            }
          } else {
            saviourInfo['issues'].add('Not online');
          }

          saviourDetails.add(saviourInfo);
        } catch (e) {
          _logger.warning(
            'Error processing saviour ${data['id']} in diagnostics: $e',
          );
        }
      }

      return {
        'timestamp': now.toIso8601String(),
        'userLocation': {
          'latitude': userLocation.latitude,
          'longitude': userLocation.longitude,
        },
        'vehicleType': vehicleType,
        'summary': {
          'totalSaviours': totalSaviours,
          'onlineSaviours': onlineSaviours,
          'availableSaviours': availableSaviours,
          'savioursWithFreshLocation': savioursWithFreshLocation,
          'savioursInRange': savioursInRange,
          'savioursWithVehicleType': savioursWithVehicleType,
        },
        'saviourDetails': saviourDetails,
        'canMatchRides': savioursInRange > 0 && savioursWithFreshLocation > 0,
      };
    } catch (e) {
      _logger.severe('Error getting diagnostic info: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
