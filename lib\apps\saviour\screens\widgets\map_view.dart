import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import '../../../../shared/widgets/maps/flutter_map_view.dart';

/// Widget for displaying a map with pickup, drop, and current locations
/// This is a wrapper around FlutterMapView for backward compatibility
class MapView extends StatelessWidget {
  final LatLng? pickupLocation;
  final LatLng? dropLocation;
  final LatLng? currentLocation;
  final bool showRoute;
  final bool fitToMarkers;
  final double initialZoom;
  final Function(MapController)? onMapCreated;

  const MapView({
    super.key,
    this.pickupLocation,
    this.dropLocation,
    this.currentLocation,
    this.showRoute = true,
    this.fitToMarkers = true,
    this.initialZoom = 14.0,
    this.onMapCreated,
  });

  @override
  Widget build(BuildContext context) {
    return FlutterMapView(
      pickupLocation: pickupLocation,
      dropLocation: dropLocation,
      currentLocation: currentLocation,
      showRoute: showRoute,
      fitToMarkers: fitToMarkers,
      initialZoom: initialZoom,
      onMapCreated: onMapCreated,
      showCurrentLocation: true,
      vehicleType: null, // No specific vehicle type for saviour map view
    );
  }
}
