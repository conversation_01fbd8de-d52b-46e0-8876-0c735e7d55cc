import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../providers/shopping/shopping_list_provider.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';

/// Dialog to save a shopping list as a template
class SaveAsTemplateDialog extends ConsumerStatefulWidget {
  /// Creates a [SaveAsTemplateDialog]
  const SaveAsTemplateDialog({
    required this.list,
    super.key,
  });

  /// The shopping list to save as a template
  final ShoppingListModel list;

  @override
  ConsumerState<SaveAsTemplateDialog> createState() =>
      _SaveAsTemplateDialogState();
}

class _SaveAsTemplateDialogState extends ConsumerState<SaveAsTemplateDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: '${widget.list.name} (Template)');
    _descriptionController = TextEditingController(text: widget.list.description);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _saveAsTemplate() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      // Create a new list as a template
      final now = DateTime.now();
      final template = ShoppingListModel(
        id: const Uuid().v4(),
        name: _nameController.text,
        description: _descriptionController.text,
        itemCount: widget.list.items.length,
        totalPrice: 0, // Reset price for template
        isShared: false,
        isTemplate: true, // Mark as template
        createdAt: now,
        updatedAt: now,
        createdBy: widget.list.createdBy,
        items: widget.list.items.map((item) {
          // Reset prices and checked status for template items
          return item.copyWith(
            price: 0,
            isChecked: false,
          );
        }).toList(),
      );

      await ref.read(shoppingListServiceProvider).createShoppingList(template);

      if (mounted) {
        Navigator.of(context).pop(true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Template saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save template: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Save as Template'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Template Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a name for the template';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('CANCEL'),
        ),
        FilledButton(
          onPressed: _saveAsTemplate,
          child: const Text('SAVE'),
        ),
      ],
    );
  }
}
