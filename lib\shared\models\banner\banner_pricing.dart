import 'package:freezed_annotation/freezed_annotation.dart';

part 'banner_pricing.freezed.dart';
part 'banner_pricing.g.dart';

@freezed
abstract class BannerPricing with _$BannerPricing {
  const factory BannerPricing({
    required String id,
    required double basePrice,
    required Map<String, double> durationPrices,
    required Map<String, double> priorityMultiplier,
    required Map<String, double> categoryMultipliers,
    required Map<String, double> seasonalMultipliers,
    required double aiSuggestedPrice,
    required DateTime lastUpdated,
    @Default(true) bool isActive,
  }) = _BannerPricing;

  factory BannerPricing.fromJson(Map<String, dynamic> json) =>
      _$BannerPricingFromJson(json);
}
