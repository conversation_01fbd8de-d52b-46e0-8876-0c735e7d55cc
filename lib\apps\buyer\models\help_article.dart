import 'package:freezed_annotation/freezed_annotation.dart';

part 'help_article.freezed.dart';
part 'help_article.g.dart';

@freezed
sealed class HelpArticle with _$HelpArticle {
  const factory HelpArticle({
    required String id,
    required String title,
    required String question,
    required String answer,
    required String status,
    required String date,
    String? description,
    List<Map<String, dynamic>>? updates,
  }) = _HelpArticle;

  factory HelpArticle.fromJson(Map<String, dynamic> json) =>
      _$HelpArticleFromJson(json);
}
