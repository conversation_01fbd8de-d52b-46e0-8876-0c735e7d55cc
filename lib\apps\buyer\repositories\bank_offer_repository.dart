import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/offer/bank_offer_model.dart';

/// Bank offer repository interface
abstract class BankOfferRepository {
  /// Get all active bank offers
  Future<List<BankOfferModel>> getActiveBankOffers();

  /// Get bank offers by product ID
  Future<List<BankOfferModel>> getBankOffersByProductId(String productId);

  /// Get bank offers by category ID
  Future<List<BankOfferModel>> getBankOffersByCategoryId(String categoryId);
}

/// Provider for the bank offer repository
final bankOfferRepositoryProvider = Provider<BankOfferRepository>((ref) {
  throw UnimplementedError('BankOfferRepository not implemented');
});
