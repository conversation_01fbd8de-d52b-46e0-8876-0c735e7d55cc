// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_gateway_analytics_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$PaymentGatewayAnalyticsEvent {

 DateTimeRange get dateRange;
/// Create a copy of PaymentGatewayAnalyticsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentGatewayAnalyticsEventCopyWith<PaymentGatewayAnalyticsEvent> get copyWith => _$PaymentGatewayAnalyticsEventCopyWithImpl<PaymentGatewayAnalyticsEvent>(this as PaymentGatewayAnalyticsEvent, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentGatewayAnalyticsEvent&&(identical(other.dateRange, dateRange) || other.dateRange == dateRange));
}


@override
int get hashCode => Object.hash(runtimeType,dateRange);

@override
String toString() {
  return 'PaymentGatewayAnalyticsEvent(dateRange: $dateRange)';
}


}

/// @nodoc
abstract mixin class $PaymentGatewayAnalyticsEventCopyWith<$Res>  {
  factory $PaymentGatewayAnalyticsEventCopyWith(PaymentGatewayAnalyticsEvent value, $Res Function(PaymentGatewayAnalyticsEvent) _then) = _$PaymentGatewayAnalyticsEventCopyWithImpl;
@useResult
$Res call({
 DateTimeRange<DateTime> dateRange
});




}
/// @nodoc
class _$PaymentGatewayAnalyticsEventCopyWithImpl<$Res>
    implements $PaymentGatewayAnalyticsEventCopyWith<$Res> {
  _$PaymentGatewayAnalyticsEventCopyWithImpl(this._self, this._then);

  final PaymentGatewayAnalyticsEvent _self;
  final $Res Function(PaymentGatewayAnalyticsEvent) _then;

/// Create a copy of PaymentGatewayAnalyticsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? dateRange = null,}) {
  return _then(_self.copyWith(
dateRange: null == dateRange ? _self.dateRange : dateRange // ignore: cast_nullable_to_non_nullable
as DateTimeRange<DateTime>,
  ));
}

}


/// Adds pattern-matching-related methods to [PaymentGatewayAnalyticsEvent].
extension PaymentGatewayAnalyticsEventPatterns on PaymentGatewayAnalyticsEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( LoadAnalytics value)?  loadAnalytics,TResult Function( UpdateDateRange value)?  updateDateRange,required TResult orElse(),}){
final _that = this;
switch (_that) {
case LoadAnalytics() when loadAnalytics != null:
return loadAnalytics(_that);case UpdateDateRange() when updateDateRange != null:
return updateDateRange(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( LoadAnalytics value)  loadAnalytics,required TResult Function( UpdateDateRange value)  updateDateRange,}){
final _that = this;
switch (_that) {
case LoadAnalytics():
return loadAnalytics(_that);case UpdateDateRange():
return updateDateRange(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( LoadAnalytics value)?  loadAnalytics,TResult? Function( UpdateDateRange value)?  updateDateRange,}){
final _that = this;
switch (_that) {
case LoadAnalytics() when loadAnalytics != null:
return loadAnalytics(_that);case UpdateDateRange() when updateDateRange != null:
return updateDateRange(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( PaymentGateway gateway,  DateTimeRange dateRange)?  loadAnalytics,TResult Function( DateTimeRange dateRange)?  updateDateRange,required TResult orElse(),}) {final _that = this;
switch (_that) {
case LoadAnalytics() when loadAnalytics != null:
return loadAnalytics(_that.gateway,_that.dateRange);case UpdateDateRange() when updateDateRange != null:
return updateDateRange(_that.dateRange);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( PaymentGateway gateway,  DateTimeRange dateRange)  loadAnalytics,required TResult Function( DateTimeRange dateRange)  updateDateRange,}) {final _that = this;
switch (_that) {
case LoadAnalytics():
return loadAnalytics(_that.gateway,_that.dateRange);case UpdateDateRange():
return updateDateRange(_that.dateRange);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( PaymentGateway gateway,  DateTimeRange dateRange)?  loadAnalytics,TResult? Function( DateTimeRange dateRange)?  updateDateRange,}) {final _that = this;
switch (_that) {
case LoadAnalytics() when loadAnalytics != null:
return loadAnalytics(_that.gateway,_that.dateRange);case UpdateDateRange() when updateDateRange != null:
return updateDateRange(_that.dateRange);case _:
  return null;

}
}

}

/// @nodoc


class LoadAnalytics implements PaymentGatewayAnalyticsEvent {
  const LoadAnalytics({required this.gateway, required this.dateRange});
  

 final  PaymentGateway gateway;
@override final  DateTimeRange dateRange;

/// Create a copy of PaymentGatewayAnalyticsEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LoadAnalyticsCopyWith<LoadAnalytics> get copyWith => _$LoadAnalyticsCopyWithImpl<LoadAnalytics>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadAnalytics&&(identical(other.gateway, gateway) || other.gateway == gateway)&&(identical(other.dateRange, dateRange) || other.dateRange == dateRange));
}


@override
int get hashCode => Object.hash(runtimeType,gateway,dateRange);

@override
String toString() {
  return 'PaymentGatewayAnalyticsEvent.loadAnalytics(gateway: $gateway, dateRange: $dateRange)';
}


}

/// @nodoc
abstract mixin class $LoadAnalyticsCopyWith<$Res> implements $PaymentGatewayAnalyticsEventCopyWith<$Res> {
  factory $LoadAnalyticsCopyWith(LoadAnalytics value, $Res Function(LoadAnalytics) _then) = _$LoadAnalyticsCopyWithImpl;
@override @useResult
$Res call({
 PaymentGateway gateway, DateTimeRange dateRange
});


$PaymentGatewayCopyWith<$Res> get gateway;

}
/// @nodoc
class _$LoadAnalyticsCopyWithImpl<$Res>
    implements $LoadAnalyticsCopyWith<$Res> {
  _$LoadAnalyticsCopyWithImpl(this._self, this._then);

  final LoadAnalytics _self;
  final $Res Function(LoadAnalytics) _then;

/// Create a copy of PaymentGatewayAnalyticsEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? gateway = null,Object? dateRange = null,}) {
  return _then(LoadAnalytics(
gateway: null == gateway ? _self.gateway : gateway // ignore: cast_nullable_to_non_nullable
as PaymentGateway,dateRange: null == dateRange ? _self.dateRange : dateRange // ignore: cast_nullable_to_non_nullable
as DateTimeRange,
  ));
}

/// Create a copy of PaymentGatewayAnalyticsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentGatewayCopyWith<$Res> get gateway {
  
  return $PaymentGatewayCopyWith<$Res>(_self.gateway, (value) {
    return _then(_self.copyWith(gateway: value));
  });
}
}

/// @nodoc


class UpdateDateRange implements PaymentGatewayAnalyticsEvent {
  const UpdateDateRange({required this.dateRange});
  

@override final  DateTimeRange dateRange;

/// Create a copy of PaymentGatewayAnalyticsEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateDateRangeCopyWith<UpdateDateRange> get copyWith => _$UpdateDateRangeCopyWithImpl<UpdateDateRange>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateDateRange&&(identical(other.dateRange, dateRange) || other.dateRange == dateRange));
}


@override
int get hashCode => Object.hash(runtimeType,dateRange);

@override
String toString() {
  return 'PaymentGatewayAnalyticsEvent.updateDateRange(dateRange: $dateRange)';
}


}

/// @nodoc
abstract mixin class $UpdateDateRangeCopyWith<$Res> implements $PaymentGatewayAnalyticsEventCopyWith<$Res> {
  factory $UpdateDateRangeCopyWith(UpdateDateRange value, $Res Function(UpdateDateRange) _then) = _$UpdateDateRangeCopyWithImpl;
@override @useResult
$Res call({
 DateTimeRange dateRange
});




}
/// @nodoc
class _$UpdateDateRangeCopyWithImpl<$Res>
    implements $UpdateDateRangeCopyWith<$Res> {
  _$UpdateDateRangeCopyWithImpl(this._self, this._then);

  final UpdateDateRange _self;
  final $Res Function(UpdateDateRange) _then;

/// Create a copy of PaymentGatewayAnalyticsEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? dateRange = null,}) {
  return _then(UpdateDateRange(
dateRange: null == dateRange ? _self.dateRange : dateRange // ignore: cast_nullable_to_non_nullable
as DateTimeRange,
  ));
}


}

/// @nodoc
mixin _$PaymentGatewayAnalyticsState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentGatewayAnalyticsState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PaymentGatewayAnalyticsState()';
}


}

/// @nodoc
class $PaymentGatewayAnalyticsStateCopyWith<$Res>  {
$PaymentGatewayAnalyticsStateCopyWith(PaymentGatewayAnalyticsState _, $Res Function(PaymentGatewayAnalyticsState) __);
}


/// Adds pattern-matching-related methods to [PaymentGatewayAnalyticsState].
extension PaymentGatewayAnalyticsStatePatterns on PaymentGatewayAnalyticsState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( Initial value)?  initial,TResult Function( Loading value)?  loading,TResult Function( Loaded value)?  loaded,TResult Function( Error value)?  error,required TResult orElse(),}){
final _that = this;
switch (_that) {
case Initial() when initial != null:
return initial(_that);case Loading() when loading != null:
return loading(_that);case Loaded() when loaded != null:
return loaded(_that);case Error() when error != null:
return error(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( Initial value)  initial,required TResult Function( Loading value)  loading,required TResult Function( Loaded value)  loaded,required TResult Function( Error value)  error,}){
final _that = this;
switch (_that) {
case Initial():
return initial(_that);case Loading():
return loading(_that);case Loaded():
return loaded(_that);case Error():
return error(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( Initial value)?  initial,TResult? Function( Loading value)?  loading,TResult? Function( Loaded value)?  loaded,TResult? Function( Error value)?  error,}){
final _that = this;
switch (_that) {
case Initial() when initial != null:
return initial(_that);case Loading() when loading != null:
return loading(_that);case Loaded() when loaded != null:
return loaded(_that);case Error() when error != null:
return error(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  initial,TResult Function()?  loading,TResult Function( PaymentGateway gateway,  Map<String, dynamic> summary,  List<Map<String, dynamic>> dailyCounts,  List<Map<String, dynamic>> statusDistribution)?  loaded,TResult Function( String message)?  error,required TResult orElse(),}) {final _that = this;
switch (_that) {
case Initial() when initial != null:
return initial();case Loading() when loading != null:
return loading();case Loaded() when loaded != null:
return loaded(_that.gateway,_that.summary,_that.dailyCounts,_that.statusDistribution);case Error() when error != null:
return error(_that.message);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  initial,required TResult Function()  loading,required TResult Function( PaymentGateway gateway,  Map<String, dynamic> summary,  List<Map<String, dynamic>> dailyCounts,  List<Map<String, dynamic>> statusDistribution)  loaded,required TResult Function( String message)  error,}) {final _that = this;
switch (_that) {
case Initial():
return initial();case Loading():
return loading();case Loaded():
return loaded(_that.gateway,_that.summary,_that.dailyCounts,_that.statusDistribution);case Error():
return error(_that.message);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  initial,TResult? Function()?  loading,TResult? Function( PaymentGateway gateway,  Map<String, dynamic> summary,  List<Map<String, dynamic>> dailyCounts,  List<Map<String, dynamic>> statusDistribution)?  loaded,TResult? Function( String message)?  error,}) {final _that = this;
switch (_that) {
case Initial() when initial != null:
return initial();case Loading() when loading != null:
return loading();case Loaded() when loaded != null:
return loaded(_that.gateway,_that.summary,_that.dailyCounts,_that.statusDistribution);case Error() when error != null:
return error(_that.message);case _:
  return null;

}
}

}

/// @nodoc


class Initial implements PaymentGatewayAnalyticsState {
  const Initial();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Initial);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PaymentGatewayAnalyticsState.initial()';
}


}




/// @nodoc


class Loading implements PaymentGatewayAnalyticsState {
  const Loading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Loading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PaymentGatewayAnalyticsState.loading()';
}


}




/// @nodoc


class Loaded implements PaymentGatewayAnalyticsState {
  const Loaded({required this.gateway, required final  Map<String, dynamic> summary, required final  List<Map<String, dynamic>> dailyCounts, required final  List<Map<String, dynamic>> statusDistribution}): _summary = summary,_dailyCounts = dailyCounts,_statusDistribution = statusDistribution;
  

 final  PaymentGateway gateway;
 final  Map<String, dynamic> _summary;
 Map<String, dynamic> get summary {
  if (_summary is EqualUnmodifiableMapView) return _summary;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_summary);
}

 final  List<Map<String, dynamic>> _dailyCounts;
 List<Map<String, dynamic>> get dailyCounts {
  if (_dailyCounts is EqualUnmodifiableListView) return _dailyCounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_dailyCounts);
}

 final  List<Map<String, dynamic>> _statusDistribution;
 List<Map<String, dynamic>> get statusDistribution {
  if (_statusDistribution is EqualUnmodifiableListView) return _statusDistribution;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_statusDistribution);
}


/// Create a copy of PaymentGatewayAnalyticsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LoadedCopyWith<Loaded> get copyWith => _$LoadedCopyWithImpl<Loaded>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Loaded&&(identical(other.gateway, gateway) || other.gateway == gateway)&&const DeepCollectionEquality().equals(other._summary, _summary)&&const DeepCollectionEquality().equals(other._dailyCounts, _dailyCounts)&&const DeepCollectionEquality().equals(other._statusDistribution, _statusDistribution));
}


@override
int get hashCode => Object.hash(runtimeType,gateway,const DeepCollectionEquality().hash(_summary),const DeepCollectionEquality().hash(_dailyCounts),const DeepCollectionEquality().hash(_statusDistribution));

@override
String toString() {
  return 'PaymentGatewayAnalyticsState.loaded(gateway: $gateway, summary: $summary, dailyCounts: $dailyCounts, statusDistribution: $statusDistribution)';
}


}

/// @nodoc
abstract mixin class $LoadedCopyWith<$Res> implements $PaymentGatewayAnalyticsStateCopyWith<$Res> {
  factory $LoadedCopyWith(Loaded value, $Res Function(Loaded) _then) = _$LoadedCopyWithImpl;
@useResult
$Res call({
 PaymentGateway gateway, Map<String, dynamic> summary, List<Map<String, dynamic>> dailyCounts, List<Map<String, dynamic>> statusDistribution
});


$PaymentGatewayCopyWith<$Res> get gateway;

}
/// @nodoc
class _$LoadedCopyWithImpl<$Res>
    implements $LoadedCopyWith<$Res> {
  _$LoadedCopyWithImpl(this._self, this._then);

  final Loaded _self;
  final $Res Function(Loaded) _then;

/// Create a copy of PaymentGatewayAnalyticsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? gateway = null,Object? summary = null,Object? dailyCounts = null,Object? statusDistribution = null,}) {
  return _then(Loaded(
gateway: null == gateway ? _self.gateway : gateway // ignore: cast_nullable_to_non_nullable
as PaymentGateway,summary: null == summary ? _self._summary : summary // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,dailyCounts: null == dailyCounts ? _self._dailyCounts : dailyCounts // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,statusDistribution: null == statusDistribution ? _self._statusDistribution : statusDistribution // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,
  ));
}

/// Create a copy of PaymentGatewayAnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentGatewayCopyWith<$Res> get gateway {
  
  return $PaymentGatewayCopyWith<$Res>(_self.gateway, (value) {
    return _then(_self.copyWith(gateway: value));
  });
}
}

/// @nodoc


class Error implements PaymentGatewayAnalyticsState {
  const Error(this.message);
  

 final  String message;

/// Create a copy of PaymentGatewayAnalyticsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ErrorCopyWith<Error> get copyWith => _$ErrorCopyWithImpl<Error>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Error&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'PaymentGatewayAnalyticsState.error(message: $message)';
}


}

/// @nodoc
abstract mixin class $ErrorCopyWith<$Res> implements $PaymentGatewayAnalyticsStateCopyWith<$Res> {
  factory $ErrorCopyWith(Error value, $Res Function(Error) _then) = _$ErrorCopyWithImpl;
@useResult
$Res call({
 String message
});




}
/// @nodoc
class _$ErrorCopyWithImpl<$Res>
    implements $ErrorCopyWith<$Res> {
  _$ErrorCopyWithImpl(this._self, this._then);

  final Error _self;
  final $Res Function(Error) _then;

/// Create a copy of PaymentGatewayAnalyticsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = null,}) {
  return _then(Error(
null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
