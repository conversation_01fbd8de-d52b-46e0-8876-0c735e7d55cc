import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HealthPackage {
  final String id;
  final String title;
  final String tests;
  final String price;
  final String discount;
  final Color color;

  HealthPackage({
    required this.id,
    required this.title,
    required this.tests,
    required this.price,
    required this.discount,
    required this.color,
  });
}

final healthPackagesProvider = Provider<List<HealthPackage>>((ref) {
  return [
    HealthPackage(
      id: '1',
      title: 'Basic Health Checkup',
      tests: '15 Tests',
      price: '₹999',
      discount: '₹1999',
      color: Colors.blue,
    ),
    HealthPackage(
      id: '2',
      title: 'Comprehensive Health Package',
      tests: '30 Tests',
      price: '₹2499',
      discount: '₹3999',
      color: Colors.green,
    ),
    HealthPackage(
      id: '3',
      title: 'Advanced Health Screening',
      tests: '45 Tests',
      price: '₹4999',
      discount: '₹7999',
      color: Colors.purple,
    ),
  ];
});