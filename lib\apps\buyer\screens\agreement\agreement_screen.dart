import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../services/buyer_agreement_service.dart';

/// Screen that shows the legal agreement for buyers
class AgreementScreen extends StatefulWidget {
  /// Creates an [AgreementScreen]
  const AgreementScreen({super.key});

  @override
  State<AgreementScreen> createState() => _AgreementScreenState();
}

class _AgreementScreenState extends State<AgreementScreen> {
  bool _isLoading = true;
  String _statusMessage = 'Checking agreement status...';

  @override
  void initState() {
    super.initState();
    _checkAgreement();
  }

  Future<void> _checkAgreement() async {
    try {
      setState(() {
        _isLoading = true;
        _statusMessage = 'Checking agreement status...';
      });

      // Show the agreement dialog
      final accepted = await BuyerAgreementService.checkAgreement(context);

      if (mounted) {
        if (accepted) {
          // If accepted, navigate to login
          context.go('/buyer/login');
        } else {
          // If not accepted, show error
          setState(() {
            _isLoading = false;
            _statusMessage = 'You must accept the agreement to continue.';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _statusMessage = 'Error: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: _isLoading
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(_statusMessage),
                ],
              )
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _statusMessage,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _checkAgreement,
                    child: const Text('Try Again'),
                  ),
                ],
              ),
      ),
    );
  }
}
