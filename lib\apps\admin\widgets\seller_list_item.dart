import 'package:flutter/material.dart';
import '../../../shared/models/seller.dart';
import '../../../shared/ui_components/dialogs/app_dialog.dart';
import '../../../shared/ui_components/dialogs/performance_update_dialog.dart';

class SellerListItem extends StatelessWidget {
  final Seller seller;
  final Function(bool isActive, bool isSuspended) onUpdateStatus;
  final Function(
    double rating,
    int totalReviews,
    int totalOrders,
    int totalProducts,
    double totalRevenue,
  ) onUpdatePerformance;
  final VoidCallback onDelete;

  const SellerListItem({
    super.key,
    required this.seller,
    required this.onUpdateStatus,
    required this.onUpdatePerformance,
    required this.onDelete,
  });

  // Direct status updates are now handled by the buttons

  void _showPerformanceUpdateDialog(BuildContext context) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => PerformanceUpdateDialog(
        title: 'Update Seller Performance',
        rating: seller.rating,
        totalReviews: seller.totalReviews,
        totalOrders: seller.totalOrders,
        totalProducts: seller.totalProducts,
        totalRevenue: seller.totalRevenue,
      ),
    );

    if (result != null) {
      onUpdatePerformance(
        result['rating']!,
        result['totalReviews']!,
        result['totalOrders']!,
        result['totalProducts']!,
        result['totalRevenue']!,
      );
    }
  }

  void _showDeleteConfirmationDialog(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const AppDialog(
        title: 'Delete Seller',
        message: 'Are you sure you want to delete this seller?',
        confirmText: 'Delete',
        cancelText: 'Cancel',
      ),
    );

    if (confirmed == true) {
      onDelete();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar
                CircleAvatar(
                  radius: 24,
                  backgroundImage: seller.profileImage != null
                      ? NetworkImage(seller.profileImage!)
                      : null,
                  child: seller.profileImage == null
                      ? Text(seller.businessName[0].toUpperCase(), style: const TextStyle(fontSize: 20))
                      : null,
                ),
                const SizedBox(width: 12),

                // Main info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name and email
                      Text(
                        seller.businessName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        seller.email,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),

                      // Address
                      if (seller.businessAddress.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.location_on, size: 14, color: Colors.grey),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                seller.businessAddress.first,
                                style: const TextStyle(fontSize: 14),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],

                      // Approver info
                      if (seller.approvedBy != null) ...[
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.person, size: 14, color: Colors.grey),
                            const SizedBox(width: 4),
                            Text(
                              'Approved by: ${seller.approvedBy}',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ],

                      // Status
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getStatusColor(seller).withAlpha(25),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: _getStatusColor(seller)),
                            ),
                            child: Text(
                              _getStatusText(seller),
                              style: TextStyle(
                                color: _getStatusColor(seller),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Action buttons
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Activate/Suspend button
                    if (seller.isActive)
                      IconButton(
                        icon: const Icon(Icons.pause_circle_outline, color: Colors.orange),
                        tooltip: 'Suspend',
                        onPressed: () {
                          onUpdateStatus(false, true);
                        },
                      )
                    else
                      IconButton(
                        icon: const Icon(Icons.play_circle_outline, color: Colors.green),
                        tooltip: 'Activate',
                        onPressed: () {
                          onUpdateStatus(true, false);
                        },
                      ),

                    // Delete button
                    IconButton(
                      icon: const Icon(Icons.delete_outline, color: Colors.red),
                      tooltip: 'Delete',
                      onPressed: () => _showDeleteConfirmationDialog(context),
                    ),

                    // More options
                    PopupMenuButton<String>(
                      icon: const Icon(Icons.more_vert),
                      onSelected: (value) {
                        switch (value) {
                          case 'performance':
                            _showPerformanceUpdateDialog(context);
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'performance',
                          child: ListTile(
                            leading: Icon(Icons.assessment),
                            title: Text('Update Performance'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(Seller seller) {
    if (seller.isSuspended) return Colors.orange;
    if (!seller.isApproved) return Colors.blue;
    if (seller.isActive) return Colors.green;
    return Colors.red;
  }

  String _getStatusText(Seller seller) {
    if (seller.isSuspended) return 'Suspended';
    if (!seller.isApproved) return 'Pending Approval';
    if (seller.isActive) return 'Active';
    return 'Inactive';
  }
}
