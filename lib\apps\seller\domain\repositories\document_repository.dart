import 'package:shivish/apps/seller/domain/models/document_model.dart';

abstract class DocumentRepository {
  Stream<List<DocumentModel>> getDocuments();
  Stream<DocumentModel> getDocument(String id);
  Future<void> uploadDocument({
    required DocumentType type,
    required String filePath,
    required String fileName,
  });
  Future<void> deleteDocument(String id);
  Future<void> updateDocumentStatus({
    required String id,
    required DocumentStatus status,
    String? rejectionReason,
    String? verifiedBy,
  });
}
