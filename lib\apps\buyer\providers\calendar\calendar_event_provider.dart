import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/calendar/calendar_event_model.dart';
import '../../../../shared/models/calendar/calendar_event_type.dart';
import '../../../../shared/models/calendar/calendar_event_visibility.dart';
import '../../../../shared/services/calendar/calendar_event_service.dart';

/// Provider for the calendar event service
final calendarEventServiceProvider = Provider<CalendarEventService>((ref) {
  return CalendarEventService();
});

/// Provider for user's calendar events by date range
final userCalendarEventsByDateRangeProvider =
    FutureProvider.family<
      List<CalendarEventModel>,
      (String userId, DateTime startDate, DateTime endDate)
    >((ref, params) async {
      final service = ref.watch(calendarEventServiceProvider);
      try {
        debugPrint(
          'userCalendarEventsByDateRangeProvider: Getting events for user ${params.$1} from ${params.$2} to ${params.$3}',
        );

        // Use the available method for events by date range with userId filter
        final events = await service.getEventsByDateRange(
          params.$2,
          params.$3,
          userId: params.$1,
        );

        debugPrint(
          'userCalendarEventsByDateRangeProvider: Retrieved ${events.length} events',
        );
        for (var event in events) {
          debugPrint(
            '  Event: ${event.id}, Title: ${event.title}, Date: ${event.startDate}',
          );
        }

        return events;
      } catch (e) {
        debugPrint('userCalendarEventsByDateRangeProvider: Error: $e');
        // Return an empty list on error
        return [];
      }
    });

/// Provider for user's calendar events by type
final userCalendarEventsByTypeProvider =
    StreamProvider.family<
      List<CalendarEventModel>,
      (String userId, CalendarEventType type)
    >((ref, params) {
      final service = ref.watch(calendarEventServiceProvider);
      try {
        debugPrint(
          'userCalendarEventsByTypeProvider: Getting events for user ${params.$1} with type ${params.$2}',
        );
        return Stream.fromFuture(
          service.getEventsByType(params.$2, userId: params.$1),
        );
      } catch (e) {
        debugPrint('userCalendarEventsByTypeProvider: Error: $e');
        // Return an empty stream on error
        return Stream.value([]);
      }
    });

/// Provider for user's calendar events
final userCalendarEventsProvider =
    StreamProvider.family<List<CalendarEventModel>, String>((ref, userId) {
      final service = ref.watch(calendarEventServiceProvider);
      try {
        debugPrint(
          'userCalendarEventsProvider: Getting all events for user $userId',
        );
        return Stream.fromFuture(service.getEventsByUser(userId));
      } catch (e) {
        debugPrint('userCalendarEventsProvider: Error: $e');
        // Return an empty stream on error
        return Stream.value([]);
      }
    });

/// Provider for public calendar events
final publicCalendarEventsProvider =
    StreamProvider.autoDispose<List<CalendarEventModel>>((ref) {
      final service = ref.watch(calendarEventServiceProvider);
      try {
        // Get all events and filter for public ones
        return Stream.fromFuture(
          service
              .getEventsByDateRange(
                DateTime.now().subtract(const Duration(days: 30)),
                DateTime.now().add(const Duration(days: 365)),
              )
              .then(
                (allEvents) => allEvents
                    .where(
                      (event) =>
                          event.visibility == CalendarEventVisibility.public,
                    )
                    .toList(),
              ),
        );
      } catch (e) {
        // Return an empty stream on error
        return Stream.value([]);
      }
    });

/// Provider for a specific calendar event by ID
final calendarEventByIdProvider =
    StreamProvider.family<CalendarEventModel?, String>((ref, eventId) {
      final service = ref.watch(calendarEventServiceProvider);
      try {
        return Stream.fromFuture(service.getEventById(eventId));
      } catch (e) {
        // Return an empty stream on error
        return Stream.value(null);
      }
    });
