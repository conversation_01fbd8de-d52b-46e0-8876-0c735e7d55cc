import 'package:freezed_annotation/freezed_annotation.dart';

part 'tone_model.freezed.dart';
part 'tone_model.g.dart';

enum ToneStatus {
  @JsonValue(0)
  pending,
  @JsonValue(1)
  approved,
  @JsonValue(2)
  rejected,
}

enum ToneType {
  @JsonValue(0)
  single, // Single tone for a language
  @JsonValue(1)
  multiple, // Multiple tones based on day of week
  @JsonValue(2)
  own, // User's own tone
}

@freezed
abstract class ToneModel with _$ToneModel {
  const factory ToneModel(
      {required String id,
      required String name,
      required String category,
      required String filePath,
      required int duration,
      required bool isDefault,
      required DateTime createdAt,
      required DateTime updatedAt,
      required String uploadedBy, // 'admin' or 'executor'
      @Default(ToneStatus.pending) ToneStatus status,
      String? rejectionReason,
      String? approvedBy,
      DateTime? approvedAt,
      @Default('en')
      String language, // Language the tone is for (e.g., 'en', 'hi', 'te')
      @Default(false)
      bool isMultiple, // Whether this is a single or multiple tone
      @Default([]) List<String> daysOfWeek // Days when this tone should be used
      }) = _ToneModel;

  factory ToneModel.fromJson(Map<String, dynamic> json) =>
      _$ToneModelFromJson(json);

  /// Create a new tone with current timestamp
  factory ToneModel.create({
    required String name,
    required String category,
    required String filePath,
    required int duration,
    required String uploadedBy,
    bool isDefault = false,
    String language = 'en',
    bool isMultiple = false,
    List<String>? daysOfWeek,
  }) {
    final now = DateTime.now();
    return ToneModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      category: category,
      filePath: filePath,
      duration: duration,
      isDefault: isDefault,
      createdAt: now,
      updatedAt: now,
      uploadedBy: uploadedBy,
      language: language,
      isMultiple: isMultiple,
      daysOfWeek: daysOfWeek ?? [],
    );
  }
}

/// Business logic extensions for ToneModel
extension ToneModelX on ToneModel {
  /// Check if tone is approved
  bool get isApproved => status == ToneStatus.approved;

  /// Check if tone is pending
  bool get isPending => status == ToneStatus.pending;

  /// Check if tone is rejected
  bool get isRejected => status == ToneStatus.rejected;

  /// Approve the tone
  ToneModel approve(String approvedBy) {
    return copyWith(
      status: ToneStatus.approved,
      approvedBy: approvedBy,
      approvedAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Reject the tone
  ToneModel reject(String rejectionReason) {
    return copyWith(
      status: ToneStatus.rejected,
      rejectionReason: rejectionReason,
      updatedAt: DateTime.now(),
    );
  }

  /// Get formatted duration
  String get formattedDuration {
    final minutes = duration ~/ 60;
    final seconds = duration % 60;
    if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    }
    return '${seconds}s';
  }

  /// Check if tone is for specific day
  bool isForDay(String day) {
    if (!isMultiple) return true;
    return daysOfWeek.contains(day.toLowerCase());
  }

  /// Get tone type based on properties
  ToneType get type {
    if (uploadedBy != 'admin' && uploadedBy != 'executor') {
      return ToneType.own;
    }
    return isMultiple ? ToneType.multiple : ToneType.single;
  }

  /// Check if tone is uploaded by admin
  bool get isAdminTone => uploadedBy == 'admin';

  /// Check if tone is uploaded by executor
  bool get isExecutorTone => uploadedBy == 'executor';

  /// Check if tone is user uploaded
  bool get isUserTone => !isAdminTone && !isExecutorTone;

  /// Get status color for UI
  String get statusColor {
    switch (status) {
      case ToneStatus.pending:
        return 'orange';
      case ToneStatus.approved:
        return 'green';
      case ToneStatus.rejected:
        return 'red';
    }
  }

  /// Get days of week as formatted string
  String get formattedDaysOfWeek {
    if (!isMultiple || daysOfWeek.isEmpty) return 'All days';
    return daysOfWeek.map((day) => day.capitalize()).join(', ');
  }
}

/// String extension for capitalize
extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1).toLowerCase()}';
  }
}
