import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../apps/buyer/buyer_routes.dart';
import '../../auth/bloc/auth_bloc.dart';
import '../../auth/bloc/auth_event.dart';
import '../../auth/bloc/auth_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BuyerDrawer extends StatelessWidget {
  const BuyerDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Drawer(
          child: Column(
            children: [
              UserAccountsDrawerHeader(
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                ),
                currentAccountPicture: CircleAvatar(
                  backgroundImage: state.maybeMap(
                    authenticated: (state) => state.user.photoUrl != null
                        ? NetworkImage(state.user.photoUrl!)
                        : null,
                    orElse: () => null,
                  ),
                  child: state.maybeMap(
                    authenticated: (state) => state.user.photoUrl == null
                        ? Text(
                            state.user.displayName.isNotEmpty
                                ? state.user.displayName
                                    .substring(0, 1)
                                    .toUpperCase()
                                : 'U',
                            style: theme.textTheme.headlineMedium?.copyWith(
                              color: theme.colorScheme.onPrimary,
                            ),
                          )
                        : null,
                    orElse: () => const Icon(Icons.person_outline_rounded),
                  ),
                ),
                accountName: Text(
                  state.maybeMap(
                    authenticated: (state) => state.user.displayName.isNotEmpty ? state.user.displayName : 'User',
                    orElse: () => 'Guest User',
                  ),
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                accountEmail: Text(
                  state.maybeMap(
                    authenticated: (state) => state.user.email,
                    orElse: () => '',
                  ),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onPrimary
                        .withValues(alpha: 204), // 0.8 * 255 = 204
                  ),
                ),
              ),
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    _DrawerItem(
                      iconAsset: 'assets/images/flavors/home.png',
                      label: 'Home',
                      onTap: () => _navigateTo(context, BuyerRoutes.home),
                    ),
                    _DrawerItem(
                      iconAsset: 'assets/images/flavors/calender.png',
                      label: 'Calendar',
                      onTap: () => _navigateTo(context, BuyerRoutes.calendar),
                    ),
                    _DrawerItem(
                      iconAsset: 'assets/images/flavors/alarm.png',
                      label: 'Alarms',
                      onTap: () => _navigateTo(context, BuyerRoutes.alarm),
                    ),
                    _DrawerItem(
                      iconAsset: 'assets/images/flavors/list.png',
                      label: 'Lists',
                      onTap: () =>
                          _navigateTo(context, BuyerRoutes.shoppingList),
                    ),
                    _DrawerItem(
                      iconAsset: 'assets/images/flavors/cart.png',
                      label: 'Cart',
                      onTap: () => _navigateTo(context, BuyerRoutes.cart),
                    ),
                    _DrawerItem(
                      icon: Icons.receipt_long_outlined,
                      label: 'Orders',
                      onTap: () => _navigateTo(context, BuyerRoutes.orders),
                    ),
                    _DrawerItem(
                      icon: Icons.book_online_outlined,
                      label: 'Bookings',
                      onTap: () => _navigateTo(context, BuyerRoutes.booking),
                    ),
                    _DrawerItem(
                      iconAsset: 'assets/icons/media.png',
                      label: 'Media',
                      onTap: () => _navigateTo(context, BuyerRoutes.media),
                    ),
                    const Divider(),
                    _DrawerItem(
                      icon: Icons.person_outline_rounded,
                      label: 'Profile',
                      onTap: () => _navigateTo(context, BuyerRoutes.profile),
                    ),
                    _DrawerItem(
                      icon: Icons.help_outline_rounded,
                      label: 'Help',
                      onTap: () => _navigateTo(context, BuyerRoutes.help),
                    ),
                    const Divider(),
                    _DrawerItem(
                      icon: Icons.logout_rounded,
                      label: 'Sign Out',
                      onTap: () {
                        context.read<AuthBloc>().add(const AuthEvent.signOut());
                        context.go(BuyerRoutes.login);
                      },
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Version 1.0.0',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface
                        .withValues(alpha: 153), // 0.6 * 255 = 153
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _navigateTo(BuildContext context, String route) {
    context.pop();
    context.go(route);
  }
}

class _DrawerItem extends StatelessWidget {
  final IconData? icon;
  final String? iconAsset;
  final String label;
  final VoidCallback onTap;

  const _DrawerItem({
    this.icon,
    this.iconAsset,
    required this.label,
    required this.onTap,
  }) : assert(icon != null || iconAsset != null,
            'Either icon or iconAsset must be provided');

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ListTile(
      leading: icon != null
          ? Icon(
              icon!,
              color: theme.colorScheme.onSurface
                  .withValues(alpha: 204), // 0.8 * 255 = 204
            )
          : Image.asset(
              iconAsset!,
              width: 24,
              height: 24,
              color: theme.colorScheme.onSurface
                  .withValues(alpha: 204), // 0.8 * 255 = 204
            ),
      title: Text(
        label,
        style: theme.textTheme.bodyLarge?.copyWith(
          color: theme.colorScheme.onSurface,
        ),
      ),
      onTap: onTap,
      dense: true,
      horizontalTitleGap: 0,
    );
  }
}
