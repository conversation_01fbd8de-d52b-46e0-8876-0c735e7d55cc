import 'package:flutter/widgets.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Enum representing the different app flavors
enum AppFlavor {
  buyer,
  seller,
  priest,
  technician,
  executor,
  admin,
  saviour,
  hospital,
  temple,
}

/// Extension to convert AppFlavor to string
extension AppFlavorExtension on AppFlavor {
  String get name {
    return toString().split('.').last;
  }
}

/// A centralized manager for handling app flavor detection and storage
class FlavorManager {
  static const String _flavorKey = 'app_flavor';
  static AppFlavor? _cachedFlavor;

  /// Detects the app flavor from various sources
  ///
  /// Order of precedence:
  /// 1. Command line arguments
  /// 2. Package name
  /// 3. Stored preference
  /// 4. Default to null (will show flavor selection dialog)
  static Future<AppFlavor?> detectFlavor() async {
    // Return cached flavor if available
    if (_cachedFlavor != null) {
      return _cachedFlavor;
    }

    AppFlavor? detectedFlavor;

    // 1. Try to detect from command line arguments
    try {
      final args = WidgetsBinding.instance.platformDispatcher.defaultRouteName
          .split('/');
      String flavorFromArgs = '';

      // Check if we have a flavor in the arguments
      for (final arg in args) {
        if (arg.startsWith('flavor=')) {
          flavorFromArgs = arg.substring('flavor='.length);
          break;
        }
      }

      if (flavorFromArgs.isNotEmpty) {
        detectedFlavor = _flavorFromString(flavorFromArgs);
        if (detectedFlavor != null) {
          debugPrint(
            'FlavorManager: Detected flavor from args: ${detectedFlavor.name}',
          );
          await _saveFlavor(detectedFlavor);
          return detectedFlavor;
        }
      }
    } catch (e) {
      debugPrint('FlavorManager: Error detecting flavor from args: $e');
    }

    // 2. Try to detect from package name
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final packageName = packageInfo.packageName.toLowerCase();

      if (packageName.contains('priest')) {
        detectedFlavor = AppFlavor.priest;
      } else if (packageName.contains('seller')) {
        detectedFlavor = AppFlavor.seller;
      } else if (packageName.contains('technician')) {
        detectedFlavor = AppFlavor.technician;
      } else if (packageName.contains('executor')) {
        detectedFlavor = AppFlavor.executor;
      } else if (packageName.contains('admin')) {
        detectedFlavor = AppFlavor.admin;
      } else if (packageName.contains('saviour')) {
        detectedFlavor = AppFlavor.saviour;
      } else if (packageName.contains('hospital')) {
        detectedFlavor = AppFlavor.hospital;
      } else if (packageName.contains('buyer')) {
        detectedFlavor = AppFlavor.buyer;
      }

      if (detectedFlavor != null) {
        debugPrint(
          'FlavorManager: Detected flavor from package name: ${detectedFlavor.name}',
        );
        await _saveFlavor(detectedFlavor);
        return detectedFlavor;
      }
    } catch (e) {
      debugPrint('FlavorManager: Error detecting flavor from package name: $e');
    }

    // 3. Try to get from shared preferences
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedFlavorString = prefs.getString(_flavorKey);

      if (savedFlavorString != null && savedFlavorString.isNotEmpty) {
        final savedFlavor = _flavorFromString(savedFlavorString);
        if (savedFlavor != null) {
          debugPrint('FlavorManager: Using saved flavor: ${savedFlavor.name}');
          _cachedFlavor = savedFlavor;
          return savedFlavor;
        }
      }
    } catch (e) {
      debugPrint(
        'FlavorManager: Error getting flavor from SharedPreferences: $e',
      );
    }

    // 4. No flavor detected
    debugPrint('FlavorManager: No flavor detected');
    return null;
  }

  /// Gets the current app flavor
  ///
  /// If no flavor is detected, returns null
  static Future<AppFlavor?> getCurrentFlavor() async {
    return await detectFlavor();
  }

  /// Sets the app flavor
  ///
  /// This is useful for testing or when the user selects a flavor
  static Future<void> setFlavor(AppFlavor flavor) async {
    await _saveFlavor(flavor);
  }

  /// Saves the flavor to SharedPreferences
  static Future<void> _saveFlavor(AppFlavor flavor) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_flavorKey, flavor.name);
      _cachedFlavor = flavor;
      debugPrint('FlavorManager: Saved flavor: ${flavor.name}');
    } catch (e) {
      debugPrint('FlavorManager: Error saving flavor: $e');
    }
  }

  /// Clears the saved flavor
  static Future<void> clearFlavor() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_flavorKey);
      _cachedFlavor = null;
      debugPrint('FlavorManager: Cleared flavor');
    } catch (e) {
      debugPrint('FlavorManager: Error clearing flavor: $e');
    }
  }

  /// Converts a string to an AppFlavor
  static AppFlavor? _flavorFromString(String flavorString) {
    final lowerFlavor = flavorString.toLowerCase();

    if (lowerFlavor == 'buyer') return AppFlavor.buyer;
    if (lowerFlavor == 'seller') return AppFlavor.seller;
    if (lowerFlavor == 'priest') return AppFlavor.priest;
    if (lowerFlavor == 'technician') return AppFlavor.technician;
    if (lowerFlavor == 'executor') return AppFlavor.executor;
    if (lowerFlavor == 'admin') return AppFlavor.admin;
    if (lowerFlavor == 'saviour') return AppFlavor.saviour;
    if (lowerFlavor == 'hospital') return AppFlavor.hospital;

    return null;
  }

  /// Gets the app name for a flavor
  static String getAppName(AppFlavor flavor) {
    switch (flavor) {
      case AppFlavor.buyer:
        return 'Shivish Buyer';
      case AppFlavor.seller:
        return 'Shivish Seller';
      case AppFlavor.priest:
        return 'Shivish Priest';
      case AppFlavor.technician:
        return 'Shivish Technician';
      case AppFlavor.executor:
        return 'Shivish Executor';
      case AppFlavor.admin:
        return 'Shivish Admin';
      case AppFlavor.saviour:
        return 'Saviour Delivery';
      case AppFlavor.hospital:
        return 'Hospital Management';
      case AppFlavor.temple:
        return 'Temple Management';
    }
  }

  /// Gets the initial route for a flavor
  static String getInitialRoute(AppFlavor flavor) {
    switch (flavor) {
      case AppFlavor.buyer:
        return '/buyer/language-selection';
      case AppFlavor.seller:
        return '/seller/language-selection';
      case AppFlavor.priest:
        return '/priest/language-selection';
      case AppFlavor.technician:
        return '/technician/language-selection';
      case AppFlavor.executor:
        return '/executor/language-selection';
      case AppFlavor.admin:
        return '/admin/login'; // Admin doesn't need language selection
      case AppFlavor.saviour:
        return '/saviour/splash'; // Start with splash screen
      case AppFlavor.hospital:
        return '/hospital/splash'; // Start with splash screen
      case AppFlavor.temple:
        return '/temple/language-selection';
    }
  }
}
