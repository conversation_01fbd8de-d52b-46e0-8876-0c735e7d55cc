import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'product_analytics_state.dart';
import '../../../../../shared/database/services/database_service.dart';
import '../../../../../shared/database/config/database_config.dart';

final productAnalyticsCubitProvider = StateNotifierProvider<
    ProductAnalyticsCubit, AsyncValue<ProductAnalyticsState>>(
  (ref) => ProductAnalyticsCubit(),
);

class ProductAnalyticsCubit
    extends StateNotifier<AsyncValue<ProductAnalyticsState>> {
  final DatabaseService _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
  final SupabaseClient _supabase = Supabase.instance.client;

  ProductAnalyticsCubit() : super(const AsyncValue.loading()) {
    refreshAnalytics();
  }

  Future<void> refreshAnalytics() async {
    state = const AsyncValue.loading();
    try {
      // Get the current user ID
      final userId = await _getCurrentUserId();
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Fetch sales data from hybrid database
      final salesData = await _fetchSalesData(userId);

      // Fetch top products data from hybrid database
      final topProductsData = await _fetchTopProductsData(userId);

      // Fetch inventory status from hybrid database
      final inventoryStatus = await _fetchInventoryStatus(userId);

      // Calculate metrics
      final metrics = await _calculateMetrics(userId);

      state = AsyncValue.data(
        ProductAnalyticsState(
          totalSales: metrics['totalSales'] ?? 0.0,
          totalOrders: metrics['totalOrders'] ?? 0,
          averageOrderValue: metrics['averageOrderValue'] ?? 0.0,
          conversionRate: metrics['conversionRate'] ?? 0.0,
          salesTrend: metrics['salesTrend'] ?? 0.0,
          ordersTrend: metrics['ordersTrend'] ?? 0.0,
          aovTrend: metrics['aovTrend'] ?? 0.0,
          conversionTrend: metrics['conversionTrend'] ?? 0.0,
          salesData: salesData,
          topProductsData: topProductsData,
          inventoryStatus: inventoryStatus,
        ),
      );
    } catch (error, stackTrace) {
      debugPrint('Error refreshing analytics: $error');
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<String?> _getCurrentUserId() async {
    try {
      final user = _supabase.auth.currentUser;
      return user?.id;
    } catch (e) {
      debugPrint('Error getting current user ID: $e');
      return null;
    }
  }

  Future<List<FlSpot>> _fetchSalesData(String userId) async {
    try {
      final salesData = await _databaseService.getAll(
        'seller_analytics',
        where: 'seller_id = @param0 AND analytics_type = @param1',
        whereParams: [userId, 'sales'],
        limit: 1,
      );

      if (salesData.isEmpty) {
        return _generateDefaultSalesData();
      }

      final data = salesData.first;
      final dailySales = data['daily_sales'] as List<dynamic>?;

      if (dailySales == null || dailySales.isEmpty) {
        return _generateDefaultSalesData();
      }

      return dailySales.asMap().entries.map((entry) {
        final index = entry.key;
        final value = entry.value as num;
        return FlSpot(index.toDouble(), value.toDouble());
      }).toList();
    } catch (e) {
      debugPrint('Error fetching sales data: $e');
      return _generateDefaultSalesData();
    }
  }

  List<FlSpot> _generateDefaultSalesData() {
    return List.generate(
      7,
      (index) => FlSpot(
        index.toDouble(),
        (1000 + index * 500).toDouble(),
      ),
    );
  }

  Future<List<BarChartGroupData>> _fetchTopProductsData(String userId) async {
    try {
      final productsData = await _databaseService.getAll(
        'seller_analytics',
        where: 'seller_id = @param0 AND analytics_type = @param1',
        whereParams: [userId, 'products'],
        limit: 1,
      );

      if (productsData.isEmpty) {
        return _generateDefaultTopProductsData();
      }

      final data = productsData.first;
      final topProducts = data['top_products'] as List<dynamic>?;

      if (topProducts == null || topProducts.isEmpty) {
        return _generateDefaultTopProductsData();
      }

      return topProducts.asMap().entries.map((entry) {
        final index = entry.key;
        final product = entry.value as Map<String, dynamic>;
        final sales = (product['sales'] as num).toDouble();

        return BarChartGroupData(
          x: index,
          barRods: [
            BarChartRodData(
              toY: sales,
              color: Colors.blue,
              width: 16,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(4),
              ),
            ),
          ],
        );
      }).toList();
    } catch (e) {
      debugPrint('Error fetching top products data: $e');
      return _generateDefaultTopProductsData();
    }
  }

  List<BarChartGroupData> _generateDefaultTopProductsData() {
    return List.generate(
      5,
      (index) => BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: (1000 + index * 200).toDouble(),
            color: Colors.blue,
            width: 16,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Future<List<InventoryStatus>> _fetchInventoryStatus(String userId) async {
    try {
      final products = await _databaseService.getAll(
        'products',
        where: 'seller_id = @param0',
        whereParams: [userId],
        orderBy: 'stock ASC',
        limit: 5,
      );

      if (products.isEmpty) {
        return _generateDefaultInventoryStatus();
      }

      return products.map((data) {
        final name = data['name'] as String? ?? 'Unknown Product';
        final stock = (data['stock'] as num?)?.toInt() ?? 0;
        final maxStock = (data['max_stock'] as num?)?.toInt() ?? 100;

        return InventoryStatus(
          productName: name,
          currentStock: stock,
          stockPercentage: maxStock > 0 ? stock / maxStock : 0,
        );
      }).toList();
    } catch (e) {
      debugPrint('Error fetching inventory status: $e');
      return _generateDefaultInventoryStatus();
    }
  }

  List<InventoryStatus> _generateDefaultInventoryStatus() {
    return List.generate(
      5,
      (index) => InventoryStatus(
        productName: 'Product ${index + 1}',
        currentStock: 100 - index * 20,
        stockPercentage: (100 - index * 20) / 100,
      ),
    );
  }

  Future<Map<String, dynamic>> _calculateMetrics(String userId) async {
    try {
      final metricsData = await _databaseService.getAll(
        'seller_analytics',
        where: 'seller_id = @param0 AND analytics_type = @param1',
        whereParams: [userId, 'metrics'],
        limit: 1,
      );

      if (metricsData.isEmpty) {
        return _generateDefaultMetrics();
      }

      final data = metricsData.first;
      return {
        'totalSales': data['total_sales'] ?? 0.0,
        'totalOrders': data['total_orders'] ?? 0,
        'averageOrderValue': data['average_order_value'] ?? 0.0,
        'conversionRate': data['conversion_rate'] ?? 0.0,
        'salesTrend': data['sales_trend'] ?? 0.0,
        'ordersTrend': data['orders_trend'] ?? 0.0,
        'aovTrend': data['aov_trend'] ?? 0.0,
        'conversionTrend': data['conversion_trend'] ?? 0.0,
      };
    } catch (e) {
      debugPrint('Error calculating metrics: $e');
      return _generateDefaultMetrics();
    }
  }

  Map<String, dynamic> _generateDefaultMetrics() {
    return {
      'totalSales': 5000.0,
      'totalOrders': 100,
      'averageOrderValue': 50.0,
      'conversionRate': 2.5,
      'salesTrend': 5.2,
      'ordersTrend': 3.8,
      'aovTrend': 1.4,
      'conversionTrend': -0.5,
    };
  }
}
