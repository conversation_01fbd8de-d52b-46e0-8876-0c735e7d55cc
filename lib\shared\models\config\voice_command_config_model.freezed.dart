// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_command_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VoiceCommandConfigModel {

 bool get enabled; String get wakeWord; String get language; double get sensitivity; List<String> get supportedCommands; Map<String, String> get commandMappings;
/// Create a copy of VoiceCommandConfigModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VoiceCommandConfigModelCopyWith<VoiceCommandConfigModel> get copyWith => _$VoiceCommandConfigModelCopyWithImpl<VoiceCommandConfigModel>(this as VoiceCommandConfigModel, _$identity);

  /// Serializes this VoiceCommandConfigModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VoiceCommandConfigModel&&(identical(other.enabled, enabled) || other.enabled == enabled)&&(identical(other.wakeWord, wakeWord) || other.wakeWord == wakeWord)&&(identical(other.language, language) || other.language == language)&&(identical(other.sensitivity, sensitivity) || other.sensitivity == sensitivity)&&const DeepCollectionEquality().equals(other.supportedCommands, supportedCommands)&&const DeepCollectionEquality().equals(other.commandMappings, commandMappings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,enabled,wakeWord,language,sensitivity,const DeepCollectionEquality().hash(supportedCommands),const DeepCollectionEquality().hash(commandMappings));

@override
String toString() {
  return 'VoiceCommandConfigModel(enabled: $enabled, wakeWord: $wakeWord, language: $language, sensitivity: $sensitivity, supportedCommands: $supportedCommands, commandMappings: $commandMappings)';
}


}

/// @nodoc
abstract mixin class $VoiceCommandConfigModelCopyWith<$Res>  {
  factory $VoiceCommandConfigModelCopyWith(VoiceCommandConfigModel value, $Res Function(VoiceCommandConfigModel) _then) = _$VoiceCommandConfigModelCopyWithImpl;
@useResult
$Res call({
 bool enabled, String wakeWord, String language, double sensitivity, List<String> supportedCommands, Map<String, String> commandMappings
});




}
/// @nodoc
class _$VoiceCommandConfigModelCopyWithImpl<$Res>
    implements $VoiceCommandConfigModelCopyWith<$Res> {
  _$VoiceCommandConfigModelCopyWithImpl(this._self, this._then);

  final VoiceCommandConfigModel _self;
  final $Res Function(VoiceCommandConfigModel) _then;

/// Create a copy of VoiceCommandConfigModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? enabled = null,Object? wakeWord = null,Object? language = null,Object? sensitivity = null,Object? supportedCommands = null,Object? commandMappings = null,}) {
  return _then(_self.copyWith(
enabled: null == enabled ? _self.enabled : enabled // ignore: cast_nullable_to_non_nullable
as bool,wakeWord: null == wakeWord ? _self.wakeWord : wakeWord // ignore: cast_nullable_to_non_nullable
as String,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,sensitivity: null == sensitivity ? _self.sensitivity : sensitivity // ignore: cast_nullable_to_non_nullable
as double,supportedCommands: null == supportedCommands ? _self.supportedCommands : supportedCommands // ignore: cast_nullable_to_non_nullable
as List<String>,commandMappings: null == commandMappings ? _self.commandMappings : commandMappings // ignore: cast_nullable_to_non_nullable
as Map<String, String>,
  ));
}

}


/// Adds pattern-matching-related methods to [VoiceCommandConfigModel].
extension VoiceCommandConfigModelPatterns on VoiceCommandConfigModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VoiceCommandConfigModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VoiceCommandConfigModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VoiceCommandConfigModel value)  $default,){
final _that = this;
switch (_that) {
case _VoiceCommandConfigModel():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VoiceCommandConfigModel value)?  $default,){
final _that = this;
switch (_that) {
case _VoiceCommandConfigModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool enabled,  String wakeWord,  String language,  double sensitivity,  List<String> supportedCommands,  Map<String, String> commandMappings)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VoiceCommandConfigModel() when $default != null:
return $default(_that.enabled,_that.wakeWord,_that.language,_that.sensitivity,_that.supportedCommands,_that.commandMappings);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool enabled,  String wakeWord,  String language,  double sensitivity,  List<String> supportedCommands,  Map<String, String> commandMappings)  $default,) {final _that = this;
switch (_that) {
case _VoiceCommandConfigModel():
return $default(_that.enabled,_that.wakeWord,_that.language,_that.sensitivity,_that.supportedCommands,_that.commandMappings);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool enabled,  String wakeWord,  String language,  double sensitivity,  List<String> supportedCommands,  Map<String, String> commandMappings)?  $default,) {final _that = this;
switch (_that) {
case _VoiceCommandConfigModel() when $default != null:
return $default(_that.enabled,_that.wakeWord,_that.language,_that.sensitivity,_that.supportedCommands,_that.commandMappings);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VoiceCommandConfigModel implements VoiceCommandConfigModel {
  const _VoiceCommandConfigModel({required this.enabled, required this.wakeWord, required this.language, required this.sensitivity, required final  List<String> supportedCommands, required final  Map<String, String> commandMappings}): _supportedCommands = supportedCommands,_commandMappings = commandMappings;
  factory _VoiceCommandConfigModel.fromJson(Map<String, dynamic> json) => _$VoiceCommandConfigModelFromJson(json);

@override final  bool enabled;
@override final  String wakeWord;
@override final  String language;
@override final  double sensitivity;
 final  List<String> _supportedCommands;
@override List<String> get supportedCommands {
  if (_supportedCommands is EqualUnmodifiableListView) return _supportedCommands;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_supportedCommands);
}

 final  Map<String, String> _commandMappings;
@override Map<String, String> get commandMappings {
  if (_commandMappings is EqualUnmodifiableMapView) return _commandMappings;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_commandMappings);
}


/// Create a copy of VoiceCommandConfigModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VoiceCommandConfigModelCopyWith<_VoiceCommandConfigModel> get copyWith => __$VoiceCommandConfigModelCopyWithImpl<_VoiceCommandConfigModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VoiceCommandConfigModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VoiceCommandConfigModel&&(identical(other.enabled, enabled) || other.enabled == enabled)&&(identical(other.wakeWord, wakeWord) || other.wakeWord == wakeWord)&&(identical(other.language, language) || other.language == language)&&(identical(other.sensitivity, sensitivity) || other.sensitivity == sensitivity)&&const DeepCollectionEquality().equals(other._supportedCommands, _supportedCommands)&&const DeepCollectionEquality().equals(other._commandMappings, _commandMappings));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,enabled,wakeWord,language,sensitivity,const DeepCollectionEquality().hash(_supportedCommands),const DeepCollectionEquality().hash(_commandMappings));

@override
String toString() {
  return 'VoiceCommandConfigModel(enabled: $enabled, wakeWord: $wakeWord, language: $language, sensitivity: $sensitivity, supportedCommands: $supportedCommands, commandMappings: $commandMappings)';
}


}

/// @nodoc
abstract mixin class _$VoiceCommandConfigModelCopyWith<$Res> implements $VoiceCommandConfigModelCopyWith<$Res> {
  factory _$VoiceCommandConfigModelCopyWith(_VoiceCommandConfigModel value, $Res Function(_VoiceCommandConfigModel) _then) = __$VoiceCommandConfigModelCopyWithImpl;
@override @useResult
$Res call({
 bool enabled, String wakeWord, String language, double sensitivity, List<String> supportedCommands, Map<String, String> commandMappings
});




}
/// @nodoc
class __$VoiceCommandConfigModelCopyWithImpl<$Res>
    implements _$VoiceCommandConfigModelCopyWith<$Res> {
  __$VoiceCommandConfigModelCopyWithImpl(this._self, this._then);

  final _VoiceCommandConfigModel _self;
  final $Res Function(_VoiceCommandConfigModel) _then;

/// Create a copy of VoiceCommandConfigModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? enabled = null,Object? wakeWord = null,Object? language = null,Object? sensitivity = null,Object? supportedCommands = null,Object? commandMappings = null,}) {
  return _then(_VoiceCommandConfigModel(
enabled: null == enabled ? _self.enabled : enabled // ignore: cast_nullable_to_non_nullable
as bool,wakeWord: null == wakeWord ? _self.wakeWord : wakeWord // ignore: cast_nullable_to_non_nullable
as String,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,sensitivity: null == sensitivity ? _self.sensitivity : sensitivity // ignore: cast_nullable_to_non_nullable
as double,supportedCommands: null == supportedCommands ? _self._supportedCommands : supportedCommands // ignore: cast_nullable_to_non_nullable
as List<String>,commandMappings: null == commandMappings ? _self._commandMappings : commandMappings // ignore: cast_nullable_to_non_nullable
as Map<String, String>,
  ));
}


}

// dart format on
