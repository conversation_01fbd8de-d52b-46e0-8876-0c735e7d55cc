import 'package:freezed_annotation/freezed_annotation.dart';

part 'campaign_model.freezed.dart';
part 'campaign_model.g.dart';

enum CampaignStatus {
  draft,
  scheduled,
  active,
  paused,
  completed,
  cancelled,
}

enum CampaignType {
  productPromotion,
  storePromotion,
  seasonalSale,
  flashSale,
}

@freezed
sealed class CampaignModel with _$CampaignModel {
  const factory CampaignModel({
    required String id,
    required String name,
    required String description,
    required CampaignType type,
    required CampaignStatus status,
    required double budget,
    required double spent,
    required DateTime startDate,
    required DateTime endDate,
    required int impressions,
    required int clicks,
    required int conversions,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isDeleted,
  }) = _CampaignModel;

  factory CampaignModel.fromJson(Map<String, dynamic> json) =>
      _$CampaignModelFromJson(json);
}
