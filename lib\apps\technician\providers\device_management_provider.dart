import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/models/device/device_model.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final deviceManagementProvider =
    AsyncNotifierProvider<DeviceManagementNotifier, List<DeviceModel>>(() {
  return DeviceManagementNotifier();
});

class DeviceManagementNotifier extends AsyncNotifier<List<DeviceModel>> {
  late final DatabaseService _databaseService;
  static const String _devicesCollection = 'user_devices';

  @override
  Future<List<DeviceModel>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadDevices();
  }

  Future<List<DeviceModel>> _loadDevices() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final devices = await _databaseService.getAll(
        _devicesCollection,
        where: 'user_id = ? AND is_deleted = ?',
        whereParams: [userId, false],
        orderBy: 'last_active DESC',
      );
      return devices
          .map((data) => DeviceModel.fromJson(data))
          .toList();
    } catch (e) {
      debugPrint('Failed to load devices: $e');
      throw Exception('Failed to load devices: $e');
    }
  }

  Future<void> removeDevice(String deviceId) async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      await _databaseService.update(_devicesCollection, deviceId, {
        'is_deleted': true,
        'deleted_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
      state = AsyncData(state.value!.where((d) => d.id != deviceId).toList());
    } catch (e) {
      debugPrint('Failed to remove device: $e');
      throw Exception('Failed to remove device: $e');
    }
  }

  Future<void> signOutFromAllDevices() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final devices = state.value!;
      final currentDevice = devices.firstWhere((d) => d.isCurrentDevice);

      // Soft delete all devices except current
      for (var device in devices) {
        if (!device.isCurrentDevice) {
          await _databaseService.update(_devicesCollection, device.id, {
            'is_deleted': true,
            'deleted_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          });
        }
      }

      state = AsyncData([currentDevice]);
    } catch (e) {
      debugPrint('Failed to sign out from all devices: $e');
      throw Exception('Failed to sign out from all devices: $e');
    }
  }
}
