import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../analytics/analytics_service.dart';

class AIAssistantService {
  final AnalyticsService _analytics;
  final FlutterSecureStorage _storage;
  static const String _baseUrl = 'https://api.openai.com/v1';
  static const String _apiKeyKey = 'openai_api_key';

  AIAssistantService(this._analytics) : _storage = const FlutterSecureStorage();

  Future<void> setApiKey(String apiKey) async {
    await _storage.write(key: _apiKeyKey, value: apiKey);
  }

  Future<String?> getApiKey() async {
    return await _storage.read(key: _apiKeyKey);
  }

  Future<String?> generateResponse(String prompt) async {
    try {
      final apiKey = await getApiKey();
      if (apiKey == null) {
        throw Exception('OpenAI API key not found');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode({
          'model': 'gpt-3.5-turbo',
          'messages': [
            {
              'role': 'user',
              'content': prompt,
            }
          ],
          'temperature': 0.7,
          'max_tokens': 150,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'] as String;
        _logAIInteraction(prompt, content);
        return content;
      } else {
        throw Exception('Failed to generate response: ${response.statusCode}');
      }
    } catch (e) {
      _handleError('Error generating AI response: $e');
      return null;
    }
  }

  Future<String?> analyzeUserIntent(String userInput) async {
    try {
      final apiKey = await getApiKey();
      if (apiKey == null) {
        throw Exception('OpenAI API key not found');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode({
          'model': 'gpt-3.5-turbo',
          'messages': [
            {
              'role': 'system',
              'content':
                  'Analyze the user intent and categorize it into one of these types: SEARCH, PURCHASE, BOOKING, HELP, SETTINGS, or OTHER. Return only the category.',
            },
            {
              'role': 'user',
              'content': userInput,
            }
          ],
          'temperature': 0.3,
          'max_tokens': 10,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'] as String;
        _logAIInteraction(userInput, content, type: 'intent_analysis');
        return content.trim();
      } else {
        throw Exception('Failed to analyze intent: ${response.statusCode}');
      }
    } catch (e) {
      _handleError('Error analyzing user intent: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> extractEntities(String text) async {
    try {
      final apiKey = await getApiKey();
      if (apiKey == null) {
        throw Exception('OpenAI API key not found');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode({
          'model': 'gpt-3.5-turbo',
          'messages': [
            {
              'role': 'system',
              'content':
                  'Extract entities like product names, quantities, dates, times, and locations from the text. Return as JSON.',
            },
            {
              'role': 'user',
              'content': text,
            }
          ],
          'temperature': 0.3,
          'max_tokens': 150,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'] as String;
        _logAIInteraction(text, content, type: 'entity_extraction');
        return jsonDecode(content);
      } else {
        throw Exception('Failed to extract entities: ${response.statusCode}');
      }
    } catch (e) {
      _handleError('Error extracting entities: $e');
      return null;
    }
  }

  void _handleError(String error) {
    debugPrint('AIAssistantService Error: $error');
    _analytics.logError(
      error: error,
      parameters: {'service': 'ai_assistant'},
    );
  }

  void _logAIInteraction(String input, String output, {String type = 'chat'}) {
    _analytics.logEvent(
      name: 'ai_interaction',
      parameters: {
        'type': type,
        'input_length': input.length,
        'output_length': output.length,
      },
    );
  }
}
