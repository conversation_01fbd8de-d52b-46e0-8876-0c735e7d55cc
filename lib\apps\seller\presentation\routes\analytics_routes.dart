import 'package:flutter/material.dart';
import 'package:shivish/apps/seller/screens/analytics/analytics_dashboard_screen.dart';
import 'package:shivish/apps/seller/screens/analytics/store_performance_screen.dart';
import 'package:shivish/apps/seller/screens/product_analytics/product_analytics_screen.dart';
import 'package:shivish/apps/seller/screens/analytics/customer_analytics_screen.dart';

class AnalyticsRoutes {
  static const String dashboard = '/analytics/dashboard';
  static const String performance = '/analytics/performance';
  static const String products = '/analytics/products';
  static const String customers = '/analytics/customers';

  static Map<String, WidgetBuilder> getRoutes() {
    return {
      dashboard: (context) => const AnalyticsDashboardScreen(),
      performance: (context) => const StorePerformanceScreen(),
      products: (context) => const ProductAnalyticsScreen(),
      customers: (context) => const CustomerAnalyticsScreen(),
    };
  }

  static void navigateToDashboard(BuildContext context) {
    Navigator.pushNamed(context, dashboard);
  }

  static void navigateToPerformance(BuildContext context) {
    Navigator.pushNamed(context, performance);
  }

  static void navigateToProducts(BuildContext context) {
    Navigator.pushNamed(context, products);
  }

  static void navigateToCustomers(BuildContext context) {
    Navigator.pushNamed(context, customers);
  }
}
