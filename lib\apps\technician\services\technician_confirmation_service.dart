import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import '../../../shared/widgets/dialogs/confirmation_dialog.dart';

/// Technician-specific override for the confirmation service
/// This ensures that the technician app shows the confirmation dialog
/// without affecting other app flavors
class TechnicianConfirmationService {
  static const String _confirmationKey =
      'technician_sanathana_dharma_confirmed';
  static const String _confirmationDateKey =
      'technician_sanathana_dharma_confirmed_date';
  static const int _confirmationValidityDays = 30; // Reconfirm every 30 days

  /// Checks if the user has already confirmed
  static Future<bool> hasConfirmed() async {
    // Get the confirmation status from SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final isConfirmed = prefs.getBool(_confirmationKey) ?? false;

    debugPrint('TechnicianConfirmationService: hasConfirmed() = $isConfirmed');

    // Check if the confirmation has expired
    if (isConfirmed) {
      final confirmationDateStr = prefs.getString(_confirmationDateKey);
      if (confirmationDateStr == null) {
        debugPrint('TechnicianConfirmationService: No confirmation date found');
        return false;
      }

      try {
        final confirmationDate = DateTime.parse(confirmationDateStr);
        final now = DateTime.now();
        final difference = now.difference(confirmationDate).inDays;

        debugPrint(
            'TechnicianConfirmationService: Days since confirmation: $difference');

        // If the confirmation is older than the validity period, it's expired
        if (difference > _confirmationValidityDays) {
          debugPrint('TechnicianConfirmationService: Confirmation expired');
          return false;
        }

        debugPrint('TechnicianConfirmationService: Confirmation is valid');
        return true;
      } catch (e) {
        debugPrint('TechnicianConfirmationService: Error parsing date: $e');
        return false;
      }
    }

    return false;
  }

  /// Saves the user's confirmation
  static Future<void> saveConfirmation(bool confirmed) async {
    debugPrint('TechnicianConfirmationService: saveConfirmation($confirmed)');
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_confirmationKey, confirmed);

    if (confirmed) {
      // Save the confirmation date
      final now = DateTime.now().toIso8601String();
      await prefs.setString(_confirmationDateKey, now);
      debugPrint(
          'TechnicianConfirmationService: Saved confirmation date: $now');
    }

    // Verify the save was successful
    final savedValue = prefs.getBool(_confirmationKey);
    debugPrint(
        'TechnicianConfirmationService: Verification - saved value is $savedValue');
  }

  /// Shows the confirmation dialog if needed
  /// Returns true if the user has confirmed, false otherwise
  static Future<bool> checkConfirmation(BuildContext context) async {
    debugPrint('TechnicianConfirmationService: checkConfirmation() called');

    // Check if the user has already confirmed
    final hasAlreadyConfirmed = await hasConfirmed();
    if (hasAlreadyConfirmed) {
      debugPrint('TechnicianConfirmationService: User already confirmed');
      return true;
    }

    debugPrint('TechnicianConfirmationService: Showing confirmation dialog');
    try {
      // Show the confirmation dialog directly
      final confirmed = await SanathanaDharmaConfirmationDialog.show(context);
      debugPrint('TechnicianConfirmationService: Dialog result: $confirmed');

      // If the user confirmed, save the confirmation
      if (confirmed == true) {
        debugPrint(
            'TechnicianConfirmationService: User confirmed, saving confirmation');
        await saveConfirmation(true);

        // Force a small delay to ensure the confirmation is saved
        await Future.delayed(const Duration(milliseconds: 100));

        // Verify the confirmation was saved
        final verifyConfirmed = await hasConfirmed();
        debugPrint(
            'TechnicianConfirmationService: Verification - hasConfirmed() = $verifyConfirmed');

        return true;
      } else {
        // If the user rejected, exit the app
        debugPrint('TechnicianConfirmationService: User rejected, exiting app');
        SystemNavigator.pop();
        return false;
      }
    } catch (e) {
      debugPrint('TechnicianConfirmationService: Error showing dialog: $e');
      return false;
    }
  }
}
