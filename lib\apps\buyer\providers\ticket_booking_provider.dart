import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/easymytrip_service.dart';
import '../models/ticket_booking_models.dart';
import '../../../shared/providers/api_config_provider.dart';

/// Provider for EasyMyTrip service
final easyMyTripServiceProvider = Provider<EasyMyTripService>((ref) {
  final apiConfigService = ref.watch(apiConfigServiceProvider);
  return EasyMyTripService(apiConfigService: apiConfigService);
});

/// Provider for flight search results
final flightSearchProvider =
    StateNotifierProvider<
      FlightSearchNotifier,
      AsyncValue<FlightSearchResponse?>
    >((ref) {
      final service = ref.watch(easyMyTripServiceProvider);
      return FlightSearchNotifier(service);
    });

/// Provider for bus search results
final busSearchProvider =
    StateNotifierProvider<BusSearchNotifier, AsyncValue<BusSearchResponse?>>((
      ref,
    ) {
      final service = ref.watch(easyMyTripServiceProvider);
      return BusSearchNotifier(service);
    });

/// Provider for hotel search results
final hotelSearchProvider =
    StateNotifierProvider<
      HotelSearchNotifier,
      AsyncValue<HotelSearchResponse?>
    >((ref) {
      final service = ref.watch(easyMyTripServiceProvider);
      return HotelSearchNotifier(service);
    });

/// Provider for train search results
final trainSearchProvider =
    StateNotifierProvider<
      TrainSearchNotifier,
      AsyncValue<TrainSearchResponse?>
    >((ref) {
      final service = ref.watch(easyMyTripServiceProvider);
      return TrainSearchNotifier(service);
    });

/// Provider for booking status
final bookingStatusProvider =
    StateNotifierProvider.family<
      BookingStatusNotifier,
      AsyncValue<BookingStatus?>,
      String
    >((ref, bookingId) {
      final service = ref.watch(easyMyTripServiceProvider);
      return BookingStatusNotifier(service, bookingId);
    });

// STATE NOTIFIERS

class FlightSearchNotifier
    extends StateNotifier<AsyncValue<FlightSearchResponse?>> {
  final EasyMyTripService _service;

  FlightSearchNotifier(this._service) : super(const AsyncValue.data(null));

  Future<void> searchFlights(FlightSearchRequest request) async {
    state = const AsyncValue.loading();
    try {
      final response = await _service.searchFlights(request);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void clearResults() {
    state = const AsyncValue.data(null);
  }
}

class BusSearchNotifier extends StateNotifier<AsyncValue<BusSearchResponse?>> {
  final EasyMyTripService _service;

  BusSearchNotifier(this._service) : super(const AsyncValue.data(null));

  Future<void> searchBuses(BusSearchRequest request) async {
    state = const AsyncValue.loading();
    try {
      final response = await _service.searchBuses(request);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void clearResults() {
    state = const AsyncValue.data(null);
  }
}

class HotelSearchNotifier
    extends StateNotifier<AsyncValue<HotelSearchResponse?>> {
  final EasyMyTripService _service;

  HotelSearchNotifier(this._service) : super(const AsyncValue.data(null));

  Future<void> searchHotels(HotelSearchRequest request) async {
    state = const AsyncValue.loading();
    try {
      final response = await _service.searchHotels(request);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void clearResults() {
    state = const AsyncValue.data(null);
  }
}

class TrainSearchNotifier
    extends StateNotifier<AsyncValue<TrainSearchResponse?>> {
  final EasyMyTripService _service;

  TrainSearchNotifier(this._service) : super(const AsyncValue.data(null));

  Future<void> searchTrains(TrainSearchRequest request) async {
    state = const AsyncValue.loading();
    try {
      final response = await _service.searchTrains(request);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void clearResults() {
    state = const AsyncValue.data(null);
  }
}

class BookingStatusNotifier extends StateNotifier<AsyncValue<BookingStatus?>> {
  final EasyMyTripService _service;
  final String _bookingId;

  BookingStatusNotifier(this._service, this._bookingId)
    : super(const AsyncValue.data(null));

  Future<void> fetchStatus() async {
    state = const AsyncValue.loading();
    try {
      final status = await _service.getBookingStatus(_bookingId);
      state = AsyncValue.data(status);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> cancelBooking(String reason) async {
    try {
      await _service.cancelBooking(_bookingId, reason);
      // Refresh status after cancellation
      await fetchStatus();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Provider for managing booking process
final bookingProcessProvider =
    StateNotifierProvider<BookingProcessNotifier, BookingProcessState>((ref) {
      final service = ref.watch(easyMyTripServiceProvider);
      return BookingProcessNotifier(service);
    });

class BookingProcessNotifier extends StateNotifier<BookingProcessState> {
  final EasyMyTripService _service;

  BookingProcessNotifier(this._service) : super(BookingProcessState.initial());

  Future<void> bookFlight(FlightBookingRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response = await _service.bookFlight(request);
      state = state.copyWith(
        isLoading: false,
        bookingResponse: response,
        bookingType: BookingType.flight,
      );
    } catch (error) {
      state = state.copyWith(isLoading: false, error: error.toString());
    }
  }

  Future<void> bookBus(BusBookingRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response = await _service.bookBus(request);
      state = state.copyWith(
        isLoading: false,
        bookingResponse: response,
        bookingType: BookingType.bus,
      );
    } catch (error) {
      state = state.copyWith(isLoading: false, error: error.toString());
    }
  }

  Future<void> bookHotel(HotelBookingRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response = await _service.bookHotel(request);
      state = state.copyWith(
        isLoading: false,
        bookingResponse: response,
        bookingType: BookingType.hotel,
      );
    } catch (error) {
      state = state.copyWith(isLoading: false, error: error.toString());
    }
  }

  Future<void> bookTrain(TrainBookingRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response = await _service.bookTrain(request);
      state = state.copyWith(
        isLoading: false,
        bookingResponse: response,
        bookingType: BookingType.train,
      );
    } catch (error) {
      state = state.copyWith(isLoading: false, error: error.toString());
    }
  }

  void clearBooking() {
    state = BookingProcessState.initial();
  }
}

enum BookingType { flight, bus, hotel, train }

class BookingProcessState {
  final bool isLoading;
  final BookingResponse? bookingResponse;
  final BookingType? bookingType;
  final String? error;

  BookingProcessState({
    required this.isLoading,
    this.bookingResponse,
    this.bookingType,
    this.error,
  });

  factory BookingProcessState.initial() =>
      BookingProcessState(isLoading: false);

  BookingProcessState copyWith({
    bool? isLoading,
    BookingResponse? bookingResponse,
    BookingType? bookingType,
    String? error,
  }) {
    return BookingProcessState(
      isLoading: isLoading ?? this.isLoading,
      bookingResponse: bookingResponse ?? this.bookingResponse,
      bookingType: bookingType ?? this.bookingType,
      error: error ?? this.error,
    );
  }
}
