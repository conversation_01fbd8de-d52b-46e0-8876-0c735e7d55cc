import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/commission/commission_bloc.dart';
import 'package:shivish/apps/admin/widgets/commission_list_item.dart';
import 'package:shivish/apps/admin/widgets/commission_form_dialog.dart';
import 'package:shivish/shared/models/commission.dart';

class CommissionManagementScreen extends StatelessWidget {
  const CommissionManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Commission Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCommissionFormDialog(context),
          ),
        ],
      ),
      body: BlocBuilder<CommissionBloc, CommissionState>(
        builder: (context, state) {
          return state.when(
            initial: () => const Center(child: Text('No commissions yet')),
            loading: () => const Center(child: CircularProgressIndicator()),
            loaded: (commissions) => _buildCommissionList(context, commissions),
            calculated: (amount) => const Center(
              child: Text('Commission calculated successfully'),
            ),
            error: (message) => Center(
              child: Text('Error: $message'),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCommissionList(
      BuildContext context, List<Commission> commissions) {
    if (commissions.isEmpty) {
      return const Center(child: Text('No commissions found'));
    }

    return ListView.builder(
      itemCount: commissions.length,
      itemBuilder: (context, index) {
        final commission = commissions[index];
        return CommissionListItem(
          commission: commission,
          onEdit: () => _showCommissionFormDialog(context, commission),
          onDelete: () => _showDeleteConfirmation(context, commission),
        );
      },
    );
  }

  void _showCommissionFormDialog(BuildContext context,
      [Commission? commission]) {
    showDialog(
      context: context,
      builder: (context) => CommissionFormDialog(
        commission: commission,
        onSubmit: (commission) {
          if (commission.id.isEmpty) {
            context.read<CommissionBloc>().add(
                  CommissionEvent.createCommission(commission),
                );
          } else {
            context.read<CommissionBloc>().add(
                  CommissionEvent.updateCommission(commission),
                );
          }
          Navigator.of(context).pop();
        },
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, Commission commission) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Commission'),
        content: const Text('Are you sure you want to delete this commission?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.read<CommissionBloc>().add(
                    CommissionEvent.deleteCommission(commission.id),
                  );
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
