import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/refresh_controller.dart';

/// Provider for the RefreshController
final refreshControllerProvider = Provider<RefreshController>((ref) {
  // Create and return the singleton instance
  final controller = RefreshController();
  
  // Dispose the controller when the provider is disposed
  ref.onDispose(() {
    controller.dispose();
  });
  
  return controller;
});

/// Provider that exposes the last refresh time
/// This can be used to trigger rebuilds when the refresh happens
final refreshTimestampProvider = Provider<DateTime>((ref) {
  // Watch the controller to get notified of changes
  final controller = ref.watch(refreshControllerProvider);
  
  // Return the last refresh time
  return controller.lastRefreshTime;
});

/// Provider that exposes whether auto-refresh is enabled
final autoRefreshEnabledProvider = Provider<bool>((ref) {
  final controller = ref.watch(refreshControllerProvider);
  return controller.autoRefreshEnabled;
});

/// Provider that exposes the refresh interval
final refreshIntervalProvider = Provider<int>((ref) {
  final controller = ref.watch(refreshControllerProvider);
  return controller.refreshInterval;
});
