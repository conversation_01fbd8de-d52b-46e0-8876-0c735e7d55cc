import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/shared/services/payment_gateway/payment_gateway_service.dart';
import 'payment_gateway_event.dart';
import 'payment_gateway_state.dart';

@injectable
class PaymentGatewayBloc
    extends Bloc<PaymentGatewayEvent, PaymentGatewayState> {
  final PaymentGatewayService _service;

  PaymentGatewayBloc(this._service)
      : super(const PaymentGatewayState.initial()) {
    on<PaymentGatewayEvent>((event, emit) async {
      await event.map(
        loadGateways: (e) => _onLoadGateways(e, emit),
        createPaymentGateway: (e) => _onCreatePaymentGateway(e, emit),
        updatePaymentGateway: (e) => _onUpdatePaymentGateway(e, emit),
        deletePaymentGateway: (e) => _onDeletePaymentGateway(e, emit),
        updateGatewayStatus: (e) => _onUpdateGatewayStatus(e, emit),
        updateGatewayCredentials: (e) => _onUpdateGatewayCredentials(e, emit),
        updateGatewayFees: (e) => _onUpdateGatewayFees(e, emit),
        calculateTransactionFee: (e) => _onCalculateTransactionFee(e, emit),
      );
    });
  }

  Future<void> _onLoadGateways(
    LoadGateways event,
    Emitter<PaymentGatewayState> emit,
  ) async {
    try {
      emit(const PaymentGatewayState.loading());
      final gateways = await _service.getGateways();
      emit(PaymentGatewayState.loaded(gateways));
    } catch (e) {
      emit(PaymentGatewayState.error(e.toString()));
    }
  }

  Future<void> _onCreatePaymentGateway(
    CreatePaymentGateway event,
    Emitter<PaymentGatewayState> emit,
  ) async {
    try {
      emit(const PaymentGatewayState.loading());
      await _service.createPaymentGateway(event.gateway);
      final gateways = await _service.getGateways();
      emit(PaymentGatewayState.loaded(gateways));
    } catch (e) {
      emit(PaymentGatewayState.error(e.toString()));
    }
  }

  Future<void> _onUpdatePaymentGateway(
    UpdatePaymentGateway event,
    Emitter<PaymentGatewayState> emit,
  ) async {
    try {
      emit(const PaymentGatewayState.loading());
      await _service.updatePaymentGateway(event.gateway);
      final gateways = await _service.getGateways();
      emit(PaymentGatewayState.loaded(gateways));
    } catch (e) {
      emit(PaymentGatewayState.error(e.toString()));
    }
  }

  Future<void> _onDeletePaymentGateway(
    DeletePaymentGateway event,
    Emitter<PaymentGatewayState> emit,
  ) async {
    try {
      emit(const PaymentGatewayState.loading());
      await _service.deletePaymentGateway(event.id);
      final gateways = await _service.getGateways();
      emit(PaymentGatewayState.loaded(gateways));
    } catch (e) {
      emit(PaymentGatewayState.error(e.toString()));
    }
  }

  Future<void> _onUpdateGatewayStatus(
    UpdateGatewayStatus event,
    Emitter<PaymentGatewayState> emit,
  ) async {
    try {
      emit(const PaymentGatewayState.loading());
      await _service.updateGatewayStatus(event.id, event.isActive);
      final gateways = await _service.getGateways();
      emit(PaymentGatewayState.loaded(gateways));
    } catch (e) {
      emit(PaymentGatewayState.error(e.toString()));
    }
  }

  Future<void> _onUpdateGatewayCredentials(
    UpdateGatewayCredentials event,
    Emitter<PaymentGatewayState> emit,
  ) async {
    try {
      emit(const PaymentGatewayState.loading());
      await _service.updateGatewayCredentials(event.id, event.credentials);
      final gateways = await _service.getGateways();
      emit(PaymentGatewayState.loaded(gateways));
    } catch (e) {
      emit(PaymentGatewayState.error(e.toString()));
    }
  }

  Future<void> _onUpdateGatewayFees(
    UpdateGatewayFees event,
    Emitter<PaymentGatewayState> emit,
  ) async {
    try {
      emit(const PaymentGatewayState.loading());
      await _service.updateGatewayFees(event.id, event.fees);
      final gateways = await _service.getGateways();
      emit(PaymentGatewayState.loaded(gateways));
    } catch (e) {
      emit(PaymentGatewayState.error(e.toString()));
    }
  }

  Future<void> _onCalculateTransactionFee(
    CalculateTransactionFee event,
    Emitter<PaymentGatewayState> emit,
  ) async {
    try {
      emit(const PaymentGatewayState.loading());
      final fee =
          await _service.calculateTransactionFee(event.id, event.amount);
      emit(PaymentGatewayState.calculated(fee));
    } catch (e) {
      emit(PaymentGatewayState.error(e.toString()));
    }
  }
}
