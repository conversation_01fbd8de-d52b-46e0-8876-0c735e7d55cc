import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../shared/models/buyer.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';

abstract class ProfileRepository {
  Future<Buyer> getProfile();
  Future<void> updateProfile(Buyer profile);
  Future<void> updatePhoneNumber(String phoneNumber);
  Future<void> requestEmailChange(String newEmail);
  Future<void> deleteProfile();
}

class ProfileRepositoryImpl implements ProfileRepository {
  final DatabaseService _databaseService;
  final SupabaseClient _supabase;
  final _logger = getLogger('ProfileRepositoryImpl');

  ProfileRepositoryImpl({
    DatabaseService? databaseService,
    SupabaseClient? supabase,
  }) : _databaseService = databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment()),
       _supabase = supabase ?? Supabase.instance.client;

  @override
  Future<Buyer> getProfile() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final buyerData = await _databaseService.find('buyers', currentUser.id);

      if (buyerData == null) {
        // Create a new profile if it doesn't exist, using Supabase auth data
        final displayName = currentUser.userMetadata?['display_name'] ??
                           currentUser.userMetadata?['full_name'] ??
                           currentUser.userMetadata?['name'] ??
                           '';

        final newProfile = Buyer(
          id: currentUser.id,
          name: displayName,
          email: currentUser.email ?? '',
          phoneNumber: currentUser.phone ?? '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final profileData = {
          'id': currentUser.id,
          ...newProfile.toJson(),
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          // Store additional Supabase metadata for reference
          'supabaseMetadata': currentUser.userMetadata ?? {},
          'emailVerified': currentUser.emailConfirmedAt != null,
          'phoneVerified': currentUser.phoneConfirmedAt != null,
        };

        await _databaseService.create('buyers', profileData);
        _logger.info('Created new buyer profile for user: ${currentUser.id}');
        return newProfile;
      }

      // Check if profile needs sync with Supabase auth data
      final profile = Buyer.fromJson(buyerData);
      final needsSync = _needsAuthSync(profile, currentUser);

      if (needsSync) {
        _logger.info('Syncing profile with Supabase auth data for user: ${currentUser.id}');
        final syncedProfile = await _syncWithAuthData(profile, currentUser);
        return syncedProfile;
      }

      return Buyer.fromJson(buyerData);
    } catch (e) {
      _logger.severe('Failed to get profile: $e');
      throw Exception('Failed to get profile: $e');
    }
  }

  @override
  Future<void> updateProfile(Buyer profile) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      if (profile.id != currentUser.id) {
        throw Exception('Profile ID does not match current user');
      }

      // Update the updatedAt timestamp
      final updatedProfile = profile.copyWith(
        updatedAt: DateTime.now(),
      );

      // Prepare profile data for database
      final profileData = {
        'id': currentUser.id,
        ...updatedProfile.toJson(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // Update Supabase auth user metadata if email or display name changed
      final needsAuthUpdate = profile.email != currentUser.email ||
                             profile.name != (currentUser.userMetadata?['display_name'] ?? '');

      if (needsAuthUpdate) {
        try {
          // Update Supabase auth user metadata
          final authUpdateData = <String, dynamic>{};

          // Update display name in user metadata
          if (profile.name != (currentUser.userMetadata?['display_name'] ?? '')) {
            authUpdateData['display_name'] = profile.name;
          }

          // Update user metadata if there are changes
          if (authUpdateData.isNotEmpty) {
            await _supabase.auth.updateUser(
              UserAttributes(
                data: {
                  ...currentUser.userMetadata ?? {},
                  ...authUpdateData,
                },
              ),
            );
            _logger.info('Updated Supabase user metadata for user: ${currentUser.id}');
          }

          // Update email separately if changed
          if (profile.email != currentUser.email) {
            await _supabase.auth.updateUser(
              UserAttributes(email: profile.email),
            );
            _logger.info('Updated Supabase user email for user: ${currentUser.id}');
          }

        } catch (authError) {
          _logger.warning('Failed to update Supabase auth profile: $authError');
          // Continue with database update even if auth update fails
          // This ensures data consistency in our database
        }
      }

      // Update the profile in our database
      await _databaseService.update('buyers', currentUser.id, profileData);
      _logger.info('Successfully updated profile for user: ${currentUser.id}');

    } catch (e) {
      _logger.severe('Failed to update profile: $e');
      throw Exception('Failed to update profile: $e');
    }
  }

  /// Update phone number with proper Supabase verification
  Future<void> updatePhoneNumber(String phoneNumber) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Update phone number in Supabase auth
      await _supabase.auth.updateUser(
        UserAttributes(phone: phoneNumber),
      );

      // Update phone number in our database
      final existingProfile = await _databaseService.find('buyers', currentUser.id);
      if (existingProfile != null) {
        await _databaseService.update('buyers', currentUser.id, {
          ...existingProfile,
          'phoneNumber': phoneNumber,
          'updatedAt': DateTime.now().toIso8601String(),
        });
      }

      _logger.info('Successfully updated phone number for user: ${currentUser.id}');
    } catch (e) {
      _logger.severe('Failed to update phone number: $e');
      throw Exception('Failed to update phone number: $e');
    }
  }

  /// Verify email change (Supabase will send confirmation email)
  Future<void> requestEmailChange(String newEmail) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Request email change - Supabase will send confirmation email
      await _supabase.auth.updateUser(
        UserAttributes(email: newEmail),
      );

      _logger.info('Email change requested for user: ${currentUser.id}');
    } catch (e) {
      _logger.severe('Failed to request email change: $e');
      throw Exception('Failed to request email change: $e');
    }
  }

  /// Delete user profile and account
  Future<void> deleteProfile() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Delete profile from database first
      await _databaseService.delete('buyers', currentUser.id);

      // Note: Supabase doesn't provide a direct way to delete users from client-side
      // This would typically be handled by an admin function or RLS policies
      // For now, we'll just mark the profile as deleted in our database
      _logger.info('Profile deleted for user: ${currentUser.id}');

    } catch (e) {
      _logger.severe('Failed to delete profile: $e');
      throw Exception('Failed to delete profile: $e');
    }
  }

  /// Check if profile needs to be synced with Supabase auth data
  bool _needsAuthSync(Buyer profile, User authUser) {
    final authDisplayName = authUser.userMetadata?['display_name'] ??
                           authUser.userMetadata?['full_name'] ??
                           authUser.userMetadata?['name'] ??
                           '';

    return profile.email != (authUser.email ?? '') ||
           profile.phoneNumber != (authUser.phone ?? '') ||
           (authDisplayName.isNotEmpty && profile.name != authDisplayName);
  }

  /// Sync profile with Supabase auth data
  Future<Buyer> _syncWithAuthData(Buyer profile, User authUser) async {
    final authDisplayName = authUser.userMetadata?['display_name'] ??
                           authUser.userMetadata?['full_name'] ??
                           authUser.userMetadata?['name'] ??
                           profile.name; // Keep existing name if no auth name

    final syncedProfile = profile.copyWith(
      email: authUser.email ?? profile.email,
      phoneNumber: authUser.phone ?? profile.phoneNumber,
      name: authDisplayName.isNotEmpty ? authDisplayName : profile.name,
      updatedAt: DateTime.now(),
    );

    // Update the synced profile in database
    final profileData = {
      'id': authUser.id,
      ...syncedProfile.toJson(),
      'updatedAt': DateTime.now().toIso8601String(),
      'supabaseMetadata': authUser.userMetadata ?? {},
      'emailVerified': authUser.emailConfirmedAt != null,
      'phoneVerified': authUser.phoneConfirmedAt != null,
      'lastSyncedAt': DateTime.now().toIso8601String(),
    };

    await _databaseService.update('buyers', authUser.id, profileData);
    return syncedProfile;
  }
}

final profileRepositoryProvider = Provider<ProfileRepository>((ref) {
  return ProfileRepositoryImpl();
});
