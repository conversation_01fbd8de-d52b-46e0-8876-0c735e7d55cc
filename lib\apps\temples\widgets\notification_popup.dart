import 'package:flutter/material.dart';
import '../../../shared/models/notification/notification_model.dart';
import '../../../shared/models/notification/notification_type.dart';
import '../../../shared/theme/traditional_colors.dart';

/// Temple-specific notification popup widget
class TempleNotificationPopup extends StatefulWidget {
  /// Creates a temple notification popup
  const TempleNotificationPopup({
    super.key,
    required this.notification,
    this.onTap,
    this.onDismiss,
  });

  /// The notification to display
  final NotificationModel notification;

  /// Callback when the notification is tapped
  final VoidCallback? onTap;

  /// Callback when the notification is dismissed
  final VoidCallback? onDismiss;

  @override
  State<TempleNotificationPopup> createState() =>
      _TempleNotificationPopupState();
}

class _TempleNotificationPopupState extends State<TempleNotificationPopup>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, -1), end: Offset.zero).animate(
          CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
        );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.15),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                widget.onTap?.call();
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Icon
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _getNotificationColor(widget.notification.type),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        _getNotificationIcon(widget.notification.type),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),

                    // Content
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            widget.notification.title,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.notification.body,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),

                    // Dismiss button
                    IconButton(
                      onPressed: widget.onDismiss,
                      icon: Icon(
                        Icons.close,
                        color: Colors.grey[400],
                        size: 18,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Get color for notification type with temple theme
  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.booking:
        return TraditionalColors.saffron;
      case NotificationType.payment:
        return TraditionalColors.gold;
      case NotificationType.system:
        return TraditionalColors.templeOrange;
      case NotificationType.calendarReminder:
        return TraditionalColors.deepSaffron;
      case NotificationType.event:
      case NotificationType.festivalReminder:
        return TraditionalColors.maroon;
      default:
        return TraditionalColors.templeOrange;
    }
  }

  /// Get icon for notification type
  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.booking:
        return Icons.event_available;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.system:
        return Icons.temple_buddhist;
      case NotificationType.calendarReminder:
        return Icons.alarm;
      case NotificationType.event:
        return Icons.event;
      case NotificationType.festivalReminder:
        return Icons.celebration;
      default:
        return Icons.notifications;
    }
  }
}

/// Service for showing temple notification popups
class TempleNotificationPopupService {
  static OverlayEntry? _currentOverlay;

  /// Show a notification popup
  static void showNotificationPopup(
    BuildContext context,
    NotificationModel notification, {
    Duration duration = const Duration(seconds: 4),
  }) {
    // Remove any existing popup
    hideNotificationPopup();

    final overlay = Overlay.of(context);

    _currentOverlay = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 16,
        left: 0,
        right: 0,
        child: TempleNotificationPopup(
          notification: notification,
          onDismiss: hideNotificationPopup,
          onTap: hideNotificationPopup,
        ),
      ),
    );

    overlay.insert(_currentOverlay!);

    // Auto-hide after duration
    Future.delayed(duration, () {
      hideNotificationPopup();
    });
  }

  /// Hide the current notification popup
  static void hideNotificationPopup() {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }
}
