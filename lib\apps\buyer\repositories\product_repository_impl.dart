import '../../../shared/models/product/product_model.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';
import 'product_repository.dart';

/// Hybrid storage implementation of [ProductRepository]
class ProductRepositoryImpl implements ProductRepository {
  /// Creates a [ProductRepositoryImpl]
  ProductRepositoryImpl({
    DatabaseService? databaseService,
  }) : _databaseService = databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment());

  final DatabaseService _databaseService;
  final _logger = getLogger('ProductRepositoryImpl');
  final String _collection = 'products';

  /// Get all active and approved products
  Future<List<ProductModel>> _getActiveProducts() async {
    final allProducts = await _databaseService.getAll(_collection);

    return allProducts
        .where((productData) {
          final isActive = productData['isActive'] == true;
          final isApproved = productData['isApproved'] == true;
          final isDeleted = productData['isDeleted'] == true;
          return isActive && isApproved && !isDeleted;
        })
        .map((productData) => ProductModel.fromJson(productData))
        .toList();
  }

  @override
  Future<List<ProductModel>> getFeaturedProducts() async {
    try {
      final activeProducts = await _getActiveProducts();

      return activeProducts
          .where((product) => product.isFeatured == true)
          .take(20)
          .toList();
    } catch (e) {
      _logger.severe('Error getting featured products: $e');
      return [];
    }
  }

  @override
  Future<List<ProductModel>> getLatestProducts() async {
    try {
      final activeProducts = await _getActiveProducts();

      // Sort by createdAt (descending)
      activeProducts.sort((a, b) {
        final createdAtA = a.createdAt ?? DateTime.now();
        final createdAtB = b.createdAt ?? DateTime.now();
        return createdAtB.compareTo(createdAtA);
      });

      return activeProducts.take(20).toList();
    } catch (e) {
      _logger.severe('Error getting latest products: $e');
      return [];
    }
  }

  @override
  Future<ProductModel?> getProductById(String id) async {
    try {
      final productData = await _databaseService.find(_collection, id);
      if (productData == null) return null;

      final isActive = productData['isActive'] == true;
      final isApproved = productData['isApproved'] == true;
      final isDeleted = productData['isDeleted'] == true;

      if (!isActive || !isApproved || isDeleted) return null;

      return ProductModel.fromJson(productData);
    } catch (e) {
      _logger.severe('Error getting product by ID: $e');
      return null;
    }
  }

  @override
  Future<List<ProductModel>> getProductsByCategory(String categoryId) async {
    try {
      final activeProducts = await _getActiveProducts();

      return activeProducts
          .where((product) => product.categoryId == categoryId)
          .toList();
    } catch (e) {
      _logger.severe('Error getting products by category: $e');
      return [];
    }
  }

  @override
  Future<List<ProductModel>> getProductsBySeller(String sellerId) async {
    try {
      final activeProducts = await _getActiveProducts();

      return activeProducts
          .where((product) => product.sellerId == sellerId)
          .toList();
    } catch (e) {
      _logger.severe('Error getting products by seller: $e');
      return [];
    }
  }

  @override
  Future<List<ProductModel>> searchProducts(String query) async {
    try {
      final activeProducts = await _getActiveProducts();
      final lowerQuery = query.toLowerCase();

      return activeProducts
          .where((product) =>
              product.name.toLowerCase().contains(lowerQuery) ||
              product.description.toLowerCase().contains(lowerQuery) ||
              (product.tags.any((tag) => tag.toLowerCase().contains(lowerQuery))))
          .toList();
    } catch (e) {
      _logger.severe('Error searching products: $e');
      return [];
    }
  }

  @override
  Future<List<ProductModel>> getProductsByIds(List<String> ids) async {
    try {
      final products = <ProductModel>[];

      for (final id in ids) {
        final product = await getProductById(id);
        if (product != null) {
          products.add(product);
        }
      }

      return products;
    } catch (e) {
      _logger.severe('Error getting products by IDs: $e');
      return [];
    }
  }

  @override
  Future<List<ProductModel>> getProductsWithFilters({
    String? categoryId,
    String? sellerId,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
    bool? sortAscending,
    ProductStatus? status,
    ProductType? type,
    bool? isFeatured,
    bool? isApproved,
    bool? isNew,
    String? searchQuery,
    int? limit,
  }) async {
    try {
      var products = await _getActiveProducts();

      // Apply filters
      if (categoryId != null) {
        products = products.where((p) => p.categoryId == categoryId).toList();
      }

      if (sellerId != null) {
        products = products.where((p) => p.sellerId == sellerId).toList();
      }

      if (minPrice != null) {
        products = products.where((p) => p.price >= minPrice).toList();
      }

      if (maxPrice != null) {
        products = products.where((p) => p.price <= maxPrice).toList();
      }

      // Note: status and type filters removed as they're not available in ProductModel

      if (isFeatured != null) {
        products = products.where((p) => p.isFeatured == isFeatured).toList();
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        final lowerQuery = searchQuery.toLowerCase();
        products = products
            .where((p) =>
                p.name.toLowerCase().contains(lowerQuery) ||
                p.description.toLowerCase().contains(lowerQuery))
            .toList();
      }

      // Apply sorting
      if (sortBy != null) {
        final ascending = sortAscending ?? true;
        products.sort((a, b) {
          int comparison = 0;
          switch (sortBy) {
            case 'price':
              comparison = a.price.compareTo(b.price);
              break;
            case 'name':
              comparison = a.name.compareTo(b.name);
              break;
            case 'createdAt':
              final aDate = a.createdAt ?? DateTime.now();
              final bDate = b.createdAt ?? DateTime.now();
              comparison = aDate.compareTo(bDate);
              break;
            default:
              comparison = 0;
          }
          return ascending ? comparison : -comparison;
        });
      }

      // Apply limit
      if (limit != null && limit > 0) {
        products = products.take(limit).toList();
      }

      return products;
    } catch (e) {
      _logger.severe('Error getting products with filters: $e');
      return [];
    }
  }

  @override
  Future<ProductModel> getProductWithSellerDetails(String id) async {
    try {
      final product = await getProductById(id);
      if (product == null) {
        throw Exception('Product not found with ID: $id');
      }

      // For now, return the product as-is
      // In a full implementation, you might fetch seller details and merge them
      return product;
    } catch (e) {
      _logger.severe('Error getting product with seller details: $e');
      throw Exception('Error getting product with seller details: $e');
    }
  }

  @override
  Future<List<ProductModel>> getProducts() async {
    try {
      return await _getActiveProducts();
    } catch (e) {
      _logger.severe('Error getting all products: $e');
      return [];
    }
  }
}