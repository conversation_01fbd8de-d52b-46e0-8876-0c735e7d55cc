import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'environment.dart';

/// Application-wide configuration settings
class AppConfig {
  // App names
  static const String adminAppName = 'Shivish Admin';
  static const String buyerAppName = 'Shivish Buyer';
  static const String sellerAppName = 'Shivish Seller';
  static const String priestAppName = 'Shivish Priest';
  static const String technicianAppName = 'Shivish Technician';
  static const String executorAppName = 'Shivish Executor';
  static const String merchantName = 'Shivish';
  static const String appName = 'Shivish';

  // App versions
  static const String appVersion = '1.0.0';
  static const int buildNumber = 1;
  static const String appBuildNumber = '1';

  // API configuration
  static String get apiUrl => Environment.apiUrl;
  static const int apiTimeoutSeconds = 30;
  static const int apiRetryCount = 3;
  static const int apiTimeout = 30000; // 30 seconds

  // Cache configuration
  static const int cacheMaxAgeDays = 7;
  static const int cacheMaxSize = 100 * 1024 * 1024; // 100 MB

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Feature flags
  static bool get enableAnalytics => true;
  static bool get enableCrashReporting => true;
  static bool get enablePerformanceMonitoring => true;
  static bool get enableRemoteConfig => true;
  static bool get enableAbtesting => Environment.isDev;
  static const bool enableWallet = true;
  static const bool enableCashOnDelivery = true;
  static const bool enableUPI = true;
  static const bool enableCardPayment = true;

  // UI configuration
  static const double defaultAnimationDuration = 300; // milliseconds
  static const double defaultBorderRadius = 12.0;
  static const double defaultPadding = 16.0;
  static const double defaultSpacing = 8.0;

  // Date formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';

  // Currency
  static const String defaultCurrency = 'INR';
  static const String defaultCurrencySymbol = '₹';
  static const String currency = 'INR';

  // Default language
  static const String defaultLanguage = 'en';

  // Default theme
  static const bool defaultDarkMode = false;

  // Default alarm settings
  static const String defaultAlarmTime = '06:00';
  static const String defaultAlarmTone = 'default_tone.mp3';
  static const int defaultAlarmVolume = 80;

  // WhatsApp integration
  static const bool enableWhatsAppIntegration = true;
  static const String defaultWhatsAppMessage = 'Hello from Shivish!';

  // AI settings
  static const bool enableAI = true;
  static const String defaultAIModel = 'gpt-3.5-turbo';
  static const int maxAITokens = 1000;

  // Voice command settings
  static const bool enableVoiceCommands = true;
  static const String defaultVoiceLanguage = 'en-US';

  // Media settings
  static const int maxImageSize = 5 * 1024 * 1024; // 5 MB
  static const int maxVideoSize = 50 * 1024 * 1024; // 50 MB
  static const int maxAudioSize = 10 * 1024 * 1024; // 10 MB
  static const List<String> supportedImageFormats = [
    'jpg',
    'jpeg',
    'png',
    'webp'
  ];
  static const List<String> supportedVideoFormats = ['mp4', 'mov', 'avi'];
  static const List<String> supportedAudioFormats = ['mp3', 'wav', 'ogg'];

  // Security settings
  static const int sessionTimeoutMinutes = 30;
  static const int maxLoginAttempts = 5;
  static const int lockoutDurationMinutes = 15;
  static const bool enableBiometricAuth = true;
  static const bool enableTwoFactorAuth = false;

  // Notification settings
  static const bool enablePushNotifications = true;
  static const bool enableEmailNotifications = true;
  static const bool enableSmsNotifications = false;

  // Payment settings
  static const double defaultCommissionRate = 0.05; // 5%
  static const double minimumWithdrawalAmount = 100.0;
  static const int withdrawalProcessingDays = 3;
  static const List<String> supportedPaymentMethods = [
    'credit_card',
    'debit_card',
    'upi',
    'net_banking',
    'wallet',
    'cod',
  ];
  static const String paymentGatewayKey = 'YOUR_PAYMENT_GATEWAY_KEY';

  // Storage Configuration
  static const String storageBucket = 'shivish.appspot.com';

  // Analytics Configuration
  static const String analyticsId = 'YOUR_ANALYTICS_ID';

  // Event settings
  static const int maxEventDurationHours = 24;
  static const int minEventDurationMinutes = 30;
  static const int maxEventParticipants = 1000;
  static const List<String> eventTypes = [
    'public',
    'private',
    'birthday',
    'anniversary',
    'festival',
    'ceremony',
  ];

  // Booking settings
  static const int maxAdvanceBookingDays = 90;
  static const int minAdvanceBookingHours = 24;
  static const int cancellationWindowHours = 24;
  static const double cancellationFeePercentage = 0.1; // 10%

  // Review settings
  static const int minReviewLength = 10;
  static const int maxReviewLength = 500;
  static const int maxReviewImages = 5;
  static const double minRating = 1.0;
  static const double maxRating = 5.0;

  // Help center settings
  static const int maxTicketLength = 1000;
  static const int maxTicketAttachments = 3;
  static const int ticketResponseTimeHours = 24;
  static const List<String> ticketCategories = [
    'account',
    'payment',
    'order',
    'product',
    'service',
    'technical',
    'other',
  ];
}

/// Environment-specific configuration
class EnvironmentConfig {
  final String apiBaseUrl;
  final String environment;

  const EnvironmentConfig({
    required this.apiBaseUrl,
    required this.environment,
  });

  static EnvironmentConfig get development => const EnvironmentConfig(
        apiBaseUrl: 'http://localhost:3000',
        environment: 'development',
      );

  static EnvironmentConfig get staging => const EnvironmentConfig(
        apiBaseUrl: 'https://staging-api.example.com',
        environment: 'staging',
      );

  static EnvironmentConfig get production => const EnvironmentConfig(
        apiBaseUrl: 'https://api.example.com',
        environment: 'production',
      );

  bool get isDev => environment == 'development';
  bool get isStaging => environment == 'staging';
  bool get isProd => environment == 'production';
}

/// Provider for environment-specific configuration
final appConfigProvider = Provider<EnvironmentConfig>((ref) {
  // Use the Environment class for environment detection
  if (Environment.isDev) {
    return EnvironmentConfig.development;
  } else if (Environment.isStaging) {
    return EnvironmentConfig.staging;
  } else {
    return EnvironmentConfig.production;
  }
});
