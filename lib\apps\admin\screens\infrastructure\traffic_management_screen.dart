import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/infrastructure_provider.dart';
import '../../models/traffic_config.dart';
import '../../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../../shared/ui_components/errors/error_message.dart';

/// Screen for managing traffic routing between AWS and datacenter
class TrafficManagementScreen extends ConsumerStatefulWidget {
  const TrafficManagementScreen({super.key});

  @override
  ConsumerState<TrafficManagementScreen> createState() => _TrafficManagementScreenState();
}

class _TrafficManagementScreenState extends ConsumerState<TrafficManagementScreen> {
  TrafficRoutingMode _selectedMode = TrafficRoutingMode.hybrid;
  double _awsTrafficPercentage = 50.0;
  bool _isUpdating = false;

  @override
  Widget build(BuildContext context) {
    final trafficConfig = ref.watch(trafficConfigProvider);
    final serverStatus = ref.watch(serverStatusProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Traffic Management'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refreshStatus(),
          ),
        ],
      ),
      body: trafficConfig.when(
        data: (config) => _buildContent(config, serverStatus),
        loading: () => const LoadingIndicator(),
        error: (error, stack) => ErrorMessage(
          message: error.toString(),
          onRetry: () => ref.refresh(trafficConfigProvider),
        ),
      ),
    );
  }

  Widget _buildContent(TrafficConfig config, AsyncValue<ServerStatus> serverStatus) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCurrentStatus(config, serverStatus),
          const SizedBox(height: 24),
          _buildTrafficControls(config),
          const SizedBox(height: 24),
          _buildServerHealth(serverStatus),
          const SizedBox(height: 24),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildCurrentStatus(TrafficConfig config, AsyncValue<ServerStatus> serverStatus) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.traffic,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Current Traffic Status',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusRow('Routing Mode', config.routingMode.displayName),
            _buildStatusRow('AWS Traffic', '${config.awsTrafficPercentage.toInt()}%'),
            _buildStatusRow('Datacenter Traffic', '${(100 - config.awsTrafficPercentage).toInt()}%'),
            _buildStatusRow('Last Updated', _formatDateTime(config.lastUpdated)),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildStatusIndicator('AWS', serverStatus.value?.awsStatus ?? ServerHealth.unknown),
                const SizedBox(width: 16),
                _buildStatusIndicator('Datacenter', serverStatus.value?.datacenterStatus ?? ServerHealth.unknown),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(String label, ServerHealth health) {
    Color color;
    IconData icon;
    
    switch (health) {
      case ServerHealth.healthy:
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case ServerHealth.warning:
        color = Colors.orange;
        icon = Icons.warning;
        break;
      case ServerHealth.critical:
        color = Colors.red;
        icon = Icons.error;
        break;
      case ServerHealth.unknown:
        color = Colors.grey;
        icon = Icons.help;
        break;
    }

    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 4),
        Text(label, style: TextStyle(color: color, fontWeight: FontWeight.w500)),
      ],
    );
  }

  Widget _buildTrafficControls(TrafficConfig config) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Traffic Routing Controls',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildRoutingModeSelector(),
            if (_selectedMode == TrafficRoutingMode.hybrid) ...[
              const SizedBox(height: 16),
              _buildTrafficSlider(),
            ],
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isUpdating ? null : _applyChanges,
                    child: _isUpdating
                        ? const SizedBox(
                            height: 16,
                            width: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Apply Changes'),
                  ),
                ),
                const SizedBox(width: 12),
                OutlinedButton(
                  onPressed: _resetToDefault,
                  child: const Text('Reset'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoutingModeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Routing Mode:', style: TextStyle(fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        ...TrafficRoutingMode.values.map((mode) => RadioListTile<TrafficRoutingMode>(
          title: Text(mode.displayName),
          subtitle: Text(mode.description),
          value: mode,
          groupValue: _selectedMode,
          onChanged: (value) => setState(() => _selectedMode = value!),
        )),
      ],
    );
  }

  Widget _buildTrafficSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('AWS Traffic: ${_awsTrafficPercentage.toInt()}%'),
        Slider(
          value: _awsTrafficPercentage,
          min: 0,
          max: 100,
          divisions: 20,
          label: '${_awsTrafficPercentage.toInt()}%',
          onChanged: (value) => setState(() => _awsTrafficPercentage = value),
        ),
        Text('Datacenter Traffic: ${(100 - _awsTrafficPercentage).toInt()}%'),
      ],
    );
  }

  Widget _buildServerHealth(AsyncValue<ServerStatus> serverStatus) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Server Health Monitoring',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            serverStatus.when(
              data: (status) => _buildHealthDetails(status),
              loading: () => const LoadingIndicator(),
              error: (error, stack) => ErrorMessage(message: error.toString()),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthDetails(ServerStatus status) {
    return Column(
      children: [
        _buildServerHealthCard('AWS Servers', status.awsServers),
        const SizedBox(height: 12),
        _buildServerHealthCard('Datacenter Servers', status.datacenterServers),
      ],
    );
  }

  Widget _buildServerHealthCard(String title, List<ServerInfo> servers) {
    return ExpansionTile(
      title: Text(title),
      children: servers.map((server) => ListTile(
        leading: _buildStatusIndicator(server.name, server.health),
        title: Text(server.name),
        subtitle: Text('${server.cpuUsage}% CPU, ${server.memoryUsage}% Memory'),
        trailing: Text('${server.responseTime}ms'),
      )).toList(),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildQuickActionButton('Emergency AWS Only', Icons.cloud, () => _setEmergencyMode(TrafficRoutingMode.awsOnly)),
                _buildQuickActionButton('Emergency Datacenter Only', Icons.storage, () => _setEmergencyMode(TrafficRoutingMode.datacenterOnly)),
                _buildQuickActionButton('Maintenance Mode', Icons.build, () => _setMaintenanceMode()),
                _buildQuickActionButton('Auto Failover', Icons.autorenew, () => _enableAutoFailover()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(String label, IconData icon, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
    );
  }

  void _refreshStatus() {
    ref.invalidate(trafficConfigProvider);
    ref.invalidate(serverStatusProvider);
  }

  Future<void> _applyChanges() async {
    setState(() => _isUpdating = true);
    
    try {
      await ref.read(infrastructureServiceProvider).updateTrafficConfig(
        TrafficConfig(
          routingMode: _selectedMode,
          awsTrafficPercentage: _awsTrafficPercentage,
          lastUpdated: DateTime.now(),
        ),
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Traffic configuration updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating configuration: $e')),
        );
      }
    } finally {
      setState(() => _isUpdating = false);
    }
  }

  void _resetToDefault() {
    setState(() {
      _selectedMode = TrafficRoutingMode.hybrid;
      _awsTrafficPercentage = 50.0;
    });
  }

  Future<void> _setEmergencyMode(TrafficRoutingMode mode) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Emergency Mode'),
        content: Text('Are you sure you want to switch to ${mode.displayName}? This will immediately redirect all traffic.'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('Cancel')),
          ElevatedButton(onPressed: () => Navigator.pop(context, true), child: const Text('Confirm')),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _selectedMode = mode;
        _awsTrafficPercentage = mode == TrafficRoutingMode.awsOnly ? 100.0 : 0.0;
      });
      await _applyChanges();
    }
  }

  Future<void> _setMaintenanceMode() async {
    // Implementation for maintenance mode
  }

  Future<void> _enableAutoFailover() async {
    // Implementation for auto failover
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute}';
  }
}
