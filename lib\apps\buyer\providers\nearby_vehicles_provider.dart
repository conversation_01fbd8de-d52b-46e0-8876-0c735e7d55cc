import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:latlong2/latlong.dart' as latlong2;
import '../../../shared/models/delivery/delivery_person_model.dart';
import '../services/nearby_vehicles_service.dart';
import '../../../shared/utils/logger.dart';

final _logger = getLogger('NearbyVehiclesProvider');

/// Provider for nearby vehicles service
final nearbyVehiclesServiceProvider = Provider<NearbyVehiclesService>((ref) {
  return NearbyVehiclesService();
});

/// State for nearby vehicles
class NearbyVehiclesState {
  final List<DeliveryPersonModel> vehicles;
  final bool isLoading;
  final String? error;
  final latlong2.LatLng? userLocation;
  final String? selectedVehicleType;

  const NearbyVehiclesState({
    this.vehicles = const [],
    this.isLoading = false,
    this.error,
    this.userLocation,
    this.selectedVehicleType,
  });

  NearbyVehiclesState copyWith({
    List<DeliveryPersonModel>? vehicles,
    bool? isLoading,
    String? error,
    latlong2.LatLng? userLocation,
    String? selectedVehicleType,
  }) {
    return NearbyVehiclesState(
      vehicles: vehicles ?? this.vehicles,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      userLocation: userLocation ?? this.userLocation,
      selectedVehicleType: selectedVehicleType ?? this.selectedVehicleType,
    );
  }
}

/// Notifier for managing nearby vehicles state
class NearbyVehiclesNotifier extends StateNotifier<NearbyVehiclesState> {
  final NearbyVehiclesService _service;

  NearbyVehiclesNotifier(this._service) : super(const NearbyVehiclesState());

  /// Update user location and search for nearby vehicles
  Future<void> updateLocation({
    required latlong2.LatLng location,
    String? vehicleType,
  }) async {
    _logger.info('Updating location and searching for vehicles: $vehicleType');
    
    state = state.copyWith(
      isLoading: true,
      error: null,
      userLocation: location,
      selectedVehicleType: vehicleType,
    );

    try {
      final vehicles = await _service.getNearbyVehicles(
        userLocation: location,
        vehicleType: vehicleType,
      );

      state = state.copyWith(
        vehicles: vehicles,
        isLoading: false,
      );
    } catch (e) {
      _logger.severe('Error updating nearby vehicles: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Update vehicle type filter
  Future<void> updateVehicleType(String? vehicleType) async {
    if (state.userLocation == null) return;

    _logger.info('Updating vehicle type filter: $vehicleType');
    
    state = state.copyWith(
      isLoading: true,
      selectedVehicleType: vehicleType,
    );

    try {
      final vehicles = await _service.getNearbyVehicles(
        userLocation: state.userLocation!,
        vehicleType: vehicleType,
      );

      state = state.copyWith(
        vehicles: vehicles,
        isLoading: false,
      );
    } catch (e) {
      _logger.severe('Error updating vehicle type: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Clear vehicles
  void clearVehicles() {
    state = const NearbyVehiclesState();
  }

  /// Get estimated arrival time for a vehicle
  Future<int> getEstimatedArrivalTime(DeliveryPersonModel vehicle) async {
    if (state.userLocation == null) return 5; // Default 5 minutes

    return await _service.getEstimatedArrivalTime(
      userLocation: state.userLocation!,
      deliveryPerson: vehicle,
    );
  }

  /// Get vehicle icon
  String getVehicleIcon(DeliveryPersonModel vehicle) {
    return _service.getVehicleIcon(vehicle);
  }
}

/// Provider for nearby vehicles state
final nearbyVehiclesProvider = StateNotifierProvider<NearbyVehiclesNotifier, NearbyVehiclesState>((ref) {
  final service = ref.watch(nearbyVehiclesServiceProvider);
  return NearbyVehiclesNotifier(service);
});

/// Stream provider for real-time nearby vehicles
final nearbyVehiclesStreamProvider = StreamProvider.family<List<DeliveryPersonModel>, Map<String, dynamic>>((ref, params) {
  final service = ref.watch(nearbyVehiclesServiceProvider);
  final userLocation = params['location'] as latlong2.LatLng;
  final vehicleType = params['vehicleType'] as String?;
  
  return service.getNearbyVehiclesStream(
    userLocation: userLocation,
    vehicleType: vehicleType,
  );
});

/// Provider for vehicle details with estimated arrival time
final vehicleDetailsProvider = FutureProvider.family<Map<String, dynamic>, Map<String, dynamic>>((ref, params) async {
  final vehicle = params['vehicle'] as DeliveryPersonModel;
  final userLocation = params['userLocation'] as latlong2.LatLng;
  final service = ref.watch(nearbyVehiclesServiceProvider);
  
  final estimatedTime = await service.getEstimatedArrivalTime(
    userLocation: userLocation,
    deliveryPerson: vehicle,
  );
  
  return {
    'vehicle': vehicle,
    'estimatedTime': estimatedTime,
    'icon': service.getVehicleIcon(vehicle),
  };
});
