import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:shivish/shared/models/product/product_model.dart';
import 'package:shivish/shared/services/product/product_service.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/apps/seller/screens/products/food_bulk_upload_template.dart';

class BulkUploadScreen extends ConsumerStatefulWidget {
  const BulkUploadScreen({super.key});

  @override
  ConsumerState<BulkUploadScreen> createState() => _BulkUploadScreenState();
}

class _BulkUploadScreenState extends ConsumerState<BulkUploadScreen> {
  List<Map<String, dynamic>> _products = [];
  bool _isLoading = false;
  String? _error;
  final List<File> _selectedImages = [];
  String _statusMessage = '';
  int _successCount = 0;
  int _errorCount = 0;

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv', 'xlsx', 'xls'],
      );

      if (result != null) {
        setState(() {
          _isLoading = true;
          _error = null;
        });

        final file = result.files.first;
        final extension = file.extension?.toLowerCase() ?? '';
        final products = <Map<String, dynamic>>[];

        if (extension == 'csv') {
          // Process CSV file
          final bytes = file.bytes!;
          final csvString = String.fromCharCodes(bytes);
          final rows = const CsvToListConverter().convert(csvString);

          if (rows.isEmpty || rows.length < 2) {
            throw Exception('CSV file is empty or invalid');
          }

          final headers = rows.first.map((e) => e.toString()).toList();

          for (var i = 1; i < rows.length; i++) {
            final row = rows[i];
            final product = <String, dynamic>{};

            for (var j = 0; j < headers.length; j++) {
              if (j < row.length) {
                product[headers[j]] = row[j];
              }
            }

            // Process image URLs if they exist
            if (product.containsKey('imageUrls')) {
              final imageUrlsStr = product['imageUrls']?.toString() ?? '';
              if (imageUrlsStr.isNotEmpty) {
                final imageUrls =
                    imageUrlsStr.split(',').map((e) => e.trim()).toList();
                product['images'] = imageUrls;
              }
            }

            // Process specifications if they exist
            if (product.containsKey('specifications')) {
              final specificationsStr =
                  product['specifications']?.toString() ?? '';
              if (specificationsStr.isNotEmpty) {
                final specs = <String, dynamic>{};
                final specPairs = specificationsStr.split(',');

                for (final pair in specPairs) {
                  final keyValue = pair.split(':');
                  if (keyValue.length == 2) {
                    specs[keyValue[0].trim()] = keyValue[1].trim();
                  }
                }

                product['specifications'] = specs;
              }
            }

            // Process day-wise availability for food products
            if (product.containsKey('dayWiseAvailability') &&
                product['categoryId'] == 'food') {
              final availabilityStr =
                  product['dayWiseAvailability']?.toString() ?? '';
              if (availabilityStr.isNotEmpty) {
                final availability = <String, Map<String, String>>{};
                final availPairs = availabilityStr.split(',');

                for (final pair in availPairs) {
                  final dayTime = pair.split(':');
                  if (dayTime.length == 2) {
                    final day = dayTime[0].trim();
                    final timeRange = dayTime[1].trim().split('-');

                    if (timeRange.length == 2) {
                      availability[day] = {
                        'from': timeRange[0].trim(),
                        'to': timeRange[1].trim(),
                      };
                    }
                  }
                }

                product['dayWiseAvailability'] = availability;
              }
            }

            products.add(product);
          }
        } else if (extension == 'xlsx' || extension == 'xls') {
          // Process Excel file
          final bytes = file.bytes!;
          try {
            final excel = Excel.decodeBytes(bytes);

            // Get the first sheet
            if (excel.tables.isEmpty) {
              throw Exception('Excel file is empty or invalid');
            }

            final sheetName = excel.tables.keys.first;
            final sheet = excel.tables[sheetName]!;

            // Get headers from the first row
            final headers = <String>[];
            for (var cell in sheet.rows.first) {
              if (cell?.value != null) {
                headers.add(cell!.value.toString());
              }
            }

            if (headers.isEmpty) {
              throw Exception('Excel file has no headers');
            }

            // Process data rows
            for (var rowIndex = 1; rowIndex < sheet.maxRows; rowIndex++) {
              final row = sheet.rows[rowIndex];
              if (row.isEmpty || row.every((cell) => cell?.value == null)) {
                continue; // Skip empty rows
              }

              final product = <String, dynamic>{};

              for (var colIndex = 0;
                  colIndex < headers.length && colIndex < row.length;
                  colIndex++) {
                final cell = row[colIndex];
                if (cell?.value != null) {
                  product[headers[colIndex]] = cell!.value.toString();
                }
              }

              // Process image URLs if they exist
              if (product.containsKey('imageUrls')) {
                final imageUrlsStr = product['imageUrls']?.toString() ?? '';
                if (imageUrlsStr.isNotEmpty) {
                  final imageUrls =
                      imageUrlsStr.split(',').map((e) => e.trim()).toList();
                  product['images'] = imageUrls;
                }
              }

              // Process specifications if they exist
              if (product.containsKey('specifications')) {
                final specificationsStr =
                    product['specifications']?.toString() ?? '';
                if (specificationsStr.isNotEmpty) {
                  final specs = <String, dynamic>{};
                  final specPairs = specificationsStr.split(',');

                  for (final pair in specPairs) {
                    final keyValue = pair.split(':');
                    if (keyValue.length == 2) {
                      specs[keyValue[0].trim()] = keyValue[1].trim();
                    }
                  }

                  product['specifications'] = specs;
                }
              }

              // Process day-wise availability for food products
              if (product.containsKey('dayWiseAvailability') &&
                  product['categoryId'] == 'food') {
                final availabilityStr =
                    product['dayWiseAvailability']?.toString() ?? '';
                if (availabilityStr.isNotEmpty) {
                  final availability = <String, Map<String, String>>{};
                  final availPairs = availabilityStr.split(',');

                  for (final pair in availPairs) {
                    final dayTime = pair.split(':');
                    if (dayTime.length == 2) {
                      final day = dayTime[0].trim();
                      final timeRange = dayTime[1].trim().split('-');

                      if (timeRange.length == 2) {
                        availability[day] = {
                          'from': timeRange[0].trim(),
                          'to': timeRange[1].trim(),
                        };
                      }
                    }
                  }

                  product['dayWiseAvailability'] = availability;
                }
              }

              // Validate and add the product
              if (_validateProductData(product)) {
                products.add(product);
              }
            }
          } catch (e) {
            throw Exception('Error parsing Excel file: $e');
          }
        } else {
          throw Exception('Unsupported file format: $extension');
        }

        setState(() {
          _products = products;
          _isLoading = false;
          _statusMessage =
              'File processed successfully. ${products.length} products found.';
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  // Download general template file
  Future<void> _downloadTemplate() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Preparing template...';
    });

    try {
      // Create a new Excel workbook
      final excel = Excel.createExcel();

      // Add a sheet for products
      final Sheet sheet = excel['Products'];

      // Add headers
      final List<String> headers = [
        'name',
        'description',
        'price',
        'quantity',
        'categoryId',
        'originalPrice',
        'brand',
        'unit',
        'weight',
        'imageUrls',
        'highlights',
        'specifications',
        'dayWiseAvailability'
      ];

      // Add header row with cell styling
      for (var i = 0; i < headers.length; i++) {
        final cell =
            sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle =
            CellStyle(bold: true, horizontalAlign: HorizontalAlign.Center);
      }

      // Add sample data row
      final List<String> sampleData = [
        'Sample Product',
        'This is a sample product description',
        '100.00',
        '10',
        'electronics',
        '120.00',
        'Sample Brand',
        'pcs',
        '0.5',
        'https://example.com/image1.jpg,https://example.com/image2.jpg',
        'Color:Red,Material:Plastic,Size:Medium',
        'Monday:9:00 AM-5:00 PM,Tuesday:9:00 AM-5:00 PM'
      ];

      for (var i = 0; i < sampleData.length; i++) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 1))
            .value = TextCellValue(sampleData[i]);
      }

      // Add food product sample
      final List<String> foodSampleData = [
        'Samosa',
        'Delicious potato filled samosa',
        '20.00',
        '50',
        'food',
        '25.00',
        'Sharma Foods',
        'piece',
        '0.1',
        'https://example.com/samosa.jpg',
        '',
        'Monday:8:00 AM-8:00 PM,Tuesday:8:00 AM-8:00 PM,Wednesday:8:00 AM-8:00 PM,Thursday:8:00 AM-8:00 PM,Friday:8:00 AM-8:00 PM,Saturday:8:00 AM-8:00 PM,Sunday:8:00 AM-8:00 PM'
      ];

      for (var i = 0; i < foodSampleData.length; i++) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 2))
            .value = TextCellValue(foodSampleData[i]);
      }

      // Add instructions sheet
      final Sheet instructionSheet = excel['Instructions'];

      final List<String> instructions = [
        'Instructions for Bulk Product Upload:',
        '',
        '1. Fill in the "Products" sheet with your product details.',
        '2. Required fields: name, description, price, quantity, categoryId',
        '3. For food products, provide day-wise availability in the format "day:from-to" (e.g., "Monday:9:00 AM-5:00 PM")',
        '4. For specifications, use format "key:value" separated by commas (e.g., "Color:Red,Material:Plastic")',
        '5. For image URLs, provide comma-separated URLs or upload images separately after importing the file',
        '6. Save the file and upload it back to the system',
        '',
        'Note: Do not modify the header row or sheet structure',
        '',
        'For food products, consider using the specialized Food Products template for easier data entry.'
      ];

      for (var i = 0; i < instructions.length; i++) {
        final cell = instructionSheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: i));
        cell.value = TextCellValue(instructions[i]);
        if (i == 0) {
          cell.cellStyle = CellStyle(bold: true, fontSize: 14);
        }
      }

      // Encode the Excel file
      final List<int>? bytes = excel.encode();
      if (bytes == null) {
        throw Exception('Failed to encode Excel file');
      }

      // Get temporary directory to save the file
      final directory = await getTemporaryDirectory();
      final filePath =
          path.join(directory.path, 'product_upload_template.xlsx');

      // Save the Excel file
      File(filePath)
        ..createSync(recursive: true)
        ..writeAsBytesSync(bytes);

      setState(() {
        _statusMessage = 'Template ready!';
        _isLoading = false;
      });

      // Show a dialog with the file path
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Template Ready'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('The template has been created successfully.'),
                const SizedBox(height: 8),
                Text('File saved to: $filePath',
                    style: const TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                const Text('Please fill in the template and upload it back.'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      setState(() {
        _error = 'Error creating template: $e';
        _isLoading = false;
      });
    }
  }

  // Download specialized food product template
  Future<void> _downloadFoodTemplate() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Preparing food product template...';
    });

    try {
      // Generate the food product template
      final filePath = await FoodBulkUploadTemplate.generateTemplate();

      setState(() {
        _statusMessage = 'Food product template ready!';
        _isLoading = false;
      });

      // Show a dialog with the file path
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Food Product Template Ready'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                    'The food product template has been created successfully.'),
                const SizedBox(height: 8),
                Text('File saved to: $filePath',
                    style: const TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                const Text(
                    'This template is optimized for food products with:'),
                const SizedBox(height: 8),
                const Text('• Vegetarian/Non-vegetarian selection'),
                const Text('• Day-wise availability timings'),
                const Text('• Category automatically set to "food"'),
                const SizedBox(height: 16),
                const Text('Please fill in the template and upload it back.'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      setState(() {
        _error = 'Error creating food template: $e';
        _isLoading = false;
      });
    }
  }

  // Validate product data
  bool _validateProductData(Map<String, dynamic> data) {
    // Check required fields
    if (!data.containsKey('name') || data['name'].toString().trim().isEmpty) {
      return false;
    }
    if (!data.containsKey('description') ||
        data['description'].toString().trim().isEmpty) {
      return false;
    }
    if (!data.containsKey('price') || data['price'].toString().trim().isEmpty) {
      return false;
    }
    if (!data.containsKey('quantity') ||
        data['quantity'].toString().trim().isEmpty) {
      return false;
    }
    if (!data.containsKey('categoryId') ||
        data['categoryId'].toString().trim().isEmpty) {
      return false;
    }

    // Validate price format
    try {
      double.parse(data['price'].toString());
    } catch (e) {
      return false;
    }

    // Validate quantity format
    try {
      int.parse(data['quantity'].toString());
    } catch (e) {
      return false;
    }

    return true;
  }

  Future<void> _pickImages() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
      );

      if (result != null) {
        final newImages = result.paths
            .where((path) => path != null)
            .map((path) => File(path!))
            .toList();

        setState(() {
          _selectedImages.addAll(newImages);
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    }
  }

  Future<void> _uploadProducts() async {
    if (_products.isEmpty) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final service = ref.read(productServiceProvider);
      final authService = ref.read(authServiceProvider);
      final currentUser = await authService.getCurrentUser();

      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final errors = <String>[];

      // Reset counters
      setState(() {
        _successCount = 0;
        _errorCount = 0;
      });

      // Upload images first if any are selected
      List<String> uploadedImageUrls = [];
      if (_selectedImages.isNotEmpty) {
        setState(() {
          _statusMessage = 'Uploading images...';
        });

        try {
          // Get the storage service
          // Note: You'll need to implement or use your actual storage service here
          // This is a placeholder for demonstration purposes

          for (int i = 0; i < _selectedImages.length; i++) {
            final file = _selectedImages[i];
            final fileName = path.basename(file.path);

            setState(() {
              _statusMessage =
                  'Uploading image ${i + 1} of ${_selectedImages.length}...';
            });

            // Here you would upload the image to your storage service
            // For example, using Firebase Storage:
            // final ref = FirebaseStorage.instance.ref().child('products/$fileName');
            // await ref.putFile(file);
            // final url = await ref.getDownloadURL();
            // uploadedImageUrls.add(url);

            // For now, we'll simulate the upload with a delay
            await Future.delayed(const Duration(milliseconds: 500));

            // Add a placeholder URL
            uploadedImageUrls
                .add('https://example.com/productImages/$fileName');
          }

          setState(() {
            _statusMessage = 'Images uploaded successfully';
          });
        } catch (e) {
          setState(() {
            _error = 'Error uploading images: $e';
          });
        }
      }

      setState(() {
        _statusMessage = 'Creating products...';
      });

      for (final product in _products) {
        try {
          // Prepare images list - combine uploaded images with any URLs from the CSV
          List<String> productImages = [];

          // Add any image URLs from the CSV
          if (product.containsKey('images') && product['images'] is List) {
            productImages.addAll(List<String>.from(product['images']));
          }

          // Add the uploaded images to each product
          if (uploadedImageUrls.isNotEmpty) {
            productImages.addAll(uploadedImageUrls);
          }

          // Process specifications
          Map<String, dynamic>? specifications;
          if (product.containsKey('specifications') &&
              product['specifications'] is Map) {
            specifications =
                Map<String, dynamic>.from(product['specifications']);
          }

          // Process day-wise availability and food type for food products
          if (product['categoryId'] == 'food') {
            // Set categoryId to 'food' if not already set
            product['categoryId'] = 'food';

            // Process food type (veg/non-veg)
            if (product.containsKey('foodType')) {
              final foodType = product['foodType']?.toString() ?? 'veg';
              if (foodType == 'veg' || foodType == 'non-veg') {
                product['foodType'] = foodType;
              } else {
                product['foodType'] = 'veg'; // Default to vegetarian
              }
            } else {
              product['foodType'] = 'veg'; // Default to vegetarian
            }

            // Process day-wise availability from specialized template format
            if (!product.containsKey('dayWiseAvailability')) {
              final availability = <String, Map<String, String>>{};

              // Check for day-specific time fields
              final days = [
                'monday',
                'tuesday',
                'wednesday',
                'thursday',
                'friday',
                'saturday',
                'sunday'
              ];
              for (final day in days) {
                final fromKey = '${day}From';
                final toKey = '${day}To';

                if (product.containsKey(fromKey) &&
                    product.containsKey(toKey)) {
                  final fromTime = product[fromKey]?.toString() ?? '';
                  final toTime = product[toKey]?.toString() ?? '';

                  if (fromTime.isNotEmpty && toTime.isNotEmpty) {
                    availability[day.substring(0, 1).toUpperCase() +
                        day.substring(1)] = {
                      'from': fromTime,
                      'to': toTime,
                    };
                  }

                  // Remove the individual day fields to clean up the product map
                  product.remove(fromKey);
                  product.remove(toKey);
                }
              }

              if (availability.isNotEmpty) {
                product['dayWiseAvailability'] = availability;
              }
            }
          }

          // Calculate discount percentage if original price is provided
          double? originalPrice =
              double.tryParse(product['originalPrice']?.toString() ?? '');
          double price =
              double.tryParse(product['price']?.toString() ?? '0') ?? 0;
          double? discountPercentage;

          if (originalPrice != null && originalPrice > price) {
            discountPercentage =
                ((originalPrice - price) / originalPrice) * 100;
          }

          // Create the product model
          final productJson = {
            'id': DateTime.now().millisecondsSinceEpoch.toString(),
            'name': product['name'] ?? '',
            'description': product['description'] ?? '',
            'price': price,
            'originalPrice': originalPrice,
            'discountPercentage': discountPercentage,
            'quantity':
                int.tryParse(product['quantity']?.toString() ?? '0') ?? 0,
            'categoryId': product['categoryId'] ?? '',
            'sellerId': currentUser.id,
            'images': productImages,
            'productType': ProductType.physical.toString(),
            'productStatus': ProductStatus.pending.toString(),
            'brand': product['brand'],
            'unit': product['unit'],
            'weight': double.tryParse(product['weight']?.toString() ?? ''),
            'specifications': specifications,
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
          };

          // Add food type for food products
          if (product['categoryId'] == 'food' &&
              product.containsKey('foodType')) {
            productJson['foodType'] = product['foodType'];
          }

          // Add day-wise availability for food products
          if (product['categoryId'] == 'food' &&
              product.containsKey('dayWiseAvailability')) {
            productJson['dayWiseAvailability'] = product['dayWiseAvailability'];
          }

          final model = ProductModel.fromJson(productJson);

          await service.createOrUpdateProduct(model);

          setState(() {
            _successCount++;
            _statusMessage =
                'Created $_successCount of ${_products.length} products';
          });
        } catch (e) {
          errors.add('Failed to add product ${product['name']}: $e');
          setState(() {
            _errorCount++;
          });
        }
      }

      if (errors.isNotEmpty) {
        throw Exception(errors.join('\n'));
      }

      setState(() {
        _statusMessage = 'All products created successfully!';
        _isLoading = false;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully created $_successCount products'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bulk Product Upload'),
      ),
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const LoadingIndicator(),
                  if (_statusMessage.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(_statusMessage),
                  ],
                ],
              ),
            )
          : _error != null
              ? ErrorMessage(
                  message: _error!,
                  onRetry: () {
                    setState(() {
                      _error = null;
                    });
                  },
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Bulk Product Upload',
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'File Format',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Required columns:\n'
                                '• name\n'
                                '• description\n'
                                '• price\n'
                                '• quantity\n'
                                '• categoryId\n\n'
                                'Optional columns:\n'
                                '• originalPrice (for discounts)\n'
                                '• brand\n'
                                '• unit\n'
                                '• weight\n'
                                '• imageUrls (comma-separated URLs)\n'
                                '• specifications (format: key:value,key:value)\n'
                                '• dayWiseAvailability (for food products, format: day:from-to,day:from-to)',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          FilledButton.icon(
                            onPressed: _pickFile,
                            icon: const Icon(Icons.upload_file),
                            label: const Text('Select File'),
                          ),
                          const SizedBox(width: 16),
                          OutlinedButton.icon(
                            onPressed: _downloadTemplate,
                            icon: const Icon(Icons.download),
                            label: const Text('General Template'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // Food product template section
                      Card(
                        color: Colors.green.shade50,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.restaurant,
                                    color: Colors.green.shade700,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Food Products',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                          color: Colors.green.shade700,
                                        ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'Use this specialized template for food products with:',
                              ),
                              const SizedBox(height: 4),
                              const Text(
                                  '• Vegetarian/Non-vegetarian selection'),
                              const Text('• Day-wise availability timings'),
                              const Text(
                                  '• Category automatically set to "food"'),
                              const SizedBox(height: 16),
                              OutlinedButton.icon(
                                onPressed: _downloadFoodTemplate,
                                icon: const Icon(Icons.download),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.green.shade700,
                                ),
                                label: const Text('Food Product Template'),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Image upload section
                      if (_products.isNotEmpty) ...[
                        const SizedBox(height: 24),
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Add Product Images',
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Upload images to be used for all products. These will be added to any image URLs specified in the CSV file.',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                                const SizedBox(height: 16),
                                Row(
                                  children: [
                                    FilledButton.icon(
                                      onPressed: _pickImages,
                                      icon:
                                          const Icon(Icons.add_photo_alternate),
                                      label: const Text('Add Images'),
                                    ),
                                    const SizedBox(width: 16),
                                    Text(
                                      _selectedImages.isEmpty
                                          ? 'No images selected'
                                          : '${_selectedImages.length} images selected',
                                    ),
                                  ],
                                ),
                                if (_selectedImages.isNotEmpty) ...[
                                  const SizedBox(height: 16),
                                  SizedBox(
                                    height: 100,
                                    child: ListView.builder(
                                      scrollDirection: Axis.horizontal,
                                      itemCount: _selectedImages.length,
                                      itemBuilder: (context, index) {
                                        return Padding(
                                          padding:
                                              const EdgeInsets.only(right: 8),
                                          child: Stack(
                                            children: [
                                              ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                child: Image.file(
                                                  _selectedImages[index],
                                                  height: 100,
                                                  width: 100,
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                              Positioned(
                                                top: 0,
                                                right: 0,
                                                child: GestureDetector(
                                                  onTap: () {
                                                    setState(() {
                                                      _selectedImages
                                                          .removeAt(index);
                                                    });
                                                  },
                                                  child: Container(
                                                    decoration: BoxDecoration(
                                                      color: Colors.red,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12),
                                                    ),
                                                    padding:
                                                        const EdgeInsets.all(4),
                                                    child: const Icon(
                                                      Icons.close,
                                                      size: 16,
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ],

                      // Product preview section
                      if (_products.isNotEmpty) ...[
                        const SizedBox(height: 24),
                        Text(
                          'Preview (${_products.length} products)',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        SizedBox(
                          height: 400,
                          child: ListView.builder(
                            itemCount: _products.length,
                            itemBuilder: (context, index) {
                              final product = _products[index];
                              return Card(
                                child: ListTile(
                                  title: Text(product['name'] ?? ''),
                                  subtitle: Text(
                                    '₹${product['price']} • ${product['quantity']} in stock',
                                  ),
                                  trailing: IconButton(
                                    icon: const Icon(Icons.delete),
                                    onPressed: () {
                                      setState(() {
                                        _products.removeAt(index);
                                      });
                                    },
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 24),
                        FilledButton.icon(
                          onPressed: _uploadProducts,
                          icon: const Icon(Icons.cloud_upload),
                          label: const Text('Upload All Products'),
                          style: FilledButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),

                        // Results section if any uploads have been attempted
                        if (_successCount > 0 || _errorCount > 0) ...[
                          const SizedBox(height: 24),
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Upload Results',
                                    style:
                                        Theme.of(context).textTheme.titleMedium,
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.check_circle,
                                        color: Colors.green,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Text('Success: $_successCount'),
                                      const SizedBox(width: 24),
                                      Icon(
                                        Icons.error,
                                        color: Colors.red,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Text('Errors: $_errorCount'),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ],
                  ),
                ),
    );
  }
}
