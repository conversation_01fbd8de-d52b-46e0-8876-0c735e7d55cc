import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:shivish/apps/technician/providers/holiday_settings_provider.dart';

class HolidaySettingsScreen extends ConsumerStatefulWidget {
  const HolidaySettingsScreen({super.key});

  @override
  ConsumerState<HolidaySettingsScreen> createState() =>
      _HolidaySettingsScreenState();
}

class _HolidaySettingsScreenState extends ConsumerState<HolidaySettingsScreen> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  Set<DateTime> _holidays = {};

  @override
  void initState() {
    super.initState();
    _loadHolidays();
  }

  Future<void> _loadHolidays() async {
    final holidays = await ref.read(holidaySettingsProvider.future);
    if (mounted) {
      setState(() {
        _holidays = holidays;
      });
    }
  }

  Future<void> _saveHolidays() async {
    try {
      await ref.read(holidaySettingsProvider.notifier).saveHolidays(_holidays);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Holiday settings updated successfully'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update holiday settings: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final holidayState = ref.watch(holidaySettingsProvider);

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Holidays',
      ),
      body: holidayState.when(
        data: (holidays) => SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Select your holidays',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      TableCalendar(
                        firstDay: DateTime.now(),
                        lastDay: DateTime.now().add(const Duration(days: 365)),
                        focusedDay: _focusedDay,
                        calendarFormat: _calendarFormat,
                        selectedDayPredicate: (day) {
                          return isSameDay(_selectedDay, day);
                        },
                        holidayPredicate: (day) {
                          return _holidays.contains(day);
                        },
                        onDaySelected: (selectedDay, focusedDay) {
                          setState(() {
                            _selectedDay = selectedDay;
                            _focusedDay = focusedDay;
                            if (_holidays.contains(selectedDay)) {
                              _holidays.remove(selectedDay);
                            } else {
                              _holidays.add(selectedDay);
                            }
                          });
                        },
                        onFormatChanged: (format) {
                          setState(() {
                            _calendarFormat = format;
                          });
                        },
                        calendarStyle: const CalendarStyle(
                          holidayTextStyle: TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Upcoming Holidays',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _holidays.length,
                        itemBuilder: (context, index) {
                          final holiday = _holidays.elementAt(index);
                          return ListTile(
                            title: Text(
                              '${holiday.day}/${holiday.month}/${holiday.year}',
                            ),
                            trailing: IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () {
                                setState(() {
                                  _holidays.remove(holiday);
                                });
                              },
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              AppButton(
                onPressed: _saveHolidays,
                child: const Text('Save Holidays'),
              ),
            ],
          ),
        ),
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }
}
