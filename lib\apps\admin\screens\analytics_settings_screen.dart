import 'package:flutter/material.dart';
import 'package:shivish/shared/services/analytics/analytics_service.dart';
import 'package:shivish/shared/services/export/export_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shivish/shared/utils/logger.dart';
import 'dart:io';

class AnalyticsSettingsScreen extends StatefulWidget {
  const AnalyticsSettingsScreen({super.key});

  @override
  State<AnalyticsSettingsScreen> createState() =>
      _AnalyticsSettingsScreenState();
}

class _AnalyticsSettingsScreenState extends State<AnalyticsSettingsScreen> {
  final _analyticsService = AnalyticsService();
  final _exportService = ExportService();
  final _logger = getLogger('AnalyticsSettingsScreen');
  bool _enableAnalytics = true;
  bool _enableCrashReporting = true;
  bool _enableUserBehaviorTracking = true;
  bool _enablePerformanceMonitoring = true;
  bool _enableCustomEvents = true;
  bool _enableHeatmaps = false;
  bool _enableSessionRecording = false;
  String _dataRetentionPeriod = '12 months';
  String _dataCollectionFrequency = 'Real-time';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _enableAnalytics = prefs.getBool('enable_analytics') ?? true;
        _enableCrashReporting = prefs.getBool('enable_crash_reporting') ?? true;
        _enableUserBehaviorTracking =
            prefs.getBool('enable_user_behavior_tracking') ?? true;
        _enablePerformanceMonitoring =
            prefs.getBool('enable_performance_monitoring') ?? true;
        _enableCustomEvents = prefs.getBool('enable_custom_events') ?? true;
        _enableHeatmaps = prefs.getBool('enable_heatmaps') ?? false;
        _enableSessionRecording =
            prefs.getBool('enable_session_recording') ?? false;
        _dataRetentionPeriod =
            prefs.getString('data_retention_period') ?? '12 months';
        _dataCollectionFrequency =
            prefs.getString('data_collection_frequency') ?? 'Real-time';
      });
    } catch (e) {
      debugPrint('Error loading analytics settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('enable_analytics', _enableAnalytics);
      await prefs.setBool('enable_crash_reporting', _enableCrashReporting);
      await prefs.setBool(
          'enable_user_behavior_tracking', _enableUserBehaviorTracking);
      await prefs.setBool(
          'enable_performance_monitoring', _enablePerformanceMonitoring);
      await prefs.setBool('enable_custom_events', _enableCustomEvents);
      await prefs.setBool('enable_heatmaps', _enableHeatmaps);
      await prefs.setBool('enable_session_recording', _enableSessionRecording);
      await prefs.setString('data_retention_period', _dataRetentionPeriod);
      await prefs.setString(
          'data_collection_frequency', _dataCollectionFrequency);

      // Log analytics settings change
      await _analyticsService.logEvent(
        name: 'analytics_settings_changed',
        parameters: {
          'analytics_enabled': _enableAnalytics,
          'crash_reporting_enabled': _enableCrashReporting,
          'user_behavior_tracking_enabled': _enableUserBehaviorTracking,
          'performance_monitoring_enabled': _enablePerformanceMonitoring,
        },
      );

      _logger.info('Analytics settings saved: analytics_enabled=$_enableAnalytics');
    } catch (e) {
      debugPrint('Error saving analytics settings: $e');
    }
  }

  Future<void> _showDataRetentionDialog() async {
    final periods = [
      '1 month',
      '3 months',
      '6 months',
      '12 months',
      '24 months',
      '36 months'
    ];
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Data Retention Period'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: periods.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(periods[index]),
                onTap: () => Navigator.pop(context, periods[index]),
              );
            },
          ),
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _dataRetentionPeriod = result;
      });
      await _saveSettings();
    }
  }

  Future<void> _showDataCollectionFrequencyDialog() async {
    final frequencies = ['Real-time', 'Hourly', 'Daily', 'Weekly'];
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Data Collection Frequency'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: frequencies.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(frequencies[index]),
                onTap: () => Navigator.pop(context, frequencies[index]),
              );
            },
          ),
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _dataCollectionFrequency = result;
      });
      await _saveSettings();
    }
  }

  Future<void> _exportData() async {
    try {
      _logger.info('Exporting analytics data');
      final analyticsData = await _analyticsService.getAnalyticsData(timeRange: 'month');
      final filePath =
          await _exportService.exportAnalyticsDataToCsv(analyticsData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Data exported to: $filePath'),
            action: SnackBarAction(
              label: 'Open',
              onPressed: () async {
                final file = File(filePath);
                if (await file.exists()) {
                  final uri = Uri.file(filePath);
                  if (await canLaunchUrl(uri)) {
                    await launchUrl(uri);
                  } else {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Could not open the file'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error exporting data: $e')),
        );
      }
    }
  }

  Future<void> _deleteData() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Analytics Data'),
        content: const Text(
            'Are you sure you want to delete all analytics data? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        _logger.info('Resetting analytics data');
        // Clear analytics cache instead of resetting all data
        await _analyticsService.clearCache();

        // Log the reset event
        await _analyticsService.logEvent(
          name: 'analytics_data_reset',
          parameters: {'reset_by': 'admin'},
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Analytics cache cleared successfully')),
          );
        }
        _logger.info('Analytics data reset completed');
      } catch (e) {
        _logger.severe('Error resetting analytics: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting data: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics Settings'),
      ),
      body: ListView(
        children: [
          _buildSection(
            context,
            'General Analytics',
            [
              SwitchListTile(
                title: const Text('Enable Analytics'),
                subtitle: const Text('Collect and analyze app usage data'),
                value: _enableAnalytics,
                onChanged: (value) async {
                  setState(() {
                    _enableAnalytics = value;
                  });
                  await _saveSettings();
                },
              ),
              SwitchListTile(
                title: const Text('Crash Reporting'),
                subtitle: const Text('Collect crash reports and error logs'),
                value: _enableCrashReporting,
                onChanged: (value) async {
                  setState(() {
                    _enableCrashReporting = value;
                  });
                  await _saveSettings();
                },
              ),
              SwitchListTile(
                title: const Text('User Behavior Tracking'),
                subtitle: const Text('Track user interactions and patterns'),
                value: _enableUserBehaviorTracking,
                onChanged: (value) async {
                  setState(() {
                    _enableUserBehaviorTracking = value;
                  });
                  await _saveSettings();
                },
              ),
            ],
          ),
          _buildSection(
            context,
            'Advanced Analytics',
            [
              SwitchListTile(
                title: const Text('Performance Monitoring'),
                subtitle: const Text('Monitor app performance metrics'),
                value: _enablePerformanceMonitoring,
                onChanged: (value) async {
                  setState(() {
                    _enablePerformanceMonitoring = value;
                  });
                  await _saveSettings();
                },
              ),
              SwitchListTile(
                title: const Text('Custom Events'),
                subtitle: const Text('Track custom events and conversions'),
                value: _enableCustomEvents,
                onChanged: (value) async {
                  setState(() {
                    _enableCustomEvents = value;
                  });
                  await _saveSettings();
                },
              ),
              SwitchListTile(
                title: const Text('Heatmaps'),
                subtitle: const Text('Generate user interaction heatmaps'),
                value: _enableHeatmaps,
                onChanged: (value) async {
                  setState(() {
                    _enableHeatmaps = value;
                  });
                  await _saveSettings();
                },
              ),
              SwitchListTile(
                title: const Text('Session Recording'),
                subtitle: const Text('Record user sessions for analysis'),
                value: _enableSessionRecording,
                onChanged: (value) async {
                  setState(() {
                    _enableSessionRecording = value;
                  });
                  await _saveSettings();
                },
              ),
            ],
          ),
          _buildSection(
            context,
            'Data Management',
            [
              ListTile(
                title: const Text('Data Retention Period'),
                subtitle: Text(_dataRetentionPeriod),
                trailing: const Icon(Icons.chevron_right),
                onTap: _showDataRetentionDialog,
              ),
              ListTile(
                title: const Text('Data Collection Frequency'),
                subtitle: Text(_dataCollectionFrequency),
                trailing: const Icon(Icons.chevron_right),
                onTap: _showDataCollectionFrequencyDialog,
              ),
              ListTile(
                title: const Text('Data Export'),
                subtitle: const Text('Export analytics data'),
                trailing: const Icon(Icons.chevron_right),
                onTap: _exportData,
              ),
              ListTile(
                title: const Text('Data Deletion'),
                subtitle: const Text('Delete collected analytics data'),
                trailing: const Icon(Icons.chevron_right),
                onTap: _deleteData,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
      BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        ...children,
      ],
    );
  }
}
