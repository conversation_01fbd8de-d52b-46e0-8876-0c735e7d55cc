import 'package:flutter/material.dart';
import 'package:shivish/shared/models/payment_gateway.dart';

class PaymentGatewayListItem extends StatelessWidget {
  final PaymentGateway gateway;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const PaymentGatewayListItem({
    super.key,
    required this.gateway,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(gateway.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${gateway.type}'),
            Text('Transaction Fee: ${gateway.transactionFee}%'),
            Text('Status: ${gateway.isActive ? 'Active' : 'Inactive'}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: onEdit,
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: onDelete,
            ),
          ],
        ),
      ),
    );
  }
}
