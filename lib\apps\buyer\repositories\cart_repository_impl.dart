import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../shared/models/product/product_model.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';
import '../domain/entities/cart/cart_model.dart';
import 'cart_repository.dart';

/// Cart repository implementation
class CartRepositoryImpl implements CartRepository {
  /// Creates a [CartRepositoryImpl]
  CartRepositoryImpl({
    DatabaseService? databaseService,
    SupabaseClient? supabase,
  })  : _databaseService = databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment()),
        _supabase = supabase ?? Supabase.instance.client;

  final DatabaseService _databaseService;
  final SupabaseClient _supabase;
  final _logger = getLogger('CartRepositoryImpl');

  String get _cartId => 'cart_${_supabase.auth.currentUser!.id}';

  Future<void> _saveCart(CartModel cart) async {
    await _databaseService.update('carts', _cartId, {
      'id': _cartId,
      ...cart.toJson(),
    });
  }

  @override
  Future<CartModel> getCart() async {
    try {
      final cartData = await _databaseService.find('carts', _cartId);
      if (cartData == null) {
        final cart = CartModel.empty();
        await _databaseService.create('carts', {
          'id': _cartId,
          ...cart.toJson(),
        });
        return cart;
      }
      return CartModel.fromJson(cartData);
    } catch (e) {
      _logger.severe('Failed to get cart: $e');
      throw Exception('Failed to get cart: $e');
    }
  }

  @override
  Future<void> addToCart({
    required ProductModel product,
    required int quantity,
    String? sellerId,
    String? notes,
  }) async {
    try {
      final cart = await getCart();
      final existingItemIndex =
          cart.items.indexWhere((item) => item.product.id == product.id);

      if (existingItemIndex != -1) {
        final existingItem = cart.items[existingItemIndex];
        final updatedItems = List<CartItemModel>.from(cart.items);
        updatedItems[existingItemIndex] = existingItem.copyWith(
          quantity: existingItem.quantity + quantity,
          sellerId: sellerId ?? existingItem.sellerId,
          notes: notes ?? existingItem.notes,
          updatedAt: DateTime.now(),
        );

        final updatedCart = cart.copyWith(
          items: updatedItems,
          updatedAt: DateTime.now(),
        );

        await _saveCart(updatedCart);
      } else {
        final newItem = CartItemModel(
          product: product,
          quantity: quantity,
          sellerId: sellerId ?? product.sellerId,
          notes: notes,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final updatedCart = cart.copyWith(
          items: [...cart.items, newItem],
          updatedAt: DateTime.now(),
        );

        await _saveCart(updatedCart);
      }
    } catch (e) {
      throw Exception('Failed to add to cart: $e');
    }
  }

  @override
  Future<void> updateQuantity({
    required ProductModel product,
    required int quantity,
  }) async {
    try {
      final cart = await getCart();
      final existingItemIndex =
          cart.items.indexWhere((item) => item.product.id == product.id);

      if (existingItemIndex != -1) {
        final existingItem = cart.items[existingItemIndex];
        final updatedItems = List<CartItemModel>.from(cart.items);

        if (quantity > 0) {
          updatedItems[existingItemIndex] = existingItem.copyWith(
            quantity: quantity,
            updatedAt: DateTime.now(),
          );

          final updatedCart = cart.copyWith(
            items: updatedItems,
            updatedAt: DateTime.now(),
          );

          await _saveCart(updatedCart);
        } else {
          await removeFromCart(product);
        }
      }
    } catch (e) {
      throw Exception('Failed to update quantity: $e');
    }
  }

  @override
  Future<void> removeFromCart(ProductModel product) async {
    try {
      final cart = await getCart();
      final existingItemIndex =
          cart.items.indexWhere((item) => item.product.id == product.id);

      if (existingItemIndex != -1) {
        final updatedItems = List<CartItemModel>.from(cart.items)
          ..removeAt(existingItemIndex);

        final updatedCart = cart.copyWith(
          items: updatedItems,
          updatedAt: DateTime.now(),
        );

        await _saveCart(updatedCart);
      }
    } catch (e) {
      throw Exception('Failed to remove from cart: $e');
    }
  }

  @override
  Future<void> clearCart() async {
    try {
      final cart = CartModel.empty();
      await _saveCart(cart);
    } catch (e) {
      throw Exception('Failed to clear cart: $e');
    }
  }

  @override
  Future<double> getTotalPrice() async {
    try {
      final cart = await getCart();
      return cart.totalPrice;
    } catch (e) {
      throw Exception('Failed to get total price: $e');
    }
  }

  @override
  Future<int> getTotalQuantity() async {
    try {
      final cart = await getCart();
      return cart.totalQuantity;
    } catch (e) {
      throw Exception('Failed to get total quantity: $e');
    }
  }

  @override
  Future<void> updateSeller({
    required ProductModel product,
    required String sellerId,
  }) async {
    try {
      final cart = await getCart();
      final existingItemIndex =
          cart.items.indexWhere((item) => item.product.id == product.id);

      if (existingItemIndex != -1) {
        final existingItem = cart.items[existingItemIndex];
        final updatedItems = List<CartItemModel>.from(cart.items);
        updatedItems[existingItemIndex] = existingItem.copyWith(
          sellerId: sellerId,
          updatedAt: DateTime.now(),
        );

        final updatedCart = cart.copyWith(
          items: updatedItems,
          updatedAt: DateTime.now(),
        );

        await _saveCart(updatedCart);
      }
    } catch (e) {
      throw Exception('Failed to update seller: $e');
    }
  }

  @override
  Future<void> updateNotes({
    required ProductModel product,
    required String notes,
  }) async {
    try {
      final cart = await getCart();
      final existingItemIndex =
          cart.items.indexWhere((item) => item.product.id == product.id);

      if (existingItemIndex != -1) {
        final existingItem = cart.items[existingItemIndex];
        final updatedItems = List<CartItemModel>.from(cart.items);
        updatedItems[existingItemIndex] = existingItem.copyWith(
          notes: notes,
          updatedAt: DateTime.now(),
        );

        final updatedCart = cart.copyWith(
          items: updatedItems,
          updatedAt: DateTime.now(),
        );

        await _saveCart(updatedCart);
      }
    } catch (e) {
      throw Exception('Failed to update notes: $e');
    }
  }
}
