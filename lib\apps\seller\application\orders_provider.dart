import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/order/order_model.dart';
import '../domain/repositories/orders_repository.dart';
import '../data/repositories/orders_repository_impl.dart';

class OrdersState {
  final bool isLoading;
  final List<OrderModel> orders;
  final String? error;
  final OrderModel? selectedOrder;

  const OrdersState({
    this.isLoading = false,
    this.orders = const [],
    this.error,
    this.selectedOrder,
  });

  OrdersState copyWith({
    bool? isLoading,
    List<OrderModel>? orders,
    String? error,
    OrderModel? selectedOrder,
  }) {
    return OrdersState(
      isLoading: isLoading ?? this.isLoading,
      orders: orders ?? this.orders,
      error: error ?? this.error,
      selectedOrder: selectedOrder ?? this.selectedOrder,
    );
  }
}

class OrdersNotifier extends StateNotifier<OrdersState> {
  final OrdersRepository _ordersRepository;

  OrdersNotifier(this._ordersRepository) : super(OrdersState());

  Future<void> loadOrders() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final orders = await _ordersRepository.getOrders();
      state = state.copyWith(orders: orders, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> loadOrder(String id) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final order = await _ordersRepository.getOrder(id);
      if (order != null) {
        state = state.copyWith(selectedOrder: order, isLoading: false);
      } else {
        state = state.copyWith(error: 'Order not found', isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> createOrder(OrderModel order) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final createdOrder = await _ordersRepository.createOrder(order);
      state = state.copyWith(
        orders: [...state.orders, createdOrder],
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> updateOrder(OrderModel order) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final updatedOrder = await _ordersRepository.updateOrder(order);
      state = state.copyWith(
        orders: state.orders
            .map((o) => o.id == order.id ? updatedOrder : o)
            .toList(),
        selectedOrder: updatedOrder,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> deleteOrder(String id) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      await _ordersRepository.deleteOrder(id);
      state = state.copyWith(
        orders: state.orders.where((o) => o.id != id).toList(),
        selectedOrder: state.selectedOrder?.id == id
            ? null
            : state.selectedOrder,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> loadOrdersByStatus(OrderStatus status) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final orders = await _ordersRepository.getOrdersByStatus(status);
      state = state.copyWith(orders: orders, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> loadOrdersBySeller(String sellerId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final orders = await _ordersRepository.getOrdersBySeller(sellerId);
      state = state.copyWith(orders: orders, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> loadOrdersByBuyer(String buyerId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final orders = await _ordersRepository.getOrdersByBuyer(buyerId);
      state = state.copyWith(orders: orders, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }
}

final ordersProvider = StateNotifierProvider<OrdersNotifier, OrdersState>((
  ref,
) {
  final ordersRepository = ref.watch(ordersRepositoryImplProvider);
  return OrdersNotifier(ordersRepository);
});
