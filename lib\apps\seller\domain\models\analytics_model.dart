import 'package:freezed_annotation/freezed_annotation.dart';

part 'analytics_model.freezed.dart';
part 'analytics_model.g.dart';

@freezed
sealed class SalesAnalytics with _$SalesAnalytics {
  const factory SalesAnalytics({
    required double totalRevenue,
    required int totalOrders,
    required double averageOrderValue,
    required double growthRate,
    required List<SalesPoint> dailySales,
    required Map<String, double> salesByCategory,
  }) = _SalesAnalytics;

  factory SalesAnalytics.fromJson(Map<String, dynamic> json) =>
      _$SalesAnalyticsFromJson(json);
}

@freezed
sealed class SalesPoint with _$SalesPoint {
  const factory SalesPoint({
    required DateTime date,
    required double amount,
  }) = _SalesPoint;

  factory SalesPoint.fromJson(Map<String, dynamic> json) =>
      _$SalesPointFromJson(json);
}

@freezed
sealed class ProductAnalytics with _$ProductAnalytics {
  const factory ProductAnalytics({
    required int totalProducts,
    required int outOfStockProducts,
    required double averageRating,
    required List<ProductPerformance> topProducts,
    required List<ProductStock> lowStockProducts,
    required Map<String, int> productsByCategory,
  }) = _ProductAnalytics;

  factory ProductAnalytics.fromJson(Map<String, dynamic> json) =>
      _$ProductAnalyticsFromJson(json);
}

@freezed
sealed class ProductPerformance with _$ProductPerformance {
  const factory ProductPerformance({
    required String name,
    required int quantity,
    required double revenue,
    required double rating,
    String? imageUrl,
    @Default(0) int soldCount,
  }) = _ProductPerformance;

  factory ProductPerformance.fromJson(Map<String, dynamic> json) =>
      _$ProductPerformanceFromJson(json);
}

@freezed
sealed class ProductStock with _$ProductStock {
  const factory ProductStock({
    required String name,
    required int quantity,
  }) = _ProductStock;

  factory ProductStock.fromJson(Map<String, dynamic> json) =>
      _$ProductStockFromJson(json);
}

@freezed
sealed class CustomerAnalytics with _$CustomerAnalytics {
  const factory CustomerAnalytics({
    required int totalCustomers,
    required int newCustomers,
    required double customerRetentionRate,
    required double averageCustomerLifetimeValue,
    required List<CustomerSegment> segments,
    required Map<String, int> customersByLocation,
    required List<CustomerActivity> recentActivity,
    @Default([]) List<CustomerGrowthPoint> growthData,
  }) = _CustomerAnalytics;

  factory CustomerAnalytics.fromJson(Map<String, dynamic> json) =>
      _$CustomerAnalyticsFromJson(json);
}

@freezed
sealed class CustomerSegment with _$CustomerSegment {
  const factory CustomerSegment({
    required String name,
    required int count,
    required double averageOrderValue,
    required double revenue,
  }) = _CustomerSegment;

  factory CustomerSegment.fromJson(Map<String, dynamic> json) =>
      _$CustomerSegmentFromJson(json);
}

@freezed
sealed class CustomerActivity with _$CustomerActivity {
  const factory CustomerActivity({
    required String customerId,
    required String action,
    required DateTime timestamp,
  }) = _CustomerActivity;

  factory CustomerActivity.fromJson(Map<String, dynamic> json) =>
      _$CustomerActivityFromJson(json);
}

@freezed
sealed class CustomerGrowthPoint with _$CustomerGrowthPoint {
  const factory CustomerGrowthPoint({
    required DateTime date,
    required int count,
  }) = _CustomerGrowthPoint;

  factory CustomerGrowthPoint.fromJson(Map<String, dynamic> json) =>
      _$CustomerGrowthPointFromJson(json);
}

@freezed
sealed class PerformanceMetrics with _$PerformanceMetrics {
  const factory PerformanceMetrics({
    required double conversionRate,
    required double returnRate,
    required double averageProcessingTime,
    required double averageDeliveryTime,
    required Map<DateTime, MetricPoint> performanceByDay,
    required List<String> improvements,
  }) = _PerformanceMetrics;

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) =>
      _$PerformanceMetricsFromJson(json);
}

@freezed
sealed class MetricPoint with _$MetricPoint {
  const factory MetricPoint({
    required double value,
  }) = _MetricPoint;

  factory MetricPoint.fromJson(Map<String, dynamic> json) =>
      _$MetricPointFromJson(json);
}
