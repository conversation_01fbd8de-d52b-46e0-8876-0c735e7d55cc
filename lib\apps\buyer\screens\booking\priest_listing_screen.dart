import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/providers/priest_provider.dart';
import '../../../../shared/models/priest.dart';
import 'priest_details_screen.dart';

class PriestListingScreen extends ConsumerStatefulWidget {
  const PriestListingScreen({super.key});

  @override
  ConsumerState<PriestListingScreen> createState() =>
      _PriestListingScreenState();
}

class _PriestListingScreenState extends ConsumerState<PriestListingScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedSpecialization = 'All';
  String _selectedLanguage = 'All';
  double _minRating = 0.0;
  bool _showFilters = false;

  final List<String> _specializations = [
    'All',
    'Puja',
    'Havan',
    'Yagya',
    'Marriage',
    'Funeral',
    'Other'
  ];

  final List<String> _languages = [
    'All',
    'Hindi',
    'Telugu',
    'Tamil',
    'Kannada',
    'Marathi',
    'English',
    'Sanskrit',
    'Gujarati',
    'Other'
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<Priest> _getFilteredPriests(List<Priest> priests) {
    return priests.where((priest) {
      // Get specialization from the list
      final specialization = priest.specializations.isNotEmpty
          ? priest.specializations.first
          : '';

      final matchesSearch = priest.name
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()) ||
          specialization
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()) ||
          priest.description.toLowerCase().contains(_searchQuery.toLowerCase());

      final matchesSpecialization = _selectedSpecialization == 'All' ||
          specialization == _selectedSpecialization;

      final matchesLanguage = _selectedLanguage == 'All' ||
          priest.languages.contains(_selectedLanguage);

      final matchesRating = priest.rating >= _minRating;

      return matchesSearch &&
          matchesSpecialization &&
          matchesLanguage &&
          matchesRating;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final priestsAsync = ref.watch(priestListProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Available Priests'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              showSearch(
                context: context,
                delegate: PriestSearchDelegate(
                  searchController: _searchController,
                  onSearch: (query) {
                    setState(() => _searchQuery = query);
                  },
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              setState(() => _showFilters = !_showFilters);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          if (_showFilters)
            Container(
              padding: const EdgeInsets.all(16),
              color: theme.colorScheme.surfaceContainerHighest,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Specialization',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _specializations.map((spec) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: Text(spec),
                            selected: _selectedSpecialization == spec,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() => _selectedSpecialization = spec);
                              }
                            },
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Language',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _languages.map((lang) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: Text(lang),
                            selected: _selectedLanguage == lang,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() => _selectedLanguage = lang);
                              }
                            },
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Minimum Rating',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  Slider(
                    value: _minRating,
                    min: 0,
                    max: 5,
                    divisions: 10,
                    label: _minRating.toStringAsFixed(1),
                    onChanged: (value) {
                      setState(() => _minRating = value);
                    },
                  ),
                ],
              ),
            ),
          Expanded(
            child: priestsAsync.when(
              data: (priests) {
                final filteredPriests = _getFilteredPriests(priests);
                if (filteredPriests.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: theme.colorScheme.primary.withAlpha(128),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No Priests Found',
                          style: theme.textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Try different search terms or filters',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.75,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: filteredPriests.length,
                  itemBuilder: (context, index) {
                    final priest = filteredPriests[index];
                    return Card(
                      clipBehavior: Clip.antiAlias,
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => PriestDetailsScreen(
                                priestId: priest.id,
                              ),
                            ),
                          );
                        },
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AspectRatio(
                              aspectRatio: 1,
                              child: Image.network(
                                priest.profileImage ?? '',
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return const Center(
                                    child: Icon(Icons.person, size: 64),
                                  );
                                },
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    priest.name,
                                    style: theme.textTheme.titleMedium,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    priest.specializations.isNotEmpty
                                        ? priest.specializations.first
                                        : "General",
                                    style: theme.textTheme.bodyMedium,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      const Icon(Icons.star,
                                          size: 16, color: Colors.amber),
                                      const SizedBox(width: 4),
                                      Text(
                                        priest.rating.toStringAsFixed(1),
                                        style: theme.textTheme.bodySmall,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Text('Error: $error'),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class PriestSearchDelegate extends SearchDelegate<String> {
  final TextEditingController searchController;
  final Function(String) onSearch;

  PriestSearchDelegate({
    required this.searchController,
    required this.onSearch,
  });

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
          searchController.clear();
          onSearch('');
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    onSearch(query);
    return const SizedBox.shrink();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    onSearch(query);
    return const SizedBox.shrink();
  }
}
