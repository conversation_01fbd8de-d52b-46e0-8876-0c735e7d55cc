import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/buyer/buyer_routes.dart';
import 'priest_booking_screen.dart';
import 'technician_booking_screen.dart';

class BookingScreen extends ConsumerStatefulWidget {
  const BookingScreen({super.key});

  @override
  ConsumerState<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends ConsumerState<BookingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // User will always be signed in when accessing this screen

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Bookings'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Priest'),
            Tab(text: 'Technician'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          PriestBookingScreen(),
          TechnicianBookingScreen(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) {
              return AlertDialog(
                title: const Text('Book a Service'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      leading: const Icon(Icons.person),
                      title: const Text('Book a Priest'),
                      onTap: () {
                        Navigator.of(context).pop();
                        _tabController.animateTo(0);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.build),
                      title: const Text('Book a Technician'),
                      onTap: () {
                        Navigator.of(context).pop();
                        _tabController.animateTo(1);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.medical_services),
                      title: const Text('Book a Doctor'),
                      onTap: () {
                        Navigator.of(context).pop();
                        context.go(BuyerRoutes.healthcareDoctors);
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
