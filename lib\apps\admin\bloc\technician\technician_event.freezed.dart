// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'technician_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$TechnicianEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TechnicianEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TechnicianEvent()';
}


}

/// @nodoc
class $TechnicianEventCopyWith<$Res>  {
$TechnicianEventCopyWith(TechnicianEvent _, $Res Function(TechnicianEvent) __);
}


/// Adds pattern-matching-related methods to [TechnicianEvent].
extension TechnicianEventPatterns on TechnicianEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _LoadTechnicians value)?  loadTechnicians,TResult Function( _LoadMoreTechnicians value)?  loadMoreTechnicians,TResult Function( _UpdateTechnicianStatus value)?  updateTechnicianStatus,TResult Function( _DeleteTechnician value)?  deleteTechnician,TResult Function( _ApplyFilters value)?  applyFilters,required TResult orElse(),}){
final _that = this;
switch (_that) {
case _LoadTechnicians() when loadTechnicians != null:
return loadTechnicians(_that);case _LoadMoreTechnicians() when loadMoreTechnicians != null:
return loadMoreTechnicians(_that);case _UpdateTechnicianStatus() when updateTechnicianStatus != null:
return updateTechnicianStatus(_that);case _DeleteTechnician() when deleteTechnician != null:
return deleteTechnician(_that);case _ApplyFilters() when applyFilters != null:
return applyFilters(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _LoadTechnicians value)  loadTechnicians,required TResult Function( _LoadMoreTechnicians value)  loadMoreTechnicians,required TResult Function( _UpdateTechnicianStatus value)  updateTechnicianStatus,required TResult Function( _DeleteTechnician value)  deleteTechnician,required TResult Function( _ApplyFilters value)  applyFilters,}){
final _that = this;
switch (_that) {
case _LoadTechnicians():
return loadTechnicians(_that);case _LoadMoreTechnicians():
return loadMoreTechnicians(_that);case _UpdateTechnicianStatus():
return updateTechnicianStatus(_that);case _DeleteTechnician():
return deleteTechnician(_that);case _ApplyFilters():
return applyFilters(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _LoadTechnicians value)?  loadTechnicians,TResult? Function( _LoadMoreTechnicians value)?  loadMoreTechnicians,TResult? Function( _UpdateTechnicianStatus value)?  updateTechnicianStatus,TResult? Function( _DeleteTechnician value)?  deleteTechnician,TResult? Function( _ApplyFilters value)?  applyFilters,}){
final _that = this;
switch (_that) {
case _LoadTechnicians() when loadTechnicians != null:
return loadTechnicians(_that);case _LoadMoreTechnicians() when loadMoreTechnicians != null:
return loadMoreTechnicians(_that);case _UpdateTechnicianStatus() when updateTechnicianStatus != null:
return updateTechnicianStatus(_that);case _DeleteTechnician() when deleteTechnician != null:
return deleteTechnician(_that);case _ApplyFilters() when applyFilters != null:
return applyFilters(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  loadTechnicians,TResult Function()?  loadMoreTechnicians,TResult Function( Technician technician,  String status,  String? notes,  bool isActive)?  updateTechnicianStatus,TResult Function( String id)?  deleteTechnician,TResult Function( Map<String, dynamic> filters)?  applyFilters,required TResult orElse(),}) {final _that = this;
switch (_that) {
case _LoadTechnicians() when loadTechnicians != null:
return loadTechnicians();case _LoadMoreTechnicians() when loadMoreTechnicians != null:
return loadMoreTechnicians();case _UpdateTechnicianStatus() when updateTechnicianStatus != null:
return updateTechnicianStatus(_that.technician,_that.status,_that.notes,_that.isActive);case _DeleteTechnician() when deleteTechnician != null:
return deleteTechnician(_that.id);case _ApplyFilters() when applyFilters != null:
return applyFilters(_that.filters);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  loadTechnicians,required TResult Function()  loadMoreTechnicians,required TResult Function( Technician technician,  String status,  String? notes,  bool isActive)  updateTechnicianStatus,required TResult Function( String id)  deleteTechnician,required TResult Function( Map<String, dynamic> filters)  applyFilters,}) {final _that = this;
switch (_that) {
case _LoadTechnicians():
return loadTechnicians();case _LoadMoreTechnicians():
return loadMoreTechnicians();case _UpdateTechnicianStatus():
return updateTechnicianStatus(_that.technician,_that.status,_that.notes,_that.isActive);case _DeleteTechnician():
return deleteTechnician(_that.id);case _ApplyFilters():
return applyFilters(_that.filters);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  loadTechnicians,TResult? Function()?  loadMoreTechnicians,TResult? Function( Technician technician,  String status,  String? notes,  bool isActive)?  updateTechnicianStatus,TResult? Function( String id)?  deleteTechnician,TResult? Function( Map<String, dynamic> filters)?  applyFilters,}) {final _that = this;
switch (_that) {
case _LoadTechnicians() when loadTechnicians != null:
return loadTechnicians();case _LoadMoreTechnicians() when loadMoreTechnicians != null:
return loadMoreTechnicians();case _UpdateTechnicianStatus() when updateTechnicianStatus != null:
return updateTechnicianStatus(_that.technician,_that.status,_that.notes,_that.isActive);case _DeleteTechnician() when deleteTechnician != null:
return deleteTechnician(_that.id);case _ApplyFilters() when applyFilters != null:
return applyFilters(_that.filters);case _:
  return null;

}
}

}

/// @nodoc


class _LoadTechnicians implements TechnicianEvent {
  const _LoadTechnicians();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadTechnicians);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TechnicianEvent.loadTechnicians()';
}


}




/// @nodoc


class _LoadMoreTechnicians implements TechnicianEvent {
  const _LoadMoreTechnicians();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadMoreTechnicians);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TechnicianEvent.loadMoreTechnicians()';
}


}




/// @nodoc


class _UpdateTechnicianStatus implements TechnicianEvent {
  const _UpdateTechnicianStatus({required this.technician, required this.status, this.notes, required this.isActive});
  

 final  Technician technician;
 final  String status;
 final  String? notes;
 final  bool isActive;

/// Create a copy of TechnicianEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateTechnicianStatusCopyWith<_UpdateTechnicianStatus> get copyWith => __$UpdateTechnicianStatusCopyWithImpl<_UpdateTechnicianStatus>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateTechnicianStatus&&(identical(other.technician, technician) || other.technician == technician)&&(identical(other.status, status) || other.status == status)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}


@override
int get hashCode => Object.hash(runtimeType,technician,status,notes,isActive);

@override
String toString() {
  return 'TechnicianEvent.updateTechnicianStatus(technician: $technician, status: $status, notes: $notes, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class _$UpdateTechnicianStatusCopyWith<$Res> implements $TechnicianEventCopyWith<$Res> {
  factory _$UpdateTechnicianStatusCopyWith(_UpdateTechnicianStatus value, $Res Function(_UpdateTechnicianStatus) _then) = __$UpdateTechnicianStatusCopyWithImpl;
@useResult
$Res call({
 Technician technician, String status, String? notes, bool isActive
});


$TechnicianCopyWith<$Res> get technician;

}
/// @nodoc
class __$UpdateTechnicianStatusCopyWithImpl<$Res>
    implements _$UpdateTechnicianStatusCopyWith<$Res> {
  __$UpdateTechnicianStatusCopyWithImpl(this._self, this._then);

  final _UpdateTechnicianStatus _self;
  final $Res Function(_UpdateTechnicianStatus) _then;

/// Create a copy of TechnicianEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? technician = null,Object? status = null,Object? notes = freezed,Object? isActive = null,}) {
  return _then(_UpdateTechnicianStatus(
technician: null == technician ? _self.technician : technician // ignore: cast_nullable_to_non_nullable
as Technician,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of TechnicianEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TechnicianCopyWith<$Res> get technician {
  
  return $TechnicianCopyWith<$Res>(_self.technician, (value) {
    return _then(_self.copyWith(technician: value));
  });
}
}

/// @nodoc


class _DeleteTechnician implements TechnicianEvent {
  const _DeleteTechnician({required this.id});
  

 final  String id;

/// Create a copy of TechnicianEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DeleteTechnicianCopyWith<_DeleteTechnician> get copyWith => __$DeleteTechnicianCopyWithImpl<_DeleteTechnician>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DeleteTechnician&&(identical(other.id, id) || other.id == id));
}


@override
int get hashCode => Object.hash(runtimeType,id);

@override
String toString() {
  return 'TechnicianEvent.deleteTechnician(id: $id)';
}


}

/// @nodoc
abstract mixin class _$DeleteTechnicianCopyWith<$Res> implements $TechnicianEventCopyWith<$Res> {
  factory _$DeleteTechnicianCopyWith(_DeleteTechnician value, $Res Function(_DeleteTechnician) _then) = __$DeleteTechnicianCopyWithImpl;
@useResult
$Res call({
 String id
});




}
/// @nodoc
class __$DeleteTechnicianCopyWithImpl<$Res>
    implements _$DeleteTechnicianCopyWith<$Res> {
  __$DeleteTechnicianCopyWithImpl(this._self, this._then);

  final _DeleteTechnician _self;
  final $Res Function(_DeleteTechnician) _then;

/// Create a copy of TechnicianEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,}) {
  return _then(_DeleteTechnician(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _ApplyFilters implements TechnicianEvent {
  const _ApplyFilters({required final  Map<String, dynamic> filters}): _filters = filters;
  

 final  Map<String, dynamic> _filters;
 Map<String, dynamic> get filters {
  if (_filters is EqualUnmodifiableMapView) return _filters;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_filters);
}


/// Create a copy of TechnicianEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ApplyFiltersCopyWith<_ApplyFilters> get copyWith => __$ApplyFiltersCopyWithImpl<_ApplyFilters>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ApplyFilters&&const DeepCollectionEquality().equals(other._filters, _filters));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_filters));

@override
String toString() {
  return 'TechnicianEvent.applyFilters(filters: $filters)';
}


}

/// @nodoc
abstract mixin class _$ApplyFiltersCopyWith<$Res> implements $TechnicianEventCopyWith<$Res> {
  factory _$ApplyFiltersCopyWith(_ApplyFilters value, $Res Function(_ApplyFilters) _then) = __$ApplyFiltersCopyWithImpl;
@useResult
$Res call({
 Map<String, dynamic> filters
});




}
/// @nodoc
class __$ApplyFiltersCopyWithImpl<$Res>
    implements _$ApplyFiltersCopyWith<$Res> {
  __$ApplyFiltersCopyWithImpl(this._self, this._then);

  final _ApplyFilters _self;
  final $Res Function(_ApplyFilters) _then;

/// Create a copy of TechnicianEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? filters = null,}) {
  return _then(_ApplyFilters(
filters: null == filters ? _self._filters : filters // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

// dart format on
