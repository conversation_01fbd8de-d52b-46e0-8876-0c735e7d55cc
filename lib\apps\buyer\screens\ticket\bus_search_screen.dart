import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../models/ticket_booking_models.dart';
import '../../providers/ticket_booking_provider.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import 'bus_seat_selection_screen.dart';

class BusSearchScreen extends ConsumerStatefulWidget {
  final BusSearchRequest searchRequest;

  const BusSearchScreen({super.key, required this.searchRequest});

  @override
  ConsumerState<BusSearchScreen> createState() => _BusSearchScreenState();
}

class _BusSearchScreenState extends ConsumerState<BusSearchScreen> {
  String _sortBy = 'price'; // price, departure, duration

  @override
  void initState() {
    super.initState();
    // Trigger search when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(busSearchProvider.notifier).searchBuses(widget.searchRequest);
    });
  }

  @override
  Widget build(BuildContext context) {
    final searchState = ref.watch(busSearchProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '${widget.searchRequest.origin} → ${widget.searchRequest.destination}',
        ),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: () => _showSortDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search summary card
          _buildSearchSummary(),

          // Search results
          Expanded(
            child: searchState.when(
              data: (response) {
                if (response == null) {
                  return const Center(child: Text('No search performed yet'));
                }

                if (response.buses.isEmpty) {
                  return _buildNoResultsView();
                }

                return _buildBusesList(response.buses);
              },
              loading: () => const LoadingIndicator(),
              error: (error, stack) => ErrorView(
                message: 'Failed to search buses: $error',
                onRetry: () => ref
                    .read(busSearchProvider.notifier)
                    .searchBuses(widget.searchRequest),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSummary() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.directions_bus, color: Colors.green.shade700),
              const SizedBox(width: 8),
              Text(
                '${widget.searchRequest.origin} → ${widget.searchRequest.destination}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16,
                color: Colors.green.shade600,
              ),
              const SizedBox(width: 4),
              Text(
                _formatDate(widget.searchRequest.journeyDate),
                style: TextStyle(color: Colors.green.shade600),
              ),
              const Spacer(),
              Text(
                '${widget.searchRequest.passengers} Passenger${widget.searchRequest.passengers > 1 ? 's' : ''}',
                style: TextStyle(color: Colors.green.shade600),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBusesList(List<Bus> buses) {
    final sortedBuses = _sortBuses(buses);

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: sortedBuses.length,
      itemBuilder: (context, index) {
        final bus = sortedBuses[index];
        return _buildBusCard(bus);
      },
    );
  }

  Widget _buildBusCard(Bus bus) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _selectBus(bus),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Operator and bus type
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      bus.operator,
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    bus.busType,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.grey,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${bus.currency} ${bus.price.toStringAsFixed(0)}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Journey times and route
              Row(
                children: [
                  // Departure
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _formatTime(bus.departureTime),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        bus.origin,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(width: 16),

                  // Journey path
                  Expanded(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                              ),
                            ),
                            Expanded(
                              child: Container(height: 2, color: Colors.green),
                            ),
                            const Icon(
                              Icons.directions_bus,
                              color: Colors.green,
                              size: 16,
                            ),
                            Expanded(
                              child: Container(height: 2, color: Colors.green),
                            ),
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          bus.duration,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Arrival
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        _formatTime(bus.arrivalTime),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        bus.destination,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Additional info
              Row(
                children: [
                  Icon(Icons.event_seat, size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    '${bus.availableSeats} seats available',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                ],
              ),

              if (bus.amenities.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: bus.amenities
                      .take(3)
                      .map(
                        (amenity) => Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.green.shade50,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: Colors.green.shade200),
                          ),
                          child: Text(
                            amenity,
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.green.shade700,
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNoResultsView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.directions_bus, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'No buses found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try searching for a different date',
            style: TextStyle(color: Colors.grey.shade500),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Modify Search'),
          ),
        ],
      ),
    );
  }

  void _selectBus(Bus bus) {
    // Navigate to seat selection or booking screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bus Selected'),
        content: Text(
          'Selected ${bus.operator} bus for ${bus.currency} ${bus.price}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to seat selection screen
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => BusSeatSelectionScreen(
                    bus: bus,
                    searchRequest: widget.searchRequest,
                  ),
                ),
              );
            },
            child: const Text('Select Seats'),
          ),
        ],
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Buses'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Price: Low to High'),
              trailing: _sortBy == 'price'
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                Navigator.of(context).pop();
                _setSortBy('price');
              },
            ),
            ListTile(
              title: const Text('Departure Time'),
              trailing: _sortBy == 'departure'
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                Navigator.of(context).pop();
                _setSortBy('departure');
              },
            ),
            ListTile(
              title: const Text('Duration'),
              trailing: _sortBy == 'duration'
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () {
                Navigator.of(context).pop();
                _setSortBy('duration');
              },
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  void _setSortBy(String sortBy) {
    setState(() {
      _sortBy = sortBy;
    });
  }

  List<Bus> _sortBuses(List<Bus> buses) {
    final sortedBuses = List<Bus>.from(buses);

    switch (_sortBy) {
      case 'price':
        sortedBuses.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'departure':
        sortedBuses.sort((a, b) => a.departureTime.compareTo(b.departureTime));
        break;
      case 'duration':
        sortedBuses.sort(
          (a, b) =>
              _parseDuration(a.duration).compareTo(_parseDuration(b.duration)),
        );
        break;
    }

    return sortedBuses;
  }

  Duration _parseDuration(String duration) {
    // Parse duration string like "5h 30m" to Duration
    final parts = duration.split(' ');
    int hours = 0;
    int minutes = 0;

    for (final part in parts) {
      if (part.endsWith('h')) {
        hours = int.tryParse(part.replaceAll('h', '')) ?? 0;
      } else if (part.endsWith('m')) {
        minutes = int.tryParse(part.replaceAll('m', '')) ?? 0;
      }
    }

    return Duration(hours: hours, minutes: minutes);
  }
}
