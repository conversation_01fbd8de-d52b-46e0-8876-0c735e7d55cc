import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/domain/models/document_model.dart';
import 'package:shivish/apps/seller/domain/repositories/document_repository.dart';

final documentRepositoryProvider = Provider<DocumentRepository>((ref) {
  throw UnimplementedError('DocumentRepository not implemented');
});

final documentsProvider = StreamProvider<List<DocumentModel>>((ref) {
  final repository = ref.watch(documentRepositoryProvider);
  return repository.getDocuments();
});

final documentProvider =
    StreamProvider.family<DocumentModel, String>((ref, id) {
  final repository = ref.watch(documentRepositoryProvider);
  return repository.getDocument(id);
});

final documentUploadProvider =
    StateNotifierProvider<DocumentUploadNotifier, AsyncValue<void>>((ref) {
  final repository = ref.watch(documentRepositoryProvider);
  return DocumentUploadNotifier(repository);
});

final digitalCertificateProvider =
    StreamProvider.family<DocumentModel?, String>((ref, sellerId) {
  final repository = ref.watch(documentRepositoryProvider);
  return repository.getDocuments().map((documents) {
    try {
      return documents.firstWhere(
        (doc) =>
            doc.type == DocumentType.businessLicense &&
            doc.status == DocumentStatus.approved,
      );
    } catch (e) {
      return null;
    }
  });
});

class DocumentUploadNotifier extends StateNotifier<AsyncValue<void>> {
  final DocumentRepository _repository;

  DocumentUploadNotifier(this._repository) : super(const AsyncValue.data(null));

  Future<void> uploadDocument({
    required String sellerId,
    required File file,
    required DocumentType type,
    DateTime? expiryDate,
  }) async {
    state = const AsyncValue.loading();
    try {
      final fileName = file.path.split('/').last;

      await _repository.uploadDocument(
        type: type,
        filePath: file.path,
        fileName: fileName,
      );
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }


}
