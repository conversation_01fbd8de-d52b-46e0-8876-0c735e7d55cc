import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/technician/technician_routes.dart';
import '../widgets/technician_bottom_nav_bar.dart';
import '../widgets/technician_drawer.dart';

class TechnicianShell extends StatelessWidget {
  final Widget child;

  const TechnicianShell({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      drawer: const TechnicianDrawer(),
      bottomNavigationBar: TechnicianBottomNavBar(
        currentIndex: _calculateSelectedIndex(context),
      ),
    );
  }

  int _calculateSelectedIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;

    if (location.startsWith(TechnicianRoutes.home)) {
      return 0;
    }
    if (location.startsWith(TechnicianRoutes.services)) {
      return 1;
    }
    if (location.startsWith(TechnicianRoutes.bookings)) {
      return 2;
    }
    if (location.startsWith(TechnicianRoutes.payments)) {
      return 3;
    }
    if (location.startsWith(TechnicianRoutes.profileSettings)) {
      return 4;
    }
    return 0;
  }
}
