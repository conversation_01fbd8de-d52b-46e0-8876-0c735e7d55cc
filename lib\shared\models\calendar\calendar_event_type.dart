/// Enum for calendar event types
enum CalendarEventType {
  /// User created event
  user,

  /// Public event
  public,

  /// Product list event
  productList,

  /// Birthday event
  birthday,

  /// Anniversary event
  anniversary,

  /// Other event
  other,
}

/// Extension on [CalendarEventType] for additional functionality
extension CalendarEventTypeX on CalendarEventType {
  /// Get the display name for the event type
  String get displayName {
    switch (this) {
      case CalendarEventType.productList:
        return 'Product List';
      case CalendarEventType.user:
        return 'My Event';
      case CalendarEventType.public:
        return 'Public Event';
      case CalendarEventType.birthday:
        return 'Birthday';
      case CalendarEventType.anniversary:
        return 'Anniversary';
      case CalendarEventType.other:
        return 'Other';
    }
  }

  /// Get the description for the event type
  String get description {
    switch (this) {
      case CalendarEventType.productList:
        return 'A list of products to purchase';
      case CalendarEventType.user:
        return 'A personal event';
      case CalendarEventType.public:
        return 'A public event visible to all users';
      case CalendarEventType.birthday:
        return 'A special day for a birthday';
      case CalendarEventType.anniversary:
        return 'A special day for an anniversary';
      case CalendarEventType.other:
        return 'An event that doesn\'t fit into other categories';
    }
  }

  /// Get the icon for the event type
  String get icon {
    switch (this) {
      case CalendarEventType.productList:
        return 'shopping_cart';
      case CalendarEventType.user:
        return 'person';
      case CalendarEventType.public:
        return 'public';
      case CalendarEventType.birthday:
        return 'birthday';
      case CalendarEventType.anniversary:
        return 'anniversary';
      case CalendarEventType.other:
        return 'other';
    }
  }
}
