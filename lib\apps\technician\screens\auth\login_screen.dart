import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/screens/auth/enhanced_login_screen.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  bool _isLoading = false;
  String? _errorMessage;

  Future<void> _handleLogin(String email, String password) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = ref.read(authServiceProvider);
      await authService.signInWithEmail(
        email: email,
        password: password,
      );

      if (mounted) {
        debugPrint('LoginScreen: Login successful, navigating to home screen');
        context.go('/technician/home');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _onRegisterPressed() {
    debugPrint(
        'LoginScreen: Register button pressed, navigating to register screen');
    context.go('/technician/register');
  }

  @override
  Widget build(BuildContext context) {
    // Force technician flavor regardless of environment detection
    const flavorName = 'technician';
    debugPrint('Technician login screen using flavor: $flavorName');

    // Use technician-specific colors
    final primaryColor = const Color(0xFF2E7D32); // Green
    final secondaryColor = const Color(0xFFAED581); // Light Green

    // Print debug information to help diagnose the issue
    debugPrint('Building Technician login screen with:');
    debugPrint('- Flavor: $flavorName');
    debugPrint('- App name: Shivish Technician');
    debugPrint('- Primary color: $primaryColor');
    debugPrint('- Secondary color: $secondaryColor');

    return EnhancedLoginScreen(
      flavor: flavorName, // Explicitly set to technician
      appName: 'Shivish Technician',
      primaryColor: primaryColor,
      secondaryColor: secondaryColor,
      onLoginPressed: _handleLogin,
      onRegisterPressed: _onRegisterPressed,
      isLoading: _isLoading,
      errorMessage: _errorMessage,
      onForgotPasswordPressed: () {
        debugPrint(
            'LoginScreen: Forgot password button pressed, navigating to forgot password screen');
        context.go('/technician/forgot-password');
      },
    );
  }
}
