import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';

/// Factory for creating cache interceptor with default options.
class ApiCacheInterceptorFactory {
  /// Creates a cache interceptor with default options.
  static DioCacheInterceptor create({
    required CacheStore cacheStore,
    CacheOptions? defaultOptions,
  }) {
    final options = defaultOptions ??
        CacheOptions(
          store: cacheStore,
          policy: CachePolicy.request,
          maxStale: const Duration(days: 7),
          priority: CachePriority.normal,
        );

    return DioCacheInterceptor(options: options);
  }
}
