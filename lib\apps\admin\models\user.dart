import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/user/user_model.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
sealed class User with _$User {
  const factory User({
    required String id,
    required String name,
    required String email,
    required String role,
    @Default(true) bool isActive,
    @Default(false) bool isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  factory User.fromUserModel(UserModel model) => User(
    id: model.id,
    name: _getUserDisplayName(model),
    email: model.email,
    role: model.role.name,
    isActive: model.status == UserStatus.active,
    isVerified: model.verificationStatus == VerificationStatus.verified,
    createdAt: model.createdAt,
    updatedAt: model.updatedAt,
  );

  static String _getUserDisplayName(UserModel model) {
    final displayName = model.displayName;
    if (displayName.isEmpty) {
      return model.email;
    }
    return displayName;
  }
}

extension UserX on User {
  /// Get profile image URL (placeholder for now)
  String? get profileImage =>
      null; // Will be implemented when User model has photoUrl

  UserModel toUserModel() {
    return UserModel(
      id: id,
      email: email,
      displayName: name,
      firstName: name.split(' ').first,
      lastName: name.split(' ').length > 1 ? name.split(' ').last : '',
      role: UserRole.values.firstWhere(
        (role) => role.name.toLowerCase() == this.role.toLowerCase(),
        orElse: () => UserRole.buyer,
      ),
      status: isActive ? UserStatus.active : UserStatus.inactive,
      verificationStatus: isVerified
          ? VerificationStatus.verified
          : VerificationStatus.unverified,
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
}

extension AdminUserX on UserModel {
  String get fullName => '$firstName $lastName'.trim();

  String? get profileImage => photoUrl;

  bool get isActive => status == UserStatus.active;

  bool get isVerified => verificationStatus == VerificationStatus.verified;

  bool get isPending => verificationStatus == VerificationStatus.pending;

  bool get isRejected => verificationStatus == VerificationStatus.rejected;

  bool get isInactive => status == UserStatus.inactive;

  bool get isBlocked => status == UserStatus.blocked;

  bool get isDeleted => status == UserStatus.deleted;

  String get roleName => role.name;

  String get statusName => status.name;

  String get verificationStatusName => verificationStatus.name;

  String? get rejectionReasonText => rejectionReason ?? suspensionReason;

  String? get verifiedByText => approvedBy ?? rejectedBy ?? suspendedBy;

  DateTime? get verifiedAtText => approvedAt ?? rejectedAt ?? suspendedAt;
}
