import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final serviceAreasProvider =
    AsyncNotifierProvider<ServiceAreasNotifier, List<List<LatLng>>>(() {
  return ServiceAreasNotifier();
});

class ServiceAreasNotifier extends AsyncNotifier<List<List<LatLng>>> {
  late final DatabaseService _databaseService;

  @override
  Future<List<List<LatLng>>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadServiceAreas();
  }

  Future<List<List<LatLng>>> _loadServiceAreas() async {
    final userId = ref.read(authProvider)?.id;
    if (userId == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('technicians', userId);
      final areas = userData?['service_areas'] as List<dynamic>?;
      if (areas == null) return [];

      return areas.map((area) {
        final points = (area as List<dynamic>).map((point) {
          return LatLng(
            point['latitude'] as double,
            point['longitude'] as double,
          );
        }).toList();
        return points;
      }).toList();
    } catch (e) {
      debugPrint('Failed to load service areas: $e');
      throw Exception('Failed to load service areas: $e');
    }
  }

  Future<void> saveServiceArea(List<LatLng> points) async {
    state = const AsyncLoading();

    try {
      final userId = ref.read(authProvider)?.id;
      if (userId == null) throw Exception('User not authenticated');

      if (points.isEmpty) {
        throw Exception('Service area must have at least one point');
      }

      final currentAreas = await _loadServiceAreas();
      final updatedAreas = [...currentAreas, points];

      await _databaseService.update('technicians', userId, {
        'service_areas': updatedAreas.map((area) {
          return area.map((point) {
            return {
              'latitude': point.latitude,
              'longitude': point.longitude,
            };
          }).toList();
        }).toList(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData(updatedAreas);
    } catch (e) {
      debugPrint('Failed to save service area: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}
