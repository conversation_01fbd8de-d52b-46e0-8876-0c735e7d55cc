import 'package:flutter/material.dart';
import '../../../../../shared/models/inventory_model.dart';

class LowStockAlert extends StatelessWidget {
  final List<InventoryModel> items;
  final VoidCallback onDismiss;

  const LowStockAlert({
    super.key,
    required this.items,
    required this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      color: Colors.orange.shade50,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(Icons.warning_amber, color: Colors.orange.shade700),
            title: Text(
              'Low Stock Alert',
              style: TextStyle(
                color: Colors.orange.shade900,
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(
              '${items.length} item${items.length == 1 ? '' : 's'} running low on stock',
            ),
            trailing: IconButton(
              icon: const Icon(Icons.close),
              onPressed: onDismiss,
            ),
          ),
          if (items.isNotEmpty)
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                return ListTile(
                  title: Text('Product ID: ${item.productId}'),
                  subtitle: Text(
                    'Current Stock: ${item.currentStock} / Minimum: ${item.minimumStock}',
                  ),
                );
              },
            ),
        ],
      ),
    );
  }
}
