import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../shared/services/language_service.dart';

/// A dialog that presents the legal agreement for technicians
class TechnicianLegalAgreementDialog extends StatefulWidget {
  /// Creates a [TechnicianLegalAgreementDialog]
  const TechnicianLegalAgreementDialog({super.key});

  @override
  State<TechnicianLegalAgreementDialog> createState() =>
      _TechnicianLegalAgreementDialogState();

  /// Shows the legal agreement dialog and returns whether the technician accepted
  static Future<bool?> show(BuildContext context) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false, // User must respond to the dialog
      builder: (context) => const TechnicianLegalAgreementDialog(),
    );
  }
}

class _TechnicianLegalAgreementDialogState
    extends State<TechnicianLegalAgreementDialog> {
  bool _hasReadTerms = false;
  String _selectedLanguage = 'en';

  @override
  void initState() {
    super.initState();
    _loadSelectedLanguage();
  }

  Future<void> _loadSelectedLanguage() async {
    final languageService =
        LanguageService(await SharedPreferences.getInstance());
    final language = languageService.getSelectedLanguage();
    if (mounted) {
      setState(() {
        _selectedLanguage = language;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      title: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.build,
            size: 40,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 8),
          Column(
            children: [
              // Display title in selected language
              Text(
                _getLocalizedTitle(),
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
                textAlign: TextAlign.center,
              ),
              // Display English title if not already in English
              if (_selectedLanguage != 'en')
                Text(
                  // Temporarily switch to English to get the title
                  (() {
                    final savedLanguage = _selectedLanguage;
                    _selectedLanguage = 'en';
                    final englishTitle = _getLocalizedTitle();
                    _selectedLanguage = savedLanguage;
                    return englishTitle;
                  })(),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                ),
            ],
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Display intro text in selected language
                Text(
                  _getLocalizedIntroText(),
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                // Display English intro text if not already in English
                if (_selectedLanguage != 'en')
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      // Temporarily switch to English to get the intro text
                      (() {
                        final savedLanguage = _selectedLanguage;
                        _selectedLanguage = 'en';
                        final englishIntroText = _getLocalizedIntroText();
                        _selectedLanguage = savedLanguage;
                        return englishIntroText;
                      })(),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[700],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSection(
              title: '1. TECHNICAL QUALIFICATIONS AND VERIFICATION',
              content:
                  'Technician warrants and represents that they possess the necessary qualifications, skills, training, certifications, and licenses required to perform the technical services offered through the SHIVISH platform. Technician agrees to provide accurate information regarding their identity, qualifications, experience, and certifications, and consents to verification checks conducted by SHIVISH. Technician acknowledges that misrepresentation of qualifications, certifications, or identity may result in immediate termination of their account and potential legal action.',
            ),
            _buildSection(
              title: '2. QUALITY OF WORKMANSHIP',
              content:
                  'Technician commits to performing all technical services with the highest standards of workmanship, in accordance with industry best practices, applicable codes, and safety standards. Technician shall use appropriate tools, equipment, and materials for each job and shall ensure that all work is performed in a manner that ensures durability, reliability, and safety. Technician acknowledges that substandard workmanship may result in liability for any resulting damages or losses, as well as penalties including account suspension or termination.',
            ),
            _buildSection(
              title: '3. SAFETY PROTOCOLS',
              content:
                  'Technician agrees to adhere to all applicable safety protocols, guidelines, and regulations while performing services. This includes but is not limited to: (a) using appropriate personal protective equipment; (b) following manufacturer specifications and guidelines; (c) implementing proper safety measures to protect clients, their property, and others who may be present; (d) properly handling, storing, and disposing of hazardous materials; and (e) maintaining all tools and equipment in safe working condition. Technician acknowledges that failure to follow safety protocols may result in immediate suspension or termination from the platform.',
            ),
            _buildSection(
              title: '4. SERVICE DESCRIPTION AND TRANSPARENCY',
              content:
                  'Technician agrees to provide clear, accurate, and detailed descriptions of all technical services offered through the SHIVISH platform, including the scope, limitations, timeframes, and deliverables. Technician shall not make false or misleading claims about their services, qualifications, or the results clients can expect. Any limitations or potential risks associated with services shall be clearly disclosed to clients before engagement. Technician acknowledges that misrepresentation of services may result in penalties, including account suspension and legal liability.',
            ),
            _buildSection(
              title: '5. DIAGNOSIS AND RECOMMENDATIONS',
              content:
                  'Technician agrees to provide honest and accurate diagnoses of technical issues, and to recommend only necessary repairs or services. Technician shall not deliberately misdiagnose issues or recommend unnecessary services for the purpose of increasing fees. When multiple solutions are available, Technician shall present all viable options to the client, along with the advantages, disadvantages, and costs of each, allowing the client to make an informed decision. Technician acknowledges that fraudulent diagnoses or recommendations may result in immediate termination from the platform and potential legal action.',
            ),
            _buildSection(
              title: '6. PARTS AND MATERIALS',
              content:
                  'When Technician is responsible for providing parts or materials, Technician agrees to use only genuine, high-quality parts and materials that meet or exceed manufacturer specifications and industry standards. Technician shall not use counterfeit, substandard, or inappropriate parts or materials. Technician shall clearly communicate to clients whether parts are new, refurbished, or aftermarket, and shall obtain client approval before using any parts that differ from what was initially agreed upon. Technician shall provide warranties for parts and materials as applicable.',
            ),
            _buildSection(
              title: '7. TIMELY COMPLETION AND COMMUNICATION',
              content:
                  'Technician agrees to complete all accepted jobs within the timeframes agreed upon with clients or as reasonably expected for the type of service provided. Technician shall promptly communicate any anticipated delays or obstacles to clients and SHIVISH. If additional time is required due to unforeseen complications, Technician shall explain the reasons to the client and provide a revised completion estimate. Technician acknowledges that repeated failure to meet deadlines or communicate delays may result in penalties, including negative ratings, account suspension, or termination.',
            ),
            _buildSection(
              title: '8. PRICING AND ESTIMATES',
              content:
                  'Technician agrees to provide clear, detailed, and accurate cost estimates before beginning work. If during the course of work, Technician discovers that additional services or parts are required that will increase the total cost, Technician shall immediately inform the client and obtain approval before proceeding with the additional work. Technician shall not engage in "bait and switch" pricing tactics or add undisclosed fees. Technician acknowledges that deceptive pricing practices may result in immediate suspension or termination from the platform.',
            ),
            _buildSection(
              title: '9. WARRANTIES AND GUARANTEES',
              content:
                  'Technician agrees to provide clear information about any warranties or guarantees offered for their services or for parts installed. Technician shall honor all warranties and guarantees as stated and shall promptly address any issues covered under warranty without additional charges. The terms, duration, and limitations of warranties shall be clearly communicated to clients in writing. Technician acknowledges that failure to honor warranties may result in penalties, including account suspension, negative ratings, or termination.',
            ),
            _buildSection(
              title: '10. CLIENT PROPERTY AND PREMISES',
              content:
                  'Technician agrees to treat clients\' property and premises with care and respect. Technician shall take reasonable precautions to prevent damage to property while performing services, including but not limited to using protective coverings, removing debris, and cleaning the work area upon completion. Technician shall immediately report any damage caused during service to both the client and SHIVISH. Technician acknowledges that they may be held liable for damages caused by negligence or improper work practices.',
            ),
            _buildSection(
              title: '11. CONFIDENTIALITY',
              content:
                  'Technician acknowledges that they may become privy to personal, sensitive, or private information about clients, their homes, businesses, or equipment in the course of providing services. Technician agrees to maintain strict confidentiality regarding all such information and shall not disclose it to any third party without explicit consent, except as required by law. This obligation of confidentiality shall survive the termination of this Agreement and Technician\'s relationship with the SHIVISH platform.',
            ),
            _buildSection(
              title: '12. DISPUTE RESOLUTION',
              content:
                  'In the event of any dispute with a client regarding the performance of services or other aspects of the engagement, Technician agrees to make good faith efforts to resolve such disputes promptly and amicably. If the dispute cannot be resolved directly with the client, Technician agrees to participate in SHIVISH\'s dispute resolution process and to be bound by its outcome.',
            ),
            _buildSection(
              title: '13. INDEMNIFICATION',
              content:
                  'Technician agrees to indemnify, defend, and hold harmless SHIVISH, its affiliates, officers, directors, employees, and agents from and against any and all claims, liabilities, damages, losses, costs, expenses, or fees (including reasonable attorneys\' fees) arising from or relating to: (a) Technician\'s violation of this Agreement; (b) Technician\'s failure to comply with applicable laws, regulations, codes, or standards; (c) the services provided by Technician; (d) Technician\'s misrepresentation of services or qualifications; or (e) Technician\'s violation of any rights of a third party.',
            ),
            _buildSection(
              title: '14. GOVERNING LAW',
              content:
                  'This Agreement shall be governed by and construed in accordance with the laws of India, without regard to its conflict of law provisions. Any disputes arising under or in connection with this Agreement shall be subject to the exclusive jurisdiction of the courts located within the jurisdiction of the registered office of SHIVISH.',
            ),
            const SizedBox(height: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Display footer text in selected language
                Text(
                  _getLocalizedFooterText(),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
                ),
                // Display English footer text if not already in English
                if (_selectedLanguage != 'en')
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      // Temporarily switch to English to get the footer text
                      (() {
                        final savedLanguage = _selectedLanguage;
                        _selectedLanguage = 'en';
                        final englishFooterText = _getLocalizedFooterText();
                        _selectedLanguage = savedLanguage;
                        return englishFooterText;
                      })(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontStyle: FontStyle.italic,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: _hasReadTerms,
                  onChanged: (value) {
                    setState(() {
                      _hasReadTerms = value ?? false;
                    });
                  },
                  activeColor: theme.colorScheme.primary,
                ),
                Expanded(
                  child: Text(
                    _getLocalizedCheckboxText(),
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(false);
            // Exit the technician section if they reject
            Navigator.of(context).pop();
          },
          style: TextButton.styleFrom(
            foregroundColor: Colors.red,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Display decline button text in selected language
              Text(_getLocalizedDeclineButton()),
              // Display English decline button text if not already in English
              if (_selectedLanguage != 'en')
                Text(
                  // Temporarily switch to English to get the button text
                  (() {
                    final savedLanguage = _selectedLanguage;
                    _selectedLanguage = 'en';
                    final englishButtonText = _getLocalizedDeclineButton();
                    _selectedLanguage = savedLanguage;
                    return englishButtonText;
                  })(),
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[700],
                  ),
                ),
            ],
          ),
        ),
        ElevatedButton(
          onPressed: _hasReadTerms
              ? () {
                  Navigator.of(context).pop(true);
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: Colors.white,
            disabledBackgroundColor: Colors.grey.shade300,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Display accept button text in selected language
              Text(_getLocalizedAcceptButton()),
              // Display English accept button text if not already in English
              if (_selectedLanguage != 'en')
                Text(
                  // Temporarily switch to English to get the button text
                  (() {
                    final savedLanguage = _selectedLanguage;
                    _selectedLanguage = 'en';
                    final englishButtonText = _getLocalizedAcceptButton();
                    _selectedLanguage = savedLanguage;
                    return englishButtonText;
                  })(),
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.white70,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSection({required String title, required String content}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          content,
          style: const TextStyle(fontSize: 13),
        ),
      ],
    );
  }

  String _getLocalizedTitle() {
    switch (_selectedLanguage) {
      case 'hi':
        return 'तकनीशियन कानूनी समझौता';
      case 'te':
        return 'టెక్నీషియన్ చట్టపరమైన ఒప్పందం';
      case 'ta':
        return 'தொழில்நுட்ப நிபுணர் சட்ட ஒப்பந்தம்';
      case 'kn':
        return 'ತಂತ್ರಜ್ಞ ಕಾನೂನು ಒಪ್ಪಂದ';
      case 'ml':
        return 'ടെക്നീഷ്യൻ നിയമപരമായ കരാർ';
      case 'bn':
        return 'প্রযুক্তিবিদ আইনি চুক্তি';
      default:
        return 'Technician Legal Agreement';
    }
  }

  String _getLocalizedIntroText() {
    switch (_selectedLanguage) {
      case 'hi':
        return 'SHIVISH प्लेटफॉर्म का तकनीशियन के रूप में उपयोग करके, आप निम्नलिखित कानूनी रूप से बाध्यकारी नियमों और शर्तों से सहमत होते हैं:';
      case 'te':
        return 'టెక్నీషియన్‌గా SHIVISH ప్లాట్‌ఫార్మ్‌ను ఉపయోగించడం ద్వారా, మీరు కింది చట్టబద్ధమైన నియమాలు మరియు షరతులకు అంగీకరిస్తున్నారు:';
      case 'ta':
        return 'தொழில்நுட்ப நிபுணராக SHIVISH தளத்தைப் பயன்படுத்துவதன் மூலம், பின்வரும் சட்டப்பூர்வமான விதிமுறைகளுக்கும் நிபந்தனைகளுக்கும் நீங்கள் உடன்படுகிறீர்கள்:';
      case 'kn':
        return 'ತಂತ್ರಜ್ಞರಾಗಿ SHIVISH ಪ್ಲಾಟ್‌ಫಾರ್ಮ್ ಬಳಸುವುದರ ಮೂಲಕ, ನೀವು ಈ ಕೆಳಗಿನ ಕಾನೂನುಬದ್ಧ ನಿಯಮಗಳು ಮತ್ತು ಷರತ್ತುಗಳಿಗೆ ಒಪ್ಪಿಕೊಳ್ಳುತ್ತೀರಿ:';
      default:
        return 'By using the SHIVISH platform as a technician, you agree to the following legally binding terms and conditions:';
    }
  }

  String _getLocalizedFooterText() {
    switch (_selectedLanguage) {
      case 'hi':
        return 'By clicking "I Accept" below, you acknowledge that you have read, understood, and agree to be bound by all the terms and conditions of this Agreement. This Agreement constitutes a legally binding contract between you and SHIVISH.';
      case 'te':
        return 'By clicking "I Accept" below, you acknowledge that you have read, understood, and agree to be bound by all the terms and conditions of this Agreement. This Agreement constitutes a legally binding contract between you and SHIVISH.';
      case 'ta':
        return 'By clicking "I Accept" below, you acknowledge that you have read, understood, and agree to be bound by all the terms and conditions of this Agreement. This Agreement constitutes a legally binding contract between you and SHIVISH.';
      case 'kn':
        return 'By clicking "I Accept" below, you acknowledge that you have read, understood, and agree to be bound by all the terms and conditions of this Agreement. This Agreement constitutes a legally binding contract between you and SHIVISH.';
      default:
        return 'By clicking "I Accept" below, you acknowledge that you have read, understood, and agree to be bound by all the terms and conditions of this Agreement. This Agreement constitutes a legally binding contract between you and SHIVISH.';
    }
  }

  String _getLocalizedDeclineButton() {
    switch (_selectedLanguage) {
      case 'hi':
        return 'Decline';
      case 'te':
        return 'Decline';
      case 'ta':
        return 'Decline';
      case 'kn':
        return 'Decline';
      default:
        return 'Decline';
    }
  }

  String _getLocalizedAcceptButton() {
    switch (_selectedLanguage) {
      case 'hi':
        return 'I Accept';
      case 'te':
        return 'I Accept';
      case 'ta':
        return 'I Accept';
      case 'kn':
        return 'I Accept';
      default:
        return 'I Accept';
    }
  }

  String _getLocalizedCheckboxText() {
    switch (_selectedLanguage) {
      case 'hi':
        return 'I have read, understood, and agree to be bound by all the terms and conditions of this Agreement.';
      case 'te':
        return 'I have read, understood, and agree to be bound by all the terms and conditions of this Agreement.';
      case 'ta':
        return 'I have read, understood, and agree to be bound by all the terms and conditions of this Agreement.';
      case 'kn':
        return 'I have read, understood, and agree to be bound by all the terms and conditions of this Agreement.';
      default:
        return 'I have read, understood, and agree to be bound by all the terms and conditions of this Agreement.';
    }
  }
}
