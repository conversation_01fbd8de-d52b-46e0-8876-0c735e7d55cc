import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/auth/auth_bloc.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';

class PriestRequestsScreen extends StatefulWidget {
  const PriestRequestsScreen({super.key});

  @override
  State<PriestRequestsScreen> createState() => _PriestRequestsScreenState();
}

class _PriestRequestsScreenState extends State<PriestRequestsScreen> {
  final _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
  final _logger = getLogger('PriestRequestsScreen');
  bool _isLoading = false;
  List<Map<String, dynamic>> _priestRequests = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPriestRequests();
  }

  Future<void> _loadPriestRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      _logger.info('Loading pending priest requests');
      // Query the priests collection for pending verification
      final requests = await _databaseService.getAll(
        'priests',
        where: 'verification_status = ?',
        whereParams: ['pending'],
        orderBy: 'created_at DESC',
      );

      setState(() {
        _priestRequests = requests;
        _isLoading = false;
      });

      _logger.info('Successfully loaded ${requests.length} pending priest requests');
    } catch (e) {
      _logger.severe('Error loading priest requests: $e');
      setState(() {
        _errorMessage = 'Error loading priest requests: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _approvePriest(String priestId) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get the current admin user ID
      String? adminId;
      if (mounted) {
        final authState = context.read<AuthBloc>().state;
        if (authState is AuthAuthenticatedState) {
          adminId = authState.user.id;
        }
      }

      // First check if the priest document exists
      final priestData = await _databaseService.find('priests', priestId);
      if (priestData == null) {
        throw Exception('Priest document not found');
      }

      // Get the priest data to find the associated user ID
      final userId = priestData['user_id'] ?? priestId; // Use the user_id field if it exists, otherwise use priestId
      final now = DateTime.now().toIso8601String();

      // Update priest document to approve
      await _databaseService.update('priests', priestId, {
        'verification_status': 'approved',
        'is_verified': true,
        'is_active': true,
        'is_approved': true,  // Add this field to match what the priest app checks for
        'approved_at': now,
        'approved_by': adminId,
        'updated_at': now,
      });

      // Check if the user document exists before updating
      final userData = await _databaseService.find('users', userId);
      if (userData != null) {
        await _databaseService.update('users', userId, {
          'is_approved': true,
          'role': 'priest', // Ensure the role is set correctly
          'updated_at': now,
        });
      } else {
        // Log that the user document wasn't found but continue without error
        _logger.warning('User document not found for priest $priestId with userId $userId');
      }

      // Reload the list
      await _loadPriestRequests();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Priest approved successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error approving priest: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _rejectPriest(String priestId) async {
    final reasonController = TextEditingController();

    if (!context.mounted) return;

    final reason = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Priest'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejection:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(reasonController.text),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (reason == null || reason.isEmpty) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get the current admin user ID
      String? adminId;
      if (mounted) {
        final authState = context.read<AuthBloc>().state;
        if (authState is AuthAuthenticatedState) {
          adminId = authState.user.id;
        }
      }

      // First check if the priest document exists
      final priestData = await _databaseService.find('priests', priestId);
      if (priestData == null) {
        throw Exception('Priest document not found');
      }

      // Get the priest data to find the associated user ID
      final userId = priestData['user_id'] ?? priestId; // Use the user_id field if it exists, otherwise use priestId
      final now = DateTime.now().toIso8601String();

      // Update priest document to reject
      await _databaseService.update('priests', priestId, {
        'verification_status': 'rejected',
        'is_verified': false,
        'is_active': false,
        'is_approved': false,  // Add this field to match what the priest app checks for
        'rejection_reason': reason,
        'rejected_at': now,
        'rejected_by': adminId,
        'updated_at': now,
      });

      // Check if the user document exists before updating
      final userData = await _databaseService.find('users', userId);
      if (userData != null) {
        await _databaseService.update('users', userId, {
          'is_approved': false,
          'rejection_reason': reason,
          'updated_at': now,
        });
      } else {
        // Log that the user document wasn't found but continue without error
        _logger.warning('User document not found for priest $priestId with userId $userId');
      }

      // Reload the list
      await _loadPriestRequests();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Priest rejected successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error rejecting priest: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Priest Requests'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPriestRequests,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadPriestRequests,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _priestRequests.isEmpty
                  ? const Center(
                      child: Text('No pending priest requests'),
                    )
                  : ListView.builder(
                      itemCount: _priestRequests.length,
                      itemBuilder: (context, index) {
                        final request = _priestRequests[index];
                        return Card(
                          margin: const EdgeInsets.all(8),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  request['name'] ?? 'Unknown',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text('Email: ${request['email'] ?? 'N/A'}'),
                                if (request['phone'] != null)
                                  Text('Phone: ${request['phone']}'),
                                const SizedBox(height: 8),
                                if (request['experienceYears'] != null)
                                  Text('Experience: ${request['experienceYears']} years'),
                                if (request['specializations'] != null && (request['specializations'] as List).isNotEmpty)
                                  Text('Specializations: ${(request['specializations'] as List).join(', ')}'),
                                if (request['serviceAreas'] != null && (request['serviceAreas'] as List).isNotEmpty)
                                  Text('Service Areas: ${(request['serviceAreas'] as List).join(', ')}'),
                                const SizedBox(height: 8),
                                Text(
                                  'Requested: ${_formatTimestamp(request['createdAt'])}',
                                  style: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                                if (request['profileImage'] != null)
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8.0),
                                      child: Image.network(
                                        request['profileImage'],
                                        height: 100,
                                        width: 100,
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, error, stackTrace) {
                                          return const SizedBox(
                                            height: 100,
                                            width: 100,
                                            child: Icon(Icons.person, size: 50),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                const SizedBox(height: 16),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton(
                                      onPressed: () => _rejectPriest(request['id']),
                                      child: const Text('Reject'),
                                    ),
                                    const SizedBox(width: 8),
                                    ElevatedButton(
                                      onPressed: () => _approvePriest(request['id']),
                                      child: const Text('Approve'),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
    );
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'N/A';

    if (timestamp is String) {
      try {
        final date = DateTime.parse(timestamp);
        return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
      } catch (e) {
        return 'Invalid date';
      }
    }

    if (timestamp is Map<String, dynamic>) {
      // Handle potential timestamp objects from database
      if (timestamp.containsKey('_seconds')) {
        final date = DateTime.fromMillisecondsSinceEpoch(
          (timestamp['_seconds'] as int) * 1000,
        );
        return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
      }
    }

    return 'N/A';
  }
}
