import 'package:flutter/material.dart';
import '../widgets/error_view.dart';

/// A custom error widget that can be used throughout the app
class ErrorWidget extends StatelessWidget {
  /// The error to display
  final dynamic error;

  /// Callback to retry the operation that failed
  final VoidCallback onRetry;

  /// Optional custom message to display
  final String? message;

  /// Creates an error widget
  const ErrorWidget({
    super.key,
    required this.error,
    required this.onRetry,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    // Extract error message
    final errorMessage = message ?? _getErrorMessage(error);

    return ErrorView(
      message: errorMessage,
      onRetry: onRetry,
    );
  }

  /// Extracts a user-friendly message from the error
  String _getErrorMessage(dynamic error) {
    if (error is String) {
      return error;
    } else if (error is Exception) {
      return error.toString();
    } else {
      return 'An unexpected error occurred';
    }
  }
}
