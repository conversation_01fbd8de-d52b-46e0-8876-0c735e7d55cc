import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shivish/apps/technician/providers/profile_settings_provider.dart';
import 'package:shivish/shared/providers/storage_provider.dart';
import 'package:shivish/apps/technician/widgets/technician_app_toolbar.dart';
import 'package:shivish/apps/technician/technician_routes.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';

class ProfileSettingsScreen extends ConsumerStatefulWidget {
  const ProfileSettingsScreen({super.key});

  @override
  ConsumerState<ProfileSettingsScreen> createState() =>
      _ProfileSettingsScreenState();
}

class _ProfileSettingsScreenState extends ConsumerState<ProfileSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  bool _isLoading = false;
  final _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();
    _loadProfileData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _loadProfileData() async {
    final profile = await ref.read(profileSettingsProvider.future);
    if (mounted && profile != null) {
      setState(() {
        _nameController.text = profile.name;
        _emailController.text = profile.email;
        _phoneController.text = profile.phone;
      });
    }
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final settings = {
        'name': _nameController.text,
        'email': _emailController.text,
        'phone': _phoneController.text,
      };

      await ref
          .read(profileSettingsProvider.notifier)
          .updateProfileSettings(settings);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating profile: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _updateProfilePicture() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 85,
      );

      if (image == null) return;

      setState(() => _isLoading = true);

      final storageService = ref.read(storageServiceProvider);
      final imageUrl = await storageService.uploadFile(
        filePath: image.path,
        storagePath: 'profileImages',
      );

      await ref
          .read(profileSettingsProvider.notifier)
          .updateProfileImage(imageUrl);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile picture updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating profile picture: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final profileState = ref.watch(profileSettingsProvider);

    return Scaffold(
      appBar: TechnicianAppToolbar.simple(
        title: 'Profile Settings',
        fallbackRoute: TechnicianRoutes.home,
      ),
      body: profileState.when(
        data: (profile) {
          if (profile == null) {
            return const Center(
              child: Text('No profile data available'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildProfileHeader(context, profile),
                  const SizedBox(height: 24),
                  _buildPersonalInfoSection(),
                  const SizedBox(height: 24),
                  _buildProfessionalInfoSection(profile),
                  const SizedBox(height: 24),
                  _buildServiceAreasSection(profile),
                  const SizedBox(height: 24),
                  _buildCertificationsSection(profile),
                  const SizedBox(height: 32),
                  _buildUpdateButton(),
                ],
              ),
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context, dynamic profile) {
    return Column(
      children: [
        Stack(
          alignment: Alignment.bottomRight,
          children: [
            CircleAvatar(
              radius: 50,
              backgroundImage: profile.profileImage != null
                  ? NetworkImage(profile.profileImage!)
                  : null,
              backgroundColor: Colors.blue.shade100,
              child: profile.profileImage == null
                  ? const Icon(Icons.person, size: 50)
                  : null,
            ),
            Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.camera_alt, color: Colors.white),
                onPressed: _isLoading ? null : _updateProfilePicture,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          profile.name,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        Text(
          profile.email,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildPersonalInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Personal Information',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        AppTextField(
          controller: _nameController,
          label: 'Full Name',
          prefixIcon: const Icon(Icons.person),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your name';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        AppTextField(
          controller: _emailController,
          label: 'Email',
          prefixIcon: const Icon(Icons.email),
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your email';
            }
            if (!value.contains('@')) {
              return 'Please enter a valid email';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        AppTextField(
          controller: _phoneController,
          label: 'Phone Number',
          prefixIcon: const Icon(Icons.phone),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your phone number';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildProfessionalInfoSection(dynamic profile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Professional Information',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        ListTile(
          leading: const Icon(Icons.work),
          title: const Text('Experience'),
          subtitle: Text('${profile.experience} years'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            context.go('/technician/settings/experience');
          },
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.school),
          title: const Text('Qualifications'),
          subtitle: Text('${profile.qualifications.length} certificates'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            context.go('/technician/settings/qualifications');
          },
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.category),
          title: const Text('Specializations'),
          subtitle: Text('${profile.specializations.length} areas'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            context.go('/technician/settings/specializations');
          },
        ),
      ],
    );
  }

  Widget _buildServiceAreasSection(dynamic profile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service Areas',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        ListTile(
          leading: const Icon(Icons.location_on),
          title: const Text('Service Locations'),
          subtitle: Text('${profile.serviceAreas.length} areas'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            context.go(TechnicianRoutes.serviceAreas);
          },
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.access_time),
          title: const Text('Working Hours'),
          subtitle: const Text('Set your availability schedule'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            context.go(TechnicianRoutes.workingHours);
          },
        ),
      ],
    );
  }

  Widget _buildCertificationsSection(dynamic profile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Certifications',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: profile.certifications.length,
          itemBuilder: (context, index) {
            final certification = profile.certifications[index];
            return ListTile(
              leading: const Icon(Icons.verified),
              title: Text(certification.name),
              subtitle: Text('Expires: ${certification.expiryDate}'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // Use context.go with parameters
                context.go('/technician/settings/certification-details/${certification.id}');
              },
            );
          },
        ),
        const SizedBox(height: 16),
        AppButton(
          onPressed: () {
            context.go('/technician/settings/add-certification');
          },
          child: const Text('Add Certification'),
        ),
      ],
    );
  }

  Widget _buildUpdateButton() {
    return SizedBox(
      width: double.infinity,
      child: AppButton(
        onPressed: _isLoading ? null : _updateProfile,
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text('Update Profile'),
      ),
    );
  }
}
