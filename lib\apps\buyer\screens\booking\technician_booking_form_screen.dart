import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../../../shared/models/booking/booking_model.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/booking_provider.dart';
import '../../../../shared/providers/technician_provider.dart';
import '../../../../shared/utils/string_extensions.dart';
import '../../../../shared/services/booking/technician_booking_notification_service.dart';

class TechnicianBookingFormScreen extends ConsumerStatefulWidget {
  final String technicianId;

  const TechnicianBookingFormScreen({super.key, required this.technicianId});

  @override
  ConsumerState<TechnicianBookingFormScreen> createState() =>
      _TechnicianBookingFormScreenState();
}

class _TechnicianBookingFormScreenState
    extends ConsumerState<TechnicianBookingFormScreen> {
  final _formKey = GlobalKey<FormState>();
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  String? _selectedService;
  final _notesController = TextEditingController();

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? TimeOfDay.now(),
    );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  Future<void> _submitBooking() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final authState = ref.read(authProvider);
    final user = authState;
    if (user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please sign in to book a service')),
      );
      return;
    }

    final technician = ref.read(technicianProvider(widget.technicianId)).value;
    if (technician == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Technician not found')));
      return;
    }

    if (_selectedDate == null ||
        _selectedTime == null ||
        _selectedService == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select date, time and service')),
      );
      return;
    }

    final bookingDate = DateTime(
      _selectedDate!.year,
      _selectedDate!.month,
      _selectedDate!.day,
      _selectedTime!.hour,
      _selectedTime!.minute,
    );

    final servicePrice = technician.serviceRates.isNotEmpty
        ? technician.serviceRates.values.first
        : 0.0;
    final taxAmount = servicePrice * 0.1; // 10% tax
    final totalAmount = servicePrice + taxAmount;

    final booking = BookingModel(
      id: const Uuid().v4(),
      bookingNumber: DateTime.now().millisecondsSinceEpoch.toString(),
      customerId: user.id,
      providerId: technician.id,
      type: BookingType.technician,
      status: BookingStatus.pending,
      paymentStatus: PaymentStatus.pending,
      bookingDate: bookingDate,
      startTime: bookingDate,
      endTime: bookingDate.add(const Duration(hours: 1)),
      serviceLocation: BookingAddress(
        street: technician.serviceAreas.isNotEmpty
            ? technician.serviceAreas.first
            : '',
        city: '',
        state: '',
        country: '',
        postalCode: '',
        contactName:
            user.userMetadata?['display_name'] ??
            user.userMetadata?['full_name'] ??
            '',
        contactPhone: user.phone ?? '',
        contactEmail: user.email,
      ),
      services: [_selectedService!],
      subtotalAmount: servicePrice,
      taxAmount: taxAmount,
      discountAmount: 0.0,
      totalAmount: totalAmount,
      notes: _notesController.text,
      customerName:
          user.userMetadata?['display_name'] ?? user.userMetadata?['full_name'],
      customerEmail: user.email,
      providerName: technician.name,
      providerPhone: technician.phone,
      providerEmail: technician.email,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    try {
      print('Creating booking with data: ${booking.toJson()}');
      final notifier = ref.read(bookingStateNotifierProvider(user.id).notifier);
      await notifier.createBooking(booking);
      print('Booking created successfully');

      // Send notification to technician about new booking
      final notificationService = TechnicianBookingNotificationService();
      await notificationService.notifyTechnicianOfNewBooking(
        booking: booking,
        technician: technician,
      );

      if (mounted) {
        // Show payment options dialog
        final shouldProceedWithPayment = await _showPaymentDialog(
          context,
          booking.totalAmount,
        );

        if (shouldProceedWithPayment == true) {
          // Process payment
          final paymentSuccess = await _processPayment(booking);

          if (paymentSuccess) {
            // Update booking status to confirmed after successful payment
            final updatedBooking = booking.copyWith(
              paymentStatus: PaymentStatus.completed,
              status: BookingStatus.confirmed,
            );

            await notifier.updateBooking(updatedBooking);

            // Send confirmation notifications
            await notificationService.notifyBuyerOfBookingConfirmation(
              booking: updatedBooking,
              technician: technician,
            );

            await notificationService.notifyPaymentUpdate(
              booking: updatedBooking,
              technicianId: technician.id,
              amount: booking.totalAmount,
              paymentStatus: 'completed',
            );

            // Schedule reminder notifications
            await notificationService.scheduleServiceReminders(
              booking: updatedBooking,
              technician: technician,
            );

            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Booking confirmed and payment successful!'),
              ),
            );
          } else {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Payment failed. Booking saved but not confirmed.',
                ),
              ),
            );
          }
        } else {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Booking saved. Complete payment to confirm.'),
            ),
          );
        }
      }
    } catch (e, stackTrace) {
      print('Error creating booking: $e');
      print('Stack trace: $stackTrace');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error creating booking: $e')));
      }
    }
  }

  Future<bool?> _showPaymentDialog(BuildContext context, double amount) async {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment Required'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Total Amount: ₹${amount.toStringAsFixed(2)}'),
            const SizedBox(height: 16),
            const Text('Choose payment method:'),
            const SizedBox(height: 8),
            const Text('• UPI Payment'),
            const Text('• Card Payment'),
            const Text('• Pay Later (booking will be pending)'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Pay Later'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Pay Now'),
          ),
        ],
      ),
    );
  }

  Future<bool> _processPayment(BookingModel booking) async {
    try {
      // Show payment processing dialog
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Processing payment...'),
              ],
            ),
          ),
        );
      }

      // Simulate payment processing delay
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        Navigator.pop(context); // Close processing dialog
      }

      // For demo purposes, return true (successful payment)
      // In production, this would call actual payment service
      return true;
    } catch (e) {
      debugPrint('Payment processing error: $e');
      if (mounted) {
        Navigator.pop(context); // Close processing dialog if open
      }
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final technicianAsync = ref.watch(technicianProvider(widget.technicianId));

    return Scaffold(
      appBar: AppBar(title: const Text('Book Technician')),
      body: technicianAsync.when(
        data: (technician) {
          if (technician == null) {
            return const Center(child: Text('Technician not found'));
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Service',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _selectedService,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: 'Select a service',
                    ),
                    items: technician.specializations.map((service) {
                      return DropdownMenuItem(
                        value: service,
                        child: Text(service.toString().capitalize()),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedService = value;
                      });
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'Please select a service';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Select Date',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: () => _selectDate(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Select date',
                      ),
                      child: Text(
                        _selectedDate != null
                            ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                            : 'Select date',
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Select Time',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: () => _selectTime(context),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Select time',
                      ),
                      child: Text(
                        _selectedTime != null
                            ? _selectedTime!.format(context)
                            : 'Select time',
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Additional Notes',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _notesController,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: 'Add any additional notes',
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 24),
                  if (_selectedService != null) ...[
                    Text(
                      'Price Details',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Service Price',
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        Text(
                          '₹${(technician.serviceRates.isNotEmpty ? technician.serviceRates.values.first : 0.0).toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Tax (10%)',
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        Text(
                          '₹${((technician.serviceRates.isNotEmpty ? technician.serviceRates.values.first : 0.0) * 0.1).toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Divider(),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Total',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        Text(
                          '₹${((technician.serviceRates.isNotEmpty ? technician.serviceRates.values.first : 0.0) * 1.1).toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ],
                    ),
                  ],
                  const SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _submitBooking,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Confirm Booking'),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(child: Text('Error: $error')),
      ),
    );
  }
}
