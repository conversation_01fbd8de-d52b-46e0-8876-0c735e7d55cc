import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/offer/bank_offer_model.dart';
import '../repositories/bank_offer_repository_impl.dart';

/// Override the repository provider with the implementation
final bankOfferRepositoryProvider = bankOfferRepositoryImplProvider;

/// Provider for bank offers by product ID
final bankOffersByProductProvider = FutureProvider.family<List<BankOfferModel>, String>((ref, productId) async {
  final repository = ref.watch(bankOfferRepositoryProvider);
  return repository.getBankOffersByProductId(productId);
});

/// Provider for bank offers by category ID
final bankOffersByCategoryProvider = FutureProvider.family<List<BankOfferModel>, String>((ref, categoryId) async {
  final repository = ref.watch(bankOfferRepositoryProvider);
  return repository.getBankOffersByCategoryId(categoryId);
});

/// Provider for all active bank offers
final activeBankOffersProvider = FutureProvider<List<BankOfferModel>>((ref) async {
  final repository = ref.watch(bankOfferRepositoryProvider);
  return repository.getActiveBankOffers();
});
