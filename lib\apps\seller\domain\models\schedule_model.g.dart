// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ScheduleModel _$ScheduleModelFromJson(Map<String, dynamic> json) =>
    _ScheduleModel(
      id: json['id'] as String,
      isOpen24x7: json['isOpen24x7'] as bool,
      weeklySchedule: (json['weeklySchedule'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, DaySchedule.fromJson(e as Map<String, dynamic>)),
      ),
      holidays: (json['holidays'] as List<dynamic>)
          .map((e) => HolidaySchedule.fromJson(e as Map<String, dynamic>))
          .toList(),
      timeZone: json['timeZone'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$ScheduleModelToJson(_ScheduleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'isOpen24x7': instance.isOpen24x7,
      'weeklySchedule': instance.weeklySchedule.map(
        (k, e) => MapEntry(k, e.toJson()),
      ),
      'holidays': instance.holidays.map((e) => e.toJson()).toList(),
      'timeZone': instance.timeZone,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

_DaySchedule _$DayScheduleFromJson(Map<String, dynamic> json) => _DaySchedule(
  isOpen: json['isOpen'] as bool,
  slots: (json['slots'] as List<dynamic>)
      .map((e) => TimeSlot.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$DayScheduleToJson(_DaySchedule instance) =>
    <String, dynamic>{
      'isOpen': instance.isOpen,
      'slots': instance.slots.map((e) => e.toJson()).toList(),
    };

_TimeSlot _$TimeSlotFromJson(Map<String, dynamic> json) => _TimeSlot(
  openTime: json['openTime'] as String,
  closeTime: json['closeTime'] as String,
  breakStart: json['breakStart'] as String?,
  breakEnd: json['breakEnd'] as String?,
);

Map<String, dynamic> _$TimeSlotToJson(_TimeSlot instance) => <String, dynamic>{
  'openTime': instance.openTime,
  'closeTime': instance.closeTime,
  'breakStart': instance.breakStart,
  'breakEnd': instance.breakEnd,
};

_HolidaySchedule _$HolidayScheduleFromJson(Map<String, dynamic> json) =>
    _HolidaySchedule(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      isRecurring: json['isRecurring'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$HolidayScheduleToJson(_HolidaySchedule instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'isRecurring': instance.isRecurring,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
