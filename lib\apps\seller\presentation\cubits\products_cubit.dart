import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/services/product/product_service.dart';
import 'package:shivish/shared/models/product/product_model.dart';

part 'products_cubit.freezed.dart';

@freezed
sealed class ProductsState with _$ProductsState {
  const factory ProductsState.initial() = _Initial;
  const factory ProductsState.loading() = _Loading;
  const factory ProductsState.loaded(List<ProductModel> products) = _Loaded;
  const factory ProductsState.error(String message) = _Error;
}

class ProductsCubit extends Cubit<ProductsState> {
  final ProductService _productService;

  ProductsCubit(this._productService) : super(const ProductsState.initial()) {
    loadProducts();
  }

  Future<void> loadProducts() async {
    emit(const ProductsState.loading());
    try {
      final products = await _productService.getSellerProducts();
      emit(ProductsState.loaded(products));
    } catch (e) {
      emit(ProductsState.error(e.toString()));
    }
  }
}

class Product {
  final String id;
  final String name;
  final String imageUrl;
  final double price;
  final int stock;
  final DateTime createdAt;
  final String category;
  final String description;
  final List<String> tags;
  final double rating;
  final int totalReviews;

  const Product({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.price,
    required this.stock,
    required this.createdAt,
    required this.category,
    required this.description,
    required this.tags,
    required this.rating,
    required this.totalReviews,
  });
}
