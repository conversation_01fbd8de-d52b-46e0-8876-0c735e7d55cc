import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/help/article_model.dart';
import '../../../../shared/ui_components/errors/error_message.dart';
import '../../providers/help/article_provider.dart';

class KnowledgeBaseScreen extends ConsumerStatefulWidget {
  const KnowledgeBaseScreen({super.key});

  @override
  ConsumerState<KnowledgeBaseScreen> createState() =>
      _KnowledgeBaseScreenState();
}

class _KnowledgeBaseScreenState extends ConsumerState<KnowledgeBaseScreen> {
  final _searchController = TextEditingController();
  final _searchFocusNode = FocusNode();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
    });
  }

  List<Article> _filterArticles(List<ArticleModel> articles) {
    if (_searchQuery.isEmpty) return articles.map(Article.fromModel).toList();
    return articles.map(Article.fromModel).where((article) {
      return article.title.toLowerCase().contains(_searchQuery) ||
          article.description.toLowerCase().contains(_searchQuery);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final categoriesAsync = ref.watch(articleCategoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Knowledge Base'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              showSearch(
                context: context,
                delegate: _ArticleSearchDelegate(
                  searchController: _searchController,
                  searchFocusNode: _searchFocusNode,
                  onSearchChanged: _onSearchChanged,
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.bookmark),
            onPressed: () {
              context.push('/help/bookmarks');
            },
          ),
        ],
      ),
      body: categoriesAsync.when(
        data: (categories) {
          if (categories.isEmpty) {
            return const Center(
              child: Text('No articles available'),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final filteredArticles = _filterArticles(category.articles);

              if (filteredArticles.isEmpty) {
                return const SizedBox.shrink();
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    category.title,
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: filteredArticles.length,
                    itemBuilder: (context, articleIndex) {
                      final article = filteredArticles[articleIndex];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: ListTile(
                          leading: Icon(
                            IconData(
                              int.parse(category.icon),
                              fontFamily: 'MaterialIcons',
                            ),
                            color: theme.colorScheme.primary,
                          ),
                          title: Text(article.title),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 4),
                              Text(article.description),
                              const SizedBox(height: 4),
                              Text(
                                article.readTime,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                          trailing: const Icon(Icons.chevron_right),
                          onTap: () {
                            context.push('/help/articles/${article.id}');
                          },
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 24),
                ],
              );
            },
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorMessage(
            message: error.toString(),
            onRetry: () {
              ref.invalidate(articleCategoriesProvider);
            },
          ),
        ),
      ),
    );
  }
}

class _ArticleSearchDelegate extends SearchDelegate<String> {
  final TextEditingController searchController;
  final FocusNode searchFocusNode;
  final Function(String) onSearchChanged;

  _ArticleSearchDelegate({
    required this.searchController,
    required this.searchFocusNode,
    required this.onSearchChanged,
  });

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
          searchController.clear();
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults();
  }

  Widget _buildSearchResults() {
    return Consumer(
      builder: (context, ref, child) {
        final categoriesAsync = ref.watch(articleCategoriesProvider);
        return categoriesAsync.when(
          data: (categories) {
            final allArticles = categories.expand((c) => c.articles).toList();
            final filteredArticles = allArticles.where((article) {
              return article.title
                      .toLowerCase()
                      .contains(query.toLowerCase()) ||
                  article.description
                      .toLowerCase()
                      .contains(query.toLowerCase());
            }).toList();

            if (filteredArticles.isEmpty) {
              return Center(
                child: Text(
                  'No articles found for "$query"',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              );
            }

            return ListView.builder(
              itemCount: filteredArticles.length,
              itemBuilder: (context, index) {
                final article = filteredArticles[index];
                return ListTile(
                  title: Text(article.title),
                  subtitle: Text(article.description),
                  onTap: () {
                    context.push('/help/articles/${article.id}');
                    close(context, '');
                  },
                );
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, stackTrace) => Center(
            child: ErrorMessage(
              message: error.toString(),
              onRetry: () {
                ref.invalidate(articleCategoriesProvider);
              },
            ),
          ),
        );
      },
    );
  }
}

class ArticleCategory {
  final String title;
  final IconData icon;
  final List<ArticleModel> articles;

  ArticleCategory({
    required this.title,
    required this.icon,
    required this.articles,
  });
}

class Article {
  final String id;
  final String title;
  final String description;
  final String readTime;

  Article({
    required this.id,
    required this.title,
    required this.description,
    required this.readTime,
  });

  factory Article.fromModel(ArticleModel model) {
    return Article(
      id: model.id,
      title: model.title,
      description: model.description,
      readTime: model.readTime,
    );
  }
}
