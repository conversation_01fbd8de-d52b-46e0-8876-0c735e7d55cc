import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import '../../../../../shared/providers/file_provider.dart';

class FileUploadDialog extends HookConsumerWidget {
  final String userId;

  const FileUploadDialog({
    super.key,
    required this.userId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedFile = useState<File?>(null);
    final descriptionController = useTextEditingController();
    final tagsController = useTextEditingController();
    final isUploading = useState(false);

    Future<void> pickFile() async {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null) {
        selectedFile.value = File(result.files.single.path!);
      }
    }

    Future<void> uploadFile() async {
      if (selectedFile.value == null) return;

      isUploading.value = true;

      try {
        final tags = tagsController.text
            .split(',')
            .map((tag) => tag.trim())
            .where((tag) => tag.isNotEmpty)
            .toList();

        await ref.read(uploadFileProvider(
          UploadFileParams(
            userId: userId,
            file: selectedFile.value!,
            description: descriptionController.text,
            tags: tags,
          ),
        ).future);

        if (context.mounted) {
          Navigator.of(context).pop();
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to upload file: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        isUploading.value = false;
      }
    }

    return AlertDialog(
      title: const Text('Upload File'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ElevatedButton.icon(
            onPressed: pickFile,
            icon: const Icon(Icons.attach_file),
            label: Text(
              selectedFile.value != null
                  ? selectedFile.value!.path.split('/').last
                  : 'Select File',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              hintText: 'Enter file description',
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: tagsController,
            decoration: const InputDecoration(
              labelText: 'Tags',
              hintText: 'Enter tags separated by commas',
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: selectedFile.value != null && !isUploading.value
              ? uploadFile
              : null,
          child: isUploading.value
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                )
              : const Text('Upload'),
        ),
      ],
    );
  }
}
