import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/ticket_booking_models.dart';

class HotelRoomSelectionScreen extends ConsumerStatefulWidget {
  final Hotel hotel;
  final HotelSearchRequest searchRequest;

  const HotelRoomSelectionScreen({
    super.key,
    required this.hotel,
    required this.searchRequest,
  });

  @override
  ConsumerState<HotelRoomSelectionScreen> createState() =>
      _HotelRoomSelectionScreenState();
}

class _HotelRoomSelectionScreenState
    extends ConsumerState<HotelRoomSelectionScreen> {
  final List<RoomType> _availableRooms = [];
  final Map<String, int> _selectedRooms = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeRoomTypes();
  }

  void _initializeRoomTypes() {
    // Generate different room types for the hotel
    _availableRooms.addAll([
      RoomType(
        id: 'standard',
        name: 'Standard Room',
        description: 'Comfortable room with basic amenities',
        price: widget.hotel.price,
        currency: widget.hotel.currency,
        maxOccupancy: 2,
        amenities: ['Free WiFi', 'Air Conditioning', 'TV', 'Room Service'],
        images: ['https://example.com/standard-room.jpg'],
        availableCount: 5,
      ),
      RoomType(
        id: 'deluxe',
        name: 'Deluxe Room',
        description: 'Spacious room with premium amenities',
        price: widget.hotel.price * 1.5,
        currency: widget.hotel.currency,
        maxOccupancy: 3,
        amenities: [
          'Free WiFi',
          'Air Conditioning',
          'TV',
          'Mini Bar',
          'Balcony',
          'Room Service',
        ],
        images: ['https://example.com/deluxe-room.jpg'],
        availableCount: 3,
      ),
      RoomType(
        id: 'suite',
        name: 'Executive Suite',
        description: 'Luxurious suite with separate living area',
        price: widget.hotel.price * 2.5,
        currency: widget.hotel.currency,
        maxOccupancy: 4,
        amenities: [
          'Free WiFi',
          'Air Conditioning',
          'TV',
          'Mini Bar',
          'Balcony',
          'Room Service',
          'Jacuzzi',
          'Butler Service',
        ],
        images: ['https://example.com/suite.jpg'],
        availableCount: 2,
      ),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Rooms'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
      body: Stack(
        children: [
          Column(
            children: [
              // Hotel Info Header
              _buildHotelInfoHeader(),

              // Room Types List
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _availableRooms.length,
                  itemBuilder: (context, index) {
                    return _buildRoomTypeCard(_availableRooms[index]);
                  },
                ),
              ),

              // Selected Rooms Summary
              _buildSelectedRoomsSummary(),

              // Continue Button
              _buildContinueButton(),
            ],
          ),

          // Loading overlay
          if (_isLoading)
            Container(
              color: Colors.black54,
              child: const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(color: Colors.white),
                    SizedBox(height: 16),
                    Text(
                      'Processing booking...',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHotelInfoHeader() {
    final nights = widget.searchRequest.checkOut
        .difference(widget.searchRequest.checkIn)
        .inDays;

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.purple.shade50,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.hotel, color: Colors.purple.shade700),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.hotel.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      widget.hotel.address,
                      style: TextStyle(color: Colors.grey.shade600),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    children: List.generate(
                      widget.hotel.rating.toInt(),
                      (index) =>
                          Icon(Icons.star, color: Colors.amber, size: 16),
                    ),
                  ),
                  Text(
                    '${widget.hotel.rating}/5',
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.calendar_today, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              Text(
                '${_formatDate(widget.searchRequest.checkIn)} - ${_formatDate(widget.searchRequest.checkOut)}',
                style: TextStyle(color: Colors.grey.shade600),
              ),
              const SizedBox(width: 16),
              Icon(Icons.nights_stay, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              Text(
                '$nights night${nights > 1 ? 's' : ''}',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRoomTypeCard(RoomType roomType) {
    final selectedCount = _selectedRooms[roomType.id] ?? 0;
    final nights = widget.searchRequest.checkOut
        .difference(widget.searchRequest.checkIn)
        .inDays;
    final totalPrice = roomType.price * nights;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        roomType.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        roomType.description,
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Max ${roomType.maxOccupancy} guests',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${roomType.currency} ${totalPrice.toStringAsFixed(0)}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                    Text(
                      'per night: ${roomType.currency} ${roomType.price.toStringAsFixed(0)}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Amenities
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: roomType.amenities.take(4).map((amenity) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.purple.shade50,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    amenity,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.purple.shade700,
                    ),
                  ),
                );
              }).toList(),
            ),

            if (roomType.amenities.length > 4) ...[
              const SizedBox(height: 4),
              Text(
                '+${roomType.amenities.length - 4} more amenities',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
            ],

            const SizedBox(height: 16),

            // Room selection controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Available: ${roomType.availableCount}',
                  style: TextStyle(
                    color: roomType.availableCount > 0
                        ? Colors.green
                        : Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      onPressed: selectedCount > 0
                          ? () =>
                                _updateRoomCount(roomType.id, selectedCount - 1)
                          : null,
                      icon: const Icon(Icons.remove_circle_outline),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '$selectedCount',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: selectedCount < roomType.availableCount
                          ? () =>
                                _updateRoomCount(roomType.id, selectedCount + 1)
                          : null,
                      icon: const Icon(Icons.add_circle_outline),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedRoomsSummary() {
    if (_selectedRooms.isEmpty ||
        _selectedRooms.values.every((count) => count == 0)) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Text(
          'Please select ${widget.searchRequest.rooms} room${widget.searchRequest.rooms > 1 ? 's' : ''}',
          style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
        ),
      );
    }

    final nights = widget.searchRequest.checkOut
        .difference(widget.searchRequest.checkIn)
        .inDays;
    double totalPrice = 0;
    int totalRooms = 0;

    for (final entry in _selectedRooms.entries) {
      final roomType = _availableRooms.firstWhere(
        (room) => room.id == entry.key,
      );
      totalPrice += roomType.price * entry.value * nights;
      totalRooms += entry.value;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Selected Rooms:',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          ..._selectedRooms.entries.where((entry) => entry.value > 0).map((
            entry,
          ) {
            final roomType = _availableRooms.firstWhere(
              (room) => room.id == entry.key,
            );
            return Text(
              '${entry.value}x ${roomType.name}',
              style: TextStyle(color: Colors.grey.shade600),
            );
          }).toList(),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total: $totalRooms room${totalRooms > 1 ? 's' : ''} × $nights night${nights > 1 ? 's' : ''}',
                style: const TextStyle(fontSize: 16),
              ),
              Text(
                '${widget.hotel.currency} ${totalPrice.toStringAsFixed(0)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton() {
    final totalSelectedRooms = _selectedRooms.values.fold(
      0,
      (sum, count) => sum + count,
    );
    final isValidSelection = totalSelectedRooms == widget.searchRequest.rooms;

    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: isValidSelection ? _proceedToBooking : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.purple,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            isValidSelection
                ? 'Continue to Booking'
                : 'Select ${widget.searchRequest.rooms} room${widget.searchRequest.rooms > 1 ? 's' : ''}',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
        ),
      ),
    );
  }

  void _updateRoomCount(String roomId, int count) {
    setState(() {
      if (count == 0) {
        _selectedRooms.remove(roomId);
      } else {
        _selectedRooms[roomId] = count;
      }
    });
  }

  Future<void> _proceedToBooking() async {
    setState(() => _isLoading = true);

    try {
      // Simulate booking process
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Hotel rooms booked successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Booking failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${months[date.month - 1]}';
  }
}

class RoomType {
  final String id;
  final String name;
  final String description;
  final double price;
  final String currency;
  final int maxOccupancy;
  final List<String> amenities;
  final List<String> images;
  final int availableCount;

  RoomType({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    required this.maxOccupancy,
    required this.amenities,
    required this.images,
    required this.availableCount,
  });
}
