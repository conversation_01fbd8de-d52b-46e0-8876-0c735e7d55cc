import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import '../../../shared/services/voice/voice_assistant_service.dart';
import '../../../shared/services/ai/payment_restriction_service.dart';
import '../admin_routes.dart';

/// A service that handles voice commands for the admin app
class AdminVoiceCommandHandler {
  final VoiceAssistantService _voiceAssistant;
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  final BuildContext _context;
  final PaymentRestrictionService _paymentRestriction =
      PaymentRestrictionService();

  AdminVoiceCommandHandler(this._context, this._voiceAssistant);

  /// Process a voice command
  Future<void> processCommand(String command) async {
    try {
      final lowerCommand = command.toLowerCase();

      // Log the command
      await _analytics.logEvent(
        name: 'admin_voice_command',
        parameters: {'command': command},
      );

      // Check if this is a payment-related command
      if (_paymentRestriction.containsPaymentKeywords(lowerCommand) &&
          _paymentRestriction.isPaymentProcessingRequest(lowerCommand)) {
        _paymentRestriction.logPaymentRestriction(
            command, "admin_payment_processing");
        await _voiceAssistant
            .speakResponse(_paymentRestriction.getPaymentRestrictionResponse());
        return;
      }

      // Navigation commands
      if (_isNavigationCommand(lowerCommand)) {
        await _handleNavigationCommand(lowerCommand);
        return;
      }

      // User management commands
      if (_isUserManagementCommand(lowerCommand)) {
        await _handleUserManagementCommand(lowerCommand);
        return;
      }

      // Settings commands
      if (_isSettingsCommand(lowerCommand)) {
        await _handleSettingsCommand(lowerCommand);
        return;
      }

      // Content management commands
      if (_isContentManagementCommand(lowerCommand)) {
        await _handleContentManagementCommand(lowerCommand);
        return;
      }

      // Unknown command
      await _voiceAssistant.speakResponse(
          "I'm sorry, I didn't understand that command. Please try again.");
    } catch (e) {
      log('Error processing admin voice command: $e');
      await _voiceAssistant.speakResponse(
          "I'm sorry, there was an error processing your command.");
    }
  }

  /// Check if the command is a navigation command
  bool _isNavigationCommand(String command) {
    return command.contains('go to') ||
        command.contains('open') ||
        command.contains('show') ||
        command.contains('navigate to');
  }

  /// Handle navigation commands
  Future<void> _handleNavigationCommand(String command) async {
    try {
      String destination = '';

      // Extract destination
      if (command.contains('go to')) {
        destination = command.split('go to').last.trim();
      } else if (command.contains('open')) {
        destination = command.split('open').last.trim();
      } else if (command.contains('show')) {
        destination = command.split('show').last.trim();
      } else if (command.contains('navigate to')) {
        destination = command.split('navigate to').last.trim();
      }

      // Map common destinations to routes
      String route = '';
      String response = '';

      switch (destination) {
        case 'home':
        case 'dashboard':
          route = AdminRoutes.home;
          response = 'Going to admin dashboard';
          break;
        case 'profile':
        case 'my profile':
        case 'account':
          route = AdminRoutes.profile;
          response = 'Opening your admin profile';
          break;
        case 'settings':
        case 'system settings':
          route = AdminRoutes.settings;
          response = 'Opening system settings';
          break;
        case 'analytics':
          route = AdminRoutes.analytics;
          response = 'Opening analytics dashboard';
          break;
        case 'notifications':
          route = AdminRoutes.notifications;
          response = 'Showing your notifications';
          break;
        default:
          await _voiceAssistant.speakResponse(
              "I'm sorry, I don't know how to navigate to $destination.");
          return;
      }

      // Navigate to the destination
      await _voiceAssistant.speakResponse(response);

      if (_context.mounted && route.isNotEmpty) {
        GoRouter.of(_context).go(route);
      }
    } catch (e) {
      log('Error handling navigation command: $e');
      await _voiceAssistant
          .speakResponse("I'm sorry, I couldn't navigate to that destination.");
    }
  }

  /// Check if the command is a user management command
  bool _isUserManagementCommand(String command) {
    return command.contains('user') ||
        command.contains('users') ||
        command.contains('seller') ||
        command.contains('sellers') ||
        command.contains('priest') ||
        command.contains('priests') ||
        command.contains('technician') ||
        command.contains('technicians') ||
        command.contains('executor') ||
        command.contains('executors');
  }

  /// Handle user management commands
  Future<void> _handleUserManagementCommand(String command) async {
    try {
      String route = '';
      String response = '';

      if (command.contains('user') || command.contains('users')) {
        route = AdminRoutes.users;
        response = 'Opening user management';
      } else if (command.contains('seller') || command.contains('sellers')) {
        route = AdminRoutes.sellers;
        response = 'Opening seller management';
      } else if (command.contains('priest') || command.contains('priests')) {
        route = AdminRoutes.priests;
        response = 'Opening priest management';
      } else if (command.contains('technician') ||
          command.contains('technicians')) {
        route = AdminRoutes.technicians;
        response = 'Opening technician management';
      } else if (command.contains('executor') ||
          command.contains('executors')) {
        route = AdminRoutes.executors;
        response = 'Opening executor management';
      }

      if (route.isNotEmpty) {
        await _voiceAssistant.speakResponse(response);
        if (_context.mounted) {
          GoRouter.of(_context).go(route);
        }
      } else {
        await _voiceAssistant.speakResponse(
            "I'm sorry, I couldn't understand which user type you want to manage.");
      }
    } catch (e) {
      log('Error handling user management command: $e');
      await _voiceAssistant.speakResponse(
          "I'm sorry, I couldn't process your user management request.");
    }
  }

  /// Check if the command is a settings command
  bool _isSettingsCommand(String command) {
    return command.contains('settings') ||
        command.contains('configuration') ||
        command.contains('config') ||
        command.contains('setup') ||
        command.contains('api key') ||
        command.contains('payment gateway') ||
        command.contains('razorpay');
  }

  /// Handle settings commands
  Future<void> _handleSettingsCommand(String command) async {
    try {
      String route = '';
      String response = '';

      if (command.contains('api key')) {
        // Check if this is a payment processing request
        if (_paymentRestriction.isPaymentProcessingRequest(command)) {
          _paymentRestriction.logPaymentRestriction(
              command, "api_key_payment_processing");
          await _voiceAssistant.speakResponse(
              _paymentRestriction.getPaymentRestrictionResponse());
          return;
        }
        route = AdminRoutes.apiKeys;
        response = 'Opening API key management';
      } else if (command.contains('payment gateway')) {
        // Check if this is a payment processing request
        if (_paymentRestriction.isPaymentProcessingRequest(command)) {
          _paymentRestriction.logPaymentRestriction(
              command, "payment_gateway_processing");
          await _voiceAssistant.speakResponse(
              _paymentRestriction.getPaymentRestrictionResponse());
          return;
        }
        route = AdminRoutes.paymentGateway;
        response = 'Opening payment gateway settings';
      } else if (command.contains('razorpay')) {
        // Check if this is a payment processing request
        if (_paymentRestriction.isPaymentProcessingRequest(command)) {
          _paymentRestriction.logPaymentRestriction(
              command, "razorpay_payment_processing");
          await _voiceAssistant.speakResponse(
              _paymentRestriction.getPaymentRestrictionResponse());
          return;
        }
        route = AdminRoutes.razorpaySettings;
        response = 'Opening Razorpay settings';
      } else if (command.contains('security')) {
        route = AdminRoutes.securitySettings;
        response = 'Opening security settings';
      } else if (command.contains('notification settings')) {
        route = AdminRoutes.notificationSettings;
        response = 'Opening notification settings';
      } else if (command.contains('voice') ||
          command.contains('voice command')) {
        route = AdminRoutes.voiceCommandTraining;
        response = 'Opening voice command training';
      } else if (command.contains('ai') || command.contains('ai settings')) {
        route = AdminRoutes.aiSettings;
        response = 'Opening AI settings';
      } else {
        route = AdminRoutes.settings;
        response = 'Opening system settings';
      }

      await _voiceAssistant.speakResponse(response);
      if (_context.mounted) {
        GoRouter.of(_context).go(route);
      }
    } catch (e) {
      log('Error handling settings command: $e');
      await _voiceAssistant.speakResponse(
          "I'm sorry, I couldn't process your settings request.");
    }
  }

  /// Check if the command is a content management command
  bool _isContentManagementCommand(String command) {
    return command.contains('product') ||
        command.contains('products') ||
        command.contains('event') ||
        command.contains('events') ||
        command.contains('media') ||
        command.contains('banner') ||
        command.contains('banners');
  }

  /// Handle content management commands
  Future<void> _handleContentManagementCommand(String command) async {
    try {
      String route = '';
      String response = '';

      if (command.contains('product') || command.contains('products')) {
        route = AdminRoutes.products;
        response = 'Opening product management';
      } else if (command.contains('event') || command.contains('events')) {
        route = AdminRoutes.events;
        response = 'Opening event management';
      } else if (command.contains('media')) {
        route = AdminRoutes.media;
        response = 'Opening media management';
      } else if (command.contains('banner') || command.contains('banners')) {
        route = AdminRoutes.banners;
        response = 'Opening banner management';
      }

      if (route.isNotEmpty) {
        await _voiceAssistant.speakResponse(response);
        if (_context.mounted) {
          GoRouter.of(_context).go(route);
        }
      } else {
        await _voiceAssistant.speakResponse(
            "I'm sorry, I couldn't understand which content you want to manage.");
      }
    } catch (e) {
      log('Error handling content management command: $e');
      await _voiceAssistant.speakResponse(
          "I'm sorry, I couldn't process your content management request.");
    }
  }
}
