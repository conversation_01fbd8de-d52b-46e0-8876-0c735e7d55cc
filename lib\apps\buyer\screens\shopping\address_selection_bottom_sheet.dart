import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/address_provider.dart';
import '../../../../shared/models/address/address_model.dart';

/// Bottom sheet for selecting an address before proceeding to seller selection
class AddressSelectionBottomSheet extends ConsumerStatefulWidget {
  /// Creates an [AddressSelectionBottomSheet]
  const AddressSelectionBottomSheet({
    required this.listId,
    required this.onAddressSelected,
    super.key,
  });

  /// The ID of the shopping list
  final String listId;

  /// Callback when an address is selected and user wants to proceed
  final Function(AddressModel) onAddressSelected;

  /// Shows the address selection bottom sheet
  static Future<AddressModel?> show(BuildContext context, String listId) async {
    try {
      return await showModalBottomSheet<AddressModel>(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        isDismissible: true,
        enableDrag: true,
        useSafeArea: true, // Ensures content is not hidden by system UI
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.9, // Limit height to 90% of screen
        ),
        builder: (context) => AddressSelectionBottomSheet(
          listId: listId,
          onAddressSelected: (address) {
            Navigator.pop(context, address);
          },
        ),
      );
    } catch (e) {
      debugPrint('Error showing address selection bottom sheet: $e');
      return null;
    }
  }

  @override
  ConsumerState<AddressSelectionBottomSheet> createState() => _AddressSelectionBottomSheetState();
}

class _AddressSelectionBottomSheetState extends ConsumerState<AddressSelectionBottomSheet> {
  bool _isLoading = false;
  AddressModel? _selectedAddress;

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  Future<void> _loadAddresses() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get addresses from provider
      final addresses = await ref.read(userAddressesProvider.future);

      // If there are addresses, select the default one
      if (addresses.isNotEmpty) {
        final defaultAddress = addresses.firstWhere(
          (address) => address.isDefault,
          orElse: () => addresses.first,
        );

        setState(() {
          _selectedAddress = defaultAddress;
        });
      }
    } catch (e) {
      debugPrint('Error loading addresses: $e');
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load addresses: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _proceedToSellerSelection() {
    if (_selectedAddress == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select an address to continue'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Call the callback with the selected address
    widget.onAddressSelected(_selectedAddress!);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final addressesAsync = ref.watch(userAddressesProvider);
    final mediaQuery = MediaQuery.of(context);

    // Get the bottom padding to account for the navigation bar
    final bottomPadding = mediaQuery.padding.bottom;
    final bottomNavBarHeight = 80.0; // Approximate height of the bottom nav bar

    // Calculate the height of the bottom sheet (80% of screen height)
    final sheetHeight = mediaQuery.size.height * 0.8;

    return Container(
      height: sheetHeight,
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Select Delivery Address',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.close,
                        color: theme.colorScheme.onPrimary,
                      ),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'Choose where you want your items delivered',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onPrimary.withAlpha(204), // 0.8 * 255 = 204
                  ),
                ),
              ],
            ),
          ),

          // Address list - make it smaller to ensure buttons are visible
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : addressesAsync.when(
                    data: (addresses) {
                      if (addresses.isEmpty) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.location_off,
                                size: 64,
                                color: theme.colorScheme.primary.withAlpha(100),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No addresses found',
                                style: theme.textTheme.titleLarge,
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'Add a new address to continue',
                                style: TextStyle(color: Colors.grey),
                              ),
                              const SizedBox(height: 24),
                              ElevatedButton.icon(
                                onPressed: () {
                                  Navigator.pop(context);
                                  // Navigate to add address screen
                                  Navigator.pushNamed(context, '/add-address');
                                },
                                icon: const Icon(Icons.add_location_alt),
                                label: const Text('Add New Address'),
                              ),
                            ],
                          ),
                        );
                      }

                      return ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: addresses.length,
                        itemBuilder: (context, index) {
                          final address = addresses[index];
                          final isSelected = _selectedAddress?.id == address.id;

                          return Card(
                            elevation: 0,
                            color: isSelected
                                ? theme.colorScheme.primaryContainer.withAlpha(50)
                                : null,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(
                                color: isSelected
                                    ? theme.colorScheme.primary
                                    : Colors.grey.withAlpha(30),
                                width: 1,
                              ),
                            ),
                            margin: const EdgeInsets.only(bottom: 12),
                            child: InkWell(
                              borderRadius: BorderRadius.circular(12),
                              onTap: () {
                                setState(() {
                                  _selectedAddress = address;
                                });
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Radio<String>(
                                      value: address.id,
                                      groupValue: _selectedAddress?.id,
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedAddress = address;
                                        });
                                      },
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  address.name,
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                              if (address.isDefault)
                                                Container(
                                                  padding: const EdgeInsets.symmetric(
                                                    horizontal: 8,
                                                    vertical: 4,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: theme.colorScheme.primary,
                                                    borderRadius: BorderRadius.circular(4),
                                                  ),
                                                  child: Text(
                                                    'Default',
                                                    style: TextStyle(
                                                      color: theme.colorScheme.onPrimary,
                                                      fontSize: 12,
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            address.street,
                                            style: TextStyle(color: Colors.grey[700]),
                                          ),
                                          Text(
                                            '${address.city}, ${address.state} ${address.postalCode}',
                                            style: TextStyle(color: Colors.grey[700]),
                                          ),
                                          if (address.phone != null && address.phone!.isNotEmpty)
                                            Text(
                                              'Phone: ${address.phone}',
                                              style: TextStyle(color: Colors.grey[700]),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      );
                    },
                    loading: () => const Center(child: CircularProgressIndicator()),
                    error: (error, stackTrace) => Center(
                      child: Text('Error loading addresses: $error'),
                    ),
                  ),
          ),

          // Bottom buttons - with extra padding to avoid the navigation bar
          Container(
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              top: 16,
              bottom: bottomPadding + bottomNavBarHeight + 16, // Add extra padding at the bottom
            ),
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26), // 0.1 * 255 = 25.5 ≈ 26
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Continue button - show prominently
                ElevatedButton(
                  onPressed: _selectedAddress != null ? _proceedToSellerSelection : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    minimumSize: const Size(double.infinity, 48),
                    disabledBackgroundColor: theme.colorScheme.primary.withAlpha(77), // 0.3 * 255 = 76.5 ≈ 77
                  ),
                  child: const Text('Continue to Seller Selection'),
                ),
                const SizedBox(height: 12),
                // Add address button - secondary action
                OutlinedButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    // Navigate to add address screen
                    Navigator.pushNamed(context, '/add-address');
                  },
                  icon: const Icon(Icons.add_location_alt),
                  label: const Text('Add New Address'),
                  style: OutlinedButton.styleFrom(
                    minimumSize: const Size(double.infinity, 48),
                    side: BorderSide(color: theme.colorScheme.primary),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
