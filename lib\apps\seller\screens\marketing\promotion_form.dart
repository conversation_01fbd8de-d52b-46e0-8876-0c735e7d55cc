import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/domain/models/promotion_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/marketing_cubit.dart';
import 'package:shivish/shared/utils/date_formatter.dart';
import 'package:uuid/uuid.dart';

class PromotionForm extends StatefulWidget {
  final String? promotionId;

  const PromotionForm({super.key, this.promotionId});

  @override
  State<PromotionForm> createState() => _PromotionFormState();
}

class _PromotionFormState extends State<PromotionForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _valueController = TextEditingController();
  DateTime? _startDate;
  DateTime? _endDate;
  PromotionType _selectedType = PromotionType.percentage;
  List<String> _selectedProductIds = [];

  @override
  void initState() {
    super.initState();
    if (widget.promotionId != null) {
      _loadPromotion();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _valueController.dispose();
    super.dispose();
  }

  void _loadPromotion() {
    final promotion = context
        .read<MarketingCubit>()
        .state
        .promotions
        .firstWhere((p) => p.id == widget.promotionId);

    _nameController.text = promotion.name;
    _descriptionController.text = promotion.description;
    _valueController.text = promotion.value.toString();
    _selectedType = promotion.type;
    _startDate = promotion.startDate;
    _endDate = promotion.endDate;
    _selectedProductIds = promotion.productIds;
    setState(() {});
  }

  Future<void> _selectDateRange() async {
    final initialDateRange = DateTimeRange(
      start: _startDate ?? DateTime.now(),
      end: _endDate ?? DateTime.now().add(const Duration(days: 7)),
    );

    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: initialDateRange,
    );

    if (dateRange != null) {
      setState(() {
        _startDate = dateRange.start;
        _endDate = dateRange.end;
      });
    }
  }

  void _submit() {
    if (_formKey.currentState!.validate() &&
        _startDate != null &&
        _endDate != null) {
      final cubit = context.read<MarketingCubit>();
      final now = DateTime.now();

      final promotion = PromotionModel(
        id: widget.promotionId ?? const Uuid().v4(),
        name: _nameController.text,
        description: _descriptionController.text,
        type: _selectedType,
        value: double.parse(_valueController.text),
        startDate: _startDate!,
        endDate: _endDate!,
        productIds: _selectedProductIds,
        isActive: false,
        createdAt: now,
        updatedAt: now,
      );

      if (widget.promotionId != null) {
        cubit.updatePromotion(promotion);
      } else {
        cubit.createPromotion(promotion);
      }

      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                widget.promotionId != null
                    ? 'Edit Promotion'
                    : 'Create Promotion',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Promotion Name',
                  hintText: 'Enter promotion name',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Enter promotion description',
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<PromotionType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Promotion Type',
                ),
                items: PromotionType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.name),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _valueController,
                decoration: InputDecoration(
                  labelText: 'Value',
                  hintText: _selectedType == PromotionType.percentage
                      ? 'Enter percentage'
                      : 'Enter amount',
                  prefixText:
                      _selectedType == PromotionType.percentage ? '%' : '\$',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a value';
                  }
                  final number = double.tryParse(value);
                  if (number == null) {
                    return 'Please enter a valid number';
                  }
                  if (_selectedType == PromotionType.percentage &&
                      (number < 0 || number > 100)) {
                    return 'Percentage must be between 0 and 100';
                  }
                  if (number < 0) {
                    return 'Value must be positive';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              ListTile(
                title: Text(_startDate != null && _endDate != null
                    ? '${DateFormatter.formatDate(_startDate!)} - ${DateFormatter.formatDate(_endDate!)}'
                    : 'Select Date Range'),
                trailing: const Icon(Icons.calendar_today),
                onTap: _selectDateRange,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _submit,
                    child:
                        Text(widget.promotionId != null ? 'Update' : 'Create'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
