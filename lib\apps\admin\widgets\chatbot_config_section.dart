import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/config/chatbot_config_model.dart';
import '../../../shared/providers/system_config_provider.dart';

class ChatbotConfigSection extends ConsumerWidget {
  final ChatbotConfigModel config;

  const ChatbotConfigSection({
    super.key,
    required this.config,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Chatbot Configuration',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchField(
              context,
              ref,
              'Enabled',
              config.enabled,
              (value) => _updateConfig(
                ref,
                config.copyWith(enabled: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildTextField(
              context,
              ref,
              'Model Name',
              config.modelName,
              (value) => _updateConfig(
                ref,
                config.copyWith(modelName: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildTextField(
              context,
              ref,
              'Language',
              config.language,
              (value) => _updateConfig(
                ref,
                config.copyWith(language: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              context,
              ref,
              'Temperature',
              (config.temperature * 100).toInt(),
              (value) => _updateConfig(
                ref,
                config.copyWith(temperature: value / 100),
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              context,
              ref,
              'Max Tokens',
              config.maxTokens,
              (value) => _updateConfig(
                ref,
                config.copyWith(maxTokens: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildSupportedLanguagesField(
              context,
              ref,
              config.supportedLanguages,
              (value) => _updateConfig(
                ref,
                config.copyWith(supportedLanguages: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildResponseTemplatesField(
              context,
              ref,
              config.responseTemplates,
              (value) => _updateConfig(
                ref,
                config.copyWith(responseTemplates: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildModelParametersField(
              context,
              ref,
              config.modelParameters,
              (value) => _updateConfig(
                ref,
                config.copyWith(modelParameters: value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
    BuildContext context,
    WidgetRef ref,
    String label,
    String value,
    Function(String) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        Expanded(
          child: TextFormField(
            initialValue: value,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildNumberField(
    BuildContext context,
    WidgetRef ref,
    String label,
    int value,
    Function(int) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        SizedBox(
          width: 100,
          child: TextFormField(
            initialValue: value.toString(),
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              final number = int.tryParse(value);
              if (number != null) {
                onChanged(number);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchField(
    BuildContext context,
    WidgetRef ref,
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildSupportedLanguagesField(
    BuildContext context,
    WidgetRef ref,
    List<String> languages,
    Function(List<String>) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Supported Languages'),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: languages.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: languages[index],
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        final newLanguages = List<String>.from(languages);
                        newLanguages[index] = value;
                        onChanged(newLanguages);
                      },
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      final newLanguages = List<String>.from(languages);
                      newLanguages.removeAt(index);
                      onChanged(newLanguages);
                    },
                  ),
                ],
              ),
            );
          },
        ),
        ElevatedButton.icon(
          onPressed: () {
            final newLanguages = List<String>.from(languages);
            newLanguages.add('');
            onChanged(newLanguages);
          },
          icon: const Icon(Icons.add),
          label: const Text('Add Language'),
        ),
      ],
    );
  }

  Widget _buildResponseTemplatesField(
    BuildContext context,
    WidgetRef ref,
    Map<String, String> templates,
    Function(Map<String, String>) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Response Templates'),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: templates.length,
          itemBuilder: (context, index) {
            final key = templates.keys.elementAt(index);
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: key,
                      decoration: const InputDecoration(
                        labelText: 'Key',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        final newTemplates =
                            Map<String, String>.from(templates);
                        final oldValue = newTemplates[key]!;
                        newTemplates.remove(key);
                        newTemplates[value] = oldValue;
                        onChanged(newTemplates);
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextFormField(
                      initialValue: templates[key],
                      decoration: const InputDecoration(
                        labelText: 'Template',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        final newTemplates =
                            Map<String, String>.from(templates);
                        newTemplates[key] = value;
                        onChanged(newTemplates);
                      },
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      final newTemplates = Map<String, String>.from(templates);
                      newTemplates.remove(key);
                      onChanged(newTemplates);
                    },
                  ),
                ],
              ),
            );
          },
        ),
        ElevatedButton.icon(
          onPressed: () {
            final newTemplates = Map<String, String>.from(templates);
            newTemplates['new_key'] = 'new_template';
            onChanged(newTemplates);
          },
          icon: const Icon(Icons.add),
          label: const Text('Add Template'),
        ),
      ],
    );
  }

  Widget _buildModelParametersField(
    BuildContext context,
    WidgetRef ref,
    Map<String, dynamic> parameters,
    Function(Map<String, dynamic>) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Model Parameters'),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: parameters.length,
          itemBuilder: (context, index) {
            final key = parameters.keys.elementAt(index);
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: key,
                      decoration: const InputDecoration(
                        labelText: 'Key',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        final newParameters =
                            Map<String, dynamic>.from(parameters);
                        final oldValue = newParameters[key]!;
                        newParameters.remove(key);
                        newParameters[value] = oldValue;
                        onChanged(newParameters);
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextFormField(
                      initialValue: parameters[key].toString(),
                      decoration: const InputDecoration(
                        labelText: 'Value',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        final newParameters =
                            Map<String, dynamic>.from(parameters);
                        // Try to parse as number if possible
                        final numberValue = num.tryParse(value);
                        newParameters[key] = numberValue ?? value;
                        onChanged(newParameters);
                      },
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      final newParameters =
                          Map<String, dynamic>.from(parameters);
                      newParameters.remove(key);
                      onChanged(newParameters);
                    },
                  ),
                ],
              ),
            );
          },
        ),
        ElevatedButton.icon(
          onPressed: () {
            final newParameters = Map<String, dynamic>.from(parameters);
            newParameters['new_key'] = 'new_value';
            onChanged(newParameters);
          },
          icon: const Icon(Icons.add),
          label: const Text('Add Parameter'),
        ),
      ],
    );
  }

  Future<void> _updateConfig(
    WidgetRef ref,
    ChatbotConfigModel newConfig,
  ) async {
    await ref
        .read(systemConfigStateProvider.notifier)
        .updateChatbotConfig(newConfig);
  }
}
