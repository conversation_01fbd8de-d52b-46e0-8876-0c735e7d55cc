import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/technician/technician_bloc.dart';
import 'package:shivish/apps/admin/bloc/technician/technician_event.dart';
import 'package:shivish/apps/admin/bloc/technician/technician_state.dart';
import 'package:shivish/apps/admin/widgets/technician_list_item.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/models/user/user_model.dart';

class TechnicianManagementScreen extends StatelessWidget {
  const TechnicianManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Technician Management',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
        ],
      ),
      body: BlocBuilder<TechnicianBloc, TechnicianState>(
        builder: (context, state) {
          return state.when(
            initial: () {
              context.read<TechnicianBloc>().add(
                    const TechnicianEvent.loadTechnicians(),
                  );
              return const Center(child: LoadingIndicator());
            },
            loading: () => const Center(child: LoadingIndicator()),
            loadingMore: (technicians) => ListView.builder(
              itemCount: technicians.length + 1,
              itemBuilder: (context, index) {
                if (index == technicians.length) {
                  return const Center(child: LoadingIndicator());
                }
                return TechnicianListItem(
                  technician: technicians[index],
                  onStatusUpdate: (technician, status, notes, isActive) {
                    context.read<TechnicianBloc>().add(
                          TechnicianEvent.updateTechnicianStatus(
                            technician: technician,
                            status: status,
                            notes: notes,
                            isActive: isActive,
                          ),
                        );
                  },
                  onDelete: (id) {
                    context.read<TechnicianBloc>().add(
                          TechnicianEvent.deleteTechnician(id: id),
                        );
                  },
                );
              },
            ),
            loaded: (technicians) => technicians.isEmpty
                ? const Center(
                    child: Text(
                      'No technicians found',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: technicians.length,
                    itemBuilder: (context, index) {
                      return TechnicianListItem(
                        technician: technicians[index],
                        onStatusUpdate: (technician, status, notes, isActive) {
                          context.read<TechnicianBloc>().add(
                                TechnicianEvent.updateTechnicianStatus(
                                  technician: technician,
                                  status: status,
                                  notes: notes,
                                  isActive: isActive,
                                ),
                              );
                        },
                        onDelete: (id) {
                          context.read<TechnicianBloc>().add(
                                TechnicianEvent.deleteTechnician(id: id),
                              );
                        },
                      );
                    },
                  ),
            error: (message) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    message,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(height: 16),
                  AppButton(
                    onPressed: () {
                      context.read<TechnicianBloc>().add(
                            const TechnicianEvent.loadTechnicians(),
                          );
                    },
                    label: 'Retry',
                    variant: AppButtonVariant.primary,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _showFilterDialog(BuildContext context) async {
    UserStatus? selectedStatus;
    bool? isActive;

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Filter Technicians'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<UserStatus>(
                value: selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Status',
                  border: OutlineInputBorder(),
                ),
                items: UserStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(status.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedStatus = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<bool>(
                value: isActive,
                decoration: const InputDecoration(
                  labelText: 'Active Status',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: true,
                    child: Text('Active'),
                  ),
                  DropdownMenuItem(
                    value: false,
                    child: Text('Inactive'),
                  ),
                ],
                onChanged: (value) {
                  setState(() {
                    isActive = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                context.read<TechnicianBloc>().add(
                      TechnicianEvent.applyFilters(
                        filters: {
                          if (selectedStatus != null) 'status': selectedStatus,
                          if (isActive != null) 'isActive': isActive,
                        },
                      ),
                    );
                Navigator.pop(context);
              },
              child: const Text('Apply'),
            ),
          ],
        ),
      ),
    );
  }
}
