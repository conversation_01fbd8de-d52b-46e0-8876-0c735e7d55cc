import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/seller/seller_routes.dart';
import '../widgets/seller_drawer.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/application/orders/orders_provider.dart';
import 'package:shivish/apps/seller/application/seller_provider.dart';
import 'package:shivish/shared/providers/auth_provider.dart' as auth;

class SellerShell extends ConsumerStatefulWidget {
  final Widget child;

  const SellerShell({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<SellerShell> createState() => _SellerShellState();
}

class _SellerShellState extends ConsumerState<SellerShell> {
  @override
  void initState() {
    super.initState();
    // Schedule the data loading for after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // _loadInitialData already calls _loadSellerData
      _loadInitialData();
    });
  }

  Future<void> _loadInitialData() async {
    // First, ensure seller data is loaded
    await _loadSellerData();

    final seller = ref.read(sellerProvider).value;
    if (seller != null && mounted) {
      debugPrint('SellerShell: Seller data loaded, checking current route');

      // Load initial orders if we're on the orders tab
      final location = GoRouterState.of(context).uri.path;
      debugPrint('SellerShell: Current location: $location');

      if (location.startsWith(SellerRoutes.orders)) {
        debugPrint('SellerShell: On orders tab, loading orders');
        try {
          await ref.read(ordersProvider.notifier).getOrders();
        } catch (e) {
          debugPrint('SellerShell: Error loading orders: $e');
        }
      } else if (location.startsWith(SellerRoutes.profile)) {
        debugPrint('SellerShell: On profile tab, seller data already loaded');
      }
    } else {
      debugPrint('SellerShell: Seller data not available after loading');
    }
  }

  Future<void> _loadSellerData() async {
    try {
      // Get the current user ID from auth service
      final authService = ref.read(auth.authServiceProvider);
      final user = await authService.getCurrentUser();

      if (user == null) {
        debugPrint('SellerShell: No authenticated user found');
        return;
      }

      if (!mounted) return;

      debugPrint('SellerShell: Loading seller data for user: ${user.id}');

      // Load seller data outside of widget lifecycle
      final seller = await ref.read(sellerProvider.notifier).getSeller(user.id);

      if (seller == null) {
        debugPrint('SellerShell: Seller is still null after getSeller');
      } else {
        debugPrint(
            'SellerShell: Seller loaded successfully: ${seller.businessName}');
      }
    } catch (e) {
      debugPrint('SellerShell: Error loading seller data: $e');
    }
  }

  Future<void> _loadOrdersAndNavigate() async {
    try {
      await ref.read(ordersProvider.notifier).getOrders();
      if (mounted) {
        context.go(SellerRoutes.orders);
      }
    } catch (e) {
      debugPrint('Error loading orders: $e');
      if (mounted) {
        context.go(SellerRoutes.orders);
      }
    }
  }

  Future<void> _loadSellerProfileAndNavigate() async {
    try {
      await _loadSellerData();
      if (mounted) {
        debugPrint(
            'SellerShell: Navigation to profile after loading seller data');
        context.go(SellerRoutes.profile);
      }
    } catch (e) {
      debugPrint('SellerShell: Error loading seller data: $e');
      if (mounted) {
        context.go(SellerRoutes.profile);
      }
    }
  }

  void _handleNavigation(int index) {
    switch (index) {
      case 0:
        context.go(SellerRoutes.home);
        break;
      case 1:
        context.go(SellerRoutes.products);
        break;
      case 2:
        // Load orders data before navigation to avoid errors
        _loadOrdersAndNavigate();
        break;
      case 3:
        context.go(SellerRoutes.analyticsDashboard);
        break;
      case 4:
        // Load seller data before navigation to profile
        // This ensures we're not modifying the provider during widget lifecycle
        debugPrint('SellerShell: Navigating to profile tab');
        _loadSellerProfileAndNavigate();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('SellerShell: Building shell with child');
    final currentIndex = _calculateSelectedIndex(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Seller Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () => SellerRoutes.navigateToNotifications(context),
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => SellerRoutes.navigateToSettings(context),
          ),
        ],
      ),
      body: SafeArea(
        child: widget.child,
      ),
      drawer: const SellerDrawer(),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: _handleNavigation,
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory),
            label: 'Products',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: 'Orders',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Analytics',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }

  int _calculateSelectedIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;

    if (location.startsWith(SellerRoutes.home)) {
      return 0;
    }
    if (location.startsWith(SellerRoutes.products)) {
      return 1;
    }
    if (location.startsWith(SellerRoutes.orders)) {
      return 2;
    }
    if (location.startsWith(SellerRoutes.analyticsDashboard)) {
      return 3;
    }
    if (location.startsWith(SellerRoutes.profile)) {
      return 4;
    }
    return 0;
  }
}
