import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/shopping/shopping_list_provider.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';

class EditShoppingListItemDialog extends ConsumerStatefulWidget {
  final String listId;
  final ShoppingListItem item;

  const EditShoppingListItemDialog({
    required this.listId,
    required this.item,
    super.key,
  });

  @override
  ConsumerState<EditShoppingListItemDialog> createState() =>
      _EditShoppingListItemDialogState();
}

class _EditShoppingListItemDialogState
    extends ConsumerState<EditShoppingListItemDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _quantityController;
  late TextEditingController _priceController;
  late TextEditingController _notesController;
  late String _selectedUnit;
  final List<String> _unitOptions = ['kg', 'ltr', 'pcs', 'gm', 'ml', 'box'];

  @override
  void initState() {
    super.initState();
    
    // Extract unit from name if it's in the format "Name (unit)"
    String name = widget.item.name;
    String unit = 'kg'; // Default unit
    
    final unitMatch = RegExp(r'\((.*?)\)$').firstMatch(name);
    if (unitMatch != null) {
      unit = unitMatch.group(1) ?? 'kg';
      name = name.substring(0, name.lastIndexOf(' ('));
    }
    
    _nameController = TextEditingController(text: name);
    _quantityController = TextEditingController(text: widget.item.quantity.toString());
    _priceController = TextEditingController(text: widget.item.price.toStringAsFixed(2));
    _notesController = TextEditingController(text: widget.item.notes ?? '');
    _selectedUnit = unit;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _quantityController.dispose();
    _priceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _updateItem() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final listAsync = ref.read(shoppingListProvider(widget.listId));
      final list = listAsync.value;
      if (list == null) {
        throw Exception('Shopping list not found');
      }

      final updatedItem = widget.item.copyWith(
        name: '${_nameController.text} ($_selectedUnit)',
        price: double.tryParse(_priceController.text) ?? 0.0,
        quantity: int.tryParse(_quantityController.text) ?? 1,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
      );

      final itemIndex = list.items.indexWhere((item) => item.id == widget.item.id);
      if (itemIndex == -1) {
        throw Exception('Item not found in the list');
      }

      final updatedItems = List<ShoppingListItem>.from(list.items);
      updatedItems[itemIndex] = updatedItem;

      // Recalculate total price
      double totalPrice = 0;
      for (final item in updatedItems) {
        totalPrice += item.price * item.quantity;
      }

      final updatedList = list.copyWith(
        items: updatedItems,
        totalPrice: totalPrice,
        updatedAt: DateTime.now(),
      );

      await ref
          .read(shoppingListServiceProvider)
          .updateShoppingList(updatedList);

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Item updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update item: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Item'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Item Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an item name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      controller: _quantityController,
                      decoration: const InputDecoration(
                        labelText: 'Quantity',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }
                        if (int.tryParse(value) == null) {
                          return 'Invalid number';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 3,
                    child: DropdownButtonFormField<String>(
                      value: _selectedUnit,
                      decoration: const InputDecoration(
                        labelText: 'Unit',
                        border: OutlineInputBorder(),
                      ),
                      items: _unitOptions.map((unit) {
                        return DropdownMenuItem<String>(
                          value: unit,
                          child: Text(unit),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedUnit = value;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _priceController,
                decoration: const InputDecoration(
                  labelText: 'Price',
                  border: OutlineInputBorder(),
                  prefixText: '\$',
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a price';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Invalid price';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('CANCEL'),
        ),
        FilledButton(
          onPressed: _updateItem,
          child: const Text('UPDATE'),
        ),
      ],
    );
  }
}
