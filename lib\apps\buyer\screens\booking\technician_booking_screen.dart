import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/booking/booking_model.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/booking_provider.dart';
import '../../../../shared/ui_components/navigation/bottom_nav_back_handler.dart';
import '../../buyer_routes.dart';
import '../technician/technician_list_screen.dart';
import 'technician_booking_details_screen.dart';

class TechnicianBookingScreen extends ConsumerStatefulWidget {
  const TechnicianBookingScreen({super.key});

  @override
  ConsumerState<TechnicianBookingScreen> createState() =>
      _TechnicianBookingScreenState();
}

class _TechnicianBookingScreenState
    extends ConsumerState<TechnicianBookingScreen> {
  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final userId = authState?.id ?? '';

    final bookingsAsync = ref.watch(bookingStateNotifierProvider(userId));

    return BottomNavBackHandler(
      fallbackRoute: BuyerRoutes.home,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Technician Bookings'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go(BuyerRoutes.home),
          ),
        ),
        body: bookingsAsync.when(
          data: (bookings) {
            final technicianBookings = bookings
                .where((booking) => booking.type == BookingType.technician)
                .toList();

            if (technicianBookings.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.build, size: 64),
                    const SizedBox(height: 16),
                    const Text(
                      'No Technician Bookings Yet',
                      style: TextStyle(fontSize: 18),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const TechnicianListScreen(),
                          ),
                        );
                      },
                      child: const Text('Book a Technician'),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              itemCount: technicianBookings.length,
              itemBuilder: (context, index) {
                final booking = technicianBookings[index];
                return Card(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ListTile(
                    leading: const CircleAvatar(
                      child: Icon(Icons.build),
                    ),
                    title: Text('Booking #${booking.bookingNumber}'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Date: ${booking.bookingDate.toString().split(' ')[0]}',
                        ),
                        Text('Status: ${booking.status.name}'),
                        if (booking.paymentStatus == PaymentStatus.completed)
                          const Text('Paid',
                              style: TextStyle(color: Colors.green)),
                      ],
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.arrow_forward_ios),
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) =>
                                TechnicianBookingDetailsScreen(
                              bookingId: booking.id,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Error: $error'),
          ),
        ),
      ),
    );
  }
}
