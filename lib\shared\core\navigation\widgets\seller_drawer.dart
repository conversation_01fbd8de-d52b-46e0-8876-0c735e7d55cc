import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/seller_routes.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/apps/seller/application/seller_provider.dart';

// Create a provider for AuthService
final authServiceProvider = Provider<AuthService>((ref) => AuthService());

class SellerDrawer extends ConsumerWidget {
  const SellerDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sellerState = ref.watch(sellerProvider);
    final seller = sellerState.value;
    final authService = ref.watch(authServiceProvider);
    final user = authService.currentUser;

    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: seller?.businessLogo != null
                      ? NetworkImage(seller!.businessLogo!)
                      : null,
                  child: seller?.businessLogo == null
                      ? const Icon(Icons.business, size: 30)
                      : null,
                ),
                const SizedBox(height: 8),
                Text(
                  seller?.businessName ?? 'Loading...',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                  ),
                ),
                Text(
                  user?.email ?? '',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.home),
            title: const Text('Home'),
            onTap: () {
              Navigator.pop(context); // Close drawer
              SellerRoutes.navigateToHome(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.inventory),
            title: const Text('Products'),
            onTap: () {
              Navigator.pop(context);
              SellerRoutes.navigateToProducts(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.shopping_cart),
            title: const Text('Orders'),
            onTap: () {
              Navigator.pop(context);
              SellerRoutes.navigateToOrders(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.analytics),
            title: const Text('Analytics'),
            onTap: () {
              Navigator.pop(context);
              SellerRoutes.navigateToAnalyticsDashboard(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('Profile'),
            onTap: () {
              Navigator.pop(context);
              SellerRoutes.navigateToProfile(context);
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Settings'),
            onTap: () {
              Navigator.pop(context);
              SellerRoutes.navigateToSettings(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('Logout'),
            onTap: () async {
              Navigator.pop(context); // Close drawer
              await authService.signOut();
              if (context.mounted) {
                SellerRoutes.navigateToLogin(context);
              }
            },
          ),
        ],
      ),
    );
  }
}
