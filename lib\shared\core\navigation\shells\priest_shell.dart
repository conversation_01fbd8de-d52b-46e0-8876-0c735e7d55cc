import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/priest/priest_routes.dart';
import '../widgets/priest_bottom_nav_bar.dart';
import '../widgets/priest_drawer.dart';

class PriestShell extends StatelessWidget {
  final Widget child;

  const PriestShell({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      drawer: const PriestDrawer(),
      bottomNavigationBar: PriestBottomNavBar(
        currentIndex: _calculateSelectedIndex(context),
      ),
    );
  }

  int _calculateSelectedIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;

    if (location.startsWith(PriestRoutes.home)) {
      return 0;
    }
    if (location.startsWith(PriestRoutes.services)) {
      return 1;
    }
    if (location.startsWith(PriestRoutes.bookings)) {
      return 2;
    }
    if (location.startsWith(PriestRoutes.payments)) {
      return 3;
    }
    if (location.startsWith(PriestRoutes.profile)) {
      return 4;
    }
    return 0;
  }
}
