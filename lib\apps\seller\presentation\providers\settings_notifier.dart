import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/presentation/cubits/settings_cubit.dart';
import 'package:shivish/shared/services/settings/settings_service.dart';

/// A StateNotifier for managing settings
class SettingsNotifier extends StateNotifier<SettingsState> {
  final SettingsService _settingsService;

  SettingsNotifier(this._settingsService)
      : super(const SettingsState.loading()) {
    loadSettings();
  }

  Future<void> loadSettings() async {
    try {
      state = const SettingsState.loading();
      final settings = await _settingsService.getSettings();
      state = SettingsState.loaded(settings);
    } catch (e) {
      state = SettingsState.error(e.toString());
    }
  }

  Future<void> toggleOrderNotifications(bool value) async {
    try {
      state.maybeWhen(
        loaded: (settings) async {
          final updatedSettings = Settings(
            orderNotifications: value,
            productNotifications: settings.productNotifications,
            systemNotifications: settings.systemNotifications,
            theme: settings.theme,
            language: settings.language,
            currency: settings.currency,
          );
          await _settingsService.updateSettings(updatedSettings);
          state = SettingsState.loaded(updatedSettings);
        },
        orElse: () {},
      );
    } catch (e) {
      state = SettingsState.error(e.toString());
    }
  }

  Future<void> toggleProductNotifications(bool value) async {
    try {
      state.maybeWhen(
        loaded: (settings) async {
          final updatedSettings = Settings(
            orderNotifications: settings.orderNotifications,
            productNotifications: value,
            systemNotifications: settings.systemNotifications,
            theme: settings.theme,
            language: settings.language,
            currency: settings.currency,
          );
          await _settingsService.updateSettings(updatedSettings);
          state = SettingsState.loaded(updatedSettings);
        },
        orElse: () {},
      );
    } catch (e) {
      state = SettingsState.error(e.toString());
    }
  }

  Future<void> toggleSystemNotifications(bool value) async {
    try {
      state.maybeWhen(
        loaded: (settings) async {
          final updatedSettings = Settings(
            orderNotifications: settings.orderNotifications,
            productNotifications: settings.productNotifications,
            systemNotifications: value,
            theme: settings.theme,
            language: settings.language,
            currency: settings.currency,
          );
          await _settingsService.updateSettings(updatedSettings);
          state = SettingsState.loaded(updatedSettings);
        },
        orElse: () {},
      );
    } catch (e) {
      state = SettingsState.error(e.toString());
    }
  }

  Future<void> updateSettings(Settings settings) async {
    try {
      await _settingsService.updateSettings(settings);
      state = SettingsState.loaded(settings);
    } catch (e) {
      state = SettingsState.error(e.toString());
    }
  }
}
