import 'package:freezed_annotation/freezed_annotation.dart';

part 'panchangam_model.freezed.dart';
part 'panchangam_model.g.dart';

@freezed
abstract class PanchangamModel with _$PanchangamModel {
  const factory PanchangamModel({
    required DateTime date,
    required bool isAuspicious,
    required String tithi,
    required String nakshatra,
    required String yoga,
    required String karana,
    required String sunrise,
    required String sunset,
    required String moonrise,
    required String moonset,
    required String rahuKalam,
    required String gulikaKalam,
    required String yamagandam,
    required String abhijitMuhurta,
    @Default([]) List<String> festivals,
    @Default([]) List<String> vratas,
    @Default([]) List<String> specialEvents,
  }) = _PanchangamModel;

  factory PanchangamModel.fromJson(Map<String, dynamic> json) =>
      _$PanchangamModelFromJson(json);
}
