import 'package:freezed_annotation/freezed_annotation.dart';

import 'notification_type.dart';
import 'notification_status.dart';

part 'notification_model.freezed.dart';
part 'notification_model.g.dart';

/// Model class for notifications
@freezed
abstract class NotificationModel with _$NotificationModel {
  const factory NotificationModel({
    required String id,
    required String userId,
    required String title,
    required String body,
    required NotificationType type,
    required NotificationStatus status,
    required Map<String, dynamic> data,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isDeleted,
  }) = _NotificationModel;

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);

  /// Empty notification model
  factory NotificationModel.empty() => NotificationModel(
        id: '',
        userId: '',
        title: '',
        body: '',
        type: NotificationType.general,
        status: NotificationStatus.unread,
        data: {},
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
}
