import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:equatable/equatable.dart';
import '../../../../shared/models/user/user_model.dart';
import '../../../../shared/database/services/database_service.dart';
import '../../../../shared/core/service_locator.dart';

// Events
abstract class RoleAuthEvent extends Equatable {
  const RoleAuthEvent();

  @override
  List<Object?> get props => [];
}

class SignInEvent extends RoleAuthEvent {
  final String email;
  final String password;
  final UserRole role;

  const SignInEvent({
    required this.email,
    required this.password,
    required this.role,
  });

  @override
  List<Object?> get props => [email, password, role];
}

class SignOutEvent extends RoleAuthEvent {}

class CheckAuthEvent extends RoleAuthEvent {
  final UserRole role;

  const CheckAuthEvent({required this.role});

  @override
  List<Object?> get props => [role];
}

class RegisterUserEvent extends RoleAuthEvent {
  final String email;
  final String password;
  final String displayName;
  final String? phoneNumber;
  final UserRole role;
  final List<Map<String, dynamic>>? documents;

  const RegisterUserEvent({
    required this.email,
    required this.password,
    required this.displayName,
    this.phoneNumber,
    required this.role,
    this.documents,
  });

  @override
  List<Object?> get props => [email, password, displayName, phoneNumber, role, documents];
}

// States
abstract class RoleAuthState extends Equatable {
  const RoleAuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitialState extends RoleAuthState {}

class AuthLoadingState extends RoleAuthState {}

class AuthAuthenticatedState extends RoleAuthState {
  final User user;
  final bool isApproved;
  final UserRole role;

  const AuthAuthenticatedState({
    required this.user,
    required this.role,
    this.isApproved = false,
  });

  @override
  List<Object?> get props => [user, isApproved, role];
}

class AuthUnauthenticatedState extends RoleAuthState {}

class AuthErrorState extends RoleAuthState {
  final String message;

  const AuthErrorState(this.message);

  @override
  List<Object?> get props => [message];
}

class RegistrationSuccessState extends RoleAuthState {
  final bool isApproved;
  final UserRole role;

  const RegistrationSuccessState({
    required this.isApproved,
    required this.role,
  });

  @override
  List<Object?> get props => [isApproved, role];
}

class PendingApprovalState extends RoleAuthState {
  final String email;
  final UserRole role;

  const PendingApprovalState({
    required this.email,
    required this.role,
  });

  @override
  List<Object?> get props => [email, role];
}

class DocumentsRequiredState extends RoleAuthState {
  final UserRole role;
  final List<String> requiredDocuments;

  const DocumentsRequiredState({
    required this.role,
    required this.requiredDocuments,
  });

  @override
  List<Object?> get props => [role, requiredDocuments];
}

// Bloc
class RoleAuthBloc extends Bloc<RoleAuthEvent, RoleAuthState> {
  final SupabaseClient _supabase = Supabase.instance.client;
  final DatabaseService _database = serviceLocator<DatabaseService>();

  RoleAuthBloc() : super(AuthInitialState()) {
    on<SignInEvent>(_onSignIn);
    on<SignOutEvent>(_onSignOut);
    on<CheckAuthEvent>(_onCheckAuth);
    on<RegisterUserEvent>(_onRegisterUser);
  }

  Future<void> _onSignIn(SignInEvent event, Emitter<RoleAuthState> emit) async {
    try {
      emit(AuthLoadingState());

      // Sign in with Supabase
      final response = await _supabase.auth.signInWithPassword(
        email: event.email,
        password: event.password,
      );

      final user = response.user;
      if (user == null) {
        emit(const AuthErrorState('User is null after sign in'));
        return;
      }

      // Check if user has the correct role and if they're approved
      final userRecord = await _database.find('users', user.id);

      if (userRecord == null) {
        await _supabase.auth.signOut();
        emit(const AuthErrorState('User account not found'));
        return;
      }

      final role = userRecord['role'] != null
          ? UserRole.values[userRecord['role'] as int]
          : UserRole.buyer;

      if (role != event.role) {
        await _supabase.auth.signOut();
        emit(AuthErrorState('You are not registered as a ${event.role.name}'));
        return;
      }

      // Check if user is approved
      final isApproved = userRecord['is_approved'] == true;

      if (!isApproved) {
        // User has the correct role but not approved yet
        emit(PendingApprovalState(email: user.email ?? '', role: event.role));
        await _supabase.auth.signOut();
        return;
      }

      emit(AuthAuthenticatedState(
        user: user,
        isApproved: isApproved,
        role: event.role,
      ));
    } catch (e) {
      debugPrint('RoleAuthBloc: Sign in error - $e');
      emit(AuthErrorState(e.toString()));
    }
  }

  Future<void> _onSignOut(SignOutEvent event, Emitter<RoleAuthState> emit) async {
    try {
      emit(AuthLoadingState());
      await _supabase.auth.signOut();
      emit(AuthUnauthenticatedState());
    } catch (e) {
      debugPrint('RoleAuthBloc: Sign out error - $e');
      emit(AuthErrorState(e.toString()));
    }
  }

  Future<void> _onCheckAuth(CheckAuthEvent event, Emitter<RoleAuthState> emit) async {
    try {
      emit(AuthLoadingState());

      final user = _supabase.auth.currentUser;

      if (user == null) {
        emit(AuthUnauthenticatedState());
        return;
      }

      // Check if user has the correct role and if they're approved
      final userRecord = await _database.find('users', user.id);

      if (userRecord == null) {
        await _supabase.auth.signOut();
        emit(AuthUnauthenticatedState());
        return;
      }

      final role = userRecord['role'] != null
          ? UserRole.values[userRecord['role'] as int]
          : UserRole.buyer;

      if (role != event.role) {
        await _supabase.auth.signOut();
        emit(AuthUnauthenticatedState());
        return;
      }

      // Check if user is approved
      final isApproved = userRecord['is_approved'] == true;

      if (!isApproved) {
        // User has the correct role but not approved yet
        emit(PendingApprovalState(email: user.email ?? '', role: event.role));
        await _supabase.auth.signOut();
        return;
      }

      emit(AuthAuthenticatedState(
        user: user,
        isApproved: isApproved,
        role: event.role,
      ));
    } catch (e) {
      debugPrint('RoleAuthBloc: Check auth error - $e');
      emit(AuthErrorState(e.toString()));
    }
  }

  Future<void> _onRegisterUser(RegisterUserEvent event, Emitter<RoleAuthState> emit) async {
    User? createdUser;
    try {
      emit(AuthLoadingState());

      // Check if documents are required for this role
      bool documentsRequired = false;
      try {
        final configRecord = await _database.find('system_config', 'registration');
        documentsRequired = configRecord != null && configRecord['documents_required'] == true;
      } catch (e) {
        // Ignore database errors for document requirements check
        debugPrint('RoleAuthBloc: Error checking document requirements - $e');
      }

      // If documents are required but not provided
      if (documentsRequired && (event.documents == null || event.documents!.isEmpty)) {
        List<String> requiredDocs = [];

        switch (event.role) {
          case UserRole.seller:
            requiredDocs = ['Business License', 'ID Proof', 'Address Proof'];
            break;
          case UserRole.priest:
            requiredDocs = ['ID Proof', 'Qualification Certificate', 'Experience Certificate'];
            break;
          case UserRole.technician:
            requiredDocs = ['ID Proof', 'Skill Certificate', 'Work Experience Proof'];
            break;
          default:
            break;
        }

        emit(DocumentsRequiredState(role: event.role, requiredDocuments: requiredDocs));
        return;
      }

      // Create user with Supabase Auth - this is the most important part
      final response = await _supabase.auth.signUp(
        email: event.email,
        password: event.password,
        data: {
          'display_name': event.displayName,
          'role': event.role.name,
        },
      );

      createdUser = response.user;
      if (createdUser == null) {
        emit(const AuthErrorState('User is null after creation'));
        return;
      }

      // Try to create user document with role and pending approval status
      final userData = {
        'id': createdUser.id,
        'email': event.email,
        'display_name': event.displayName,
        'phone_number': event.phoneNumber,
        'role': event.role.index,
        'status': UserStatus.active.index,
        'is_approved': false, // Needs approval
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      try {
        await _database.create('users', userData);
      } catch (e) {
        // Log error but continue - the user is still created in Supabase Auth
        debugPrint('RoleAuthBloc: Error creating user document - $e');
      }

      // Try to create role-specific request document
      try {
        await _createRoleRequest(
          role: event.role,
          uid: createdUser.id,
          email: event.email,
          displayName: event.displayName,
          phoneNumber: event.phoneNumber,
          documents: event.documents,
        );
      } catch (e) {
        // Log error but continue - the user is still created in Supabase Auth
        debugPrint('RoleAuthBloc: Error creating request document - $e');
      }

      debugPrint('RoleAuthBloc: ${event.role.name} registration submitted for approval');
      emit(RegistrationSuccessState(isApproved: false, role: event.role));

      // Sign out since they need approval
      await _supabase.auth.signOut();
    } catch (e) {
      // If we created a user but encountered an error later, sign out
      if (createdUser != null) {
        try {
          await _supabase.auth.signOut();
          debugPrint('RoleAuthBloc: Signed out user after error');
        } catch (signOutError) {
          debugPrint('RoleAuthBloc: Error signing out user after error - $signOutError');
        }
      }

      debugPrint('RoleAuthBloc: Register user error - $e');
      emit(AuthErrorState(e.toString()));
    }
  }

  /// Create role-specific request document
  Future<void> _createRoleRequest({
    required UserRole role,
    required String uid,
    required String email,
    required String displayName,
    String? phoneNumber,
    List<Map<String, dynamic>>? documents,
  }) async {
    final baseData = {
      'uid': uid,
      'email': email,
      'display_name': displayName,
      'phone_number': phoneNumber,
      'status': 'pending',
      'documents': documents ?? [],
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'is_deleted': false,
    };

    String collection;
    Map<String, dynamic> roleSpecificData = {};

    switch (role) {
      case UserRole.seller:
        collection = 'seller_requests';
        roleSpecificData = {
          'business_type': '',
          'business_name': '',
          'business_address': '',
          'gst_number': '',
          'pan_number': '',
        };
        break;
      case UserRole.priest:
        collection = 'priest_requests';
        roleSpecificData = {
          'specializations': <String>[],
          'languages': <String>[],
          'services': <String>[],
          'experience_years': 0,
          'qualification_details': '',
          'temple_affiliation': '',
          'location': {},
          'availability': {},
          'pricing': {},
        };
        break;
      case UserRole.technician:
        collection = 'technician_requests';
        roleSpecificData = {
          'specializations': <String>[],
          'service_areas': <String>[],
          'certifications': <String>[],
          'tools_equipment': <String>[],
          'experience_years': 0,
          'availability_schedule': {},
          'service_rates': {},
        };
        break;
      default:
        // For other roles, just create a generic request
        collection = 'user_requests';
        break;
    }

    final requestData = {
      ...baseData,
      ...roleSpecificData,
    };

    await _database.create(collection, requestData);
  }
}
