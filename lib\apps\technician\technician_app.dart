import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart' as provider;
import 'package:shivish/apps/technician/theme/technician_theme.dart';
import 'package:shivish/shared/core/localization/l10n/app_localizations.dart';
import 'package:shivish/apps/technician/technician_routes.dart';
import 'package:shivish/shared/ui_components/navigation/global_back_handler.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/shared/services/messaging_service.dart';
import 'services/notification_popup_service.dart';

class TechnicianApp extends ConsumerStatefulWidget {
  const TechnicianApp({super.key});

  @override
  ConsumerState<TechnicianApp> createState() => _TechnicianAppState();
}

class _TechnicianAppState extends ConsumerState<TechnicianApp> {
  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  /// Initialize notification service for technician app
  Future<void> _initializeNotifications() async {
    try {
      debugPrint('Initializing technician notification service...');
      final messagingService = MessagingService();
      await messagingService.initialize();

      // Subscribe to technician-specific notification topics
      await messagingService.subscribeToTopic('technician_notifications');
      await messagingService.subscribeToTopic('booking_updates');
      await messagingService.subscribeToTopic('payment_notifications');
      await messagingService.subscribeToTopic('service_reminders');

      debugPrint('Technician notification service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing technician notification service: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Wrap the app with the AuthService provider
    return provider.MultiProvider(
      providers: [
        // Provide AuthService for the router
        provider.Provider<AuthService>(create: (_) => AuthService()),
      ],
      child: MaterialApp.router(
        title: 'Technician App',
        theme: TechnicianTheme.lightTheme,
        darkTheme: TechnicianTheme.darkTheme,
        routerConfig: TechnicianRoutes.router,
        debugShowCheckedModeBanner: false,
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en'), // English
          Locale('hi'), // Hindi
          Locale('te'), // Telugu
          Locale('ta'), // Tamil
          Locale('kn'), // Kannada
        ],
        builder: (context, child) {
          return GlobalBackHandler(
            fallbackRoute: TechnicianRoutes.home,
            child: TechnicianNotificationPopupInitializer(child: child!),
          );
        },
      ),
    );
  }
}
