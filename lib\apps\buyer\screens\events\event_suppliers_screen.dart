import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/services.dart';
import '../../../../shared/models/seller.dart';
import '../../../../apps/seller/domain/models/seller_category.dart';
import '../../../../shared/database/services/database_service.dart';
import '../../../../shared/database/config/database_config.dart';
import '../../buyer_routes.dart';
import '../../widgets/navigation/buyer_app_bar.dart';

class EventSuppliersScreen extends ConsumerStatefulWidget {
  final String category;

  const EventSuppliersScreen({super.key, required this.category});

  @override
  ConsumerState<EventSuppliersScreen> createState() =>
      _EventSuppliersScreenState();
}

class _EventSuppliersScreenState extends ConsumerState<EventSuppliersScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedSubCategory = 'All';
  String _sortBy = 'rating';
  double _maxPrice = 100000;
  double _minRating = 0;

  SellerCategory? _categoryModel;
  List<Seller> _suppliers = [];
  List<Seller> _filteredSuppliers = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCategoryData();
    _loadSuppliers();
  }

  void _loadCategoryData() {
    _categoryModel = _getSellerCategoryFromEventCategory(widget.category);
    setState(() {});
  }

  void _loadSuppliers() async {
    try {
      final databaseService = DatabaseService(DatabaseConfig.fromEnvironment());

      // Get the seller category that matches the event service category
      final sellerCategory = _getSellerCategoryFromEventCategory(
        widget.category,
      );

      if (sellerCategory != null) {
        final sellersData = await databaseService.getAll(
          'sellers',
          where:
              'category = ? AND is_approved = ? AND is_active = ? AND is_deleted = ?',
          whereParams: [sellerCategory.name, true, true, false],
        );

        final sellers = sellersData.map((data) {
          return Seller.fromJson(data);
        }).toList();

        setState(() {
          _suppliers = sellers;
          _filteredSuppliers = List.from(_suppliers);
          _isLoading = false;
        });
      } else {
        setState(() {
          _suppliers = [];
          _filteredSuppliers = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading suppliers: $e');
      setState(() {
        _suppliers = [];
        _filteredSuppliers = [];
        _isLoading = false;
      });
    }
  }

  SellerCategory? _getSellerCategoryFromEventCategory(String eventCategory) {
    switch (eventCategory) {
      case 'venues':
        return SellerCategory.eventVenues;
      case 'organizers':
        return SellerCategory.eventOrganizers;
      case 'photography':
        return SellerCategory.eventPhotography;
      case 'decorations':
        return SellerCategory.eventDecorations;
      case 'catering':
        return SellerCategory.eventCatering;
      case 'entertainment':
        return SellerCategory.eventEntertainment;
      case 'transportation':
        return SellerCategory.eventTransportation;
      case 'invitations':
        return SellerCategory.eventInvitations;
      case 'makeup':
        return SellerCategory.eventMakeup;
      case 'equipment':
        return SellerCategory.eventEquipment;
      default:
        return null;
    }
  }

  void _filterSuppliers() {
    List<Seller> filtered = List.from(_suppliers);

    // Search filter
    if (_searchController.text.isNotEmpty) {
      filtered = filtered
          .where(
            (supplier) =>
                supplier.businessName.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                ) ||
                (supplier.businessDescription ?? '').toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                ),
          )
          .toList();
    }

    // Sub-category filter - check service types in metadata
    if (_selectedSubCategory != 'All') {
      filtered = filtered.where((supplier) {
        final metadata =
            supplier.notificationSettings; // Using this as metadata storage
        if (metadata.containsKey('serviceTypes')) {
          final serviceTypes = metadata['serviceTypes'] as List<dynamic>?;
          return serviceTypes?.contains(_selectedSubCategory) ?? false;
        }
        return false;
      }).toList();
    }

    // Price filter - using totalRevenue as a proxy for pricing capability
    // Note: In production, you may want to add specific pricing fields to the seller model

    // Rating filter
    filtered = filtered
        .where((supplier) => supplier.rating >= _minRating)
        .toList();

    // Sort
    switch (_sortBy) {
      case 'rating':
        filtered.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'price_low':
        // Sort by total revenue (lower revenue might indicate lower pricing)
        filtered.sort((a, b) => a.totalRevenue.compareTo(b.totalRevenue));
        break;
      case 'price_high':
        filtered.sort((a, b) => b.totalRevenue.compareTo(a.totalRevenue));
        break;
      case 'reviews':
        filtered.sort((a, b) => b.totalReviews.compareTo(a.totalReviews));
        break;
    }

    setState(() {
      _filteredSuppliers = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BuyerAppBar(
        title: Text(_categoryModel?.displayName ?? 'Suppliers'),
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search suppliers...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                  ),
                  onChanged: (value) => _filterSuppliers(),
                ),

                const SizedBox(height: 12),

                // Filter Row
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        'Sub Category',
                        _selectedSubCategory,
                        () {
                          _showSubCategoryFilter();
                        },
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip('Sort', _getSortDisplayName(), () {
                        _showSortOptions();
                      }),
                      const SizedBox(width: 8),
                      _buildFilterChip('Price', '₹${_maxPrice.toInt()}', () {
                        _showPriceFilter();
                      }),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'Rating',
                        '${_minRating.toStringAsFixed(1)}+',
                        () {
                          _showRatingFilter();
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Results Count
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Text(
                  '${_filteredSuppliers.length} suppliers found',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const Spacer(),
                if (_isLoading)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
          ),

          // Suppliers List
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredSuppliers.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredSuppliers.length,
                    itemBuilder: (context, index) {
                      return _buildSupplierCard(_filteredSuppliers[index]);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$label: $value',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_down,
              size: 16,
              color: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSupplierCard(Seller supplier) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          context.go(
            BuyerRoutes.eventSupplierDetails.replaceAll(':id', supplier.id),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Supplier Image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey[200],
                    ),
                    child: supplier.profileImage != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              supplier.profileImage!,
                              fit: BoxFit.cover,
                            ),
                          )
                        : Icon(
                            Icons.business,
                            size: 30,
                            color: Colors.grey[400],
                          ),
                  ),

                  const SizedBox(width: 12),

                  // Supplier Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          supplier.businessName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          supplier.businessAddress.isNotEmpty
                              ? supplier.businessAddress.first
                              : 'Address not provided',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.star, size: 16, color: Colors.amber),
                            const SizedBox(width: 4),
                            Text(
                              supplier.rating.toStringAsFixed(1),
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '(${supplier.totalReviews})',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Price
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '₹${supplier.totalRevenue.toInt()}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const Text(
                        'Starting from',
                        style: TextStyle(fontSize: 10, color: Colors.grey),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Description
              Text(
                supplier.businessDescription ?? 'No description available',
                style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              // Tags and Status
              Row(
                children: [
                  // Verification Badge
                  if (supplier.isApproved)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.verified, size: 12, color: Colors.green),
                          const SizedBox(width: 4),
                          Text(
                            'Verified',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                  const SizedBox(width: 8),

                  // Availability Status
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: supplier.isActive
                          ? Colors.blue.withValues(alpha: 0.1)
                          : Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      supplier.isActive ? 'Available' : 'Unavailable',
                      style: TextStyle(
                        fontSize: 10,
                        color: supplier.isActive ? Colors.blue : Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),

                  const Spacer(),

                  // Contact Button
                  ElevatedButton(
                    onPressed: () {
                      _showContactOptions(supplier);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'Contact',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No suppliers found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your filters or search terms',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  String _getSortDisplayName() {
    switch (_sortBy) {
      case 'rating':
        return 'Rating';
      case 'price_low':
        return 'Price: Low to High';
      case 'price_high':
        return 'Price: High to Low';
      case 'reviews':
        return 'Most Reviews';
      default:
        return 'Rating';
    }
  }

  void _showSubCategoryFilter() {
    if (_categoryModel == null) return;

    final subCategories = ['All', ..._categoryModel!.availableServiceTypes];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter by Service Type'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: subCategories.length,
            itemBuilder: (context, index) {
              final subCategory = subCategories[index];
              final isSelected = _selectedSubCategory == subCategory;

              return RadioListTile<String>(
                title: Text(subCategory),
                value: subCategory,
                groupValue: _selectedSubCategory,
                onChanged: (value) {
                  setState(() {
                    _selectedSubCategory = value!;
                  });
                  _filterSuppliers();
                  Navigator.of(context).pop();
                },
                selected: isSelected,
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showSortOptions() {
    final sortOptions = [
      {'value': 'rating', 'label': 'Rating (High to Low)'},
      {'value': 'price_low', 'label': 'Price (Low to High)'},
      {'value': 'price_high', 'label': 'Price (High to Low)'},
      {'value': 'reviews', 'label': 'Most Reviews'},
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort By'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: sortOptions.map((option) {
            final isSelected = _sortBy == option['value'];

            return RadioListTile<String>(
              title: Text(option['label']!),
              value: option['value']!,
              groupValue: _sortBy,
              onChanged: (value) {
                setState(() {
                  _sortBy = value!;
                });
                _filterSuppliers();
                Navigator.of(context).pop();
              },
              selected: isSelected,
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showPriceFilter() {
    double tempMaxPrice = _maxPrice;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Price Filter'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Maximum Price: ₹${tempMaxPrice.toInt()}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            Slider(
              value: tempMaxPrice,
              min: 5000,
              max: 500000,
              divisions: 99,
              label: '₹${tempMaxPrice.toInt()}',
              onChanged: (value) {
                setState(() {
                  tempMaxPrice = value;
                });
              },
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('₹5,000', style: TextStyle(color: Colors.grey[600])),
                Text('₹5,00,000', style: TextStyle(color: Colors.grey[600])),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _maxPrice = tempMaxPrice;
              });
              _filterSuppliers();
              Navigator.of(context).pop();
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _showRatingFilter() {
    final ratingOptions = [
      {'value': 0.0, 'label': 'All Ratings'},
      {'value': 4.0, 'label': '4+ Stars'},
      {'value': 3.5, 'label': '3.5+ Stars'},
      {'value': 3.0, 'label': '3+ Stars'},
      {'value': 2.5, 'label': '2.5+ Stars'},
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter by Rating'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ratingOptions.map((option) {
            final isSelected = _minRating == option['value'];

            return RadioListTile<double>(
              title: Row(
                children: [
                  Text(option['label']! as String),
                  const SizedBox(width: 8),
                  if ((option['value']! as double) > 0)
                    Row(
                      children: List.generate(5, (index) {
                        return Icon(
                          index < (option['value']! as double)
                              ? Icons.star
                              : Icons.star_border,
                          size: 16,
                          color: Colors.amber,
                        );
                      }),
                    ),
                ],
              ),
              value: option['value']! as double,
              groupValue: _minRating,
              onChanged: (value) {
                setState(() {
                  _minRating = value!;
                });
                _filterSuppliers();
                Navigator.of(context).pop();
              },
              selected: isSelected,
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showContactOptions(Seller supplier) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundImage: supplier.profileImage != null
                      ? NetworkImage(supplier.profileImage!)
                      : null,
                  child: supplier.profileImage == null
                      ? const Icon(Icons.business, size: 30)
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        supplier.businessName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _categoryModel?.displayName ?? 'Service Provider',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 10),

            // Contact Options
            if (supplier.phoneNumber != null) ...[
              _buildContactOption(
                icon: Icons.phone,
                title: 'Call',
                subtitle: supplier.phoneNumber!,
                color: Colors.green,
                onTap: () {
                  Navigator.pop(context);
                  _makePhoneCall(supplier.phoneNumber!);
                },
              ),
              const SizedBox(height: 12),
              _buildContactOption(
                icon: Icons.message,
                title: 'WhatsApp',
                subtitle: 'Send message on WhatsApp',
                color: Colors.green[700]!,
                onTap: () {
                  Navigator.pop(context);
                  _openWhatsApp(supplier.phoneNumber!);
                },
              ),
              const SizedBox(height: 12),
            ],

            if (supplier.email.isNotEmpty) ...[
              _buildContactOption(
                icon: Icons.email,
                title: 'Email',
                subtitle: supplier.email,
                color: Colors.blue,
                onTap: () {
                  Navigator.pop(context);
                  _sendEmail(supplier.email);
                },
              ),
              const SizedBox(height: 12),
            ],

            _buildContactOption(
              icon: Icons.info_outline,
              title: 'View Details',
              subtitle: 'See full profile and services',
              color: Colors.orange,
              onTap: () {
                Navigator.pop(context);
                // Navigate to supplier details screen
                context.go('/buyer/supplier/${supplier.id}');
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildContactOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  void _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Calling $phoneNumber...'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        throw 'Could not launch phone dialer';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not make phone call: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Copy Number',
              textColor: Colors.white,
              onPressed: () => _copyToClipboard(phoneNumber),
            ),
          ),
        );
      }
    }
  }

  void _openWhatsApp(String phoneNumber) async {
    final String message =
        'Hi, I found your ${_categoryModel?.displayName ?? 'service'} on the app and would like to know more about your services.';

    // Format phone number (remove any non-digits and ensure it starts with country code)
    String formattedNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    if (formattedNumber.startsWith('0')) {
      formattedNumber = '91${formattedNumber.substring(1)}'; // Indian numbers
    } else if (!formattedNumber.startsWith('91') &&
        formattedNumber.length == 10) {
      formattedNumber = '91$formattedNumber'; // Add India country code
    }

    final Uri whatsappUri = Uri.parse(
      'https://wa.me/$formattedNumber?text=${Uri.encodeComponent(message)}',
    );

    try {
      // Try to launch WhatsApp with message
      if (await canLaunchUrl(whatsappUri)) {
        await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);

        // Show success feedback
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Opening WhatsApp for $phoneNumber...'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        // Fallback: Try opening WhatsApp without message
        final fallbackUri = Uri.parse('https://wa.me/$formattedNumber');
        if (await canLaunchUrl(fallbackUri)) {
          await launchUrl(fallbackUri, mode: LaunchMode.externalApplication);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text(
                  'WhatsApp opened. Please send your message manually.',
                ),
                backgroundColor: Colors.orange,
                action: SnackBarAction(
                  label: 'Copy Message',
                  textColor: Colors.white,
                  onPressed: () => _copyToClipboard(message),
                ),
              ),
            );
          }
        } else {
          throw 'WhatsApp not installed or not available';
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open WhatsApp. Error: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Copy Number',
              textColor: Colors.white,
              onPressed: () => _copyToClipboard(phoneNumber),
            ),
          ),
        );
      }
    }
  }

  void _sendEmail(String email) async {
    final String subject =
        'Inquiry about ${_categoryModel?.displayName ?? 'your services'}';
    final String body =
        'Hi,\n\nI found your services on the app and would like to know more about your offerings.\n\nThank you!';
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query:
          'subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Opening email for $email...'),
              backgroundColor: Colors.blue,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        throw 'Could not launch email app';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open email app: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Copy Email',
              textColor: Colors.white,
              onPressed: () => _copyToClipboard(email),
            ),
          ),
        );
      }
    }
  }

  /// Copy text to clipboard with user feedback
  void _copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Copied to clipboard: $text'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to copy: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
