// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get inventory => 'Inventory';

  @override
  String get unitPrice => 'Unit Price';

  @override
  String get minStock => 'Minimum Stock';

  @override
  String get addStock => 'Add Stock';

  @override
  String get removeStock => 'Remove Stock';

  @override
  String get setLowStockAlert => 'Set Low Stock Alert';

  @override
  String get lowStockAlert => 'Low Stock Alert';

  @override
  String get retry => 'Retry';

  @override
  String get error => 'Error';

  @override
  String get loading => 'Loading...';

  @override
  String get noItems => 'No items found';

  @override
  String get stock => 'Stock';

  @override
  String get currentStock => 'Current Stock';

  @override
  String get maximumStock => 'Maximum Stock';

  @override
  String get minimumStock => 'Minimum Stock';

  @override
  String get unit => 'Unit';

  @override
  String get productId => 'Product ID';

  @override
  String itemsRunningLow(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count items running low',
      one: '1 item running low',
      zero: 'No items running low',
    );
    return '$_temp0';
  }

  @override
  String get defaultAlarm => 'Default Alarm';

  @override
  String get time => 'Time';

  @override
  String get tone => 'Tone';

  @override
  String get volume => 'Volume';

  @override
  String get vibration => 'Vibration';

  @override
  String get snoozeSettings => 'Snooze Settings';

  @override
  String get snoozeDuration => 'Snooze Duration';

  @override
  String get maxSnoozeCount => 'Max Snooze Count';

  @override
  String get minutes => 'minutes';

  @override
  String get save => 'Save';

  @override
  String get alarmSaved => 'Alarm saved successfully';

  @override
  String get errorSavingAlarm => 'Error saving alarm';
}
