import 'package:flutter/material.dart';
import '../../../shared/models/executor.dart';

class ExecutorListItem extends StatelessWidget {
  final Executor executor;
  final bool isSelected;
  final VoidCallback onSelect;
  final VoidCallback onStatusTap;
  final VoidCallback onPerformanceTap;
  final VoidCallback onDeleteTap;

  const ExecutorListItem({
    super.key,
    required this.executor,
    required this.isSelected,
    required this.onSelect,
    required this.onStatusTap,
    required this.onPerformanceTap,
    required this.onDeleteTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar
                CircleAvatar(
                  radius: 24,
                  backgroundImage: executor.profileImage != null
                      ? NetworkImage(executor.profileImage!)
                      : null,
                  child: executor.profileImage == null
                      ? Text(executor.name[0].toUpperCase(), style: const TextStyle(fontSize: 20))
                      : null,
                ),
                const SizedBox(width: 12),

                // Main info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name and email
                      Text(
                        executor.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        executor.email,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),

                      // Phone
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.phone, size: 14, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            executor.phone,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ),

                      // Address
                      if (executor.address != null) ...[
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.location_on, size: 14, color: Colors.grey),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                executor.address!,
                                style: const TextStyle(fontSize: 14),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],

                      // Role
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.work, size: 14, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            'Role: ${executor.role}',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ),

                      // Status
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getStatusColor(executor.status).withAlpha(25),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: _getStatusColor(executor.status)),
                        ),
                        child: Text(
                          executor.status.toUpperCase(),
                          style: TextStyle(
                            color: _getStatusColor(executor.status),
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Action buttons
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Status update button
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.blue),
                      tooltip: 'Update Status',
                      onPressed: onStatusTap,
                    ),

                    // Performance button
                    IconButton(
                      icon: const Icon(Icons.analytics, color: Colors.green),
                      tooltip: 'Update Performance',
                      onPressed: onPerformanceTap,
                    ),

                    // Delete button
                    IconButton(
                      icon: const Icon(Icons.delete_outline, color: Colors.red),
                      tooltip: 'Delete',
                      onPressed: onDeleteTap,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      case 'suspended':
        return Colors.deepOrange;
      default:
        return Colors.grey;
    }
  }
}

class _StatusDialog extends StatefulWidget {
  final String currentStatus;

  const _StatusDialog({required this.currentStatus});

  @override
  State<_StatusDialog> createState() => _StatusDialogState();
}

class _StatusDialogState extends State<_StatusDialog> {
  late String _selectedStatus;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.currentStatus;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Update Status'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          RadioListTile<String>(
            title: const Text('Active'),
            value: 'active',
            groupValue: _selectedStatus,
            onChanged: (value) => setState(() => _selectedStatus = value!),
          ),
          RadioListTile<String>(
            title: const Text('Inactive'),
            value: 'inactive',
            groupValue: _selectedStatus,
            onChanged: (value) => setState(() => _selectedStatus = value!),
          ),
          RadioListTile<String>(
            title: const Text('Pending'),
            value: 'pending',
            groupValue: _selectedStatus,
            onChanged: (value) => setState(() => _selectedStatus = value!),
          ),
          RadioListTile<String>(
            title: const Text('Suspended'),
            value: 'suspended',
            groupValue: _selectedStatus,
            onChanged: (value) => setState(() => _selectedStatus = value!),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(_selectedStatus),
          child: const Text('Update'),
        ),
      ],
    );
  }
}

class _PerformanceDialog extends StatefulWidget {
  const _PerformanceDialog();

  @override
  State<_PerformanceDialog> createState() => _PerformanceDialogState();
}

class _PerformanceDialogState extends State<_PerformanceDialog> {
  late final TextEditingController _ratingController;
  late final TextEditingController _completedTasksController;
  late final TextEditingController _successRateController;

  @override
  void initState() {
    super.initState();
    _ratingController = TextEditingController(text: '0.0');
    _completedTasksController = TextEditingController(text: '0');
    _successRateController = TextEditingController(text: '0.0');
  }

  @override
  void dispose() {
    _ratingController.dispose();
    _completedTasksController.dispose();
    _successRateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Update Performance'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _ratingController,
            decoration: const InputDecoration(
              labelText: 'Rating (0-5)',
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _completedTasksController,
            decoration: const InputDecoration(
              labelText: 'Completed Tasks',
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _successRateController,
            decoration: const InputDecoration(
              labelText: 'Success Rate (%)',
            ),
            keyboardType: TextInputType.number,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            final metrics = {
              'rating': double.tryParse(_ratingController.text) ?? 0.0,
              'completedTasks':
                  int.tryParse(_completedTasksController.text) ?? 0,
              'successRate':
                  double.tryParse(_successRateController.text) ?? 0.0,
            };
            Navigator.of(context).pop(metrics);
          },
          child: const Text('Update'),
        ),
      ],
    );
  }
}
