// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alarm_settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_NotificationSettings _$NotificationSettingsFromJson(
  Map<String, dynamic> json,
) => _NotificationSettings(
  sound: json['sound'] as bool? ?? true,
  vibration: json['vibration'] as bool? ?? true,
  led: json['led'] as bool? ?? true,
);

Map<String, dynamic> _$NotificationSettingsToJson(
  _NotificationSettings instance,
) => <String, dynamic>{
  'sound': instance.sound,
  'vibration': instance.vibration,
  'led': instance.led,
};

_AlarmSettingsModel _$AlarmSettingsModelFromJson(Map<String, dynamic> json) =>
    _AlarmSettingsModel(
      defaultTimeString: json['defaultTimeString'] as String? ?? '06:00',
      snoozeDuration: (json['snoozeDuration'] as num?)?.toInt() ?? 5,
      volume: (json['volume'] as num?)?.toDouble() ?? 1.0,
      vibrate: json['vibrate'] as bool? ?? true,
      aiEnabled: json['aiEnabled'] as bool? ?? false,
      notificationSettings: json['notificationSettings'] == null
          ? const NotificationSettings()
          : NotificationSettings.fromJson(
              json['notificationSettings'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$AlarmSettingsModelToJson(_AlarmSettingsModel instance) =>
    <String, dynamic>{
      'defaultTimeString': instance.defaultTimeString,
      'snoozeDuration': instance.snoozeDuration,
      'volume': instance.volume,
      'vibrate': instance.vibrate,
      'aiEnabled': instance.aiEnabled,
      'notificationSettings': instance.notificationSettings.toJson(),
    };
