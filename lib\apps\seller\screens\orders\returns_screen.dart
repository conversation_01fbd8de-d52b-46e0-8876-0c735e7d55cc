import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/shared/models/order/order_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/order_cubit.dart';
import 'package:shivish/apps/seller/screens/orders/widgets/order_filters.dart';
import 'package:shivish/apps/seller/screens/orders/widgets/order_list_item.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';
import 'package:shivish/apps/seller/screens/orders/order_details_screen.dart';

class ReturnsScreen extends StatefulWidget {
  const ReturnsScreen({super.key});

  @override
  State<ReturnsScreen> createState() => _ReturnsScreenState();
}

class _ReturnsScreenState extends State<ReturnsScreen> {
  @override
  void initState() {
    super.initState();
    context.read<OrderCubit>().setStatusFilter(OrderStatus.refunded);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Returns Management'),
      ),
      body: BlocBuilder<OrderCubit, OrderState>(
        builder: (context, state) {
          if (state.isLoading && state.orders.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.hasError) {
            return Center(
              child: ErrorMessage(
                message: state.errorMessage ?? 'An error occurred',
              ),
            );
          }

          return Column(
            children: [
              _buildReturnAnalytics(state.orders),
              const OrderFilters(),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await context.read<OrderCubit>().loadOrders(refresh: true);
                  },
                  child: state.orders.isEmpty
                      ? const Center(
                          child: Text('No returns found'),
                        )
                      : ListView.builder(
                          itemCount: state.orders.length + 1,
                          itemBuilder: (context, index) {
                            if (index == state.orders.length) {
                              if (state.isLoading) {
                                return const Center(
                                  child: Padding(
                                    padding: EdgeInsets.all(16),
                                    child: CircularProgressIndicator(),
                                  ),
                                );
                              }
                              return const SizedBox.shrink();
                            }

                            final order = state.orders[index];
                            return OrderListItem(
                              order: order,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => OrderDetailsScreen(
                                      orderId: order.id,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildReturnAnalytics(List<OrderModel> returns) {
    final totalReturns = returns.length;
    final totalRefundAmount =
        returns.fold<double>(0, (sum, order) => sum + order.total);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Return Analytics',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildAnalyticItem(
                    context,
                    'Total Returns',
                    totalReturns.toString(),
                    Icons.assignment_return,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildAnalyticItem(
                    context,
                    'Total Refunds',
                    CurrencyFormatter.format(totalRefundAmount),
                    Icons.payments,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16),
            const SizedBox(width: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium,
        ),
      ],
    );
  }
}
