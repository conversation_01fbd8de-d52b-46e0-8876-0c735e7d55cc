import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/apps/admin/models/user.dart';
import 'package:shivish/shared/models/user/user_model.dart';

part 'user_management_event.freezed.dart';

@freezed
sealed class UserManagementEvent with _$UserManagementEvent {
  const factory UserManagementEvent.loadUsers() = LoadUsers;
  const factory UserManagementEvent.addUser(User user) = AddUser;
  const factory UserManagementEvent.updateUser(User user) = UpdateUser;
  const factory UserManagementEvent.deleteUser(String userId) = DeleteUser;
  const factory UserManagementEvent.filterUsers({
    UserRole? role,
    UserStatus? status,
    bool? isActive,
  }) = FilterUsers;
}
