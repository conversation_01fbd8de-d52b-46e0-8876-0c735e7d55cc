import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/product/product_model.dart';
import '../repositories/product_repository.dart';
import '../providers/product_provider.dart';
import 'product_state.dart';

/// Featured products provider
final featuredProductsProvider =
    StateNotifierProvider<ProductNotifier, ProductState>((ref) {
  final productRepository = ref.watch(productRepositoryProvider);
  return ProductNotifier(productRepository)..loadFeaturedProducts();
});

/// Latest products provider
final latestProductsProvider =
    StateNotifierProvider<ProductNotifier, ProductState>((ref) {
  final productRepository = ref.watch(productRepositoryProvider);
  return ProductNotifier(productRepository)..loadLatestProducts();
});

/// Search products provider
final searchProductsProvider =
    StateNotifierProvider<ProductNotifier, ProductState>((ref) {
  final productRepository = ref.watch(productRepositoryProvider);
  return ProductNotifier(productRepository);
});

/// Product notifier
class ProductNotifier extends StateNotifier<ProductState> {
  /// Constructor
  ProductNotifier(this._productRepository)
      : super(const ProductState.initial());

  final ProductRepository _productRepository;

  /// Load featured products
  Future<void> loadFeaturedProducts() async {
    state = const ProductState.loading();
    try {
      final products = await _productRepository.getFeaturedProducts();
      state = ProductState.loaded(products);
    } catch (e) {
      state = ProductState.error(e.toString());
    }
  }

  /// Load latest products
  Future<void> loadLatestProducts() async {
    state = const ProductState.loading();
    try {
      final products = await _productRepository.getLatestProducts();
      state = ProductState.loaded(products);
    } catch (e) {
      state = ProductState.error(e.toString());
    }
  }

  /// Load products by category
  Future<void> loadProductsByCategory(String category) async {
    state = const ProductState.loading();
    try {
      final products = await _productRepository.getProductsByCategory(category);
      state = ProductState.loaded(products);
    } catch (e) {
      state = ProductState.error(e.toString());
    }
  }

  /// Search products
  Future<void> searchProducts(String query) async {
    state = const ProductState.loading();
    try {
      final products = await _productRepository.searchProducts(query);
      state = ProductState.loaded(products);
    } catch (e) {
      state = ProductState.error(e.toString());
    }
  }

  /// Get product by id
  Future<ProductModel?> getProductById(String id) async {
    try {
      return await _productRepository.getProductById(id);
    } catch (e) {
      state = ProductState.error(e.toString());
      return null;
    }
  }

  /// Get products with filters
  Future<void> getProductsWithFilters({
    String? categoryId,
    String? sellerId,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
    bool? sortAscending,
  }) async {
    state = const ProductState.loading();
    try {
      final products = await _productRepository.getProductsWithFilters(
        categoryId: categoryId,
        sellerId: sellerId,
        minPrice: minPrice,
        maxPrice: maxPrice,
        sortBy: sortBy,
        sortAscending: sortAscending,
      );
      state = ProductState.loaded(products);
    } catch (e) {
      state = ProductState.error(e.toString());
    }
  }

  /// Clears search
  void clearSearch() {
    state = const ProductState.initial();
  }
}

/// Product notifier provider
final productNotifierProvider =
    StateNotifierProvider<ProductNotifier, ProductState>((ref) {
  final repository = ref.watch(productRepositoryProvider);
  return ProductNotifier(repository);
});
