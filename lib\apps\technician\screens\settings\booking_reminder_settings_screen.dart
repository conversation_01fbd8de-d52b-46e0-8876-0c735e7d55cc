import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/models/technician/reminder_settings_model.dart';
import 'package:shivish/apps/technician/providers/reminder_settings_provider.dart';

class BookingReminderSettingsScreen extends ConsumerStatefulWidget {
  const BookingReminderSettingsScreen({super.key});

  @override
  ConsumerState<BookingReminderSettingsScreen> createState() =>
      _BookingReminderSettingsScreenState();
}

class _BookingReminderSettingsScreenState
    extends ConsumerState<BookingReminderSettingsScreen> {
  late bool _enableReminders;
  late bool _enableEmailReminders;
  late bool _enablePushReminders;
  late bool _enableSMSReminders;
  late int _reminderTime;
  late bool _enableCustomReminders;
  late List<CustomReminderModel> _customReminders;

  @override
  void initState() {
    super.initState();
    // Initialize with default values
    _enableReminders = true;
    _enableEmailReminders = true;
    _enablePushReminders = true;
    _enableSMSReminders = false;
    _reminderTime = 30;
    _enableCustomReminders = false;
    _customReminders = [];
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Load settings from provider
    final settings = ref.watch(reminderSettingsProvider).value;
    if (settings != null) {
      _enableReminders = settings.enableReminders;
      _enableEmailReminders = settings.enableEmailReminders;
      _enablePushReminders = settings.enablePushReminders;
      _enableSMSReminders = settings.enableSMSReminders;
      _reminderTime = settings.defaultReminderTime;
      _enableCustomReminders = settings.enableCustomReminders;
      _customReminders = settings.customReminders;
    }
  }

  Future<void> _showAddCustomReminderDialog() async {
    final result = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Custom Reminder'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Minutes before booking:'),
            Slider(
              value: 60.0,
              min: 0,
              max: 120,
              divisions: 12,
              label: '60 minutes',
              onChanged: (value) {
                // Update label
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, 60),
            child: const Text('Add'),
          ),
        ],
      ),
    );

    if (result != null) {
      setState(() {
        _customReminders.add(
          CustomReminderModel(
            minutesBefore: result,
            isEnabled: true,
          ),
        );
      });
    }
  }

  Future<void> _saveSettings() async {
    final settings = ReminderSettingsModel(
      enableReminders: _enableReminders,
      enableEmailReminders: _enableEmailReminders,
      enablePushReminders: _enablePushReminders,
      enableSMSReminders: _enableSMSReminders,
      defaultReminderTime: _reminderTime,
      enableCustomReminders: _enableCustomReminders,
      customReminders: _customReminders,
    );

    try {
      await ref
          .read(reminderSettingsProvider.notifier)
          .updateSettings(settings);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reminder settings updated successfully'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Booking Reminders',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SwitchListTile(
                      title: const Text('Enable Reminders'),
                      subtitle:
                          const Text('Receive notifications for bookings'),
                      value: _enableReminders,
                      onChanged: (value) {
                        setState(() {
                          _enableReminders = value;
                        });
                      },
                    ),
                    const Divider(),
                    SwitchListTile(
                      title: const Text('Email Reminders'),
                      subtitle:
                          const Text('Receive booking reminders via email'),
                      value: _enableEmailReminders,
                      onChanged: _enableReminders
                          ? (value) {
                              setState(() {
                                _enableEmailReminders = value;
                              });
                            }
                          : null,
                    ),
                    SwitchListTile(
                      title: const Text('Push Notifications'),
                      subtitle: const Text(
                          'Receive booking reminders via push notifications'),
                      value: _enablePushReminders,
                      onChanged: _enableReminders
                          ? (value) {
                              setState(() {
                                _enablePushReminders = value;
                              });
                            }
                          : null,
                    ),
                    SwitchListTile(
                      title: const Text('SMS Reminders'),
                      subtitle: const Text('Receive booking reminders via SMS'),
                      value: _enableSMSReminders,
                      onChanged: _enableReminders
                          ? (value) {
                              setState(() {
                                _enableSMSReminders = value;
                              });
                            }
                          : null,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Default Reminder Time',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Slider(
                      value: _reminderTime.toDouble(),
                      min: 0,
                      max: 120,
                      divisions: 12,
                      label: '$_reminderTime minutes before',
                      onChanged: (value) {
                        setState(() {
                          _reminderTime = value.round();
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SwitchListTile(
                      title: const Text('Custom Reminders'),
                      subtitle: const Text('Set up multiple reminder times'),
                      value: _enableCustomReminders,
                      onChanged: (value) {
                        setState(() {
                          _enableCustomReminders = value;
                        });
                      },
                    ),
                    if (_enableCustomReminders) ...[
                      const Divider(),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _customReminders.length,
                        itemBuilder: (context, index) {
                          final reminder = _customReminders[index];
                          return ListTile(
                            title: Text(
                                '${reminder.minutesBefore} minutes before'),
                            trailing: IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () {
                                setState(() {
                                  _customReminders.removeAt(index);
                                });
                              },
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 8),
                      AppButton(
                        onPressed: _showAddCustomReminderDialog,
                        child: const Text('Add Custom Reminder'),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            AppButton(
              onPressed: _saveSettings,
              child: const Text('Save Settings'),
            ),
          ],
        ),
      ),
    );
  }
}
