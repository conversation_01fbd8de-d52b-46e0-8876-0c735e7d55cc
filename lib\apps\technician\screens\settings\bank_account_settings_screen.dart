import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/technician/providers/technician_provider.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';
import 'package:shivish/shared/models/technician/bank_account.dart';

class BankAccountSettingsScreen extends ConsumerStatefulWidget {
  const BankAccountSettingsScreen({super.key});

  @override
  ConsumerState<BankAccountSettingsScreen> createState() =>
      _BankAccountSettingsScreenState();
}

class _BankAccountSettingsScreenState
    extends ConsumerState<BankAccountSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _accountNumberController;
  late TextEditingController _bankNameController;
  late TextEditingController _accountHolderNameController;
  late TextEditingController _ifscCodeController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    final technician = ref.read(technicianProvider).value;
    _accountNumberController = TextEditingController(
      text: technician?.bankAccount?.accountNumber ?? '',
    );
    _bankNameController = TextEditingController(
      text: technician?.bankAccount?.bankName ?? '',
    );
    _accountHolderNameController = TextEditingController(
      text: technician?.bankAccount?.accountHolderName ?? '',
    );
    _ifscCodeController = TextEditingController(
      text: technician?.bankAccount?.ifscCode ?? '',
    );
  }

  @override
  void dispose() {
    _accountNumberController.dispose();
    _bankNameController.dispose();
    _accountHolderNameController.dispose();
    _ifscCodeController.dispose();
    super.dispose();
  }

  Future<void> _updateBankAccount() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final technician = ref.read(technicianProvider).value;
      if (technician == null) {
        throw Exception('Technician not found');
      }

      final updatedBankAccount = BankAccount(
        accountNumber: _accountNumberController.text,
        bankName: _bankNameController.text,
        accountHolderName: _accountHolderNameController.text,
        ifscCode: _ifscCodeController.text,
        createdAt: technician.bankAccount?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final updatedTechnician = technician.copyWith(
        bankAccount: updatedBankAccount,
        updatedAt: DateTime.now(),
      );

      await ref
          .read(technicianProvider.notifier)
          .updateTechnician(updatedTechnician);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bank account updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating bank account: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppToolbar.simple(title: 'Bank Account Settings'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppTextField(
                controller: _accountNumberController,
                label: 'Account Number',
                prefixIcon: const Icon(Icons.account_balance),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter account number';
                  }
                  if (value.length < 8) {
                    return 'Account number must be at least 8 digits';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _bankNameController,
                label: 'Bank Name',
                prefixIcon: const Icon(Icons.business),
                textCapitalization: TextCapitalization.words,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter bank name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _accountHolderNameController,
                label: 'Account Holder Name',
                prefixIcon: const Icon(Icons.person),
                textCapitalization: TextCapitalization.words,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter account holder name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _ifscCodeController,
                label: 'IFSC Code',
                prefixIcon: const Icon(Icons.code),
                textCapitalization: TextCapitalization.characters,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter IFSC code';
                  }
                  if (!RegExp(r'^[A-Z]{4}0[A-Z0-9]{6}$').hasMatch(value)) {
                    return 'Please enter a valid IFSC code';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: AppButton(
                  onPressed: _isLoading ? null : _updateBankAccount,
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : const Text('Update Bank Account'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
