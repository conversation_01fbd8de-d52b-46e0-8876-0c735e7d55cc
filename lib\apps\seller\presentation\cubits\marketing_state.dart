import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/apps/seller/domain/models/promotion_model.dart';
import 'package:shivish/apps/seller/domain/models/campaign_model.dart';

part 'marketing_state.freezed.dart';
part 'marketing_state.g.dart';

@freezed
sealed class MarketingState with _$MarketingState {
  const factory MarketingState({
    @Default([]) List<PromotionModel> promotions,
    @Default([]) List<CampaignModel> campaigns,
    @Default(false) bool isLoading,
    String? error,
  }) = _MarketingState;

  const MarketingState._();

  bool get hasError => error != null;
  String? get errorMessage => error;

  factory MarketingState.fromJson(Map<String, dynamic> json) =>
      _$MarketingStateFromJson(json);
}
