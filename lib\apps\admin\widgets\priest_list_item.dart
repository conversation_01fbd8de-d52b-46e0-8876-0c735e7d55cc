import 'package:flutter/material.dart';
import '../../../shared/models/priest.dart';

class PriestListItem extends StatelessWidget {
  final Priest priest;
  final bool isSelected;
  final VoidCallback onSelect;
  final VoidCallback onStatusUpdate;
  final VoidCallback onVerificationUpdate;
  final VoidCallback onDelete;

  const PriestListItem({
    super.key,
    required this.priest,
    required this.isSelected,
    required this.onSelect,
    required this.onStatusUpdate,
    required this.onVerificationUpdate,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar
                CircleAvatar(
                  radius: 24,
                  backgroundImage: priest.profileImage != null
                      ? NetworkImage(priest.profileImage!)
                      : null,
                  child: priest.profileImage == null
                      ? Text(priest.name[0].toUpperCase(), style: const TextStyle(fontSize: 20))
                      : null,
                ),
                const SizedBox(width: 12),

                // Main info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name and email
                      Text(
                        priest.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        priest.email,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),

                      // Phone
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.phone, size: 14, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            priest.phone,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ),

                      // Address
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.location_on, size: 14, color: Colors.grey),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              priest.address,
                              style: const TextStyle(fontSize: 14),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),

                      // Status
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getStatusColor(priest).withAlpha(25),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: _getStatusColor(priest)),
                            ),
                            child: Text(
                              _getStatusText(priest),
                              style: TextStyle(
                                color: _getStatusColor(priest),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getVerificationColor(priest.verificationStatus).withAlpha(25),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: _getVerificationColor(priest.verificationStatus)),
                            ),
                            child: Text(
                              priest.verificationStatus,
                              style: TextStyle(
                                color: _getVerificationColor(priest.verificationStatus),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Action buttons
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Activate/Suspend button
                    if (priest.isActive)
                      IconButton(
                        icon: const Icon(Icons.pause_circle_outline, color: Colors.orange),
                        tooltip: 'Deactivate',
                        onPressed: onStatusUpdate,
                      )
                    else
                      IconButton(
                        icon: const Icon(Icons.play_circle_outline, color: Colors.green),
                        tooltip: 'Activate',
                        onPressed: onStatusUpdate,
                      ),

                    // Verification button
                    IconButton(
                      icon: Icon(
                        priest.isVerified ? Icons.verified : Icons.verified_user,
                        color: priest.isVerified ? Colors.blue : Colors.orange,
                      ),
                      tooltip: 'Verification',
                      onPressed: onVerificationUpdate,
                    ),

                    // Delete button
                    IconButton(
                      icon: const Icon(Icons.delete_outline, color: Colors.red),
                      tooltip: 'Delete',
                      onPressed: onDelete,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(Priest priest) {
    if (!priest.isActive) return Colors.red;
    return Colors.green;
  }

  String _getStatusText(Priest priest) {
    if (!priest.isActive) return 'Inactive';
    return 'Active';
  }

  Color _getVerificationColor(String status) {
    switch (status.toLowerCase()) {
      case 'verified':
        return Colors.blue;
      case 'pending':
        return Colors.orange;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
