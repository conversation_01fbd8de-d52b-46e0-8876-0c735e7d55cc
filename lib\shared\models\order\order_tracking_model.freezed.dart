// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_tracking_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OrderTrackingModel {

 String get id; String get orderId; OrderTrackingStatus get status; String get location; String? get description; DateTime get timestamp; String? get updatedBy; double? get latitude; double? get longitude; String? get estimatedDeliveryTime; DateTime get createdAt; DateTime get updatedAt;
/// Create a copy of OrderTrackingModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OrderTrackingModelCopyWith<OrderTrackingModel> get copyWith => _$OrderTrackingModelCopyWithImpl<OrderTrackingModel>(this as OrderTrackingModel, _$identity);

  /// Serializes this OrderTrackingModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OrderTrackingModel&&(identical(other.id, id) || other.id == id)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.status, status) || other.status == status)&&(identical(other.location, location) || other.location == location)&&(identical(other.description, description) || other.description == description)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.updatedBy, updatedBy) || other.updatedBy == updatedBy)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.estimatedDeliveryTime, estimatedDeliveryTime) || other.estimatedDeliveryTime == estimatedDeliveryTime)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,orderId,status,location,description,timestamp,updatedBy,latitude,longitude,estimatedDeliveryTime,createdAt,updatedAt);

@override
String toString() {
  return 'OrderTrackingModel(id: $id, orderId: $orderId, status: $status, location: $location, description: $description, timestamp: $timestamp, updatedBy: $updatedBy, latitude: $latitude, longitude: $longitude, estimatedDeliveryTime: $estimatedDeliveryTime, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $OrderTrackingModelCopyWith<$Res>  {
  factory $OrderTrackingModelCopyWith(OrderTrackingModel value, $Res Function(OrderTrackingModel) _then) = _$OrderTrackingModelCopyWithImpl;
@useResult
$Res call({
 String id, String orderId, OrderTrackingStatus status, String location, String? description, DateTime timestamp, String? updatedBy, double? latitude, double? longitude, String? estimatedDeliveryTime, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class _$OrderTrackingModelCopyWithImpl<$Res>
    implements $OrderTrackingModelCopyWith<$Res> {
  _$OrderTrackingModelCopyWithImpl(this._self, this._then);

  final OrderTrackingModel _self;
  final $Res Function(OrderTrackingModel) _then;

/// Create a copy of OrderTrackingModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? orderId = null,Object? status = null,Object? location = null,Object? description = freezed,Object? timestamp = null,Object? updatedBy = freezed,Object? latitude = freezed,Object? longitude = freezed,Object? estimatedDeliveryTime = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,orderId: null == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderTrackingStatus,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,updatedBy: freezed == updatedBy ? _self.updatedBy : updatedBy // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,estimatedDeliveryTime: freezed == estimatedDeliveryTime ? _self.estimatedDeliveryTime : estimatedDeliveryTime // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [OrderTrackingModel].
extension OrderTrackingModelPatterns on OrderTrackingModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OrderTrackingModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OrderTrackingModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OrderTrackingModel value)  $default,){
final _that = this;
switch (_that) {
case _OrderTrackingModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OrderTrackingModel value)?  $default,){
final _that = this;
switch (_that) {
case _OrderTrackingModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String orderId,  OrderTrackingStatus status,  String location,  String? description,  DateTime timestamp,  String? updatedBy,  double? latitude,  double? longitude,  String? estimatedDeliveryTime,  DateTime createdAt,  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OrderTrackingModel() when $default != null:
return $default(_that.id,_that.orderId,_that.status,_that.location,_that.description,_that.timestamp,_that.updatedBy,_that.latitude,_that.longitude,_that.estimatedDeliveryTime,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String orderId,  OrderTrackingStatus status,  String location,  String? description,  DateTime timestamp,  String? updatedBy,  double? latitude,  double? longitude,  String? estimatedDeliveryTime,  DateTime createdAt,  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _OrderTrackingModel():
return $default(_that.id,_that.orderId,_that.status,_that.location,_that.description,_that.timestamp,_that.updatedBy,_that.latitude,_that.longitude,_that.estimatedDeliveryTime,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String orderId,  OrderTrackingStatus status,  String location,  String? description,  DateTime timestamp,  String? updatedBy,  double? latitude,  double? longitude,  String? estimatedDeliveryTime,  DateTime createdAt,  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _OrderTrackingModel() when $default != null:
return $default(_that.id,_that.orderId,_that.status,_that.location,_that.description,_that.timestamp,_that.updatedBy,_that.latitude,_that.longitude,_that.estimatedDeliveryTime,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _OrderTrackingModel implements OrderTrackingModel {
  const _OrderTrackingModel({required this.id, required this.orderId, required this.status, required this.location, this.description, required this.timestamp, this.updatedBy, this.latitude, this.longitude, this.estimatedDeliveryTime, required this.createdAt, required this.updatedAt});
  factory _OrderTrackingModel.fromJson(Map<String, dynamic> json) => _$OrderTrackingModelFromJson(json);

@override final  String id;
@override final  String orderId;
@override final  OrderTrackingStatus status;
@override final  String location;
@override final  String? description;
@override final  DateTime timestamp;
@override final  String? updatedBy;
@override final  double? latitude;
@override final  double? longitude;
@override final  String? estimatedDeliveryTime;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;

/// Create a copy of OrderTrackingModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OrderTrackingModelCopyWith<_OrderTrackingModel> get copyWith => __$OrderTrackingModelCopyWithImpl<_OrderTrackingModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OrderTrackingModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OrderTrackingModel&&(identical(other.id, id) || other.id == id)&&(identical(other.orderId, orderId) || other.orderId == orderId)&&(identical(other.status, status) || other.status == status)&&(identical(other.location, location) || other.location == location)&&(identical(other.description, description) || other.description == description)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.updatedBy, updatedBy) || other.updatedBy == updatedBy)&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.estimatedDeliveryTime, estimatedDeliveryTime) || other.estimatedDeliveryTime == estimatedDeliveryTime)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,orderId,status,location,description,timestamp,updatedBy,latitude,longitude,estimatedDeliveryTime,createdAt,updatedAt);

@override
String toString() {
  return 'OrderTrackingModel(id: $id, orderId: $orderId, status: $status, location: $location, description: $description, timestamp: $timestamp, updatedBy: $updatedBy, latitude: $latitude, longitude: $longitude, estimatedDeliveryTime: $estimatedDeliveryTime, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$OrderTrackingModelCopyWith<$Res> implements $OrderTrackingModelCopyWith<$Res> {
  factory _$OrderTrackingModelCopyWith(_OrderTrackingModel value, $Res Function(_OrderTrackingModel) _then) = __$OrderTrackingModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String orderId, OrderTrackingStatus status, String location, String? description, DateTime timestamp, String? updatedBy, double? latitude, double? longitude, String? estimatedDeliveryTime, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class __$OrderTrackingModelCopyWithImpl<$Res>
    implements _$OrderTrackingModelCopyWith<$Res> {
  __$OrderTrackingModelCopyWithImpl(this._self, this._then);

  final _OrderTrackingModel _self;
  final $Res Function(_OrderTrackingModel) _then;

/// Create a copy of OrderTrackingModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? orderId = null,Object? status = null,Object? location = null,Object? description = freezed,Object? timestamp = null,Object? updatedBy = freezed,Object? latitude = freezed,Object? longitude = freezed,Object? estimatedDeliveryTime = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_OrderTrackingModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,orderId: null == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OrderTrackingStatus,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,updatedBy: freezed == updatedBy ? _self.updatedBy : updatedBy // ignore: cast_nullable_to_non_nullable
as String?,latitude: freezed == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double?,longitude: freezed == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double?,estimatedDeliveryTime: freezed == estimatedDeliveryTime ? _self.estimatedDeliveryTime : estimatedDeliveryTime // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
