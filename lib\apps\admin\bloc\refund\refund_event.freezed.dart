// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refund_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$RefundEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RefundEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'RefundEvent()';
}


}

/// @nodoc
class $RefundEventCopyWith<$Res>  {
$RefundEventCopyWith(RefundEvent _, $Res Function(RefundEvent) __);
}


/// Adds pattern-matching-related methods to [RefundEvent].
extension RefundEventPatterns on RefundEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( LoadRefunds value)?  loadRefunds,TResult Function( CreateRefund value)?  createRefund,TResult Function( UpdateRefund value)?  updateRefund,TResult Function( DeleteRefund value)?  deleteRefund,TResult Function( ApproveRefund value)?  approveRefund,TResult Function( RejectRefund value)?  rejectRefund,TResult Function( ProcessRefund value)?  processRefund,TResult Function( CreateDispute value)?  createDispute,TResult Function( ResolveDispute value)?  resolveDispute,TResult Function( UpdatePartialRefund value)?  updatePartialRefund,TResult Function( UpdateRefundMethod value)?  updateRefundMethod,TResult Function( UpdateTrackingInfo value)?  updateTrackingInfo,TResult Function( AddAuditLog value)?  addAuditLog,required TResult orElse(),}){
final _that = this;
switch (_that) {
case LoadRefunds() when loadRefunds != null:
return loadRefunds(_that);case CreateRefund() when createRefund != null:
return createRefund(_that);case UpdateRefund() when updateRefund != null:
return updateRefund(_that);case DeleteRefund() when deleteRefund != null:
return deleteRefund(_that);case ApproveRefund() when approveRefund != null:
return approveRefund(_that);case RejectRefund() when rejectRefund != null:
return rejectRefund(_that);case ProcessRefund() when processRefund != null:
return processRefund(_that);case CreateDispute() when createDispute != null:
return createDispute(_that);case ResolveDispute() when resolveDispute != null:
return resolveDispute(_that);case UpdatePartialRefund() when updatePartialRefund != null:
return updatePartialRefund(_that);case UpdateRefundMethod() when updateRefundMethod != null:
return updateRefundMethod(_that);case UpdateTrackingInfo() when updateTrackingInfo != null:
return updateTrackingInfo(_that);case AddAuditLog() when addAuditLog != null:
return addAuditLog(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( LoadRefunds value)  loadRefunds,required TResult Function( CreateRefund value)  createRefund,required TResult Function( UpdateRefund value)  updateRefund,required TResult Function( DeleteRefund value)  deleteRefund,required TResult Function( ApproveRefund value)  approveRefund,required TResult Function( RejectRefund value)  rejectRefund,required TResult Function( ProcessRefund value)  processRefund,required TResult Function( CreateDispute value)  createDispute,required TResult Function( ResolveDispute value)  resolveDispute,required TResult Function( UpdatePartialRefund value)  updatePartialRefund,required TResult Function( UpdateRefundMethod value)  updateRefundMethod,required TResult Function( UpdateTrackingInfo value)  updateTrackingInfo,required TResult Function( AddAuditLog value)  addAuditLog,}){
final _that = this;
switch (_that) {
case LoadRefunds():
return loadRefunds(_that);case CreateRefund():
return createRefund(_that);case UpdateRefund():
return updateRefund(_that);case DeleteRefund():
return deleteRefund(_that);case ApproveRefund():
return approveRefund(_that);case RejectRefund():
return rejectRefund(_that);case ProcessRefund():
return processRefund(_that);case CreateDispute():
return createDispute(_that);case ResolveDispute():
return resolveDispute(_that);case UpdatePartialRefund():
return updatePartialRefund(_that);case UpdateRefundMethod():
return updateRefundMethod(_that);case UpdateTrackingInfo():
return updateTrackingInfo(_that);case AddAuditLog():
return addAuditLog(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( LoadRefunds value)?  loadRefunds,TResult? Function( CreateRefund value)?  createRefund,TResult? Function( UpdateRefund value)?  updateRefund,TResult? Function( DeleteRefund value)?  deleteRefund,TResult? Function( ApproveRefund value)?  approveRefund,TResult? Function( RejectRefund value)?  rejectRefund,TResult? Function( ProcessRefund value)?  processRefund,TResult? Function( CreateDispute value)?  createDispute,TResult? Function( ResolveDispute value)?  resolveDispute,TResult? Function( UpdatePartialRefund value)?  updatePartialRefund,TResult? Function( UpdateRefundMethod value)?  updateRefundMethod,TResult? Function( UpdateTrackingInfo value)?  updateTrackingInfo,TResult? Function( AddAuditLog value)?  addAuditLog,}){
final _that = this;
switch (_that) {
case LoadRefunds() when loadRefunds != null:
return loadRefunds(_that);case CreateRefund() when createRefund != null:
return createRefund(_that);case UpdateRefund() when updateRefund != null:
return updateRefund(_that);case DeleteRefund() when deleteRefund != null:
return deleteRefund(_that);case ApproveRefund() when approveRefund != null:
return approveRefund(_that);case RejectRefund() when rejectRefund != null:
return rejectRefund(_that);case ProcessRefund() when processRefund != null:
return processRefund(_that);case CreateDispute() when createDispute != null:
return createDispute(_that);case ResolveDispute() when resolveDispute != null:
return resolveDispute(_that);case UpdatePartialRefund() when updatePartialRefund != null:
return updatePartialRefund(_that);case UpdateRefundMethod() when updateRefundMethod != null:
return updateRefundMethod(_that);case UpdateTrackingInfo() when updateTrackingInfo != null:
return updateTrackingInfo(_that);case AddAuditLog() when addAuditLog != null:
return addAuditLog(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  loadRefunds,TResult Function( Refund refund)?  createRefund,TResult Function( Refund refund)?  updateRefund,TResult Function( String id)?  deleteRefund,TResult Function( String id,  String approvedBy)?  approveRefund,TResult Function( String id,  String rejectedBy,  String rejectionReason)?  rejectRefund,TResult Function( String id,  String processedBy,  String transactionId)?  processRefund,TResult Function( String id,  String disputeReason)?  createDispute,TResult Function( String id,  String resolvedBy,  String resolution)?  resolveDispute,TResult Function( String id,  double partialAmount,  String partialReason)?  updatePartialRefund,TResult Function( String id,  Map<String, dynamic> method)?  updateRefundMethod,TResult Function( String id,  Map<String, dynamic> tracking)?  updateTrackingInfo,TResult Function( String id,  String action,  String performedBy,  Map<String, dynamic> details)?  addAuditLog,required TResult orElse(),}) {final _that = this;
switch (_that) {
case LoadRefunds() when loadRefunds != null:
return loadRefunds();case CreateRefund() when createRefund != null:
return createRefund(_that.refund);case UpdateRefund() when updateRefund != null:
return updateRefund(_that.refund);case DeleteRefund() when deleteRefund != null:
return deleteRefund(_that.id);case ApproveRefund() when approveRefund != null:
return approveRefund(_that.id,_that.approvedBy);case RejectRefund() when rejectRefund != null:
return rejectRefund(_that.id,_that.rejectedBy,_that.rejectionReason);case ProcessRefund() when processRefund != null:
return processRefund(_that.id,_that.processedBy,_that.transactionId);case CreateDispute() when createDispute != null:
return createDispute(_that.id,_that.disputeReason);case ResolveDispute() when resolveDispute != null:
return resolveDispute(_that.id,_that.resolvedBy,_that.resolution);case UpdatePartialRefund() when updatePartialRefund != null:
return updatePartialRefund(_that.id,_that.partialAmount,_that.partialReason);case UpdateRefundMethod() when updateRefundMethod != null:
return updateRefundMethod(_that.id,_that.method);case UpdateTrackingInfo() when updateTrackingInfo != null:
return updateTrackingInfo(_that.id,_that.tracking);case AddAuditLog() when addAuditLog != null:
return addAuditLog(_that.id,_that.action,_that.performedBy,_that.details);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  loadRefunds,required TResult Function( Refund refund)  createRefund,required TResult Function( Refund refund)  updateRefund,required TResult Function( String id)  deleteRefund,required TResult Function( String id,  String approvedBy)  approveRefund,required TResult Function( String id,  String rejectedBy,  String rejectionReason)  rejectRefund,required TResult Function( String id,  String processedBy,  String transactionId)  processRefund,required TResult Function( String id,  String disputeReason)  createDispute,required TResult Function( String id,  String resolvedBy,  String resolution)  resolveDispute,required TResult Function( String id,  double partialAmount,  String partialReason)  updatePartialRefund,required TResult Function( String id,  Map<String, dynamic> method)  updateRefundMethod,required TResult Function( String id,  Map<String, dynamic> tracking)  updateTrackingInfo,required TResult Function( String id,  String action,  String performedBy,  Map<String, dynamic> details)  addAuditLog,}) {final _that = this;
switch (_that) {
case LoadRefunds():
return loadRefunds();case CreateRefund():
return createRefund(_that.refund);case UpdateRefund():
return updateRefund(_that.refund);case DeleteRefund():
return deleteRefund(_that.id);case ApproveRefund():
return approveRefund(_that.id,_that.approvedBy);case RejectRefund():
return rejectRefund(_that.id,_that.rejectedBy,_that.rejectionReason);case ProcessRefund():
return processRefund(_that.id,_that.processedBy,_that.transactionId);case CreateDispute():
return createDispute(_that.id,_that.disputeReason);case ResolveDispute():
return resolveDispute(_that.id,_that.resolvedBy,_that.resolution);case UpdatePartialRefund():
return updatePartialRefund(_that.id,_that.partialAmount,_that.partialReason);case UpdateRefundMethod():
return updateRefundMethod(_that.id,_that.method);case UpdateTrackingInfo():
return updateTrackingInfo(_that.id,_that.tracking);case AddAuditLog():
return addAuditLog(_that.id,_that.action,_that.performedBy,_that.details);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  loadRefunds,TResult? Function( Refund refund)?  createRefund,TResult? Function( Refund refund)?  updateRefund,TResult? Function( String id)?  deleteRefund,TResult? Function( String id,  String approvedBy)?  approveRefund,TResult? Function( String id,  String rejectedBy,  String rejectionReason)?  rejectRefund,TResult? Function( String id,  String processedBy,  String transactionId)?  processRefund,TResult? Function( String id,  String disputeReason)?  createDispute,TResult? Function( String id,  String resolvedBy,  String resolution)?  resolveDispute,TResult? Function( String id,  double partialAmount,  String partialReason)?  updatePartialRefund,TResult? Function( String id,  Map<String, dynamic> method)?  updateRefundMethod,TResult? Function( String id,  Map<String, dynamic> tracking)?  updateTrackingInfo,TResult? Function( String id,  String action,  String performedBy,  Map<String, dynamic> details)?  addAuditLog,}) {final _that = this;
switch (_that) {
case LoadRefunds() when loadRefunds != null:
return loadRefunds();case CreateRefund() when createRefund != null:
return createRefund(_that.refund);case UpdateRefund() when updateRefund != null:
return updateRefund(_that.refund);case DeleteRefund() when deleteRefund != null:
return deleteRefund(_that.id);case ApproveRefund() when approveRefund != null:
return approveRefund(_that.id,_that.approvedBy);case RejectRefund() when rejectRefund != null:
return rejectRefund(_that.id,_that.rejectedBy,_that.rejectionReason);case ProcessRefund() when processRefund != null:
return processRefund(_that.id,_that.processedBy,_that.transactionId);case CreateDispute() when createDispute != null:
return createDispute(_that.id,_that.disputeReason);case ResolveDispute() when resolveDispute != null:
return resolveDispute(_that.id,_that.resolvedBy,_that.resolution);case UpdatePartialRefund() when updatePartialRefund != null:
return updatePartialRefund(_that.id,_that.partialAmount,_that.partialReason);case UpdateRefundMethod() when updateRefundMethod != null:
return updateRefundMethod(_that.id,_that.method);case UpdateTrackingInfo() when updateTrackingInfo != null:
return updateTrackingInfo(_that.id,_that.tracking);case AddAuditLog() when addAuditLog != null:
return addAuditLog(_that.id,_that.action,_that.performedBy,_that.details);case _:
  return null;

}
}

}

/// @nodoc


class LoadRefunds implements RefundEvent {
  const LoadRefunds();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadRefunds);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'RefundEvent.loadRefunds()';
}


}




/// @nodoc


class CreateRefund implements RefundEvent {
  const CreateRefund(this.refund);
  

 final  Refund refund;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateRefundCopyWith<CreateRefund> get copyWith => _$CreateRefundCopyWithImpl<CreateRefund>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateRefund&&(identical(other.refund, refund) || other.refund == refund));
}


@override
int get hashCode => Object.hash(runtimeType,refund);

@override
String toString() {
  return 'RefundEvent.createRefund(refund: $refund)';
}


}

/// @nodoc
abstract mixin class $CreateRefundCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $CreateRefundCopyWith(CreateRefund value, $Res Function(CreateRefund) _then) = _$CreateRefundCopyWithImpl;
@useResult
$Res call({
 Refund refund
});


$RefundCopyWith<$Res> get refund;

}
/// @nodoc
class _$CreateRefundCopyWithImpl<$Res>
    implements $CreateRefundCopyWith<$Res> {
  _$CreateRefundCopyWithImpl(this._self, this._then);

  final CreateRefund _self;
  final $Res Function(CreateRefund) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? refund = null,}) {
  return _then(CreateRefund(
null == refund ? _self.refund : refund // ignore: cast_nullable_to_non_nullable
as Refund,
  ));
}

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RefundCopyWith<$Res> get refund {
  
  return $RefundCopyWith<$Res>(_self.refund, (value) {
    return _then(_self.copyWith(refund: value));
  });
}
}

/// @nodoc


class UpdateRefund implements RefundEvent {
  const UpdateRefund(this.refund);
  

 final  Refund refund;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateRefundCopyWith<UpdateRefund> get copyWith => _$UpdateRefundCopyWithImpl<UpdateRefund>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateRefund&&(identical(other.refund, refund) || other.refund == refund));
}


@override
int get hashCode => Object.hash(runtimeType,refund);

@override
String toString() {
  return 'RefundEvent.updateRefund(refund: $refund)';
}


}

/// @nodoc
abstract mixin class $UpdateRefundCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $UpdateRefundCopyWith(UpdateRefund value, $Res Function(UpdateRefund) _then) = _$UpdateRefundCopyWithImpl;
@useResult
$Res call({
 Refund refund
});


$RefundCopyWith<$Res> get refund;

}
/// @nodoc
class _$UpdateRefundCopyWithImpl<$Res>
    implements $UpdateRefundCopyWith<$Res> {
  _$UpdateRefundCopyWithImpl(this._self, this._then);

  final UpdateRefund _self;
  final $Res Function(UpdateRefund) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? refund = null,}) {
  return _then(UpdateRefund(
null == refund ? _self.refund : refund // ignore: cast_nullable_to_non_nullable
as Refund,
  ));
}

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RefundCopyWith<$Res> get refund {
  
  return $RefundCopyWith<$Res>(_self.refund, (value) {
    return _then(_self.copyWith(refund: value));
  });
}
}

/// @nodoc


class DeleteRefund implements RefundEvent {
  const DeleteRefund(this.id);
  

 final  String id;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DeleteRefundCopyWith<DeleteRefund> get copyWith => _$DeleteRefundCopyWithImpl<DeleteRefund>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DeleteRefund&&(identical(other.id, id) || other.id == id));
}


@override
int get hashCode => Object.hash(runtimeType,id);

@override
String toString() {
  return 'RefundEvent.deleteRefund(id: $id)';
}


}

/// @nodoc
abstract mixin class $DeleteRefundCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $DeleteRefundCopyWith(DeleteRefund value, $Res Function(DeleteRefund) _then) = _$DeleteRefundCopyWithImpl;
@useResult
$Res call({
 String id
});




}
/// @nodoc
class _$DeleteRefundCopyWithImpl<$Res>
    implements $DeleteRefundCopyWith<$Res> {
  _$DeleteRefundCopyWithImpl(this._self, this._then);

  final DeleteRefund _self;
  final $Res Function(DeleteRefund) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,}) {
  return _then(DeleteRefund(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class ApproveRefund implements RefundEvent {
  const ApproveRefund(this.id, this.approvedBy);
  

 final  String id;
 final  String approvedBy;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ApproveRefundCopyWith<ApproveRefund> get copyWith => _$ApproveRefundCopyWithImpl<ApproveRefund>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ApproveRefund&&(identical(other.id, id) || other.id == id)&&(identical(other.approvedBy, approvedBy) || other.approvedBy == approvedBy));
}


@override
int get hashCode => Object.hash(runtimeType,id,approvedBy);

@override
String toString() {
  return 'RefundEvent.approveRefund(id: $id, approvedBy: $approvedBy)';
}


}

/// @nodoc
abstract mixin class $ApproveRefundCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $ApproveRefundCopyWith(ApproveRefund value, $Res Function(ApproveRefund) _then) = _$ApproveRefundCopyWithImpl;
@useResult
$Res call({
 String id, String approvedBy
});




}
/// @nodoc
class _$ApproveRefundCopyWithImpl<$Res>
    implements $ApproveRefundCopyWith<$Res> {
  _$ApproveRefundCopyWithImpl(this._self, this._then);

  final ApproveRefund _self;
  final $Res Function(ApproveRefund) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? approvedBy = null,}) {
  return _then(ApproveRefund(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == approvedBy ? _self.approvedBy : approvedBy // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class RejectRefund implements RefundEvent {
  const RejectRefund(this.id, this.rejectedBy, this.rejectionReason);
  

 final  String id;
 final  String rejectedBy;
 final  String rejectionReason;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RejectRefundCopyWith<RejectRefund> get copyWith => _$RejectRefundCopyWithImpl<RejectRefund>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RejectRefund&&(identical(other.id, id) || other.id == id)&&(identical(other.rejectedBy, rejectedBy) || other.rejectedBy == rejectedBy)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason));
}


@override
int get hashCode => Object.hash(runtimeType,id,rejectedBy,rejectionReason);

@override
String toString() {
  return 'RefundEvent.rejectRefund(id: $id, rejectedBy: $rejectedBy, rejectionReason: $rejectionReason)';
}


}

/// @nodoc
abstract mixin class $RejectRefundCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $RejectRefundCopyWith(RejectRefund value, $Res Function(RejectRefund) _then) = _$RejectRefundCopyWithImpl;
@useResult
$Res call({
 String id, String rejectedBy, String rejectionReason
});




}
/// @nodoc
class _$RejectRefundCopyWithImpl<$Res>
    implements $RejectRefundCopyWith<$Res> {
  _$RejectRefundCopyWithImpl(this._self, this._then);

  final RejectRefund _self;
  final $Res Function(RejectRefund) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? rejectedBy = null,Object? rejectionReason = null,}) {
  return _then(RejectRefund(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == rejectedBy ? _self.rejectedBy : rejectedBy // ignore: cast_nullable_to_non_nullable
as String,null == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class ProcessRefund implements RefundEvent {
  const ProcessRefund(this.id, this.processedBy, this.transactionId);
  

 final  String id;
 final  String processedBy;
 final  String transactionId;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProcessRefundCopyWith<ProcessRefund> get copyWith => _$ProcessRefundCopyWithImpl<ProcessRefund>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProcessRefund&&(identical(other.id, id) || other.id == id)&&(identical(other.processedBy, processedBy) || other.processedBy == processedBy)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId));
}


@override
int get hashCode => Object.hash(runtimeType,id,processedBy,transactionId);

@override
String toString() {
  return 'RefundEvent.processRefund(id: $id, processedBy: $processedBy, transactionId: $transactionId)';
}


}

/// @nodoc
abstract mixin class $ProcessRefundCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $ProcessRefundCopyWith(ProcessRefund value, $Res Function(ProcessRefund) _then) = _$ProcessRefundCopyWithImpl;
@useResult
$Res call({
 String id, String processedBy, String transactionId
});




}
/// @nodoc
class _$ProcessRefundCopyWithImpl<$Res>
    implements $ProcessRefundCopyWith<$Res> {
  _$ProcessRefundCopyWithImpl(this._self, this._then);

  final ProcessRefund _self;
  final $Res Function(ProcessRefund) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? processedBy = null,Object? transactionId = null,}) {
  return _then(ProcessRefund(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == processedBy ? _self.processedBy : processedBy // ignore: cast_nullable_to_non_nullable
as String,null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class CreateDispute implements RefundEvent {
  const CreateDispute(this.id, this.disputeReason);
  

 final  String id;
 final  String disputeReason;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateDisputeCopyWith<CreateDispute> get copyWith => _$CreateDisputeCopyWithImpl<CreateDispute>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateDispute&&(identical(other.id, id) || other.id == id)&&(identical(other.disputeReason, disputeReason) || other.disputeReason == disputeReason));
}


@override
int get hashCode => Object.hash(runtimeType,id,disputeReason);

@override
String toString() {
  return 'RefundEvent.createDispute(id: $id, disputeReason: $disputeReason)';
}


}

/// @nodoc
abstract mixin class $CreateDisputeCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $CreateDisputeCopyWith(CreateDispute value, $Res Function(CreateDispute) _then) = _$CreateDisputeCopyWithImpl;
@useResult
$Res call({
 String id, String disputeReason
});




}
/// @nodoc
class _$CreateDisputeCopyWithImpl<$Res>
    implements $CreateDisputeCopyWith<$Res> {
  _$CreateDisputeCopyWithImpl(this._self, this._then);

  final CreateDispute _self;
  final $Res Function(CreateDispute) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? disputeReason = null,}) {
  return _then(CreateDispute(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == disputeReason ? _self.disputeReason : disputeReason // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class ResolveDispute implements RefundEvent {
  const ResolveDispute(this.id, this.resolvedBy, this.resolution);
  

 final  String id;
 final  String resolvedBy;
 final  String resolution;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ResolveDisputeCopyWith<ResolveDispute> get copyWith => _$ResolveDisputeCopyWithImpl<ResolveDispute>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ResolveDispute&&(identical(other.id, id) || other.id == id)&&(identical(other.resolvedBy, resolvedBy) || other.resolvedBy == resolvedBy)&&(identical(other.resolution, resolution) || other.resolution == resolution));
}


@override
int get hashCode => Object.hash(runtimeType,id,resolvedBy,resolution);

@override
String toString() {
  return 'RefundEvent.resolveDispute(id: $id, resolvedBy: $resolvedBy, resolution: $resolution)';
}


}

/// @nodoc
abstract mixin class $ResolveDisputeCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $ResolveDisputeCopyWith(ResolveDispute value, $Res Function(ResolveDispute) _then) = _$ResolveDisputeCopyWithImpl;
@useResult
$Res call({
 String id, String resolvedBy, String resolution
});




}
/// @nodoc
class _$ResolveDisputeCopyWithImpl<$Res>
    implements $ResolveDisputeCopyWith<$Res> {
  _$ResolveDisputeCopyWithImpl(this._self, this._then);

  final ResolveDispute _self;
  final $Res Function(ResolveDispute) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? resolvedBy = null,Object? resolution = null,}) {
  return _then(ResolveDispute(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == resolvedBy ? _self.resolvedBy : resolvedBy // ignore: cast_nullable_to_non_nullable
as String,null == resolution ? _self.resolution : resolution // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class UpdatePartialRefund implements RefundEvent {
  const UpdatePartialRefund(this.id, this.partialAmount, this.partialReason);
  

 final  String id;
 final  double partialAmount;
 final  String partialReason;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdatePartialRefundCopyWith<UpdatePartialRefund> get copyWith => _$UpdatePartialRefundCopyWithImpl<UpdatePartialRefund>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdatePartialRefund&&(identical(other.id, id) || other.id == id)&&(identical(other.partialAmount, partialAmount) || other.partialAmount == partialAmount)&&(identical(other.partialReason, partialReason) || other.partialReason == partialReason));
}


@override
int get hashCode => Object.hash(runtimeType,id,partialAmount,partialReason);

@override
String toString() {
  return 'RefundEvent.updatePartialRefund(id: $id, partialAmount: $partialAmount, partialReason: $partialReason)';
}


}

/// @nodoc
abstract mixin class $UpdatePartialRefundCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $UpdatePartialRefundCopyWith(UpdatePartialRefund value, $Res Function(UpdatePartialRefund) _then) = _$UpdatePartialRefundCopyWithImpl;
@useResult
$Res call({
 String id, double partialAmount, String partialReason
});




}
/// @nodoc
class _$UpdatePartialRefundCopyWithImpl<$Res>
    implements $UpdatePartialRefundCopyWith<$Res> {
  _$UpdatePartialRefundCopyWithImpl(this._self, this._then);

  final UpdatePartialRefund _self;
  final $Res Function(UpdatePartialRefund) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? partialAmount = null,Object? partialReason = null,}) {
  return _then(UpdatePartialRefund(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == partialAmount ? _self.partialAmount : partialAmount // ignore: cast_nullable_to_non_nullable
as double,null == partialReason ? _self.partialReason : partialReason // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class UpdateRefundMethod implements RefundEvent {
  const UpdateRefundMethod(this.id, final  Map<String, dynamic> method): _method = method;
  

 final  String id;
 final  Map<String, dynamic> _method;
 Map<String, dynamic> get method {
  if (_method is EqualUnmodifiableMapView) return _method;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_method);
}


/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateRefundMethodCopyWith<UpdateRefundMethod> get copyWith => _$UpdateRefundMethodCopyWithImpl<UpdateRefundMethod>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateRefundMethod&&(identical(other.id, id) || other.id == id)&&const DeepCollectionEquality().equals(other._method, _method));
}


@override
int get hashCode => Object.hash(runtimeType,id,const DeepCollectionEquality().hash(_method));

@override
String toString() {
  return 'RefundEvent.updateRefundMethod(id: $id, method: $method)';
}


}

/// @nodoc
abstract mixin class $UpdateRefundMethodCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $UpdateRefundMethodCopyWith(UpdateRefundMethod value, $Res Function(UpdateRefundMethod) _then) = _$UpdateRefundMethodCopyWithImpl;
@useResult
$Res call({
 String id, Map<String, dynamic> method
});




}
/// @nodoc
class _$UpdateRefundMethodCopyWithImpl<$Res>
    implements $UpdateRefundMethodCopyWith<$Res> {
  _$UpdateRefundMethodCopyWithImpl(this._self, this._then);

  final UpdateRefundMethod _self;
  final $Res Function(UpdateRefundMethod) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? method = null,}) {
  return _then(UpdateRefundMethod(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == method ? _self._method : method // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

/// @nodoc


class UpdateTrackingInfo implements RefundEvent {
  const UpdateTrackingInfo(this.id, final  Map<String, dynamic> tracking): _tracking = tracking;
  

 final  String id;
 final  Map<String, dynamic> _tracking;
 Map<String, dynamic> get tracking {
  if (_tracking is EqualUnmodifiableMapView) return _tracking;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_tracking);
}


/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateTrackingInfoCopyWith<UpdateTrackingInfo> get copyWith => _$UpdateTrackingInfoCopyWithImpl<UpdateTrackingInfo>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateTrackingInfo&&(identical(other.id, id) || other.id == id)&&const DeepCollectionEquality().equals(other._tracking, _tracking));
}


@override
int get hashCode => Object.hash(runtimeType,id,const DeepCollectionEquality().hash(_tracking));

@override
String toString() {
  return 'RefundEvent.updateTrackingInfo(id: $id, tracking: $tracking)';
}


}

/// @nodoc
abstract mixin class $UpdateTrackingInfoCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $UpdateTrackingInfoCopyWith(UpdateTrackingInfo value, $Res Function(UpdateTrackingInfo) _then) = _$UpdateTrackingInfoCopyWithImpl;
@useResult
$Res call({
 String id, Map<String, dynamic> tracking
});




}
/// @nodoc
class _$UpdateTrackingInfoCopyWithImpl<$Res>
    implements $UpdateTrackingInfoCopyWith<$Res> {
  _$UpdateTrackingInfoCopyWithImpl(this._self, this._then);

  final UpdateTrackingInfo _self;
  final $Res Function(UpdateTrackingInfo) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? tracking = null,}) {
  return _then(UpdateTrackingInfo(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == tracking ? _self._tracking : tracking // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

/// @nodoc


class AddAuditLog implements RefundEvent {
  const AddAuditLog(this.id, this.action, this.performedBy, final  Map<String, dynamic> details): _details = details;
  

 final  String id;
 final  String action;
 final  String performedBy;
 final  Map<String, dynamic> _details;
 Map<String, dynamic> get details {
  if (_details is EqualUnmodifiableMapView) return _details;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_details);
}


/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AddAuditLogCopyWith<AddAuditLog> get copyWith => _$AddAuditLogCopyWithImpl<AddAuditLog>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AddAuditLog&&(identical(other.id, id) || other.id == id)&&(identical(other.action, action) || other.action == action)&&(identical(other.performedBy, performedBy) || other.performedBy == performedBy)&&const DeepCollectionEquality().equals(other._details, _details));
}


@override
int get hashCode => Object.hash(runtimeType,id,action,performedBy,const DeepCollectionEquality().hash(_details));

@override
String toString() {
  return 'RefundEvent.addAuditLog(id: $id, action: $action, performedBy: $performedBy, details: $details)';
}


}

/// @nodoc
abstract mixin class $AddAuditLogCopyWith<$Res> implements $RefundEventCopyWith<$Res> {
  factory $AddAuditLogCopyWith(AddAuditLog value, $Res Function(AddAuditLog) _then) = _$AddAuditLogCopyWithImpl;
@useResult
$Res call({
 String id, String action, String performedBy, Map<String, dynamic> details
});




}
/// @nodoc
class _$AddAuditLogCopyWithImpl<$Res>
    implements $AddAuditLogCopyWith<$Res> {
  _$AddAuditLogCopyWithImpl(this._self, this._then);

  final AddAuditLog _self;
  final $Res Function(AddAuditLog) _then;

/// Create a copy of RefundEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? id = null,Object? action = null,Object? performedBy = null,Object? details = null,}) {
  return _then(AddAuditLog(
null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as String,null == performedBy ? _self.performedBy : performedBy // ignore: cast_nullable_to_non_nullable
as String,null == details ? _self._details : details // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,
  ));
}


}

// dart format on
