import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/calendar/calendar_event_model.dart';
import '../../../../shared/models/calendar/calendar_event_type.dart';
import '../../../../shared/models/calendar/calendar_event_status.dart';
import '../../../../shared/ui_components/navigation/back_button_handler.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../providers/calendar/calendar_event_provider.dart';
import '../../widgets/calendar/edit_event_dialog.dart';
import '../../widgets/calendar/create_event_dialog.dart';
import '../../buyer_routes.dart';

/// Screen for displaying calendar event details
class CalendarEventDetailsScreen extends ConsumerWidget {
  /// Creates a [CalendarEventDetailsScreen] with a pre-loaded event
  const CalendarEventDetailsScreen({
    required this.event,
    this.eventId,
    super.key,
  });

  /// Creates a [CalendarEventDetailsScreen] that loads the event from an ID
  factory CalendarEventDetailsScreen.fromId({required String eventId}) {
    return CalendarEventDetailsScreen(eventId: eventId, event: null);
  }

  /// The event to display (may be null if loading from ID)
  final CalendarEventModel? event;

  /// The ID of the event to load (may be null if event is provided directly)
  final String? eventId;

  /// Shows the create event dialog
  Future<void> _showCreateEventDialog(
      BuildContext context, WidgetRef ref, DateTime initialDate) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const CreateEventDialog(),
    );

    if (result == true) {
      // Refresh the events list
      final user = ref.read(currentUserProvider).value;
      if (user != null) {
        ref.invalidate(userCalendarEventsProvider(user.id));
        ref.invalidate(userCalendarEventsByDateRangeProvider(
            (user.id, initialDate, initialDate)));
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // If we have an eventId, use it to load the event
    // Otherwise, watch for updates to the provided event
    final eventStream = eventId != null
        ? ref.watch(calendarEventByIdProvider(eventId!))
        : ref.watch(calendarEventByIdProvider(event!.id));

    return eventStream.when(
      data: (updatedEvent) {
        // If the event was deleted, close the details screen
        if (updatedEvent == null) {
          if (context.mounted) {
            Navigator.of(context).pop();
          }
          return const SizedBox.shrink();
        }

        return BackButtonHandler(
          fallbackRoute: BuyerRoutes.calendar,
          child: Scaffold(
            appBar: AppBar(
              title: const Text('Event Details'),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  // Check if we can pop the current route
                  if (Navigator.of(context).canPop()) {
                    Navigator.of(context).pop();
                  } else {
                    // If we can't pop, go to the calendar screen
                    context.go(BuyerRoutes.calendar);
                  }
                },
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () async {
                    final result = await showDialog<bool>(
                      context: context,
                      builder: (context) =>
                          EditEventDialog(event: updatedEvent),
                    );

                    if (result == true && context.mounted) {
                      Navigator.of(context).pop();
                    }
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () async {
                    final shouldDelete = await showDialog<bool>(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Delete Event'),
                        content: const Text(
                            'Are you sure you want to delete this event?'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(false),
                            child: const Text('Cancel'),
                          ),
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(true),
                            child: const Text(
                              'Delete',
                              style: TextStyle(color: Colors.red),
                            ),
                          ),
                        ],
                      ),
                    );

                    if (shouldDelete == true) {
                      try {
                        await ref
                            .read(calendarEventServiceProvider)
                            .deleteEvent(updatedEvent.id);
                        if (context.mounted) {
                          context.go(BuyerRoutes.calendar);
                        }
                      } catch (e) {
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Failed to delete event: $e'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    }
                  },
                ),
              ],
            ),
            body: Column(
              children: [
                // Main content in scrollable area
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildEventHeader(updatedEvent),
                        const SizedBox(height: 24),
                        _buildEventDetails(updatedEvent),
                        if (updatedEvent.type ==
                            CalendarEventType.productList) ...[
                          const SizedBox(height: 24),
                          _buildProductList(updatedEvent),
                        ],
                        if (updatedEvent.type == CalendarEventType.birthday ||
                            updatedEvent.type ==
                                CalendarEventType.anniversary) ...[
                          const SizedBox(height: 24),
                          _buildContactInfo(updatedEvent),
                        ],
                        // Add some padding at the bottom for better scrolling
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
                // Persistent Create Event button at the bottom
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _showCreateEventDialog(
                          context, ref, updatedEvent.startDate),
                      icon: const Icon(Icons.add),
                      label: const Text('Create Event'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: const Color.fromARGB(255, 196, 67, 3),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stackTrace) => Scaffold(
        appBar: AppBar(
          title: const Text('Event Details'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Text('Error loading event: $error'),
        ),
      ),
    );
  }

  Widget _buildEventHeader(CalendarEventModel event) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          event.title,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildEventTypeChip(event),
            const SizedBox(width: 8),
            _buildEventStatusChip(event),
          ],
        ),
      ],
    );
  }

  Widget _buildEventDetails(CalendarEventModel event) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (event.description.isNotEmpty) ...[
          const Text(
            'Description',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(event.description),
          const SizedBox(height: 16),
        ],
        _buildDateTimeInfo(event),
        if (event.hasReminder) ...[
          const SizedBox(height: 16),
          _buildReminderInfo(event),
        ],
      ],
    );
  }

  Widget _buildEventTypeChip(CalendarEventModel event) {
    return Chip(
      label: Text(event.type.toString().split('.').last),
      backgroundColor: Colors.blue.withAlpha(50),
      labelStyle: const TextStyle(color: Colors.blue),
    );
  }

  Widget _buildEventStatusChip(CalendarEventModel event) {
    final color = switch (event.status) {
      CalendarEventStatus.scheduled => Colors.blue,
      CalendarEventStatus.completed => Colors.green,
      CalendarEventStatus.cancelled => Colors.red,
      CalendarEventStatus.postponed => Colors.orange,
    };

    return Chip(
      label: Text(event.status.toString().split('.').last),
      backgroundColor: color.withAlpha(50),
      labelStyle: TextStyle(color: color),
    );
  }

  Widget _buildContactInfo(CalendarEventModel event) {
    if (event.contactName == null && event.contactPhone == null) {
      return const SizedBox.shrink();
    }

    return Builder(builder: (context) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Contact Information',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          if (event.contactName != null)
            ListTile(
              leading: const Icon(Icons.person),
              title: const Text('Name'),
              subtitle: Text(event.contactName!),
            ),
          if (event.contactPhone != null)
            ListTile(
              leading: const Icon(Icons.phone),
              title: const Text('Phone Number'),
              subtitle: Text(event.contactPhone!),
              trailing: IconButton(
                icon: const Icon(Icons.message),
                onPressed: () {
                  // This would be implemented to send a WhatsApp message
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Sending greeting message...'),
                    ),
                  );
                },
              ),
            ),
          if (event.relationship != null)
            ListTile(
              leading: const Icon(Icons.people),
              title: const Text('Relationship'),
              subtitle: Text(event.relationship!),
            ),
          if (event.sendGreeting == true)
            Card(
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
              color: Colors.green.withAlpha(30),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Row(
                  children: [
                    const Icon(Icons.message, color: Colors.green),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Greeting Message',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            event.customGreetingMessage ??
                                'A personalized greeting will be sent on this date.',
                            style: const TextStyle(fontStyle: FontStyle.italic),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      );
    });
  }

  Widget _buildDateTimeInfo(CalendarEventModel event) {
    final startDate = event.startDate;
    final endDate = event.endDate;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Date & Time',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ListTile(
          leading: const Icon(Icons.calendar_today),
          title: const Text('Start'),
          subtitle: Text(
            '${startDate.day}/${startDate.month}/${startDate.year} '
            '${startDate.hour.toString().padLeft(2, '0')}:'
            '${startDate.minute.toString().padLeft(2, '0')}',
          ),
        ),
        ListTile(
          leading: const Icon(Icons.calendar_today),
          title: const Text('End'),
          subtitle: Text(
            '${endDate.day}/${endDate.month}/${endDate.year} '
            '${endDate.hour.toString().padLeft(2, '0')}:'
            '${endDate.minute.toString().padLeft(2, '0')}',
          ),
        ),
      ],
    );
  }

  Widget _buildReminderInfo(CalendarEventModel event) {
    final reminder = event.reminder;
    if (reminder == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Reminder',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ListTile(
          leading: const Icon(Icons.notifications),
          title: Text(reminder.type.toString().split('.').last),
          subtitle: Text('${reminder.timeBeforeEvent} minutes before'),
        ),
      ],
    );
  }

  Widget _buildProductList(CalendarEventModel event) {
    if (event.products == null || event.products!.isEmpty) {
      return const Center(
        child: Text('No products in this list'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Products',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: event.products!.length,
          itemBuilder: (context, index) {
            final product = event.products![index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: product.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: Image.network(
                          product.imageUrl!,
                          width: 56,
                          height: 56,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              const Icon(Icons.image_not_supported),
                        ),
                      )
                    : const Icon(Icons.image_not_supported),
                title: Text(product.name),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (product.description != null)
                      Text(
                        product.description!,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    const SizedBox(height: 4),
                    Text(
                      'Quantity: ${product.quantity}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                trailing: Text(
                  product.price != null
                      ? '₹${product.price!.toStringAsFixed(2)}'
                      : 'N/A',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
