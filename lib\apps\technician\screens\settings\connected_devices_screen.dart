import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/apps/technician/providers/connected_devices_provider.dart';
import 'package:shivish/shared/models/device/connected_device_model.dart';
import 'package:timeago/timeago.dart' as timeago;

class ConnectedDevicesScreen extends ConsumerWidget {
  const ConnectedDevicesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final devicesState = ref.watch(connectedDevicesProvider);

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Connected Devices',
      ),
      body: devicesState.when(
        data: (devices) => ListView(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Manage devices that are logged into your account. You can remove access for any device.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            ...devices.map((device) => _buildDeviceTile(
                  context,
                  device,
                  () => _showRemoveDeviceDialog(context, ref, device),
                )),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('Add New Device'),
              subtitle: const Text('Authorize a new device'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () => _addNewDevice(context, ref),
            ),
            ListTile(
              leading: const Icon(Icons.security),
              title: const Text('Security Settings'),
              subtitle: const Text('Configure device security options'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () => _showSecuritySettings(context, ref, devices),
            ),
          ],
        ),
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildDeviceTile(
    BuildContext context,
    ConnectedDeviceModel device,
    VoidCallback onRemove,
  ) {
    return ListTile(
      leading: Icon(_getDeviceIcon(device.type)),
      title: Row(
        children: [
          Expanded(child: Text(device.name)),
          if (device.isCurrentDevice)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Current',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
      subtitle: Text(
        'Last active: ${timeago.format(device.lastActive)}',
      ),
      trailing: device.isCurrentDevice
          ? null
          : IconButton(
              icon: const Icon(Icons.logout),
              onPressed: onRemove,
              tooltip: 'Remove device',
            ),
    );
  }

  IconData _getDeviceIcon(String type) {
    switch (type.toLowerCase()) {
      case 'android':
        return Icons.phone_android;
      case 'ios':
        return Icons.phone_iphone;
      case 'tablet':
        return Icons.tablet_mac;
      default:
        return Icons.devices;
    }
  }

  Future<void> _showRemoveDeviceDialog(
    BuildContext context,
    WidgetRef ref,
    ConnectedDeviceModel device,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Device'),
        content: Text(
          'Are you sure you want to remove ${device.name}? This will revoke access for this device.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref
            .read(connectedDevicesProvider.notifier)
            .removeDevice(device.id);
        if (context.mounted) {
          _showSnackBar(context, 'Device removed successfully');
        }
      } catch (e) {
        if (context.mounted) {
          _showSnackBar(context, 'Failed to remove device: $e');
        }
      }
    }
  }

  Future<void> _addNewDevice(BuildContext context, WidgetRef ref) async {
    try {
      await ref.read(connectedDevicesProvider.notifier).addNewDevice();
      if (context.mounted) {
        _showSnackBar(context, 'New device added successfully');
      }
    } catch (e) {
      if (context.mounted) {
        _showSnackBar(context, 'Failed to add new device: $e');
      }
    }
  }

  Future<void> _showSecuritySettings(
    BuildContext context,
    WidgetRef ref,
    List<ConnectedDeviceModel> devices,
  ) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Device Security Settings'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ...devices.map((device) => SwitchListTile(
                    title: Text(device.name),
                    subtitle:
                        Text(device.isCurrentDevice ? 'Current Device' : ''),
                    value: device.isAuthorized,
                    onChanged: device.isCurrentDevice
                        ? null
                        : (value) async {
                            try {
                              await ref
                                  .read(connectedDevicesProvider.notifier)
                                  .updateDeviceSecurity(device.id, value);
                              if (context.mounted) {
                                Navigator.pop(context);
                              }
                            } catch (e) {
                              if (context.mounted) {
                                _showSnackBar(
                                    context, 'Failed to update security: $e');
                              }
                            }
                          },
                  )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
