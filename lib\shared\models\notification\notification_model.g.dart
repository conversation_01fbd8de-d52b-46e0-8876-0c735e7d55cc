// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_NotificationModel _$NotificationModelFromJson(Map<String, dynamic> json) =>
    _NotificationModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
      status: $enumDecode(_$NotificationStatusEnumMap, json['status']),
      data: json['data'] as Map<String, dynamic>,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$NotificationModelToJson(_NotificationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'title': instance.title,
      'body': instance.body,
      'type': _$NotificationTypeEnumMap[instance.type]!,
      'status': _$NotificationStatusEnumMap[instance.status]!,
      'data': instance.data,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDeleted': instance.isDeleted,
    };

const _$NotificationTypeEnumMap = {
  NotificationType.general: 'general',
  NotificationType.order: 'order',
  NotificationType.payment: 'payment',
  NotificationType.booking: 'booking',
  NotificationType.event: 'event',
  NotificationType.chat: 'chat',
  NotificationType.system: 'system',
  NotificationType.verification: 'verification',
  NotificationType.deliveryRequest: 'delivery_request',
  NotificationType.statusUpdate: 'status_update',
  NotificationType.earnings: 'earnings',
  NotificationType.healthcareReminder: 'healthcare_reminder',
  NotificationType.dailyHoroscope: 'daily_horoscope',
  NotificationType.panchangam: 'panchangam',
  NotificationType.shoppingListAlert: 'shopping_list_alert',
  NotificationType.safetyTracking: 'safety_tracking',
  NotificationType.friendTracking: 'friend_tracking',
  NotificationType.alarmNotification: 'alarm_notification',
  NotificationType.calendarReminder: 'calendar_reminder',
  NotificationType.promotions: 'promotions',
  NotificationType.festivalReminder: 'festival_reminder',
  NotificationType.priceAlert: 'price_alert',
  NotificationType.lowStockAlert: 'low_stock_alert',
  NotificationType.rideBooking: 'ride_booking',
  NotificationType.ticketBooking: 'ticket_booking',
  NotificationType.medicineOrder: 'medicine_order',
  NotificationType.doctorConsultation: 'doctor_consultation',
  NotificationType.aiAssistant: 'ai_assistant',
  NotificationType.technicianBooking: 'technician_booking',
  NotificationType.priestBooking: 'priest_booking',
};

const _$NotificationStatusEnumMap = {
  NotificationStatus.unread: 'unread',
  NotificationStatus.read: 'read',
};
