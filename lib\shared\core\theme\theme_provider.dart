import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider extends ChangeNotifier {
  final SharedPreferences _prefs;
  static const String _themeModeKey = 'theme_mode';
  static const String _useAutoNightModeKey = 'use_auto_night_mode';
  static const String _nightModeStartHourKey = 'night_mode_start_hour';
  static const String _nightModeStartMinuteKey = 'night_mode_start_minute';
  static const String _nightModeEndHourKey = 'night_mode_end_hour';
  static const String _nightModeEndMinuteKey = 'night_mode_end_minute';
  static const String _colorSchemeKey = 'color_scheme';
  static const String _textScaleKey = 'text_scale';

  ThemeProvider(this._prefs) {
    _loadSettings();
  }

  // Theme mode
  ThemeMode _themeMode = ThemeMode.system;
  ThemeMode get themeMode => _themeMode;

  // Auto night mode
  bool _useAutoNightMode = false;
  bool get useAutoNightMode => _useAutoNightMode;

  TimeOfDay _nightModeStartTime = const TimeOfDay(hour: 20, minute: 0); // 8 PM
  TimeOfDay get nightModeStartTime => _nightModeStartTime;

  TimeOfDay _nightModeEndTime = const TimeOfDay(hour: 6, minute: 0); // 6 AM
  TimeOfDay get nightModeEndTime => _nightModeEndTime;

  // Color scheme
  String _colorScheme = 'light';
  String get colorScheme => _colorScheme;

  // Text scale
  double _textScale = 1.0;
  double get textScale => _textScale;

  // Computed properties
  bool get isDarkMode =>
      _themeMode == ThemeMode.dark ||
      (_themeMode == ThemeMode.system &&
          WidgetsBinding.instance.platformDispatcher.platformBrightness ==
              Brightness.dark) ||
      (_useAutoNightMode && _isNightTime());

  void _loadSettings() {
    // Load theme mode
    final themeModeString = _prefs.getString(_themeModeKey) ?? 'system';
    _themeMode = _getThemeMode(themeModeString);

    // Load auto night mode settings
    _useAutoNightMode = _prefs.getBool(_useAutoNightModeKey) ?? false;

    // Load night mode start time
    final startHour = _prefs.getInt(_nightModeStartHourKey) ?? 20;
    final startMinute = _prefs.getInt(_nightModeStartMinuteKey) ?? 0;
    _nightModeStartTime = TimeOfDay(hour: startHour, minute: startMinute);

    // Load night mode end time
    final endHour = _prefs.getInt(_nightModeEndHourKey) ?? 6;
    final endMinute = _prefs.getInt(_nightModeEndMinuteKey) ?? 0;
    _nightModeEndTime = TimeOfDay(hour: endHour, minute: endMinute);

    // Load color scheme
    _colorScheme = _prefs.getString(_colorSchemeKey) ?? 'light';

    // Load text scale
    _textScale = _prefs.getDouble(_textScaleKey) ?? 1.0;
  }

  ThemeMode _getThemeMode(String value) {
    switch (value) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }

  bool _isNightTime() {
    if (!_useAutoNightMode) return false;

    final now = TimeOfDay.now();
    final nowMinutes = now.hour * 60 + now.minute;
    final startMinutes =
        _nightModeStartTime.hour * 60 + _nightModeStartTime.minute;
    final endMinutes = _nightModeEndTime.hour * 60 + _nightModeEndTime.minute;

    // If start time is before end time (e.g., 20:00 to 06:00)
    if (startMinutes > endMinutes) {
      return nowMinutes >= startMinutes || nowMinutes <= endMinutes;
    }
    // If start time is after end time (unusual case, e.g., 06:00 to 20:00)
    else {
      return nowMinutes >= startMinutes && nowMinutes <= endMinutes;
    }
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      await _prefs.setString(_themeModeKey, mode.toString().split('.').last);
      notifyListeners();
    }
  }

  Future<void> setUseAutoNightMode(bool value) async {
    if (_useAutoNightMode != value) {
      _useAutoNightMode = value;
      await _prefs.setBool(_useAutoNightModeKey, value);
      notifyListeners();
    }
  }

  Future<void> setNightModeStartTime(TimeOfDay time) async {
    _nightModeStartTime = time;
    await _prefs.setInt(_nightModeStartHourKey, time.hour);
    await _prefs.setInt(_nightModeStartMinuteKey, time.minute);
    notifyListeners();
  }

  Future<void> setNightModeEndTime(TimeOfDay time) async {
    _nightModeEndTime = time;
    await _prefs.setInt(_nightModeEndHourKey, time.hour);
    await _prefs.setInt(_nightModeEndMinuteKey, time.minute);
    notifyListeners();
  }

  Future<void> setColorScheme(String scheme) async {
    if (_colorScheme != scheme) {
      _colorScheme = scheme;
      await _prefs.setString(_colorSchemeKey, scheme);
      notifyListeners();
    }
  }

  Future<void> setTextScale(double scale) async {
    if (_textScale != scale) {
      _textScale = scale;
      await _prefs.setDouble(_textScaleKey, scale);
      notifyListeners();
    }
  }
}
