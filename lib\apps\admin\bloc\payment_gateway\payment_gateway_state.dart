import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/payment_gateway.dart';

part 'payment_gateway_state.freezed.dart';

@freezed
sealed class PaymentGatewayState with _$PaymentGatewayState {
  const factory PaymentGatewayState.initial() = Initial;
  const factory PaymentGatewayState.loading() = Loading;
  const factory PaymentGatewayState.loaded(List<PaymentGateway> gateways) =
      Loaded;
  const factory PaymentGatewayState.calculated(double amount) = Calculated;
  const factory PaymentGatewayState.error(String message) = Error;
}
