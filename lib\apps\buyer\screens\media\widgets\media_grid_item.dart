import 'package:flutter/material.dart';
import '../../../../../shared/models/media/media_model.dart';

class MediaGridItem extends StatelessWidget {
  final MediaModel media;
  final VoidCallback onTap;

  const MediaGridItem({
    super.key,
    required this.media,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: _buildMediaPreview(),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    media.title,
                    style: Theme.of(context).textTheme.titleMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getMediaTypeText(),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaPreview() {
    return switch (media.type) {
      MediaType.image => Image.network(
          media.url,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => const Center(
            child: Icon(Icons.image_not_supported),
          ),
        ),
      MediaType.video => Stack(
          alignment: Alignment.center,
          children: [
            Image.network(
              media.url,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => const Center(
                child: Icon(Icons.video_library),
              ),
            ),
            const Icon(
              Icons.play_circle_outline,
              size: 48,
              color: Colors.white,
            ),
          ],
        ),
      MediaType.audio => const Center(
          child: Icon(
            Icons.audio_file,
            size: 48,
          ),
        ),
    };
  }

  String _getMediaTypeText() {
    return switch (media.type) {
      MediaType.image => 'Image',
      MediaType.video => 'Video',
      MediaType.audio => 'Audio',
    };
  }
}
