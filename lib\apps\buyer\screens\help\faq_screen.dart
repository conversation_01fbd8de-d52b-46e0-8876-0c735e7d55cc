import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class FAQScreen extends ConsumerStatefulWidget {
  const FAQScreen({super.key});

  @override
  ConsumerState<FAQScreen> createState() => _FAQScreenState();
}

class _FAQScreenState extends ConsumerState<FAQScreen> {
  final List<FAQItem> _faqs = [
    FAQItem(
      question: 'How do I place an order?',
      answer:
          'To place an order, browse our products, select the items you want, add them to your cart, and proceed to checkout. Follow the prompts to enter your shipping and payment information.',
    ),
    FAQItem(
      question: 'What payment methods do you accept?',
      answer:
          'We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers. Some regions may have additional payment options available.',
    ),
    FAQItem(
      question: 'How long does shipping take?',
      answer:
          'Shipping times vary depending on your location and the shipping method chosen. Standard shipping typically takes 3-5 business days, while express shipping can deliver in 1-2 business days.',
    ),
    FAQItem(
      question: 'What is your return policy?',
      answer:
          'We offer a 30-day return policy for most items. Products must be unused and in their original packaging. Some items may have different return policies due to their nature.',
    ),
    FAQItem(
      question: 'How do I track my order?',
      answer:
          'Once your order ships, you\'ll receive a tracking number via email. You can use this number to track your order status on our website or the shipping carrier\'s website.',
    ),
    FAQItem(
      question: 'Do you ship internationally?',
      answer:
          'Yes, we ship to most countries worldwide. Shipping costs and delivery times vary by location. Some items may have shipping restrictions based on local regulations.',
    ),
    FAQItem(
      question: 'How do I contact customer support?',
      answer:
          'You can reach our customer support team through live chat, email (<EMAIL>), or phone (+****************). We typically respond within 24 hours.',
    ),
    FAQItem(
      question: 'What is your warranty policy?',
      answer:
          'Most products come with a manufacturer\'s warranty. Warranty terms vary by product and manufacturer. Please check the product description for specific warranty details.',
    ),
    FAQItem(
      question: 'How do I change my shipping address?',
      answer:
          'You can change your shipping address in your account settings or during checkout. If you need to change the address after placing an order, contact customer support immediately.',
    ),
    FAQItem(
      question: 'Do you offer gift wrapping?',
      answer:
          'Yes, we offer gift wrapping services for most items. You can select this option during checkout. Gift wrapping includes a personalized message and festive packaging.',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Frequently Asked Questions'),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _faqs.length,
        itemBuilder: (context, index) {
          final faq = _faqs[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: ExpansionTile(
              title: Text(
                faq.question,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    faq.answer,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class FAQItem {
  final String question;
  final String answer;

  FAQItem({
    required this.question,
    required this.answer,
  });
}
