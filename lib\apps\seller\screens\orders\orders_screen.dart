import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/seller_routes.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';
import 'package:shivish/shared/utils/string_utils.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/models/order/order_model.dart';

import '../../application/seller_provider.dart';
import '../../application/orders/orders_provider.dart';

class OrdersScreen extends ConsumerStatefulWidget {
  const OrdersScreen({super.key});

  @override
  ConsumerState<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends ConsumerState<OrdersScreen> {
  String _selectedStatusFilter = 'all';
  String _selectedDateFilter = 'all';
  bool _showShoppingListsOnly = false;

  @override
  void initState() {
    super.initState();
    // Load orders after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadOrders();
    });
  }

  Future<void> _loadOrders() async {
    try {
      final seller = ref.read(sellerProvider).value;
      if (seller == null) {
        debugPrint('Cannot load orders: Seller is null');
        return;
      }

      // Use the ordersBySellerProvider to get only this seller's orders
      ref.read(ordersBySellerProvider(seller.id).notifier).getOrdersBySeller();
    } catch (e) {
      debugPrint('Error loading orders: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final sellerState = ref.watch(sellerProvider);

    // If seller is not loaded, show loading
    if (sellerState.isLoading || sellerState.value == null) {
      return const Scaffold(body: Center(child: LoadingIndicator()));
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Orders'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
        ],
      ),
      body: ref
          .watch(ordersBySellerProvider(sellerState.value!.id))
          .when(
            data: (orders) {
              // Apply filters to orders
              final filteredOrders = _applyFilters(orders);

              if (filteredOrders.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('No orders found'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadOrders,
                        child: const Text('Refresh'),
                      ),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: _loadOrders,
                child: ListView.builder(
                  itemCount: filteredOrders.length,
                  itemBuilder: (context, index) {
                    final order = filteredOrders[index];
                    return _buildOrderCard(context, order);
                  },
                ),
              );
            },
            loading: () => const Center(child: LoadingIndicator()),
            error: (error, stackTrace) => Center(child: Text('Error: $error')),
          ),
    );
  }

  Widget _buildOrderCard(BuildContext context, OrderModel order) {
    // Check if this is a shopping list order by looking for shoppingListId field
    final isShoppingList = order.notes?.contains('shoppingListId') == true;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () {
          // Navigate to order details
          SellerRoutes.navigateToOrderDetails(context, order.id);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      // Add an icon to distinguish between regular orders and shopping lists
                      Icon(
                        isShoppingList
                            ? Icons.shopping_basket
                            : Icons.shopping_cart,
                        size: 20,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Order #${StringUtils.safeSubstring(order.id, 0, 6)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  _buildStatusChip(order.status.toString().split('.').last),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Customer: ${order.customerName}',
                style: const TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 4),
              Text(
                'Items: ${order.items.length}',
                style: const TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total: ${CurrencyFormatter.format(order.total)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    'Payment: ${order.paymentMethod.name}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  if (isShoppingList)
                    const Text(
                      '(Shopping List)',
                      style: TextStyle(color: Colors.grey),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    final statusColor = _getStatusColor(status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withAlpha(25),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.toUpperCase(),
        style: TextStyle(
          color: statusColor,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('Filter Orders'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Status',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      _buildFilterChip('All', 'all', _selectedStatusFilter, (
                        value,
                      ) {
                        setState(() => _selectedStatusFilter = value);
                      }),
                      _buildFilterChip(
                        'Pending',
                        'pending',
                        _selectedStatusFilter,
                        (value) {
                          setState(() => _selectedStatusFilter = value);
                        },
                      ),
                      _buildFilterChip(
                        'Confirmed',
                        'confirmed',
                        _selectedStatusFilter,
                        (value) {
                          setState(() => _selectedStatusFilter = value);
                        },
                      ),
                      _buildFilterChip(
                        'Processing',
                        'processing',
                        _selectedStatusFilter,
                        (value) {
                          setState(() => _selectedStatusFilter = value);
                        },
                      ),
                      _buildFilterChip(
                        'Delivered',
                        'delivered',
                        _selectedStatusFilter,
                        (value) {
                          setState(() => _selectedStatusFilter = value);
                        },
                      ),
                      _buildFilterChip(
                        'Cancelled',
                        'cancelled',
                        _selectedStatusFilter,
                        (value) {
                          setState(() => _selectedStatusFilter = value);
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Date',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      _buildFilterChip('All', 'all', _selectedDateFilter, (
                        value,
                      ) {
                        setState(() => _selectedDateFilter = value);
                      }),
                      _buildFilterChip('Today', 'today', _selectedDateFilter, (
                        value,
                      ) {
                        setState(() => _selectedDateFilter = value);
                      }),
                      _buildFilterChip(
                        'This Week',
                        'week',
                        _selectedDateFilter,
                        (value) {
                          setState(() => _selectedDateFilter = value);
                        },
                      ),
                      _buildFilterChip(
                        'This Month',
                        'month',
                        _selectedDateFilter,
                        (value) {
                          setState(() => _selectedDateFilter = value);
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Order Type',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: const Text('Shopping Lists Only'),
                    value: _showShoppingListsOnly,
                    onChanged: (value) {
                      setState(() => _showShoppingListsOnly = value);
                    },
                    dense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  this.setState(() {});
                  Navigator.pop(context);
                  _loadOrders();
                },
                child: const Text('Apply'),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    String value,
    String selectedValue,
    Function(String) onSelected,
  ) {
    final isSelected = selectedValue == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onSelected(value),
      backgroundColor: isSelected
          ? Theme.of(context).colorScheme.primary.withAlpha(25)
          : null,
      checkmarkColor: Theme.of(context).colorScheme.primary,
    );
  }

  List<OrderModel> _applyFilters(List<OrderModel> orders) {
    return orders.where((order) {
      // Filter by status
      if (_selectedStatusFilter != 'all' &&
          order.status.toString().split('.').last != _selectedStatusFilter) {
        return false;
      }

      // Filter by date
      if (_selectedDateFilter != 'all') {
        final orderDate = order.createdAt;
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);

        if (_selectedDateFilter == 'today') {
          final orderDay = DateTime(
            orderDate.year,
            orderDate.month,
            orderDate.day,
          );
          if (orderDay != today) {
            return false;
          }
        } else if (_selectedDateFilter == 'week') {
          final weekStart = today.subtract(Duration(days: today.weekday - 1));
          if (orderDate.isBefore(weekStart)) {
            return false;
          }
        } else if (_selectedDateFilter == 'month') {
          final monthStart = DateTime(now.year, now.month, 1);
          if (orderDate.isBefore(monthStart)) {
            return false;
          }
        }
      }

      // Filter by order type (shopping list)
      if (_showShoppingListsOnly) {
        final isShoppingList = order.notes?.contains('shoppingListId') == true;
        if (!isShoppingList) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'processing':
        return Colors.purple;
      case 'inTransit':
        return Colors.indigo;
      case 'outForDelivery':
        return Colors.teal;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'refunded':
        return Colors.grey;
      case 'readyForPickup':
        return Colors.cyan;
      case 'requested':
        return Colors.deepPurple;
      case 'priced':
        return Colors.teal;
      case 'completed':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
