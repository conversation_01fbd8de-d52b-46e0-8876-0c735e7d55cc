/// Environment configuration for the application
class Environment {
  /// The current environment
  static const String _environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );

  /// Whether the app is running in development mode
  static bool get isDev => _environment == 'development';

  /// Whether the app is running in staging mode
  static bool get isStaging => _environment == 'staging';

  /// Whether the app is running in production mode
  static bool get isProd => _environment == 'production';

  /// The API URL for the current environment
  static String get apiUrl {
    switch (_environment) {
      case 'development':
        return 'http://localhost:3000';
      case 'staging':
        return 'https://staging-api.example.com';
      case 'production':
        return 'https://api.example.com';
      default:
        return 'http://localhost:3000';
    }
  }
}
