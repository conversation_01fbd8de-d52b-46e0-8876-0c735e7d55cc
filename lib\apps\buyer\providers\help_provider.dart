import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/buyer/services/help_service.dart';
import 'package:shivish/shared/models/help/help_article.dart';
import 'package:shivish/shared/models/help/help_suggestion.dart';

class HelpState {
  final String searchQuery;
  final List<Map<String, String>> faqItems;
  final List<Map<String, dynamic>> tickets;
  final bool isLoading;
  final String? error;

  const HelpState({
    this.searchQuery = '',
    this.faqItems = const [],
    this.tickets = const [],
    this.isLoading = false,
    this.error,
  });

  HelpState copyWith({
    String? searchQuery,
    List<Map<String, String>>? faqItems,
    List<Map<String, dynamic>>? tickets,
    bool? isLoading,
    String? error,
  }) {
    return HelpState(
      searchQuery: searchQuery ?? this.searchQuery,
      faqItems: faqItems ?? this.faqItems,
      tickets: tickets ?? this.tickets,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

final helpProvider = AsyncNotifierProvider<HelpNotifier, List<HelpArticle>>(() {
  return HelpNotifier();
});

class HelpNotifier extends AsyncNotifier<List<HelpArticle>> {
  @override
  Future<List<HelpArticle>> build() async {
    // Initial load of tickets
    final helpService = ref.read(helpServiceProvider);
    return helpService.getTickets();
  }

  Future<void> submitTicket(HelpArticle ticket) async {
    state = const AsyncValue.loading();
    try {
      final helpService = ref.read(helpServiceProvider);
      await helpService.submitTicket(ticket);
      // Refresh the tickets list
      state = AsyncValue.data(await helpService.getTickets());
    } catch (error, stack) {
      state = AsyncValue.error(error, stack);
    }
  }

  Future<void> updateTicket(HelpArticle ticket) async {
    state = const AsyncValue.loading();
    try {
      final helpService = ref.read(helpServiceProvider);
      // Get current tickets
      final tickets = await helpService.getTickets();

      // Find and update the ticket
      final updatedTickets = tickets.map((t) {
        if (t.id == ticket.id) {
          return ticket;
        }
        return t;
      }).toList();

      // Update tickets in service
      await helpService.updateTickets(updatedTickets);

      // Update state
      state = AsyncValue.data(updatedTickets);
    } catch (error, stack) {
      state = AsyncValue.error(error, stack);
    }
  }

  Future<void> searchArticles(String query) async {
    state = const AsyncValue.loading();
    try {
      final helpService = ref.read(helpServiceProvider);
      final articles = await helpService.searchArticles(query);
      state = AsyncValue.data(articles);
    } catch (error, stack) {
      state = AsyncValue.error(error, stack);
    }
  }

  Future<void> updateSearchQuery(String query) async {
    if (query.isEmpty) {
      // If query is empty, load all tickets
      state = const AsyncValue.loading();
      try {
        final helpService = ref.read(helpServiceProvider);
        final tickets = await helpService.getTickets();
        state = AsyncValue.data(tickets);
      } catch (error, stack) {
        state = AsyncValue.error(error, stack);
      }
    } else {
      // Search for articles matching the query
      await searchArticles(query);
    }
  }

  Future<void> openEmailSupport() async {
    try {
      final helpService = ref.read(helpServiceProvider);
      await helpService.sendEmail(
        'Support Request',
        'I need help with...',
      );
    } catch (error) {
      // Handle error if needed
      debugPrint('Error opening email: $error');
    }
  }

  Future<void> openPhoneSupport() async {
    try {
      final helpService = ref.read(helpServiceProvider);
      await helpService.initiatePhoneCall('+12345678900');
    } catch (error) {
      // Handle error if needed
      debugPrint('Error opening phone: $error');
    }
  }

  Future<void> openLiveChat() async {
    try {
      final helpService = ref.read(helpServiceProvider);
      await helpService.startLiveChat();
    } catch (error) {
      // Handle error if needed
      debugPrint('Error opening live chat: $error');
    }
  }

  Future<void> closeTicket(String ticketId) async {
    state = const AsyncValue.loading();
    try {
      final helpService = ref.read(helpServiceProvider);
      // Get current tickets
      final tickets = await helpService.getTickets();

      // Find and update the ticket
      final updatedTickets = tickets.map((ticket) {
        if (ticket.id == ticketId) {
          return ticket.copyWith(
            status: 'closed',
            updates: [
              ...?ticket.updates,
              {
                'date': DateTime.now().toString(),
                'message': 'Ticket closed by user',
                'type': 'system',
              },
            ],
          );
        }
        return ticket;
      }).toList();

      // Update tickets in service
      await helpService.updateTickets(updatedTickets);

      // Update state
      state = AsyncValue.data(updatedTickets);
    } catch (error, stack) {
      state = AsyncValue.error(error, stack);
    }
  }
}

final suggestionsProvider = StateNotifierProvider<SuggestionsNotifier,
    AsyncValue<List<HelpSuggestion>>>((ref) {
  final helpService = ref.watch(helpServiceProvider);
  return SuggestionsNotifier(helpService);
});

class SuggestionsNotifier
    extends StateNotifier<AsyncValue<List<HelpSuggestion>>> {
  final HelpService _helpService;

  SuggestionsNotifier(this._helpService) : super(const AsyncValue.loading());

  Future<void> getSuggestions(String query) async {
    state = const AsyncValue.loading();
    try {
      final suggestions = await _helpService.getSuggestions(query);
      state = AsyncValue.data(suggestions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
