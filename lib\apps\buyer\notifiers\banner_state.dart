import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/entities/banner/banner_model.dart';

part 'banner_state.freezed.dart';

/// State for banner carousel
@freezed
sealed class BannerState with _$BannerState {
  /// Initial state
  const factory BannerState.initial() = _Initial;

  /// Loading state
  const factory BannerState.loading() = _Loading;

  /// Loaded state with banners
  const factory BannerState.loaded({
    required List<BannerModel> banners,
  }) = _Loaded;

  /// Error state
  const factory BannerState.error({
    required String message,
  }) = _Error;
}
