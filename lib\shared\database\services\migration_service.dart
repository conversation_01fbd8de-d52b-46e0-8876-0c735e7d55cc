import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'database_service.dart';

/// Database Migration Service
/// Handles running SQL migrations for the hybrid database system
@singleton
class MigrationService {
  final DatabaseService _database;
  final _logger = Logger('MigrationService');

  MigrationService(this._database);

  /// Run all pending migrations
  Future<void> runMigrations() async {
    try {
      _logger.info('Starting database migrations...');
      
      // Create migrations table if it doesn't exist
      await _createMigrationsTable();
      
      // Get list of applied migrations
      final appliedMigrations = await _getAppliedMigrations();
      
      // List of migration files to run
      final migrations = [
        'create_request_tables.sql',
        'create_config_and_payment_tables.sql',
        'create_payment_tables.sql',
      ];
      
      for (final migration in migrations) {
        if (!appliedMigrations.contains(migration)) {
          await _runMigration(migration);
          await _recordMigration(migration);
          _logger.info('Applied migration: $migration');
        } else {
          _logger.info('Migration already applied: $migration');
        }
      }
      
      _logger.info('Database migrations completed successfully');
    } catch (e) {
      _logger.severe('Migration failed: $e');
      rethrow;
    }
  }

  /// Create the migrations tracking table
  Future<void> _createMigrationsTable() async {
    const sql = '''
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        migration_name VARCHAR(255) NOT NULL UNIQUE,
        applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    ''';
    
    await _database.execute(sql);
  }

  /// Get list of applied migrations
  Future<Set<String>> _getAppliedMigrations() async {
    try {
      final result = await _database.query('SELECT migration_name FROM migrations');
      return result.map((row) => row['migration_name'] as String).toSet();
    } catch (e) {
      _logger.warning('Could not get applied migrations: $e');
      return <String>{};
    }
  }

  /// Run a specific migration file
  Future<void> _runMigration(String migrationFile) async {
    try {
      // Load migration SQL from assets
      final sql = await _loadMigrationSql(migrationFile);
      
      // Split SQL into individual statements and execute them
      final statements = _splitSqlStatements(sql);
      
      for (final statement in statements) {
        if (statement.trim().isNotEmpty) {
          await _database.execute(statement);
        }
      }
    } catch (e) {
      _logger.severe('Failed to run migration $migrationFile: $e');
      rethrow;
    }
  }

  /// Load migration SQL from assets or file system
  Future<String> _loadMigrationSql(String migrationFile) async {
    try {
      // Try to load from assets first (for production)
      return await rootBundle.loadString('lib/shared/database/migrations/$migrationFile');
    } catch (e) {
      // Fallback to file system (for development)
      try {
        final file = File('lib/shared/database/migrations/$migrationFile');
        return await file.readAsString();
      } catch (fileError) {
        _logger.severe('Could not load migration file $migrationFile: $fileError');
        rethrow;
      }
    }
  }

  /// Split SQL into individual statements
  List<String> _splitSqlStatements(String sql) {
    // Simple SQL statement splitter - splits on semicolon followed by newline
    // This handles most cases but may need refinement for complex SQL
    return sql
        .split(RegExp(r';\s*\n'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty && !s.startsWith('--'))
        .toList();
  }

  /// Record that a migration has been applied
  Future<void> _recordMigration(String migrationName) async {
    await _database.execute(
      'INSERT INTO migrations (migration_name) VALUES (@param0)',
      [migrationName],
    );
  }

  /// Check if database is up to date
  Future<bool> isDatabaseUpToDate() async {
    try {
      final appliedMigrations = await _getAppliedMigrations();
      const expectedMigrations = [
        'create_request_tables.sql',
      ];
      
      return expectedMigrations.every((migration) => appliedMigrations.contains(migration));
    } catch (e) {
      _logger.warning('Could not check database status: $e');
      return false;
    }
  }

  /// Get migration status
  Future<Map<String, dynamic>> getMigrationStatus() async {
    try {
      final appliedMigrations = await _getAppliedMigrations();
      const allMigrations = [
        'create_request_tables.sql',
      ];
      
      final pendingMigrations = allMigrations
          .where((migration) => !appliedMigrations.contains(migration))
          .toList();
      
      return {
        'total_migrations': allMigrations.length,
        'applied_migrations': appliedMigrations.length,
        'pending_migrations': pendingMigrations.length,
        'is_up_to_date': pendingMigrations.isEmpty,
        'applied': appliedMigrations.toList(),
        'pending': pendingMigrations,
      };
    } catch (e) {
      _logger.severe('Could not get migration status: $e');
      return {
        'error': e.toString(),
        'is_up_to_date': false,
      };
    }
  }
}

/// Logger utility (reused from database_service.dart)
class Logger {
  final String name;
  
  Logger(this.name);
  
  void info(String message) {
    if (kDebugMode) {
      debugPrint('[$name] INFO: $message');
    }
  }
  
  void warning(String message) {
    if (kDebugMode) {
      debugPrint('[$name] WARNING: $message');
    }
  }
  
  void severe(String message) {
    if (kDebugMode) {
      debugPrint('[$name] ERROR: $message');
    }
  }
}
