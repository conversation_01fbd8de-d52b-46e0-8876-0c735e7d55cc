import 'package:freezed_annotation/freezed_annotation.dart';

part 'auth_state.freezed.dart';

/// Authentication state for the buyer app
@freezed
sealed class AuthState with _$AuthState {
  /// Initial state
  const factory AuthState.initial() = _Initial;

  /// Loading state
  const factory AuthState.loading() = _Loading;

  /// Authenticated state with user data
  const factory AuthState.authenticated({
    required String userId,
    required String email,
    String? displayName,
    String? photoUrl,
    String? phoneNumber,
  }) = _Authenticated;

  /// Error state with error message
  const factory AuthState.error(String message) = _Error;

  /// Unauthenticated state
  const factory AuthState.unauthenticated() = _Unauthenticated;
}
