import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/providers/settings_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class StorageSettingsScreen extends ConsumerWidget {
  const StorageSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final autoClearCache = ref.watch(autoClearCacheProvider);
    final setAutoClearCache =
        ref.read(autoClearCacheProvider.notifier).setAutoClearCache;
    final autoClearCacheDays = ref.watch(autoClearCacheDaysProvider);
    final setAutoClearCacheDays =
        ref.read(autoClearCacheDaysProvider.notifier).setAutoClearCacheDays;
    final saveToGallery = ref.watch(saveToGalleryProvider);
    final setSaveToGallery =
        ref.read(saveToGalleryProvider.notifier).setSaveToGallery;
    final saveToDownloads = ref.watch(saveToDownloadsProvider);
    final setSaveToDownloads =
        ref.read(saveToDownloadsProvider.notifier).setSaveToDownloads;
    final offlineMode = ref.watch(offlineModeProvider);
    final setOfflineMode =
        ref.read(offlineModeProvider.notifier).setOfflineMode;

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Storage Settings',
      ),
      body: ListView(
        children: [
          _buildStorageSection(
            context,
            'Auto Clear Cache',
            'Automatically clear app cache',
            autoClearCache,
            setAutoClearCache,
            Icons.cleaning_services,
          ),
          if (autoClearCache) ...[
            _buildDaysSection(
              context,
              'Clear Cache After',
              'Set days before clearing cache',
              autoClearCacheDays,
              setAutoClearCacheDays,
            ),
          ],
          _buildStorageSection(
            context,
            'Save to Gallery',
            'Save images to device gallery',
            saveToGallery,
            setSaveToGallery,
            Icons.photo_library,
          ),
          _buildStorageSection(
            context,
            'Save to Downloads',
            'Save files to downloads folder',
            saveToDownloads,
            setSaveToDownloads,
            Icons.download,
          ),
          _buildStorageSection(
            context,
            'Offline Mode',
            'Enable offline access to content',
            offlineMode,
            setOfflineMode,
            Icons.offline_bolt,
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.storage),
            title: const Text('Clear Cache Now'),
            subtitle: const Text('Free up storage space'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _clearCache(context),
          ),
          ListTile(
            leading: const Icon(Icons.delete),
            title: const Text('Clear All Data'),
            subtitle: const Text('Remove all app data'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _showClearDataDialog(context),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageSection(
    BuildContext context,
    String title,
    String subtitle,
    bool enabled,
    void Function(bool) setEnabled,
    IconData icon,
  ) {
    return SwitchListTile(
      secondary: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      value: enabled,
      onChanged: (value) {
        setEnabled(value);
        _showSnackBar(
          context,
          '$title ${value ? 'enabled' : 'disabled'}',
        );
      },
    );
  }

  Widget _buildDaysSection(
    BuildContext context,
    String title,
    String subtitle,
    int days,
    void Function(int) setDays,
  ) {
    return ListTile(
      leading: const Icon(Icons.calendar_today),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: DropdownButton<int>(
        value: days,
        items: const [
          DropdownMenuItem(
            value: 1,
            child: Text('1 day'),
          ),
          DropdownMenuItem(
            value: 3,
            child: Text('3 days'),
          ),
          DropdownMenuItem(
            value: 7,
            child: Text('7 days'),
          ),
          DropdownMenuItem(
            value: 14,
            child: Text('14 days'),
          ),
          DropdownMenuItem(
            value: 30,
            child: Text('30 days'),
          ),
        ],
        onChanged: (value) {
          if (value != null) {
            setDays(value);
            _showSnackBar(
              context,
              'Cache will be cleared after $value ${value == 1 ? 'day' : 'days'}',
            );
          }
        },
      ),
    );
  }

  Future<void> _clearCache(BuildContext context) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final cacheDir = Directory('${tempDir.path}/cache');

      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
        _showSnackBar(context, 'Cache cleared successfully');
      } else {
        _showSnackBar(context, 'No cache to clear');
      }
    } catch (e) {
      _showSnackBar(context, 'Failed to clear cache: ${e.toString()}');
    }
  }

  Future<void> _showClearDataDialog(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will remove all app data including settings, preferences, and offline content. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Clear All Data'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _clearAllData(context);
    }
  }

  Future<void> _clearAllData(BuildContext context) async {
    try {
      // Clear SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      // Clear app directories
      final appDir = await getApplicationDocumentsDirectory();
      final tempDir = await getTemporaryDirectory();

      await Future.wait([
        _clearDirectory(appDir),
        _clearDirectory(tempDir),
      ]);

      _showSnackBar(context, 'All data cleared successfully');
    } catch (e) {
      _showSnackBar(context, 'Failed to clear data: ${e.toString()}');
    }
  }

  Future<void> _clearDirectory(Directory directory) async {
    if (await directory.exists()) {
      final List<FileSystemEntity> entities = await directory.list().toList();
      for (var entity in entities) {
        if (entity is File) {
          await entity.delete();
        } else if (entity is Directory) {
          await _clearDirectory(entity);
          await entity.delete();
        }
      }
    }
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
