import 'package:flutter/material.dart';
import '../../domain/models/seller_category.dart';

class ServiceTypesSelector extends StatefulWidget {
  final SellerCategory selectedCategory;
  final List<String> selectedServiceTypes;
  final Function(List<String>) onServiceTypesChanged;

  const ServiceTypesSelector({
    super.key,
    required this.selectedCategory,
    required this.selectedServiceTypes,
    required this.onServiceTypesChanged,
  });

  @override
  State<ServiceTypesSelector> createState() => _ServiceTypesSelectorState();
}

class _ServiceTypesSelectorState extends State<ServiceTypesSelector> {
  late List<String> _selectedTypes;

  @override
  void initState() {
    super.initState();
    _selectedTypes = List.from(widget.selectedServiceTypes);
  }

  @override
  void didUpdateWidget(ServiceTypesSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedCategory != widget.selectedCategory) {
      // Category changed, reset selections
      _selectedTypes.clear();
      widget.onServiceTypesChanged(_selectedTypes);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.selectedCategory.isEventService) {
      return const SizedBox.shrink();
    }

    final availableTypes = widget.selectedCategory.availableServiceTypes;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.checklist,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Select Service Types',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Choose the specific services you offer in ${widget.selectedCategory.displayName}:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            
            // Service types grid
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: availableTypes.map((serviceType) {
                final isSelected = _selectedTypes.contains(serviceType);
                return FilterChip(
                  label: Text(serviceType),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedTypes.add(serviceType);
                      } else {
                        _selectedTypes.remove(serviceType);
                      }
                    });
                    widget.onServiceTypesChanged(_selectedTypes);
                  },
                  selectedColor: Theme.of(context).primaryColor.withAlpha(50),
                  checkmarkColor: Theme.of(context).primaryColor,
                  labelStyle: TextStyle(
                    color: isSelected 
                        ? Theme.of(context).primaryColor 
                        : Colors.grey[700],
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // Selection summary
            if (_selectedTypes.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(25),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withAlpha(50)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Colors.green[700],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${_selectedTypes.length} service type${_selectedTypes.length == 1 ? '' : 's'} selected',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withAlpha(25),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withAlpha(50)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning,
                      color: Colors.orange[700],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Please select at least one service type to continue',
                        style: TextStyle(
                          color: Colors.orange[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Quick selection buttons
            Row(
              children: [
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _selectedTypes = List.from(availableTypes);
                    });
                    widget.onServiceTypesChanged(_selectedTypes);
                  },
                  icon: const Icon(Icons.select_all, size: 16),
                  label: const Text('Select All'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _selectedTypes.clear();
                    });
                    widget.onServiceTypesChanged(_selectedTypes);
                  },
                  icon: const Icon(Icons.clear, size: 16),
                  label: const Text('Clear All'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
