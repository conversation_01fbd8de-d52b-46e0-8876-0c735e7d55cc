// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'panchangam_notification_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PanchangamNotificationSettings {

 bool get isEnabled; int get notificationHour; int get notificationMinute; String get language; String get location;
/// Create a copy of PanchangamNotificationSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PanchangamNotificationSettingsCopyWith<PanchangamNotificationSettings> get copyWith => _$PanchangamNotificationSettingsCopyWithImpl<PanchangamNotificationSettings>(this as PanchangamNotificationSettings, _$identity);

  /// Serializes this PanchangamNotificationSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PanchangamNotificationSettings&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.notificationHour, notificationHour) || other.notificationHour == notificationHour)&&(identical(other.notificationMinute, notificationMinute) || other.notificationMinute == notificationMinute)&&(identical(other.language, language) || other.language == language)&&(identical(other.location, location) || other.location == location));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isEnabled,notificationHour,notificationMinute,language,location);

@override
String toString() {
  return 'PanchangamNotificationSettings(isEnabled: $isEnabled, notificationHour: $notificationHour, notificationMinute: $notificationMinute, language: $language, location: $location)';
}


}

/// @nodoc
abstract mixin class $PanchangamNotificationSettingsCopyWith<$Res>  {
  factory $PanchangamNotificationSettingsCopyWith(PanchangamNotificationSettings value, $Res Function(PanchangamNotificationSettings) _then) = _$PanchangamNotificationSettingsCopyWithImpl;
@useResult
$Res call({
 bool isEnabled, int notificationHour, int notificationMinute, String language, String location
});




}
/// @nodoc
class _$PanchangamNotificationSettingsCopyWithImpl<$Res>
    implements $PanchangamNotificationSettingsCopyWith<$Res> {
  _$PanchangamNotificationSettingsCopyWithImpl(this._self, this._then);

  final PanchangamNotificationSettings _self;
  final $Res Function(PanchangamNotificationSettings) _then;

/// Create a copy of PanchangamNotificationSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isEnabled = null,Object? notificationHour = null,Object? notificationMinute = null,Object? language = null,Object? location = null,}) {
  return _then(_self.copyWith(
isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,notificationHour: null == notificationHour ? _self.notificationHour : notificationHour // ignore: cast_nullable_to_non_nullable
as int,notificationMinute: null == notificationMinute ? _self.notificationMinute : notificationMinute // ignore: cast_nullable_to_non_nullable
as int,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PanchangamNotificationSettings].
extension PanchangamNotificationSettingsPatterns on PanchangamNotificationSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PanchangamNotificationSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PanchangamNotificationSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PanchangamNotificationSettings value)  $default,){
final _that = this;
switch (_that) {
case _PanchangamNotificationSettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PanchangamNotificationSettings value)?  $default,){
final _that = this;
switch (_that) {
case _PanchangamNotificationSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isEnabled,  int notificationHour,  int notificationMinute,  String language,  String location)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PanchangamNotificationSettings() when $default != null:
return $default(_that.isEnabled,_that.notificationHour,_that.notificationMinute,_that.language,_that.location);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isEnabled,  int notificationHour,  int notificationMinute,  String language,  String location)  $default,) {final _that = this;
switch (_that) {
case _PanchangamNotificationSettings():
return $default(_that.isEnabled,_that.notificationHour,_that.notificationMinute,_that.language,_that.location);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isEnabled,  int notificationHour,  int notificationMinute,  String language,  String location)?  $default,) {final _that = this;
switch (_that) {
case _PanchangamNotificationSettings() when $default != null:
return $default(_that.isEnabled,_that.notificationHour,_that.notificationMinute,_that.language,_that.location);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PanchangamNotificationSettings implements PanchangamNotificationSettings {
  const _PanchangamNotificationSettings({this.isEnabled = true, this.notificationHour = 7, this.notificationMinute = 0, this.language = 'en', this.location = 'Mumbai'});
  factory _PanchangamNotificationSettings.fromJson(Map<String, dynamic> json) => _$PanchangamNotificationSettingsFromJson(json);

@override@JsonKey() final  bool isEnabled;
@override@JsonKey() final  int notificationHour;
@override@JsonKey() final  int notificationMinute;
@override@JsonKey() final  String language;
@override@JsonKey() final  String location;

/// Create a copy of PanchangamNotificationSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PanchangamNotificationSettingsCopyWith<_PanchangamNotificationSettings> get copyWith => __$PanchangamNotificationSettingsCopyWithImpl<_PanchangamNotificationSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PanchangamNotificationSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PanchangamNotificationSettings&&(identical(other.isEnabled, isEnabled) || other.isEnabled == isEnabled)&&(identical(other.notificationHour, notificationHour) || other.notificationHour == notificationHour)&&(identical(other.notificationMinute, notificationMinute) || other.notificationMinute == notificationMinute)&&(identical(other.language, language) || other.language == language)&&(identical(other.location, location) || other.location == location));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isEnabled,notificationHour,notificationMinute,language,location);

@override
String toString() {
  return 'PanchangamNotificationSettings(isEnabled: $isEnabled, notificationHour: $notificationHour, notificationMinute: $notificationMinute, language: $language, location: $location)';
}


}

/// @nodoc
abstract mixin class _$PanchangamNotificationSettingsCopyWith<$Res> implements $PanchangamNotificationSettingsCopyWith<$Res> {
  factory _$PanchangamNotificationSettingsCopyWith(_PanchangamNotificationSettings value, $Res Function(_PanchangamNotificationSettings) _then) = __$PanchangamNotificationSettingsCopyWithImpl;
@override @useResult
$Res call({
 bool isEnabled, int notificationHour, int notificationMinute, String language, String location
});




}
/// @nodoc
class __$PanchangamNotificationSettingsCopyWithImpl<$Res>
    implements _$PanchangamNotificationSettingsCopyWith<$Res> {
  __$PanchangamNotificationSettingsCopyWithImpl(this._self, this._then);

  final _PanchangamNotificationSettings _self;
  final $Res Function(_PanchangamNotificationSettings) _then;

/// Create a copy of PanchangamNotificationSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isEnabled = null,Object? notificationHour = null,Object? notificationMinute = null,Object? language = null,Object? location = null,}) {
  return _then(_PanchangamNotificationSettings(
isEnabled: null == isEnabled ? _self.isEnabled : isEnabled // ignore: cast_nullable_to_non_nullable
as bool,notificationHour: null == notificationHour ? _self.notificationHour : notificationHour // ignore: cast_nullable_to_non_nullable
as int,notificationMinute: null == notificationMinute ? _self.notificationMinute : notificationMinute // ignore: cast_nullable_to_non_nullable
as int,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,location: null == location ? _self.location : location // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
