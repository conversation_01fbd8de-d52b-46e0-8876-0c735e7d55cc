/// Enum for calendar event visibility
enum CalendarEventVisibility {
  /// Private event
  private,

  /// Public event
  public,

  /// Shared event
  shared,
}

/// Extension on [CalendarEventVisibility] for additional functionality
extension CalendarEventVisibilityX on CalendarEventVisibility {
  /// Get the display name for the event visibility
  String get displayName {
    switch (this) {
      case CalendarEventVisibility.public:
        return 'Public';
      case CalendarEventVisibility.private:
        return 'Private';
      case CalendarEventVisibility.shared:
        return 'Shared';
    }
  }

  /// Get the description for the event visibility
  String get description {
    switch (this) {
      case CalendarEventVisibility.public:
        return 'Visible to all users';
      case CalendarEventVisibility.private:
        return 'Only visible to you';
      case CalendarEventVisibility.shared:
        return 'Visible to a group of users';
    }
  }

  /// Get the icon for the event visibility
  String get icon {
    switch (this) {
      case CalendarEventVisibility.public:
        return 'public';
      case CalendarEventVisibility.private:
        return 'lock';
      case CalendarEventVisibility.shared:
        return 'group';
    }
  }
}
