import 'dart:developer';

/// Utility class for common validation methods
class ValidationUtils {
  /// Email validation regex pattern
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  /// Phone number validation regex pattern (Indian format)
  /// Supports both +91 and without country code
  static final RegExp _phoneRegex = RegExp(
    r'^(\+91|91)?[6-9]\d{9}$',
  );

  /// Password validation regex pattern
  /// Requires at least:
  /// - 8 characters
  /// - 1 uppercase letter
  /// - 1 lowercase letter
  /// - 1 number
  /// - 1 special character
  static final RegExp _passwordRegex = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$',
  );

  /// URL validation regex pattern
  static final RegExp _urlRegex = RegExp(
    r'^(http|https)://[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}(:[0-9]+)?(\/\S*)?$',
  );

  /// Validate email address
  static bool isValidEmail(String? email) {
    try {
      if (email == null || email.isEmpty) return false;
      return _emailRegex.hasMatch(email);
    } catch (e) {
      log('Error validating email: $e');
      return false;
    }
  }

  /// Validate phone number
  static bool isValidPhone(String? phone) {
    try {
      if (phone == null || phone.isEmpty) return false;
      return _phoneRegex.hasMatch(phone);
    } catch (e) {
      log('Error validating phone: $e');
      return false;
    }
  }

  /// Format phone number for Indian numbers
  static String formatPhoneNumber(String phone) {
    // Remove all non-digit characters
    String cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // If it starts with 91, remove it
    if (cleanPhone.startsWith('91') && cleanPhone.length == 12) {
      cleanPhone = cleanPhone.substring(2);
    }

    // Add +91 prefix if not present
    if (!cleanPhone.startsWith('+91')) {
      cleanPhone = '+91$cleanPhone';
    }

    return cleanPhone;
  }

  /// Validate and format phone number for registration
  static String? validateAndFormatPhone(String? phone) {
    if (phone == null || phone.isEmpty) {
      return 'Phone number is required';
    }

    // Clean the phone number
    String cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // Check if it's a valid Indian mobile number
    if (cleanPhone.length == 10 && cleanPhone.startsWith(RegExp(r'[6-9]'))) {
      return null; // Valid
    } else if (cleanPhone.length == 12 && cleanPhone.startsWith('91')) {
      String withoutCountryCode = cleanPhone.substring(2);
      if (withoutCountryCode.startsWith(RegExp(r'[6-9]'))) {
        return null; // Valid
      }
    }

    return 'Please enter a valid Indian mobile number (10 digits starting with 6-9)';
  }

  /// Validate password strength
  static bool isValidPassword(String? password) {
    try {
      if (password == null || password.isEmpty) return false;
      return _passwordRegex.hasMatch(password);
    } catch (e) {
      log('Error validating password: $e');
      return false;
    }
  }

  /// Validate URL format
  static bool isValidUrl(String? url) {
    try {
      if (url == null || url.isEmpty) return false;
      return _urlRegex.hasMatch(url);
    } catch (e) {
      log('Error validating URL: $e');
      return false;
    }
  }

  /// Validate number range
  static bool isInRange(num? value, num min, num max) {
    try {
      if (value == null) return false;
      return value >= min && value <= max;
    } catch (e) {
      log('Error validating range: $e');
      return false;
    }
  }

  /// Validate string length
  static bool isValidLength(String? value, int minLength, int maxLength) {
    try {
      if (value == null) return false;
      return value.length >= minLength && value.length <= maxLength;
    } catch (e) {
      log('Error validating length: $e');
      return false;
    }
  }

  /// Validate decimal places
  static bool hasValidDecimalPlaces(double? value, int maxDecimalPlaces) {
    try {
      if (value == null) return false;
      final decimalStr = value.toString().split('.');
      if (decimalStr.length != 2) return true;
      return decimalStr[1].length <= maxDecimalPlaces;
    } catch (e) {
      log('Error validating decimal places: $e');
      return false;
    }
  }

  /// Validate if string contains only letters
  static bool isAlphabetic(String? value) {
    try {
      if (value == null || value.isEmpty) return false;
      return RegExp(r'^[a-zA-Z]+$').hasMatch(value);
    } catch (e) {
      log('Error validating alphabetic string: $e');
      return false;
    }
  }

  /// Validate if string contains only numbers
  static bool isNumeric(String? value) {
    try {
      if (value == null || value.isEmpty) return false;
      return RegExp(r'^[0-9]+$').hasMatch(value);
    } catch (e) {
      log('Error validating numeric string: $e');
      return false;
    }
  }

  /// Validate if string contains only letters and numbers
  static bool isAlphanumeric(String? value) {
    try {
      if (value == null || value.isEmpty) return false;
      return RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value);
    } catch (e) {
      log('Error validating alphanumeric string: $e');
      return false;
    }
  }

  /// Get password requirements message
  static String get passwordRequirements => '''
Password must contain:
• At least 8 characters
• At least 1 uppercase letter
• At least 1 lowercase letter
• At least 1 number
• At least 1 special character (@\$!%*?&)''';

  /// Get validation error message
  static String getErrorMessage(String field, String type) {
    switch (type) {
      case 'required':
        return '$field is required';
      case 'email':
        return 'Please enter a valid email address';
      case 'phone':
        return 'Please enter a valid phone number';
      case 'password':
        return passwordRequirements;
      case 'url':
        return 'Please enter a valid URL';
      case 'length':
        return '$field length is invalid';
      case 'range':
        return '$field is out of valid range';
      case 'decimal':
        return '$field has too many decimal places';
      case 'alphabetic':
        return '$field must contain only letters';
      case 'numeric':
        return '$field must contain only numbers';
      case 'alphanumeric':
        return '$field must contain only letters and numbers';
      default:
        return 'Invalid $field';
    }
  }

  /// Sanitize string input
  static String sanitizeString(String? value) {
    try {
      if (value == null) return '';
      // Remove any HTML tags
      value = value.replaceAll(RegExp(r'<[^>]*>'), '');
      // Remove extra whitespace
      value = value.trim().replaceAll(RegExp(r'\s+'), ' ');
      return value;
    } catch (e) {
      log('Error sanitizing string: $e');
      return '';
    }
  }

  /// Sanitize numeric input
  static num? sanitizeNumber(String? value) {
    try {
      if (value == null || value.isEmpty) return null;
      // Remove any non-numeric characters except decimal point and minus sign
      final sanitized = value.replaceAll(RegExp(r'[^\d.-]'), '');
      return num.tryParse(sanitized);
    } catch (e) {
      log('Error sanitizing number: $e');
      return null;
    }
  }
}
