import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';
import '../../../../shared/services/cache/shopping_list_cache_service.dart';
import '../../../../shared/database/services/database_service.dart';
import '../../../../shared/database/config/database_config.dart';
import '../../../../shared/utils/logger.dart';
import 'dart:async';

final shoppingListServiceProvider = Provider<ShoppingListService>(
  (ref) => ShoppingListService(),
);

class ShoppingListService {
  final DatabaseService _databaseService;
  final ShoppingListCacheService _cacheService = ShoppingListCacheService();
  final _logger = getLogger('ShoppingListService');
  bool _isInitialized = false;
  static const String _shoppingListsCollection = 'shopping_lists';

  ShoppingListService({DatabaseService? databaseService})
    : _databaseService =
          databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment()) {
    _initialize();
  }

  Future<void> _initialize() async {
    if (_isInitialized) return;
    await _cacheService.initialize();
    _isInitialized = true;
  }

  Stream<ShoppingListModel?> getShoppingList(String listId) {
    // Convert to polling-based stream for hybrid storage
    late StreamController<ShoppingListModel?> controller;
    Timer? timer;

    controller = StreamController<ShoppingListModel?>(
      onListen: () {
        // Immediate fetch
        _getShoppingListOnce(listId).then((list) {
          if (!controller.isClosed) controller.add(list);
        });

        // Periodic updates
        timer = Timer.periodic(const Duration(seconds: 5), (_) async {
          try {
            final listData = await _databaseService.find(
              'shoppingLists',
              listId,
            );
            final list = listData != null
                ? ShoppingListModel.fromJson(listData)
                : null;
            if (!controller.isClosed) controller.add(list);
          } catch (e) {
            _logger.severe('Error getting shopping list: $e');
            if (!controller.isClosed) controller.add(null);
          }
        });
      },
      onCancel: () {
        timer?.cancel();
      },
    );

    return controller.stream;
  }

  Future<ShoppingListModel?> _getShoppingListOnce(String listId) async {
    try {
      final listData = await _databaseService.find('shoppingLists', listId);
      if (listData == null) return null;
      return ShoppingListModel.fromJson(listData);
    } catch (e) {
      _logger.severe('Error getting shopping list: $e');
      return null;
    }
  }

  // Get a shopping list directly (not as a stream)
  Future<ShoppingListModel?> getShoppingListDirect(String listId) async {
    try {
      // First check the cache
      ShoppingListModel? cachedList = _cacheService.getCachedShoppingList(
        listId,
      );
      if (cachedList != null) {
        return cachedList;
      }

      // If not in cache, try to get from hybrid database
      final data = await _databaseService.find(
        _shoppingListsCollection,
        listId,
      );
      if (data == null) return null;

      final list = ShoppingListModel.fromJson({'id': listId, ...data});

      // Cache the list for future use
      await _cacheService.cacheShoppingList(list);

      return list;
    } catch (e) {
      // If there's an error, return null
      return null;
    }
  }

  Stream<List<ShoppingListModel>> getUserShoppingLists(String userId) async* {
    // First, yield cached data if available
    final cachedLists = _cacheService
        .getAllCachedShoppingLists()
        .where((list) => list.createdBy == userId && list.isTemplate != true)
        .toList();

    if (cachedLists.isNotEmpty) {
      yield cachedLists;
    }

    // Check connectivity
    final isOnline = await _cacheService.isOnline();

    if (!isOnline) {
      // If offline and we already yielded cached data, we're done
      if (cachedLists.isNotEmpty) return;

      // If offline and no cached data, yield empty list
      yield [];
      return;
    }

    // If online, get data from hybrid database and update cache
    try {
      final stream = _databaseService
          .watchCollection(
            _shoppingListsCollection,
            where: 'created_by = ? AND is_template = ?',
            whereParams: [userId, false],
            orderBy: 'created_at DESC',
          )
          .map((data) {
            final lists = data.map((item) {
              return ShoppingListModel.fromJson(item);
            }).toList();

            // Update cache
            _cacheService.cacheShoppingLists(lists);

            return lists;
          });

      await for (final lists in stream) {
        yield lists;
      }
    } catch (e) {
      // If there's an error and we already yielded cached data, we're done
      if (cachedLists.isNotEmpty) return;

      // Otherwise, rethrow the error
      rethrow;
    }
  }

  Future<void> shareShoppingList(String listId, String userId) async {
    final data = await _databaseService.find(_shoppingListsCollection, listId);
    if (data == null) return;

    final list = ShoppingListModel.fromJson({'id': listId, ...data});

    if (list.sharedWith.contains(userId)) return;

    final updatedList = list.copyWith(
      isShared: true,
      sharedWith: [...list.sharedWith, userId],
      updatedAt: DateTime.now(),
    );

    await updateShoppingList(updatedList);
  }

  Future<void> unshareShoppingList(String listId, String userId) async {
    final data = await _databaseService.find(_shoppingListsCollection, listId);
    if (data == null) return;

    final list = ShoppingListModel.fromJson({'id': listId, ...data});

    if (!list.sharedWith.contains(userId)) return;

    final updatedSharedWith = list.sharedWith
        .where((id) => id != userId)
        .toList();
    final updatedList = list.copyWith(
      isShared: updatedSharedWith.isNotEmpty,
      sharedWith: updatedSharedWith,
      updatedAt: DateTime.now(),
    );

    await updateShoppingList(updatedList);
  }

  Future<void> updateUserAccessLevel(
    String listId,
    String userId,
    String accessLevel,
  ) async {
    final data = await _databaseService.find(_shoppingListsCollection, listId);
    if (data == null) return;

    final list = ShoppingListModel.fromJson({'id': listId, ...data});

    if (!list.sharedWith.contains(userId)) return;

    // In a real app, you would store the access level in a separate collection
    // or in a map of user IDs to access levels
    // For now, we'll just update the list to reflect the change
    final updatedList = list.copyWith(updatedAt: DateTime.now());

    await updateShoppingList(updatedList);
  }

  Future<void> updateShoppingList(ShoppingListModel list) async {
    try {
      // Check connectivity
      final isOnline = await _cacheService.isOnline();

      // Update cache regardless of connectivity
      if (list.isTemplate == true) {
        await _cacheService.cacheTemplate(list);
      } else if (list.status == 'completed') {
        await _cacheService.cacheHistoryItem(list);
      } else {
        await _cacheService.cacheShoppingList(list);
      }

      if (!isOnline) {
        // If offline, add to pending operations
        await _cacheService.addPendingOperation(
          'update',
          list.id,
          list.toJson(),
        );
        return;
      }

      // If online, update in hybrid database
      await _databaseService.update(
        _shoppingListsCollection,
        list.id,
        list.toJson(),
      );
    } catch (e) {
      // If there's an error, add to pending operations
      await _cacheService.addPendingOperation('update', list.id, list.toJson());
      rethrow;
    }
  }

  Future<ShoppingListModel> createShoppingList(ShoppingListModel list) async {
    try {
      // Check connectivity
      final isOnline = await _cacheService.isOnline();

      if (!isOnline) {
        // If offline, cache locally and add to pending operations
        final listWithId = list.copyWith(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
        );

        // Cache based on list type
        if (listWithId.isTemplate == true) {
          await _cacheService.cacheTemplate(listWithId);
        } else if (listWithId.status == 'completed') {
          await _cacheService.cacheHistoryItem(listWithId);
        } else {
          await _cacheService.cacheShoppingList(listWithId);
        }

        await _cacheService.addPendingOperation(
          'create',
          listWithId.id,
          listWithId.toJson(),
        );
        return listWithId;
      }

      // If online, check if the list already exists with the given ID
      if (list.id.isNotEmpty) {
        final existingData = await _databaseService.find(
          _shoppingListsCollection,
          list.id,
        );
        if (existingData != null) {
          // List already exists, update it instead of creating a new one
          await _databaseService.update(
            _shoppingListsCollection,
            list.id,
            list.toJson(),
          );

          // Get the updated document
          final updatedData = await _databaseService.find(
            _shoppingListsCollection,
            list.id,
          );

          final updatedList = ShoppingListModel.fromJson({
            'id': list.id,
            ...updatedData!,
          });

          // Cache the updated list
          if (updatedList.isTemplate == true) {
            await _cacheService.cacheTemplate(updatedList);
          } else if (updatedList.status == 'completed') {
            await _cacheService.cacheHistoryItem(updatedList);
          } else {
            await _cacheService.cacheShoppingList(updatedList);
          }

          return updatedList;
        } else {
          // Document doesn't exist, create it with the specified ID
          final createdData = await _databaseService.create(
            _shoppingListsCollection,
            {'id': list.id, ...list.toJson()},
          );

          final createdList = ShoppingListModel.fromJson(createdData);

          // Cache the created list
          if (createdList.isTemplate == true) {
            await _cacheService.cacheTemplate(createdList);
          } else if (createdList.status == 'completed') {
            await _cacheService.cacheHistoryItem(createdList);
          } else {
            await _cacheService.cacheShoppingList(createdList);
          }

          return createdList;
        }
      } else {
        // No ID specified, create a new document with auto-generated ID
        final createdData = await _databaseService.create(
          _shoppingListsCollection,
          list.toJson(),
        );

        final createdList = ShoppingListModel.fromJson(createdData);

        // Cache the created list
        if (createdList.isTemplate == true) {
          await _cacheService.cacheTemplate(createdList);
        } else if (createdList.status == 'completed') {
          await _cacheService.cacheHistoryItem(createdList);
        } else {
          await _cacheService.cacheShoppingList(createdList);
        }

        return createdList;
      }
    } catch (e) {
      // If there's an error, cache locally with a temporary ID
      final listWithId = list.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
      );

      // Cache based on list type
      if (listWithId.isTemplate == true) {
        await _cacheService.cacheTemplate(listWithId);
      } else if (listWithId.status == 'completed') {
        await _cacheService.cacheHistoryItem(listWithId);
      } else {
        await _cacheService.cacheShoppingList(listWithId);
      }

      await _cacheService.addPendingOperation(
        'create',
        listWithId.id,
        listWithId.toJson(),
      );
      return listWithId;
    }
  }

  Future<void> deleteShoppingList(String listId) async {
    try {
      // Check connectivity
      final isOnline = await _cacheService.isOnline();

      if (!isOnline) {
        // If offline, add to pending operations
        await _cacheService.addPendingOperation('delete', listId, {
          'id': listId,
        });
        return;
      }

      // If online, delete from hybrid database
      await _databaseService.delete(_shoppingListsCollection, listId);
    } catch (e) {
      // If there's an error, add to pending operations
      await _cacheService.addPendingOperation('delete', listId, {'id': listId});
      rethrow;
    }
  }

  /// Get templates created by the user
  Stream<List<ShoppingListModel>> getUserTemplates(String userId) async* {
    // First, yield cached data if available
    final cachedTemplates = _cacheService
        .getAllCachedTemplates()
        .where((template) => template.createdBy == userId)
        .toList();

    if (cachedTemplates.isNotEmpty) {
      yield cachedTemplates;
    }

    // Check connectivity
    final isOnline = await _cacheService.isOnline();

    if (!isOnline) {
      // If offline and we already yielded cached data, we're done
      if (cachedTemplates.isNotEmpty) return;

      // If offline and no cached data, yield empty list
      yield [];
      return;
    }

    // If online, get data from hybrid database and update cache
    try {
      final stream = _databaseService
          .watchCollection(
            _shoppingListsCollection,
            where: 'created_by = ? AND is_template = ?',
            whereParams: [userId, true],
            orderBy: 'created_at DESC',
          )
          .map((data) {
            final templates = data.map((item) {
              return ShoppingListModel.fromJson(item);
            }).toList();

            // Update cache
            _cacheService.cacheTemplates(templates);

            return templates;
          });

      await for (final templates in stream) {
        yield templates;
      }
    } catch (e) {
      // If there's an error and we already yielded cached data, we're done
      if (cachedTemplates.isNotEmpty) return;

      // Otherwise, rethrow the error
      rethrow;
    }
  }

  /// Get completed shopping lists for the user
  Stream<List<ShoppingListModel>> getCompletedShoppingLists(
    String userId,
  ) async* {
    // First, yield cached data if available
    final cachedHistory = _cacheService
        .getAllCachedHistoryItems()
        .where((item) => item.createdBy == userId && item.status == 'completed')
        .toList();

    if (cachedHistory.isNotEmpty) {
      yield cachedHistory;
    }

    // Check connectivity
    final isOnline = await _cacheService.isOnline();

    if (!isOnline) {
      // If offline and we already yielded cached data, we're done
      if (cachedHistory.isNotEmpty) return;

      // If offline and no cached data, yield empty list
      yield [];
      return;
    }

    // If online, get data from hybrid database and update cache
    try {
      final stream = _databaseService
          .watchCollection(
            _shoppingListsCollection,
            where: 'created_by = ? AND status = ?',
            whereParams: [userId, 'completed'],
            orderBy: 'updated_at DESC',
          )
          .map((data) {
            final historyItems = data.map((item) {
              return ShoppingListModel.fromJson(item);
            }).toList();

            // Update cache
            _cacheService.cacheHistoryItems(historyItems);

            return historyItems;
          });

      await for (final historyItems in stream) {
        yield historyItems;
      }
    } catch (e) {
      // If there's an error and we already yielded cached data, we're done
      if (cachedHistory.isNotEmpty) return;

      // Otherwise, rethrow the error
      rethrow;
    }
  }
}
