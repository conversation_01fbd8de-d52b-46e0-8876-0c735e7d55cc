// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$unreadNotificationsCountHash() =>
    r'c593f786634d9adc16f6e07856920c7c6dcc89e1';

/// Provider for unread notifications count
///
/// Copied from [unreadNotificationsCount].
@ProviderFor(unreadNotificationsCount)
final unreadNotificationsCountProvider =
    AutoDisposeStreamProvider<int>.internal(
      unreadNotificationsCount,
      name: r'unreadNotificationsCountProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$unreadNotificationsCountHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UnreadNotificationsCountRef = AutoDisposeStreamProviderRef<int>;
String _$buyerNotificationCountsByTypeHash() =>
    r'9eb07c9d7d215e0ee34b4572ce9a7b4f0aed9d98';

/// Provider for buyer-specific notification counts by type
///
/// Copied from [buyerNotificationCountsByType].
@ProviderFor(buyerNotificationCountsByType)
final buyerNotificationCountsByTypeProvider =
    AutoDisposeStreamProvider<Map<NotificationType, int>>.internal(
      buyerNotificationCountsByType,
      name: r'buyerNotificationCountsByTypeProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$buyerNotificationCountsByTypeHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BuyerNotificationCountsByTypeRef =
    AutoDisposeStreamProviderRef<Map<NotificationType, int>>;
String _$healthcareReminderNotificationsHash() =>
    r'1b28a92c1f6a4428d458e15af22bc2dd0f70ea62';

/// Provider for healthcare reminder notifications
///
/// Copied from [healthcareReminderNotifications].
@ProviderFor(healthcareReminderNotifications)
final healthcareReminderNotificationsProvider =
    AutoDisposeStreamProvider<List<NotificationModel>>.internal(
      healthcareReminderNotifications,
      name: r'healthcareReminderNotificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$healthcareReminderNotificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HealthcareReminderNotificationsRef =
    AutoDisposeStreamProviderRef<List<NotificationModel>>;
String _$calendarReminderNotificationsHash() =>
    r'ba6660a38ab3ba10e23664b9bbef00f02464b42f';

/// Provider for calendar reminder notifications
///
/// Copied from [calendarReminderNotifications].
@ProviderFor(calendarReminderNotifications)
final calendarReminderNotificationsProvider =
    AutoDisposeStreamProvider<List<NotificationModel>>.internal(
      calendarReminderNotifications,
      name: r'calendarReminderNotificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$calendarReminderNotificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CalendarReminderNotificationsRef =
    AutoDisposeStreamProviderRef<List<NotificationModel>>;
String _$shoppingListAlertNotificationsHash() =>
    r'80207999bba51abbf722d88d566c5123895442c4';

/// Provider for shopping list alert notifications
///
/// Copied from [shoppingListAlertNotifications].
@ProviderFor(shoppingListAlertNotifications)
final shoppingListAlertNotificationsProvider =
    AutoDisposeStreamProvider<List<NotificationModel>>.internal(
      shoppingListAlertNotifications,
      name: r'shoppingListAlertNotificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$shoppingListAlertNotificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ShoppingListAlertNotificationsRef =
    AutoDisposeStreamProviderRef<List<NotificationModel>>;
String _$safetyTrackingNotificationsHash() =>
    r'88c52cd87e316384fd747367915b1fbb9b7b028a';

/// Provider for safety and tracking notifications
///
/// Copied from [safetyTrackingNotifications].
@ProviderFor(safetyTrackingNotifications)
final safetyTrackingNotificationsProvider =
    AutoDisposeStreamProvider<List<NotificationModel>>.internal(
      safetyTrackingNotifications,
      name: r'safetyTrackingNotificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$safetyTrackingNotificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SafetyTrackingNotificationsRef =
    AutoDisposeStreamProviderRef<List<NotificationModel>>;
String _$bookingNotificationsHash() =>
    r'c0e45c3a075f449d4c5f8c3d0d95f39cd22abbd4';

/// Provider for booking-related notifications (ride, ticket, technician, priest)
///
/// Copied from [bookingNotifications].
@ProviderFor(bookingNotifications)
final bookingNotificationsProvider =
    AutoDisposeStreamProvider<List<NotificationModel>>.internal(
      bookingNotifications,
      name: r'bookingNotificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bookingNotificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BookingNotificationsRef =
    AutoDisposeStreamProviderRef<List<NotificationModel>>;
String _$healthcareNotificationsHash() =>
    r'a00cf24f44ccd91d62ced43cd9a008811c738739';

/// Provider for healthcare-related notifications
///
/// Copied from [healthcareNotifications].
@ProviderFor(healthcareNotifications)
final healthcareNotificationsProvider =
    AutoDisposeStreamProvider<List<NotificationModel>>.internal(
      healthcareNotifications,
      name: r'healthcareNotificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$healthcareNotificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HealthcareNotificationsRef =
    AutoDisposeStreamProviderRef<List<NotificationModel>>;
String _$aiAssistantNotificationsHash() =>
    r'da85c914e8cba8f578ff47dd417f9641be1d524a';

/// Provider for AI assistant notifications
///
/// Copied from [aiAssistantNotifications].
@ProviderFor(aiAssistantNotifications)
final aiAssistantNotificationsProvider =
    AutoDisposeStreamProvider<List<NotificationModel>>.internal(
      aiAssistantNotifications,
      name: r'aiAssistantNotificationsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$aiAssistantNotificationsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AiAssistantNotificationsRef =
    AutoDisposeStreamProviderRef<List<NotificationModel>>;
String _$notificationsNotifierHash() =>
    r'43ad6a23b9c13769aad0a77dece423be98a98397';

/// See also [NotificationsNotifier].
@ProviderFor(NotificationsNotifier)
final notificationsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      NotificationsNotifier,
      List<NotificationModel>
    >.internal(
      NotificationsNotifier.new,
      name: r'notificationsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$notificationsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$NotificationsNotifier =
    AutoDisposeAsyncNotifier<List<NotificationModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
