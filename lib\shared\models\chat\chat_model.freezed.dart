// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChatMessage {

 String get id; String get content; ChatMessageType get type; ChatSenderType get sender; DateTime get timestamp; String? get voiceUrl; String? get productId; String? get orderId;
/// Create a copy of ChatMessage
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatMessageCopyWith<ChatMessage> get copyWith => _$ChatMessageCopyWithImpl<ChatMessage>(this as ChatMessage, _$identity);

  /// Serializes this ChatMessage to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatMessage&&(identical(other.id, id) || other.id == id)&&(identical(other.content, content) || other.content == content)&&(identical(other.type, type) || other.type == type)&&(identical(other.sender, sender) || other.sender == sender)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.voiceUrl, voiceUrl) || other.voiceUrl == voiceUrl)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.orderId, orderId) || other.orderId == orderId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,content,type,sender,timestamp,voiceUrl,productId,orderId);

@override
String toString() {
  return 'ChatMessage(id: $id, content: $content, type: $type, sender: $sender, timestamp: $timestamp, voiceUrl: $voiceUrl, productId: $productId, orderId: $orderId)';
}


}

/// @nodoc
abstract mixin class $ChatMessageCopyWith<$Res>  {
  factory $ChatMessageCopyWith(ChatMessage value, $Res Function(ChatMessage) _then) = _$ChatMessageCopyWithImpl;
@useResult
$Res call({
 String id, String content, ChatMessageType type, ChatSenderType sender, DateTime timestamp, String? voiceUrl, String? productId, String? orderId
});




}
/// @nodoc
class _$ChatMessageCopyWithImpl<$Res>
    implements $ChatMessageCopyWith<$Res> {
  _$ChatMessageCopyWithImpl(this._self, this._then);

  final ChatMessage _self;
  final $Res Function(ChatMessage) _then;

/// Create a copy of ChatMessage
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? content = null,Object? type = null,Object? sender = null,Object? timestamp = null,Object? voiceUrl = freezed,Object? productId = freezed,Object? orderId = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ChatMessageType,sender: null == sender ? _self.sender : sender // ignore: cast_nullable_to_non_nullable
as ChatSenderType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,voiceUrl: freezed == voiceUrl ? _self.voiceUrl : voiceUrl // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [ChatMessage].
extension ChatMessagePatterns on ChatMessage {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatMessage value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatMessage() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatMessage value)  $default,){
final _that = this;
switch (_that) {
case _ChatMessage():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatMessage value)?  $default,){
final _that = this;
switch (_that) {
case _ChatMessage() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String content,  ChatMessageType type,  ChatSenderType sender,  DateTime timestamp,  String? voiceUrl,  String? productId,  String? orderId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatMessage() when $default != null:
return $default(_that.id,_that.content,_that.type,_that.sender,_that.timestamp,_that.voiceUrl,_that.productId,_that.orderId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String content,  ChatMessageType type,  ChatSenderType sender,  DateTime timestamp,  String? voiceUrl,  String? productId,  String? orderId)  $default,) {final _that = this;
switch (_that) {
case _ChatMessage():
return $default(_that.id,_that.content,_that.type,_that.sender,_that.timestamp,_that.voiceUrl,_that.productId,_that.orderId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String content,  ChatMessageType type,  ChatSenderType sender,  DateTime timestamp,  String? voiceUrl,  String? productId,  String? orderId)?  $default,) {final _that = this;
switch (_that) {
case _ChatMessage() when $default != null:
return $default(_that.id,_that.content,_that.type,_that.sender,_that.timestamp,_that.voiceUrl,_that.productId,_that.orderId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatMessage implements ChatMessage {
  const _ChatMessage({required this.id, required this.content, required this.type, required this.sender, required this.timestamp, this.voiceUrl, this.productId, this.orderId});
  factory _ChatMessage.fromJson(Map<String, dynamic> json) => _$ChatMessageFromJson(json);

@override final  String id;
@override final  String content;
@override final  ChatMessageType type;
@override final  ChatSenderType sender;
@override final  DateTime timestamp;
@override final  String? voiceUrl;
@override final  String? productId;
@override final  String? orderId;

/// Create a copy of ChatMessage
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatMessageCopyWith<_ChatMessage> get copyWith => __$ChatMessageCopyWithImpl<_ChatMessage>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatMessageToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatMessage&&(identical(other.id, id) || other.id == id)&&(identical(other.content, content) || other.content == content)&&(identical(other.type, type) || other.type == type)&&(identical(other.sender, sender) || other.sender == sender)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.voiceUrl, voiceUrl) || other.voiceUrl == voiceUrl)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.orderId, orderId) || other.orderId == orderId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,content,type,sender,timestamp,voiceUrl,productId,orderId);

@override
String toString() {
  return 'ChatMessage(id: $id, content: $content, type: $type, sender: $sender, timestamp: $timestamp, voiceUrl: $voiceUrl, productId: $productId, orderId: $orderId)';
}


}

/// @nodoc
abstract mixin class _$ChatMessageCopyWith<$Res> implements $ChatMessageCopyWith<$Res> {
  factory _$ChatMessageCopyWith(_ChatMessage value, $Res Function(_ChatMessage) _then) = __$ChatMessageCopyWithImpl;
@override @useResult
$Res call({
 String id, String content, ChatMessageType type, ChatSenderType sender, DateTime timestamp, String? voiceUrl, String? productId, String? orderId
});




}
/// @nodoc
class __$ChatMessageCopyWithImpl<$Res>
    implements _$ChatMessageCopyWith<$Res> {
  __$ChatMessageCopyWithImpl(this._self, this._then);

  final _ChatMessage _self;
  final $Res Function(_ChatMessage) _then;

/// Create a copy of ChatMessage
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? content = null,Object? type = null,Object? sender = null,Object? timestamp = null,Object? voiceUrl = freezed,Object? productId = freezed,Object? orderId = freezed,}) {
  return _then(_ChatMessage(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ChatMessageType,sender: null == sender ? _self.sender : sender // ignore: cast_nullable_to_non_nullable
as ChatSenderType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,voiceUrl: freezed == voiceUrl ? _self.voiceUrl : voiceUrl // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,orderId: freezed == orderId ? _self.orderId : orderId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$ChatState {

 List<ChatMessage> get messages; bool get isLoading; bool get hasError; String? get errorMessage; bool get isVoiceRecording; bool get isVoicePlaying; String? get currentVoiceUrl; int get currentVoicePosition; int get currentVoiceDuration; ChatContext? get chatContext; String? get currentVoiceMessageId;
/// Create a copy of ChatState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatStateCopyWith<ChatState> get copyWith => _$ChatStateCopyWithImpl<ChatState>(this as ChatState, _$identity);

  /// Serializes this ChatState to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatState&&const DeepCollectionEquality().equals(other.messages, messages)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasError, hasError) || other.hasError == hasError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isVoiceRecording, isVoiceRecording) || other.isVoiceRecording == isVoiceRecording)&&(identical(other.isVoicePlaying, isVoicePlaying) || other.isVoicePlaying == isVoicePlaying)&&(identical(other.currentVoiceUrl, currentVoiceUrl) || other.currentVoiceUrl == currentVoiceUrl)&&(identical(other.currentVoicePosition, currentVoicePosition) || other.currentVoicePosition == currentVoicePosition)&&(identical(other.currentVoiceDuration, currentVoiceDuration) || other.currentVoiceDuration == currentVoiceDuration)&&(identical(other.chatContext, chatContext) || other.chatContext == chatContext)&&(identical(other.currentVoiceMessageId, currentVoiceMessageId) || other.currentVoiceMessageId == currentVoiceMessageId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(messages),isLoading,hasError,errorMessage,isVoiceRecording,isVoicePlaying,currentVoiceUrl,currentVoicePosition,currentVoiceDuration,chatContext,currentVoiceMessageId);

@override
String toString() {
  return 'ChatState(messages: $messages, isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, isVoiceRecording: $isVoiceRecording, isVoicePlaying: $isVoicePlaying, currentVoiceUrl: $currentVoiceUrl, currentVoicePosition: $currentVoicePosition, currentVoiceDuration: $currentVoiceDuration, chatContext: $chatContext, currentVoiceMessageId: $currentVoiceMessageId)';
}


}

/// @nodoc
abstract mixin class $ChatStateCopyWith<$Res>  {
  factory $ChatStateCopyWith(ChatState value, $Res Function(ChatState) _then) = _$ChatStateCopyWithImpl;
@useResult
$Res call({
 List<ChatMessage> messages, bool isLoading, bool hasError, String? errorMessage, bool isVoiceRecording, bool isVoicePlaying, String? currentVoiceUrl, int currentVoicePosition, int currentVoiceDuration, ChatContext? chatContext, String? currentVoiceMessageId
});


$ChatContextCopyWith<$Res>? get chatContext;

}
/// @nodoc
class _$ChatStateCopyWithImpl<$Res>
    implements $ChatStateCopyWith<$Res> {
  _$ChatStateCopyWithImpl(this._self, this._then);

  final ChatState _self;
  final $Res Function(ChatState) _then;

/// Create a copy of ChatState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? messages = null,Object? isLoading = null,Object? hasError = null,Object? errorMessage = freezed,Object? isVoiceRecording = null,Object? isVoicePlaying = null,Object? currentVoiceUrl = freezed,Object? currentVoicePosition = null,Object? currentVoiceDuration = null,Object? chatContext = freezed,Object? currentVoiceMessageId = freezed,}) {
  return _then(_self.copyWith(
messages: null == messages ? _self.messages : messages // ignore: cast_nullable_to_non_nullable
as List<ChatMessage>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isVoiceRecording: null == isVoiceRecording ? _self.isVoiceRecording : isVoiceRecording // ignore: cast_nullable_to_non_nullable
as bool,isVoicePlaying: null == isVoicePlaying ? _self.isVoicePlaying : isVoicePlaying // ignore: cast_nullable_to_non_nullable
as bool,currentVoiceUrl: freezed == currentVoiceUrl ? _self.currentVoiceUrl : currentVoiceUrl // ignore: cast_nullable_to_non_nullable
as String?,currentVoicePosition: null == currentVoicePosition ? _self.currentVoicePosition : currentVoicePosition // ignore: cast_nullable_to_non_nullable
as int,currentVoiceDuration: null == currentVoiceDuration ? _self.currentVoiceDuration : currentVoiceDuration // ignore: cast_nullable_to_non_nullable
as int,chatContext: freezed == chatContext ? _self.chatContext : chatContext // ignore: cast_nullable_to_non_nullable
as ChatContext?,currentVoiceMessageId: freezed == currentVoiceMessageId ? _self.currentVoiceMessageId : currentVoiceMessageId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of ChatState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChatContextCopyWith<$Res>? get chatContext {
    if (_self.chatContext == null) {
    return null;
  }

  return $ChatContextCopyWith<$Res>(_self.chatContext!, (value) {
    return _then(_self.copyWith(chatContext: value));
  });
}
}


/// Adds pattern-matching-related methods to [ChatState].
extension ChatStatePatterns on ChatState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatState value)  $default,){
final _that = this;
switch (_that) {
case _ChatState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatState value)?  $default,){
final _that = this;
switch (_that) {
case _ChatState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<ChatMessage> messages,  bool isLoading,  bool hasError,  String? errorMessage,  bool isVoiceRecording,  bool isVoicePlaying,  String? currentVoiceUrl,  int currentVoicePosition,  int currentVoiceDuration,  ChatContext? chatContext,  String? currentVoiceMessageId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatState() when $default != null:
return $default(_that.messages,_that.isLoading,_that.hasError,_that.errorMessage,_that.isVoiceRecording,_that.isVoicePlaying,_that.currentVoiceUrl,_that.currentVoicePosition,_that.currentVoiceDuration,_that.chatContext,_that.currentVoiceMessageId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<ChatMessage> messages,  bool isLoading,  bool hasError,  String? errorMessage,  bool isVoiceRecording,  bool isVoicePlaying,  String? currentVoiceUrl,  int currentVoicePosition,  int currentVoiceDuration,  ChatContext? chatContext,  String? currentVoiceMessageId)  $default,) {final _that = this;
switch (_that) {
case _ChatState():
return $default(_that.messages,_that.isLoading,_that.hasError,_that.errorMessage,_that.isVoiceRecording,_that.isVoicePlaying,_that.currentVoiceUrl,_that.currentVoicePosition,_that.currentVoiceDuration,_that.chatContext,_that.currentVoiceMessageId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<ChatMessage> messages,  bool isLoading,  bool hasError,  String? errorMessage,  bool isVoiceRecording,  bool isVoicePlaying,  String? currentVoiceUrl,  int currentVoicePosition,  int currentVoiceDuration,  ChatContext? chatContext,  String? currentVoiceMessageId)?  $default,) {final _that = this;
switch (_that) {
case _ChatState() when $default != null:
return $default(_that.messages,_that.isLoading,_that.hasError,_that.errorMessage,_that.isVoiceRecording,_that.isVoicePlaying,_that.currentVoiceUrl,_that.currentVoicePosition,_that.currentVoiceDuration,_that.chatContext,_that.currentVoiceMessageId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatState implements ChatState {
  const _ChatState({required final  List<ChatMessage> messages, required this.isLoading, required this.hasError, this.errorMessage, this.isVoiceRecording = false, this.isVoicePlaying = false, this.currentVoiceUrl, this.currentVoicePosition = 0, this.currentVoiceDuration = 0, this.chatContext, this.currentVoiceMessageId}): _messages = messages;
  factory _ChatState.fromJson(Map<String, dynamic> json) => _$ChatStateFromJson(json);

 final  List<ChatMessage> _messages;
@override List<ChatMessage> get messages {
  if (_messages is EqualUnmodifiableListView) return _messages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_messages);
}

@override final  bool isLoading;
@override final  bool hasError;
@override final  String? errorMessage;
@override@JsonKey() final  bool isVoiceRecording;
@override@JsonKey() final  bool isVoicePlaying;
@override final  String? currentVoiceUrl;
@override@JsonKey() final  int currentVoicePosition;
@override@JsonKey() final  int currentVoiceDuration;
@override final  ChatContext? chatContext;
@override final  String? currentVoiceMessageId;

/// Create a copy of ChatState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatStateCopyWith<_ChatState> get copyWith => __$ChatStateCopyWithImpl<_ChatState>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatStateToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatState&&const DeepCollectionEquality().equals(other._messages, _messages)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasError, hasError) || other.hasError == hasError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isVoiceRecording, isVoiceRecording) || other.isVoiceRecording == isVoiceRecording)&&(identical(other.isVoicePlaying, isVoicePlaying) || other.isVoicePlaying == isVoicePlaying)&&(identical(other.currentVoiceUrl, currentVoiceUrl) || other.currentVoiceUrl == currentVoiceUrl)&&(identical(other.currentVoicePosition, currentVoicePosition) || other.currentVoicePosition == currentVoicePosition)&&(identical(other.currentVoiceDuration, currentVoiceDuration) || other.currentVoiceDuration == currentVoiceDuration)&&(identical(other.chatContext, chatContext) || other.chatContext == chatContext)&&(identical(other.currentVoiceMessageId, currentVoiceMessageId) || other.currentVoiceMessageId == currentVoiceMessageId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_messages),isLoading,hasError,errorMessage,isVoiceRecording,isVoicePlaying,currentVoiceUrl,currentVoicePosition,currentVoiceDuration,chatContext,currentVoiceMessageId);

@override
String toString() {
  return 'ChatState(messages: $messages, isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, isVoiceRecording: $isVoiceRecording, isVoicePlaying: $isVoicePlaying, currentVoiceUrl: $currentVoiceUrl, currentVoicePosition: $currentVoicePosition, currentVoiceDuration: $currentVoiceDuration, chatContext: $chatContext, currentVoiceMessageId: $currentVoiceMessageId)';
}


}

/// @nodoc
abstract mixin class _$ChatStateCopyWith<$Res> implements $ChatStateCopyWith<$Res> {
  factory _$ChatStateCopyWith(_ChatState value, $Res Function(_ChatState) _then) = __$ChatStateCopyWithImpl;
@override @useResult
$Res call({
 List<ChatMessage> messages, bool isLoading, bool hasError, String? errorMessage, bool isVoiceRecording, bool isVoicePlaying, String? currentVoiceUrl, int currentVoicePosition, int currentVoiceDuration, ChatContext? chatContext, String? currentVoiceMessageId
});


@override $ChatContextCopyWith<$Res>? get chatContext;

}
/// @nodoc
class __$ChatStateCopyWithImpl<$Res>
    implements _$ChatStateCopyWith<$Res> {
  __$ChatStateCopyWithImpl(this._self, this._then);

  final _ChatState _self;
  final $Res Function(_ChatState) _then;

/// Create a copy of ChatState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? messages = null,Object? isLoading = null,Object? hasError = null,Object? errorMessage = freezed,Object? isVoiceRecording = null,Object? isVoicePlaying = null,Object? currentVoiceUrl = freezed,Object? currentVoicePosition = null,Object? currentVoiceDuration = null,Object? chatContext = freezed,Object? currentVoiceMessageId = freezed,}) {
  return _then(_ChatState(
messages: null == messages ? _self._messages : messages // ignore: cast_nullable_to_non_nullable
as List<ChatMessage>,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isVoiceRecording: null == isVoiceRecording ? _self.isVoiceRecording : isVoiceRecording // ignore: cast_nullable_to_non_nullable
as bool,isVoicePlaying: null == isVoicePlaying ? _self.isVoicePlaying : isVoicePlaying // ignore: cast_nullable_to_non_nullable
as bool,currentVoiceUrl: freezed == currentVoiceUrl ? _self.currentVoiceUrl : currentVoiceUrl // ignore: cast_nullable_to_non_nullable
as String?,currentVoicePosition: null == currentVoicePosition ? _self.currentVoicePosition : currentVoicePosition // ignore: cast_nullable_to_non_nullable
as int,currentVoiceDuration: null == currentVoiceDuration ? _self.currentVoiceDuration : currentVoiceDuration // ignore: cast_nullable_to_non_nullable
as int,chatContext: freezed == chatContext ? _self.chatContext : chatContext // ignore: cast_nullable_to_non_nullable
as ChatContext?,currentVoiceMessageId: freezed == currentVoiceMessageId ? _self.currentVoiceMessageId : currentVoiceMessageId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of ChatState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChatContextCopyWith<$Res>? get chatContext {
    if (_self.chatContext == null) {
    return null;
  }

  return $ChatContextCopyWith<$Res>(_self.chatContext!, (value) {
    return _then(_self.copyWith(chatContext: value));
  });
}
}


/// @nodoc
mixin _$ChatContext {

 String get sessionId; String get userId; List<ChatMessage> get messages; DateTime get lastUpdated;
/// Create a copy of ChatContext
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatContextCopyWith<ChatContext> get copyWith => _$ChatContextCopyWithImpl<ChatContext>(this as ChatContext, _$identity);

  /// Serializes this ChatContext to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatContext&&(identical(other.sessionId, sessionId) || other.sessionId == sessionId)&&(identical(other.userId, userId) || other.userId == userId)&&const DeepCollectionEquality().equals(other.messages, messages)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sessionId,userId,const DeepCollectionEquality().hash(messages),lastUpdated);

@override
String toString() {
  return 'ChatContext(sessionId: $sessionId, userId: $userId, messages: $messages, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class $ChatContextCopyWith<$Res>  {
  factory $ChatContextCopyWith(ChatContext value, $Res Function(ChatContext) _then) = _$ChatContextCopyWithImpl;
@useResult
$Res call({
 String sessionId, String userId, List<ChatMessage> messages, DateTime lastUpdated
});




}
/// @nodoc
class _$ChatContextCopyWithImpl<$Res>
    implements $ChatContextCopyWith<$Res> {
  _$ChatContextCopyWithImpl(this._self, this._then);

  final ChatContext _self;
  final $Res Function(ChatContext) _then;

/// Create a copy of ChatContext
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sessionId = null,Object? userId = null,Object? messages = null,Object? lastUpdated = null,}) {
  return _then(_self.copyWith(
sessionId: null == sessionId ? _self.sessionId : sessionId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,messages: null == messages ? _self.messages : messages // ignore: cast_nullable_to_non_nullable
as List<ChatMessage>,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [ChatContext].
extension ChatContextPatterns on ChatContext {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatContext value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatContext() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatContext value)  $default,){
final _that = this;
switch (_that) {
case _ChatContext():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatContext value)?  $default,){
final _that = this;
switch (_that) {
case _ChatContext() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String sessionId,  String userId,  List<ChatMessage> messages,  DateTime lastUpdated)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatContext() when $default != null:
return $default(_that.sessionId,_that.userId,_that.messages,_that.lastUpdated);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String sessionId,  String userId,  List<ChatMessage> messages,  DateTime lastUpdated)  $default,) {final _that = this;
switch (_that) {
case _ChatContext():
return $default(_that.sessionId,_that.userId,_that.messages,_that.lastUpdated);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String sessionId,  String userId,  List<ChatMessage> messages,  DateTime lastUpdated)?  $default,) {final _that = this;
switch (_that) {
case _ChatContext() when $default != null:
return $default(_that.sessionId,_that.userId,_that.messages,_that.lastUpdated);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatContext implements ChatContext {
  const _ChatContext({required this.sessionId, required this.userId, required final  List<ChatMessage> messages, required this.lastUpdated}): _messages = messages;
  factory _ChatContext.fromJson(Map<String, dynamic> json) => _$ChatContextFromJson(json);

@override final  String sessionId;
@override final  String userId;
 final  List<ChatMessage> _messages;
@override List<ChatMessage> get messages {
  if (_messages is EqualUnmodifiableListView) return _messages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_messages);
}

@override final  DateTime lastUpdated;

/// Create a copy of ChatContext
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatContextCopyWith<_ChatContext> get copyWith => __$ChatContextCopyWithImpl<_ChatContext>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatContextToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatContext&&(identical(other.sessionId, sessionId) || other.sessionId == sessionId)&&(identical(other.userId, userId) || other.userId == userId)&&const DeepCollectionEquality().equals(other._messages, _messages)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sessionId,userId,const DeepCollectionEquality().hash(_messages),lastUpdated);

@override
String toString() {
  return 'ChatContext(sessionId: $sessionId, userId: $userId, messages: $messages, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class _$ChatContextCopyWith<$Res> implements $ChatContextCopyWith<$Res> {
  factory _$ChatContextCopyWith(_ChatContext value, $Res Function(_ChatContext) _then) = __$ChatContextCopyWithImpl;
@override @useResult
$Res call({
 String sessionId, String userId, List<ChatMessage> messages, DateTime lastUpdated
});




}
/// @nodoc
class __$ChatContextCopyWithImpl<$Res>
    implements _$ChatContextCopyWith<$Res> {
  __$ChatContextCopyWithImpl(this._self, this._then);

  final _ChatContext _self;
  final $Res Function(_ChatContext) _then;

/// Create a copy of ChatContext
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sessionId = null,Object? userId = null,Object? messages = null,Object? lastUpdated = null,}) {
  return _then(_ChatContext(
sessionId: null == sessionId ? _self.sessionId : sessionId // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,messages: null == messages ? _self._messages : messages // ignore: cast_nullable_to_non_nullable
as List<ChatMessage>,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
