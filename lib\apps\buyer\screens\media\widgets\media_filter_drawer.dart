import 'package:flutter/material.dart';
import '../../../../../shared/models/media/media_model.dart';

class MediaFilterDrawer extends StatelessWidget {
  final MediaType? selectedType;
  final MediaVisibility? selectedVisibility;
  final ValueChanged<MediaType?> onTypeChanged;
  final ValueChanged<MediaVisibility?> onVisibilityChanged;
  final VoidCallback onClearFilters;

  const MediaFilterDrawer({
    super.key,
    required this.selectedType,
    required this.selectedVisibility,
    required this.onTypeChanged,
    required this.onVisibilityChanged,
    required this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Filter Media',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Select filters to narrow down your media',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                Text(
                  'Media Type',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: [
                    FilterChip(
                      label: const Text('All'),
                      selected: selectedType == null,
                      onSelected: (selected) {
                        if (selected) {
                          onTypeChanged(null);
                        }
                      },
                    ),
                    ...MediaType.values.map(
                      (type) => FilterChip(
                        label: Text(_getMediaTypeText(type)),
                        selected: selectedType == type,
                        onSelected: (selected) {
                          onTypeChanged(selected ? type : null);
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Text(
                  'Visibility',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: [
                    FilterChip(
                      label: const Text('All'),
                      selected: selectedVisibility == null,
                      onSelected: (selected) {
                        if (selected) {
                          onVisibilityChanged(null);
                        }
                      },
                    ),
                    ...MediaVisibility.values.map(
                      (visibility) => FilterChip(
                        label: Text(_getVisibilityText(visibility)),
                        selected: selectedVisibility == visibility,
                        onSelected: (selected) {
                          onVisibilityChanged(selected ? visibility : null);
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: ElevatedButton(
              onPressed: onClearFilters,
              child: const Text('Clear All Filters'),
            ),
          ),
        ],
      ),
    );
  }

  String _getMediaTypeText(MediaType type) {
    return switch (type) {
      MediaType.image => 'Image',
      MediaType.video => 'Video',
      MediaType.audio => 'Audio',
    };
  }

  String _getVisibilityText(MediaVisibility visibility) {
    return switch (visibility) {
      MediaVisibility.public => 'Public',
      MediaVisibility.private => 'Private',
      MediaVisibility.restricted => 'Restricted',
    };
  }
}
