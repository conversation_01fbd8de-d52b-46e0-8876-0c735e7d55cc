package com.shivish.utils

import android.content.Context
import android.content.res.Resources
import android.graphics.drawable.Drawable
import android.util.Log
import androidx.core.content.ContextCompat

/**
 * Utility class for resource operations
 */
object ResourceUtils {
    private const val TAG = "ResourceUtils"

    /**
     * Safely get a string resource
     */
    fun getString(context: Context, resId: Int): String? {
        return try {
            context.getString(resId)
        } catch (e: Resources.NotFoundException) {
            Log.e(TAG, "String resource not found: $resId")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting string resource: ${e.message}")
            null
        }
    }

    /**
     * Safely get a string resource with formatting
     */
    fun getString(context: Context, resId: Int, vararg formatArgs: Any): String? {
        return try {
            context.getString(resId, *formatArgs)
        } catch (e: Resources.NotFoundException) {
            Log.e(TAG, "String resource not found: $resId")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting formatted string resource: ${e.message}")
            null
        }
    }

    /**
     * Safely get a color resource
     */
    fun getColor(context: Context, resId: Int): Int? {
        return try {
            ContextCompat.getColor(context, resId)
        } catch (e: Resources.NotFoundException) {
            Log.e(TAG, "Color resource not found: $resId")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting color resource: ${e.message}")
            null
        }
    }

    /**
     * Safely get a drawable resource
     */
    fun getDrawable(context: Context, resId: Int): Drawable? {
        return try {
            ContextCompat.getDrawable(context, resId)
        } catch (e: Resources.NotFoundException) {
            Log.e(TAG, "Drawable resource not found: $resId")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting drawable resource: ${e.message}")
            null
        }
    }

    /**
     * Safely get a dimension resource
     */
    fun getDimension(context: Context, resId: Int): Float? {
        return try {
            context.resources.getDimension(resId)
        } catch (e: Resources.NotFoundException) {
            Log.e(TAG, "Dimension resource not found: $resId")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting dimension resource: ${e.message}")
            null
        }
    }

    /**
     * Safely get a dimension pixel size resource
     */
    fun getDimensionPixelSize(context: Context, resId: Int): Int? {
        return try {
            context.resources.getDimensionPixelSize(resId)
        } catch (e: Resources.NotFoundException) {
            Log.e(TAG, "Dimension pixel size resource not found: $resId")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting dimension pixel size resource: ${e.message}")
            null
        }
    }

    /**
     * Safely get an integer resource
     */
    fun getInteger(context: Context, resId: Int): Int? {
        return try {
            context.resources.getInteger(resId)
        } catch (e: Resources.NotFoundException) {
            Log.e(TAG, "Integer resource not found: $resId")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting integer resource: ${e.message}")
            null
        }
    }

    /**
     * Safely get a boolean resource
     */
    fun getBoolean(context: Context, resId: Int): Boolean? {
        return try {
            context.resources.getBoolean(resId)
        } catch (e: Resources.NotFoundException) {
            Log.e(TAG, "Boolean resource not found: $resId")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting boolean resource: ${e.message}")
            null
        }
    }

    /**
     * Check if a resource exists
     */
    fun resourceExists(context: Context, resId: Int): Boolean {
        return try {
            context.resources.getResourceName(resId)
            true
        } catch (e: Resources.NotFoundException) {
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking resource existence: ${e.message}")
            false
        }
    }

    /**
     * Get resource identifier by name
     */
    fun getResourceId(context: Context, name: String, defType: String, defPackage: String? = null): Int {
        return try {
            val packageName = defPackage ?: context.packageName
            context.resources.getIdentifier(name, defType, packageName)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting resource ID for $name: ${e.message}")
            0
        }
    }
}
