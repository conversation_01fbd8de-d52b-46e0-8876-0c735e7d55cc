/// SSL Mode for PostgreSQL connections
enum SslMode {
  disable,
  allow,
  prefer,
  require,
  verifyCA,
  verifyFull,
}

/// Database configuration class
class DatabaseConfig {
  final String host;
  final int port;
  final String database;
  final String username;
  final String password;
  final SslMode sslMode;
  final int connectTimeout;
  final int queryTimeout;
  final int maxConnections;
  final bool enableLogging;

  const DatabaseConfig({
    required this.host,
    required this.port,
    required this.database,
    required this.username,
    required this.password,
    this.sslMode = SslMode.prefer,
    this.connectTimeout = 30,
    this.queryTimeout = 30,
    this.maxConnections = 10,
    this.enableLogging = false,
  });

  /// Create configuration for production environment
  factory DatabaseConfig.production() {
    return const DatabaseConfig(
      host: 'prod-db.your-domain.com',
      port: 5432,
      database: 'shivish_prod',
      username: 'shivish_user',
      password: 'your-secure-password',
      sslMode: SslMode.require,
      connectTimeout: 30,
      queryTimeout: 60,
      maxConnections: 20,
      enableLogging: false,
    );
  }

  /// Create configuration for staging environment
  factory DatabaseConfig.staging() {
    return const DatabaseConfig(
      host: 'staging-db.your-domain.com',
      port: 5432,
      database: 'shivish_staging',
      username: 'shivish_user',
      password: 'your-staging-password',
      sslMode: SslMode.prefer,
      connectTimeout: 30,
      queryTimeout: 30,
      maxConnections: 15,
      enableLogging: true,
    );
  }

  /// Create configuration for development environment
  factory DatabaseConfig.development() {
    return const DatabaseConfig(
      host: 'localhost',
      port: 5432,
      database: 'shivish_dev',
      username: 'postgres',
      password: 'password',
      sslMode: SslMode.disable,
      connectTimeout: 10,
      queryTimeout: 30,
      maxConnections: 5,
      enableLogging: true,
    );
  }

  /// Create configuration from environment variables
  factory DatabaseConfig.fromEnvironment() {
    return DatabaseConfig(
      host: const String.fromEnvironment('DB_HOST', defaultValue: 'localhost'),
      port: const int.fromEnvironment('DB_PORT', defaultValue: 5432),
      database: const String.fromEnvironment('DB_NAME', defaultValue: 'shivish'),
      username: const String.fromEnvironment('DB_USER', defaultValue: 'postgres'),
      password: const String.fromEnvironment('DB_PASSWORD', defaultValue: 'password'),
      sslMode: _parseSslMode(const String.fromEnvironment('DB_SSL_MODE', defaultValue: 'prefer')),
      connectTimeout: const int.fromEnvironment('DB_CONNECT_TIMEOUT', defaultValue: 30),
      queryTimeout: const int.fromEnvironment('DB_QUERY_TIMEOUT', defaultValue: 30),
      maxConnections: const int.fromEnvironment('DB_MAX_CONNECTIONS', defaultValue: 10),
      enableLogging: const bool.fromEnvironment('DB_ENABLE_LOGGING', defaultValue: false),
    );
  }

  /// Parse SSL mode from string
  static SslMode _parseSslMode(String mode) {
    switch (mode.toLowerCase()) {
      case 'disable':
        return SslMode.disable;
      case 'allow':
        return SslMode.allow;
      case 'prefer':
        return SslMode.prefer;
      case 'require':
        return SslMode.require;
      case 'verify-ca':
        return SslMode.verifyCA;
      case 'verify-full':
        return SslMode.verifyFull;
      default:
        return SslMode.prefer;
    }
  }

  /// Get connection string
  String get connectionString {
    return 'postgresql://$username:$password@$host:$port/$database';
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'host': host,
      'port': port,
      'database': database,
      'username': username,
      'ssl_mode': sslMode.name,
      'connect_timeout': connectTimeout,
      'query_timeout': queryTimeout,
      'max_connections': maxConnections,
      'enable_logging': enableLogging,
    };
  }

  @override
  String toString() {
    return 'DatabaseConfig(host: $host, port: $port, database: $database, username: $username)';
  }
}

/// File storage configuration
class FileStorageConfig {
  final String provider; // 's3', 'local', 'gcs'
  final String bucket;
  final String region;
  final String accessKey;
  final String secretKey;
  final String endpoint;
  final bool useSSL;
  final String cdnUrl;
  final String localPath;

  const FileStorageConfig({
    required this.provider,
    required this.bucket,
    required this.region,
    required this.accessKey,
    required this.secretKey,
    required this.endpoint,
    this.useSSL = true,
    required this.cdnUrl,
    required this.localPath,
  });

  /// Create S3 configuration for production
  factory FileStorageConfig.s3Production() {
    return const FileStorageConfig(
      provider: 's3',
      bucket: 'shivish-storage-prod',
      region: 'us-east-1',
      accessKey: 'your-access-key',
      secretKey: 'your-secret-key',
      endpoint: 's3.amazonaws.com',
      useSSL: true,
      cdnUrl: 'https://cdn.your-domain.com',
      localPath: '/tmp/storage',
    );
  }

  /// Create S3 configuration for staging
  factory FileStorageConfig.s3Staging() {
    return const FileStorageConfig(
      provider: 's3',
      bucket: 'shivish-storage-staging',
      region: 'us-west-2',
      accessKey: 'your-staging-access-key',
      secretKey: 'your-staging-secret-key',
      endpoint: 's3.amazonaws.com',
      useSSL: true,
      cdnUrl: 'https://staging-cdn.your-domain.com',
      localPath: '/tmp/storage',
    );
  }

  /// Create local storage configuration for development
  factory FileStorageConfig.local() {
    return const FileStorageConfig(
      provider: 'local',
      bucket: 'local-storage',
      region: 'local',
      accessKey: '',
      secretKey: '',
      endpoint: '',
      useSSL: false,
      cdnUrl: 'http://localhost:8080/storage',
      localPath: './storage',
    );
  }

  /// Create configuration from environment variables
  factory FileStorageConfig.fromEnvironment() {
    return FileStorageConfig(
      provider: const String.fromEnvironment('STORAGE_PROVIDER', defaultValue: 'local'),
      bucket: const String.fromEnvironment('STORAGE_BUCKET', defaultValue: 'shivish-storage'),
      region: const String.fromEnvironment('STORAGE_REGION', defaultValue: 'us-east-1'),
      accessKey: const String.fromEnvironment('STORAGE_ACCESS_KEY', defaultValue: ''),
      secretKey: const String.fromEnvironment('STORAGE_SECRET_KEY', defaultValue: ''),
      endpoint: const String.fromEnvironment('STORAGE_ENDPOINT', defaultValue: 's3.amazonaws.com'),
      useSSL: const bool.fromEnvironment('STORAGE_USE_SSL', defaultValue: true),
      cdnUrl: const String.fromEnvironment('STORAGE_CDN_URL', defaultValue: ''),
      localPath: const String.fromEnvironment('STORAGE_LOCAL_PATH', defaultValue: './storage'),
    );
  }

  /// Check if using S3
  bool get isS3 => provider == 's3';

  /// Check if using local storage
  bool get isLocal => provider == 'local';

  /// Check if using Google Cloud Storage
  bool get isGCS => provider == 'gcs';

  /// Get full endpoint URL
  String get fullEndpoint {
    if (isLocal) return localPath;
    return useSSL ? 'https://$endpoint' : 'http://$endpoint';
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'provider': provider,
      'bucket': bucket,
      'region': region,
      'endpoint': endpoint,
      'use_ssl': useSSL,
      'cdn_url': cdnUrl,
      'local_path': localPath,
    };
  }

  @override
  String toString() {
    return 'FileStorageConfig(provider: $provider, bucket: $bucket, region: $region)';
  }
}

/// Cache configuration
class CacheConfig {
  final String provider; // 'redis', 'memory'
  final String host;
  final int port;
  final String password;
  final int database;
  final int maxConnections;
  final Duration defaultTtl;
  final bool enableLogging;

  const CacheConfig({
    required this.provider,
    required this.host,
    required this.port,
    required this.password,
    this.database = 0,
    this.maxConnections = 10,
    this.defaultTtl = const Duration(hours: 1),
    this.enableLogging = false,
  });

  /// Create Redis configuration for production
  factory CacheConfig.redisProduction() {
    return const CacheConfig(
      provider: 'redis',
      host: 'prod-redis.your-domain.com',
      port: 6379,
      password: 'your-redis-password',
      database: 0,
      maxConnections: 20,
      defaultTtl: Duration(hours: 24),
      enableLogging: false,
    );
  }

  /// Create Redis configuration for development
  factory CacheConfig.redisDevelopment() {
    return const CacheConfig(
      provider: 'redis',
      host: 'localhost',
      port: 6379,
      password: '',
      database: 0,
      maxConnections: 5,
      defaultTtl: Duration(minutes: 30),
      enableLogging: true,
    );
  }

  /// Create memory cache configuration
  factory CacheConfig.memory() {
    return const CacheConfig(
      provider: 'memory',
      host: '',
      port: 0,
      password: '',
      database: 0,
      maxConnections: 1,
      defaultTtl: Duration(minutes: 15),
      enableLogging: false,
    );
  }

  /// Create configuration from environment variables
  factory CacheConfig.fromEnvironment() {
    return CacheConfig(
      provider: const String.fromEnvironment('CACHE_PROVIDER', defaultValue: 'memory'),
      host: const String.fromEnvironment('CACHE_HOST', defaultValue: 'localhost'),
      port: const int.fromEnvironment('CACHE_PORT', defaultValue: 6379),
      password: const String.fromEnvironment('CACHE_PASSWORD', defaultValue: ''),
      database: const int.fromEnvironment('CACHE_DATABASE', defaultValue: 0),
      maxConnections: const int.fromEnvironment('CACHE_MAX_CONNECTIONS', defaultValue: 10),
      defaultTtl: Duration(
        seconds: const int.fromEnvironment('CACHE_DEFAULT_TTL', defaultValue: 3600),
      ),
      enableLogging: const bool.fromEnvironment('CACHE_ENABLE_LOGGING', defaultValue: false),
    );
  }

  /// Check if using Redis
  bool get isRedis => provider == 'redis';

  /// Check if using memory cache
  bool get isMemory => provider == 'memory';

  /// Get connection string
  String get connectionString {
    if (isMemory) return 'memory://';
    return 'redis://${password.isNotEmpty ? ':$password@' : ''}$host:$port/$database';
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'provider': provider,
      'host': host,
      'port': port,
      'database': database,
      'max_connections': maxConnections,
      'default_ttl_seconds': defaultTtl.inSeconds,
      'enable_logging': enableLogging,
    };
  }

  @override
  String toString() {
    return 'CacheConfig(provider: $provider, host: $host, port: $port, database: $database)';
  }
}
