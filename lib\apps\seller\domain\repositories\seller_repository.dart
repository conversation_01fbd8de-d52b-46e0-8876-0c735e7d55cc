import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/seller.dart';

abstract class SellerRepository {
  Future<Seller?> getSeller(String id);
  Future<Seller> createSeller(Seller seller);
  Future<Seller> updateSeller(Seller seller);
  Future<void> deleteSeller(String id);
  Future<List<Seller>> getSellers();
  Future<List<Seller>> getSellersByCategory(String category);
  Future<List<Seller>> getSellersByLocation(
      double latitude, double longitude, double radius);
  Future<List<Seller>> getSellersByRating(double minRating);
  Future<List<Seller>> getSellersByStatus(bool isApproved);
  Future<List<Seller>> searchSellers(String query);
}

final sellerRepositoryProvider = Provider<SellerRepository>((ref) {
  throw UnimplementedError('SellerRepository not implemented');
});
