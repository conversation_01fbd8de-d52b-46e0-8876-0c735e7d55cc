import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shivish/shared/core/service_locator.dart';
import 'package:shivish/shared/core/theme/app_theme.dart';
import 'package:shivish/shared/core/theme/theme_provider.dart';
import 'package:shivish/shared/core/analytics/analytics_service.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/core/localization/l10n/app_localizations.dart';
import 'package:shivish/shared/core/config/app_config.dart';
import 'package:shivish/shared/core/error/error_widget.dart' as app_error;
import 'package:shivish/apps/admin/admin_routes.dart';
import 'package:shivish/apps/admin/bloc/auth/auth_bloc.dart';
import 'package:shivish/apps/admin/bloc/user_management/user_management_bloc.dart';
import 'package:shivish/apps/admin/bloc/user_management/user_management_event.dart';
import 'package:shivish/apps/admin/bloc/seller/seller_bloc.dart';
import 'package:shivish/apps/admin/bloc/priest/priest_bloc.dart';
import 'package:shivish/apps/admin/bloc/technician/technician_bloc.dart';
import 'package:shivish/apps/admin/bloc/event_management/admin_event_management_bloc.dart';
import 'package:shivish/apps/admin/bloc/event_management/admin_event_management_event.dart';
import 'package:shivish/apps/admin/bloc/media_management/admin_media_management_bloc.dart';
import 'package:shivish/apps/admin/bloc/media_management/admin_media_management_event_simple.dart';
import 'package:shivish/shared/services/user/user_service.dart';
import 'package:shivish/shared/services/event/event_service.dart';
import 'package:shivish/shared/services/media/media_service.dart';
import 'package:shivish/apps/admin/providers/system_config_provider_impl.dart';
import 'package:shivish/shared/ui_components/navigation/global_back_handler.dart';
import 'services/notification_popup_service.dart';

class AdminApp extends StatelessWidget {
  const AdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<SharedPreferences>(
      future: SharedPreferences.getInstance(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return MaterialApp(
            home: const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            ),
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
          );
        }

        if (snapshot.hasError) {
          return MaterialApp(
            home: Scaffold(
              body: Center(
                child: app_error.ErrorWidget(
                  error: snapshot.error,
                  onRetry: () {
                    // Retry loading SharedPreferences
                    SharedPreferences.getInstance();
                  },
                ),
              ),
            ),
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
          );
        }

        final prefs = snapshot.data!;

        return riverpod.ProviderScope(
          overrides: getSystemConfigProviderOverrides(),
          child: riverpod.Consumer(
            builder: (context, ref, child) {
              return AdminNotificationPopupInitializer(
                child: MultiProvider(
                  providers: [
                    ChangeNotifierProvider(create: (_) => ThemeProvider(prefs)),
                    Provider<AnalyticsService>(
                      create: (_) => serviceLocator<AnalyticsService>(),
                    ),
                    // Use AuthBloc that handles Firebase authentication
                    BlocProvider<AuthBloc>(
                      create: (_) =>
                          AuthBloc(serviceLocator<DatabaseService>()),
                      lazy:
                          false, // Initialize immediately to ensure Firebase auth happens
                    ),
                    // Provide UserManagementBloc for user management screens
                    BlocProvider<UserManagementBloc>(
                      create: (_) =>
                          UserManagementBloc(UserService())
                            ..add(const UserManagementEvent.loadUsers()),
                    ),
                    // Provide SellerBloc for seller management screens
                    BlocProvider<SellerBloc>(
                      create: (_) => serviceLocator<SellerBloc>(),
                    ),
                    // Provide PriestBloc for priest management screens
                    BlocProvider<PriestBloc>(
                      create: (_) => serviceLocator<PriestBloc>(),
                    ),
                    // Provide TechnicianBloc for technician management screens
                    BlocProvider<TechnicianBloc>(
                      create: (_) => serviceLocator<TechnicianBloc>(),
                    ),
                    // Provide AdminEventManagementBloc for event management screens
                    BlocProvider<AdminEventManagementBloc>(
                      create: (_) =>
                          AdminEventManagementBloc(
                            eventService: EventService(),
                          )..add(
                            const AdminEventManagementEvent.loadPendingEvents(),
                          ),
                    ),
                    // Provide AdminMediaManagementBloc for media management screens
                    BlocProvider<AdminMediaManagementBloc>(
                      create: (_) =>
                          AdminMediaManagementBloc(mediaService: MediaService())
                            ..add(const LoadPendingMediaEvent()),
                    ),
                    // We'll use the default AdminAuthNotifier from the provider
                  ],
                  child: Consumer<ThemeProvider>(
                    builder: (context, themeProvider, child) {
                      // Create the router without authentication
                      final router = AdminRoutes.createRouter(context);

                      return MaterialApp.router(
                        title: AppConfig.adminAppName,
                        theme: AppTheme.lightTheme,
                        darkTheme: AppTheme.darkTheme,
                        themeMode: themeProvider.isDarkMode
                            ? ThemeMode.dark
                            : ThemeMode.light,
                        routerConfig: router,
                        debugShowCheckedModeBanner: false,
                        localizationsDelegates:
                            AppLocalizations.localizationsDelegates,
                        supportedLocales: AppLocalizations.supportedLocales,
                        builder: (context, child) {
                          return GlobalBackHandler(
                            fallbackRoute: AdminRoutes.home,
                            child: ScrollConfiguration(
                              behavior: ScrollBehavior().copyWith(
                                physics: const BouncingScrollPhysics(),
                              ),
                              child: child!,
                            ),
                          );
                        },
                        onGenerateTitle: (context) => AppConfig.adminAppName,
                      );
                    },
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}
