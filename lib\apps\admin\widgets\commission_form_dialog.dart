import 'package:flutter/material.dart';
import 'package:shivish/shared/models/commission.dart';

class CommissionFormDialog extends StatefulWidget {
  final Commission? commission;
  final Function(Commission) onSubmit;

  const CommissionFormDialog({
    super.key,
    this.commission,
    required this.onSubmit,
  });

  @override
  State<CommissionFormDialog> createState() => _CommissionFormDialogState();
}

class _CommissionFormDialogState extends State<CommissionFormDialog> {
  late final _formKey = GlobalKey<FormState>();
  late final _categoryIdController = TextEditingController(
    text: widget.commission?.categoryId,
  );
  late final _percentageController = TextEditingController(
    text: widget.commission?.percentage.toString(),
  );
  late final _minimumAmountController = TextEditingController(
    text: widget.commission?.minimumAmount.toString(),
  );
  late final _maximumAmountController = TextEditingController(
    text: widget.commission?.maximumAmount.toString(),
  );
  late bool _isActive = widget.commission?.isActive ?? true;

  @override
  void dispose() {
    _categoryIdController.dispose();
    _percentageController.dispose();
    _minimumAmountController.dispose();
    _maximumAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
          widget.commission == null ? 'Add Commission' : 'Edit Commission'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _categoryIdController,
                decoration: const InputDecoration(labelText: 'Category ID'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a category ID';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _percentageController,
                decoration: const InputDecoration(labelText: 'Percentage'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a percentage';
                  }
                  final percentage = double.tryParse(value);
                  if (percentage == null ||
                      percentage < 0 ||
                      percentage > 100) {
                    return 'Please enter a valid percentage between 0 and 100';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _minimumAmountController,
                decoration: const InputDecoration(labelText: 'Minimum Amount'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a minimum amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount < 0) {
                    return 'Please enter a valid minimum amount';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _maximumAmountController,
                decoration: const InputDecoration(labelText: 'Maximum Amount'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a maximum amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount < 0) {
                    return 'Please enter a valid maximum amount';
                  }
                  final minimumAmount =
                      double.tryParse(_minimumAmountController.text);
                  if (minimumAmount != null && amount < minimumAmount) {
                    return 'Maximum amount must be greater than minimum amount';
                  }
                  return null;
                },
              ),
              SwitchListTile(
                title: const Text('Active'),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: _submitForm,
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      final commission = Commission(
        id: widget.commission?.id ?? '',
        categoryId: _categoryIdController.text,
        categoryName: _categoryIdController.text,
        percentage: double.parse(_percentageController.text),
        minimumAmount: double.parse(_minimumAmountController.text),
        maximumAmount: double.parse(_maximumAmountController.text),
        isActive: _isActive,
        createdAt: widget.commission?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );
      widget.onSubmit(commission);
    }
  }
}
