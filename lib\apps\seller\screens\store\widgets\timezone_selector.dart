import 'package:flutter/material.dart';
import 'package:timezone/timezone.dart' as tz;

class TimeZoneSelector extends StatelessWidget {
  final String currentTimeZone;
  final ValueChanged<String> onTimeZoneChanged;

  const TimeZoneSelector({
    super.key,
    required this.currentTimeZone,
    required this.onTimeZoneChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Time Zone',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: currentTimeZone,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: tz.timeZoneDatabase.locations.keys.map((String key) {
                return DropdownMenuItem<String>(
                  value: key,
                  child: Text(
                    key.replaceAll('_', ' '),
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  onTimeZoneChanged(newValue);
                }
              },
            ),
            const SizedBox(height: 8),
            Text(
              'Current time: ${_getCurrentTime(currentTimeZone)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  String _getCurrentTime(String timeZone) {
    final location = tz.getLocation(timeZone);
    final now = tz.TZDateTime.now(location);
    return '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
  }
}
