import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/models/technician/technician.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final technicianServiceProvider = Provider<TechnicianService>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return TechnicianService(databaseService, Supabase.instance.client);
});

class TechnicianService {
  final DatabaseService _databaseService;
  final SupabaseClient _supabase;
  static const String _collection = 'technicians';

  TechnicianService(this._databaseService, this._supabase);

  Future<Technician> createTechnician(Technician technician) async {
    try {
      final technicianData = technician.toJson();
      technicianData['created_at'] = DateTime.now().toIso8601String();
      technicianData['updated_at'] = DateTime.now().toIso8601String();
      technicianData['is_deleted'] = false;
      technicianData['verification_status'] = 'pending';
      technicianData['is_active'] = false;

      final result = await _databaseService.create(_collection, technicianData);
      return technician.copyWith(id: result['id'] as String);
    } catch (e) {
      debugPrint('Failed to create technician: $e');
      throw Exception('Failed to create technician: $e');
    }
  }

  Future<Technician?> getTechnician(String id) async {
    try {
      final data = await _databaseService.find(_collection, id);
      if (data == null) return null;
      return Technician.fromJson(data);
    } catch (e) {
      debugPrint('Failed to get technician: $e');
      throw Exception('Failed to get technician: $e');
    }
  }

  Future<void> updateTechnician(Technician technician) async {
    try {
      final technicianData = technician.toJson();
      technicianData['updated_at'] = DateTime.now().toIso8601String();

      await _databaseService.update(_collection, technician.id, technicianData);
    } catch (e) {
      debugPrint('Failed to update technician: $e');
      throw Exception('Failed to update technician: $e');
    }
  }

  Future<void> deleteTechnician(String id) async {
    try {
      await _databaseService.update(_collection, id, {
        'is_deleted': true,
        'deleted_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to delete technician: $e');
      throw Exception('Failed to delete technician: $e');
    }
  }

  Stream<List<Technician>> getTechnicians() {
    try {
      return _databaseService.watchCollection(
        _collection,
        where: 'is_deleted = ?',
        whereParams: [false],
        orderBy: 'created_at DESC',
      ).map((docs) => docs
          .map((doc) => Technician.fromJson(doc))
          .toList());
    } catch (e) {
      debugPrint('Error streaming technicians: $e');
      return Stream.value([]);
    }
  }

  Stream<Technician?> getCurrentTechnician() {
    return _supabase.auth.onAuthStateChange.asyncMap((authState) async {
      try {
        final user = authState.session?.user;
        if (user == null) return null;
        return await getTechnician(user.id);
      } catch (e) {
        debugPrint('Error getting current technician: $e');
        return null;
      }
    });
  }

  Future<Technician> login({
    required String email,
    required String password,
  }) async {
    try {
      final authResponse = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (authResponse.user == null) {
        throw Exception('Authentication failed');
      }

      final technician = await getTechnician(authResponse.user!.id);
      if (technician == null) {
        throw Exception('Technician profile not found');
      }

      // Update last login timestamp
      await _databaseService.update(_collection, technician.id, {
        'last_login_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      return technician;
    } catch (e) {
      debugPrint('Failed to login: $e');
      throw Exception('Failed to login: $e');
    }
  }

  Future<Technician> register({
    required String email,
    required String password,
    required Technician technician,
  }) async {
    try {
      // Validate input
      if (email.isEmpty || password.isEmpty) {
        throw Exception('Email and password are required');
      }

      if (password.length < 6) {
        throw Exception('Password must be at least 6 characters long');
      }

      final authResponse = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      if (authResponse.user == null) {
        throw Exception('Registration failed');
      }

      final newTechnician = technician.copyWith(
        id: authResponse.user!.id,
        email: email,
      );

      return await createTechnician(newTechnician);
    } catch (e) {
      debugPrint('Failed to register: $e');
      throw Exception('Failed to register: $e');
    }
  }

  Future<void> logout() async {
    try {
      await _supabase.auth.signOut();
    } catch (e) {
      debugPrint('Failed to logout: $e');
      throw Exception('Failed to logout: $e');
    }
  }

  Future<void> updateTechnicianStatus({
    required String technicianId,
    required String verificationStatus,
    required bool isActive,
  }) async {
    try {
      await _databaseService.update(_collection, technicianId, {
        'verification_status': verificationStatus,
        'is_active': isActive,
        'status_updated_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Failed to update technician status: $e');
      throw Exception('Failed to update technician status: $e');
    }
  }

  Future<List<Technician>> getTechniciansByStatus(String status) async {
    try {
      final data = await _databaseService.getAll(
        _collection,
        where: 'verification_status = ? AND is_deleted = ?',
        whereParams: [status, false],
        orderBy: 'created_at DESC',
      );
      return data.map((item) => Technician.fromJson(item)).toList();
    } catch (e) {
      debugPrint('Failed to get technicians by status: $e');
      return [];
    }
  }

  Future<int> getTechnicianCount() async {
    try {
      return await _databaseService.count(
        _collection,
        where: 'is_deleted = ?',
        whereParams: [false],
      );
    } catch (e) {
      debugPrint('Failed to get technician count: $e');
      return 0;
    }
  }
}
