import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../states/profile_state.dart';
import '../../../shared/repositories/profile_repository.dart';

final profileProvider =
    StateNotifierProvider<ProfileNotifier, AsyncValue<ProfileState>>((ref) {
  final repository = ref.watch(profileRepositoryProvider);
  return ProfileNotifier(repository);
});

class ProfileNotifier extends StateNotifier<AsyncValue<ProfileState>> {
  final ProfileRepository _repository;

  ProfileNotifier(this._repository) : super(const AsyncValue.loading()) {
    refreshProfile();
  }

  Future<void> refreshProfile() async {
    try {
      state = const AsyncValue.loading();
      final buyer = await _repository.getProfile();
      state = AsyncValue.data(ProfileState(buyer: buyer));
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateProfile(ProfileState newProfile) async {
    try {
      state = const AsyncValue.loading();
      await _repository.updateProfile(newProfile.buyer);
      state = AsyncValue.data(newProfile);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
