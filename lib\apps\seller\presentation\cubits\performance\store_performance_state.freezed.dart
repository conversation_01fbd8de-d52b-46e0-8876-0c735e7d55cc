// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'store_performance_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$StorePerformanceState {

 bool get isLoading; bool get hasError; String? get errorMessage; PerformanceMetrics? get metrics; String get timeRange;
/// Create a copy of StorePerformanceState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StorePerformanceStateCopyWith<StorePerformanceState> get copyWith => _$StorePerformanceStateCopyWithImpl<StorePerformanceState>(this as StorePerformanceState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StorePerformanceState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasError, hasError) || other.hasError == hasError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.metrics, metrics) || other.metrics == metrics)&&(identical(other.timeRange, timeRange) || other.timeRange == timeRange));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,hasError,errorMessage,metrics,timeRange);

@override
String toString() {
  return 'StorePerformanceState(isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, metrics: $metrics, timeRange: $timeRange)';
}


}

/// @nodoc
abstract mixin class $StorePerformanceStateCopyWith<$Res>  {
  factory $StorePerformanceStateCopyWith(StorePerformanceState value, $Res Function(StorePerformanceState) _then) = _$StorePerformanceStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, bool hasError, String? errorMessage, PerformanceMetrics? metrics, String timeRange
});


$PerformanceMetricsCopyWith<$Res>? get metrics;

}
/// @nodoc
class _$StorePerformanceStateCopyWithImpl<$Res>
    implements $StorePerformanceStateCopyWith<$Res> {
  _$StorePerformanceStateCopyWithImpl(this._self, this._then);

  final StorePerformanceState _self;
  final $Res Function(StorePerformanceState) _then;

/// Create a copy of StorePerformanceState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? hasError = null,Object? errorMessage = freezed,Object? metrics = freezed,Object? timeRange = null,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,metrics: freezed == metrics ? _self.metrics : metrics // ignore: cast_nullable_to_non_nullable
as PerformanceMetrics?,timeRange: null == timeRange ? _self.timeRange : timeRange // ignore: cast_nullable_to_non_nullable
as String,
  ));
}
/// Create a copy of StorePerformanceState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PerformanceMetricsCopyWith<$Res>? get metrics {
    if (_self.metrics == null) {
    return null;
  }

  return $PerformanceMetricsCopyWith<$Res>(_self.metrics!, (value) {
    return _then(_self.copyWith(metrics: value));
  });
}
}


/// Adds pattern-matching-related methods to [StorePerformanceState].
extension StorePerformanceStatePatterns on StorePerformanceState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _StorePerformanceState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _StorePerformanceState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _StorePerformanceState value)  $default,){
final _that = this;
switch (_that) {
case _StorePerformanceState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _StorePerformanceState value)?  $default,){
final _that = this;
switch (_that) {
case _StorePerformanceState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isLoading,  bool hasError,  String? errorMessage,  PerformanceMetrics? metrics,  String timeRange)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _StorePerformanceState() when $default != null:
return $default(_that.isLoading,_that.hasError,_that.errorMessage,_that.metrics,_that.timeRange);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isLoading,  bool hasError,  String? errorMessage,  PerformanceMetrics? metrics,  String timeRange)  $default,) {final _that = this;
switch (_that) {
case _StorePerformanceState():
return $default(_that.isLoading,_that.hasError,_that.errorMessage,_that.metrics,_that.timeRange);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isLoading,  bool hasError,  String? errorMessage,  PerformanceMetrics? metrics,  String timeRange)?  $default,) {final _that = this;
switch (_that) {
case _StorePerformanceState() when $default != null:
return $default(_that.isLoading,_that.hasError,_that.errorMessage,_that.metrics,_that.timeRange);case _:
  return null;

}
}

}

/// @nodoc


class _StorePerformanceState implements StorePerformanceState {
  const _StorePerformanceState({this.isLoading = false, this.hasError = false, this.errorMessage, this.metrics, this.timeRange = 'week'});
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool hasError;
@override final  String? errorMessage;
@override final  PerformanceMetrics? metrics;
@override@JsonKey() final  String timeRange;

/// Create a copy of StorePerformanceState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StorePerformanceStateCopyWith<_StorePerformanceState> get copyWith => __$StorePerformanceStateCopyWithImpl<_StorePerformanceState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StorePerformanceState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasError, hasError) || other.hasError == hasError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.metrics, metrics) || other.metrics == metrics)&&(identical(other.timeRange, timeRange) || other.timeRange == timeRange));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,hasError,errorMessage,metrics,timeRange);

@override
String toString() {
  return 'StorePerformanceState(isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, metrics: $metrics, timeRange: $timeRange)';
}


}

/// @nodoc
abstract mixin class _$StorePerformanceStateCopyWith<$Res> implements $StorePerformanceStateCopyWith<$Res> {
  factory _$StorePerformanceStateCopyWith(_StorePerformanceState value, $Res Function(_StorePerformanceState) _then) = __$StorePerformanceStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, bool hasError, String? errorMessage, PerformanceMetrics? metrics, String timeRange
});


@override $PerformanceMetricsCopyWith<$Res>? get metrics;

}
/// @nodoc
class __$StorePerformanceStateCopyWithImpl<$Res>
    implements _$StorePerformanceStateCopyWith<$Res> {
  __$StorePerformanceStateCopyWithImpl(this._self, this._then);

  final _StorePerformanceState _self;
  final $Res Function(_StorePerformanceState) _then;

/// Create a copy of StorePerformanceState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? hasError = null,Object? errorMessage = freezed,Object? metrics = freezed,Object? timeRange = null,}) {
  return _then(_StorePerformanceState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,metrics: freezed == metrics ? _self.metrics : metrics // ignore: cast_nullable_to_non_nullable
as PerformanceMetrics?,timeRange: null == timeRange ? _self.timeRange : timeRange // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

/// Create a copy of StorePerformanceState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PerformanceMetricsCopyWith<$Res>? get metrics {
    if (_self.metrics == null) {
    return null;
  }

  return $PerformanceMetricsCopyWith<$Res>(_self.metrics!, (value) {
    return _then(_self.copyWith(metrics: value));
  });
}
}

// dart format on
