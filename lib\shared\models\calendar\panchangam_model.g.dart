// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'panchangam_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PanchangamModel _$PanchangamModelFromJson(
  Map<String, dynamic> json,
) => _PanchangamModel(
  date: DateTime.parse(json['date'] as String),
  isAuspicious: json['isAuspicious'] as bool,
  tithi: json['tithi'] as String,
  nakshatra: json['nakshatra'] as String,
  yoga: json['yoga'] as String,
  karana: json['karana'] as String,
  sunrise: json['sunrise'] as String,
  sunset: json['sunset'] as String,
  moonrise: json['moonrise'] as String,
  moonset: json['moonset'] as String,
  rahuKalam: json['rahuKalam'] as String,
  gulikaKalam: json['gulikaKalam'] as String,
  yamagandam: json['yamagandam'] as String,
  abhijitMuhurta: json['abhijitMuhurta'] as String,
  festivals:
      (json['festivals'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  vratas:
      (json['vratas'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  specialEvents:
      (json['specialEvents'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
);

Map<String, dynamic> _$PanchangamModelToJson(_PanchangamModel instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'isAuspicious': instance.isAuspicious,
      'tithi': instance.tithi,
      'nakshatra': instance.nakshatra,
      'yoga': instance.yoga,
      'karana': instance.karana,
      'sunrise': instance.sunrise,
      'sunset': instance.sunset,
      'moonrise': instance.moonrise,
      'moonset': instance.moonset,
      'rahuKalam': instance.rahuKalam,
      'gulikaKalam': instance.gulikaKalam,
      'yamagandam': instance.yamagandam,
      'abhijitMuhurta': instance.abhijitMuhurta,
      'festivals': instance.festivals,
      'vratas': instance.vratas,
      'specialEvents': instance.specialEvents,
    };
