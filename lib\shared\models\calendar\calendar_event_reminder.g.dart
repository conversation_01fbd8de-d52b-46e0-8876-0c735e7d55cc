// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'calendar_event_reminder.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CalendarEventReminder _$CalendarEventReminderFromJson(
  Map<String, dynamic> json,
) => _CalendarEventReminder(
  type: $enumDecode(_$ReminderTypeEnumMap, json['type']),
  timeBeforeEvent: (json['timeBeforeEvent'] as num).toInt(),
  isEnabled: json['isEnabled'] as bool? ?? true,
  customMessage: json['customMessage'] as String?,
);

Map<String, dynamic> _$CalendarEventReminderToJson(
  _CalendarEventReminder instance,
) => <String, dynamic>{
  'type': _$ReminderTypeEnumMap[instance.type]!,
  'timeBeforeEvent': instance.timeBeforeEvent,
  'isEnabled': instance.isEnabled,
  'customMessage': instance.customMessage,
};

const _$ReminderTypeEnumMap = {
  ReminderType.notification: 'notification',
  ReminderType.email: 'email',
  ReminderType.sms: 'sms',
  ReminderType.whatsapp: 'whatsapp',
};
