import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../shared/ui_components/errors/error_message.dart';
import '../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../shared/core/navigation/shells/admin_shell.dart';
import '../bloc/home/<USER>';
import '../bloc/home/<USER>';
import '../../../shared/models/notification/notification_model.dart';
import '../../../shared/models/notification/notification_status.dart';
import '../../../shared/models/notification/notification_type.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AdminShell(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Notifications'),
          actions: [
            IconButton(
              icon: const Icon(Icons.delete_outline),
              onPressed: () {
                context.read<HomeCubit>().markNotificationsAsRead();
              },
            ),
          ],
        ),
        body: SafeArea(
          child: BlocBuilder<HomeCubit, HomeState>(
            builder: (context, state) {
              if (state.isLoading) {
                return const LoadingIndicator();
              }

              if (state.error != null) {
                return ErrorMessage(message: state.error!);
              }

              return StreamBuilder<List<NotificationModel>>(
                stream: context.read<HomeCubit>().getUserNotifications(),
                builder: (context, snapshot) {
                  if (snapshot.hasError) {
                    return ErrorMessage(message: snapshot.error.toString());
                  }

                  if (!snapshot.hasData) {
                    return const LoadingIndicator();
                  }

                  final notifications = snapshot.data!;

                  if (notifications.isEmpty) {
                    return const Center(child: Text('No notifications'));
                  }

                  return ListView.builder(
                    itemCount: notifications.length,
                    itemBuilder: (context, index) {
                      final notification = notifications[index];
                      return _NotificationCard(
                        notification: notification,
                        onMarkAsRead: () {
                          context.read<HomeCubit>().markNotificationAsRead(
                            notification.id,
                          );
                        },
                      );
                    },
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }
}

class _NotificationCard extends StatelessWidget {
  const _NotificationCard({
    required this.notification,
    required this.onMarkAsRead,
  });

  final NotificationModel notification;
  final VoidCallback onMarkAsRead;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: notification.status == NotificationStatus.unread
              ? Theme.of(context).colorScheme.primary
              : Colors.grey,
          child: Icon(
            _getNotificationIcon(notification.type),
            color: Colors.white,
          ),
        ),
        title: Text(notification.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.body),
            const SizedBox(height: 4),
            Text(
              timeago.format(notification.createdAt),
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        trailing: notification.status == NotificationStatus.unread
            ? IconButton(icon: const Icon(Icons.check), onPressed: onMarkAsRead)
            : null,
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.order:
        return Icons.shopping_cart;
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.system:
        return Icons.info;
      case NotificationType.booking:
        return Icons.calendar_today;
      case NotificationType.event:
        return Icons.event;
      case NotificationType.chat:
        return Icons.chat;
      case NotificationType.verification:
        return Icons.verified_user;
      case NotificationType.general:
        return Icons.notifications;
      case NotificationType.deliveryRequest:
        return Icons.local_shipping;
      case NotificationType.statusUpdate:
        return Icons.update;
      case NotificationType.earnings:
        return Icons.monetization_on;
      default:
        // Handle all other notification types (buyer-specific, etc.)
        return Icons.notifications;
    }
  }
}
