import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:geolocator/geolocator.dart';
import '../../../../shared/models/temple/temple_model.dart';
import '../../providers/temple_provider.dart';
import '../../widgets/temple/temple_card.dart';
import '../../widgets/temple/temple_search_bar.dart';
import '../../widgets/temple/temple_filters.dart';

import '../../../../shared/widgets/traditional/traditional_pattern_widget.dart';
import '../../../../shared/theme/traditional_colors.dart';

class TemplesScreen extends ConsumerStatefulWidget {
  const TemplesScreen({super.key});

  @override
  ConsumerState<TemplesScreen> createState() => _TemplesScreenState();
}

class _TemplesScreenState extends ConsumerState<TemplesScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  Position? _currentPosition;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _getCurrentLocation();
    _loadNearbyTemples();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    try {
      final position = await Geolocator.getCurrentPosition();
      setState(() {
        _currentPosition = position;
      });
      ref
          .read(templeProvider.notifier)
          .updateUserLocation(position.latitude, position.longitude);
    } catch (e) {
      // Handle location permission denied or other errors
      debugPrint('Error getting location: $e');
    }
  }

  void _loadNearbyTemples() {
    ref.read(templeProvider.notifier).loadNearbyTemples();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final templeState = ref.watch(templeProvider);

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildAppBar(context, theme),
          _buildSearchSection(context, theme),
          _buildTabSection(context, theme),
          _buildContent(context, theme, templeState),
        ],
      ),
    );
  }

  Widget _buildAppBar(BuildContext context, ThemeData theme) {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: TraditionalColors.templeOrange,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'श्री मंदिर',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 20,
            shadows: [
              Shadow(
                offset: Offset(1, 1),
                blurRadius: 3,
                color: Colors.black26,
              ),
            ],
          ),
        ),
        background: Container(
          decoration: BoxDecoration(gradient: TraditionalColors.templeGradient),
          child: Stack(
            children: [
              // Traditional pattern overlay
              Positioned.fill(
                child: TraditionalPatternWidget(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.white,
                  opacity: 0.1,
                  pattern: PatternType.mandala,
                ),
              ),
              // Additional patterns
              Positioned(
                top: 20,
                left: 20,
                child: TraditionalPatternWidget(
                  width: 60,
                  height: 60,
                  color: Colors.white,
                  opacity: 0.2,
                  pattern: PatternType.lotus,
                ),
              ),
              Positioned(
                bottom: 20,
                right: 100,
                child: TraditionalPatternWidget(
                  width: 80,
                  height: 80,
                  color: Colors.white,
                  opacity: 0.15,
                  pattern: PatternType.om,
                ),
              ),
              // Temple silhouette
              Positioned(
                right: 20,
                bottom: 20,
                child: Icon(
                  Icons.temple_hindu,
                  size: 80,
                  color: Colors.white.withValues(alpha: 0.3),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications, color: Colors.white),
          onPressed: () {
            // Show temple notifications
          },
        ),
        IconButton(
          icon: const Icon(Icons.location_on, color: Colors.white),
          onPressed: () {
            _getCurrentLocation();
          },
        ),
      ],
    );
  }

  Widget _buildSearchSection(BuildContext context, ThemeData theme) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(20),
            bottomRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            TempleSearchBar(
              controller: _searchController,
              onSearch: (query) {
                ref.read(templeProvider.notifier).searchTemples(query);
              },
              onFilterTap: () {
                _showFilters(context);
              },
            ),
            const SizedBox(height: 16),
            _buildQuickFilters(context, theme),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickFilters(BuildContext context, ThemeData theme) {
    final quickFilters = [
      {'label': 'Nearby', 'icon': Icons.location_on},
      {'label': 'Famous', 'icon': Icons.star},
      {'label': 'Pilgrimage', 'icon': Icons.temple_hindu},
      {'label': 'Open Now', 'icon': Icons.access_time},
    ];

    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: quickFilters.length,
        itemBuilder: (context, index) {
          final filter = quickFilters[index];
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              avatar: Icon(
                filter['icon'] as IconData,
                size: 16,
                color: Colors.orange.shade700,
              ),
              label: Text(
                filter['label'] as String,
                style: TextStyle(
                  color: Colors.orange.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
              backgroundColor: Colors.white,
              selectedColor: Colors.orange.shade100,
              onSelected: (selected) {
                _applyQuickFilter(filter['label'] as String);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildTabSection(BuildContext context, ThemeData theme) {
    return SliverToBoxAdapter(
      child: Container(
        color: Colors.white,
        child: TabBar(
          controller: _tabController,
          labelColor: Colors.orange.shade700,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.orange.shade700,
          tabs: const [
            Tab(text: 'Nearby'),
            Tab(text: 'Popular'),
            Tab(text: 'Pilgrimage'),
            Tab(text: 'All'),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    ThemeData theme,
    dynamic templeState,
  ) {
    return SliverFillRemaining(
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildTempleList(templeState.nearbyTemples ?? []),
          _buildTempleList(templeState.popularTemples ?? []),
          _buildTempleList(templeState.pilgrimageTemples ?? []),
          _buildTempleList(templeState.allTemples ?? []),
        ],
      ),
    );
  }

  Widget _buildTempleList(List<Temple> temples) {
    if (temples.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.temple_hindu, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No temples found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Try adjusting your search or filters',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: temples.length,
      itemBuilder: (context, index) {
        final temple = temples[index];
        return TempleCard(
          temple: temple,
          onTap: () {
            context.push('/buyer/temples/${temple.id}');
          },
          currentPosition: _currentPosition,
        );
      },
    );
  }

  void _showFilters(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => TempleFilters(
        onApplyFilters: (filters) {
          ref.read(templeProvider.notifier).applyFilters(filters);
        },
      ),
    );
  }

  void _applyQuickFilter(String filterType) {
    switch (filterType) {
      case 'Nearby':
        ref.read(templeProvider.notifier).loadNearbyTemples();
        break;
      case 'Famous':
        ref.read(templeProvider.notifier).loadFamousTemples();
        break;
      case 'Pilgrimage':
        ref.read(templeProvider.notifier).loadPilgrimageTemples();
        break;
      case 'Open Now':
        ref.read(templeProvider.notifier).loadOpenTemples();
        break;
    }
  }
}
