// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'server_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ServerConfig _$ServerConfigFromJson(
  Map<String, dynamic> json,
) => _ServerConfig(
  environment: json['environment'] as String,
  awsRegion: json['awsRegion'] as String,
  datacenterIp: json['datacenterIp'] as String,
  databaseUrl: json['databaseUrl'] as String,
  redisUrl: json['redisUrl'] as String,
  maxConnections: (json['maxConnections'] as num).toInt(),
  timeout: (json['timeout'] as num).toInt(),
  awsStatus: $enumDecode(_$ServerStateEnumMap, json['awsStatus']),
  datacenterStatus: $enumDecode(_$ServerStateEnumMap, json['datacenterStatus']),
  autoScaling: json['autoScaling'] as bool,
  loadBalancing: json['loadBalancing'] as bool,
  cachingEnabled: json['cachingEnabled'] as bool,
  sslEnabled: json['sslEnabled'] as bool,
  rateLimitingEnabled: json['rateLimitingEnabled'] as bool,
  ddosProtection: json['ddosProtection'] as bool,
  lastUpdated: DateTime.parse(json['lastUpdated'] as String),
  customSettings: json['customSettings'] as Map<String, dynamic>? ?? const {},
  awsInstances:
      (json['awsInstances'] as List<dynamic>?)
          ?.map((e) => ServerInstance.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  datacenterInstances:
      (json['datacenterInstances'] as List<dynamic>?)
          ?.map((e) => ServerInstance.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
);

Map<String, dynamic> _$ServerConfigToJson(_ServerConfig instance) =>
    <String, dynamic>{
      'environment': instance.environment,
      'awsRegion': instance.awsRegion,
      'datacenterIp': instance.datacenterIp,
      'databaseUrl': instance.databaseUrl,
      'redisUrl': instance.redisUrl,
      'maxConnections': instance.maxConnections,
      'timeout': instance.timeout,
      'awsStatus': _$ServerStateEnumMap[instance.awsStatus]!,
      'datacenterStatus': _$ServerStateEnumMap[instance.datacenterStatus]!,
      'autoScaling': instance.autoScaling,
      'loadBalancing': instance.loadBalancing,
      'cachingEnabled': instance.cachingEnabled,
      'sslEnabled': instance.sslEnabled,
      'rateLimitingEnabled': instance.rateLimitingEnabled,
      'ddosProtection': instance.ddosProtection,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'customSettings': instance.customSettings,
      'awsInstances': instance.awsInstances.map((e) => e.toJson()).toList(),
      'datacenterInstances': instance.datacenterInstances
          .map((e) => e.toJson())
          .toList(),
    };

const _$ServerStateEnumMap = {
  ServerState.online: 'online',
  ServerState.offline: 'offline',
  ServerState.maintenance: 'maintenance',
};

_ServerInstance _$ServerInstanceFromJson(Map<String, dynamic> json) =>
    _ServerInstance(
      id: json['id'] as String,
      name: json['name'] as String,
      ipAddress: json['ipAddress'] as String,
      port: (json['port'] as num).toInt(),
      type: $enumDecode(_$ServerInstanceTypeEnumMap, json['type']),
      status: $enumDecode(_$ServerStateEnumMap, json['status']),
      metrics: ServerMetrics.fromJson(json['metrics'] as Map<String, dynamic>),
      lastHealthCheck: DateTime.parse(json['lastHealthCheck'] as String),
      region: json['region'] as String? ?? 'Unknown',
      tags:
          (json['tags'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
    );

Map<String, dynamic> _$ServerInstanceToJson(_ServerInstance instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'ipAddress': instance.ipAddress,
      'port': instance.port,
      'type': _$ServerInstanceTypeEnumMap[instance.type]!,
      'status': _$ServerStateEnumMap[instance.status]!,
      'metrics': instance.metrics.toJson(),
      'lastHealthCheck': instance.lastHealthCheck.toIso8601String(),
      'region': instance.region,
      'tags': instance.tags,
    };

const _$ServerInstanceTypeEnumMap = {
  ServerInstanceType.apiGateway: 'apiGateway',
  ServerInstanceType.authService: 'authService',
  ServerInstanceType.businessLogic: 'businessLogic',
  ServerInstanceType.database: 'database',
  ServerInstanceType.fileStorage: 'fileStorage',
  ServerInstanceType.paymentProcessor: 'paymentProcessor',
  ServerInstanceType.analytics: 'analytics',
  ServerInstanceType.backgroundJobs: 'backgroundJobs',
};

_ServerMetrics _$ServerMetricsFromJson(Map<String, dynamic> json) =>
    _ServerMetrics(
      cpuUsage: (json['cpuUsage'] as num).toDouble(),
      memoryUsage: (json['memoryUsage'] as num).toDouble(),
      diskUsage: (json['diskUsage'] as num).toDouble(),
      networkIn: (json['networkIn'] as num).toDouble(),
      networkOut: (json['networkOut'] as num).toDouble(),
      activeConnections: (json['activeConnections'] as num).toInt(),
      requestsPerSecond: (json['requestsPerSecond'] as num).toInt(),
      averageResponseTime: (json['averageResponseTime'] as num).toDouble(),
      errorRate: (json['errorRate'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$ServerMetricsToJson(_ServerMetrics instance) =>
    <String, dynamic>{
      'cpuUsage': instance.cpuUsage,
      'memoryUsage': instance.memoryUsage,
      'diskUsage': instance.diskUsage,
      'networkIn': instance.networkIn,
      'networkOut': instance.networkOut,
      'activeConnections': instance.activeConnections,
      'requestsPerSecond': instance.requestsPerSecond,
      'averageResponseTime': instance.averageResponseTime,
      'errorRate': instance.errorRate,
      'timestamp': instance.timestamp.toIso8601String(),
    };

_AutoScalingConfig _$AutoScalingConfigFromJson(Map<String, dynamic> json) =>
    _AutoScalingConfig(
      enabled: json['enabled'] as bool,
      minInstances: (json['minInstances'] as num).toInt(),
      maxInstances: (json['maxInstances'] as num).toInt(),
      cpuThresholdUp: (json['cpuThresholdUp'] as num).toDouble(),
      cpuThresholdDown: (json['cpuThresholdDown'] as num).toDouble(),
      memoryThresholdUp: (json['memoryThresholdUp'] as num).toDouble(),
      memoryThresholdDown: (json['memoryThresholdDown'] as num).toDouble(),
      scaleUpCooldown: (json['scaleUpCooldown'] as num).toInt(),
      scaleDownCooldown: (json['scaleDownCooldown'] as num).toInt(),
      customPolicies:
          (json['customPolicies'] as List<dynamic>?)
              ?.map((e) => ScalingPolicy.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$AutoScalingConfigToJson(_AutoScalingConfig instance) =>
    <String, dynamic>{
      'enabled': instance.enabled,
      'minInstances': instance.minInstances,
      'maxInstances': instance.maxInstances,
      'cpuThresholdUp': instance.cpuThresholdUp,
      'cpuThresholdDown': instance.cpuThresholdDown,
      'memoryThresholdUp': instance.memoryThresholdUp,
      'memoryThresholdDown': instance.memoryThresholdDown,
      'scaleUpCooldown': instance.scaleUpCooldown,
      'scaleDownCooldown': instance.scaleDownCooldown,
      'customPolicies': instance.customPolicies.map((e) => e.toJson()).toList(),
    };

_ScalingPolicy _$ScalingPolicyFromJson(Map<String, dynamic> json) =>
    _ScalingPolicy(
      name: json['name'] as String,
      metric: $enumDecode(_$ScalingMetricEnumMap, json['metric']),
      comparison: $enumDecode(_$ScalingComparisonEnumMap, json['comparison']),
      threshold: (json['threshold'] as num).toDouble(),
      action: $enumDecode(_$ScalingActionEnumMap, json['action']),
      cooldownSeconds: (json['cooldownSeconds'] as num).toInt(),
    );

Map<String, dynamic> _$ScalingPolicyToJson(_ScalingPolicy instance) =>
    <String, dynamic>{
      'name': instance.name,
      'metric': _$ScalingMetricEnumMap[instance.metric]!,
      'comparison': _$ScalingComparisonEnumMap[instance.comparison]!,
      'threshold': instance.threshold,
      'action': _$ScalingActionEnumMap[instance.action]!,
      'cooldownSeconds': instance.cooldownSeconds,
    };

const _$ScalingMetricEnumMap = {
  ScalingMetric.cpuUsage: 'cpuUsage',
  ScalingMetric.memoryUsage: 'memoryUsage',
  ScalingMetric.diskUsage: 'diskUsage',
  ScalingMetric.networkIn: 'networkIn',
  ScalingMetric.networkOut: 'networkOut',
  ScalingMetric.activeConnections: 'activeConnections',
  ScalingMetric.requestsPerSecond: 'requestsPerSecond',
  ScalingMetric.responseTime: 'responseTime',
  ScalingMetric.errorRate: 'errorRate',
};

const _$ScalingComparisonEnumMap = {
  ScalingComparison.greaterThan: 'greaterThan',
  ScalingComparison.lessThan: 'lessThan',
  ScalingComparison.greaterThanOrEqual: 'greaterThanOrEqual',
  ScalingComparison.lessThanOrEqual: 'lessThanOrEqual',
};

const _$ScalingActionEnumMap = {
  ScalingAction.scaleUp: 'scaleUp',
  ScalingAction.scaleDown: 'scaleDown',
  ScalingAction.sendAlert: 'sendAlert',
  ScalingAction.runScript: 'runScript',
};

_ConfigTestResult _$ConfigTestResultFromJson(Map<String, dynamic> json) =>
    _ConfigTestResult(
      success: json['success'] as bool,
      error: json['error'] as String?,
      details: json['details'] as Map<String, dynamic>? ?? const {},
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$ConfigTestResultToJson(_ConfigTestResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'error': instance.error,
      'details': instance.details,
      'timestamp': instance.timestamp?.toIso8601String(),
    };

_DatabaseConfig _$DatabaseConfigFromJson(Map<String, dynamic> json) =>
    _DatabaseConfig(
      host: json['host'] as String,
      port: (json['port'] as num).toInt(),
      database: json['database'] as String,
      username: json['username'] as String,
      password: json['password'] as String,
      maxConnections: (json['maxConnections'] as num).toInt(),
      connectionTimeout: (json['connectionTimeout'] as num).toInt(),
      sslEnabled: json['sslEnabled'] as bool,
      type: json['type'] as String? ?? 'postgresql',
      additionalParams:
          json['additionalParams'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$DatabaseConfigToJson(_DatabaseConfig instance) =>
    <String, dynamic>{
      'host': instance.host,
      'port': instance.port,
      'database': instance.database,
      'username': instance.username,
      'password': instance.password,
      'maxConnections': instance.maxConnections,
      'connectionTimeout': instance.connectionTimeout,
      'sslEnabled': instance.sslEnabled,
      'type': instance.type,
      'additionalParams': instance.additionalParams,
    };

_RedisConfig _$RedisConfigFromJson(Map<String, dynamic> json) => _RedisConfig(
  host: json['host'] as String,
  port: (json['port'] as num).toInt(),
  password: json['password'] as String,
  database: (json['database'] as num).toInt(),
  maxConnections: (json['maxConnections'] as num).toInt(),
  connectionTimeout: (json['connectionTimeout'] as num).toInt(),
  clusterMode: json['clusterMode'] as bool? ?? false,
  clusterNodes:
      (json['clusterNodes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
);

Map<String, dynamic> _$RedisConfigToJson(_RedisConfig instance) =>
    <String, dynamic>{
      'host': instance.host,
      'port': instance.port,
      'password': instance.password,
      'database': instance.database,
      'maxConnections': instance.maxConnections,
      'connectionTimeout': instance.connectionTimeout,
      'clusterMode': instance.clusterMode,
      'clusterNodes': instance.clusterNodes,
    };
