import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/domain/models/payment_model.dart';
import 'package:shivish/apps/seller/presentation/providers/payment_settings_provider.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';

class BankAccountSettingsScreen extends ConsumerStatefulWidget {
  const BankAccountSettingsScreen({super.key});

  @override
  ConsumerState<BankAccountSettingsScreen> createState() => _BankAccountSettingsScreenState();
}

class _BankAccountSettingsScreenState extends ConsumerState<BankAccountSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _bankNameController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _accountHolderNameController = TextEditingController();
  final _ifscCodeController = TextEditingController();
  bool _isLoading = false;
  String? _error;
  List<BankAccountModel> _bankAccounts = [];

  @override
  void initState() {
    super.initState();
    _loadBankAccounts();
  }

  @override
  void dispose() {
    _bankNameController.dispose();
    _accountNumberController.dispose();
    _accountHolderNameController.dispose();
    _ifscCodeController.dispose();
    super.dispose();
  }

  Future<void> _loadBankAccounts() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final accounts = await ref.read(bankAccountsProvider.future);
      setState(() {
        _bankAccounts = accounts;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load bank accounts: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showAddAccountDialog() {
    // Clear form fields
    _bankNameController.clear();
    _accountNumberController.clear();
    _accountHolderNameController.clear();
    _ifscCodeController.clear();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Bank Account'),
        content: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppTextField(
                  controller: _accountHolderNameController,
                  label: 'Account Holder Name',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter account holder name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                AppTextField(
                  controller: _bankNameController,
                  label: 'Bank Name',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter bank name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                AppTextField(
                  controller: _accountNumberController,
                  label: 'Account Number',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter account number';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                AppTextField(
                  controller: _ifscCodeController,
                  label: 'IFSC Code',
                  textCapitalization: TextCapitalization.characters,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter IFSC code';
                    }
                    if (!RegExp(r'^[A-Z]{4}0[A-Z0-9]{6}$').hasMatch(value)) {
                      return 'Please enter a valid IFSC code';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: _addBankAccount,
            child: const Text('Add Account'),
          ),
        ],
      ),
    );
  }

  Future<void> _addBankAccount() async {
    if (!_formKey.currentState!.validate()) return;

    Navigator.pop(context); // Close dialog

    setState(() {
      _isLoading = true;
    });

    try {
      final account = BankAccountModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        accountNumber: _accountNumberController.text.trim(),
        bankName: _bankNameController.text.trim(),
        accountHolderName: _accountHolderNameController.text.trim(),
        ifscCode: _ifscCodeController.text.trim(),
      );

      await ref.read(paymentSettingsProvider.notifier).addBankAccount(account);
      
      // Reload accounts
      await _loadBankAccounts();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bank account added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to add bank account: $e';
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add bank account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteBankAccount(String accountId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(paymentSettingsProvider.notifier).deleteBankAccount(accountId);
      
      // Reload accounts
      await _loadBankAccounts();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bank account deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to delete bank account: $e';
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete bank account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _setPrimaryAccount(String accountId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(paymentSettingsProvider.notifier).setPrimaryBankAccount(accountId);
      
      // Reload accounts
      await _loadBankAccounts();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Primary bank account updated'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to update primary account: $e';
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update primary account: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        appBar: AppToolbar(
          title: 'Bank Accounts',
        ),
        body: Center(
          child: LoadingIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: const AppToolbar(
          title: 'Bank Accounts',
        ),
        body: ErrorMessage(
          message: _error!,
          onRetry: _loadBankAccounts,
        ),
      );
    }

    return Scaffold(
      appBar: const AppToolbar(
        title: 'Bank Accounts',
      ),
      body: _bankAccounts.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'No bank accounts added yet',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _showAddAccountDialog,
                    icon: const Icon(Icons.add),
                    label: const Text('Add Bank Account'),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _bankAccounts.length,
              itemBuilder: (context, index) {
                final account = _bankAccounts[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: ListTile(
                    title: Text(account.bankName),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(account.accountHolderName),
                        Text('A/C: ${account.accountNumber}'),
                        Text('IFSC: ${account.ifscCode}'),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (account.isPrimary)
                          const Chip(
                            label: Text('Primary'),
                            backgroundColor: Colors.green,
                            labelStyle: TextStyle(color: Colors.white),
                          )
                        else
                          IconButton(
                            icon: const Icon(Icons.check_circle_outline),
                            tooltip: 'Set as Primary',
                            onPressed: () => _setPrimaryAccount(account.id),
                          ),
                        IconButton(
                          icon: const Icon(Icons.delete),
                          tooltip: 'Delete Account',
                          onPressed: () => _deleteBankAccount(account.id),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddAccountDialog,
        child: const Icon(Icons.add),
      ),
    );
  }
}
