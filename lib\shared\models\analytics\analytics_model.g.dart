// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AnalyticsData _$AnalyticsDataFromJson(Map<String, dynamic> json) =>
    _AnalyticsData(
      totalSales: (json['totalSales'] as num).toDouble(),
      totalOrders: (json['totalOrders'] as num).toInt(),
      averageOrderValue: (json['averageOrderValue'] as num).toDouble(),
      salesData: (json['salesData'] as List<dynamic>)
          .map((e) => SalesDataPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      topProducts: (json['topProducts'] as List<dynamic>)
          .map((e) => TopProduct.fromJson(e as Map<String, dynamic>))
          .toList(),
      customerMetrics: CustomerMetrics.fromJson(
        json['customerMetrics'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$AnalyticsDataToJson(_AnalyticsData instance) =>
    <String, dynamic>{
      'totalSales': instance.totalSales,
      'totalOrders': instance.totalOrders,
      'averageOrderValue': instance.averageOrderValue,
      'salesData': instance.salesData.map((e) => e.toJson()).toList(),
      'topProducts': instance.topProducts.map((e) => e.toJson()).toList(),
      'customerMetrics': instance.customerMetrics.toJson(),
    };

_SalesDataPoint _$SalesDataPointFromJson(Map<String, dynamic> json) =>
    _SalesDataPoint(
      date: json['date'] as String,
      amount: (json['amount'] as num).toDouble(),
    );

Map<String, dynamic> _$SalesDataPointToJson(_SalesDataPoint instance) =>
    <String, dynamic>{'date': instance.date, 'amount': instance.amount};

_TopProduct _$TopProductFromJson(Map<String, dynamic> json) => _TopProduct(
  id: json['id'] as String,
  name: json['name'] as String,
  imageUrl: json['imageUrl'] as String,
  quantity: (json['quantity'] as num).toInt(),
  revenue: (json['revenue'] as num).toDouble(),
);

Map<String, dynamic> _$TopProductToJson(_TopProduct instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'imageUrl': instance.imageUrl,
      'quantity': instance.quantity,
      'revenue': instance.revenue,
    };

_CustomerMetrics _$CustomerMetricsFromJson(Map<String, dynamic> json) =>
    _CustomerMetrics(
      newCustomers: (json['newCustomers'] as num).toInt(),
      repeatCustomers: (json['repeatCustomers'] as num).toInt(),
      customerRetentionRate: (json['customerRetentionRate'] as num).toDouble(),
      customerSatisfaction: (json['customerSatisfaction'] as num).toDouble(),
    );

Map<String, dynamic> _$CustomerMetricsToJson(_CustomerMetrics instance) =>
    <String, dynamic>{
      'newCustomers': instance.newCustomers,
      'repeatCustomers': instance.repeatCustomers,
      'customerRetentionRate': instance.customerRetentionRate,
      'customerSatisfaction': instance.customerSatisfaction,
    };
