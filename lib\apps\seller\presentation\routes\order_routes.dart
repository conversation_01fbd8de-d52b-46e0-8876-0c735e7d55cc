import 'package:flutter/material.dart';
import 'package:shivish/apps/seller/screens/orders/order_details_screen.dart';
import 'package:shivish/apps/seller/screens/orders/orders_screen.dart';

class OrderRoutes {
  static const String orders = '/orders';
  static const String orderDetails = '/orders/:id';

  static Map<String, WidgetBuilder> getRoutes() {
    return {
      orders: (context) => const OrdersScreen(),
      orderDetails: (context) {
        final args =
            ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
        return OrderDetailsScreen(orderId: args['orderId'] as String);
      },
    };
  }

  static void navigateToOrders(BuildContext context) {
    Navigator.pushNamed(context, orders);
  }

  static void navigateToOrderDetails(BuildContext context, String orderId) {
    Navigator.pushNamed(
      context,
      orderDetails,
      arguments: {'orderId': orderId},
    );
  }
}
