import 'package:freezed_annotation/freezed_annotation.dart';

part 'bank_offer_model.freezed.dart';
part 'bank_offer_model.g.dart';

enum BankOfferType {
  @JsonValue(0)
  cashback,
  @JsonValue(1)
  discount,
  @JsonValue(2)
  emi,
  @JsonValue(3)
  reward,
}

@freezed
abstract class BankOfferModel with _$BankOfferModel {
  const factory BankOfferModel({
    required String id,
    required String title,
    required String description,
    required String bankName,
    String? bankLogoUrl,
    required BankOfferType offerType,
    required double value,
    String? code,
    required DateTime startDate,
    required DateTime endDate,
    required List<String> applicableProductIds,
    required List<String> applicableCategories,
    required double minimumPurchaseAmount,
    double? maximumDiscountAmount,
    @Default(false) bool isActive,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isDeleted,
    String? termsAndConditions,
  }) = _BankOfferModel;

  factory BankOfferModel.fromJson(Map<String, dynamic> json) =>
      _$BankOfferModelFromJson(json);
}
