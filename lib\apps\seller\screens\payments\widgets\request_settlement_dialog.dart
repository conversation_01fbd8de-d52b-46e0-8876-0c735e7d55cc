import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/domain/models/payment_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/payment_cubit.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';

class RequestSettlementDialog extends StatefulWidget {
  final double availableAmount;
  final List<BankAccountModel> bankAccounts;

  const RequestSettlementDialog({
    super.key,
    required this.availableAmount,
    required this.bankAccounts,
  });

  @override
  State<RequestSettlementDialog> createState() =>
      _RequestSettlementDialogState();
}

class _RequestSettlementDialogState extends State<RequestSettlementDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  String? _selectedAccountId;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _amountController.text = widget.availableAmount.toStringAsFixed(2);
    if (widget.bankAccounts.isNotEmpty) {
      final primaryAccount = widget.bankAccounts.firstWhere(
        (account) => account.isPrimary,
        orElse: () => widget.bankAccounts.first,
      );
      _selectedAccountId = primaryAccount.id;
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  void _submit() {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedAccountId == null) {
      setState(() {
        _errorText = 'Please select a bank account';
      });
      return;
    }

    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      setState(() {
        _errorText = 'Please enter a valid amount';
      });
      return;
    }

    if (amount > widget.availableAmount) {
      setState(() {
        _errorText = 'Amount cannot exceed available balance';
      });
      return;
    }

    context.read<PaymentCubit>().requestSettlement(
          amount: amount,
          bankAccountId: _selectedAccountId!,
        );

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Request Settlement',
                style: theme.textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Available Balance: ${CurrencyFormatter.format(widget.availableAmount)}',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'Amount',
                  border: OutlineInputBorder(),
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Please enter a valid amount';
                  }
                  if (amount > widget.availableAmount) {
                    return 'Amount cannot exceed available balance';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedAccountId,
                decoration: const InputDecoration(
                  labelText: 'Bank Account',
                  border: OutlineInputBorder(),
                ),
                items: widget.bankAccounts.map((account) {
                  return DropdownMenuItem(
                    value: account.id,
                    child: Text(
                      '${account.bankName} - ${account.accountNumber}',
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedAccountId = value;
                    _errorText = null;
                  });
                },
              ),
              if (_errorText != null) ...[
                const SizedBox(height: 8),
                Text(
                  _errorText!,
                  style: TextStyle(
                    color: theme.colorScheme.error,
                    fontSize: 12,
                  ),
                ),
              ],
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _submit,
                    child: const Text('Request Settlement'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
