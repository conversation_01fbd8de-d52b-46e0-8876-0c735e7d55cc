import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/product/product_model.dart';
import '../repositories/cart_repository.dart';
import '../states/cart_state.dart';

/// Cart notifier
class CartNotifier extends StateNotifier<CartState> {
  /// Creates a [CartNotifier]
  CartNotifier(this._repository) : super(const CartState.initial()) {
    getCart();
  }

  final CartRepository _repository;

  /// Gets the cart
  Future<void> getCart() async {
    try {
      state = const CartState.loading();
      final cart = await _repository.getCart();
      state = CartState.loaded(cart);
    } catch (e) {
      state = CartState.error(e.toString());
    }
  }

  /// Adds a product to the cart
  /// If [clearCartFirst] is true, the cart will be cleared before adding the product
  Future<void> addToCart({
    required ProductModel product,
    required int quantity,
    String? sellerId,
    String? notes,
    bool clearCartFirst = false,
  }) async {
    try {
      state = const CartState.loading();

      // Clear the cart first if requested
      if (clearCartFirst) {
        await _repository.clearCart();
      }

      await _repository.addToCart(
        product: product,
        quantity: quantity,
        sellerId: sellerId,
        notes: notes,
      );
      await getCart();
    } catch (e) {
      state = CartState.error(e.toString());
    }
  }

  /// Updates the quantity of a product in the cart
  Future<void> updateQuantity({
    required ProductModel product,
    required int quantity,
  }) async {
    try {
      state = const CartState.loading();
      await _repository.updateQuantity(
        product: product,
        quantity: quantity,
      );
      await getCart();
    } catch (e) {
      state = CartState.error(e.toString());
    }
  }

  /// Removes a product from the cart
  Future<void> removeFromCart(ProductModel product) async {
    try {
      state = const CartState.loading();
      await _repository.removeFromCart(product);
      await getCart();
    } catch (e) {
      state = CartState.error(e.toString());
    }
  }

  /// Clears the cart
  Future<void> clearCart() async {
    try {
      state = const CartState.loading();
      await _repository.clearCart();
      await getCart();
    } catch (e) {
      state = CartState.error(e.toString());
    }
  }

  /// Updates the seller of a product in the cart
  Future<void> updateSeller({
    required ProductModel product,
    required String sellerId,
  }) async {
    try {
      state = const CartState.loading();
      await _repository.updateSeller(
        product: product,
        sellerId: sellerId,
      );
      await getCart();
    } catch (e) {
      state = CartState.error(e.toString());
    }
  }

  /// Updates the notes of a product in the cart
  Future<void> updateNotes({
    required ProductModel product,
    required String notes,
  }) async {
    try {
      state = const CartState.loading();
      await _repository.updateNotes(
        product: product,
        notes: notes,
      );
      await getCart();
    } catch (e) {
      state = CartState.error(e.toString());
    }
  }
}
