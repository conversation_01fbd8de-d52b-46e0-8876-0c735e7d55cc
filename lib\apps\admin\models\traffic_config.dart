import 'package:freezed_annotation/freezed_annotation.dart';

part 'traffic_config.freezed.dart';
part 'traffic_config.g.dart';

/// Traffic routing configuration model
@freezed
sealed class TrafficConfig with _$TrafficConfig {
  const factory TrafficConfig({
    required TrafficRoutingMode routingMode,
    required double awsTrafficPercentage,
    required DateTime lastUpdated,
    @Default(false) bool maintenanceMode,
    @Default(true) bool autoFailoverEnabled,
    @Default({}) Map<String, dynamic> customRules,
  }) = _TrafficConfig;

  factory TrafficConfig.fromJson(Map<String, dynamic> json) =>
      _$TrafficConfigFromJson(json);
}

/// Traffic routing modes
enum TrafficRoutingMode {
  awsOnly,
  datacenterOnly,
  hybrid,
  geographic,
  failover,
  maintenance;

  String get displayName {
    switch (this) {
      case TrafficRoutingMode.awsOnly:
        return 'AWS Only';
      case TrafficRoutingMode.datacenterOnly:
        return 'Datacenter Only';
      case TrafficRoutingMode.hybrid:
        return 'Hybrid (Balanced)';
      case TrafficRoutingMode.geographic:
        return 'Geographic Routing';
      case TrafficRoutingMode.failover:
        return 'Auto Failover';
      case TrafficRoutingMode.maintenance:
        return 'Maintenance Mode';
    }
  }

  String get description {
    switch (this) {
      case TrafficRoutingMode.awsOnly:
        return 'Route all traffic to AWS servers';
      case TrafficRoutingMode.datacenterOnly:
        return 'Route all traffic to datacenter servers';
      case TrafficRoutingMode.hybrid:
        return 'Split traffic between AWS and datacenter';
      case TrafficRoutingMode.geographic:
        return 'Route based on user location';
      case TrafficRoutingMode.failover:
        return 'Automatically switch on server failure';
      case TrafficRoutingMode.maintenance:
        return 'Show maintenance page to users';
    }
  }
}

/// Server health status
enum ServerHealth {
  healthy,
  warning,
  critical,
  unknown;

  String get displayName {
    switch (this) {
      case ServerHealth.healthy:
        return 'Healthy';
      case ServerHealth.warning:
        return 'Warning';
      case ServerHealth.critical:
        return 'Critical';
      case ServerHealth.unknown:
        return 'Unknown';
    }
  }
}

/// Server status information
@freezed
sealed class ServerStatus with _$ServerStatus {
  const factory ServerStatus({
    required ServerHealth awsStatus,
    required ServerHealth datacenterStatus,
    required List<ServerInfo> awsServers,
    required List<ServerInfo> datacenterServers,
    required DateTime lastChecked,
    @Default(0) int totalRequests,
    @Default(0) int awsRequests,
    @Default(0) int datacenterRequests,
  }) = _ServerStatus;

  factory ServerStatus.fromJson(Map<String, dynamic> json) =>
      _$ServerStatusFromJson(json);
}

/// Individual server information
@freezed
sealed class ServerInfo with _$ServerInfo {
  const factory ServerInfo({
    required String id,
    required String name,
    required String ipAddress,
    required ServerHealth health,
    required double cpuUsage,
    required double memoryUsage,
    required double diskUsage,
    required int responseTime,
    required int activeConnections,
    required DateTime lastHealthCheck,
    @Default('Unknown') String region,
    @Default('Unknown') String serverType,
  }) = _ServerInfo;

  factory ServerInfo.fromJson(Map<String, dynamic> json) =>
      _$ServerInfoFromJson(json);
}

/// Traffic routing rule
@freezed
sealed class RoutingRule with _$RoutingRule {
  const factory RoutingRule({
    required String id,
    required String name,
    required RoutingCondition condition,
    required RoutingAction action,
    required int priority,
    required bool isActive,
    required DateTime createdAt,
    DateTime? updatedAt,
  }) = _RoutingRule;

  factory RoutingRule.fromJson(Map<String, dynamic> json) =>
      _$RoutingRuleFromJson(json);
}

/// Routing condition types
enum RoutingCondition {
  geographic,
  userAgent,
  ipRange,
  timeOfDay,
  serverLoad,
  customHeader;

  String get displayName {
    switch (this) {
      case RoutingCondition.geographic:
        return 'Geographic Location';
      case RoutingCondition.userAgent:
        return 'User Agent';
      case RoutingCondition.ipRange:
        return 'IP Address Range';
      case RoutingCondition.timeOfDay:
        return 'Time of Day';
      case RoutingCondition.serverLoad:
        return 'Server Load';
      case RoutingCondition.customHeader:
        return 'Custom Header';
    }
  }
}

/// Routing action types
enum RoutingAction {
  routeToAws,
  routeToDatacenter,
  routeToMaintenance,
  blockRequest,
  rateLimitRequest;

  String get displayName {
    switch (this) {
      case RoutingAction.routeToAws:
        return 'Route to AWS';
      case RoutingAction.routeToDatacenter:
        return 'Route to Datacenter';
      case RoutingAction.routeToMaintenance:
        return 'Show Maintenance Page';
      case RoutingAction.blockRequest:
        return 'Block Request';
      case RoutingAction.rateLimitRequest:
        return 'Rate Limit Request';
    }
  }
}

/// Traffic analytics data
@freezed
sealed class TrafficAnalytics with _$TrafficAnalytics {
  const factory TrafficAnalytics({
    required DateTime timestamp,
    required int totalRequests,
    required int awsRequests,
    required int datacenterRequests,
    required double averageResponseTime,
    required double errorRate,
    required Map<String, int> requestsByCountry,
    required Map<String, int> requestsByEndpoint,
    @Default(0) int blockedRequests,
    @Default(0) int rateLimitedRequests,
  }) = _TrafficAnalytics;

  factory TrafficAnalytics.fromJson(Map<String, dynamic> json) =>
      _$TrafficAnalyticsFromJson(json);
}

/// Load balancer configuration
@freezed
sealed class LoadBalancerConfig with _$LoadBalancerConfig {
  const factory LoadBalancerConfig({
    required String id,
    required String name,
    required LoadBalancingAlgorithm algorithm,
    required List<ServerEndpoint> endpoints,
    required HealthCheckConfig healthCheck,
    @Default(true) bool stickySession,
    @Default(30) int sessionTimeout,
    @Default(5) int maxRetries,
    @Default(1000) int timeoutMs,
  }) = _LoadBalancerConfig;

  factory LoadBalancerConfig.fromJson(Map<String, dynamic> json) =>
      _$LoadBalancerConfigFromJson(json);
}

/// Load balancing algorithms
enum LoadBalancingAlgorithm {
  roundRobin,
  leastConnections,
  weightedRoundRobin,
  ipHash,
  leastResponseTime;

  String get displayName {
    switch (this) {
      case LoadBalancingAlgorithm.roundRobin:
        return 'Round Robin';
      case LoadBalancingAlgorithm.leastConnections:
        return 'Least Connections';
      case LoadBalancingAlgorithm.weightedRoundRobin:
        return 'Weighted Round Robin';
      case LoadBalancingAlgorithm.ipHash:
        return 'IP Hash';
      case LoadBalancingAlgorithm.leastResponseTime:
        return 'Least Response Time';
    }
  }
}

/// Server endpoint configuration
@freezed
sealed class ServerEndpoint with _$ServerEndpoint {
  const factory ServerEndpoint({
    required String id,
    required String host,
    required int port,
    required int weight,
    required bool isActive,
    @Default('http') String protocol,
    @Default('/health') String healthCheckPath,
  }) = _ServerEndpoint;

  factory ServerEndpoint.fromJson(Map<String, dynamic> json) =>
      _$ServerEndpointFromJson(json);
}

/// Health check configuration
@freezed
sealed class HealthCheckConfig with _$HealthCheckConfig {
  const factory HealthCheckConfig({
    required String path,
    required int intervalSeconds,
    required int timeoutSeconds,
    required int healthyThreshold,
    required int unhealthyThreshold,
    @Default(200) int expectedStatusCode,
    String? expectedResponse,
  }) = _HealthCheckConfig;

  factory HealthCheckConfig.fromJson(Map<String, dynamic> json) =>
      _$HealthCheckConfigFromJson(json);
}
