import '../../../../../shared/models/inventory_model.dart';

abstract class InventoryRepository {
  Future<List<InventoryModel>> getInventory();
  Future<InventoryModel> getInventoryById(String productId);
  Future<List<InventoryModel>> getSellerInventory(String sellerId);
  Future<List<InventoryModel>> getLowStockItems(String sellerId);
  Stream<List<InventoryModel>> watchInventory(String sellerId);
  Future<void> addStock(String inventoryId, int quantity);
  Future<void> removeStock(String inventoryId, int quantity);
  Future<void> setLowStockAlert(String inventoryId, bool enabled);
  Future<void> createInventory(InventoryModel inventory);
  Future<void> deleteInventory(String productId);
}
