import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/seller/domain/repositories/analytics_repository.dart';
import 'package:shivish/apps/seller/presentation/cubits/performance/store_performance_state.dart';

@injectable
class StorePerformanceCubit extends Cubit<StorePerformanceState> {
  final AnalyticsRepository _analyticsRepository;

  StorePerformanceCubit(this._analyticsRepository)
      : super(const StorePerformanceState());

  Future<void> loadPerformanceMetrics() async {
    try {
      emit(state.copyWith(isLoading: true, hasError: false));
      final metrics = await _analyticsRepository.getPerformanceMetrics();
      emit(state.copyWith(
        isLoading: false,
        metrics: metrics,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      ));
    }
  }

  void setTimeRange(String range) {
    emit(state.copyWith(timeRange: range));
    loadPerformanceMetrics();
  }

  Future<void> refresh() async {
    await loadPerformanceMetrics();
  }
}
