import '../../../shared/models/order/order_model.dart';
import '../../../shared/utils/logger.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import 'order_repository.dart';

/// Implementation of [OrderRepository]
class OrderRepositoryImpl implements OrderRepository {
  /// Creates an [OrderRepositoryImpl]
  OrderRepositoryImpl({
    DatabaseService? databaseService,
  }) : _databaseService = databaseService ?? DatabaseService(DatabaseConfig.fromEnvironment());

  final DatabaseService _databaseService;
  final _logger = getLogger('OrderRepositoryImpl');

  @override
  Future<OrderModel> createOrder(OrderModel order) async {
    try {
      // Validate order data
      if (order.buyerId.isEmpty) {
        _logger.severe('Order creation failed: buyerId is empty');
        throw Exception('Order creation failed: buyerId is empty');
      }

      if (order.sellerId.isEmpty) {
        _logger.severe('Order creation failed: sellerId is empty');
        throw Exception('Order creation failed: sellerId is empty');
      }

      if (order.items.isEmpty) {
        _logger.severe('Order creation failed: items list is empty');
        throw Exception('Order creation failed: items list is empty');
      }

      // Check if user document exists and create it if it doesn't
      try {
        final userData = await _databaseService.find('users', order.buyerId);
        if (userData == null) {
          _logger.warning(
              'User document not found for buyerId: ${order.buyerId}. Creating a basic user document.');

          // Create a basic user document to ensure orders can be created
          await _databaseService.create('users', {
            'id': order.buyerId,
            'role': 'buyer',
            'displayName': order.customerName,
            'email': order.customerEmail ?? '',
            'phone': order.customerPhone,
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
          });

          _logger.info(
              'Created basic user document for buyerId: ${order.buyerId}');
        }
      } catch (userError) {
        _logger.warning(
            'Error checking/creating user document: $userError. Continuing with order creation.');
      }

      // Generate a unique ID for the order
      final orderId = 'order_${DateTime.now().millisecondsSinceEpoch}_${order.buyerId}';

      // Prepare data for hybrid storage
      final orderData = {
        'id': orderId,
        ...order.toJson(),
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      _logger.info(
          'Creating order with buyerId: ${order.buyerId}, sellerId: ${order.sellerId}, items count: ${order.items.length}');

      // Create the document in hybrid storage
      await _databaseService.create('orders', orderData);
      _logger.info('Order document created with ID: $orderId');

      // Fetch the created document to return the complete order
      final createdOrderData = await _databaseService.find('orders', orderId);
      if (createdOrderData != null) {
        final createdOrder = OrderModel.fromJson(createdOrderData);
        _logger.info('Order created successfully with ID: ${createdOrder.id}');
        return createdOrder;
      } else {
        _logger.warning('Order document not found after creation: $orderId');
        throw Exception('Order document not found after creation');
      }
    } catch (e) {
      _logger.severe('Failed to create order: $e');
      throw Exception('Failed to create order: $e');
    }
  }

  @override
  Future<OrderModel?> getOrderById(String id) async {
    try {
      final orderData = await _databaseService.find('orders', id);

      if (orderData == null) {
        _logger.warning('Order not found with ID: $id');
        return null;
      }

      return OrderModel.fromJson(orderData);
    } catch (e) {
      _logger.severe('Error getting order by ID: $e');
      return null;
    }
  }

  @override
  Future<List<OrderModel>> getOrdersByUserId(String userId) async {
    try {
      _logger.info('Fetching orders for user: $userId');

      // Get all orders and filter by buyerId
      final allOrders = await _databaseService.getAll('orders');

      final userOrders = allOrders
          .where((orderData) => orderData['buyerId'] == userId)
          .toList();

      // Sort by createdAt (descending)
      userOrders.sort((a, b) {
        final createdAtA = DateTime.tryParse(a['createdAt'] as String? ?? '') ?? DateTime.now();
        final createdAtB = DateTime.tryParse(b['createdAt'] as String? ?? '') ?? DateTime.now();
        return createdAtB.compareTo(createdAtA);
      });

      final orders = userOrders
          .map((orderData) => OrderModel.fromJson(orderData))
          .toList();

      _logger.info('Found ${orders.length} orders for user: $userId');
      return orders;
    } catch (e) {
      _logger.severe('Error getting orders by user ID: $e');
      return [];
    }
  }

  @override
  Future<OrderModel> updateOrderStatus(String id, OrderStatus status) async {
    try {
      final existingOrder = await _databaseService.find('orders', id);
      if (existingOrder == null) {
        throw Exception('Order not found with ID: $id');
      }

      final updatedData = {
        ...existingOrder,
        'status': status.name,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      await _databaseService.update('orders', id, updatedData);

      final updatedOrderData = await _databaseService.find('orders', id);
      if (updatedOrderData != null) {
        _logger.info('Order status updated successfully for ID: $id to ${status.name}');
        return OrderModel.fromJson(updatedOrderData);
      } else {
        throw Exception('Failed to fetch updated order');
      }
    } catch (e) {
      _logger.severe('Error updating order status: $e');
      throw Exception('Error updating order status: $e');
    }
  }

  @override
  Future<OrderModel> cancelOrder(String id) async {
    try {
      return await updateOrderStatus(id, OrderStatus.cancelled);
    } catch (e) {
      _logger.severe('Error cancelling order: $e');
      throw Exception('Error cancelling order: $e');
    }
  }
}