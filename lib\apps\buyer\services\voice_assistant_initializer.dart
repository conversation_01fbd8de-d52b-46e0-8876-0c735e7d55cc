import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/services/voice/voice_assistant_troubleshooter.dart';
import '../../../shared/providers/voice_assistant_provider.dart';

/// Service to initialize the voice assistant for the buyer app
class VoiceAssistantInitializer {
  /// Initialize the voice assistant
  static Future<bool> initialize(WidgetRef ref) async {
    try {
      debugPrint('Initializing voice assistant...');

      // Get the voice assistant service
      final voiceAssistantService = ref.read(voiceAssistantProvider);

      // Initialize the service
      final initialized = await voiceAssistantService.initialize();

      if (initialized) {
        debugPrint('Voice assistant initialized successfully');

        // Check if voice assistant is enabled in preferences
        final isEnabled = await ref.read(voiceAssistantEnabledProvider.future);

        // Start listening for wake words if enabled
        if (isEnabled) {
          debugPrint(
              'Voice assistant is enabled, starting to listen for wake words');

          // Troubleshoot and fix any issues before starting
          await VoiceAssistantTroubleshooter.checkAndFix(voiceAssistantService);

          // Start listening for wake words
          await voiceAssistantService.startListeningForWakeWord();
        } else {
          debugPrint('Voice assistant is disabled in preferences');
        }

        return true;
      } else {
        debugPrint('Failed to initialize voice assistant');
        return false;
      }
    } catch (e) {
      debugPrint('Error initializing voice assistant: $e');
      return false;
    }
  }
}
