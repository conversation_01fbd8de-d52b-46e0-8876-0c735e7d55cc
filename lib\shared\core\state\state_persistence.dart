import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class StatePersistence {
  static const _prefix = 'app_state_';

  /// Saves data to persistent storage
  static Future<bool> saveData<T>(String key, T data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(data);
      return await prefs.setString('$_prefix$key', jsonString);
    } catch (e) {
      debugPrint('Error saving state data: $e');
      return false;
    }
  }

  /// Loads data from persistent storage
  static Future<T?> loadData<T>(
      String key, T Function(Map<String, dynamic>) fromJson) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('$_prefix$key');
      if (jsonString != null) {
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        return fromJson(json);
      }
      return null;
    } catch (e) {
      debugPrint('Error loading state data: $e');
      return null;
    }
  }

  /// Removes data from persistent storage
  static Future<bool> removeData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove('$_prefix$key');
    } catch (e) {
      debugPrint('Error removing state data: $e');
      return false;
    }
  }

  /// Clears all persisted state data
  static Future<bool> clearAll() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_prefix));
      for (final key in keys) {
        await prefs.remove(key);
      }
      return true;
    } catch (e) {
      debugPrint('Error clearing state data: $e');
      return false;
    }
  }
}
