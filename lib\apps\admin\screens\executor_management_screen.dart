import 'package:flutter/material.dart';
import '../../../shared/models/executor.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';

class ExecutorManagementScreen extends StatefulWidget {
  const ExecutorManagementScreen({super.key});

  @override
  State<ExecutorManagementScreen> createState() => _ExecutorManagementScreenState();
}

class _ExecutorManagementScreenState extends State<ExecutorManagementScreen> {
  final _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
  final _logger = getLogger('ExecutorManagementScreen');
  List<Executor> _executors = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadExecutors();
  }

  Future<void> _loadExecutors() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      _logger.info('Loading executors');
      final executorsData = await _databaseService.getAll(
        'executors',
        where: 'is_deleted = ?',
        whereParams: [false],
        orderBy: 'created_at DESC',
      );

      final executors = executorsData.map((data) => Executor.fromJson(data)).toList();

      setState(() {
        _executors = executors;
        _isLoading = false;
      });

      _logger.info('Successfully loaded ${executors.length} executors');
    } catch (e) {
      _logger.severe('Error loading executors: $e');
      setState(() {
        _errorMessage = 'Error loading executors: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Executor Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddExecutorDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadExecutors,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_errorMessage!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadExecutors,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_executors.isEmpty) {
      return const Center(child: Text('No executors found'));
    }

    return ListView.builder(
      itemCount: _executors.length,
      itemBuilder: (context, index) {
        final executor = _executors[index];
        return ListTile(
          leading: CircleAvatar(
            child: Text(executor.name.substring(0, 1).toUpperCase()),
          ),
          title: Text(executor.name),
          subtitle: Text(executor.email),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Icon(
                  executor.status == 'active' ? Icons.check_circle : Icons.cancel,
                  color: executor.status == 'active' ? Colors.green : Colors.red,
                ),
                onPressed: () => _updateExecutorStatus(
                  context,
                  executor.id,
                  executor.status != 'active'
                ),
              ),
              IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () => _deleteExecutor(context, executor),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _updateExecutorStatus(BuildContext context, String executorId, bool isActive) async {
    try {
      _logger.info('Updating executor status: $executorId -> active: $isActive');
      await _databaseService.update('executors', executorId, {
        'status': isActive ? 'active' : 'inactive',
        'updated_at': DateTime.now().toIso8601String(),
      });

      _logger.info('Successfully updated executor status: $executorId');
      _loadExecutors(); // Reload the list

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Executor ${isActive ? 'activated' : 'deactivated'} successfully'),
          ),
        );
      }
    } catch (e) {
      _logger.severe('Error updating executor: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating executor: $e')),
        );
      }
    }
  }

  Future<void> _deleteExecutor(BuildContext context, Executor executor) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Executor'),
        content: Text('Are you sure you want to delete ${executor.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm == true && context.mounted) {
      try {
        _logger.info('Deleting executor: ${executor.id}');
        await _databaseService.update('executors', executor.id, {
          'is_deleted': true,
          'updated_at': DateTime.now().toIso8601String(),
        });

        _logger.info('Successfully deleted executor: ${executor.id}');
        _loadExecutors(); // Reload the list

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Executor deleted successfully')),
          );
        }
      } catch (e) {
        _logger.severe('Error deleting executor: $e');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting executor: $e')),
          );
        }
      }
    }
  }

  Future<void> _showAddExecutorDialog(BuildContext context) async {
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final phoneController = TextEditingController();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Executor'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(labelText: 'Name'),
            ),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(labelText: 'Email'),
              keyboardType: TextInputType.emailAddress,
            ),
            TextField(
              controller: phoneController,
              decoration: const InputDecoration(labelText: 'Phone'),
              keyboardType: TextInputType.phone,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Add'),
          ),
        ],
      ),
    );

    if (result == true && context.mounted) {
      if (nameController.text.isEmpty || emailController.text.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Name and email are required')),
        );
        return;
      }

      try {
        _logger.info('Adding new executor: ${nameController.text}');
        final now = DateTime.now().toIso8601String();

        await _databaseService.create('executors', {
          'name': nameController.text,
          'email': emailController.text,
          'phone': phoneController.text,
          'role': 'executor',
          'status': 'active',
          'is_active': true,
          'is_deleted': false,
          'created_at': now,
          'updated_at': now,
        });

        _logger.info('Successfully added executor: ${nameController.text}');
        _loadExecutors(); // Reload the list

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Executor added successfully')),
          );
        }
      } catch (e) {
        _logger.severe('Error adding executor: $e');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error adding executor: $e')),
          );
        }
      }
    }
  }


}
