import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'notification_settings_state.dart';

class NotificationSettingsCubit extends Cubit<NotificationSettingsState> {
  NotificationSettingsCubit()
      : super(const NotificationSettingsState.loading()) {
    loadSettings();
  }

  Future<void> loadSettings() async {
    try {
      emit(const NotificationSettingsState.loading());
      final prefs = await SharedPreferences.getInstance();

      final settings = [
        NotificationSetting(
          id: 'order_notifications',
          title: 'Order Notifications',
          isEnabled: prefs.getBool('order_notifications') ?? true,
          description: 'Receive notifications about your orders',
          icon: 'shopping_cart',
        ),
        NotificationSetting(
          id: 'promotion_notifications',
          title: 'Promotion Notifications',
          isEnabled: prefs.getBool('promotion_notifications') ?? true,
          description: 'Receive notifications about promotions and offers',
          icon: 'local_offer',
        ),
        NotificationSetting(
          id: 'system_notifications',
          title: 'System Notifications',
          isEnabled: prefs.getBool('system_notifications') ?? true,
          description: 'Receive system updates and maintenance notifications',
          icon: 'info',
        ),
      ];

      emit(NotificationSettingsState.loaded(settings));
    } catch (e) {
      emit(NotificationSettingsState.error(e.toString()));
    }
  }

  Future<void> toggleSetting(String id, bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(id, value);
      await loadSettings();
    } catch (e) {
      emit(NotificationSettingsState.error(e.toString()));
    }
  }
}
