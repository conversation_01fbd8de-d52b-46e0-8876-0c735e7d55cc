import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/technician/technician_routes.dart';
import '../../auth/bloc/auth_bloc.dart';
import '../../auth/bloc/auth_event.dart';
import '../../auth/bloc/auth_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TechnicianDrawer extends StatelessWidget {
  const TechnicianDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Drawer(
          child: Column(
            children: [
              UserAccountsDrawerHeader(
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                ),
                currentAccountPicture: CircleAvatar(
                  backgroundImage: state.maybeMap(
                    authenticated: (state) => state.user.photoUrl != null
                        ? NetworkImage(state.user.photoUrl!)
                        : null,
                    orElse: () => null,
                  ),
                  child: state.maybeMap(
                    authenticated: (state) => state.user.photoUrl == null
                        ? Text(
                            state.user.displayName.isNotEmpty
                                ? state.user.displayName
                                    .substring(0, 1)
                                    .toUpperCase()
                                : 'T',
                            style: theme.textTheme.headlineMedium?.copyWith(
                              color: theme.colorScheme.onPrimary,
                            ),
                          )
                        : null,
                    orElse: () => const Icon(Icons.person_outline_rounded),
                  ),
                ),
                accountName: Text(
                  state.maybeMap(
                    authenticated: (state) =>
                        state.user.displayName.isNotEmpty ? state.user.displayName : 'Technician',
                    orElse: () => 'Technician',
                  ),
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                accountEmail: Text(
                  state.maybeMap(
                    authenticated: (state) => state.user.email,
                    orElse: () => '',
                  ),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ),
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    _DrawerItem(
                      icon: Icons.home_outlined,
                      label: 'Home',
                      onTap: () => _navigateTo(context, TechnicianRoutes.home),
                    ),
                    _DrawerItem(
                      icon: Icons.handyman_outlined,
                      label: 'Services',
                      onTap: () =>
                          _navigateTo(context, TechnicianRoutes.services),
                    ),
                    _DrawerItem(
                      icon: Icons.book_online_outlined,
                      label: 'Bookings',
                      onTap: () =>
                          _navigateTo(context, TechnicianRoutes.bookings),
                    ),
                    _DrawerItem(
                      icon: Icons.payments_outlined,
                      label: 'Payments',
                      onTap: () =>
                          _navigateTo(context, TechnicianRoutes.payments),
                    ),
                    const Divider(),
                    _DrawerItem(
                      icon: Icons.person_outline_rounded,
                      label: 'Profile',
                      onTap: () => _navigateTo(
                          context, TechnicianRoutes.profileSettings),
                    ),
                    const Divider(),
                    _DrawerItem(
                      icon: Icons.logout_rounded,
                      label: 'Sign Out',
                      onTap: () {
                        context.read<AuthBloc>().add(const AuthEvent.signOut());
                        context.go(TechnicianRoutes.login);
                      },
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Version 1.0.0',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _navigateTo(BuildContext context, String route) {
    context.pop();
    context.go(route);
  }
}

class _DrawerItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _DrawerItem({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
      ),
      title: Text(
        label,
        style: theme.textTheme.bodyLarge?.copyWith(
          color: theme.colorScheme.onSurface,
        ),
      ),
      onTap: onTap,
      dense: true,
      horizontalTitleGap: 0,
    );
  }
}
