import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../shared/models/booking/booking_model.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/booking_provider.dart';
import '../../../../shared/services/storage/storage_service.dart';

class TechnicianReviewScreen extends ConsumerStatefulWidget {
  final String bookingId;

  const TechnicianReviewScreen({
    super.key,
    required this.bookingId,
  });

  @override
  ConsumerState<TechnicianReviewScreen> createState() =>
      _TechnicianReviewScreenState();
}

class _TechnicianReviewScreenState
    extends ConsumerState<TechnicianReviewScreen> {
  final _formKey = GlobalKey<FormState>();
  double _rating = 0.0;
  final _reviewController = TextEditingController();
  final List<String> _attachments = [];
  bool _isUploading = false;

  @override
  void dispose() {
    _reviewController.dispose();
    super.dispose();
  }

  Future<void> _submitReview() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final authState = ref.read(authProvider);
    final user = authState;
    if (user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please sign in to submit a review')),
      );
      return;
    }

    try {
      final notifier =
          ref.read(bookingStateNotifierProvider(user.id).notifier);
      await notifier.addReview(
        bookingId: widget.bookingId,
        rating: _rating,
        review: _reviewController.text,
        attachments: _attachments,
      );
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Review submitted successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error submitting review: $e')),
        );
      }
    }
  }

  Future<void> _addAttachment() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image == null) return;

      setState(() => _isUploading = true);

      final storageService = ref.read(storageServiceProvider);
      final downloadUrl = await storageService.uploadFile(
        filePath: image.path,
        storagePath: 'reviews/${widget.bookingId}/images',
        onProgress: (progress) {
          // You can show a progress indicator here if needed
        },
      );

      if (mounted) {
        setState(() {
          _attachments.add(downloadUrl);
          _isUploading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isUploading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error uploading image: $e')),
        );
      }
    }
  }

  Future<void> _removeAttachment(int index) async {
    try {
      final storageService = ref.read(storageServiceProvider);
      await storageService.deleteFile(_attachments[index]);
      if (mounted) {
        setState(() {
          _attachments.removeAt(index);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error removing image: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final user = authState;
    if (user == null) {
      return const Center(child: Text('Please sign in to submit a review'));
    }

    final bookingsAsync = ref.watch(bookingStateNotifierProvider(user.id));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Review'),
      ),
      body: bookingsAsync.when(
        data: (bookings) {
          final booking = bookings.firstWhere(
            (b) => b.id == widget.bookingId,
            orElse: () => throw Exception('Booking not found'),
          );

          if (booking.status != BookingStatus.completed ||
              booking.paymentStatus != PaymentStatus.completed) {
            return const Center(
              child: Text('Only completed bookings can be reviewed'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Rate your experience',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(5, (index) {
                      return IconButton(
                        icon: Icon(
                          index < _rating ? Icons.star : Icons.star_border,
                          color: Colors.amber,
                          size: 40,
                        ),
                        onPressed: () {
                          setState(() {
                            _rating = index + 1.0;
                          });
                        },
                      );
                    }),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Write your review',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _reviewController,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: 'Share your experience with the service',
                    ),
                    maxLines: 5,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please write your review';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Add photos (optional)',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 100,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _attachments.length + 1,
                      itemBuilder: (context, index) {
                        if (index == _attachments.length) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: InkWell(
                              onTap: _isUploading ? null : _addAttachment,
                              child: Container(
                                width: 100,
                                height: 100,
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: _isUploading
                                    ? const Center(
                                        child: CircularProgressIndicator(),
                                      )
                                    : const Icon(Icons.add_photo_alternate),
                              ),
                            ),
                          );
                        }

                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: Stack(
                            children: [
                              Container(
                                width: 100,
                                height: 100,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  image: DecorationImage(
                                    image: NetworkImage(_attachments[index]),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 4,
                                right: 4,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.5),
                                    shape: BoxShape.circle,
                                  ),
                                  child: IconButton(
                                    icon: const Icon(
                                      Icons.close,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                    onPressed: () => _removeAttachment(index),
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _submitReview,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Submit Review'),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }
}
