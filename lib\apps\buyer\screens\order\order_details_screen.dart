import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'dart:io';
import 'widgets/live_tracking_bottom_sheet.dart';
import '../../../../shared/models/order/order_model.dart';
import '../../../../shared/models/delivery/delivery_provider_model.dart';

import '../../../../shared/services/order/invoice_service.dart';
import '../../../../shared/providers/order_provider.dart';
import '../../../../shared/utils/date_formatter.dart';
import '../../../../shared/utils/string_utils.dart';
import '../../../../shared/utils/gst_utils.dart';
import '../../buyer_routes.dart';
import '../review/create_review_screen.dart';

class OrderDetailsScreen extends ConsumerStatefulWidget {
  final String orderId;

  const OrderDetailsScreen({super.key, required this.orderId});

  @override
  ConsumerState<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends ConsumerState<OrderDetailsScreen> {
  bool _isLoading = true;
  bool _isGeneratingInvoice = false;
  bool _isBuyingAgain = false;
  OrderModel? _order;
  String? _error;
  String? _invoicePath;

  // Constants for order summary calculations
  double _deliveryFee = 0.0; // Will be calculated dynamically

  // Track if the live tracking bottom sheet is shown
  bool _showLiveTracking = false;

  @override
  void initState() {
    super.initState();
    debugPrint(
      'OrderDetailsScreen initialized with orderId: ${widget.orderId}',
    );
    _loadOrder();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Update delivery fee when order is loaded
    if (_order != null) {
      _updateDeliveryFee();
    }
  }

  // Update delivery fee based on order data
  void _updateDeliveryFee() {
    if (_order != null) {
      setState(() {
        _deliveryFee = _getDeliveryFee(_order!);
      });
    }
  }

  Future<void> _loadOrder() async {
    debugPrint('Loading order with ID: ${widget.orderId}');
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Validate order ID
      if (widget.orderId.isEmpty) {
        debugPrint('Empty order ID provided');
        if (mounted) {
          setState(() {
            _error = "Invalid order ID";
            _isLoading = false;
          });
        }
        return;
      }

      debugPrint(
        'Calling OrderService.getOrderById with ID: ${widget.orderId}',
      );

      // Use getOrderById instead of getOrder to get the OrderModel directly
      final order = await ref
          .read(orderServiceProvider)
          .getOrderById(widget.orderId);

      debugPrint('Order loaded: ${order != null ? 'success' : 'null'}');

      if (mounted) {
        setState(() {
          if (order == null) {
            _error = "Order not found";
            debugPrint('Order not found for ID: ${widget.orderId}');
          } else {
            _order = order;
            debugPrint('Order set successfully: ${order.id}');

            // Log order details for debugging
            debugPrint('Order type: ${order.orderType ?? "not set"}');
            debugPrint(
              'Shopping list ID: ${order.shoppingListId ?? "not set"}',
            );
            debugPrint('Status: ${order.status}');
            debugPrint('Items count: ${order.items.length}');

            // Update delivery fee
            _updateDeliveryFee();

            // Check if this is a shopping list order
            final isShoppingList =
                order.shoppingListId != null ||
                order.orderType == 'shopping_list';
            debugPrint('Is shopping list order: $isShoppingList');
          }
          _isLoading = false;
        });
      }
    } catch (e, stackTrace) {
      debugPrint('Error loading order: $e');
      debugPrint('Stack trace: $stackTrace');
      if (mounted) {
        setState(() {
          _error = "Error loading order: ${e.toString()}";
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Order Details'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              // Check if we can pop the current route
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              } else {
                // If we can't pop, navigate to orders screen
                context.go('/buyer/orders');
              }
            },
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                // Search functionality
              },
            ),
            Stack(
              alignment: Alignment.center,
              children: [
                IconButton(
                  icon: const Icon(Icons.shopping_cart_outlined),
                  onPressed: () {
                    // Cart functionality
                  },
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: const Text(
                      '2',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Order Details'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              // Check if we can pop the current route
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              } else {
                // If we can't pop, navigate to orders screen
                context.go('/buyer/orders');
              }
            },
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Error: $_error',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.error,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(onPressed: _loadOrder, child: const Text('Retry')),
            ],
          ),
        ),
      );
    }

    if (_order == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Order Details'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              // Check if we can pop the current route
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              } else {
                // If we can't pop, navigate to orders screen
                context.go('/buyer/orders');
              }
            },
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('Order not found', style: theme.textTheme.headlineSmall),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => BuyerRoutes.navigateToOrders(context),
                child: const Text('Go to Orders'),
              ),
            ],
          ),
        ),
      );
    }

    final order = _order!;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Details'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we can pop the current route
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              // If we can't pop, navigate to orders screen
              context.go('/buyer/orders');
            }
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Search functionality
            },
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.shopping_cart_outlined),
                onPressed: () {
                  // Cart functionality
                },
              ),
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: const Text(
                    '2',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
      body: RefreshIndicator(
        onRefresh: _loadOrder,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product details section
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ordered on ${DateFormatter.formatDate(order.createdAt)}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(height: 16),
                    // Check if this is a shopping list order
                    if (order.shoppingListId != null ||
                        order.orderType == 'shopping_list')
                      // Shopping list header
                      Row(
                        children: [
                          Icon(
                            Icons.shopping_basket,
                            color: theme.colorScheme.primary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Shopping List Order',
                            style: TextStyle(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),

                    const SizedBox(height: 8),

                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Product image
                        Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child:
                                  order.items.isNotEmpty &&
                                      order.items.first.imageUrl != null
                                  ? Image.network(
                                      order.items.first.imageUrl!,
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                            return Container(
                                              width: 80,
                                              height: 80,
                                              color: Colors.grey[200],
                                              child: Icon(
                                                (order.shoppingListId != null ||
                                                        order.orderType ==
                                                            'shopping_list')
                                                    ? Icons.shopping_basket
                                                    : Icons.image_not_supported,
                                              ),
                                            );
                                          },
                                    )
                                  : Container(
                                      width: 80,
                                      height: 80,
                                      color: Colors.grey[200],
                                      child: Icon(
                                        (order.shoppingListId != null ||
                                                order.orderType ==
                                                    'shopping_list')
                                            ? Icons.shopping_basket
                                            : Icons.image,
                                      ),
                                    ),
                            ),

                            // Shopping list badge
                            if (order.shoppingListId != null ||
                                order.orderType == 'shopping_list')
                              Positioned(
                                top: 0,
                                right: 0,
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.primary,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Icon(
                                    Icons.shopping_basket,
                                    size: 12,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(width: 16),

                        // Product details
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                order.items.isNotEmpty
                                    ? order.items.first.name
                                    : 'Product',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),

                              // Show item count for shopping lists
                              if (order.shoppingListId != null ||
                                  order.orderType == 'shopping_list')
                                Text(
                                  '${order.items.length} items in list',
                                  style: TextStyle(
                                    color: theme.colorScheme.primary,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                )
                              else
                                Text(
                                  'Pantry, 750g',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                ),

                              const SizedBox(height: 8),
                              Text(
                                '₹${order.total.toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                order.items.length > 1
                                    ? '${order.items.length} items'
                                    : '1 item',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Order status
                    Row(
                      children: [
                        Icon(
                          order.status == OrderStatus.delivered
                              ? Icons.check_circle
                              : order.status == OrderStatus.processing
                              ? Icons.hourglass_empty
                              : order.status == OrderStatus.cancelled
                              ? Icons.cancel
                              : Icons.info_outline,
                          color: order.status == OrderStatus.delivered
                              ? theme.colorScheme.primary
                              : order.status == OrderStatus.processing
                              ? Colors.orange
                              : order.status == OrderStatus.cancelled
                              ? theme.colorScheme.error
                              : theme.colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            // Special handling for shopping list orders
                            (order.shoppingListId != null ||
                                        order.orderType == 'shopping_list') &&
                                    order.status == OrderStatus.processing
                                ? 'Waiting for seller to approve shopping list and provide pricing'
                                : order.status == OrderStatus.delivered
                                ? 'Delivered on ${DateFormatter.formatDate(order.updatedAt)}'
                                : order.status == OrderStatus.processing
                                ? 'Waiting for seller to approve and send price quotation'
                                : order.status == OrderStatus.cancelled
                                ? 'Order cancelled'
                                : _getStatusText(order.status),
                            style: TextStyle(
                              color: order.status == OrderStatus.delivered
                                  ? theme.colorScheme.primary
                                  : order.status == OrderStatus.processing
                                  ? Colors.orange
                                  : order.status == OrderStatus.cancelled
                                  ? theme.colorScheme.error
                                  : theme.colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Buttons with modern design
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _isBuyingAgain ? null : _buyAgain,
                              icon: _isBuyingAgain
                                  ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    )
                                  : const Icon(Icons.replay),
                              label: Text(
                                _isBuyingAgain ? 'Processing...' : 'Buy Again',
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: theme.colorScheme.onPrimary,
                                elevation: 0,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: _viewSimilar,
                              icon: const Icon(Icons.search),
                              label: const Text('View Similar'),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: theme.colorScheme.primary,
                                side: BorderSide(
                                  color: theme.colorScheme.primary,
                                ),
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const Divider(height: 1),

              // Order Status Timeline
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Order Status',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Row(
                          children: [
                            TextButton(
                              onPressed: () {
                                // Track order
                                BuyerRoutes.navigateToOrderTracking(
                                  context,
                                  order.id,
                                );
                              },
                              child: Text(
                                'Track Order',
                                style: TextStyle(
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            if (order.status == OrderStatus.outForDelivery)
                              TextButton.icon(
                                onPressed: _showLiveTracking
                                    ? null
                                    : () {
                                        // Show live tracking bottom sheet
                                        _showLiveTrackingBottomSheet(
                                          context,
                                          order,
                                        );
                                      },
                                icon: _showLiveTracking
                                    ? SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: theme.colorScheme.secondary,
                                        ),
                                      )
                                    : Icon(
                                        Icons.location_on,
                                        color: theme.colorScheme.secondary,
                                        size: 16,
                                      ),
                                label: Text(
                                  _showLiveTracking
                                      ? 'Opening...'
                                      : 'Live Track',
                                  style: TextStyle(
                                    color: _showLiveTracking
                                        ? theme.colorScheme.secondary
                                              .withValues(alpha: 0.6)
                                        : theme.colorScheme.secondary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Status timeline
                    _buildStatusTimeline(context, order),

                    // Write Review button (only for delivered orders)
                    if (_canWriteReview(order)) ...[
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: FilledButton.icon(
                          onPressed: () =>
                              _navigateToReviewScreen(context, order),
                          icon: const Icon(Icons.rate_review),
                          label: const Text('Write a Review'),
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              const Divider(height: 1),

              // Order Information
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order Information',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Order ID
                    _buildInfoRow(
                      'Order ID',
                      '#${StringUtils.safeSubstring(order.id, 0, 8)}',
                      showDivider: true,
                    ),

                    // Order Date
                    _buildInfoRow(
                      'Order Date',
                      DateFormatter.formatDate(order.createdAt),
                      showDivider: true,
                    ),

                    // Payment Method
                    _buildInfoRow(
                      'Payment Method',
                      order.paymentMethod.toString().split('.').last,
                      showDivider: false,
                    ),
                  ],
                ),
              ),

              const Divider(height: 1),

              // Shipping Address
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Shipping Address',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            // Change address functionality
                          },
                          child: Text(
                            'Change',
                            style: TextStyle(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      order.deliveryAddress.contactName,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      order.deliveryAddress.street,
                      style: TextStyle(color: Colors.grey[700]),
                    ),
                    Text(
                      '${order.deliveryAddress.city}, ${order.deliveryAddress.state} ${order.deliveryAddress.postalCode}',
                      style: TextStyle(color: Colors.grey[700]),
                    ),
                    Text(
                      'Phone: ${order.deliveryAddress.contactPhone}',
                      style: TextStyle(color: Colors.grey[700]),
                    ),
                  ],
                ),
              ),

              const Divider(height: 1),

              // Shopping List Items (only shown for shopping list orders)
              if (order.shoppingListId != null ||
                  order.orderType == 'shopping_list')
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Shopping List Items',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${order.items.length} items',
                            style: TextStyle(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // List of all items in the shopping list
                      ...order.items.map(
                        (item) => Padding(
                          padding: const EdgeInsets.only(bottom: 12.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Item image or placeholder
                              Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: item.imageUrl != null
                                    ? Image.network(
                                        item.imageUrl!,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                              return const Icon(
                                                Icons.shopping_basket,
                                              );
                                            },
                                      )
                                    : const Icon(Icons.shopping_basket),
                              ),
                              const SizedBox(width: 12),

                              // Item details
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      item.name,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Quantity: ${item.quantity}',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Price
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    '₹${item.price.toStringAsFixed(2)}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'per item',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              const Divider(height: 1),

              // You might also like section (only shown for regular orders)
              if (order.shoppingListId == null &&
                  order.orderType != 'shopping_list')
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'You might also be interested in',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Horizontal product list
                      SizedBox(
                        height: 200,
                        child: order.items.isEmpty
                            ? Center(
                                child: Text(
                                  'No similar products available',
                                  style: TextStyle(color: Colors.grey[600]),
                                ),
                              )
                            : ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: order.items.length,
                                itemBuilder: (context, index) {
                                  final item = order.items[index];
                                  return Container(
                                    width: 120,
                                    margin: const EdgeInsets.only(right: 12),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // Product image
                                        ClipRRect(
                                          borderRadius: BorderRadius.circular(
                                            4,
                                          ),
                                          child: item.imageUrl != null
                                              ? Image.network(
                                                  item.imageUrl!,
                                                  height: 120,
                                                  width: 120,
                                                  fit: BoxFit.cover,
                                                  errorBuilder:
                                                      (
                                                        context,
                                                        error,
                                                        stackTrace,
                                                      ) {
                                                        return Container(
                                                          height: 120,
                                                          width: 120,
                                                          color:
                                                              Colors.grey[200],
                                                          child: const Icon(
                                                            Icons
                                                                .image_not_supported,
                                                          ),
                                                        );
                                                      },
                                                )
                                              : Container(
                                                  height: 120,
                                                  width: 120,
                                                  color: Colors.grey[200],
                                                  child: const Icon(
                                                    Icons.image,
                                                  ),
                                                ),
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          item.name,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          '₹${item.price.toStringAsFixed(2)}',
                                          style: TextStyle(
                                            color: theme.colorScheme.primary,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                      ),
                    ],
                  ),
                ),

              const Divider(height: 1),

              // Order Summary with modern card design
              Card(
                margin: const EdgeInsets.all(16.0),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Order Summary',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          ElevatedButton.icon(
                            onPressed: _isGeneratingInvoice
                                ? null
                                : _generateInvoice,
                            icon: _isGeneratingInvoice
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  )
                                : const Icon(Icons.download, size: 16),
                            label: const Text('Download Invoice'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  theme.colorScheme.primaryContainer,
                              foregroundColor:
                                  theme.colorScheme.onPrimaryContainer,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Price breakdown with improved styling
                      _buildSummaryRow(
                        'Item Total (without tax)',
                        _calculateBasePrice(order),
                        style: TextStyle(fontSize: 14, color: Colors.grey[800]),
                      ),
                      _buildSummaryRow(
                        'Discount',
                        _calculateDiscount(order),
                        style: TextStyle(fontSize: 14, color: Colors.grey[800]),
                      ),
                      _buildSummaryRow(
                        'Delivery Fee',
                        _deliveryFee,
                        style: TextStyle(fontSize: 14, color: Colors.grey[800]),
                      ),
                      _buildSummaryRow(
                        _getGstRateText(order),
                        _calculateTax(order),
                        style: TextStyle(fontSize: 14, color: Colors.grey[800]),
                      ),

                      const Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Divider(),
                      ),

                      // Total with improved styling
                      _buildSummaryRow(
                        'Order Total',
                        _calculateBasePrice(order) +
                            _deliveryFee +
                            _calculateTax(order) -
                            _calculateDiscount(order),
                        isTotal: true,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: theme.colorScheme.primary,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Payment method with icon
                      Row(
                        children: [
                          Icon(
                            order.paymentMethod == PaymentMethod.cod
                                ? Icons.payments_outlined
                                : Icons.credit_card,
                            size: 14,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Paid using ${_getPaymentMethodText(order.paymentMethod)}',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Payment and Delivery Options (shown when order is confirmed by seller)
              if (order.status == OrderStatus.confirmed)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Payment Options',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Payment options
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                // Navigate to payment screen
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Processing online payment...',
                                    ),
                                  ),
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: theme.colorScheme.onPrimary,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                minimumSize: const Size(double.infinity, 48),
                              ),
                              child: const Text('Pay Now'),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                // Set payment method to COD
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Cash on Delivery selected'),
                                  ),
                                );
                              },
                              style: OutlinedButton.styleFrom(
                                side: BorderSide(
                                  color: theme.colorScheme.primary,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                minimumSize: const Size(double.infinity, 48),
                              ),
                              child: const Text('Cash on Delivery'),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 24),

                      Text(
                        'Delivery Options',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Delivery options
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                // Select delivery option
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Delivery selected'),
                                  ),
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: theme.colorScheme.onPrimary,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                minimumSize: const Size(double.infinity, 48),
                              ),
                              child: const Text('Home Delivery'),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                // Select Drive-Thru option
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Drive-Thru selected'),
                                  ),
                                );
                              },
                              style: OutlinedButton.styleFrom(
                                side: BorderSide(
                                  color: theme.colorScheme.primary,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                minimumSize: const Size(double.infinity, 48),
                              ),
                              child: const Text('Self Pickup'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

              // Send Order Details button
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: OutlinedButton(
                  onPressed: () {
                    // Share order details
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Sharing order details...')),
                    );
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: theme.colorScheme.primary),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    minimumSize: const Size(double.infinity, 48),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.share,
                        color: theme.colorScheme.primary,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Send Order Details',
                        style: TextStyle(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Removed unused method

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.processing:
        return 'Waiting for Seller';
      case OrderStatus.readyForPickup:
        return 'Ready for Pickup';
      case OrderStatus.inTransit:
        return 'In Transit';
      case OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.refunded:
        return 'Refunded';
    }
  }

  String _getPaymentMethodText(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cod:
        return 'Cash on Delivery';
      case PaymentMethod.online:
        return 'Online Payment';
    }
  }

  // Check if the order is eligible for review (delivered status)
  bool _canWriteReview(OrderModel order) {
    return order.status == OrderStatus.delivered;
  }

  // Navigate to the review screen
  void _navigateToReviewScreen(BuildContext context, OrderModel order) {
    // Get the first product ID from the order
    if (order.items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No products found in this order')),
      );
      return;
    }

    // Navigate to the create review screen with the first product ID
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateReviewScreen(
          productId: order.items.first.productId,
          orderId: order.id,
        ),
      ),
    );
  }

  // Removed unused method

  Widget _buildSummaryRow(
    String label,
    double amount, {
    TextStyle? style,
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal
                ? const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)
                : null,
          ),
          Text(
            '₹${amount.toStringAsFixed(2)}',
            style: isTotal
                ? TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Theme.of(context).colorScheme.primary,
                  )
                : style,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusTimeline(BuildContext context, OrderModel order) {
    final theme = Theme.of(context);

    // Define the status steps
    final List<Map<String, dynamic>> steps = [
      {
        'title': 'Order Placed',
        'subtitle': DateFormatter.formatDate(order.createdAt),
        'icon': Icons.shopping_bag_outlined,
        'isCompleted': true,
      },
      {
        'title': 'Processing',
        'subtitle': order.status == OrderStatus.processing
            ? 'Waiting for seller approval and pricing'
            : 'Preparing your order',
        'icon': Icons.inventory_2_outlined,
        'isCompleted': order.status.index >= OrderStatus.processing.index,
      },
      {
        'title': 'Shipped',
        'subtitle': 'On the way',
        'icon': Icons.local_shipping_outlined,
        'isCompleted': order.status.index >= OrderStatus.inTransit.index,
      },
      {
        'title': 'Delivered',
        'subtitle': order.status == OrderStatus.delivered
            ? DateFormatter.formatDate(order.updatedAt)
            : 'Pending',
        'icon': Icons.check_circle_outline,
        'isCompleted': order.status == OrderStatus.delivered,
      },
    ];

    return Column(
      children: List.generate(steps.length, (index) {
        final step = steps[index];
        final isLast = index == steps.length - 1;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status icon and line
            Column(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: step['isCompleted']
                        ? theme.colorScheme.primary
                        : Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    step['icon'],
                    size: 16,
                    color: step['isCompleted']
                        ? Colors.white
                        : Colors.grey[600],
                  ),
                ),
                if (!isLast)
                  Container(
                    width: 2,
                    height: 40,
                    color: step['isCompleted']
                        ? theme.colorScheme.primary
                        : Colors.grey[300],
                  ),
              ],
            ),
            const SizedBox(width: 16),

            // Status text
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(bottom: isLast ? 0 : 24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      step['title'],
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: step['isCompleted']
                            ? Colors.black
                            : Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      step['subtitle'],
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool showDivider = false}) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(label, style: const TextStyle(color: Colors.black87)),
              Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
            ],
          ),
        ),
        if (showDivider) const Divider(height: 1),
      ],
    );
  }

  // Get category from product name
  String _getCategoryFromName(String name) {
    // Convert to lowercase for easier matching
    final lowerName = name.toLowerCase();

    // Check for fresh fruits (0% GST)
    if (lowerName.contains('apple') ||
        lowerName.contains('banana') ||
        lowerName.contains('orange') ||
        lowerName.contains('grape') ||
        lowerName.contains('mango') ||
        lowerName.contains('pineapple') ||
        lowerName.contains('watermelon') ||
        lowerName.contains('papaya') ||
        lowerName.contains('guava') ||
        lowerName.contains('kiwi') ||
        lowerName.contains('strawberry') ||
        lowerName.contains('cherry') ||
        lowerName.contains('coconut') ||
        lowerName.contains('date') ||
        lowerName.contains('fig') ||
        lowerName.contains('avocado') ||
        lowerName.contains('lemon') ||
        lowerName.contains('lime') ||
        lowerName.contains('peach') ||
        lowerName.contains('pear') ||
        lowerName.contains('plum')) {
      return 'fresh_fruits'; // 0% GST
    }

    // Check for fresh vegetables (0% GST)
    if (lowerName.contains('rice') ||
        lowerName.contains('dal') ||
        lowerName.contains('flour') ||
        lowerName.contains('atta') ||
        lowerName.contains('wheat') ||
        lowerName.contains('sugar') ||
        lowerName.contains('salt') ||
        lowerName.contains('potato') ||
        lowerName.contains('tomato') ||
        lowerName.contains('onion') ||
        lowerName.contains('carrot') ||
        lowerName.contains('cabbage') ||
        lowerName.contains('cauliflower') ||
        lowerName.contains('spinach') ||
        lowerName.contains('cucumber') ||
        lowerName.contains('peas') ||
        lowerName.contains('beans') ||
        lowerName.contains('broccoli') ||
        lowerName.contains('capsicum') ||
        lowerName.contains('garlic') ||
        lowerName.contains('ginger')) {
      return 'fresh_vegetables'; // 0% GST
    }

    // Check for processed food
    if (lowerName.contains('biscuit') ||
        lowerName.contains('cake') ||
        lowerName.contains('pastry') ||
        lowerName.contains('chocolate') ||
        lowerName.contains('ice cream') ||
        lowerName.contains('sauce') ||
        lowerName.contains('jam') ||
        lowerName.contains('ketchup')) {
      return 'processed_food'; // 18% GST
    }

    // Check for electronics
    if (lowerName.contains('phone') ||
        lowerName.contains('tv') ||
        lowerName.contains('laptop') ||
        lowerName.contains('computer') ||
        lowerName.contains('camera') ||
        lowerName.contains('headphone') ||
        lowerName.contains('speaker')) {
      return 'electronics'; // 18% GST
    }

    // Check for clothing
    if (lowerName.contains('shirt') ||
        lowerName.contains('pant') ||
        lowerName.contains('dress') ||
        lowerName.contains('cloth') ||
        lowerName.contains('wear') ||
        lowerName.contains('apparel')) {
      return 'clothing_above_1000'; // 12% GST
    }

    // Check for medicines
    if (lowerName.contains('medicine') ||
        lowerName.contains('tablet') ||
        lowerName.contains('syrup') ||
        lowerName.contains('capsule')) {
      return 'medicine'; // 5% GST
    }

    // Default to general category
    return 'general';
  }

  // Calculate the base price of all items (excluding tax)
  double _calculateBasePrice(OrderModel order) {
    double total = 0.0;
    for (var item in order.items) {
      // Use the product name to determine category
      String category = _getCategoryFromName(item.name);
      double baseItemPrice = GstUtils.calculateBasePrice(item.price, category);
      total += baseItemPrice * item.quantity;
    }
    return total;
  }

  // Removed unused method

  // Calculate the discount (if any)
  double _calculateDiscount(OrderModel order) {
    // In this implementation, we don't have a discount field
    // This could be extended in the future
    return 0.0;
  }

  // Calculate the tax amount
  double _calculateTax(OrderModel order) {
    double totalTax = 0.0;
    for (var item in order.items) {
      // Use the product name to determine category
      String category = _getCategoryFromName(item.name);
      double itemTax =
          GstUtils.calculateGstAmount(item.price, category) * item.quantity;
      totalTax += itemTax;
    }
    return totalTax;
  }

  // Get the GST rate for display
  String _getGstRateText(OrderModel order) {
    // If all items have the same GST rate, show that rate
    // Otherwise show "GST" without a specific rate
    if (order.items.isEmpty) return "Tax";

    // Get category for first item
    String firstCategory = _getCategoryFromName(order.items.first.name);
    double firstRate = GstUtils.getGstRate(firstCategory);

    // Check if all items have the same rate
    bool allSameRate = order.items.every((item) {
      String category = _getCategoryFromName(item.name);
      return GstUtils.getGstRate(category) == firstRate;
    });

    if (allSameRate) {
      return "GST (${(firstRate * 100).toStringAsFixed(0)}%)";
    }

    return "GST";
  }

  // Get delivery fee from order or calculate it
  double _getDeliveryFee(OrderModel order) {
    // If order has shipping details with delivery charge, use it
    if (order.shippingDetails != null &&
        order.shippingDetails!.deliveryCharge != null &&
        order.shippingDetails!.deliveryCharge! > 0) {
      return order.shippingDetails!.deliveryCharge!;
    }

    // Otherwise use a default based on order type
    if (order.orderType == 'shopping_list') {
      return 40.0; // Default for shopping list orders
    } else if (order.shippingDetails != null &&
        order.shippingDetails!.providerType ==
            DeliveryProviderType.localDelivery) {
      return 30.0; // Default for local delivery
    } else {
      return 50.0; // Default for Ecom Express
    }
  }

  // Removed unused method

  // Generate and download invoice
  Future<void> _generateInvoice() async {
    if (_order == null) return;

    setState(() {
      _isGeneratingInvoice = true;
    });

    try {
      // Check if invoice already exists, if not generate it
      final existingPath = await InvoiceService.getInvoicePath(_order!.id);
      _invoicePath =
          existingPath ?? await InvoiceService.generateInvoice(_order!);

      // Download and open the invoice
      if (_invoicePath != null) {
        final file = File(_invoicePath!);
        if (await file.exists()) {
          // Save to downloads and open the file
          await InvoiceService.downloadAndOpenInvoice(_order!.id);

          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Invoice downloaded successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to generate invoice: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingInvoice = false;
        });
      }
    }
  }

  // Handle "Buy Again" functionality
  Future<void> _buyAgain() async {
    if (_order == null || _order!.items.isEmpty) return;

    setState(() {
      _isBuyingAgain = true;
    });

    try {
      // Navigate to the product detail page of the first item
      // or to the shopping list creation page for shopping list orders
      if (_order!.shoppingListId != null ||
          _order!.orderType == 'shopping_list') {
        // For shopping list orders, create a new shopping list with the same items
        if (mounted) {
          // Navigate to shopping list screen with pre-filled items
          final items = _order!.items.map((item) => item.name).join(',');
          context.push('/buyer/shopping/create?items=$items');
        }
      } else {
        // For regular orders, navigate to the product detail page
        if (_order!.items.isNotEmpty && mounted) {
          context.push('/buyer/product/${_order!.items.first.productId}');
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to process request: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isBuyingAgain = false;
        });
      }
    }
  }

  // Handle "View Similar" functionality
  void _viewSimilar() {
    if (_order == null || _order!.items.isEmpty) return;

    // For shopping list orders, navigate to the shopping category
    if (_order!.shoppingListId != null ||
        _order!.orderType == 'shopping_list') {
      context.push('/buyer/categories/groceries');
    } else {
      // For regular orders, navigate to the product category
      if (_order!.items.isNotEmpty) {
        // Ideally, we would have a category field in the order item
        // For now, we'll just navigate to a generic category
        context.push('/buyer/categories/all');
      }
    }
  }

  // Show live tracking bottom sheet
  void _showLiveTrackingBottomSheet(BuildContext context, OrderModel order) {
    setState(() {
      _showLiveTracking = true;
    });

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return LiveTrackingBottomSheet(orderId: order.id, order: order);
      },
    ).then((_) {
      setState(() {
        _showLiveTracking = false;
      });
    });
  }
}
