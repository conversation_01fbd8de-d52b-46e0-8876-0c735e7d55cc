import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../providers/shopping/shopping_list_provider.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';

/// Dialog for creating a new shopping list in Excel-like sheet format
class CreateShoppingListDialog extends ConsumerStatefulWidget {
  const CreateShoppingListDialog({super.key});

  @override
  ConsumerState<CreateShoppingListDialog> createState() =>
      _CreateShoppingListDialogState();
}

class _CreateShoppingListDialogState
    extends ConsumerState<CreateShoppingListDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _searchController = TextEditingController();
  final List<ShoppingListItem> _items = [];
  final List<TextEditingController> _nameControllers = [];
  final List<TextEditingController> _quantityControllers = [];
  final List<String> _selectedUnits = [];
  bool _isSearching = false;
  final List<String> _unitOptions = ['kg', 'ltr', 'pcs', 'gm', 'ml', 'box'];

  @override
  void initState() {
    super.initState();
    _addNewRow();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _searchController.dispose();
    for (final controller in _nameControllers) {
      controller.dispose();
    }
    for (final controller in _quantityControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _addNewRow() {
    final nameController = TextEditingController();
    final quantityController = TextEditingController(text: '1');

    setState(() {
      _items.add(
        ShoppingListItem(
          id: const Uuid().v4(),
          name: '',
          price: 0,
          quantity: 1,
          isChecked: false,
        ),
      );
      _nameControllers.add(nameController);
      _quantityControllers.add(quantityController);
      _selectedUnits.add('kg');
    });
  }

  void _removeRow(int index) {
    setState(() {
      _items.removeAt(index);
      _nameControllers.removeAt(index).dispose();
      _quantityControllers.removeAt(index).dispose();
      _selectedUnits.removeAt(index);
    });
  }

  Future<void> _createList() async {
    if (!_formKey.currentState!.validate()) return;

    final user = ref.read(currentUserProvider).value;
    if (user == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please sign in to create a shopping list'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Update items with current values
    for (int i = 0; i < _items.length; i++) {
      _items[i] = _items[i].copyWith(
        name: _nameControllers[i].text,
        quantity: int.tryParse(_quantityControllers[i].text) ?? 1,
      );
    }

    // Filter out empty items
    final validItems = _items.where((item) => item.name.isNotEmpty).toList();

    if (validItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add at least one item to the list'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final now = DateTime.now();
      final list = ShoppingListModel(
        id: const Uuid().v4(),
        name: _nameController.text,
        description: _descriptionController.text,
        itemCount: validItems.length,
        totalPrice: 0, // Price will be set by seller
        isShared: false,
        createdAt: now,
        updatedAt: now,
        createdBy: user.id,
        items: validItems,
      );

      await ref.read(shoppingListServiceProvider).createShoppingList(list);

      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create shopping list: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _addSearchedItem(String itemName) {
    setState(() {
      _isSearching = false;
      _searchController.clear();

      // Add the searched item to the list
      final nameController = TextEditingController(text: itemName);
      final quantityController = TextEditingController(text: '1');

      _items.add(
        ShoppingListItem(
          id: const Uuid().v4(),
          name: itemName,
          price: 0,
          quantity: 1,
          isChecked: false,
        ),
      );
      _nameControllers.add(nameController);
      _quantityControllers.add(quantityController);
      _selectedUnits.add('kg');
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog.fullscreen(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Create Shopping List'),
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
          actions: [
            TextButton(
              onPressed: _createList,
              child: const Text('SAVE'),
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'List Name',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a name for the list';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description (Optional)',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
              const Divider(),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    const SizedBox(width: 24), // Serial number width
                    const SizedBox(width: 8),
                    const Text('Item Name',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const Spacer(),
                    const Text('Unit',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(width: 16),
                    const Text('Qty',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(width: 16),
                    const SizedBox(width: 40), // Action button width
                  ],
                ),
              ),
              const Divider(),
              Expanded(
                child: _isSearching
                    ? _buildSearchResults()
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _items.length,
                        itemBuilder: (context, index) {
                          return _buildItemRow(index);
                        },
                      ),
              ),
              const Divider(),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _addNewRow,
                        icon: const Icon(Icons.add),
                        label: const Text('Add Item'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _isSearching = true;
                          });
                        },
                        icon: const Icon(Icons.search),
                        label: const Text('Search Items'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.secondary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItemRow(int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // Serial number
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '${index + 1}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),

          // Item name
          Expanded(
            child: TextFormField(
              controller: _nameControllers[index],
              decoration: const InputDecoration(
                hintText: 'Enter item name',
                isDense: true,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Required';
                }
                return null;
              },
              onChanged: (value) {
                _items[index] = _items[index].copyWith(name: value);
              },
            ),
          ),
          const SizedBox(width: 8),

          // Unit dropdown
          SizedBox(
            width: 80,
            child: DropdownButtonFormField<String>(
              value: _selectedUnits[index],
              decoration: const InputDecoration(
                isDense: true,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                border: OutlineInputBorder(),
              ),
              items: _unitOptions.map((unit) {
                return DropdownMenuItem<String>(
                  value: unit,
                  child: Text(unit),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedUnits[index] = value;
                  });
                }
              },
            ),
          ),
          const SizedBox(width: 8),

          // Quantity
          SizedBox(
            width: 60,
            child: TextFormField(
              controller: _quantityControllers[index],
              decoration: const InputDecoration(
                hintText: 'Qty',
                isDense: true,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Required';
                }
                if (int.tryParse(value) == null) {
                  return 'Invalid';
                }
                return null;
              },
              onChanged: (value) {
                _items[index] = _items[index].copyWith(
                  quantity: int.tryParse(value) ?? 1,
                );
              },
            ),
          ),
          const SizedBox(width: 8),

          // Delete button
          IconButton(
            icon: const Icon(Icons.delete, color: Colors.red),
            onPressed: () => _removeRow(index),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    // Mock search results - in a real app, this would come from a database
    final searchResults = [
      'Rice',
      'Wheat Flour',
      'Sugar',
      'Salt',
      'Cooking Oil',
      'Milk',
      'Eggs',
      'Bread',
      'Butter',
      'Cheese',
    ];

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              labelText: 'Search Items',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _isSearching = false;
                    _searchController.clear();
                  });
                },
              ),
              border: const OutlineInputBorder(),
            ),
            autofocus: true,
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: searchResults.length,
            itemBuilder: (context, index) {
              final item = searchResults[index];
              return ListTile(
                title: Text(item),
                trailing: IconButton(
                  icon: const Icon(Icons.add_circle, color: Colors.green),
                  onPressed: () => _addSearchedItem(item),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
