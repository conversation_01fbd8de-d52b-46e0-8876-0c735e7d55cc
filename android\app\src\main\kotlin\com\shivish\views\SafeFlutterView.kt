﻿package com.shivish.views

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import io.flutter.embedding.android.FlutterView
import io.flutter.embedding.android.RenderMode
import io.flutter.embedding.android.TransparencyMode

/**
 * A safe wrapper around FlutterView that handles potential crashes gracefully
 */
class SafeFlutterView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FlutterView(
    context,
    RenderMode.surface,
    TransparencyMode.transparent
) {

    private val TAG = "SafeFlutterView"

    init {
        try {
            // Initialize with error handling
            Log.d(TAG, "SafeFlutterView initialized")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing SafeFlutterView: ${e.message}")
        }
    }

    override fun onAttachedToWindow() {
        try {
            super.onAttachedToWindow()
            Log.d(TAG, "SafeFlutterView attached to window")
        } catch (e: Exception) {
            Log.e(TAG, "Error attaching SafeFlutterView to window: ${e.message}")
        }
    }

    override fun onDetachedFromWindow() {
        try {
            super.onDetachedFromWindow()
            Log.d(TAG, "SafeFlutterView detached from window")
        } catch (e: Exception) {
            Log.e(TAG, "Error detaching SafeFlutterView from window: ${e.message}")
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        try {
            super.onSizeChanged(w, h, oldw, oldh)
            Log.d(TAG, "SafeFlutterView size changed: ${w}x${h}")
        } catch (e: Exception) {
            Log.e(TAG, "Error handling size change in SafeFlutterView: ${e.message}")
        }
    }
}
