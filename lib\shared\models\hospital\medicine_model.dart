import 'package:freezed_annotation/freezed_annotation.dart';
import 'medicine_category.dart';

part 'medicine_model.freezed.dart';
part 'medicine_model.g.dart';

enum MedicineType {
  @JsonValue('tablet')
  tablet,
  @JsonValue('capsule')
  capsule,
  @JsonValue('syrup')
  syrup,
  @JsonValue('injection')
  injection,
  @JsonValue('cream')
  cream,
  @JsonValue('ointment')
  ointment,
  @JsonValue('drops')
  drops,
  @JsonValue('inhaler')
  inhaler,
  @JsonValue('powder')
  powder,
  @JsonValue('other')
  other
}

@freezed
abstract class MedicineModel with _$MedicineModel {
  const factory MedicineModel({
    required String id,
    required String name,
    required String description,
    required String hospitalId,
    required MedicineType type,
    required MedicineCategory category,
    required String manufacturer,
    required String composition,
    required double price,
    required int stockQuantity,
    required String dosageForm,
    required String strength,
    required bool requiresPrescription,
    required bool isAvailable,
    String? imageUrl,
    required String batchNumber,
    required DateTime expiryDate,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isDeleted,
    DateTime? deletedAt,
  }) = _MedicineModel;

  factory MedicineModel.fromJson(Map<String, dynamic> json) =>
      _$MedicineModelFromJson(json);

  /// Creates an empty [MedicineModel]
  factory MedicineModel.empty() => MedicineModel(
        id: '',
        name: '',
        description: '',
        hospitalId: '',
        type: MedicineType.tablet,
        category: MedicineCategory.other,
        manufacturer: '',
        composition: '',
        price: 0.0,
        stockQuantity: 0,
        dosageForm: '',
        strength: '',
        batchNumber: '',
        requiresPrescription: true,
        isAvailable: true,
        expiryDate: DateTime.now().add(const Duration(days: 365)),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
}
