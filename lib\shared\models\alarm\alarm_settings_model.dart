

import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'alarm_settings_model.freezed.dart';
part 'alarm_settings_model.g.dart';

@freezed
abstract class NotificationSettings with _$NotificationSettings {
  const factory NotificationSettings({
    @Default(true) bool sound,
    @Default(true) bool vibration,
    @Default(true) bool led,
  }) = _NotificationSettings;

  factory NotificationSettings.fromJson(Map<String, dynamic> json) =>
      _$NotificationSettingsFromJson(json);
}

@freezed
abstract class AlarmSettingsModel with _$AlarmSettingsModel {
  const factory AlarmSettingsModel({
    @Default('06:00') String defaultTimeString,
    @Default(5) int snoozeDuration,
    @Default(1.0) double volume,
    @Default(true) bool vibrate,
    @Default(false) bool aiEnabled,
    @Default(NotificationSettings()) NotificationSettings notificationSettings,
  }) = _AlarmSettingsModel;

  factory AlarmSettingsModel.fromJson(Map<String, dynamic> json) =>
      _$AlarmSettingsModelFromJson(json);
}

/// Business logic extensions for AlarmSettingsModel
extension AlarmSettingsModelX on AlarmSettingsModel {
  /// Get default time as TimeOfDay
  TimeOfDay get defaultTime {
    final parts = defaultTimeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  /// Update default time from TimeOfDay
  AlarmSettingsModel withDefaultTime(TimeOfDay time) {
    return copyWith(
      defaultTimeString:
          '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}',
    );
  }

  /// Check if all notifications are enabled
  bool get allNotificationsEnabled {
    return notificationSettings.sound &&
           notificationSettings.vibration &&
           notificationSettings.led;
  }

  /// Get volume percentage
  int get volumePercentage => (volume * 100).round();

  /// Set volume from percentage
  AlarmSettingsModel withVolumePercentage(int percentage) {
    return copyWith(volume: percentage / 100.0);
  }

  /// Get formatted snooze duration
  String get formattedSnoozeDuration {
    if (snoozeDuration == 1) {
      return '1 minute';
    }
    return '$snoozeDuration minutes';
  }

  /// Create default settings
  static AlarmSettingsModel get defaultSettings {
    return const AlarmSettingsModel();
  }

  /// Create settings optimized for AI
  static AlarmSettingsModel get aiOptimized {
    return const AlarmSettingsModel(
      aiEnabled: true,
      snoozeDuration: 3, // Shorter snooze for AI learning
      volume: 0.8, // Slightly lower volume for gradual wake-up
    );
  }

  /// Toggle AI enabled state
  AlarmSettingsModel toggleAI() {
    return copyWith(aiEnabled: !aiEnabled);
  }

  /// Toggle vibration
  AlarmSettingsModel toggleVibration() {
    return copyWith(vibrate: !vibrate);
  }

  /// Update notification settings
  AlarmSettingsModel withNotificationSettings({
    bool? sound,
    bool? vibration,
    bool? led,
  }) {
    return copyWith(
      notificationSettings: notificationSettings.copyWith(
        sound: sound ?? notificationSettings.sound,
        vibration: vibration ?? notificationSettings.vibration,
        led: led ?? notificationSettings.led,
      ),
    );
  }

  /// Check if settings are valid
  bool get isValid {
    return defaultTimeString.isNotEmpty &&
           snoozeDuration > 0 &&
           volume >= 0.0 && volume <= 1.0;
  }

  /// Get notification count (how many notification types are enabled)
  int get enabledNotificationCount {
    int count = 0;
    if (notificationSettings.sound) count++;
    if (notificationSettings.vibration) count++;
    if (notificationSettings.led) count++;
    return count;
  }
}

/// Business logic extensions for NotificationSettings
extension NotificationSettingsX on NotificationSettings {
  /// Check if any notification is enabled
  bool get hasAnyEnabled => sound || vibration || led;

  /// Check if all notifications are disabled
  bool get allDisabled => !sound && !vibration && !led;

  /// Get enabled notifications as list
  List<String> get enabledNotifications {
    final enabled = <String>[];
    if (sound) enabled.add('Sound');
    if (vibration) enabled.add('Vibration');
    if (led) enabled.add('LED');
    return enabled;
  }

  /// Get formatted enabled notifications string
  String get formattedEnabledNotifications {
    final enabled = enabledNotifications;
    if (enabled.isEmpty) return 'None';
    if (enabled.length == 1) return enabled.first;
    if (enabled.length == 2) return '${enabled[0]} & ${enabled[1]}';
    return '${enabled.sublist(0, enabled.length - 1).join(', ')} & ${enabled.last}';
  }
}

/// Utility class for TimeOfDay conversion
class TimeOfDayConverter {
  const TimeOfDayConverter();

  static TimeOfDay fromJson(String json) {
    final parts = json.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  static String toJson(TimeOfDay time) =>
      '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
}
