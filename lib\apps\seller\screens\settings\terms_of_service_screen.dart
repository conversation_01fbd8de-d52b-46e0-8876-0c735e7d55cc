import 'package:flutter/material.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppToolbar(
        title: 'Terms of Service',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Terms of Service',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            Text(
              'Last Updated: ${DateTime.now().toString().substring(0, 10)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey,
                  ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Please read these Terms of Service ("Terms") carefully before using the SHIVISH mobile application ("App") operated by SHIVISH ("we", "our", or "us").',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            _buildSection(
              context,
              title: 'Acceptance of Terms',
              content:
                  'By accessing or using the App, you agree to be bound by these Terms. If you disagree with any part of the Terms, you may not access the App.',
            ),
            _buildSection(
              context,
              title: 'Changes to Terms',
              content:
                  'We reserve the right to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days\' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion.',
            ),
            _buildSection(
              context,
              title: 'Account Registration',
              content:
                  'To use certain features of the App, you must register for an account. You must provide accurate, current, and complete information during the registration process and keep your account information up-to-date.\n\nYou are responsible for safeguarding the password that you use to access the App and for any activities or actions under your password. We encourage you to use a strong password (a password that uses a combination of upper and lower case letters, numbers, and symbols) with your account.',
            ),
            _buildSection(
              context,
              title: 'Seller Obligations',
              content:
                  'As a seller on our platform, you agree to:\n\n• Provide accurate and complete information about your products and services\n• Maintain the quality of products and services as described\n• Process orders promptly and communicate with buyers about order status\n• Comply with all applicable laws and regulations\n• Not engage in any fraudulent, deceptive, or misleading practices\n• Maintain reasonable inventory levels for listed products\n• Honor the prices and terms as listed on the platform\n• Respond to customer inquiries and complaints in a timely manner',
            ),
            _buildSection(
              context,
              title: 'Fees and Payments',
              content:
                  'We may charge fees for certain features or services. You agree to pay all fees and charges incurred in connection with your account. Fees are non-refundable except as required by law or as explicitly stated otherwise.\n\nPayments to sellers will be processed according to our payment terms, which may be updated from time to time. We may withhold payments or impose other penalties if we determine, in our sole discretion, that you have violated these Terms.',
            ),
            _buildSection(
              context,
              title: 'Intellectual Property',
              content:
                  'The App and its original content, features, and functionality are and will remain the exclusive property of SHIVISH and its licensors. The App is protected by copyright, trademark, and other laws of both India and foreign countries.\n\nOur trademarks and trade dress may not be used in connection with any product or service without the prior written consent of SHIVISH.',
            ),
            _buildSection(
              context,
              title: 'Termination',
              content:
                  'We may terminate or suspend your account and bar access to the App immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms.\n\nIf you wish to terminate your account, you may simply discontinue using the App or contact us to request account deletion.',
            ),
            _buildSection(
              context,
              title: 'Limitation of Liability',
              content:
                  'In no event shall SHIVISH, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the App.',
            ),
            _buildSection(
              context,
              title: 'Governing Law',
              content:
                  'These Terms shall be governed and construed in accordance with the laws of India, without regard to its conflict of law provisions.\n\nOur failure to enforce any right or provision of these Terms will not be considered a waiver of those rights.',
            ),
            _buildSection(
              context,
              title: 'Contact Us',
              content:
                  'If you have any questions about these Terms, please contact us at:\n\nEmail: <EMAIL>\nPhone: +91 1234567890\nAddress: 123 Main Street, City, State, PIN Code, India',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required String content,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(fontSize: 16, height: 1.5),
          ),
        ],
      ),
    );
  }
}
