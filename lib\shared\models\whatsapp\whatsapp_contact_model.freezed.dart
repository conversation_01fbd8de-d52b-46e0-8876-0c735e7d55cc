// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'whatsapp_contact_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MessageTemplate {

 String get id; String get name; String get content; MessageTemplateType get type; String? get description; bool get isActive; DateTime get createdAt; DateTime get updatedAt; bool get isDeleted;
/// Create a copy of MessageTemplate
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MessageTemplateCopyWith<MessageTemplate> get copyWith => _$MessageTemplateCopyWithImpl<MessageTemplate>(this as MessageTemplate, _$identity);

  /// Serializes this MessageTemplate to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MessageTemplate&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.content, content) || other.content == content)&&(identical(other.type, type) || other.type == type)&&(identical(other.description, description) || other.description == description)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,content,type,description,isActive,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'MessageTemplate(id: $id, name: $name, content: $content, type: $type, description: $description, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $MessageTemplateCopyWith<$Res>  {
  factory $MessageTemplateCopyWith(MessageTemplate value, $Res Function(MessageTemplate) _then) = _$MessageTemplateCopyWithImpl;
@useResult
$Res call({
 String id, String name, String content, MessageTemplateType type, String? description, bool isActive, DateTime createdAt, DateTime updatedAt, bool isDeleted
});




}
/// @nodoc
class _$MessageTemplateCopyWithImpl<$Res>
    implements $MessageTemplateCopyWith<$Res> {
  _$MessageTemplateCopyWithImpl(this._self, this._then);

  final MessageTemplate _self;
  final $Res Function(MessageTemplate) _then;

/// Create a copy of MessageTemplate
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? content = null,Object? type = null,Object? description = freezed,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as MessageTemplateType,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [MessageTemplate].
extension MessageTemplatePatterns on MessageTemplate {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MessageTemplate value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MessageTemplate() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MessageTemplate value)  $default,){
final _that = this;
switch (_that) {
case _MessageTemplate():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MessageTemplate value)?  $default,){
final _that = this;
switch (_that) {
case _MessageTemplate() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String content,  MessageTemplateType type,  String? description,  bool isActive,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MessageTemplate() when $default != null:
return $default(_that.id,_that.name,_that.content,_that.type,_that.description,_that.isActive,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String content,  MessageTemplateType type,  String? description,  bool isActive,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _MessageTemplate():
return $default(_that.id,_that.name,_that.content,_that.type,_that.description,_that.isActive,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String content,  MessageTemplateType type,  String? description,  bool isActive,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _MessageTemplate() when $default != null:
return $default(_that.id,_that.name,_that.content,_that.type,_that.description,_that.isActive,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MessageTemplate implements MessageTemplate {
  const _MessageTemplate({required this.id, required this.name, required this.content, required this.type, this.description, this.isActive = true, required this.createdAt, required this.updatedAt, this.isDeleted = false});
  factory _MessageTemplate.fromJson(Map<String, dynamic> json) => _$MessageTemplateFromJson(json);

@override final  String id;
@override final  String name;
@override final  String content;
@override final  MessageTemplateType type;
@override final  String? description;
@override@JsonKey() final  bool isActive;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool isDeleted;

/// Create a copy of MessageTemplate
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MessageTemplateCopyWith<_MessageTemplate> get copyWith => __$MessageTemplateCopyWithImpl<_MessageTemplate>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MessageTemplateToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MessageTemplate&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.content, content) || other.content == content)&&(identical(other.type, type) || other.type == type)&&(identical(other.description, description) || other.description == description)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,content,type,description,isActive,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'MessageTemplate(id: $id, name: $name, content: $content, type: $type, description: $description, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$MessageTemplateCopyWith<$Res> implements $MessageTemplateCopyWith<$Res> {
  factory _$MessageTemplateCopyWith(_MessageTemplate value, $Res Function(_MessageTemplate) _then) = __$MessageTemplateCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String content, MessageTemplateType type, String? description, bool isActive, DateTime createdAt, DateTime updatedAt, bool isDeleted
});




}
/// @nodoc
class __$MessageTemplateCopyWithImpl<$Res>
    implements _$MessageTemplateCopyWith<$Res> {
  __$MessageTemplateCopyWithImpl(this._self, this._then);

  final _MessageTemplate _self;
  final $Res Function(_MessageTemplate) _then;

/// Create a copy of MessageTemplate
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? content = null,Object? type = null,Object? description = freezed,Object? isActive = null,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,}) {
  return _then(_MessageTemplate(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as MessageTemplateType,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$MessageHistory {

 String get id; String get message; MessageStatus get status; String? get templateId; DateTime? get sentAt; DateTime? get deliveredAt; DateTime? get readAt; String? get errorMessage; DateTime get createdAt; DateTime get updatedAt; bool get isDeleted;
/// Create a copy of MessageHistory
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MessageHistoryCopyWith<MessageHistory> get copyWith => _$MessageHistoryCopyWithImpl<MessageHistory>(this as MessageHistory, _$identity);

  /// Serializes this MessageHistory to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MessageHistory&&(identical(other.id, id) || other.id == id)&&(identical(other.message, message) || other.message == message)&&(identical(other.status, status) || other.status == status)&&(identical(other.templateId, templateId) || other.templateId == templateId)&&(identical(other.sentAt, sentAt) || other.sentAt == sentAt)&&(identical(other.deliveredAt, deliveredAt) || other.deliveredAt == deliveredAt)&&(identical(other.readAt, readAt) || other.readAt == readAt)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,message,status,templateId,sentAt,deliveredAt,readAt,errorMessage,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'MessageHistory(id: $id, message: $message, status: $status, templateId: $templateId, sentAt: $sentAt, deliveredAt: $deliveredAt, readAt: $readAt, errorMessage: $errorMessage, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class $MessageHistoryCopyWith<$Res>  {
  factory $MessageHistoryCopyWith(MessageHistory value, $Res Function(MessageHistory) _then) = _$MessageHistoryCopyWithImpl;
@useResult
$Res call({
 String id, String message, MessageStatus status, String? templateId, DateTime? sentAt, DateTime? deliveredAt, DateTime? readAt, String? errorMessage, DateTime createdAt, DateTime updatedAt, bool isDeleted
});




}
/// @nodoc
class _$MessageHistoryCopyWithImpl<$Res>
    implements $MessageHistoryCopyWith<$Res> {
  _$MessageHistoryCopyWithImpl(this._self, this._then);

  final MessageHistory _self;
  final $Res Function(MessageHistory) _then;

/// Create a copy of MessageHistory
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? message = null,Object? status = null,Object? templateId = freezed,Object? sentAt = freezed,Object? deliveredAt = freezed,Object? readAt = freezed,Object? errorMessage = freezed,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as MessageStatus,templateId: freezed == templateId ? _self.templateId : templateId // ignore: cast_nullable_to_non_nullable
as String?,sentAt: freezed == sentAt ? _self.sentAt : sentAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deliveredAt: freezed == deliveredAt ? _self.deliveredAt : deliveredAt // ignore: cast_nullable_to_non_nullable
as DateTime?,readAt: freezed == readAt ? _self.readAt : readAt // ignore: cast_nullable_to_non_nullable
as DateTime?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [MessageHistory].
extension MessageHistoryPatterns on MessageHistory {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MessageHistory value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MessageHistory() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MessageHistory value)  $default,){
final _that = this;
switch (_that) {
case _MessageHistory():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MessageHistory value)?  $default,){
final _that = this;
switch (_that) {
case _MessageHistory() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String message,  MessageStatus status,  String? templateId,  DateTime? sentAt,  DateTime? deliveredAt,  DateTime? readAt,  String? errorMessage,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MessageHistory() when $default != null:
return $default(_that.id,_that.message,_that.status,_that.templateId,_that.sentAt,_that.deliveredAt,_that.readAt,_that.errorMessage,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String message,  MessageStatus status,  String? templateId,  DateTime? sentAt,  DateTime? deliveredAt,  DateTime? readAt,  String? errorMessage,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted)  $default,) {final _that = this;
switch (_that) {
case _MessageHistory():
return $default(_that.id,_that.message,_that.status,_that.templateId,_that.sentAt,_that.deliveredAt,_that.readAt,_that.errorMessage,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String message,  MessageStatus status,  String? templateId,  DateTime? sentAt,  DateTime? deliveredAt,  DateTime? readAt,  String? errorMessage,  DateTime createdAt,  DateTime updatedAt,  bool isDeleted)?  $default,) {final _that = this;
switch (_that) {
case _MessageHistory() when $default != null:
return $default(_that.id,_that.message,_that.status,_that.templateId,_that.sentAt,_that.deliveredAt,_that.readAt,_that.errorMessage,_that.createdAt,_that.updatedAt,_that.isDeleted);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MessageHistory implements MessageHistory {
  const _MessageHistory({required this.id, required this.message, required this.status, this.templateId, this.sentAt, this.deliveredAt, this.readAt, this.errorMessage, required this.createdAt, required this.updatedAt, this.isDeleted = false});
  factory _MessageHistory.fromJson(Map<String, dynamic> json) => _$MessageHistoryFromJson(json);

@override final  String id;
@override final  String message;
@override final  MessageStatus status;
@override final  String? templateId;
@override final  DateTime? sentAt;
@override final  DateTime? deliveredAt;
@override final  DateTime? readAt;
@override final  String? errorMessage;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;
@override@JsonKey() final  bool isDeleted;

/// Create a copy of MessageHistory
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MessageHistoryCopyWith<_MessageHistory> get copyWith => __$MessageHistoryCopyWithImpl<_MessageHistory>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MessageHistoryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MessageHistory&&(identical(other.id, id) || other.id == id)&&(identical(other.message, message) || other.message == message)&&(identical(other.status, status) || other.status == status)&&(identical(other.templateId, templateId) || other.templateId == templateId)&&(identical(other.sentAt, sentAt) || other.sentAt == sentAt)&&(identical(other.deliveredAt, deliveredAt) || other.deliveredAt == deliveredAt)&&(identical(other.readAt, readAt) || other.readAt == readAt)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,message,status,templateId,sentAt,deliveredAt,readAt,errorMessage,createdAt,updatedAt,isDeleted);

@override
String toString() {
  return 'MessageHistory(id: $id, message: $message, status: $status, templateId: $templateId, sentAt: $sentAt, deliveredAt: $deliveredAt, readAt: $readAt, errorMessage: $errorMessage, createdAt: $createdAt, updatedAt: $updatedAt, isDeleted: $isDeleted)';
}


}

/// @nodoc
abstract mixin class _$MessageHistoryCopyWith<$Res> implements $MessageHistoryCopyWith<$Res> {
  factory _$MessageHistoryCopyWith(_MessageHistory value, $Res Function(_MessageHistory) _then) = __$MessageHistoryCopyWithImpl;
@override @useResult
$Res call({
 String id, String message, MessageStatus status, String? templateId, DateTime? sentAt, DateTime? deliveredAt, DateTime? readAt, String? errorMessage, DateTime createdAt, DateTime updatedAt, bool isDeleted
});




}
/// @nodoc
class __$MessageHistoryCopyWithImpl<$Res>
    implements _$MessageHistoryCopyWith<$Res> {
  __$MessageHistoryCopyWithImpl(this._self, this._then);

  final _MessageHistory _self;
  final $Res Function(_MessageHistory) _then;

/// Create a copy of MessageHistory
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? message = null,Object? status = null,Object? templateId = freezed,Object? sentAt = freezed,Object? deliveredAt = freezed,Object? readAt = freezed,Object? errorMessage = freezed,Object? createdAt = null,Object? updatedAt = null,Object? isDeleted = null,}) {
  return _then(_MessageHistory(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as MessageStatus,templateId: freezed == templateId ? _self.templateId : templateId // ignore: cast_nullable_to_non_nullable
as String?,sentAt: freezed == sentAt ? _self.sentAt : sentAt // ignore: cast_nullable_to_non_nullable
as DateTime?,deliveredAt: freezed == deliveredAt ? _self.deliveredAt : deliveredAt // ignore: cast_nullable_to_non_nullable
as DateTime?,readAt: freezed == readAt ? _self.readAt : readAt // ignore: cast_nullable_to_non_nullable
as DateTime?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$WhatsAppContactModel {

 String get id; String get name; String get phoneNumber; String get userId; ContactType get type; ContactStatus get status; bool get isDeleted; List<String> get messageTemplates; List<MessageHistory> get messageHistory; Map<String, dynamic> get metadata; String? get profilePicture; String? get about; String? get notes; DateTime get createdAt; DateTime get updatedAt;
/// Create a copy of WhatsAppContactModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WhatsAppContactModelCopyWith<WhatsAppContactModel> get copyWith => _$WhatsAppContactModelCopyWithImpl<WhatsAppContactModel>(this as WhatsAppContactModel, _$identity);

  /// Serializes this WhatsAppContactModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WhatsAppContactModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&const DeepCollectionEquality().equals(other.messageTemplates, messageTemplates)&&const DeepCollectionEquality().equals(other.messageHistory, messageHistory)&&const DeepCollectionEquality().equals(other.metadata, metadata)&&(identical(other.profilePicture, profilePicture) || other.profilePicture == profilePicture)&&(identical(other.about, about) || other.about == about)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,phoneNumber,userId,type,status,isDeleted,const DeepCollectionEquality().hash(messageTemplates),const DeepCollectionEquality().hash(messageHistory),const DeepCollectionEquality().hash(metadata),profilePicture,about,notes,createdAt,updatedAt);

@override
String toString() {
  return 'WhatsAppContactModel(id: $id, name: $name, phoneNumber: $phoneNumber, userId: $userId, type: $type, status: $status, isDeleted: $isDeleted, messageTemplates: $messageTemplates, messageHistory: $messageHistory, metadata: $metadata, profilePicture: $profilePicture, about: $about, notes: $notes, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $WhatsAppContactModelCopyWith<$Res>  {
  factory $WhatsAppContactModelCopyWith(WhatsAppContactModel value, $Res Function(WhatsAppContactModel) _then) = _$WhatsAppContactModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String phoneNumber, String userId, ContactType type, ContactStatus status, bool isDeleted, List<String> messageTemplates, List<MessageHistory> messageHistory, Map<String, dynamic> metadata, String? profilePicture, String? about, String? notes, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class _$WhatsAppContactModelCopyWithImpl<$Res>
    implements $WhatsAppContactModelCopyWith<$Res> {
  _$WhatsAppContactModelCopyWithImpl(this._self, this._then);

  final WhatsAppContactModel _self;
  final $Res Function(WhatsAppContactModel) _then;

/// Create a copy of WhatsAppContactModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? phoneNumber = null,Object? userId = null,Object? type = null,Object? status = null,Object? isDeleted = null,Object? messageTemplates = null,Object? messageHistory = null,Object? metadata = null,Object? profilePicture = freezed,Object? about = freezed,Object? notes = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ContactType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ContactStatus,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,messageTemplates: null == messageTemplates ? _self.messageTemplates : messageTemplates // ignore: cast_nullable_to_non_nullable
as List<String>,messageHistory: null == messageHistory ? _self.messageHistory : messageHistory // ignore: cast_nullable_to_non_nullable
as List<MessageHistory>,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,profilePicture: freezed == profilePicture ? _self.profilePicture : profilePicture // ignore: cast_nullable_to_non_nullable
as String?,about: freezed == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [WhatsAppContactModel].
extension WhatsAppContactModelPatterns on WhatsAppContactModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WhatsAppContactModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WhatsAppContactModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WhatsAppContactModel value)  $default,){
final _that = this;
switch (_that) {
case _WhatsAppContactModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WhatsAppContactModel value)?  $default,){
final _that = this;
switch (_that) {
case _WhatsAppContactModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String phoneNumber,  String userId,  ContactType type,  ContactStatus status,  bool isDeleted,  List<String> messageTemplates,  List<MessageHistory> messageHistory,  Map<String, dynamic> metadata,  String? profilePicture,  String? about,  String? notes,  DateTime createdAt,  DateTime updatedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WhatsAppContactModel() when $default != null:
return $default(_that.id,_that.name,_that.phoneNumber,_that.userId,_that.type,_that.status,_that.isDeleted,_that.messageTemplates,_that.messageHistory,_that.metadata,_that.profilePicture,_that.about,_that.notes,_that.createdAt,_that.updatedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String phoneNumber,  String userId,  ContactType type,  ContactStatus status,  bool isDeleted,  List<String> messageTemplates,  List<MessageHistory> messageHistory,  Map<String, dynamic> metadata,  String? profilePicture,  String? about,  String? notes,  DateTime createdAt,  DateTime updatedAt)  $default,) {final _that = this;
switch (_that) {
case _WhatsAppContactModel():
return $default(_that.id,_that.name,_that.phoneNumber,_that.userId,_that.type,_that.status,_that.isDeleted,_that.messageTemplates,_that.messageHistory,_that.metadata,_that.profilePicture,_that.about,_that.notes,_that.createdAt,_that.updatedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String phoneNumber,  String userId,  ContactType type,  ContactStatus status,  bool isDeleted,  List<String> messageTemplates,  List<MessageHistory> messageHistory,  Map<String, dynamic> metadata,  String? profilePicture,  String? about,  String? notes,  DateTime createdAt,  DateTime updatedAt)?  $default,) {final _that = this;
switch (_that) {
case _WhatsAppContactModel() when $default != null:
return $default(_that.id,_that.name,_that.phoneNumber,_that.userId,_that.type,_that.status,_that.isDeleted,_that.messageTemplates,_that.messageHistory,_that.metadata,_that.profilePicture,_that.about,_that.notes,_that.createdAt,_that.updatedAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WhatsAppContactModel implements WhatsAppContactModel {
  const _WhatsAppContactModel({this.id = '', required this.name, required this.phoneNumber, required this.userId, required this.type, this.status = ContactStatus.active, this.isDeleted = false, final  List<String> messageTemplates = const [], final  List<MessageHistory> messageHistory = const [], final  Map<String, dynamic> metadata = const {}, this.profilePicture, this.about, this.notes, required this.createdAt, required this.updatedAt}): _messageTemplates = messageTemplates,_messageHistory = messageHistory,_metadata = metadata;
  factory _WhatsAppContactModel.fromJson(Map<String, dynamic> json) => _$WhatsAppContactModelFromJson(json);

@override@JsonKey() final  String id;
@override final  String name;
@override final  String phoneNumber;
@override final  String userId;
@override final  ContactType type;
@override@JsonKey() final  ContactStatus status;
@override@JsonKey() final  bool isDeleted;
 final  List<String> _messageTemplates;
@override@JsonKey() List<String> get messageTemplates {
  if (_messageTemplates is EqualUnmodifiableListView) return _messageTemplates;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_messageTemplates);
}

 final  List<MessageHistory> _messageHistory;
@override@JsonKey() List<MessageHistory> get messageHistory {
  if (_messageHistory is EqualUnmodifiableListView) return _messageHistory;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_messageHistory);
}

 final  Map<String, dynamic> _metadata;
@override@JsonKey() Map<String, dynamic> get metadata {
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_metadata);
}

@override final  String? profilePicture;
@override final  String? about;
@override final  String? notes;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;

/// Create a copy of WhatsAppContactModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WhatsAppContactModelCopyWith<_WhatsAppContactModel> get copyWith => __$WhatsAppContactModelCopyWithImpl<_WhatsAppContactModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WhatsAppContactModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WhatsAppContactModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&const DeepCollectionEquality().equals(other._messageTemplates, _messageTemplates)&&const DeepCollectionEquality().equals(other._messageHistory, _messageHistory)&&const DeepCollectionEquality().equals(other._metadata, _metadata)&&(identical(other.profilePicture, profilePicture) || other.profilePicture == profilePicture)&&(identical(other.about, about) || other.about == about)&&(identical(other.notes, notes) || other.notes == notes)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,phoneNumber,userId,type,status,isDeleted,const DeepCollectionEquality().hash(_messageTemplates),const DeepCollectionEquality().hash(_messageHistory),const DeepCollectionEquality().hash(_metadata),profilePicture,about,notes,createdAt,updatedAt);

@override
String toString() {
  return 'WhatsAppContactModel(id: $id, name: $name, phoneNumber: $phoneNumber, userId: $userId, type: $type, status: $status, isDeleted: $isDeleted, messageTemplates: $messageTemplates, messageHistory: $messageHistory, metadata: $metadata, profilePicture: $profilePicture, about: $about, notes: $notes, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$WhatsAppContactModelCopyWith<$Res> implements $WhatsAppContactModelCopyWith<$Res> {
  factory _$WhatsAppContactModelCopyWith(_WhatsAppContactModel value, $Res Function(_WhatsAppContactModel) _then) = __$WhatsAppContactModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String phoneNumber, String userId, ContactType type, ContactStatus status, bool isDeleted, List<String> messageTemplates, List<MessageHistory> messageHistory, Map<String, dynamic> metadata, String? profilePicture, String? about, String? notes, DateTime createdAt, DateTime updatedAt
});




}
/// @nodoc
class __$WhatsAppContactModelCopyWithImpl<$Res>
    implements _$WhatsAppContactModelCopyWith<$Res> {
  __$WhatsAppContactModelCopyWithImpl(this._self, this._then);

  final _WhatsAppContactModel _self;
  final $Res Function(_WhatsAppContactModel) _then;

/// Create a copy of WhatsAppContactModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? phoneNumber = null,Object? userId = null,Object? type = null,Object? status = null,Object? isDeleted = null,Object? messageTemplates = null,Object? messageHistory = null,Object? metadata = null,Object? profilePicture = freezed,Object? about = freezed,Object? notes = freezed,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_WhatsAppContactModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,phoneNumber: null == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ContactType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ContactStatus,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,messageTemplates: null == messageTemplates ? _self._messageTemplates : messageTemplates // ignore: cast_nullable_to_non_nullable
as List<String>,messageHistory: null == messageHistory ? _self._messageHistory : messageHistory // ignore: cast_nullable_to_non_nullable
as List<MessageHistory>,metadata: null == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>,profilePicture: freezed == profilePicture ? _self.profilePicture : profilePicture // ignore: cast_nullable_to_non_nullable
as String?,about: freezed == about ? _self.about : about // ignore: cast_nullable_to_non_nullable
as String?,notes: freezed == notes ? _self.notes : notes // ignore: cast_nullable_to_non_nullable
as String?,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
