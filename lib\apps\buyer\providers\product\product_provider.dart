import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/product/product_model.dart';
import '../../../../shared/services/product/product_service.dart';

/// Provider for the product service
final productServiceProvider = Provider<ProductService>((ref) {
  return ProductService();
});

/// Provider for all products
final productsProvider = StreamProvider<List<ProductModel>>((ref) {
  return ref.watch(productServiceProvider).getProducts();
});

/// Provider for featured products
final featuredProductsProvider = StreamProvider<List<ProductModel>>((ref) {
  return ref.watch(productServiceProvider).getFeaturedProducts();
});

/// Provider for latest products
final latestProductsProvider = StreamProvider<List<ProductModel>>((ref) {
  return ref.watch(productServiceProvider).getLatestProducts();
});

/// Provider for products by category
final productsByCategoryProvider =
    StreamProvider.family<List<ProductModel>, String>((ref, categoryId) {
  return ref.watch(productServiceProvider).getProductsByCategory(categoryId);
});

/// Provider for products by seller
final productsBySellerProvider =
    StreamProvider.family<List<ProductModel>, String>((ref, sellerId) {
  return ref.watch(productServiceProvider).getProductsBySeller(sellerId);
});

/// Provider for a single product
final productProvider =
    StreamProvider.family<ProductModel?, String>((ref, productId) {
  return ref.watch(productServiceProvider).getProduct(productId);
});

/// Provider for product search results
final productSearchProvider =
    StreamProvider.family<List<ProductModel>, String>((ref, query) {
  return ref.watch(productServiceProvider).searchProducts(query);
});

/// Provider for product recommendations
final productRecommendationsProvider =
    StreamProvider.family<List<ProductModel>, String>((ref, productId) {
  return ref.watch(productServiceProvider).getRecommendations(productId);
});

/// Provider for product reviews
final productReviewsProvider =
    StreamProvider.family<List<dynamic>, String>((ref, productId) {
  return ref.watch(productServiceProvider).getProductReviews(productId);
});

/// Provider for product statistics
final productStatisticsProvider =
    FutureProvider.family<Map<String, dynamic>, String>((ref, productId) {
  return ref.watch(productServiceProvider).getProductStatistics(productId);
});
