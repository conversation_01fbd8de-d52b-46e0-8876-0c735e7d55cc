import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';
import '../../technician_routes.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

/// Screen shown when a technician account is pending approval
class PendingApprovalScreen extends ConsumerStatefulWidget {
  const PendingApprovalScreen({super.key});

  @override
  ConsumerState<PendingApprovalScreen> createState() => _PendingApprovalScreenState();
}

class _PendingApprovalScreenState extends ConsumerState<PendingApprovalScreen> {
  StreamSubscription? _approvalSubscription;
  bool _isCheckingStatus = false;
  String? _technicianName;
  String? _technicianEmail;
  String? _registrationDate;

  @override
  void initState() {
    super.initState();
    // Start listening for approval status changes after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _loadTechnicianInfo();
        _setupApprovalListener();
      }
    });
  }

  @override
  void dispose() {
    // Cancel the subscription when the widget is disposed
    _approvalSubscription?.cancel();
    super.dispose();
  }

  // Load basic technician information
  Future<void> _loadTechnicianInfo() async {
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) return;

      final databaseService = ref.read(databaseServiceProvider);
      final data = await databaseService.find('technicians', userId);

      if (data != null && mounted) {
        setState(() {
          _technicianName = data['name'] ?? 'Unknown';
          _technicianEmail = data['email'] ?? 'Unknown';

          // Format registration date
          if (data['created_at'] != null) {
            final createdAt = data['created_at'] as String;
            _registrationDate = createdAt.split('T')[0];
          } else {
            _registrationDate = 'N/A';
          }
        });
      }
    } catch (e) {
      debugPrint('Error loading technician info: $e');
    }
  }

  // Set up a listener for changes to the technician's approval status
  void _setupApprovalListener() {
    final userId = Supabase.instance.client.auth.currentUser?.id;
    if (userId == null) {
      debugPrint('Cannot setup approval listener: User ID is null');
      return;
    }

    debugPrint('Setting up approval listener for technician with ID: $userId');

    // Use periodic checking since we don't have real-time streaming yet
    Timer.periodic(const Duration(seconds: 10), (timer) async {
      if (!mounted) {
        timer.cancel();
        return;
      }

      try {
        final databaseService = ref.read(databaseServiceProvider);
        final data = await databaseService.find('technicians', userId);

        if (data != null) {
          final verificationStatus = data['verification_status'] as String? ?? 'pending';
          final isActive = data['is_active'] == true;

          debugPrint('Verification status: $verificationStatus, isActive: $isActive');

          if (verificationStatus == 'approved' && isActive) {
            timer.cancel();
            _navigateToHome();
          }
        }
      } catch (e) {
        debugPrint('Error checking approval status: $e');
      }
    });
  }

  // Check approval status manually
  Future<void> _checkApprovalStatus() async {
    if (_isCheckingStatus) return;

    setState(() {
      _isCheckingStatus = true;
    });

    try {
      debugPrint('Manually checking approval status');

      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) return;

      // Check the technician document
      final databaseService = ref.read(databaseServiceProvider);
      final data = await databaseService.find('technicians', userId);

      if (data != null) {
        final verificationStatus = data['verification_status'] as String? ?? 'pending';
        final isActive = data['is_active'] == true;

        debugPrint('Technician verification status: $verificationStatus, isActive=$isActive');

        if (verificationStatus == 'approved' && isActive) {
          // Navigate to home screen
          _navigateToHome();
        } else {
          // Show a message that the account is still pending
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Your account is still pending approval.'),
                duration: Duration(seconds: 2),
              ),
            );
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Unable to find your account information.'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error checking approval status: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error checking status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCheckingStatus = false;
        });
      }
    }
  }

  // Navigate to home screen with a success message
  void _navigateToHome() {
    if (!mounted) return;

    debugPrint('Account approved, navigating to home screen');

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Your account has been approved! Welcome to Shivish Technician.'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
      ),
    );

    // Navigate to home screen
    context.go(TechnicianRoutes.home);
  }

  // Sign out
  Future<void> _signOut() async {
    try {
      await Supabase.instance.client.auth.signOut();
      if (mounted) {
        context.go(TechnicianRoutes.login);
      }
    } catch (e) {
      debugPrint('Error signing out: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error signing out: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Account Pending'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _signOut,
          ),
        ],
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.pending_outlined,
                  size: 80,
                  color: Colors.orange,
                ),
                const SizedBox(height: 24),
                Text(
                  'Account Pending Approval',
                  style: theme.textTheme.headlineSmall,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'Your technician account is currently under review. This usually takes 1-2 business days.',
                  style: theme.textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                if (_technicianName != null) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildInfoRow('Name', _technicianName!),
                        const SizedBox(height: 8),
                        _buildInfoRow('Email', _technicianEmail ?? 'N/A'),
                        const SizedBox(height: 8),
                        _buildInfoRow('Registration Date', _registrationDate ?? 'N/A'),
                      ],
                    ),
                  ),
                ],
                const SizedBox(height: 32),
                const Text(
                  'We will notify you via email once your account has been approved.',
                  style: TextStyle(
                    color: Colors.grey,
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                AppButton(
                  onPressed: _isCheckingStatus ? null : _checkApprovalStatus,
                  isLoading: _isCheckingStatus,
                  child: const Text('Check Approval Status'),
                ),
                const SizedBox(height: 16),
                AppButton(
                  onPressed: _signOut,
                  variant: AppButtonVariant.secondary,
                  child: const Text('Sign Out'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            '$label:',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: Text(value),
        ),
      ],
    );
  }
}
