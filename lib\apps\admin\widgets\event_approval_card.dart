import 'package:flutter/material.dart';
import '../../../shared/models/event/event_model.dart';
import '../../../shared/ui_components/buttons/app_button.dart';
import '../../../shared/ui_components/dialogs/app_dialog.dart';
import '../../../shared/ui_components/dialogs/text_input_dialog.dart';

class EventApprovalCard extends StatelessWidget {
  final EventModel event;
  final VoidCallback onApprove;
  final Function(String reason) onReject;
  final VoidCallback onEdit;

  const EventApprovalCard({
    super.key,
    required this.event,
    required this.onApprove,
    required this.onReject,
    required this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    event.title,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: onEdit,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              event.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  _getEventTypeIcon(event.type),
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  event.type.name,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: AppButton(
                    onPressed: () => _showRejectDialog(context),
                    child: const Text('Reject'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: AppButton(
                    onPressed: () => _showApproveDialog(context),
                    child: const Text('Approve'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getEventTypeIcon(EventType type) {
    switch (type) {
      case EventType.festival:
        return Icons.celebration;
      case EventType.birthday:
        return Icons.cake;
      case EventType.anniversary:
        return Icons.favorite;
      case EventType.custom:
        return Icons.event;
    }
  }

  Future<void> _showApproveDialog(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const AppDialog(
        title: 'Approve Event',
        message: 'Are you sure you want to approve this event?',
        confirmText: 'Approve',
        cancelText: 'Cancel',
      ),
    );

    if (confirmed == true) {
      onApprove();
    }
  }

  Future<void> _showRejectDialog(BuildContext context) async {
    final reason = await showDialog<String>(
      context: context,
      builder: (context) => const TextInputDialog(
        title: 'Reject Event',
        content: 'Please provide a reason for rejecting this event:',
        hintText: 'Enter rejection reason',
        confirmText: 'Reject',
        cancelText: 'Cancel',
      ),
    );

    if (reason != null && reason.isNotEmpty) {
      onReject(reason);
    }
  }
}
