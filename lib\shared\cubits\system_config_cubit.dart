import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../models/config/system_config_model.dart';
import '../models/config/security_config_model.dart';
import '../models/config/refund_config_model.dart';
import '../models/config/ai_config_model.dart';
import '../models/config/voice_command_config_model.dart';
import '../models/config/chatbot_config_model.dart';
import '../repositories/system_config_repository.dart';

part 'system_config_cubit.freezed.dart';

@freezed
sealed class SystemConfigState with _$SystemConfigState {
  const factory SystemConfigState({
    @Default(false) bool isLoading,
    @Default(null) SystemConfigModel? config,
    @Default(null) String? error,
  }) = _SystemConfigState;
}

class SystemConfigCubit extends Cubit<SystemConfigState> {
  final SystemConfigRepository _repository;

  SystemConfigCubit(this._repository) : super(const SystemConfigState());

  Future<void> loadConfig() async {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      final config = await _repository.getConfig();
      emit(state.copyWith(
        isLoading: false,
        config: config,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> updateConfig(SystemConfigModel newConfig) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      await _repository.updateConfig(newConfig);
      emit(state.copyWith(
        isLoading: false,
        config: newConfig,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> updateSecurityConfig(SecurityConfigModel security) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      final newConfig = state.config?.copyWith(security: security);
      if (newConfig != null) {
        await updateConfig(newConfig);
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> updateRefundConfig(RefundConfigModel refund) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      final newConfig = state.config?.copyWith(refund: refund);
      if (newConfig != null) {
        await updateConfig(newConfig);
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> updateAIConfig(AIConfigModel ai) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      final newConfig = state.config?.copyWith(ai: ai);
      if (newConfig != null) {
        await updateConfig(newConfig);
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> updateVoiceCommandConfig(
      VoiceCommandConfigModel voiceCommand) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      final newConfig = state.config?.copyWith(voiceCommand: voiceCommand);
      if (newConfig != null) {
        await updateConfig(newConfig);
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> updateChatbotConfig(ChatbotConfigModel chatbot) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      final newConfig = state.config?.copyWith(chatbot: chatbot);
      if (newConfig != null) {
        await updateConfig(newConfig);
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }
}
