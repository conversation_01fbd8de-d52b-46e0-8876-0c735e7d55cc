import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/shared/models/order/order_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/order_cubit.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';

class ShippingOptionsDialog extends StatefulWidget {
  final OrderModel order;

  const ShippingOptionsDialog({
    super.key,
    required this.order,
  });

  @override
  State<ShippingOptionsDialog> createState() => _ShippingOptionsDialogState();
}

class _ShippingOptionsDialogState extends State<ShippingOptionsDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _trackingController;
  String _selectedProvider = 'FedEx';
  DateTime? _estimatedDeliveryDate;

  final _providers = ['FedEx', 'DHL', 'UPS', 'USPS', 'Other'];

  @override
  void initState() {
    super.initState();
    _trackingController = TextEditingController(
      text: widget.order.shippingDetails?.trackingNumber,
    );
    _selectedProvider =
        widget.order.shippingDetails?.provider ?? _providers.first;
    _estimatedDeliveryDate =
        widget.order.shippingDetails?.estimatedDeliveryDate;
  }

  @override
  void dispose() {
    _trackingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Shipping Options',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedProvider,
                decoration: const InputDecoration(
                  labelText: 'Shipping Provider',
                  border: OutlineInputBorder(),
                ),
                items: _providers.map((provider) {
                  return DropdownMenuItem(
                    value: provider,
                    child: Text(provider),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedProvider = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _trackingController,
                label: 'Tracking Number',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a tracking number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              ListTile(
                contentPadding: EdgeInsets.zero,
                title: Text(
                  'Estimated Delivery Date',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                subtitle: Text(
                  _estimatedDeliveryDate != null
                      ? '${_estimatedDeliveryDate!.day}/${_estimatedDeliveryDate!.month}/${_estimatedDeliveryDate!.year}'
                      : 'Not set',
                ),
                trailing: IconButton(
                  icon: const Icon(Icons.calendar_today),
                  onPressed: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: _estimatedDeliveryDate ??
                          DateTime.now().add(const Duration(days: 3)),
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 30)),
                    );
                    if (date != null) {
                      setState(() {
                        _estimatedDeliveryDate = date;
                      });
                    }
                  },
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _submit,
                    child: const Text('Save'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _submit() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<OrderCubit>().updateShippingDetails(
            orderId: widget.order.id,
            trackingNumber: _trackingController.text,
            shippingProvider: _selectedProvider,
            estimatedDeliveryDate: _estimatedDeliveryDate,
          );
      Navigator.of(context).pop();
    }
  }
}
