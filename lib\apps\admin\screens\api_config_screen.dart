import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/providers/api_config_provider.dart';
import '../../../shared/services/api_config_service.dart';

class ApiConfigScreen extends ConsumerStatefulWidget {
  const ApiConfigScreen({super.key});

  @override
  ConsumerState<ApiConfigScreen> createState() => _ApiConfigScreenState();
}

class _ApiConfigScreenState extends ConsumerState<ApiConfigScreen> {
  final _formKey = GlobalKey<FormState>();
  final _apiKeyController = TextEditingController();
  final _clientIdController = TextEditingController();
  final _baseUrlController = TextEditingController();

  bool _isLoading = false;
  bool _showApiKey = false;
  bool _showClientId = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentConfig();
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    _clientIdController.dispose();
    _baseUrlController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentConfig() async {
    try {
      final config = await ref.read(easyMyTripConfigProvider.future);
      setState(() {
        _apiKeyController.text = config.apiKey;
        _clientIdController.text = config.clientId;
        _baseUrlController.text = config.baseUrl;
      });
    } catch (e) {
      debugPrint('Failed to load current config: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final configStatus = ref.watch(apiConfigStatusProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('API Configuration'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // ignore: unused_result
              ref.refresh(apiConfigStatusProvider);
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Configuration Status Card
              _buildStatusCard(configStatus),

              const SizedBox(height: 24),

              // EasyMyTrip Configuration Section
              _buildSectionHeader('EasyMyTrip B2B Configuration'),
              const SizedBox(height: 16),

              // Base URL Field
              _buildTextField(
                controller: _baseUrlController,
                label: 'Base URL',
                hint: 'https://api.easemytrip.com/b2b/v1',
                icon: Icons.link,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Base URL is required';
                  }
                  if (Uri.tryParse(value)?.hasAbsolutePath != true) {
                    return 'Please enter a valid URL';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // API Key Field
              _buildTextField(
                controller: _apiKeyController,
                label: 'API Key',
                hint: 'Enter your EasyMyTrip API key',
                icon: Icons.key,
                obscureText: !_showApiKey,
                suffixIcon: IconButton(
                  icon: Icon(
                    _showApiKey ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: () => setState(() => _showApiKey = !_showApiKey),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'API Key is required';
                  }
                  if (value.length < 10) {
                    return 'API Key seems too short';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Client ID Field
              _buildTextField(
                controller: _clientIdController,
                label: 'Client ID',
                hint: 'Enter your EasyMyTrip Client ID',
                icon: Icons.account_circle,
                obscureText: !_showClientId,
                suffixIcon: IconButton(
                  icon: Icon(
                    _showClientId ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: () =>
                      setState(() => _showClientId = !_showClientId),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Client ID is required';
                  }
                  if (value.length < 5) {
                    return 'Client ID seems too short';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 24),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _testConnection,
                      icon: const Icon(Icons.wifi_tethering),
                      label: const Text('Test Connection'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _saveConfiguration,
                      icon: _isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Icon(Icons.save),
                      label: Text(
                        _isLoading ? 'Saving...' : 'Save Configuration',
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Clear Configuration Button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _isLoading ? null : _clearConfiguration,
                  icon: const Icon(Icons.clear_all),
                  label: const Text('Clear Configuration'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red,
                    side: const BorderSide(color: Colors.red),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Help Section
              _buildHelpSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard(AsyncValue<ApiConfigStatus> configStatus) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Configuration Status',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            configStatus.when(
              data: (status) => Column(
                children: [
                  _buildStatusRow(
                    'EasyMyTrip Configured',
                    status.isEasyMyTripConfigured,
                  ),
                  _buildStatusRow('Valid API Key', status.hasValidApiKey),
                  _buildStatusRow('Valid Client ID', status.hasValidClientId),
                  if (status.lastUpdated != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        'Last updated: ${_formatDateTime(status.lastUpdated!)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                ],
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text(
                'Error loading status: $error',
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool isValid) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isValid ? Icons.check_circle : Icons.error,
            color: isValid ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(label),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.blue,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        suffixIcon: suffixIcon,
        border: const OutlineInputBorder(),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: Colors.blue, width: 2),
        ),
      ),
    );
  }

  Widget _buildHelpSection() {
    return Card(
      elevation: 1,
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.help_outline, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Text(
                  'Help & Information',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• Contact EasyMyTrip support to get your B2B API credentials\n'
              '• API Key and Client ID are required for all booking operations\n'
              '• Test connection before saving to ensure credentials are valid\n'
              '• Configuration is stored securely and shared across the app',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testConnection() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final apiConfigService = ref.read(apiConfigServiceProvider);
      final isValid = await apiConfigService.validateEasyMyTripCredentials(
        apiKey: _apiKeyController.text.trim(),
        clientId: _clientIdController.text.trim(),
        baseUrl: _baseUrlController.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isValid
                  ? 'Connection test successful!'
                  : 'Connection test failed. Please check your credentials.',
            ),
            backgroundColor: isValid ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Connection test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _saveConfiguration() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final success = await ref
          .read(apiConfigNotifierProvider.notifier)
          .updateEasyMyTripConfig(
            apiKey: _apiKeyController.text.trim(),
            clientId: _clientIdController.text.trim(),
            baseUrl: _baseUrlController.text.trim(),
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Configuration saved successfully!'
                  : 'Failed to save configuration. Please check your credentials.',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }

      if (success) {
        // Refresh status
        // ignore: unused_result
        ref.refresh(apiConfigStatusProvider);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _clearConfiguration() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Configuration'),
        content: const Text(
          'Are you sure you want to clear the API configuration? '
          'This will disable all booking functionality until reconfigured.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      try {
        await ref
            .read(apiConfigNotifierProvider.notifier)
            .clearEasyMyTripConfig();

        // Clear form fields
        _apiKeyController.clear();
        _clientIdController.clear();
        _baseUrlController.text = 'https://api.easemytrip.com/b2b/v1';

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Configuration cleared successfully'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to clear configuration: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
