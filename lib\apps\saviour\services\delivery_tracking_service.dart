import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/models/delivery/delivery_request_model.dart';
import '../../../shared/utils/logger.dart';
import '../providers/saviour_auth_provider.dart';
import '../providers/location_provider.dart';

final _logger = getLogger('DeliveryTrackingService');

/// Provider for the delivery tracking service
final deliveryTrackingServiceProvider = Provider<DeliveryTrackingService>((
  ref,
) {
  final authState = ref.watch(saviourAuthStateProvider);
  final locationState = ref.watch(saviourLocationProvider);

  return DeliveryTrackingService(
    databaseService: DatabaseService(DatabaseConfig.fromEnvironment()),
    deliveryPersonId: authState.deliveryPerson?.id,
    currentLocation: locationState.lastPosition != null
        ? LatLng(
            locationState.lastPosition!.latitude,
            locationState.lastPosition!.longitude,
          )
        : null,
  );
});

/// Service for tracking delivery progress and ETA
class DeliveryTrackingService {
  final DatabaseService _databaseService;
  final String? deliveryPersonId;
  final LatLng? currentLocation;

  // Database tables
  static const String _deliveryTrackingTable = 'delivery_tracking';
  static const String _deliveryRequestsTable = 'delivery_requests';

  // Update interval for location updates
  static const Duration _updateInterval = Duration(seconds: 30);

  // Active tracking timers
  final Map<String, Timer> _trackingTimers = {};

  DeliveryTrackingService({
    required DatabaseService databaseService,
    required this.deliveryPersonId,
    required this.currentLocation,
  }) : _databaseService = databaseService;

  /// Start tracking a delivery
  Future<bool> startTracking(String deliveryId) async {
    if (deliveryPersonId == null || currentLocation == null) {
      _logger.warning(
        'Cannot start tracking: missing deliveryPersonId or currentLocation',
      );
      return false;
    }

    try {
      // Check if delivery exists and is assigned to this delivery person
      final deliveryData = await _databaseService.find(
        _deliveryRequestsTable,
        deliveryId,
      );

      if (deliveryData == null) {
        _logger.warning('Cannot start tracking: delivery not found');
        return false;
      }

      if (deliveryData['delivery_person_id'] != deliveryPersonId) {
        _logger.warning(
          'Cannot start tracking: delivery not assigned to this delivery person',
        );
        return false;
      }

      final delivery = DeliveryRequestModel.fromJson(deliveryData);

      // Create or update tracking document
      final trackingData = {
        'id': deliveryId,
        'delivery_id': deliveryId,
        'delivery_person_id': deliveryPersonId,
        'current_location': {
          'latitude': currentLocation!.latitude,
          'longitude': currentLocation!.longitude,
        },
        'status': delivery.status.name,
        'last_updated': DateTime.now().toIso8601String(),
        'is_tracking': true,
        'estimated_pickup_time': _calculateEstimatedTime(
          currentLocation!,
          LatLng(
            delivery.pickupLocation.latitude,
            delivery.pickupLocation.longitude,
          ),
        ).toIso8601String(),
        'estimated_delivery_time': _calculateEstimatedTime(
          currentLocation!,
          LatLng(
            delivery.deliveryLocation.latitude,
            delivery.deliveryLocation.longitude,
          ),
        ).toIso8601String(),
        'distance_to_pickup': _calculateDistance(
          currentLocation!,
          LatLng(
            delivery.pickupLocation.latitude,
            delivery.pickupLocation.longitude,
          ),
        ),
        'distance_to_delivery': _calculateDistance(
          LatLng(
            delivery.pickupLocation.latitude,
            delivery.pickupLocation.longitude,
          ),
          LatLng(
            delivery.deliveryLocation.latitude,
            delivery.deliveryLocation.longitude,
          ),
        ),
        'tracking_started': DateTime.now().toIso8601String(),
      };

      // Check if tracking record exists
      final existingTracking = await _databaseService.find(
        _deliveryTrackingTable,
        deliveryId,
      );
      if (existingTracking != null) {
        // Update existing record
        await _databaseService.update(
          _deliveryTrackingTable,
          deliveryId,
          trackingData,
        );
      } else {
        // Create new record
        await _databaseService.create(_deliveryTrackingTable, trackingData);
      }

      // Start periodic location updates
      _startPeriodicUpdates(deliveryId);

      _logger.info('Started tracking delivery: $deliveryId');
      return true;
    } catch (e) {
      _logger.severe('Error starting tracking: $e');
      return false;
    }
  }

  /// Stop tracking a delivery
  Future<bool> stopTracking(String deliveryId) async {
    try {
      // Stop periodic updates
      _stopPeriodicUpdates(deliveryId);

      // Update tracking document
      await _databaseService.update(_deliveryTrackingTable, deliveryId, {
        'is_tracking': false,
        'last_updated': DateTime.now().toIso8601String(),
        'tracking_stopped': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      _logger.info('Stopped tracking delivery: $deliveryId');
      return true;
    } catch (e) {
      _logger.severe('Error stopping tracking: $e');
      return false;
    }
  }

  /// Update tracking information
  Future<bool> updateTracking(
    String deliveryId,
    LatLng location,
    DeliveryRequestStatus status,
  ) async {
    if (deliveryPersonId == null) {
      _logger.warning('Cannot update tracking: deliveryPersonId is null');
      return false;
    }

    try {
      final delivery = await _databaseService.find(
        _deliveryRequestsTable,
        deliveryId,
      );
      if (delivery == null) {
        _logger.warning('Cannot update tracking: delivery not found');
        return false;
      }

      final deliveryModel = DeliveryRequestModel.fromJson(delivery);

      await _databaseService.update(_deliveryTrackingTable, deliveryId, {
        'current_location': {
          'latitude': location.latitude,
          'longitude': location.longitude,
        },
        'status': status.name,
        'last_updated': DateTime.now().toIso8601String(),
        'estimated_pickup_time': _calculateEstimatedTime(
          location,
          LatLng(
            deliveryModel.pickupLocation.latitude,
            deliveryModel.pickupLocation.longitude,
          ),
        ).toIso8601String(),
        'estimated_delivery_time': _calculateEstimatedTime(
          location,
          LatLng(
            deliveryModel.deliveryLocation.latitude,
            deliveryModel.deliveryLocation.longitude,
          ),
        ).toIso8601String(),
        'distance_to_pickup': _calculateDistance(
          location,
          LatLng(
            deliveryModel.pickupLocation.latitude,
            deliveryModel.pickupLocation.longitude,
          ),
        ),
        'distance_to_delivery': _calculateDistance(
          LatLng(
            deliveryModel.pickupLocation.latitude,
            deliveryModel.pickupLocation.longitude,
          ),
          LatLng(
            deliveryModel.deliveryLocation.latitude,
            deliveryModel.deliveryLocation.longitude,
          ),
        ),
        'updated_at': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (e) {
      _logger.severe('Error updating tracking: $e');
      return false;
    }
  }

  /// Get tracking information for a delivery
  Future<Map<String, dynamic>?> getTrackingInfo(String deliveryId) async {
    try {
      return await _databaseService.find(_deliveryTrackingTable, deliveryId);
    } catch (e) {
      _logger.severe('Error getting tracking info: $e');
      return null;
    }
  }

  /// Start periodic location updates
  void _startPeriodicUpdates(String deliveryId) {
    _stopPeriodicUpdates(deliveryId); // Stop any existing timer

    _trackingTimers[deliveryId] = Timer.periodic(_updateInterval, (
      timer,
    ) async {
      if (currentLocation != null) {
        final delivery = await _databaseService.find(
          _deliveryRequestsTable,
          deliveryId,
        );
        if (delivery != null) {
          final deliveryModel = DeliveryRequestModel.fromJson(delivery);
          await updateTracking(
            deliveryId,
            currentLocation!,
            deliveryModel.status,
          );
        }
      }
    });
  }

  /// Stop periodic location updates
  void _stopPeriodicUpdates(String deliveryId) {
    _trackingTimers[deliveryId]?.cancel();
    _trackingTimers.remove(deliveryId);
  }

  /// Calculate estimated time to reach a destination
  DateTime _calculateEstimatedTime(LatLng from, LatLng to) {
    final distance = _calculateDistance(from, to);
    // Assume average speed of 30 km/h in city traffic
    final timeInHours = distance / 30.0;
    final timeInMinutes = (timeInHours * 60).round();
    return DateTime.now().add(Duration(minutes: timeInMinutes));
  }

  /// Calculate distance between two points in kilometers
  double _calculateDistance(LatLng from, LatLng to) {
    return Geolocator.distanceBetween(
          from.latitude,
          from.longitude,
          to.latitude,
          to.longitude,
        ) /
        1000; // Convert meters to kilometers
  }

  /// Dispose of all timers
  void dispose() {
    for (final timer in _trackingTimers.values) {
      timer.cancel();
    }
    _trackingTimers.clear();
  }
}
