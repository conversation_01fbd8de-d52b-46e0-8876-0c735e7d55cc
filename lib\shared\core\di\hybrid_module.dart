import 'package:injectable/injectable.dart';
import 'package:shivish/shared/services/auth/supabase_auth_service.dart';
import 'package:shivish/shared/core/auth/repositories/supabase_auth_repository.dart';
import 'package:shivish/shared/core/auth/repositories/auth_repository.dart';
import 'package:shivish/shared/services/storage_service.dart';

@module
abstract class HybridModule {
  // Hybrid storage module - replaces Firebase services with hybrid PostgreSQL + S3 solution

  @singleton
  StorageService get storageService => StorageService();

  @singleton
  SupabaseAuthService get supabaseAuthService => SupabaseAuthService();

  @singleton
  AuthRepository get authRepository =>
      SupabaseAuthRepository(supabaseAuthService);
}
