import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/safety_tracking_service.dart';
import '../../../shared/providers/auth_provider.dart';
import '../../../shared/utils/logger.dart';

final _logger = getLogger('SafetyTrackingProvider');

/// Production-ready provider for safety tracking service with proper lifecycle management
final safetyTrackingServiceProvider = Provider<SafetyTrackingService>((ref) {
  final service = SafetyTrackingService();

  // Ensure proper disposal when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for current safety tracking session with error handling
final currentSafetySessionProvider =
    StateNotifierProvider<SafetySessionNotifier, SafetyTrackingSession?>((ref) {
      final service = ref.watch(safetyTrackingServiceProvider);
      return SafetySessionNotifier(service);
    });

/// Provider for safety tracking status with null safety
final safetyTrackingStatusProvider = Provider<SafetyTrackingStatus>((ref) {
  final session = ref.watch(currentSafetySessionProvider);
  return session?.status ?? SafetyTrackingStatus.inactive;
});

/// Provider for user's safety configuration with error handling
final safetyConfigProvider =
    FutureProvider.family<SafetyTrackingConfig?, String>((ref, userId) async {
      if (userId.isEmpty) {
        _logger.warning('safetyConfigProvider called with empty userId');
        return null;
      }

      try {
        final service = ref.watch(safetyTrackingServiceProvider);
        return await service.getSafetyConfig(userId);
      } catch (e, stackTrace) {
        _logger.severe(
          'Error loading safety config for user $userId: $e',
          e,
          stackTrace,
        );
        return null;
      }
    });

/// State notifier for safety tracking session with comprehensive error handling
class SafetySessionNotifier extends StateNotifier<SafetyTrackingSession?> {
  final SafetyTrackingService _service;
  bool _isDisposed = false;

  SafetySessionNotifier(this._service) : super(null) {
    try {
      // Listen to service changes
      _service.addListener(_onServiceChanged);
      state = _service.currentSession;
    } catch (e, stackTrace) {
      _logger.severe(
        'Error initializing SafetySessionNotifier: $e',
        e,
        stackTrace,
      );
    }
  }

  void _onServiceChanged() {
    if (_isDisposed) return;

    try {
      state = _service.currentSession;
    } catch (e) {
      _logger.warning('Error updating session state: $e');
    }
  }

  /// Start safety tracking with validation
  Future<bool> startTracking({
    required String rideId,
    required String userId,
    required SafetyTrackingConfig config,
  }) async {
    if (_isDisposed) {
      _logger.warning('Attempted to start tracking on disposed notifier');
      return false;
    }

    if (rideId.isEmpty || userId.isEmpty) {
      _logger.warning('Invalid parameters for starting safety tracking');
      return false;
    }

    try {
      return await _service.startSafetyTracking(
        rideId: rideId,
        userId: userId,
        config: config,
      );
    } catch (e, stackTrace) {
      _logger.severe('Error starting safety tracking: $e', e, stackTrace);
      return false;
    }
  }

  /// Stop safety tracking with error handling
  Future<void> stopTracking() async {
    if (_isDisposed) {
      _logger.warning('Attempted to stop tracking on disposed notifier');
      return;
    }

    try {
      await _service.stopSafetyTracking();
    } catch (e, stackTrace) {
      _logger.severe('Error stopping safety tracking: $e', e, stackTrace);
    }
  }

  /// Verify PIN with validation
  Future<bool> verifyPin(String pin) async {
    if (_isDisposed) {
      _logger.warning('Attempted to verify PIN on disposed notifier');
      return false;
    }

    if (pin.isEmpty) {
      _logger.warning('Empty PIN provided for verification');
      return false;
    }

    try {
      return await _service.verifyPin(pin);
    } catch (e, stackTrace) {
      _logger.severe('Error verifying PIN: $e', e, stackTrace);
      return false;
    }
  }

  /// Cancel emergency alert with error handling
  Future<void> cancelEmergencyAlert() async {
    if (_isDisposed) {
      _logger.warning(
        'Attempted to cancel emergency alert on disposed notifier',
      );
      return;
    }

    try {
      await _service.cancelEmergencyAlert();
    } catch (e, stackTrace) {
      _logger.severe('Error cancelling emergency alert: $e', e, stackTrace);
    }
  }

  @override
  void dispose() {
    if (_isDisposed) return;

    try {
      _service.removeListener(_onServiceChanged);
      _isDisposed = true;
    } catch (e) {
      _logger.warning('Error disposing SafetySessionNotifier: $e');
    } finally {
      super.dispose();
    }
  }
}

/// Provider for safety tracking actions with null-safe user access
final safetyTrackingActionsProvider = Provider<SafetyTrackingActions>((ref) {
  final service = ref.watch(safetyTrackingServiceProvider);
  final auth = ref.watch(authProvider);
  return SafetyTrackingActions(service, auth?.id);
});

/// Production-ready actions for safety tracking with comprehensive error handling
class SafetyTrackingActions {
  final SafetyTrackingService _service;
  final String? _userId;

  SafetyTrackingActions(this._service, this._userId);

  /// Start safety tracking for a ride with validation
  Future<bool> startSafetyTracking({
    required String rideId,
    required SafetyTrackingConfig config,
  }) async {
    if (_userId == null || _userId.isEmpty) {
      _logger.warning('Cannot start safety tracking: user not authenticated');
      return false;
    }

    if (rideId.isEmpty) {
      _logger.warning('Cannot start safety tracking: invalid ride ID');
      return false;
    }

    try {
      return await _service.startSafetyTracking(
        rideId: rideId,
        userId: _userId,
        config: config,
      );
    } catch (e, stackTrace) {
      _logger.severe('Error starting safety tracking: $e', e, stackTrace);
      return false;
    }
  }

  /// Stop safety tracking with error handling
  Future<void> stopSafetyTracking() async {
    try {
      await _service.stopSafetyTracking();
    } catch (e, stackTrace) {
      _logger.severe('Error stopping safety tracking: $e', e, stackTrace);
    }
  }

  /// Verify safety PIN with validation
  Future<bool> verifyPin(String pin) async {
    if (pin.isEmpty) {
      _logger.warning('Cannot verify empty PIN');
      return false;
    }

    try {
      return await _service.verifyPin(pin);
    } catch (e, stackTrace) {
      _logger.severe('Error verifying PIN: $e', e, stackTrace);
      return false;
    }
  }

  /// Cancel emergency alert with error handling
  Future<void> cancelEmergencyAlert() async {
    try {
      await _service.cancelEmergencyAlert();
    } catch (e, stackTrace) {
      _logger.severe('Error cancelling emergency alert: $e', e, stackTrace);
    }
  }

  /// Get user's safety configuration with validation
  Future<SafetyTrackingConfig?> getSafetyConfig() async {
    if (_userId == null || _userId.isEmpty) {
      _logger.warning('Cannot get safety config: user not authenticated');
      return null;
    }

    try {
      return await _service.getSafetyConfig(_userId);
    } catch (e, stackTrace) {
      _logger.severe('Error getting safety config: $e', e, stackTrace);
      return null;
    }
  }

  /// Save user's safety configuration with validation
  Future<void> saveSafetyConfig(SafetyTrackingConfig config) async {
    if (_userId == null || _userId.isEmpty) {
      _logger.warning('Cannot save safety config: user not authenticated');
      return;
    }

    try {
      await _service.saveSafetyConfig(_userId, config);
    } catch (e, stackTrace) {
      _logger.severe('Error saving safety config: $e', e, stackTrace);
    }
  }

  /// Check if safety tracking is active
  bool get isActive {
    try {
      return _service.isActive;
    } catch (e) {
      _logger.warning('Error checking if safety tracking is active: $e');
      return false;
    }
  }

  /// Get current session with error handling
  SafetyTrackingSession? get currentSession {
    try {
      return _service.currentSession;
    } catch (e) {
      _logger.warning('Error getting current session: $e');
      return null;
    }
  }

  /// Get current status with error handling
  SafetyTrackingStatus get status {
    try {
      return _service.status;
    } catch (e) {
      _logger.warning('Error getting safety tracking status: $e');
      return SafetyTrackingStatus.inactive;
    }
  }

  /// Check if user is authenticated
  bool get isUserAuthenticated => _userId != null && _userId.isNotEmpty;

  /// Get user ID (for debugging purposes)
  String? get userId => _userId;
}
