import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uuid/uuid.dart';
import '../../../../shared/sound.dart';

import '../../providers/shopping/shopping_list_provider.dart' as list_provider;
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart'
    as shared_model;
import '../../../../shared/ui_components/navigation/bottom_nav_back_handler.dart';
import '../../buyer_routes.dart';
import '../../../../shared/widgets/sound_gesture_detector.dart';
import '../../../../shared/services/cache/shopping_list_cache_service.dart';

// Import screens
import 'shopping_list_settings_screen.dart';
import 'seller_selection_screen.dart';
import 'address_selection_bottom_sheet.dart';
import '../order/orders_screen.dart';

/// Combined shopping list screen that shows both regular shopping lists
/// and calendar-based shopping lists with details view
class ShoppingListScreen extends ConsumerStatefulWidget {
  const ShoppingListScreen({super.key});

  @override
  ConsumerState<ShoppingListScreen> createState() => _ShoppingListScreenState();
}

class _ShoppingListScreenState extends ConsumerState<ShoppingListScreen> with WidgetsBindingObserver {

  // Form key for validation
  final _formKey = GlobalKey<FormState>();

  // Controllers for list details
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  // Controllers and state for direct item creation
  final List<TextEditingController> _itemNameControllers = [];
  final List<TextEditingController> _itemQuantityControllers = [];
  final List<String> _selectedUnits = [];
  final List<shared_model.ShoppingListItem> _newItems = [];

  // Available units for dropdown
  final List<String> _unitOptions = [
    'kg', // Kilogram
    'gm', // Gram
    'ltr', // Liter
    'ml', // Milliliter
    'pcs', // Pieces
    'dozen', // Dozen
    'box', // Box
    'pack', // Pack
    'bag', // Bag
    'bottle' // Bottle
  ];

  // Available units for reference - all units are now allowed for all items
  // This removes the "inappropriate units" error that was causing confusion

  // Note: Solid items categorization is now handled directly in unit selection logic

  // Common fruit items - can be sold by weight or count
  final List<String> _fruitItems = [
    'apple',
    'banana',
    'orange',
    'grape',
    'watermelon',
    'melon',
    'pineapple',
    'mango',
    'papaya',
    'strawberry',
    'berry',
    'kiwi',
    'pear',
    'peach',
    'plum',
    'cherry',
    'lemon',
    'lime',
    'coconut',
    'avocado',
    'guava',
    'fruit', // Generic fruit
  ];

  // Common liquid items
  final List<String> _liquidItems = [
    'water',
    'milk',
    'oil',
    'juice',
    'soda',
    'drink',
    'beverage',
    'wine',
    'beer',
    'alcohol',
    'vinegar',
    'sauce',
    'syrup',
    'honey',
    'ghee',
    'cream',
    'yogurt',
    'curd',
    'buttermilk',
    'soup',
    'shampoo',
    'conditioner',
    'soap',
    'detergent',
    'cleaner',
    'petrol',
    'diesel',
    'fuel',
  ];

  // Item categorization is now done directly in the unit selection logic
  // This simplifies the code and makes it more maintainable

  // This method is kept for reference but no longer used for validation
  // We now use a simpler approach in _addNewRow that doesn't require validation

  // Search state
  bool _isSearching = false;
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Register as an observer for app lifecycle changes
    WidgetsBinding.instance.addObserver(this);
    // Add an initial empty row
    _addNewRow();
    // Load any cached unchecked items
    _loadCachedUncheckedItems();
  }

  // Load cached unchecked items from previous sessions
  Future<void> _loadCachedUncheckedItems() async {
    final user = ref.read(currentUserProvider).value;
    if (user == null) return;

    // Get the cache service
    final cacheService = ShoppingListCacheService();
    await cacheService.initialize();

    // Get cached unchecked items for this user
    final uncheckedItems = cacheService.getUncheckedItems(user.id);

    // Always update the UI with cached items, even if empty
    // This ensures we're always showing the latest state
    setState(() {
      // Clear existing items
      for (final controller in _itemNameControllers) {
        controller.dispose();
      }
      for (final controller in _itemQuantityControllers) {
        controller.dispose();
      }

      _itemNameControllers.clear();
      _itemQuantityControllers.clear();
      _selectedUnits.clear();
      _newItems.clear();

      if (uncheckedItems.isNotEmpty) {
        // Add cached items
        for (final item in uncheckedItems) {
          _addNewRow(
            itemName: item.name,
            quantity: item.quantity,
            isChecked: false, // Always unchecked when restored
          );
        }
      }

      // Always add an empty row if there are no items or the last item has content
      if (_newItems.isEmpty ||
          (_newItems.isNotEmpty && _itemNameControllers.last.text.isNotEmpty)) {
        _addNewRow();
      }
    });

    // Log the loaded items for debugging
    debugPrint('Loaded ${uncheckedItems.length} cached items for user ${user.id}');
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Force refresh of shopping lists when dependencies change
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref.invalidate(list_provider.userShoppingListsProvider);
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Save items to cache when app is paused or inactive
    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      _saveCurrentItemsToCache();
    }
  }

  @override
  void dispose() {
    // Unregister as an observer for app lifecycle changes
    WidgetsBinding.instance.removeObserver(this);

    // Save current items to cache before disposing
    _saveCurrentItemsToCache();

    // Dispose all controllers
    _nameController.dispose();
    _descriptionController.dispose();
    _searchController.dispose();

    for (final controller in _itemNameControllers) {
      controller.dispose();
    }
    for (final controller in _itemQuantityControllers) {
      controller.dispose();
    }

    super.dispose();
  }

  // Save current items to cache
  Future<void> _saveCurrentItemsToCache() async {
    final user = ref.read(currentUserProvider).value;
    if (user == null) return;

    try {
      // Update all items with current values from controllers
      for (int i = 0; i < _newItems.length; i++) {
        if (i < _itemNameControllers.length && i < _itemQuantityControllers.length) {
          _newItems[i] = _newItems[i].copyWith(
            name: _itemNameControllers[i].text.trim(),
            quantity: int.tryParse(_itemQuantityControllers[i].text) ?? 1,
          );
        }
      }

      // Filter out empty items
      final nonEmptyItems = _newItems.where((item) => item.name.isNotEmpty).toList();

      // Get the cache service
      final cacheService = ShoppingListCacheService();
      await cacheService.initialize();

      // Save non-empty items to cache
      await cacheService.cacheUncheckedItems(user.id, nonEmptyItems);

      debugPrint('Saved ${nonEmptyItems.length} items to cache for user ${user.id}');
    } catch (e) {
      debugPrint('Error saving items to cache: $e');
    }
  }

  // Add a new empty row for item input
  void _addNewRow(
      {String itemName = '', int quantity = 1, bool isChecked = false}) {
    final nameController = TextEditingController(text: itemName);
    final quantityController = TextEditingController(text: quantity.toString());
    final itemId = const Uuid().v4();

    // Simplified unit selection - use pcs for fruits, kg for most other items
    String defaultUnit = 'kg';

    if (itemName.isNotEmpty) {
      final name = itemName.toLowerCase();
      // Check if it's a fruit
      if (_fruitItems.any((fruit) => name.contains(fruit))) {
        defaultUnit = 'pcs'; // Use pieces for fruits by default
      } else if (_liquidItems.any((liquid) => name.contains(liquid))) {
        defaultUnit = 'ltr'; // Use liters for liquids
      } else if (name.contains('egg') ||
                name.contains('bread') ||
                name.contains('cookie') ||
                name.contains('biscuit')) {
        defaultUnit = 'pcs'; // Use pieces for countable items
      }
    }

    setState(() {
      _newItems.add(shared_model.ShoppingListItem(
        id: itemId,
        name: itemName,
        price: 0,
        quantity: quantity,
        isChecked: isChecked,
      ));
      _itemNameControllers.add(nameController);
      _itemQuantityControllers.add(quantityController);
      _selectedUnits.add(defaultUnit);
    });
  }

  // Create a new shopping list from the input fields
  Future<void> _createList() async {
    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final user = ref.read(currentUserProvider).value;
    if (user == null) {
      _showSnackBar('Please sign in to create a shopping list', isError: true);
      return;
    }

    // Store current items for potential restoration
    final currentItems = List<shared_model.ShoppingListItem>.from(_newItems);

    // Update all items with current values
    for (int i = 0; i < _newItems.length; i++) {
      if (i < _itemNameControllers.length &&
          i < _itemQuantityControllers.length) {
        _newItems[i] = _newItems[i].copyWith(
          name: _itemNameControllers[i].text.trim(),
          quantity: int.tryParse(_itemQuantityControllers[i].text) ?? 1,
          // Note: unit is stored in _selectedUnits[i] but not part of the model
        );
      }
    }

    // Get checked items only
    final checkedItems = _newItems.where((item) => item.isChecked).toList();

    // Check if any items are checked
    if (checkedItems.isEmpty) {
      _showSnackBar('Please select items to proceed', isError: true);

      // Also show a dialog for better visibility
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('No Items Selected'),
            content:
                const Text('Please check the items you want to proceed with.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
      return;
    }

    // Check if any checked items are empty
    final emptyCheckedItems =
        checkedItems.where((item) => item.name.isEmpty).toList();
    if (emptyCheckedItems.isNotEmpty) {
      _showSnackBar('Some checked items are empty', isError: true);

      // Show a dialog for better visibility
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Empty Items Selected'),
            content: const Text(
                'You have checked items without names. Please enter names for all checked items or uncheck the empty ones.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
      return;
    }

    // Skip unit validation - allow any unit for any item
    // This removes the "inappropriate units" error that was causing confusion

    // All checked items have names at this point
    final validCheckedItems = checkedItems;

    try {
      final now = DateTime.now();
      // Use a default name if none provided
      final listName = _nameController.text.trim().isNotEmpty
          ? _nameController.text.trim()
          : 'Shopping List ${now.day}/${now.month}/${now.year}';

      final list = shared_model.ShoppingListModel(
        id: const Uuid().v4(),
        name: listName,
        description: _descriptionController.text.trim(),
        itemCount: validCheckedItems.length,
        totalPrice: 0, // Price will be set by seller
        isShared: false,
        createdAt: now,
        updatedAt: now,
        createdBy: user.id,
        items: validCheckedItems, // Only include checked items
        status: 'pending', // Set initial status to pending
      );

      await ref
          .read(list_provider.shoppingListServiceProvider)
          .createShoppingList(list);

      // Show address selection bottom sheet first
      if (!mounted) return;

      // Show the address selection bottom sheet
      final selectedAddress = await AddressSelectionBottomSheet.show(context, list.id);

      // If an address was selected, proceed to seller selection
      if (selectedAddress != null && mounted) {
        // Show the seller selection bottom drawer with the selected address
        final result = await SellerSelectionBottomDrawer.showWithAddress(
          context,
          list.id,
          selectedAddress,
        );

        if (result != null && mounted) {
          // If the order was successfully created, clear the form
          setState(() {
            // Clear controllers for checked items
            for (int i = _newItems.length - 1; i >= 0; i--) {
              if (_newItems[i].isChecked) {
                // Remove this item
                _newItems.removeAt(i);
                _itemNameControllers.removeAt(i).dispose();
                _itemQuantityControllers.removeAt(i).dispose();
                _selectedUnits.removeAt(i);
              }
            }

            // If all items were checked, add a new empty row
            if (_newItems.isEmpty) {
              _addNewRow();
            }
          });

          try {
            // Get the cache service
            final cacheService = ShoppingListCacheService();
            await cacheService.initialize();

            if (_newItems.isNotEmpty) {
              // Save unchecked items to cache
              await cacheService.cacheUncheckedItems(user.id, _newItems);
            } else {
              // Clear the cache if all items were checked
              await cacheService.clearUncheckedItems(user.id);
            }
          } catch (e) {
            debugPrint('Error managing unchecked items cache: $e');
          }

          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Request sent to ${result.name}'),
                backgroundColor: Colors.green,
              ),
            );
          }

          // Navigate to orders screen directly
          if (mounted) {
            try {
              // Use go instead of pushReplacement to ensure we're at a known state in the navigation stack
              context.go(BuyerRoutes.orders);
            } catch (e) {
              debugPrint('Error navigating to orders screen with go: $e');

              // Fallback to pushReplacement if go fails
              try {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const OrdersScreen(),
                  ),
                );
              } catch (e2) {
                debugPrint('Error with fallback navigation using pushReplacement: $e2');

                // Last resort: try push if pushReplacement fails
                try {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const OrdersScreen(),
                    ),
                  );
                } catch (e3) {
                  debugPrint('All navigation attempts failed: $e3');
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Navigation error. Please go to Orders screen manually.'),
                      backgroundColor: Colors.orange,
                      duration: Duration(seconds: 5),
                    ),
                  );
                }
              }
            }
          }
        }
      } else {
        // If user came back without selecting a seller, restore the items
        setState(() {
          // Clear existing items
          for (final controller in _itemNameControllers) {
            controller.dispose();
          }
          for (final controller in _itemQuantityControllers) {
            controller.dispose();
          }

          _itemNameControllers.clear();
          _itemQuantityControllers.clear();
          _selectedUnits.clear();
          _newItems.clear();

          // Restore previous items
          for (int i = 0; i < currentItems.length; i++) {
            _addNewRow(
              itemName: currentItems[i].name,
              quantity: currentItems[i].quantity,
              isChecked: currentItems[i].isChecked,
            );
          }

          // Add an empty row if needed
          if (currentItems.isEmpty) {
            _addNewRow();
          }
        });

        // Delete the created list since user didn't select a seller
        try {
          await ref
              .read(list_provider.shoppingListServiceProvider)
              .deleteShoppingList(list.id);
        } catch (e) {
          debugPrint('Error deleting list: $e');
        }

        _showSnackBar('List creation cancelled. Items restored.',
            isError: false);
      }

      // Refresh the shopping lists
      ref.invalidate(list_provider.userShoppingListsProvider);
    } catch (e) {
      if (mounted) {
        _showSnackBar('Failed to create shopping list: $e', isError: true);
      }
    }
  }

  // Build the form for creating a new shopping list
  Widget _buildCreateListForm() {
    if (_isSearching) {
      return _buildSearchResults();
    }

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with column titles
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                const SizedBox(width: 24), // Serial number width
                const SizedBox(width: 8),
                const SizedBox(width: 24), // Checkbox width
                const SizedBox(width: 8),
                const SizedBox(width: 24), // Image width
                const SizedBox(width: 8),
                const Expanded(
                    child: Text('Item Name',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                const SizedBox(width: 8),
                const Text('Unit',
                    style:
                        TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                const SizedBox(width: 8),
                const Text('Qty',
                    style:
                        TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                const SizedBox(width: 16),
                const SizedBox(width: 24), // Action button width
              ],
            ),
          ),
          const Divider(),
          // Main content with items and search button
          Expanded(
            child: Column(
              children: [
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _newItems.length,
                    itemBuilder: (context, index) {
                      return _buildItemRow(index);
                    },
                  ),
                ),
                // Search button will be added inline with the list items
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build a row for a shopping list item
  Widget _buildItemRow(int index) {
    final theme = Theme.of(context);
    final isLastRow = index == _newItems.length - 1;

    // Helper function to show image dialog
    void showImageDialog() {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          contentPadding: EdgeInsets.zero,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                'assets/images/placeholder.png',
                fit: BoxFit.cover,
                height: 200,
                width: double.infinity,
                errorBuilder: (context, error, stackTrace) => const Icon(
                  Icons.image_not_supported,
                  size: 200,
                  color: Colors.grey,
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  _itemNameControllers[index].text.isEmpty
                      ? 'Item Image'
                      : _itemNameControllers[index].text,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }

    // Helper function to handle item deletion
    void handleDelete() {
      // If this is the last row, clear it instead of removing
      if (_newItems.length == 1) {
        setState(() {
          _itemNameControllers[0].clear();
          _itemQuantityControllers[0].text = '1';
          _newItems[0] = _newItems[0].copyWith(
            name: '',
            quantity: 1,
            isChecked: false,
          );
        });
      } else {
        setState(() {
          _newItems.removeAt(index);
          _itemNameControllers.removeAt(index).dispose();
          _itemQuantityControllers.removeAt(index).dispose();
          _selectedUnits.removeAt(index);
        });
      }
    }

    return Column(
      children: [
        // Regular item row
        Padding(
          padding: const EdgeInsets.symmetric(
              vertical: 2), // Reduced vertical padding
          child: Row(
            children: [
              // Serial number
              Container(
                width: 22,
                height: 22, // Reduced size to match row height
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // Checkbox
              SizedBox(
                width: 22,
                height: 22, // Reduced size to match row height
                child: Checkbox(
                  value: _newItems[index].isChecked,
                  onChanged: (value) {
                    setState(() {
                      _newItems[index] = _newItems[index].copyWith(
                        isChecked: value ?? false,
                      );
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),

              // Item image (clickable)
              GestureDetector(
                onTap: showImageDialog,
                child: SizedBox(
                  width: 32,
                  height: 32, // Match height of text input field
                  child: const Icon(
                    Icons.image,
                    size: 26, // Smaller icon to fit in the smaller container
                    color: Colors.grey,
                  ),
                ),
              ),
              const SizedBox(width: 1),

              // Item name
              Expanded(
                child: SizedBox(
                  height: 26, // Match height of other input fields
                  child: TextFormField(
                    controller: _itemNameControllers[index],
                    decoration: const InputDecoration(
                      hintText: 'Enter item name',
                      isDense: true,
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      border: OutlineInputBorder(),
                    ),
                    style: const TextStyle(fontSize: 13),
                    validator: (value) {
                      // Individual items can be empty, we'll filter them out later
                      return null;
                    },
                    onChanged: (value) {
                      // Update the item
                      _newItems[index] = _newItems[index].copyWith(name: value);

                      // Auto-select appropriate unit based on item name
                      if (value.isNotEmpty) {
                        // Simplified unit selection logic
                        String defaultUnit = 'kg';
                        final name = value.toLowerCase();

                        // Check if it's a fruit
                        if (_fruitItems.any((fruit) => name.contains(fruit))) {
                          defaultUnit = 'pcs'; // Use pieces for fruits by default
                        } else if (_liquidItems.any((liquid) => name.contains(liquid))) {
                          defaultUnit = 'ltr'; // Use liters for liquids
                        } else if (name.contains('egg') ||
                                  name.contains('bread') ||
                                  name.contains('cookie') ||
                                  name.contains('biscuit')) {
                          defaultUnit = 'pcs'; // Use pieces for countable items
                        }

                        setState(() {
                          _selectedUnits[index] = defaultUnit;
                        });
                      }

                      // If this is the last row and user has entered something, add a new empty row
                      if (isLastRow && value.isNotEmpty) {
                        _addNewRow();
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // Unit dropdown
              SizedBox(
                width: 60,
                height: 26, // Match height of other input fields
                child: DropdownButtonFormField<String>(
                  value: _selectedUnits[index],
                  decoration: InputDecoration(
                    isDense: true,
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                    border: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 1.0,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: Color.alphaBlend(
                          Theme.of(context)
                              .colorScheme
                              .primary
                              .withAlpha(128), // 0.5 * 255 = 128
                          Colors.transparent,
                        ),
                        width: 1.0,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 1.5,
                      ),
                    ),
                  ),
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                  icon: const Icon(Icons.arrow_drop_down, size: 16),
                  dropdownColor: Theme.of(context).scaffoldBackgroundColor,
                  menuMaxHeight: 200, // Ensure menu is visible
                  isExpanded: true, // Make dropdown items use full width
                  itemHeight: 48.0,
                  items: _unitOptions.map((unit) {
                    return DropdownMenuItem<String>(
                      value: unit,
                      child: Text(
                        unit,
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedUnits[index] = value;
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),

              // Quantity
              SizedBox(
                width: 45,
                height: 26, // Match height of other input fields
                child: TextFormField(
                  controller: _itemQuantityControllers[index],
                  decoration: const InputDecoration(
                    hintText: 'Qty',
                    isDense: true,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                    border: OutlineInputBorder(),
                  ),
                  style: const TextStyle(fontSize: 12),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      if (int.tryParse(value) == null) {
                        return 'Invalid';
                      }
                    }
                    return null;
                  },
                  onChanged: (value) {
                    _newItems[index] = _newItems[index].copyWith(
                      quantity: int.tryParse(value) ?? 1,
                    );
                  },
                ),
              ),
              const SizedBox(width: 8),

              // Delete button for all rows
              IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: handleDelete,
              ),
            ],
          ),
        ),

        // Add search and proceed buttons after the last row
        if (isLastRow)
          Padding(
            padding: const EdgeInsets.only(left: 180, top: 2, bottom: 4),
            child: Row(
              children: [
                // Search button
                IconButton.filled(
                  onPressed: () {
                    setState(() {
                      _isSearching = true;
                    });
                  },
                  icon: const Icon(Icons.search, size: 10),
                  tooltip: 'Search Items',
                  style: IconButton.styleFrom(
                    backgroundColor: Color.alphaBlend(
                        theme.colorScheme.secondary
                            .withAlpha(51), // 0.2 * 255 = 51
                        Colors.transparent),
                    foregroundColor: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 3),
                Text(
                  'Search',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(), // Push PROCEED button to the right
                // PROCEED button with sound - using SoundGestureDetector with Container
                SoundGestureDetector(
                  onTap: _createList,
                  soundType: SoundType.success,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.check, size: 10, color: Colors.white),
                        SizedBox(width: 5),
                        Text(
                          'PROCEED',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 1), // Add some padding on the right
              ],
            ),
          ),
      ],
    );
  }

  // Build the search results screen
  Widget _buildSearchResults() {
    // Mock search results - in a real app, this would come from a database
    final allItems = [
      'Rice',
      'Wheat Flour',
      'Sugar',
      'Salt',
      'Cooking Oil',
      'Milk',
      'Eggs',
      'Bread',
      'Butter',
      'Cheese',
      'Tomatoes',
      'Potatoes',
      'Onions',
      'Garlic',
      'Ginger',
      'Chicken',
      'Fish',
      'Mutton',
      'Yogurt',
      'Curd',
    ];

    // Filter based on search text
    final searchText = _searchController.text.toLowerCase();
    final searchResults = searchText.isEmpty
        ? allItems
        : allItems
            .where((item) => item.toLowerCase().contains(searchText))
            .toList();

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  setState(() {
                    _isSearching = false;
                    _searchController.clear();
                  });
                },
              ),
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    labelText: 'Search Items',
                    hintText: 'Type to search for items',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  autofocus: true,
                  onChanged: (value) {
                    // Refresh the search results when text changes
                    setState(() {});
                  },
                ),
              ),
            ],
          ),
        ),
        if (searchResults.isEmpty)
          const Expanded(
            child: Center(
              child: Text(
                'No items found. Try a different search term.',
                style: TextStyle(fontStyle: FontStyle.italic),
              ),
            ),
          )
        else
          Expanded(
            child: Column(
              children: [
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    itemCount: searchResults.length,
                    itemBuilder: (context, index) {
                      final item = searchResults[index];
                      return ListTile(
                        leading: const Icon(Icons.shopping_basket),
                        title: Text(item),
                        trailing: IconButton(
                          icon:
                              const Icon(Icons.add_circle, color: Colors.green),
                          tooltip: 'Add to list',
                          onPressed: () => _addSearchedItem(item),
                        ),
                        onTap: () => _addSearchedItem(item),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // Add a searched item to the list
  void _addSearchedItem(String itemName) {
    setState(() {
      _isSearching = false;
      _searchController.clear();

      // Find the first empty row or add a new one
      int emptyRowIndex = _itemNameControllers
          .indexWhere((controller) => controller.text.isEmpty);

      if (emptyRowIndex >= 0) {
        // Use the existing empty row
        _itemNameControllers[emptyRowIndex].text = itemName;
        _newItems[emptyRowIndex] =
            _newItems[emptyRowIndex].copyWith(name: itemName);

        // Set the appropriate unit based on item name
        // Simplified unit selection logic
        String defaultUnit = 'kg';
        final name = itemName.toLowerCase();

        // Check if it's a fruit
        if (_fruitItems.any((fruit) => name.contains(fruit))) {
          defaultUnit = 'pcs'; // Use pieces for fruits by default
        } else if (_liquidItems.any((liquid) => name.contains(liquid))) {
          defaultUnit = 'ltr'; // Use liters for liquids
        } else if (name.contains('egg') ||
                  name.contains('bread') ||
                  name.contains('cookie') ||
                  name.contains('biscuit')) {
          defaultUnit = 'pcs'; // Use pieces for countable items
        }

        _selectedUnits[emptyRowIndex] = defaultUnit;

        // Check if this was the last row, if so add a new empty row
        if (emptyRowIndex == _newItems.length - 1) {
          _addNewRow();
        }
      } else {
        // Add a new row with the item and appropriate unit
        _addNewRow(itemName: itemName);

        // Add another empty row for the next item
        _addNewRow();
      }
    });
  }

  // Helper method to show a standardized snackbar
  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    final color = isError ? Colors.red : Colors.green;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
      ),
    );
  }

  /// Merge products from calendar into the current form (never create a new list)
  void addCalendarProductsToForm(List<shared_model.ShoppingListItem> items) {
    setState(() {
      for (final item in items) {
        // Optional: skip duplicates by name
        if (!_newItems.any((e) =>
            e.name.trim().toLowerCase() == item.name.trim().toLowerCase())) {
          _addNewRow(
            itemName: item.name,
            quantity: item.quantity,
            isChecked: false,
          );
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider).value;
    if (user == null) {
      return const Center(
        child: Text('Please sign in to view your shopping lists'),
      );
    }
    return BottomNavBackHandler(
      fallbackRoute: BuyerRoutes.home,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Shopping Lists'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go(BuyerRoutes.home),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute<void>(
                    builder: (context) => const ShoppingListSettingsScreen(),
                  ),
                );
              },
            ),
          ],
        ),
        body: _buildCreateListForm(),
        floatingActionButton: null,
      ),
    );
  }
}

