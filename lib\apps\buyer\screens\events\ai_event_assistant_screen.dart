import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uuid/uuid.dart';
import '../../widgets/navigation/buyer_app_bar.dart';
import '../../../../shared/services/pricing/real_time_pricing_service.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';
import '../../providers/shopping/shopping_list_provider.dart';
import '../../../../shared/providers/auth_provider.dart';

class AIEventAssistantScreen extends ConsumerStatefulWidget {
  const AIEventAssistantScreen({super.key});

  @override
  ConsumerState<AIEventAssistantScreen> createState() =>
      _AIEventAssistantScreenState();
}

class _AIEventAssistantScreenState
    extends ConsumerState<AIEventAssistantScreen> {
  final _formKey = GlobalKey<FormState>();
  final _eventNameController = TextEditingController();
  final _guestCountController = TextEditingController();
  final _durationController = TextEditingController();
  final _specialRequestsController = TextEditingController();
  final _budgetController = TextEditingController();

  String _selectedEventType = 'Wedding';
  String _selectedMealType = 'Vegetarian';
  String _selectedCuisine = 'South Indian';
  String _selectedVenueType = 'Function Hall';
  String _selectedLocation = 'Bangalore';
  bool _isLoading = false;
  bool _includeDecoration = true;
  bool _includePhotography = true;
  bool _includeEntertainment = false;

  final List<String> _eventTypes = [
    'Wedding',
    'Birthday Party',
    'Anniversary',
    'Corporate Event',
    'Religious Ceremony',
    'Baby Shower',
    'Engagement',
    'House Warming',
    'Festival Celebration',
    'Other',
  ];

  final List<String> _mealTypes = [
    'Vegetarian',
    'Non-Vegetarian',
    'Mixed (Veg + Non-Veg)',
    'Vegan',
    'Jain Food',
  ];

  final List<String> _cuisines = [
    'South Indian',
    'North Indian',
    'Chinese',
    'Continental',
    'Italian',
    'Mexican',
    'Thai',
    'Mixed Cuisine',
  ];

  // Comprehensive food items database
  final Map<String, List<Map<String, dynamic>>> _foodItemsDatabase = {
    'Rice & Grains': [
      {'name': 'Basmati Rice', 'cost': 80, 'category': 'staple'},
      {'name': 'Jeera Rice', 'cost': 90, 'category': 'staple'},
      {'name': 'Biryani (Veg)', 'cost': 180, 'category': 'main'},
      {'name': 'Biryani (Chicken)', 'cost': 280, 'category': 'main'},
      {'name': 'Biryani (Mutton)', 'cost': 350, 'category': 'main'},
      {'name': 'Pulao', 'cost': 120, 'category': 'main'},
      {'name': 'Fried Rice', 'cost': 150, 'category': 'main'},
    ],
    'Curries & Gravies': [
      {'name': 'Chicken Curry', 'cost': 200, 'category': 'main'},
      {'name': 'Mutton Curry', 'cost': 280, 'category': 'main'},
      {'name': 'Fish Curry', 'cost': 220, 'category': 'main'},
      {'name': 'Paneer Butter Masala', 'cost': 180, 'category': 'main'},
      {'name': 'Dal Makhani', 'cost': 120, 'category': 'main'},
      {'name': 'Sambar', 'cost': 80, 'category': 'main'},
      {'name': 'Rasam', 'cost': 60, 'category': 'main'},
      {'name': 'Rajma', 'cost': 100, 'category': 'main'},
      {'name': 'Chole', 'cost': 110, 'category': 'main'},
    ],
    'Breads': [
      {'name': 'Roti/Chapati', 'cost': 15, 'category': 'staple'},
      {'name': 'Naan', 'cost': 25, 'category': 'staple'},
      {'name': 'Paratha', 'cost': 30, 'category': 'staple'},
      {'name': 'Puri', 'cost': 20, 'category': 'staple'},
      {'name': 'Kulcha', 'cost': 35, 'category': 'staple'},
    ],
    'Appetizers': [
      {'name': 'Chicken 65', 'cost': 180, 'category': 'starter'},
      {'name': 'Mutton Fry', 'cost': 220, 'category': 'starter'},
      {'name': 'Fish Fry', 'cost': 200, 'category': 'starter'},
      {'name': 'Paneer Tikka', 'cost': 150, 'category': 'starter'},
      {'name': 'Veg Manchurian', 'cost': 120, 'category': 'starter'},
      {'name': 'Gobi 65', 'cost': 100, 'category': 'starter'},
    ],
    'Desserts': [
      {'name': 'Gulab Jamun', 'cost': 40, 'category': 'dessert'},
      {'name': 'Rasgulla', 'cost': 35, 'category': 'dessert'},
      {'name': 'Kheer', 'cost': 50, 'category': 'dessert'},
      {'name': 'Ice Cream', 'cost': 60, 'category': 'dessert'},
      {'name': 'Payasam', 'cost': 45, 'category': 'dessert'},
    ],
    'Beverages': [
      {'name': 'Soft Drinks', 'cost': 30, 'category': 'beverage'},
      {'name': 'Fresh Juice', 'cost': 50, 'category': 'beverage'},
      {'name': 'Lassi', 'cost': 40, 'category': 'beverage'},
      {'name': 'Tea/Coffee', 'cost': 20, 'category': 'beverage'},
      {'name': 'Buttermilk', 'cost': 25, 'category': 'beverage'},
    ],
    'Snacks': [
      {'name': 'Samosa', 'cost': 15, 'category': 'snack'},
      {'name': 'Pakora', 'cost': 20, 'category': 'snack'},
      {'name': 'Vada', 'cost': 18, 'category': 'snack'},
      {'name': 'Dosa', 'cost': 80, 'category': 'snack'},
      {'name': 'Idli', 'cost': 60, 'category': 'snack'},
    ],
  };

  List<Map<String, dynamic>> _selectedMenuItems = [];

  @override
  void initState() {
    super.initState();
    // Add some default menu items for demo
    _selectedMenuItems = [
      {
        'name': 'Biryani (Chicken)',
        'cost': 280,
        'category': 'main',
        'quantity': 1,
      },
      {'name': 'Chicken Curry', 'cost': 200, 'category': 'main', 'quantity': 1},
      {'name': 'Naan', 'cost': 25, 'category': 'staple', 'quantity': 2},
    ];
  }

  final List<String> _venueTypes = [
    'Function Hall',
    'Banquet Hall',
    'Outdoor Venue',
    'Resort',
    'Community Center',
    'Marriage Garden',
    'Convention Center',
    'Home/Residence',
  ];

  final List<String> _locations = [
    'Bangalore',
    'Mumbai',
    'Delhi',
    'Chennai',
    'Hyderabad',
    'Pune',
    'Kolkata',
    'Ahmedabad',
    'Jaipur',
    'Lucknow',
    'Other',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const BuyerAppBar(title: Text('AI Event Assistant')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card
              _buildHeaderCard(),

              const SizedBox(height: 24),

              // Event Details Section
              _buildSectionCard('Event Details', Icons.event, Colors.blue, [
                _buildEventNameField(),
                const SizedBox(height: 16),
                _buildEventTypeDropdown(),
                const SizedBox(height: 16),
                _buildGuestCountField(),
                const SizedBox(height: 16),
                _buildDurationField(),
              ]),

              const SizedBox(height: 20),

              // Food Preferences Section
              _buildSectionCard(
                'Food Preferences',
                Icons.restaurant,
                Colors.orange,
                [
                  _buildMealTypeDropdown(),
                  const SizedBox(height: 16),
                  _buildCuisineDropdown(),
                  const SizedBox(height: 16),
                  _buildDynamicMenuBuilder(),
                ],
              ),

              const SizedBox(height: 20),

              // Budget & Venue Section
              _buildSectionCard(
                'Budget & Venue',
                Icons.location_on,
                Colors.purple,
                [
                  _buildBudgetField(),
                  const SizedBox(height: 16),
                  _buildVenueTypeDropdown(),
                  const SizedBox(height: 16),
                  _buildLocationDropdown(),
                ],
              ),

              const SizedBox(height: 20),

              // Additional Services Section
              _buildSectionCard(
                'Additional Services',
                Icons.miscellaneous_services,
                Colors.teal,
                [_buildServiceCheckboxes()],
              ),

              const SizedBox(height: 20),

              // Special Requests Section
              _buildSectionCard(
                'Special Requests',
                Icons.note_add,
                Colors.green,
                [_buildSpecialRequestsField()],
              ),

              const SizedBox(height: 32),

              // Generate Button
              _buildGenerateButton(),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.deepPurple.withValues(alpha: 0.8), Colors.indigo],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.psychology,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'AI Event Assistant',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            'Tell us about your event and we\'ll calculate the exact raw materials needed for food preparation plus provide detailed cost estimates. Our AI will generate a comprehensive shopping list with quantities and budget breakdown!',
            style: TextStyle(fontSize: 14, color: Colors.white70, height: 1.4),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(
    String title,
    IconData icon,
    Color color,
    List<Widget> children,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildEventNameField() {
    return TextFormField(
      controller: _eventNameController,
      decoration: const InputDecoration(
        labelText: 'Event Name',
        hintText: 'e.g., Priya & Raj Wedding',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.celebration),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter event name';
        }
        return null;
      },
    );
  }

  Widget _buildEventTypeDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedEventType,
      decoration: const InputDecoration(
        labelText: 'Event Type',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.event_note),
      ),
      items: _eventTypes.map((type) {
        return DropdownMenuItem(value: type, child: Text(type));
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedEventType = value!;
        });
      },
    );
  }

  Widget _buildGuestCountField() {
    return TextFormField(
      controller: _guestCountController,
      decoration: const InputDecoration(
        labelText: 'Number of Guests',
        hintText: 'e.g., 150',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.people),
      ),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter number of guests';
        }
        if (int.tryParse(value) == null || int.parse(value) <= 0) {
          return 'Please enter a valid number';
        }
        return null;
      },
    );
  }

  Widget _buildDurationField() {
    return TextFormField(
      controller: _durationController,
      decoration: const InputDecoration(
        labelText: 'Event Duration (hours)',
        hintText: 'e.g., 6',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.schedule),
      ),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter event duration';
        }
        if (int.tryParse(value) == null || int.parse(value) <= 0) {
          return 'Please enter a valid duration';
        }
        return null;
      },
    );
  }

  Widget _buildMealTypeDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedMealType,
      decoration: const InputDecoration(
        labelText: 'Meal Type',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.restaurant_menu),
      ),
      items: _mealTypes.map((type) {
        return DropdownMenuItem(value: type, child: Text(type));
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedMealType = value!;
        });
      },
    );
  }

  Widget _buildCuisineDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedCuisine,
      decoration: const InputDecoration(
        labelText: 'Cuisine Type',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.local_dining),
      ),
      items: _cuisines.map((cuisine) {
        return DropdownMenuItem(value: cuisine, child: Text(cuisine));
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedCuisine = value!;
        });
      },
    );
  }

  Widget _buildSpecialRequestsField() {
    return TextFormField(
      controller: _specialRequestsController,
      decoration: const InputDecoration(
        labelText: 'Special Requests (Optional)',
        hintText: 'e.g., No onions, extra spicy, traditional recipes...',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.note_add),
      ),
      maxLines: 3,
    );
  }

  Widget _buildBudgetField() {
    return TextFormField(
      controller: _budgetController,
      decoration: const InputDecoration(
        labelText: 'Your Budget',
        hintText: 'e.g., 50000',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.currency_rupee),
        suffixText: '₹',
        helperText: 'We\'ll suggest optimizations if cost exceeds budget',
      ),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your budget for smart suggestions';
        }
        if (int.tryParse(value) == null || int.parse(value) <= 0) {
          return 'Please enter a valid budget amount';
        }
        return null;
      },
    );
  }

  Widget _buildDynamicMenuBuilder() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with Add Button
        Row(
          children: [
            const Expanded(
              child: Text(
                'Menu Items',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
            ElevatedButton.icon(
              onPressed: _showAddMenuItemDialog,
              icon: const Icon(Icons.add, size: 18),
              label: const Text('Add Item'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Selected Menu Items List
        if (_selectedMenuItems.isEmpty)
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.restaurant_menu,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 8),
                Text(
                  'No menu items added yet',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Tap "Add Item" to build your custom menu',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade500),
                ),
              ],
            ),
          )
        else
          Column(
            children: _selectedMenuItems.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return _buildMenuItemCard(item, index);
            }).toList(),
          ),

        // Total Cost Summary
        if (_selectedMenuItems.isNotEmpty) ...[
          const SizedBox(height: 12),
          _buildMenuCostSummary(),
        ],
      ],
    );
  }

  Widget _buildVenueTypeDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedVenueType,
      decoration: const InputDecoration(
        labelText: 'Venue Type',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.business),
      ),
      items: _venueTypes.map((type) {
        return DropdownMenuItem(value: type, child: Text(type));
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedVenueType = value!;
        });
      },
    );
  }

  Widget _buildLocationDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedLocation,
      decoration: const InputDecoration(
        labelText: 'Location/City',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.location_city),
      ),
      items: _locations.map((location) {
        return DropdownMenuItem(value: location, child: Text(location));
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedLocation = value!;
        });
      },
    );
  }

  Widget _buildServiceCheckboxes() {
    return Column(
      children: [
        CheckboxListTile(
          title: const Text('Decoration & Themes'),
          subtitle: const Text(
            'Floral arrangements, lighting, stage decoration',
          ),
          value: _includeDecoration,
          onChanged: (value) {
            setState(() {
              _includeDecoration = value!;
            });
          },
          secondary: const Icon(Icons.auto_awesome, color: Colors.pink),
        ),
        CheckboxListTile(
          title: const Text('Photography & Videography'),
          subtitle: const Text('Professional photographers and videographers'),
          value: _includePhotography,
          onChanged: (value) {
            setState(() {
              _includePhotography = value!;
            });
          },
          secondary: const Icon(Icons.camera_alt, color: Colors.green),
        ),
        CheckboxListTile(
          title: const Text('Entertainment'),
          subtitle: const Text('DJs, live bands, dancers, anchors'),
          value: _includeEntertainment,
          onChanged: (value) {
            setState(() {
              _includeEntertainment = value!;
            });
          },
          secondary: const Icon(Icons.music_note, color: Colors.red),
        ),
      ],
    );
  }

  Widget _buildGenerateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _generateShoppingList,
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.psychology),
        label: Text(
          _isLoading ? 'Generating...' : 'Generate AI List & Cost Estimate',
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.deepPurple,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 4,
        ),
      ),
    );
  }

  void _generateShoppingList() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    // Simulate AI processing
    await Future.delayed(const Duration(seconds: 3));

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      _showGeneratedShoppingList();
    }
  }

  void _showGeneratedShoppingList() async {
    final guestCount = int.parse(_guestCountController.text);
    final shoppingList = _generateMockShoppingList(guestCount);
    final costEstimate = await _generateCostEstimate(guestCount);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        maxChildSize: 0.95,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Header
              Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green, size: 28),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'AI Analysis Complete!',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Cost Summary Card
              _buildCostSummaryCard(costEstimate),

              const SizedBox(height: 16),

              // Tab Bar for Shopping List, Cost Breakdown, and Budget Optimization
              DefaultTabController(
                length: costEstimate['isOverBudget'] ? 3 : 2,
                child: Expanded(
                  child: Column(
                    children: [
                      TabBar(
                        tabs: [
                          const Tab(
                            icon: Icon(Icons.shopping_cart),
                            text: 'Shopping List',
                          ),
                          const Tab(
                            icon: Icon(Icons.analytics),
                            text: 'Cost Breakdown',
                          ),
                          if (costEstimate['isOverBudget'])
                            const Tab(
                              icon: Icon(Icons.lightbulb_outline),
                              text: 'Budget Tips',
                            ),
                        ],
                      ),
                      Expanded(
                        child: TabBarView(
                          children: [
                            // Shopping List Tab
                            Column(
                              children: [
                                // Header with info
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  margin: const EdgeInsets.only(bottom: 8),
                                  decoration: BoxDecoration(
                                    color: Colors.green.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.green.withValues(
                                        alpha: 0.3,
                                      ),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.lightbulb_outline,
                                        color: Colors.green,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          'AI-calculated quantities for ${shoppingList.length} items',
                                          style: const TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.green,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // Shopping list items
                                Expanded(
                                  child: ListView.builder(
                                    controller: scrollController,
                                    itemCount: shoppingList.length,
                                    itemBuilder: (context, index) {
                                      final item = shoppingList[index];
                                      return Card(
                                        margin: const EdgeInsets.only(
                                          bottom: 8,
                                        ),
                                        child: ListTile(
                                          leading: CircleAvatar(
                                            backgroundColor: Colors.deepPurple
                                                .withValues(alpha: 0.1),
                                            child: Text(
                                              item['emoji']!,
                                              style: const TextStyle(
                                                fontSize: 20,
                                              ),
                                            ),
                                          ),
                                          title: Text(
                                            item['name']!,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          subtitle: Text(item['category']!),
                                          trailing: Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 12,
                                              vertical: 6,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.deepPurple
                                                  .withValues(alpha: 0.1),
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                            child: Text(
                                              item['quantity']!,
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Colors.deepPurple,
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                            // Cost Breakdown Tab
                            _buildCostBreakdownTab(costEstimate),
                            // Budget Optimization Tab (only if over budget)
                            if (costEstimate['isOverBudget'])
                              _buildBudgetOptimizationTab(costEstimate),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.edit),
                      label: const Text('Modify'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () =>
                          _showAddToShoppingListDialog(shoppingList),
                      icon: const Icon(Icons.add_shopping_cart),
                      label: const Text('Add All to Shopping List'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Map<String, String>> _generateMockShoppingList(int guestCount) {
    // AI-calculated quantities based on guest count
    final riceKg = (guestCount * 0.15).ceil();
    final dalKg = (guestCount * 0.08).ceil();
    final vegetablesKg = (guestCount * 0.2).ceil();
    final oilLiters = (guestCount * 0.05).ceil();
    final spicesGrams = (guestCount * 20).ceil();

    return [
      {
        'name': 'Basmati Rice',
        'quantity': '${riceKg}kg',
        'category': 'Grains & Cereals',
        'emoji': '🍚',
      },
      {
        'name': 'Toor Dal',
        'quantity': '${dalKg}kg',
        'category': 'Pulses & Lentils',
        'emoji': '🫘',
      },
      {
        'name': 'Mixed Vegetables',
        'quantity': '${vegetablesKg}kg',
        'category': 'Fresh Vegetables',
        'emoji': '🥕',
      },
      {
        'name': 'Cooking Oil',
        'quantity': '${oilLiters}L',
        'category': 'Cooking Essentials',
        'emoji': '🫗',
      },
      {
        'name': 'Garam Masala',
        'quantity': '${spicesGrams}g',
        'category': 'Spices & Seasonings',
        'emoji': '🌶️',
      },
      {
        'name': 'Onions',
        'quantity': '${(guestCount * 0.1).ceil()}kg',
        'category': 'Fresh Vegetables',
        'emoji': '🧅',
      },
      {
        'name': 'Tomatoes',
        'quantity': '${(guestCount * 0.08).ceil()}kg',
        'category': 'Fresh Vegetables',
        'emoji': '🍅',
      },
      {
        'name': 'Ginger-Garlic Paste',
        'quantity': '${(guestCount * 10).ceil()}g',
        'category': 'Cooking Essentials',
        'emoji': '🧄',
      },
      {
        'name': 'Coconut',
        'quantity': '${(guestCount * 0.02).ceil()}kg',
        'category': 'Fresh Ingredients',
        'emoji': '🥥',
      },
      {
        'name': 'Curd/Yogurt',
        'quantity': '${(guestCount * 0.05).ceil()}kg',
        'category': 'Dairy Products',
        'emoji': '🥛',
      },
    ];
  }

  void _showAddToShoppingListDialog(List<Map<String, String>> items) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.add_shopping_cart, color: Colors.green),
            SizedBox(width: 8),
            Text('Add to Shopping List'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add all ${items.length} items to your shopping list?',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            const Text(
              'This will create a new shopping list with:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Container(
              constraints: const BoxConstraints(maxHeight: 200),
              child: SingleChildScrollView(
                child: Column(
                  children: items.take(5).map((item) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.check_circle,
                            size: 16,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '${item['name']} (${item['quantity']})',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            if (items.length > 5)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  '... and ${items.length - 5} more items',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info_outline, size: 16, color: Colors.blue),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Sellers will provide prices for these items',
                      style: TextStyle(fontSize: 12, color: Colors.blue),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pop(context); // Close the shopping list modal
              _addToShoppingList(items);
            },
            icon: const Icon(Icons.add_shopping_cart),
            label: const Text('Add to List'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _addToShoppingList(List<Map<String, String>> items) async {
    try {
      final user = ref.read(currentUserProvider).value;
      if (user == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please log in to add items to shopping list'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Convert generated items to ShoppingListItem format
      final shoppingListItems = items.map((item) {
        return ShoppingListItem(
          id: const Uuid().v4(),
          name: item['name']!,
          price: 0.0, // Price will be set by seller
          quantity: int.parse(
            item['quantity']!.replaceAll(RegExp(r'[^\d]'), ''),
          ),
          isChecked: false,
          notes: 'Generated by AI Event Assistant for ${_selectedEventType}',
        );
      }).toList();

      // Calculate total estimated price (for display purposes)
      final totalPrice = shoppingListItems.fold<double>(
        0.0,
        (sum, item) => sum + (item.price * item.quantity),
      );

      // Create new shopping list
      final newShoppingList = ShoppingListModel(
        id: const Uuid().v4(),
        name: '${_selectedEventType} Shopping List',
        description:
            'AI-generated shopping list for ${_selectedEventType} event with ${_guestCountController.text} guests',
        itemCount: shoppingListItems.length,
        totalPrice: totalPrice,
        isShared: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: user.id,
        items: shoppingListItems,
        status: 'pending',
      );

      // Add to shopping list using the service
      final shoppingListService = ref.read(shoppingListServiceProvider);
      await shoppingListService.createShoppingList(newShoppingList);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${items.length} items added to shopping list!'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'View List',
              textColor: Colors.white,
              onPressed: () {
                Navigator.pop(context); // Close the modal first
                context.go('/buyer/shopping-list');
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding items to shopping list: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<Map<String, dynamic>> _generateCostEstimate(int guestCount) async {
    // Get real-time pricing data
    final venuePrices = await RealTimePricingService.getCurrentVenuePrices();
    final servicePrices =
        await RealTimePricingService.getCurrentServicePrices();

    // Calculate food cost using selected menu item
    final selectedMenu = _getSelectedMenuDetails();
    final foodCost = selectedMenu['cost'] * guestCount.toDouble();

    // Get venue cost based on current location and type
    final locationVenues =
        venuePrices[_selectedLocation] ?? venuePrices['Bangalore']!;
    final venueCost = locationVenues[_selectedVenueType] ?? 25000.0;

    // Calculate additional services with current rates
    final decorationCost = _includeDecoration
        ? (guestCount * servicePrices['decoration_per_guest']!)
        : 0.0;

    final photographyCost = _includePhotography
        ? servicePrices['photography_basic']!
        : 0.0;

    final entertainmentCost = _includeEntertainment
        ? servicePrices['entertainment_dj']!
        : 0.0;

    // Calculate totals
    final subtotal =
        (foodCost +
                venueCost +
                decorationCost +
                photographyCost +
                entertainmentCost)
            .round();
    final taxes = (subtotal * 0.18).round(); // 18% GST
    final total = subtotal + taxes;

    return {
      'foodCost': foodCost.round(),
      'venueCost': venueCost.round(),
      'decorationCost': decorationCost.round(),
      'photographyCost': photographyCost.round(),
      'entertainmentCost': entertainmentCost.round(),
      'subtotal': subtotal,
      'taxes': taxes,
      'total': total,
      'guestCount': guestCount,
      'priceUpdateTime': RealTimePricingService.getPriceUpdateTimestamp(),
      'selectedMenu': _getSelectedMenuDetails(),
      'userBudget': int.tryParse(_budgetController.text) ?? 0,
      'isOverBudget': total > (int.tryParse(_budgetController.text) ?? total),
    };
  }

  Map<String, dynamic> _getSelectedMenuDetails() {
    // Calculate total cost from selected menu items
    final totalCost = _selectedMenuItems.fold<double>(
      0.0,
      (sum, item) => sum + (item['cost'] * item['quantity']),
    );

    return {
      'name': 'Custom Menu (${_selectedMenuItems.length} items)',
      'cost':
          totalCost /
          (_selectedMenuItems.isNotEmpty ? 1 : 1), // Total cost for all items
      'type': 'custom',
      'items': _selectedMenuItems,
    };
  }

  void _showAddMenuItemDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Menu Item'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: DefaultTabController(
            length: _foodItemsDatabase.keys.length,
            child: Column(
              children: [
                TabBar(
                  isScrollable: true,
                  tabs: _foodItemsDatabase.keys.map((category) {
                    return Tab(text: category);
                  }).toList(),
                ),
                Expanded(
                  child: TabBarView(
                    children: _foodItemsDatabase.entries.map((entry) {
                      final items = entry.value;

                      return ListView.builder(
                        itemCount: items.length,
                        itemBuilder: (context, index) {
                          final item = items[index];
                          return ListTile(
                            title: Text(item['name']),
                            subtitle: Text('₹${item['cost']}/person'),
                            trailing: IconButton(
                              icon: const Icon(
                                Icons.add_circle,
                                color: Colors.green,
                              ),
                              onPressed: () {
                                _addMenuItem(item);
                                Navigator.of(context).pop();
                              },
                            ),
                          );
                        },
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _addMenuItem(Map<String, dynamic> item) {
    setState(() {
      // Check if item already exists
      final existingIndex = _selectedMenuItems.indexWhere(
        (existing) => existing['name'] == item['name'],
      );

      if (existingIndex >= 0) {
        // Increase quantity if item already exists
        _selectedMenuItems[existingIndex]['quantity']++;
      } else {
        // Add new item with quantity 1
        _selectedMenuItems.add({...item, 'quantity': 1});
      }
    });
  }

  Widget _buildMenuItemCard(Map<String, dynamic> item, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Item Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['name'],
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '₹${item['cost']}/person • ${item['category']}',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),

            // Quantity Controls
            Row(
              children: [
                IconButton(
                  onPressed: () => _decreaseQuantity(index),
                  icon: const Icon(Icons.remove_circle_outline),
                  color: Colors.red,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${item['quantity']}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                IconButton(
                  onPressed: () => _increaseQuantity(index),
                  icon: const Icon(Icons.add_circle_outline),
                  color: Colors.green,
                ),
              ],
            ),

            // Remove Button
            IconButton(
              onPressed: () => _removeMenuItem(index),
              icon: const Icon(Icons.delete_outline),
              color: Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuCostSummary() {
    final guestCount = int.tryParse(_guestCountController.text) ?? 100;
    final totalCostPerPerson = _selectedMenuItems.fold<double>(
      0.0,
      (sum, item) => sum + (item['cost'] * item['quantity']),
    );
    final totalCost = totalCostPerPerson * guestCount;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Total Menu Cost:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              Text(
                '₹${totalCostPerPerson.toStringAsFixed(0)}/person',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'For $guestCount guests:',
                style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
              ),
              Text(
                '₹${totalCost.toStringAsFixed(0)}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _increaseQuantity(int index) {
    setState(() {
      _selectedMenuItems[index]['quantity']++;
    });
  }

  void _decreaseQuantity(int index) {
    setState(() {
      if (_selectedMenuItems[index]['quantity'] > 1) {
        _selectedMenuItems[index]['quantity']--;
      }
    });
  }

  void _removeMenuItem(int index) {
    setState(() {
      _selectedMenuItems.removeAt(index);
    });
  }

  List<Map<String, dynamic>> _generateBudgetOptimizations(
    Map<String, dynamic> costEstimate,
  ) {
    final userBudget = costEstimate['userBudget'] as int;
    final currentTotal = costEstimate['total'] as int;
    final guestCount = costEstimate['guestCount'] as int;

    if (userBudget <= 0 || currentTotal <= userBudget) {
      return []; // No optimizations needed
    }

    final optimizations = <Map<String, dynamic>>[];

    // 1. Suggest removing expensive menu items
    if (_selectedMenuItems.isNotEmpty) {
      // Sort items by cost (highest first) and suggest removing expensive ones
      final expensiveItems = List<Map<String, dynamic>>.from(_selectedMenuItems)
        ..sort(
          (a, b) =>
              (b['cost'] * b['quantity']).compareTo(a['cost'] * a['quantity']),
        );

      for (int i = 0; i < expensiveItems.length && i < 3; i++) {
        final item = expensiveItems[i];
        final itemCost = item['cost'] * item['quantity'] * guestCount;
        final newTotal = currentTotal - itemCost;

        optimizations.add({
          'type': 'remove_menu_item',
          'title': 'Remove ${item['name']}',
          'description':
              'Remove ${item['name']} (${item['quantity']}x) from menu',
          'savings': itemCost,
          'newTotal': newTotal,
          'icon': Icons.remove_circle_outline,
          'color': Colors.red,
          'itemIndex': _selectedMenuItems.indexOf(item),
        });
      }

      // Suggest reducing quantities of expensive items
      for (int i = 0; i < expensiveItems.length && i < 2; i++) {
        final item = expensiveItems[i];
        if (item['quantity'] > 1) {
          final reducedQuantity = (item['quantity'] / 2).ceil();
          final savings =
              (item['quantity'] - reducedQuantity) * item['cost'] * guestCount;
          final newTotal = currentTotal - savings;

          optimizations.add({
            'type': 'reduce_quantity',
            'title': 'Reduce ${item['name']} quantity',
            'description':
                'Reduce from ${item['quantity']} to $reducedQuantity servings',
            'savings': savings,
            'newTotal': newTotal,
            'icon': Icons.remove,
            'color': Colors.orange,
            'itemIndex': _selectedMenuItems.indexOf(item),
            'newQuantity': reducedQuantity,
          });
        }
      }
    }

    // 2. Suggest reducing guest count
    final targetGuestCount = (userBudget * guestCount / currentTotal).floor();
    if (targetGuestCount < guestCount && targetGuestCount > 0) {
      final guestReduction = guestCount - targetGuestCount;
      optimizations.add({
        'type': 'guest_reduction',
        'title': 'Reduce Guest Count',
        'description':
            'Reduce from $guestCount to $targetGuestCount guests (-$guestReduction guests)',
        'savings': currentTotal - userBudget,
        'newTotal': userBudget,
        'icon': Icons.people_outline,
        'color': Colors.blue,
        'newGuestCount': targetGuestCount,
      });
    }

    // 3. Suggest removing optional services
    if (costEstimate['decorationCost'] > 0) {
      optimizations.add({
        'type': 'remove_decoration',
        'title': 'Skip Decoration',
        'description': 'Remove decoration services to save costs',
        'savings': costEstimate['decorationCost'],
        'newTotal': currentTotal - costEstimate['decorationCost'],
        'icon': Icons.auto_awesome_outlined,
        'color': Colors.pink,
      });
    }

    if (costEstimate['photographyCost'] > 0) {
      optimizations.add({
        'type': 'remove_photography',
        'title': 'Skip Photography',
        'description': 'Remove professional photography services',
        'savings': costEstimate['photographyCost'],
        'newTotal': currentTotal - costEstimate['photographyCost'],
        'icon': Icons.camera_alt_outlined,
        'color': Colors.green,
      });
    }

    if (costEstimate['entertainmentCost'] > 0) {
      optimizations.add({
        'type': 'remove_entertainment',
        'title': 'Skip Entertainment',
        'description': 'Remove entertainment services',
        'savings': costEstimate['entertainmentCost'],
        'newTotal': currentTotal - costEstimate['entertainmentCost'],
        'icon': Icons.music_note_outlined,
        'color': Colors.red,
      });
    }

    // Sort by savings amount (highest first)
    optimizations.sort(
      (a, b) => (b['savings'] as int).compareTo(a['savings'] as int),
    );

    return optimizations.take(5).toList(); // Return top 5 suggestions
  }

  Widget _buildBudgetOptimizationTab(Map<String, dynamic> costEstimate) {
    final optimizations = _generateBudgetOptimizations(costEstimate);
    final userBudget = costEstimate['userBudget'] as int;
    final currentTotal = costEstimate['total'] as int;
    final overBudgetAmount = currentTotal - userBudget;

    return ListView(
      children: [
        // Header
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(Icons.warning_amber, color: Colors.red, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Budget Exceeded',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Your event cost is ₹${overBudgetAmount.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')} over your budget of ₹${userBudget.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
                style: TextStyle(fontSize: 14, color: Colors.red.shade600),
              ),
              const SizedBox(height: 8),
              Text(
                'Here are some smart suggestions to fit your budget:',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
            ],
          ),
        ),

        // Optimization Suggestions
        ...optimizations.map(
          (optimization) => Card(
            margin: const EdgeInsets.only(bottom: 12),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: (optimization['color'] as Color).withValues(
                  alpha: 0.1,
                ),
                child: Icon(
                  optimization['icon'] as IconData,
                  color: optimization['color'] as Color,
                  size: 20,
                ),
              ),
              title: Text(
                optimization['title'] as String,
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(optimization['description'] as String),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        'Save: ₹${(optimization['savings'] as int).toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'New total: ₹${(optimization['newTotal'] as int).toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              trailing: ElevatedButton(
                onPressed: () => _applyOptimization(optimization),
                style: ElevatedButton.styleFrom(
                  backgroundColor: optimization['color'] as Color,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                child: const Text('Apply', style: TextStyle(fontSize: 12)),
              ),
            ),
          ),
        ),

        if (optimizations.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  'No optimization suggestions available',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Consider reducing guest count or choosing a different venue type.',
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
      ],
    );
  }

  void _applyOptimization(Map<String, dynamic> optimization) {
    final type = optimization['type'] as String;

    switch (type) {
      case 'remove_menu_item':
        final itemIndex = optimization['itemIndex'] as int;
        setState(() {
          _selectedMenuItems.removeAt(itemIndex);
        });
        break;
      case 'reduce_quantity':
        final itemIndex = optimization['itemIndex'] as int;
        final newQuantity = optimization['newQuantity'] as int;
        setState(() {
          _selectedMenuItems[itemIndex]['quantity'] = newQuantity;
        });
        break;
      case 'guest_reduction':
        final newGuestCount = optimization['newGuestCount'] as int;
        setState(() {
          _guestCountController.text = newGuestCount.toString();
        });
        break;
      case 'remove_decoration':
        setState(() {
          _includeDecoration = false;
        });
        break;
      case 'remove_photography':
        setState(() {
          _includePhotography = false;
        });
        break;
      case 'remove_entertainment':
        setState(() {
          _includeEntertainment = false;
        });
        break;
    }

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Applied: ${optimization['title']}'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'Recalculate',
          textColor: Colors.white,
          onPressed: () => _generateShoppingList(),
        ),
      ),
    );
  }

  Widget _buildCostSummaryCard(Map<String, dynamic> costEstimate) {
    final isOverBudget = costEstimate['isOverBudget'] as bool;
    final userBudget = costEstimate['userBudget'] as int;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isOverBudget
              ? [Colors.red.shade400, Colors.red.shade600]
              : [Colors.green.shade400, Colors.green.shade600],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(
                Icons.account_balance_wallet,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'Total Estimated Cost',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '₹${costEstimate['total'].toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'For ${costEstimate['guestCount']} guests',
            style: const TextStyle(fontSize: 14, color: Colors.white70),
          ),
          if (userBudget > 0) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                isOverBudget
                    ? 'Over budget by ₹${(costEstimate['total'] - userBudget).toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}'
                    : 'Within budget! ₹${(userBudget - costEstimate['total']).toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')} saved',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.access_time, size: 12, color: Colors.white60),
              const SizedBox(width: 4),
              Text(
                'Prices updated: ${costEstimate['priceUpdateTime']}',
                style: const TextStyle(fontSize: 10, color: Colors.white60),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCostBreakdownTab(Map<String, dynamic> costEstimate) {
    final items = [
      {
        'name': 'Food & Catering',
        'cost': costEstimate['foodCost'],
        'icon': Icons.restaurant,
      },
      {
        'name': 'Venue Booking',
        'cost': costEstimate['venueCost'],
        'icon': Icons.business,
      },
      if (costEstimate['decorationCost'] > 0)
        {
          'name': 'Decoration & Themes',
          'cost': costEstimate['decorationCost'],
          'icon': Icons.auto_awesome,
        },
      if (costEstimate['photographyCost'] > 0)
        {
          'name': 'Photography & Video',
          'cost': costEstimate['photographyCost'],
          'icon': Icons.camera_alt,
        },
      if (costEstimate['entertainmentCost'] > 0)
        {
          'name': 'Entertainment',
          'cost': costEstimate['entertainmentCost'],
          'icon': Icons.music_note,
        },
    ];

    return ListView(
      children: [
        ...items.map(
          (item) => Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.blue.withValues(alpha: 0.1),
                child: Icon(item['icon'] as IconData, color: Colors.blue),
              ),
              title: Text(
                item['name'] as String,
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
              trailing: Text(
                '₹${(item['cost'] as int).toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.green,
                ),
              ),
            ),
          ),
        ),
        const Divider(),
        ListTile(
          title: const Text(
            'Subtotal',
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
          trailing: Text(
            '₹${costEstimate['subtotal'].toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
        ),
        ListTile(
          title: const Text(
            'Taxes (18% GST)',
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
          trailing: Text(
            '₹${costEstimate['taxes'].toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
        ),
        const Divider(thickness: 2),
        ListTile(
          title: const Text(
            'Total Amount',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
          ),
          trailing: Text(
            '₹${costEstimate['total'].toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
              color: Colors.green,
            ),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _eventNameController.dispose();
    _guestCountController.dispose();
    _durationController.dispose();
    _specialRequestsController.dispose();
    _budgetController.dispose();
    super.dispose();
  }
}
