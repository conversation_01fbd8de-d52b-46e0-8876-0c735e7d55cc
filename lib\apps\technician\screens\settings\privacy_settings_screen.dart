import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/providers/settings_provider.dart';
import 'package:shivish/apps/technician/screens/settings/privacy_policy_screen.dart';
import 'package:shivish/apps/technician/screens/settings/data_management_screen.dart';

class PrivacySettingsScreen extends ConsumerWidget {
  const PrivacySettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locationTracking = ref.watch(locationTrackingProvider);
    final setLocationTracking =
        ref.read(locationTrackingProvider.notifier).setLocationTracking;
    final analyticsTracking = ref.watch(analyticsTrackingProvider);
    final setAnalyticsTracking =
        ref.read(analyticsTrackingProvider.notifier).setAnalyticsTracking;
    final crashReporting = ref.watch(crashReportingProvider);
    final setCrashReporting =
        ref.read(crashReportingProvider.notifier).setCrashReporting;
    final dataCollection = ref.watch(dataCollectionProvider);
    final setDataCollection =
        ref.read(dataCollectionProvider.notifier).setDataCollection;
    final personalizedAds = ref.watch(personalizedAdsProvider);
    final setPersonalizedAds =
        ref.read(personalizedAdsProvider.notifier).setPersonalizedAds;

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Privacy Settings',
      ),
      body: ListView(
        children: [
          _buildPrivacySection(
            context,
            'Location Tracking',
            'Allow app to access your location',
            locationTracking,
            setLocationTracking,
            Icons.location_on,
          ),
          _buildPrivacySection(
            context,
            'Analytics Tracking',
            'Help us improve the app by sharing usage data',
            analyticsTracking,
            setAnalyticsTracking,
            Icons.analytics,
          ),
          _buildPrivacySection(
            context,
            'Crash Reporting',
            'Send crash reports to help fix issues',
            crashReporting,
            setCrashReporting,
            Icons.bug_report,
          ),
          _buildPrivacySection(
            context,
            'Data Collection',
            'Allow collection of app usage data',
            dataCollection,
            setDataCollection,
            Icons.data_usage,
          ),
          _buildPrivacySection(
            context,
            'Personalized Ads',
            'Show personalized advertisements',
            personalizedAds,
            setPersonalizedAds,
            Icons.ads_click,
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.privacy_tip),
            title: const Text('Privacy Policy'),
            subtitle: const Text('Read our privacy policy'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PrivacyPolicyScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.security),
            title: const Text('Data Management'),
            subtitle: const Text('Manage your data and privacy'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DataManagementScreen(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacySection(
    BuildContext context,
    String title,
    String subtitle,
    bool enabled,
    void Function(bool) setEnabled,
    IconData icon,
  ) {
    return SwitchListTile(
      secondary: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      value: enabled,
      onChanged: (value) {
        setEnabled(value);
        _showSnackBar(
          context,
          '$title ${value ? 'enabled' : 'disabled'}',
        );
      },
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
