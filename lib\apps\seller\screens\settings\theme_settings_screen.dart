import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/presentation/providers/settings_provider.dart';
import 'package:shivish/apps/seller/presentation/cubits/settings_cubit.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';

class ThemeSettingsScreen extends ConsumerStatefulWidget {
  const ThemeSettingsScreen({super.key});

  @override
  ConsumerState<ThemeSettingsScreen> createState() =>
      _ThemeSettingsScreenState();
}

class _ThemeSettingsScreenState extends ConsumerState<ThemeSettingsScreen> {
  String _selectedTheme = 'system';
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadThemeSettings();
  }

  Future<void> _loadThemeSettings() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final settingsState = ref.read(settingsProvider);

      settingsState.maybeWhen(
        loaded: (settings) {
          setState(() {
            _selectedTheme = settings.theme;
          });
        },
        orElse: () {},
      );
    } catch (e) {
      setState(() {
        _error = 'Failed to load theme settings: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveThemeSettings(String theme) async {
    setState(() {
      _isLoading = true;
      _error = null;
      _selectedTheme = theme;
    });

    try {
      final settingsState = ref.read(settingsProvider);

      settingsState.maybeWhen(
        loaded: (settings) async {
          final updatedSettings = Settings(
            orderNotifications: settings.orderNotifications,
            productNotifications: settings.productNotifications,
            systemNotifications: settings.systemNotifications,
            theme: theme,
            language: settings.language,
            currency: settings.currency,
          );

          await ref
              .read(settingsProvider.notifier)
              .updateSettings(updatedSettings);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Theme settings updated successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        orElse: () {},
      );
    } catch (e) {
      setState(() {
        _error = 'Failed to save theme settings: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save theme settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        appBar: AppToolbar(
          title: 'Theme Settings',
        ),
        body: Center(
          child: LoadingIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: const AppToolbar(
          title: 'Theme Settings',
        ),
        body: ErrorMessage(
          message: _error!,
          onRetry: _loadThemeSettings,
        ),
      );
    }

    return Scaffold(
      appBar: const AppToolbar(
        title: 'Theme Settings',
      ),
      body: ListView(
        children: [
          RadioListTile<String>(
            title: const Text('System Default'),
            subtitle: const Text('Follow system theme settings'),
            value: 'system',
            groupValue: _selectedTheme,
            onChanged: (value) {
              if (value != null) {
                _saveThemeSettings(value);
              }
            },
          ),
          RadioListTile<String>(
            title: const Text('Light Theme'),
            subtitle: const Text('Use light theme'),
            value: 'light',
            groupValue: _selectedTheme,
            onChanged: (value) {
              if (value != null) {
                _saveThemeSettings(value);
              }
            },
          ),
          RadioListTile<String>(
            title: const Text('Dark Theme'),
            subtitle: const Text('Use dark theme'),
            value: 'dark',
            groupValue: _selectedTheme,
            onChanged: (value) {
              if (value != null) {
                _saveThemeSettings(value);
              }
            },
          ),
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'Note: Theme changes will take effect when you restart the app.',
              style: TextStyle(
                fontStyle: FontStyle.italic,
                color: Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
