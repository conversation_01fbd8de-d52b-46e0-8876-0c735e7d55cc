import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/auth/supabase_auth_service.dart';
import '../services/storage_service.dart';
import '../services/analytics/analytics_service.dart';
import '../services/messaging_service.dart';
import '../core/auth/repositories/auth_repository.dart';
import '../core/auth/repositories/supabase_auth_repository.dart';
import '../services/product/product_service.dart';
import '../services/review/review_service.dart';
import '../services/executor_service.dart';
import '../services/seller_service.dart';
import '../services/priest_service.dart';
import '../services/technician_service.dart';
import '../repositories/technician_repository.dart';
import '../../apps/admin/bloc/executor/executor_bloc.dart';
import '../../apps/admin/bloc/priest/priest_bloc.dart';
import '../../apps/admin/bloc/product/product_bloc.dart';
import '../../apps/admin/bloc/seller/seller_bloc.dart';
import '../../apps/admin/bloc/technician/technician_bloc.dart';
import 'package:injectable/injectable.dart';
import '../services/commission/commission_service.dart';
import '../../apps/admin/bloc/commission/commission_bloc.dart';
import '../../apps/admin/bloc/analytics/analytics_bloc.dart';
import '../services/payment_gateway/payment_gateway_service.dart';
import '../services/payment_gateway/payment_gateway_bloc.dart';
import '../services/temple/temple_service.dart';
import '../services/admin/admin_temple_service.dart';
import '../services/temple/temple_dashboard_service.dart';
import '../services/temple/temple_booking_service.dart';
import '../services/temple/temple_notification_service.dart';
import '../services/temple/temple_booking_notification_service.dart';
import '../services/auth/auth_service.dart';
import '../services/notification/notification_service.dart';
import '../services/location/location_service.dart';
import '../services/debug_service.dart';
import 'package:shivish/shared/services/language_service.dart';
import '../services/payment/biometric_auth_service.dart';
import '../services/system_config_service.dart';
import '../repositories/system_config_repository.dart';
import '../repositories/payment_gateway_repository.dart';
import '../services/tax/tax_update_service.dart';
import '../services/storage/adaptive_storage_service.dart';
import '../services/storage/storage_config_listener.dart';
import '../database/config/database_config.dart';
import '../database/services/database_service.dart';
import '../database/services/migration_service.dart';
import '../repositories/user_repository.dart';

final GetIt serviceLocator = GetIt.instance;

// Global navigator key for accessing context from anywhere
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

@InjectableInit()
Future<void> setupServiceLocator() async {
  // Register Firebase services as singletons (keeping free services only)
  if (!serviceLocator.isRegistered<GoogleSignIn>()) {
    serviceLocator.registerLazySingleton<GoogleSignIn>(
      () => GoogleSignIn.instance,
    );
  }

  // Register NotificationService
  if (!serviceLocator.isRegistered<NotificationService>()) {
    serviceLocator.registerLazySingleton<NotificationService>(() {
      return NotificationService();
    });
    debugPrint('NotificationService registered successfully');
  }

  // Register DebugService
  if (!serviceLocator.isRegistered<DebugService>()) {
    serviceLocator.registerLazySingleton<DebugService>(() => DebugService());
  }

  // Register SharedPreferences
  final prefs = await SharedPreferences.getInstance();
  if (!serviceLocator.isRegistered<SharedPreferences>()) {
    serviceLocator.registerSingleton<SharedPreferences>(prefs);
  }

  // Register language service
  final languageService = await LanguageService.initialize();
  if (!serviceLocator.isRegistered<LanguageService>()) {
    serviceLocator.registerSingleton<LanguageService>(languageService);
  }

  // Register Supabase auth services as singletons
  if (!serviceLocator.isRegistered<SupabaseAuthService>()) {
    serviceLocator.registerLazySingleton<SupabaseAuthService>(
      () => SupabaseAuthService(),
    );
  }

  // Register hybrid database services
  if (!serviceLocator.isRegistered<DatabaseConfig>()) {
    serviceLocator.registerLazySingleton<DatabaseConfig>(
      () => DatabaseConfig.fromEnvironment(),
    );
  }

  if (!serviceLocator.isRegistered<DatabaseService>()) {
    serviceLocator.registerLazySingleton<DatabaseService>(
      () => DatabaseService(serviceLocator<DatabaseConfig>()),
    );
  }

  if (!serviceLocator.isRegistered<MigrationService>()) {
    serviceLocator.registerLazySingleton<MigrationService>(
      () => MigrationService(serviceLocator<DatabaseService>()),
    );
  }

  if (!serviceLocator.isRegistered<UserRepository>()) {
    serviceLocator.registerLazySingleton<UserRepository>(
      () => UserRepository(),
    );
  }
  if (!serviceLocator.isRegistered<StorageService>()) {
    serviceLocator.registerLazySingleton<StorageService>(
      () => StorageService(),
    );
  }

  // Register adaptive storage service
  if (!serviceLocator.isRegistered<AdaptiveStorageService>()) {
    final adaptiveStorageService = AdaptiveStorageService();
    // Initialize the service
    adaptiveStorageService.initialize();
    serviceLocator.registerSingleton<AdaptiveStorageService>(
      adaptiveStorageService,
    );
  }

  // Register storage config listener
  if (!serviceLocator.isRegistered<StorageConfigListener>()) {
    final storageConfigListener = StorageConfigListener(null);
    storageConfigListener.initialize();
    serviceLocator.registerSingleton<StorageConfigListener>(
      storageConfigListener,
    );
  }
  if (!serviceLocator.isRegistered<AnalyticsService>()) {
    serviceLocator.registerSingleton<AnalyticsService>(AnalyticsService());
  }
  if (!serviceLocator.isRegistered<MessagingService>()) {
    serviceLocator.registerLazySingleton<MessagingService>(
      () => MessagingService(),
    );
  }

  if (!serviceLocator.isRegistered<AuthRepository>()) {
    serviceLocator.registerSingleton<AuthRepository>(
      SupabaseAuthRepository(serviceLocator.get<SupabaseAuthService>()),
    );
  }
  if (!serviceLocator.isRegistered<ProductService>()) {
    serviceLocator.registerLazySingleton<ProductService>(
      () => ProductService(),
    );
  }
  if (!serviceLocator.isRegistered<ReviewService>()) {
    serviceLocator.registerLazySingleton<ReviewService>(() => ReviewService());
  }

  // Register ExecutorService and ExecutorBloc
  if (!serviceLocator.isRegistered<ExecutorService>()) {
    serviceLocator.registerLazySingleton<ExecutorService>(
      () => ExecutorService(serviceLocator<SharedPreferences>()),
    );
  }
  if (!serviceLocator.isRegistered<ExecutorBloc>()) {
    serviceLocator.registerFactory<ExecutorBloc>(
      () => ExecutorBloc(serviceLocator<ExecutorService>()),
    );
  }

  // Register PriestService and PriestBloc
  if (!serviceLocator.isRegistered<PriestService>()) {
    serviceLocator.registerSingleton<PriestService>(
      PriestService(
        serviceLocator<DatabaseService>(),
        serviceLocator<SharedPreferences>(),
      ),
    );
  }

  // Register Temple Services
  if (!serviceLocator.isRegistered<TempleService>()) {
    serviceLocator.registerLazySingleton<TempleService>(
      () => TempleService(
        serviceLocator<DatabaseService>(),
        serviceLocator<AuthService>(),
        serviceLocator<TempleBookingNotificationService>(),
      ),
    );
  }

  if (!serviceLocator.isRegistered<AdminTempleService>()) {
    serviceLocator.registerLazySingleton<AdminTempleService>(
      () => AdminTempleService(
        serviceLocator<DatabaseService>(),
        serviceLocator<NotificationService>(),
      ),
    );
  }

  if (!serviceLocator.isRegistered<TempleDashboardService>()) {
    serviceLocator.registerLazySingleton<TempleDashboardService>(
      () => TempleDashboardService(serviceLocator<DatabaseService>()),
    );
  }

  if (!serviceLocator.isRegistered<TempleBookingService>()) {
    serviceLocator.registerLazySingleton<TempleBookingService>(
      () => TempleBookingService(serviceLocator<DatabaseService>()),
    );
  }

  if (!serviceLocator.isRegistered<TempleNotificationService>()) {
    serviceLocator.registerLazySingleton<TempleNotificationService>(
      () => TempleNotificationService(
        serviceLocator<NotificationService>(),
        serviceLocator<LocationService>(),
        serviceLocator<DatabaseService>(),
      ),
    );
  }

  if (!serviceLocator.isRegistered<TempleBookingNotificationService>()) {
    serviceLocator.registerLazySingleton<TempleBookingNotificationService>(
      () => TempleBookingNotificationService(
        serviceLocator<NotificationService>(),
      ),
    );
  }
  if (!serviceLocator.isRegistered<PriestBloc>()) {
    serviceLocator.registerFactory<PriestBloc>(
      () => PriestBloc(serviceLocator<PriestService>()),
    );
  }

  // Register ProductBloc
  if (!serviceLocator.isRegistered<ProductBloc>()) {
    serviceLocator.registerFactory<ProductBloc>(
      () => ProductBloc(serviceLocator<ProductService>()),
    );
  }

  // Register SellerService
  if (!serviceLocator.isRegistered<SellerService>()) {
    serviceLocator.registerLazySingleton<SellerService>(
      () => SellerService(
        serviceLocator<DatabaseService>(),
        serviceLocator<SharedPreferences>(),
      ),
    );
  }

  // Register SellerBloc
  if (!serviceLocator.isRegistered<SellerBloc>()) {
    serviceLocator.registerFactory<SellerBloc>(
      () => SellerBloc(serviceLocator<SellerService>()),
    );
  }

  // Register TechnicianRepository if not already registered
  if (!serviceLocator.isRegistered<TechnicianRepository>()) {
    serviceLocator.registerLazySingleton<TechnicianRepository>(
      () => TechnicianRepository(serviceLocator<SharedPreferences>()),
    );
  }

  // Register TechnicianService if not already registered
  if (!serviceLocator.isRegistered<TechnicianService>()) {
    serviceLocator.registerLazySingleton<TechnicianService>(
      () => TechnicianService(serviceLocator<TechnicianRepository>()),
    );
  }

  // Register TechnicianBloc
  if (!serviceLocator.isRegistered<TechnicianBloc>()) {
    serviceLocator.registerFactory<TechnicianBloc>(
      () => TechnicianBloc(serviceLocator<TechnicianService>()),
    );
  }

  // Register LocationService
  if (!serviceLocator.isRegistered<LocationService>()) {
    serviceLocator.registerLazySingleton<LocationService>(
      () => LocationService(),
    );
  }

  // Register SystemConfigService with DatabaseService
  if (!serviceLocator.isRegistered<SystemConfigService>()) {
    serviceLocator.registerLazySingleton<SystemConfigService>(() {
      final repository = SystemConfigRepository(
        serviceLocator<DatabaseService>(),
      );
      return SystemConfigService(repository);
    });
  }

  // Register PaymentGatewayRepository
  if (!serviceLocator.isRegistered<PaymentGatewayRepository>()) {
    serviceLocator.registerLazySingleton<PaymentGatewayRepository>(() {
      return PaymentGatewayRepository(serviceLocator<DatabaseService>());
    });
  }

  // Register PaymentGatewayService with DatabaseService
  if (!serviceLocator.isRegistered<PaymentGatewayService>()) {
    serviceLocator.registerLazySingleton<PaymentGatewayService>(() {
      return PaymentGatewayService(serviceLocator<PaymentGatewayRepository>());
    });
  }

  // Register PaymentGatewayBloc
  if (!serviceLocator.isRegistered<PaymentGatewayBloc>()) {
    serviceLocator.registerFactory<PaymentGatewayBloc>(() {
      return PaymentGatewayBloc(serviceLocator<PaymentGatewayService>());
    });
  }

  // Register CommissionService
  if (!serviceLocator.isRegistered<CommissionService>()) {
    serviceLocator.registerLazySingleton<CommissionService>(
      () => CommissionService(),
    );
  }

  // Register CommissionBloc
  if (!serviceLocator.isRegistered<CommissionBloc>()) {
    serviceLocator.registerFactory<CommissionBloc>(() {
      return CommissionBloc(serviceLocator<CommissionService>());
    });
  }

  // Register AnalyticsBloc
  if (!serviceLocator.isRegistered<AnalyticsBloc>()) {
    serviceLocator.registerFactory<AnalyticsBloc>(() {
      return AnalyticsBloc(serviceLocator<AnalyticsService>());
    });
  }

  // Note: BackupController and related services are not implemented yet
  // The hybrid storage system already provides automatic backup functionality

  // Initialize database and run migrations
  try {
    debugPrint('Initializing database...');
    final databaseService = serviceLocator<DatabaseService>();
    await databaseService.initialize();
    debugPrint('Database initialized successfully');

    debugPrint('Running database migrations...');
    final migrationService = serviceLocator<MigrationService>();
    await migrationService.runMigrations();
    debugPrint('Database migrations completed successfully');
  } catch (e) {
    debugPrint('Database initialization or migration failed: $e');
    // This is critical - rethrow to prevent app startup with broken database
    rethrow;
  }

  // Initialize services that require async initialization
  try {
    final messagingService = serviceLocator<MessagingService>();
    await messagingService.initialize();
    debugPrint('MessagingService initialized successfully');
  } catch (e) {
    debugPrint('MessagingService initialization failed: $e');
    // Continue without messaging service
  }

  // Initialize biometric authentication service
  try {
    debugPrint('Initializing BiometricAuthService...');
    await BiometricAuthService.initialize();
    debugPrint('BiometricAuthService initialized');
  } catch (e) {
    debugPrint('BiometricAuthService initialization failed: $e');
    // Continue without biometric authentication service
  }

  // Initialize location service
  try {
    debugPrint('Initializing LocationService...');
    final locationService = serviceLocator<LocationService>();
    await locationService.initialize();
    debugPrint('LocationService initialized');
  } catch (e) {
    debugPrint('LocationService initialization failed: $e');
    // Continue without location service
  }

  // Initialize tax update service
  try {
    debugPrint('Initializing TaxUpdateService...');
    final taxUpdateService = TaxUpdateService();
    await taxUpdateService.initialize();
    taxUpdateService.start();
    serviceLocator.registerSingleton<TaxUpdateService>(taxUpdateService);
    debugPrint('TaxUpdateService initialized and started');
  } catch (e) {
    debugPrint('TaxUpdateService initialization failed: $e');
    // Continue without tax update service
  }
}

// Extension method for easier access to services
extension ServiceLocatorExtension on GetIt {
  SupabaseAuthService get auth => get<SupabaseAuthService>();
  SupabaseAuthService get supabaseAuth => get<SupabaseAuthService>();
  DatabaseService get database => get<DatabaseService>();
  StorageService get storage => get<StorageService>();
  AnalyticsService get analytics => get<AnalyticsService>();
  MessagingService get messaging => get<MessagingService>();
  AuthRepository get authRepository => get<AuthRepository>();
  ProductService get product => get<ProductService>();
  ReviewService get review => get<ReviewService>();
  ExecutorService get executor => get<ExecutorService>();
  ExecutorBloc get executorBloc => get<ExecutorBloc>();
  PriestService get priest => get<PriestService>();
  PriestBloc get priestBloc => get<PriestBloc>();
  SellerService get seller => get<SellerService>();
  SellerBloc get sellerBloc => get<SellerBloc>();
  TechnicianService get technician => get<TechnicianService>();
  TechnicianBloc get technicianBloc => get<TechnicianBloc>();
  DebugService get debug => get<DebugService>();
  LocationService get location => get<LocationService>();
  MigrationService get migration => get<MigrationService>();
  CommissionService get commission => get<CommissionService>();
  CommissionBloc get commissionBloc => get<CommissionBloc>();
  AnalyticsBloc get analyticsBloc => get<AnalyticsBloc>();
  PaymentGatewayService get paymentGateway => get<PaymentGatewayService>();
  PaymentGatewayBloc get paymentGatewayBloc => get<PaymentGatewayBloc>();
  SystemConfigService get systemConfig => get<SystemConfigService>();
  AdaptiveStorageService get adaptiveStorage => get<AdaptiveStorageService>();
  StorageConfigListener get storageConfigListener =>
      get<StorageConfigListener>();
  TaxUpdateService get taxUpdate => get<TaxUpdateService>();
}
