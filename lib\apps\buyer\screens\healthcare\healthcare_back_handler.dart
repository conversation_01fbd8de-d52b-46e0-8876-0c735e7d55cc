import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/buyer/buyer_routes.dart';

/// A specialized handler for back button navigation in the healthcare screen
class HealthcareBackHandler extends StatefulWidget {
  /// The child widget to wrap with back button handling
  final Widget child;
  
  const HealthcareBackHandler({
    super.key,
    required this.child,
  });

  @override
  State<HealthcareBackHandler> createState() => _HealthcareBackHandlerState();
}

class _HealthcareBackHandlerState extends State<HealthcareBackHandler> {
  @override
  void initState() {
    super.initState();
    // Set up a method channel to intercept Android back button
    SystemChannels.navigation.setMethodCallHandler(_handleSystemNavigationCall);
  }
  
  @override
  void dispose() {
    // Clear the handler when disposed
    SystemChannels.navigation.setMethodCallHandler(null);
    super.dispose();
  }
  
  /// Handle system navigation calls (including Android back button)
  Future<dynamic> _handleSystemNavigationCall(MethodCall call) async {
    if (call.method == 'popRoute') {
      // Android back button was pressed
      return _handleBackButton();
    }
    
    // For other navigation events, let the system handle them
    return false;
  }
  
  /// Handle back button press
  Future<bool> _handleBackButton() async {
    debugPrint('HealthcareBackHandler: Back button pressed');
    
    // Get the router
    final router = GoRouter.of(context);
    
    // Check if we can pop using GoRouter
    if (router.canPop()) {
      debugPrint('HealthcareBackHandler: GoRouter can pop, popping');
      router.pop();
      return true; // We handled the back button
    }
    
    // If we can't pop, go to home
    debugPrint('HealthcareBackHandler: Going to home');
    router.go(BuyerRoutes.home);
    return true; // We handled the back button
  }
  
  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
