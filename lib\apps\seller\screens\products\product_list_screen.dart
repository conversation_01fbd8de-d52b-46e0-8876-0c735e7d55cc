import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/apps/seller/presentation/providers/product_provider.dart';
import 'package:shivish/apps/seller/application/seller_provider.dart';
import 'package:shivish/apps/seller/screens/products/add_product_screen.dart';
import 'package:shivish/apps/seller/screens/products/bulk_upload_screen.dart';
import 'package:shivish/apps/seller/screens/products/product_details_screen.dart';
import 'package:shivish/apps/seller/widgets/seller_product_card.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

class ProductListScreen extends ConsumerStatefulWidget {
  const ProductListScreen({super.key});

  @override
  ConsumerState<ProductListScreen> createState() => _ProductListScreenState();
}

class _ProductListScreenState extends ConsumerState<ProductListScreen> {
  // Timer for periodic refresh
  Timer? _refreshTimer;

  /// Check if current seller is an event service provider
  bool get _isEventService {
    final sellerAsync = ref.read(sellerProvider);
    return sellerAsync.value?.category.isEventService ?? false;
  }

  @override
  void initState() {
    super.initState();
    // Refresh products when the screen is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(productsProvider.notifier).refreshProducts();

      // Set up periodic refresh every 30 seconds
      _refreshTimer = Timer.periodic(const Duration(seconds: 30), (_) {
        if (mounted) {
          ref.read(productsProvider.notifier).refreshProducts();
        }
      });
    });
  }

  @override
  void dispose() {
    // Cancel the timer when the screen is disposed
    _refreshTimer?.cancel();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // This ensures the product list is refreshed when returning from other screens
    ref.read(productsProvider.notifier).refreshProducts();
  }

  @override
  Widget build(BuildContext context) {
    final productsState = ref.watch(productsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEventService ? 'Service Packages' : 'Products'),
        actions: [
          // Add Single Product Button
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: _isEventService ? 'Add Service Package' : 'Add Product',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddProductScreen(),
                ),
              );
            },
          ),
        ],
      ),
      // Add a floating action button for bulk upload
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const BulkUploadScreen()),
          );
        },
        icon: const Icon(Icons.upload_file),
        label: const Text('Bulk Upload'),
        tooltip: 'Upload multiple products at once',
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.read(productsProvider.notifier).refreshProducts();
        },
        child: productsState.when(
          data: (products) {
            if (products.isEmpty) {
              return Center(
                child: Text(
                  _isEventService
                      ? 'No service packages found'
                      : 'No products found',
                ),
              );
            }

            return GridView.builder(
              padding: const EdgeInsets.all(12),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 12,
                crossAxisSpacing: 12,
                childAspectRatio:
                    0.7, // Adjusted for the new layout with stock on new line
              ),
              itemCount: products.length,
              itemBuilder: (context, index) {
                final product = products[index];
                return SellerProductCard(
                  product: product,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            ProductDetailsScreen(productId: product.id),
                      ),
                    );
                  },
                );
              },
            );
          },
          loading: () => const LoadingIndicator(),
          error: (error, stackTrace) => ErrorMessage(
            message: error.toString(),
            onRetry: () {
              ref.read(productsProvider.notifier).loadProducts();
            },
          ),
        ),
      ),
    );
  }
}
