import 'package:fl_chart/fl_chart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_analytics_state.freezed.dart';

@freezed
sealed class ProductAnalyticsState with _$ProductAnalyticsState {
  const factory ProductAnalyticsState({
    required double totalSales,
    required int totalOrders,
    required double averageOrderValue,
    required double conversionRate,
    required double salesTrend,
    required double ordersTrend,
    required double aovTrend,
    required double conversionTrend,
    required List<FlSpot> salesData,
    required List<BarChartGroupData> topProductsData,
    required List<InventoryStatus> inventoryStatus,
  }) = _ProductAnalyticsState;
}

class InventoryStatus {
  final String productName;
  final int currentStock;
  final double stockPercentage;

  const InventoryStatus({
    required this.productName,
    required this.currentStock,
    required this.stockPercentage,
  });
}
