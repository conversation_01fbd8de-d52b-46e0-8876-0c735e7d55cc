import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../temples_routes.dart';

class TemplesShell extends StatefulWidget {
  final Widget child;

  const TemplesShell({super.key, required this.child});

  @override
  State<TemplesShell> createState() => _TemplesShellState();
}

class _TemplesShellState extends State<TemplesShell> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    // Determine current index based on route
    final location = GoRouterState.of(context).uri.path;
    _selectedIndex = _getIndexFromLocation(location);

    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        backgroundColor: Colors.white,
        elevation: 8,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.temple_hindu),
            label: 'Services',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.book_online),
            label: 'Bookings',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Analytics',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }

  int _getIndexFromLocation(String location) {
    if (location.startsWith(TemplesRoutes.home)) return 0;
    if (location.startsWith(TemplesRoutes.services)) return 1;
    if (location.startsWith(TemplesRoutes.bookings)) return 2;
    if (location.startsWith(TemplesRoutes.analytics)) return 3;
    if (location.startsWith(TemplesRoutes.profile)) return 4;
    return 0;
  }

  void _onItemTapped(int index) {
    if (index == _selectedIndex) return;

    switch (index) {
      case 0:
        context.go(TemplesRoutes.home);
        break;
      case 1:
        context.go(TemplesRoutes.services);
        break;
      case 2:
        context.go(TemplesRoutes.bookings);
        break;
      case 3:
        context.go(TemplesRoutes.analytics);
        break;
      case 4:
        context.go(TemplesRoutes.profile);
        break;
    }
  }
}
