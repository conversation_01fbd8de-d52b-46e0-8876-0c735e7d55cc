import 'package:flutter/material.dart';
import '../widgets/admin_bottom_nav_bar.dart';
import '../../../../apps/admin/widgets/voice/admin_voice_assistant_overlay.dart';

class AdminShell extends StatelessWidget {
  final Widget child;
  final int currentIndex;

  const AdminShell({
    super.key,
    required this.child,
    this.currentIndex = 2, // Default to home tab
  });

  @override
  Widget build(BuildContext context) {
    // Wrap everything with the AdminVoiceAssistantOverlay for AI assistant functionality
    return AdminVoiceAssistantOverlay(child: _buildShellContent(context));
  }

  Widget _buildShellContent(BuildContext context) {
    // Check if child is already a Scaffold
    if (child is Scaffold) {
      final scaffold = child as Scaffold;
      // Return a modified version of the child Scaffold with our bottom nav bar
      return Scaffold(
        key: scaffold.key,
        appBar: scaffold.appBar,
        body: scaffold.body,
        drawer: scaffold.drawer,
        endDrawer: scaffold.endDrawer,
        floatingActionButton: scaffold.floatingActionButton,
        floatingActionButtonLocation: scaffold.floatingActionButtonLocation,
        floatingActionButtonAnimator: scaffold.floatingActionButtonAnimator,
        persistentFooterButtons: scaffold.persistentFooterButtons,
        bottomNavigationBar: AdminBottomNavBar(currentIndex: currentIndex),
        backgroundColor: scaffold.backgroundColor,
        resizeToAvoidBottomInset: scaffold.resizeToAvoidBottomInset,
        primary: scaffold.primary,
        drawerDragStartBehavior: scaffold.drawerDragStartBehavior,
        extendBody: scaffold.extendBody,
        extendBodyBehindAppBar: scaffold.extendBodyBehindAppBar,
        drawerScrimColor: scaffold.drawerScrimColor,
        drawerEdgeDragWidth: scaffold.drawerEdgeDragWidth,
        drawerEnableOpenDragGesture: scaffold.drawerEnableOpenDragGesture,
        endDrawerEnableOpenDragGesture: scaffold.endDrawerEnableOpenDragGesture,
      );
    } else {
      // If child is not a Scaffold, wrap it in one
      return Scaffold(
        body: child,
        bottomNavigationBar: AdminBottomNavBar(currentIndex: currentIndex),
      );
    }
  }
}
