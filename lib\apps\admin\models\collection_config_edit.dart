import '../../../shared/models/config/storage_config_model.dart';

/// Class to hold temporary collection config changes
class CollectionConfigEdit {
  final ContentCollectionType collectionType;
  String primaryProviderName;
  List<String> additionalProviderNames;
  String basePath;
  bool isEnabled;
  BalancingStrategy balancingStrategy;
  bool hasChanges = false;

  CollectionConfigEdit({
    required this.collectionType,
    required this.primaryProviderName,
    required this.additionalProviderNames,
    required this.basePath,
    required this.isEnabled,
    required this.balancingStrategy,
  });

  factory CollectionConfigEdit.fromConfig(CollectionStorageConfig config) {
    return CollectionConfigEdit(
      collectionType: config.collectionType,
      primaryProviderName: config.primaryProviderName,
      additionalProviderNames: List.from(config.additionalProviderNames),
      basePath: config.basePath,
      isEnabled: config.isEnabled,
      balancingStrategy: config.balancingStrategy,
    );
  }

  CollectionStorageConfig toConfig() {
    return CollectionStorageConfig(
      collectionType: collectionType,
      primaryProviderName: primaryProviderName,
      additionalProviderNames: additionalProviderNames,
      basePath: basePath,
      isEnabled: isEnabled,
      balancingStrategy: balancingStrategy,
    );
  }
}
