import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/apps/technician/providers/tax_settings_provider.dart';

class TaxSettingsScreen extends ConsumerStatefulWidget {
  const TaxSettingsScreen({super.key});

  @override
  ConsumerState<TaxSettingsScreen> createState() => _TaxSettingsScreenState();
}

class _TaxSettingsScreenState extends ConsumerState<TaxSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _gstNumberController;
  late TextEditingController _taxRateController;
  late TextEditingController _panNumberController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _gstNumberController = TextEditingController();
    _taxRateController = TextEditingController();
    _panNumberController = TextEditingController();
    _loadTaxSettings();
  }

  @override
  void dispose() {
    _gstNumberController.dispose();
    _taxRateController.dispose();
    _panNumberController.dispose();
    super.dispose();
  }

  Future<void> _loadTaxSettings() async {
    final settings = await ref.read(taxSettingsProvider.future);
    if (mounted) {
      setState(() {
        _gstNumberController.text = settings['gstNumber'] ?? '';
        _panNumberController.text = settings['panNumber'] ?? '';
        _taxRateController.text = (settings['taxRate'] ?? 0.0).toString();
      });
    }
  }

  Future<void> _updateTaxSettings() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      try {
        final settings = {
          'gstNumber': _gstNumberController.text,
          'panNumber': _panNumberController.text,
          'taxRate': double.parse(_taxRateController.text),
        };

        await ref
            .read(taxSettingsProvider.notifier)
            .updateTaxSettings(settings);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tax settings updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update tax settings: ${e.toString()}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Tax Settings',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Tax Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      AppTextField(
                        controller: _gstNumberController,
                        label: 'GST Number',
                        hint: 'Enter GST number',
                        prefixIcon: const Icon(Icons.numbers),
                        textCapitalization: TextCapitalization.characters,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter GST number';
                          }
                          if (value.length != 15) {
                            return 'Please enter a valid GST number';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      AppTextField(
                        controller: _panNumberController,
                        label: 'PAN Number',
                        hint: 'Enter PAN number',
                        prefixIcon: const Icon(Icons.numbers),
                        textCapitalization: TextCapitalization.characters,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter PAN number';
                          }
                          if (value.length != 10) {
                            return 'Please enter a valid PAN number';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      AppTextField(
                        controller: _taxRateController,
                        label: 'Tax Rate (%)',
                        hint: 'Enter tax rate',
                        prefixIcon: const Icon(Icons.percent),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter tax rate';
                          }
                          final rate = double.tryParse(value);
                          if (rate == null || rate < 0 || rate > 100) {
                            return 'Please enter a valid tax rate (0-100)';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              AppButton(
                onPressed: _isLoading ? null : _updateTaxSettings,
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Text('Update Tax Settings'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
