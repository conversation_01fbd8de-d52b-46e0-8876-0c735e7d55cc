import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';
import '../../repositories/shopping_list_repository.dart';
import '../../services/shopping_list_service.dart';
import '../../../../shared/providers/auth_provider.dart';

/// Provider for the shopping list service
final shoppingListServiceProvider = Provider<ShoppingListService>((ref) {
  return ShoppingListService();
});

/// Provider for the shopping list repository
final shoppingListRepositoryProvider = Provider<ShoppingListRepository>((ref) {
  return ShoppingListRepository(ref.watch(shoppingListServiceProvider));
});

/// Provider for the current user's shopping lists
final userShoppingListsProvider =
    StreamProvider<List<ShoppingListModel>>((ref) {
  final user = ref.watch(currentUserProvider).value;
  if (user == null) return Stream.value([]);
  return ref.watch(shoppingListServiceProvider).getUserShoppingLists(user.id);
});

/// Provider for the current user's templates
final userTemplatesProvider = StreamProvider<List<ShoppingListModel>>((ref) {
  final user = ref.watch(currentUserProvider).value;
  if (user == null) return Stream.value([]);
  return ref.watch(shoppingListServiceProvider).getUserTemplates(user.id);
});

/// Provider for the current user's completed shopping lists
final userCompletedShoppingListsProvider =
    StreamProvider.family<List<ShoppingListModel>, String>((ref, userId) {
  return ref
      .watch(shoppingListServiceProvider)
      .getCompletedShoppingLists(userId);
});

/// Provider for a specific shopping list
final shoppingListProvider = StreamProvider.family<ShoppingListModel?, String>(
  (ref, listId) =>
      ref.watch(shoppingListRepositoryProvider).getShoppingList(listId),
);

/// Provider for shopping list operations
final shoppingListNotifierProvider =
    StateNotifierProvider<ShoppingListNotifier, AsyncValue<void>>(
  (ref) => ShoppingListNotifier(ref.watch(shoppingListRepositoryProvider)),
);

class ShoppingListNotifier extends StateNotifier<AsyncValue<void>> {
  final ShoppingListRepository _repository;

  ShoppingListNotifier(this._repository) : super(const AsyncValue.data(null));

  Future<void> shareShoppingList(String listId, String userId) async {
    state = const AsyncValue.loading();
    try {
      await _repository.shareShoppingList(listId, userId);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> unshareShoppingList(String listId, String userId) async {
    state = const AsyncValue.loading();
    try {
      await _repository.unshareShoppingList(listId, userId);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateUserAccessLevel(
    String listId,
    String userId,
    String accessLevel,
  ) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateUserAccessLevel(listId, userId, accessLevel);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
