import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/theme/traditional_colors.dart';
import '../../providers/temple_auth_provider.dart';

class TempleProfileScreen extends ConsumerWidget {
  const TempleProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(templeAuthProvider);
    final temple = authState.temple;

    if (temple == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Temple Profile'),
        backgroundColor: TraditionalColors.templeOrange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => context.push('/temples/profile/edit'),
            icon: const Icon(Icons.edit),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildHeader(temple),
            _buildBasicInfo(temple),
            _buildContactInfo(temple),
            _buildLocationInfo(temple),
            _buildServicesInfo(temple),
            _buildAdditionalInfo(temple),
            _buildActionButtons(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(temple) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            TraditionalColors.templeOrange.withValues(alpha: 0.1),
            Colors.white,
          ],
        ),
      ),
      child: Column(
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: TraditionalColors.templeOrange,
              borderRadius: BorderRadius.circular(50),
            ),
            child: temple.coverImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(50),
                    child: Image.network(
                      temple.coverImage!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildPlaceholderIcon(),
                    ),
                  )
                : _buildPlaceholderIcon(),
          ),
          const SizedBox(height: 16),
          Text(
            temple.name,
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildStatusBadge(temple.status),
              const SizedBox(width: 8),
              if (temple.isVerified)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.verified,
                        size: 14,
                        color: Colors.green.shade700,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Verified',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderIcon() {
    return Icon(Icons.temple_hindu, size: 50, color: Colors.white);
  }

  Widget _buildStatusBadge(status) {
    Color color;
    String text;

    switch (status.toString()) {
      case 'TempleStatus.pending':
        color = Colors.orange;
        text = 'Pending Approval';
        break;
      case 'TempleStatus.approved':
        color = Colors.green;
        text = 'Approved';
        break;
      case 'TempleStatus.active':
        color = Colors.blue;
        text = 'Active';
        break;
      case 'TempleStatus.suspended':
        color = Colors.red;
        text = 'Suspended';
        break;
      default:
        color = Colors.grey;
        text = 'Unknown';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildBasicInfo(temple) {
    return _buildSection('Basic Information', [
      _buildInfoRow('Temple Type', _getTypeLabel(temple.type)),
      _buildInfoRow('Description', temple.description),
      _buildInfoRow(
        'Registration Number',
        temple.registrationNumber ?? 'Not provided',
      ),
      _buildInfoRow('Manager', temple.managerName ?? 'Not specified'),
      if (temple.deities.isNotEmpty)
        _buildInfoRow('Deities', temple.deities.join(', ')),
    ]);
  }

  Widget _buildContactInfo(temple) {
    return _buildSection('Contact Information', [
      _buildInfoRow('Phone', temple.contact.phone),
      if (temple.contact.email != null)
        _buildInfoRow('Email', temple.contact.email!),
      if (temple.contact.website != null)
        _buildInfoRow('Website', temple.contact.website!),
    ]);
  }

  Widget _buildLocationInfo(temple) {
    return _buildSection('Location', [
      _buildInfoRow('Address', temple.location.address),
      _buildInfoRow('City', temple.location.city),
      _buildInfoRow('State', temple.location.state),
      _buildInfoRow('Pincode', temple.location.pincode),
      if (temple.location.latitude != 0 && temple.location.longitude != 0)
        _buildInfoRow(
          'Coordinates',
          '${temple.location.latitude.toStringAsFixed(6)}, ${temple.location.longitude.toStringAsFixed(6)}',
        ),
    ]);
  }

  Widget _buildServicesInfo(temple) {
    return _buildSection('Services', [
      _buildInfoRow('Darshan Services', '${temple.darshans.length} services'),
      _buildInfoRow('Seva Services', '${temple.sevas.length} services'),
      if (temple.facilities.isNotEmpty)
        _buildInfoRow('Facilities', temple.facilities.join(', ')),
      _buildInfoRow(
        'Rating',
        '${temple.rating.toStringAsFixed(1)} (${temple.totalReviews} reviews)',
      ),
    ]);
  }

  Widget _buildAdditionalInfo(temple) {
    return _buildSection('Additional Information', [
      if (temple.history != null) _buildInfoRow('History', temple.history!),
      if (temple.significance != null)
        _buildInfoRow('Significance', temple.significance!),
      if (temple.architecture != null)
        _buildInfoRow('Architecture', temple.architecture!),
      if (temple.festivals.isNotEmpty)
        _buildInfoRow('Festivals', temple.festivals.join(', ')),
      _buildInfoRow('Created', _formatDate(temple.createdAt)),
      _buildInfoRow('Last Updated', _formatDate(temple.updatedAt)),
    ]);
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => context.push('/temples/profile/edit'),
              icon: const Icon(Icons.edit),
              label: const Text('Edit Profile'),
              style: ElevatedButton.styleFrom(
                backgroundColor: TraditionalColors.templeOrange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => context.push('/temples/settings'),
              icon: const Icon(Icons.settings),
              label: const Text('Settings'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showLogoutDialog(context, ref),
              icon: const Icon(Icons.logout, color: Colors.red),
              label: const Text('Logout', style: TextStyle(color: Colors.red)),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: Colors.red),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(templeAuthProvider.notifier).logout();
              context.go('/temples/login');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  String _getTypeLabel(type) {
    switch (type.toString()) {
      case 'TempleType.ancient':
        return 'Ancient Temple';
      case 'TempleType.modern':
        return 'Modern Temple';
      case 'TempleType.heritage':
        return 'Heritage Temple';
      case 'TempleType.pilgrimage':
        return 'Pilgrimage Site';
      case 'TempleType.local':
        return 'Local Temple';
      default:
        return 'Unknown';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
