import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'app_state_provider.dart';

class AppProviderScope extends ConsumerWidget {
  final Widget child;

  const AppProviderScope({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Initialize app state
    ref.read(appStateProvider.notifier).loadState();

    return child;
  }
}
