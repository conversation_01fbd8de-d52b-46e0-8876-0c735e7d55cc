import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geolocator/geolocator.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';
import '../../../shared/utils/logger.dart';
import 'sos_alert_service.dart';
import '../../../shared/models/ride/ride_request_model.dart';

final _logger = getLogger('SafetyTrackingService');

/// Safety tracking intervals in minutes
enum SafetyInterval {
  oneMinute(1, '1 minute'),
  twoMinutes(2, '2 minutes'),
  threeMinutes(3, '3 minutes'),
  fiveMinutes(5, '5 minutes'),
  tenMinutes(10, '10 minutes');

  const SafetyInterval(this.minutes, this.displayName);
  final int minutes;
  final String displayName;
}

/// Safety tracking status
enum SafetyTrackingStatus {
  inactive,
  active,
  waitingForPin,
  warning,
  emergency,
}

/// Safety tracking configuration
class SafetyTrackingConfig {
  final bool isEnabled;
  final String pin;
  final SafetyInterval interval;
  final List<String> emergencyContacts;
  final bool notifyPolice;
  final bool notifyExecutors;

  const SafetyTrackingConfig({
    required this.isEnabled,
    required this.pin,
    required this.interval,
    this.emergencyContacts = const [],
    this.notifyPolice = true,
    this.notifyExecutors = true,
  });

  Map<String, dynamic> toJson() => {
    'isEnabled': isEnabled,
    'pin': pin,
    'interval': interval.minutes,
    'emergencyContacts': emergencyContacts,
    'notifyPolice': notifyPolice,
    'notifyExecutors': notifyExecutors,
  };

  factory SafetyTrackingConfig.fromJson(Map<String, dynamic> json) {
    return SafetyTrackingConfig(
      isEnabled: json['isEnabled'] ?? false,
      pin: json['pin'] ?? '',
      interval: SafetyInterval.values.firstWhere(
        (e) => e.minutes == (json['interval'] ?? 2),
        orElse: () => SafetyInterval.twoMinutes,
      ),
      emergencyContacts: List<String>.from(json['emergencyContacts'] ?? []),
      notifyPolice: json['notifyPolice'] ?? true,
      notifyExecutors: json['notifyExecutors'] ?? true,
    );
  }
}

/// Safety tracking session data
class SafetyTrackingSession {
  final String rideId;
  final String userId;
  final SafetyTrackingConfig config;
  final DateTime startTime;
  DateTime lastPinEntry;
  SafetyTrackingStatus status;
  int missedChecks;

  SafetyTrackingSession({
    required this.rideId,
    required this.userId,
    required this.config,
    required this.startTime,
    required this.lastPinEntry,
    this.status = SafetyTrackingStatus.active,
    this.missedChecks = 0,
  });

  Map<String, dynamic> toJson() => {
    'rideId': rideId,
    'userId': userId,
    'config': config.toJson(),
    'startTime': startTime.toIso8601String(),
    'lastPinEntry': lastPinEntry.toIso8601String(),
    'status': status.name,
    'missedChecks': missedChecks,
  };

  factory SafetyTrackingSession.fromJson(Map<String, dynamic> json) {
    return SafetyTrackingSession(
      rideId: json['rideId'],
      userId: json['userId'],
      config: SafetyTrackingConfig.fromJson(json['config']),
      startTime: DateTime.parse(json['startTime']),
      lastPinEntry: DateTime.parse(json['lastPinEntry']),
      status: SafetyTrackingStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SafetyTrackingStatus.active,
      ),
      missedChecks: json['missedChecks'] ?? 0,
    );
  }
}

/// Service for managing safety tracking during rides
class SafetyTrackingService extends ChangeNotifier {
  final DatabaseService _databaseService;
  final FlutterLocalNotificationsPlugin _localNotifications;
  final SOSAlertService _sosAlertService;

  // Collections
  static const String _safetySessionsCollection = 'safety_sessions';
  static const String _safetyConfigCollection = 'safety_configs';

  // Current session
  SafetyTrackingSession? _currentSession;
  Timer? _checkTimer;
  Timer? _warningTimer;

  // Notification channels
  static const String _safetyChannelId = 'safety_tracking';
  static const String _emergencyChannelId = 'emergency_alerts';

  SafetyTrackingService()
    : _databaseService = DatabaseService(DatabaseConfig.fromEnvironment()),
      _localNotifications = FlutterLocalNotificationsPlugin(),
      _sosAlertService = SOSAlertService() {
    _initializeNotifications();
  }

  // Getters
  SafetyTrackingSession? get currentSession => _currentSession;
  bool get isActive => _currentSession != null;
  SafetyTrackingStatus get status =>
      _currentSession?.status ?? SafetyTrackingStatus.inactive;

  /// Initialize notification channels
  Future<void> _initializeNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTap,
    );

    // Create notification channels for Android
    const safetyChannel = AndroidNotificationChannel(
      _safetyChannelId,
      'Safety Tracking',
      description: 'Safety check notifications during rides',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('safety_check'),
    );

    const emergencyChannel = AndroidNotificationChannel(
      _emergencyChannelId,
      'Emergency Alerts',
      description: 'Emergency SOS alerts',
      importance: Importance.max,
      sound: RawResourceAndroidNotificationSound('emergency_alert'),
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(safetyChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(emergencyChannel);
  }

  /// Handle notification tap
  void _onNotificationTap(NotificationResponse response) {
    final payload = response.payload;
    if (payload != null) {
      try {
        final data = json.decode(payload);
        final type = data['type'];

        if (type == 'safety_check') {
          // Navigate to PIN entry screen
          _logger.info('Safety check notification tapped');
        } else if (type == 'emergency_alert') {
          // Handle emergency alert tap
          _logger.info('Emergency alert notification tapped');
        }
      } catch (e) {
        _logger.severe('Error handling notification tap: $e');
      }
    }
  }

  /// Start safety tracking for a ride
  Future<bool> startSafetyTracking({
    required String rideId,
    required String userId,
    required SafetyTrackingConfig config,
  }) async {
    try {
      _logger.info('Starting safety tracking for ride: $rideId');

      // Stop any existing session
      await stopSafetyTracking();

      // Create new session
      final session = SafetyTrackingSession(
        rideId: rideId,
        userId: userId,
        config: config,
        startTime: DateTime.now(),
        lastPinEntry: DateTime.now(),
      );

      // Save session to database
      await _databaseService.create(_safetySessionsCollection, {
        'id': rideId,
        ...session.toJson(),
      });

      _currentSession = session;

      // Start the check timer
      _startCheckTimer();

      _logger.info('Safety tracking started successfully');
      notifyListeners();
      return true;
    } catch (e) {
      _logger.severe('Error starting safety tracking: $e');
      return false;
    }
  }

  /// Stop safety tracking
  Future<void> stopSafetyTracking() async {
    if (_currentSession == null) return;

    try {
      _logger.info(
        'Stopping safety tracking for ride: ${_currentSession!.rideId}',
      );

      // Cancel timers
      _checkTimer?.cancel();
      _warningTimer?.cancel();

      // Update session status
      _currentSession!.status = SafetyTrackingStatus.inactive;

      // Update in database
      await _databaseService
          .update(_safetySessionsCollection, _currentSession!.rideId, {
            'status': SafetyTrackingStatus.inactive.name,
            'endTime': DateTime.now().toIso8601String(),
          });

      // Clear notifications
      await _localNotifications.cancel(1001); // Safety check notification
      await _localNotifications.cancel(1002); // Warning notification

      _currentSession = null;
      _logger.info('Safety tracking stopped successfully');
      notifyListeners();
    } catch (e) {
      _logger.severe('Error stopping safety tracking: $e');
    }
  }

  /// Start the check timer
  void _startCheckTimer() {
    if (_currentSession == null) return;

    final intervalDuration = Duration(
      minutes: _currentSession!.config.interval.minutes,
    );

    _checkTimer = Timer.periodic(intervalDuration, (timer) {
      _performSafetyCheck();
    });

    _logger.info(
      'Safety check timer started with ${_currentSession!.config.interval.displayName} interval',
    );
  }

  /// Perform safety check
  Future<void> _performSafetyCheck() async {
    if (_currentSession == null) return;

    try {
      _logger.info(
        'Performing safety check for ride: ${_currentSession!.rideId}',
      );

      // Check if ride is still active
      final rideData = await _databaseService.find(
        'ride_requests',
        _currentSession!.rideId,
      );
      if (rideData == null) {
        await stopSafetyTracking();
        return;
      }

      final ride = RideRequestModel.fromJson(rideData);
      if (ride.status == RideStatus.completed ||
          ride.status == RideStatus.cancelled) {
        await stopSafetyTracking();
        return;
      }

      // Update session status
      _currentSession!.status = SafetyTrackingStatus.waitingForPin;
      notifyListeners();

      // Show safety check notification
      await _showSafetyCheckNotification();

      // Start warning timer (90 seconds grace period)
      _startWarningTimer();
    } catch (e) {
      _logger.severe('Error performing safety check: $e');
    }
  }

  /// Start warning timer
  void _startWarningTimer() {
    _warningTimer?.cancel();

    _warningTimer = Timer(const Duration(seconds: 90), () {
      _handleMissedCheck();
    });
  }

  /// Handle missed safety check
  Future<void> _handleMissedCheck() async {
    if (_currentSession == null) return;

    try {
      _logger.warning(
        'Safety check missed for ride: ${_currentSession!.rideId}',
      );

      _currentSession!.missedChecks++;
      _currentSession!.status = SafetyTrackingStatus.warning;

      // Show warning notification
      await _showWarningNotification();

      // Start emergency timer (30 seconds)
      Timer(const Duration(seconds: 30), () {
        _triggerEmergencyAlert();
      });

      notifyListeners();
    } catch (e) {
      _logger.severe('Error handling missed check: $e');
    }
  }

  /// Verify PIN entry
  Future<bool> verifyPin(String enteredPin) async {
    if (_currentSession == null) return false;

    try {
      if (enteredPin == _currentSession!.config.pin) {
        _logger.info('PIN verified successfully');

        // Cancel warning timer
        _warningTimer?.cancel();

        // Update session
        _currentSession!.lastPinEntry = DateTime.now();
        _currentSession!.status = SafetyTrackingStatus.active;
        _currentSession!.missedChecks = 0;

        // Update in database
        await _databaseService
            .update(_safetySessionsCollection, _currentSession!.rideId, {
              'lastPinEntry': DateTime.now().toIso8601String(),
              'status': SafetyTrackingStatus.active.name,
              'missedChecks': 0,
            });

        // Clear notifications
        await _localNotifications.cancel(1001);
        await _localNotifications.cancel(1002);

        notifyListeners();
        return true;
      } else {
        _logger.warning('Invalid PIN entered');
        return false;
      }
    } catch (e) {
      _logger.severe('Error verifying PIN: $e');
      return false;
    }
  }

  /// Trigger emergency alert
  Future<void> _triggerEmergencyAlert() async {
    if (_currentSession == null) return;

    try {
      _logger.severe(
        'Triggering emergency alert for ride: ${_currentSession!.rideId}',
      );

      _currentSession!.status = SafetyTrackingStatus.emergency;

      // Get current location
      Position? location;
      try {
        location = await Geolocator.getCurrentPosition(
          locationSettings: const LocationSettings(
            accuracy: LocationAccuracy.high,
            timeLimit: Duration(seconds: 10),
          ),
        );
      } catch (e) {
        _logger.warning('Could not get current location for emergency: $e');
      }

      // Create SOS alert for missed safety check
      await _sosAlertService.createSafetyCheckMissedAlert(
        rideId: _currentSession!.rideId,
        userId: _currentSession!.userId,
        missedChecks: _currentSession!.missedChecks,
      );

      // Send notifications
      if (_currentSession!.config.notifyExecutors) {
        await _notifyNearbyExecutors(location);
      }

      if (_currentSession!.config.notifyPolice) {
        await _notifyPolice(location);
      }

      // Notify emergency contacts
      for (final contact in _currentSession!.config.emergencyContacts) {
        await _notifyEmergencyContact(contact, location);
      }

      // Show emergency notification to user
      await _showEmergencyNotification();

      notifyListeners();
    } catch (e) {
      _logger.severe('Error triggering emergency alert: $e');
    }
  }

  /// Show safety check notification
  Future<void> _showSafetyCheckNotification() async {
    const androidDetails = AndroidNotificationDetails(
      _safetyChannelId,
      'Safety Check',
      channelDescription: 'Time for your safety check',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      autoCancel: false,
      ongoing: true,
      actions: [
        AndroidNotificationAction(
          'enter_pin',
          'Enter PIN',
          showsUserInterface: true,
        ),
      ],
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      interruptionLevel: InterruptionLevel.timeSensitive,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      1001,
      'Safety Check Required',
      'Please enter your 4-digit PIN to confirm you are safe',
      details,
      payload: json.encode({
        'type': 'safety_check',
        'rideId': _currentSession!.rideId,
      }),
    );
  }

  /// Show warning notification
  Future<void> _showWarningNotification() async {
    const androidDetails = AndroidNotificationDetails(
      _safetyChannelId,
      'Safety Warning',
      channelDescription: 'Safety check missed - please respond',
      importance: Importance.max,
      priority: Priority.max,
      showWhen: true,
      autoCancel: false,
      ongoing: true,
      color: Color(0xFFFF5722),
      actions: [
        AndroidNotificationAction(
          'enter_pin_urgent',
          'ENTER PIN NOW',
          showsUserInterface: true,
        ),
      ],
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      interruptionLevel: InterruptionLevel.critical,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      1002,
      '⚠️ SAFETY WARNING',
      'You missed your safety check! Enter PIN within 30 seconds or emergency alert will be sent.',
      details,
      payload: json.encode({
        'type': 'safety_warning',
        'rideId': _currentSession!.rideId,
      }),
    );
  }

  /// Show emergency notification
  Future<void> _showEmergencyNotification() async {
    const androidDetails = AndroidNotificationDetails(
      _emergencyChannelId,
      'Emergency Alert',
      channelDescription: 'Emergency SOS alert sent',
      importance: Importance.max,
      priority: Priority.max,
      showWhen: true,
      autoCancel: false,
      ongoing: true,
      color: Color(0xFFD32F2F),
      actions: [
        AndroidNotificationAction(
          'cancel_emergency',
          'I\'m Safe - Cancel Alert',
          showsUserInterface: true,
        ),
      ],
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      interruptionLevel: InterruptionLevel.critical,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      1003,
      '🚨 EMERGENCY ALERT SENT',
      'SOS alert has been sent to nearby executors and emergency contacts. Tap to cancel if you are safe.',
      details,
      payload: json.encode({
        'type': 'emergency_alert',
        'rideId': _currentSession!.rideId,
      }),
    );
  }

  /// Notify nearby executors
  Future<void> _notifyNearbyExecutors(Position? location) async {
    try {
      _logger.info('Notifying nearby executors of emergency');

      // Get nearby executors (within 5km radius)
      if (location != null) {
        // In a real implementation, this would query nearby executors
        // For now, we'll create a notification record
        await _databaseService.create('emergency_notifications', {
          'id':
              '${_currentSession!.rideId}_executors_${DateTime.now().millisecondsSinceEpoch}',
          'type': 'executor_emergency',
          'rideId': _currentSession!.rideId,
          'userId': _currentSession!.userId,
          'location': {
            'latitude': location.latitude,
            'longitude': location.longitude,
          },
          'message': 'Emergency: Passenger safety check failed during ride',
          'timestamp': DateTime.now().toIso8601String(),
          'status': 'sent',
        });
      }
    } catch (e) {
      _logger.severe('Error notifying nearby executors: $e');
    }
  }

  /// Notify police
  Future<void> _notifyPolice(Position? location) async {
    try {
      _logger.info('Notifying police of emergency');

      await _databaseService.create('emergency_notifications', {
        'id':
            '${_currentSession!.rideId}_police_${DateTime.now().millisecondsSinceEpoch}',
        'type': 'police_emergency',
        'rideId': _currentSession!.rideId,
        'userId': _currentSession!.userId,
        'location': location != null
            ? {'latitude': location.latitude, 'longitude': location.longitude}
            : null,
        'message':
            'Emergency: Passenger safety check failed during ride - immediate assistance required',
        'timestamp': DateTime.now().toIso8601String(),
        'status': 'sent',
      });
    } catch (e) {
      _logger.severe('Error notifying police: $e');
    }
  }

  /// Notify emergency contact
  Future<void> _notifyEmergencyContact(
    String contact,
    Position? location,
  ) async {
    try {
      _logger.info('Notifying emergency contact: $contact');

      await _databaseService.create('emergency_notifications', {
        'id':
            '${_currentSession!.rideId}_contact_${contact}_${DateTime.now().millisecondsSinceEpoch}',
        'type': 'emergency_contact',
        'rideId': _currentSession!.rideId,
        'userId': _currentSession!.userId,
        'contact': contact,
        'location': location != null
            ? {'latitude': location.latitude, 'longitude': location.longitude}
            : null,
        'message':
            'EMERGENCY: Your contact is in a ride and failed to respond to safety check. Location attached.',
        'timestamp': DateTime.now().toIso8601String(),
        'status': 'sent',
      });
    } catch (e) {
      _logger.severe('Error notifying emergency contact $contact: $e');
    }
  }

  /// Cancel emergency alert (if user responds after alert is sent)
  Future<void> cancelEmergencyAlert() async {
    if (_currentSession == null) return;

    try {
      _logger.info(
        'Cancelling emergency alert for ride: ${_currentSession!.rideId}',
      );

      // Update session status
      _currentSession!.status = SafetyTrackingStatus.active;
      _currentSession!.lastPinEntry = DateTime.now();

      // Update emergency alerts to cancelled
      // In a real implementation, this would update all related emergency notifications

      // Clear emergency notification
      await _localNotifications.cancel(1003);

      notifyListeners();
    } catch (e) {
      _logger.severe('Error cancelling emergency alert: $e');
    }
  }

  /// Get safety configuration for user
  Future<SafetyTrackingConfig?> getSafetyConfig(String userId) async {
    try {
      final configData = await _databaseService.find(
        _safetyConfigCollection,
        userId,
      );
      if (configData != null) {
        return SafetyTrackingConfig.fromJson(configData);
      }
      return null;
    } catch (e) {
      _logger.severe('Error getting safety config: $e');
      return null;
    }
  }

  /// Save safety configuration for user
  Future<void> saveSafetyConfig(
    String userId,
    SafetyTrackingConfig config,
  ) async {
    try {
      await _databaseService.update(_safetyConfigCollection, userId, {
        'id': userId,
        ...config.toJson(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      _logger.severe('Error saving safety config: $e');
    }
  }

  @override
  void dispose() {
    _checkTimer?.cancel();
    _warningTimer?.cancel();
    super.dispose();
  }
}
