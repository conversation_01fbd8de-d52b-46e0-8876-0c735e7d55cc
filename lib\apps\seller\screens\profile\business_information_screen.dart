import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shivish/apps/seller/application/seller_provider.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';

class BusinessInformationScreen extends ConsumerStatefulWidget {
  const BusinessInformationScreen({super.key});

  @override
  ConsumerState<BusinessInformationScreen> createState() =>
      _BusinessInformationScreenState();
}

class _BusinessInformationScreenState
    extends ConsumerState<BusinessInformationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _businessNameController = TextEditingController();
  final _businessDescriptionController = TextEditingController();
  final _businessAddressController = TextEditingController();
  final _gstNumberController = TextEditingController();
  final _panNumberController = TextEditingController();
  bool _isLoading = false;
  String? _error;
  File? _logoImage;
  final _imagePicker = ImagePicker();
  @override
  void initState() {
    super.initState();
    _loadBusinessInformation();
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _businessDescriptionController.dispose();
    _businessAddressController.dispose();
    _gstNumberController.dispose();
    _panNumberController.dispose();
    super.dispose();
  }

  Future<void> _loadBusinessInformation() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final sellerState = ref.read(sellerProvider);
      if (sellerState.hasValue && sellerState.value != null) {
        final seller = sellerState.value!;
        _businessNameController.text = seller.businessName;
        _businessDescriptionController.text = seller.businessDescription ?? '';

        // Get the first business address if available
        if (seller.businessAddress.isNotEmpty) {
          _businessAddressController.text = seller.businessAddress.first;
        }

        // Get GST and PAN numbers from payment settings if available
        if (seller.paymentSettings.containsKey('taxInfo')) {
          final taxInfo =
              seller.paymentSettings['taxInfo'] as Map<String, dynamic>?;
          if (taxInfo != null) {
            _gstNumberController.text = taxInfo['gstNumber'] as String? ?? '';
            _panNumberController.text = taxInfo['panNumber'] as String? ?? '';
          }
        }
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to load business information: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _pickImage() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 800,
      );

      if (pickedFile != null && mounted) {
        setState(() {
          _logoImage = File(pickedFile.path);
        });

        // Here you would typically upload the image to your storage service
        // and get back a URL to store in the seller's profile

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Logo updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking image: $e')),
        );
      }
    }
  }

  Future<void> _saveBusinessInformation() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final sellerState = ref.read(sellerProvider);
      if (sellerState.hasValue && sellerState.value != null) {
        final seller = sellerState.value!;

        // Create updated business addresses list
        List<String> updatedAddresses = List.from(seller.businessAddress);
        if (updatedAddresses.isEmpty) {
          updatedAddresses.add(_businessAddressController.text.trim());
        } else {
          updatedAddresses[0] = _businessAddressController.text.trim();
        }

        // Create updated payment settings with tax info
        Map<String, dynamic> updatedPaymentSettings =
            Map.from(seller.paymentSettings);
        updatedPaymentSettings['taxInfo'] = {
          'gstNumber': _gstNumberController.text.trim(),
          'panNumber': _panNumberController.text.trim(),
          'updatedAt': DateTime.now().toIso8601String(),
        };

        // If we have a new logo image, we would upload it here
        // For now, we'll just note that a logo was selected in the payment settings
        if (_logoImage != null) {
          // In a real implementation, you would upload the image to storage
          // and get back a URL to store
          updatedPaymentSettings['hasBusinessLogo'] = true;
        }

        final updatedSeller = seller.copyWith(
          businessName: _businessNameController.text.trim(),
          businessDescription: _businessDescriptionController.text.trim(),
          businessAddress: updatedAddresses,
          paymentSettings: updatedPaymentSettings,
          updatedAt: DateTime.now(),
        );

        await ref.read(sellerProvider.notifier).updateSeller(updatedSeller);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Business information updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to save business information: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save business information: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        appBar: AppToolbar(
          title: 'Business Information',
        ),
        body: Center(
          child: LoadingIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: const AppToolbar(
          title: 'Business Information',
        ),
        body: ErrorMessage(
          message: _error!,
          onRetry: _loadBusinessInformation,
        ),
      );
    }

    return Scaffold(
      appBar: const AppToolbar(
        title: 'Business Information',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Business Logo
              Center(
                child: Stack(
                  children: [
                    CircleAvatar(
                      radius: 60,
                      backgroundColor: Colors.grey[300],
                      backgroundImage: _logoImage != null
                          ? FileImage(_logoImage!)
                          : null,
                      child: _logoImage == null
                          ? const Icon(
                              Icons.business,
                              size: 80,
                              color: Colors.grey,
                            )
                          : null,
                    ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: CircleAvatar(
                        backgroundColor: Theme.of(context).primaryColor,
                        radius: 20,
                        child: IconButton(
                          icon: const Icon(
                            Icons.camera_alt,
                            color: Colors.white,
                          ),
                          onPressed: _pickImage,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Business Information
              const Text(
                'Business Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _businessNameController,
                label: 'Business Name',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your business name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _businessDescriptionController,
                label: 'Business Description',
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your business description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _businessAddressController,
                label: 'Business Address',
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your business address';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 32),

              // Tax Information
              const Text(
                'Tax Information',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _gstNumberController,
                label: 'GST Number',
                textCapitalization: TextCapitalization.characters,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!RegExp(
                            r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$')
                        .hasMatch(value)) {
                      return 'Please enter a valid GST number';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              AppTextField(
                controller: _panNumberController,
                label: 'PAN Number',
                textCapitalization: TextCapitalization.characters,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!RegExp(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$')
                        .hasMatch(value)) {
                      return 'Please enter a valid PAN number';
                    }
                  }
                  return null;
                },
              ),

              const SizedBox(height: 32),

              AppButton(
                onPressed: _saveBusinessInformation,
                child: const Text('Save Business Information'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
