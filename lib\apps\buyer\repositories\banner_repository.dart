import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../domain/entities/banner/banner_model.dart';
import 'banner_repository_impl.dart';

/// Repository interface for managing banners
abstract class BannerRepository {
  /// Get all active banners
  Future<List<BannerModel>> getActiveBanners();

  /// Get banner by ID
  Future<BannerModel?> getBannerById(String id);

  /// Get banners by action type
  Future<List<BannerModel>> getBannersByActionType(String actionType);

  /// Get banners by priority range
  Future<List<BannerModel>> getBannersByPriorityRange({
    required int minPriority,
    required int maxPriority,
  });
}

final bannerRepositoryProvider = Provider<BannerRepository>((ref) {
  return BannerRepositoryImpl();
});
