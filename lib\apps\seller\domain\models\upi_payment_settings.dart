import 'package:freezed_annotation/freezed_annotation.dart';

part 'upi_payment_settings.freezed.dart';
part 'upi_payment_settings.g.dart';

@freezed
sealed class UpiPaymentSettings with _$UpiPaymentSettings {
  const factory UpiPaymentSettings({
    required String upiId,
    @Default(false) bool isVerified,
    @Default(true) bool isEnabled,
    DateTime? verifiedAt,
    DateTime? updatedAt,
  }) = _UpiPaymentSettings;

  factory UpiPaymentSettings.fromJson(Map<String, dynamic> json) =>
      _$UpiPaymentSettingsFromJson(json);
}
