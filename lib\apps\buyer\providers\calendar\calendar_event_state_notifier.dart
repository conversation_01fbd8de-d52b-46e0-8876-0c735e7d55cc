import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/calendar/calendar_event_model.dart';
import '../../../../shared/models/calendar/calendar_event_type.dart';
import '../../../../shared/models/calendar/calendar_event_reminder.dart';
import '../../../../shared/models/calendar/calendar_event_recurrence.dart';
import '../../../../shared/services/calendar/calendar_event_service.dart';
import 'calendar_event_provider.dart';

/// State for calendar events
class CalendarEventState {
  /// Creates a [CalendarEventState]
  const CalendarEventState({
    this.events = const [],
    this.isLoading = false,
    this.error,
  });

  /// The list of calendar events
  final List<CalendarEventModel> events;

  /// Whether the events are being loaded
  final bool isLoading;

  /// The error message if any
  final String? error;

  /// Creates a copy of [CalendarEventState] with the given fields replaced with the new values
  CalendarEventState copyWith({
    List<CalendarEventModel>? events,
    bool? isLoading,
    String? error,
  }) {
    return CalendarEventState(
      events: events ?? this.events,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// Notifier for calendar event state
class CalendarEventStateNotifier extends StateNotifier<CalendarEventState> {
  /// Creates a [CalendarEventStateNotifier]
  CalendarEventStateNotifier(this._service) : super(const CalendarEventState());

  /// The calendar event service
  final CalendarEventService _service;

  /// Load calendar events for a user
  Future<void> loadEvents(String userId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final events = await _service.getEventsByUser(userId);
      state = state.copyWith(events: events, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Load calendar events by date range
  Future<void> loadEventsByDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final events = await _service.getEventsByDateRange(
        startDate,
        endDate,
        userId: userId,
      );
      state = state.copyWith(events: events, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Load calendar events by type
  Future<void> loadEventsByType(String userId, CalendarEventType type) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final events = await _service.getEventsByType(type, userId: userId);
      state = state.copyWith(events: events, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Create a new calendar event
  Future<void> createEvent(CalendarEventModel event) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final savedEvent = await _service.saveEvent(event);
      state = state.copyWith(
        events: [...state.events, savedEvent],
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Update a calendar event
  Future<void> updateEvent(CalendarEventModel event) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      await _service.saveEvent(event);
      state = state.copyWith(
        events: state.events.map((e) => e.id == event.id ? event : e).toList(),
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Delete a calendar event
  Future<void> deleteEvent(String eventId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      await _service.deleteEvent(eventId);
      state = state.copyWith(
        events: state.events.where((e) => e.id != eventId).toList(),
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Add a participant to a calendar event
  Future<void> addParticipant(String eventId, String userId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Get the event, add participant, and save it back
      final event = await _service.getEventById(eventId);
      if (event != null) {
        // For now, we'll just mark as successful since the CalendarEventService
        // doesn't have participant management yet
        state = state.copyWith(isLoading: false);
      } else {
        throw Exception('Event not found');
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Remove a participant from a calendar event
  Future<void> removeParticipant(String eventId, String userId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Get the event and remove participant (stub implementation)
      final event = await _service.getEventById(eventId);
      if (event == null) {
        throw Exception('Event not found');
      }
      // For now, we'll just mark as successful since the CalendarEventService
      // doesn't have participant management yet
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Update calendar event reminder
  Future<void> updateEventReminder(
    String eventId,
    CalendarEventReminder reminder,
  ) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Get the event and update reminder (stub implementation)
      final event = await _service.getEventById(eventId);
      if (event == null) {
        throw Exception('Event not found');
      }
      // For now, we'll just mark as successful since the CalendarEventService
      // doesn't have reminder management yet
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Remove calendar event reminder
  Future<void> removeEventReminder(String eventId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Get the event and remove reminder (stub implementation)
      final event = await _service.getEventById(eventId);
      if (event == null) {
        throw Exception('Event not found');
      }
      // For now, we'll just mark as successful since the CalendarEventService
      // doesn't have reminder management yet
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Update calendar event recurrence
  Future<void> updateEventRecurrence(
    String eventId,
    CalendarEventRecurrence recurrence,
  ) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Get the event and update recurrence (stub implementation)
      final event = await _service.getEventById(eventId);
      if (event == null) {
        throw Exception('Event not found');
      }
      // For now, we'll just mark as successful since the CalendarEventService
      // doesn't have recurrence management yet
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Remove calendar event recurrence
  Future<void> removeEventRecurrence(String eventId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Get the event and remove recurrence (stub implementation)
      final event = await _service.getEventById(eventId);
      if (event == null) {
        throw Exception('Event not found');
      }
      // For now, we'll just mark as successful since the CalendarEventService
      // doesn't have recurrence management yet
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }
}

/// Provider for the calendar event state notifier
final calendarEventStateNotifierProvider =
    StateNotifierProvider<CalendarEventStateNotifier, CalendarEventState>((
      ref,
    ) {
      final service = ref.watch(calendarEventServiceProvider);
      return CalendarEventStateNotifier(service);
    });
