import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shivish/apps/seller/domain/models/store_model.dart';
import 'package:shivish/apps/seller/domain/repositories/store_repository.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/services/storage/adaptive_storage_service.dart';

class StoreRepositoryImpl implements StoreRepository {
  final DatabaseService _databaseService;
  final AdaptiveStorageService _storageService;
  final AuthService _authService;

  StoreRepositoryImpl({
    required DatabaseService databaseService,
    required AdaptiveStorageService storageService,
    required AuthService authService,
  })  : _databaseService = databaseService,
        _storageService = storageService,
        _authService = authService;

  @override
  Future<StoreModel> getStore() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final doc = await _databaseService.find('stores', userId);

      if (doc == null) {
        throw Exception('Store not found');
      }

      return StoreModel.fromJson(doc);
    } catch (e) {
      debugPrint('Error getting store: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateStore(StoreModel store) async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Check if store exists, if not create it
      final existingStore = await _databaseService.find('stores', userId);
      if (existingStore == null) {
        await _databaseService.create('stores', store.toJson());
      } else {
        final data = store.toJson();
        data.remove('id'); // Remove ID from update data
        await _databaseService.update('stores', userId, data);
      }
    } catch (e) {
      debugPrint('Error updating store: $e');
      rethrow;
    }
  }

  @override
  Future<String> uploadLogo(XFile file) async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Read file bytes
      final fileBytes = await File(file.path).readAsBytes();
      final fileName = 'logo.${file.path.split('.').last}';

      // Upload to hybrid storage
      final downloadUrl = await _storageService.uploadData(
        data: fileBytes,
        fileName: fileName,
        storagePath: 'stores/$userId',
      );

      return downloadUrl;
    } catch (e) {
      debugPrint('Error uploading logo: $e');
      rethrow;
    }
  }


}
