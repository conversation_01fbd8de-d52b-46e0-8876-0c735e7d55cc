import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../../../shared/models/temple/temple_booking_model.dart';
import '../../../shared/services/temple/temple_booking_service.dart';
import '../../../shared/core/service_locator.dart';

/// Temple bookings state
@immutable
class TempleBookingsState {
  final List<TempleBooking> allBookings;
  final List<TempleBooking> todayBookings;
  final List<TempleBooking> tomorrowBookings;
  final List<TempleBooking> upcomingBookings;
  final List<TempleBooking> completedBookings;
  final bool isLoading;
  final String? error;

  const TempleBookingsState({
    this.allBookings = const [],
    this.todayBookings = const [],
    this.tomorrowBookings = const [],
    this.upcomingBookings = const [],
    this.completedBookings = const [],
    this.isLoading = false,
    this.error,
  });

  TempleBookingsState copyWith({
    List<TempleBooking>? allBookings,
    List<TempleBooking>? todayBookings,
    List<TempleBooking>? tomorrowBookings,
    List<TempleBooking>? upcomingBookings,
    List<TempleBooking>? completedBookings,
    bool? isLoading,
    String? error,
  }) {
    return TempleBookingsState(
      allBookings: allBookings ?? this.allBookings,
      todayBookings: todayBookings ?? this.todayBookings,
      tomorrowBookings: tomorrowBookings ?? this.tomorrowBookings,
      upcomingBookings: upcomingBookings ?? this.upcomingBookings,
      completedBookings: completedBookings ?? this.completedBookings,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Temple bookings notifier
class TempleBookingsNotifier extends StateNotifier<TempleBookingsState> {
  final TempleBookingService _bookingService;

  TempleBookingsNotifier(this._bookingService)
    : super(const TempleBookingsState());

  /// Load bookings for temple
  Future<void> loadBookings(String templeId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final allBookings = await _bookingService.getTempleBookings(templeId);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 1));

      // Filter bookings by date and status
      final todayBookings = allBookings.where((booking) {
        final visitDate = DateTime(
          booking.visitDate.year,
          booking.visitDate.month,
          booking.visitDate.day,
        );
        return visitDate.isAtSameMomentAs(today);
      }).toList();

      final tomorrowBookings = allBookings.where((booking) {
        final visitDate = DateTime(
          booking.visitDate.year,
          booking.visitDate.month,
          booking.visitDate.day,
        );
        return visitDate.isAtSameMomentAs(tomorrow) &&
            booking.status != TempleBookingStatus.completed &&
            booking.status != TempleBookingStatus.cancelled;
      }).toList();

      final upcomingBookings = allBookings.where((booking) {
        final visitDate = DateTime(
          booking.visitDate.year,
          booking.visitDate.month,
          booking.visitDate.day,
        );
        return visitDate.isAfter(tomorrow) &&
            booking.status != TempleBookingStatus.completed &&
            booking.status != TempleBookingStatus.cancelled;
      }).toList();

      final completedBookings = allBookings.where((booking) {
        return booking.status == TempleBookingStatus.completed;
      }).toList();

      state = state.copyWith(
        allBookings: allBookings,
        todayBookings: todayBookings,
        tomorrowBookings: tomorrowBookings,
        upcomingBookings: upcomingBookings,
        completedBookings: completedBookings,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  /// Update booking status
  Future<bool> updateBookingStatus(
    String bookingId,
    TempleBookingStatus status,
  ) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final success = await _bookingService.updateBookingStatus(
        bookingId,
        status,
      );

      if (success) {
        // Update the booking in all lists
        final updatedAllBookings = state.allBookings.map((booking) {
          if (booking.id == bookingId) {
            return booking.copyWith(status: status);
          }
          return booking;
        }).toList();

        // Recategorize bookings
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);

        final todayBookings = updatedAllBookings.where((booking) {
          final visitDate = DateTime(
            booking.visitDate.year,
            booking.visitDate.month,
            booking.visitDate.day,
          );
          return visitDate.isAtSameMomentAs(today);
        }).toList();

        final upcomingBookings = updatedAllBookings.where((booking) {
          final visitDate = DateTime(
            booking.visitDate.year,
            booking.visitDate.month,
            booking.visitDate.day,
          );
          return visitDate.isAfter(today) &&
              booking.status != TempleBookingStatus.completed &&
              booking.status != TempleBookingStatus.cancelled;
        }).toList();

        final completedBookings = updatedAllBookings.where((booking) {
          return booking.status == TempleBookingStatus.completed;
        }).toList();

        state = state.copyWith(
          allBookings: updatedAllBookings,
          todayBookings: todayBookings,
          upcomingBookings: upcomingBookings,
          completedBookings: completedBookings,
          isLoading: false,
        );

        return true;
      } else {
        state = state.copyWith(
          error: 'Failed to update booking status',
          isLoading: false,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Get booking by ID
  TempleBooking? getBookingById(String bookingId) {
    try {
      return state.allBookings.firstWhere((booking) => booking.id == bookingId);
    } catch (e) {
      return null;
    }
  }

  /// Cancel booking
  Future<bool> cancelBooking(String bookingId, String reason) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final success = await _bookingService.cancelBooking(bookingId, reason);

      if (success) {
        await updateBookingStatus(bookingId, TempleBookingStatus.cancelled);
        return true;
      } else {
        state = state.copyWith(
          error: 'Failed to cancel booking',
          isLoading: false,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      return false;
    }
  }

  /// Get bookings by date range
  List<TempleBooking> getBookingsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) {
    return state.allBookings.where((booking) {
      return booking.visitDate.isAfter(
            startDate.subtract(const Duration(days: 1)),
          ) &&
          booking.visitDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  /// Get bookings by status
  List<TempleBooking> getBookingsByStatus(TempleBookingStatus status) {
    return state.allBookings
        .where((booking) => booking.status == status)
        .toList();
  }

  /// Get revenue for date range
  double getRevenueForDateRange(DateTime startDate, DateTime endDate) {
    final bookings = getBookingsByDateRange(startDate, endDate);
    return bookings
        .where((booking) => booking.status == TempleBookingStatus.completed)
        .fold(0.0, (sum, booking) => sum + booking.totalAmount);
  }

  /// Get booking statistics
  Map<String, int> getBookingStatistics() {
    return {
      'total': state.allBookings.length,
      'today': state.todayBookings.length,
      'upcoming': state.upcomingBookings.length,
      'completed': state.completedBookings.length,
      'pending': state.allBookings
          .where((booking) => booking.status == TempleBookingStatus.pending)
          .length,
      'confirmed': state.allBookings
          .where((booking) => booking.status == TempleBookingStatus.confirmed)
          .length,
      'cancelled': state.allBookings
          .where((booking) => booking.status == TempleBookingStatus.cancelled)
          .length,
    };
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Temple bookings provider
final templeBookingsProvider =
    StateNotifierProvider<TempleBookingsNotifier, TempleBookingsState>((ref) {
      final bookingService = ref.watch(templeBookingServiceProvider);
      return TempleBookingsNotifier(bookingService);
    });

/// Temple booking service provider
final templeBookingServiceProvider = Provider<TempleBookingService>((ref) {
  return serviceLocator<TempleBookingService>();
});
