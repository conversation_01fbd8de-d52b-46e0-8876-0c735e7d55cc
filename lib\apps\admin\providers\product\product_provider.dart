import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/product/product_model.dart';

/// Provider for products
final productsProvider = FutureProvider<List<ProductModel>>((ref) async {
  // This would typically fetch products from a service
  // For now, we'll return a mock list
  await Future.delayed(const Duration(milliseconds: 500));
  return [
    ProductModel(
      id: '1',
      name: 'Rice',
      description: 'Premium quality basmati rice',
      price: 120.0,
      categoryId: 'cat1',
      sellerId: 'seller1',
      images: [],
      productType: ProductType.physical,
      productStatus: ProductStatus.approved,
      quantity: 100,
    ),
    ProductModel(
      id: '2',
      name: 'Wheat Flour',
      description: 'Organic whole wheat flour',
      price: 45.0,
      categoryId: 'cat1',
      sellerId: 'seller1',
      images: [],
      productType: ProductType.physical,
      productStatus: ProductStatus.approved,
      quantity: 100,
    ),
    ProductModel(
      id: '3',
      name: 'Sugar',
      description: 'Refined white sugar',
      price: 40.0,
      categoryId: 'cat1',
      sellerId: 'seller1',
      images: [],
      productType: ProductType.physical,
      productStatus: ProductStatus.approved,
      quantity: 100,
    ),
    ProductModel(
      id: '4',
      name: 'Cooking Oil',
      description: 'Pure sunflower oil',
      price: 110.0,
      categoryId: 'cat1',
      sellerId: 'seller1',
      images: [],
      productType: ProductType.physical,
      productStatus: ProductStatus.approved,
      quantity: 100,
    ),
    ProductModel(
      id: '5',
      name: 'Milk',
      description: 'Fresh cow milk',
      price: 25.0,
      categoryId: 'cat1',
      sellerId: 'seller1',
      images: [],
      productType: ProductType.physical,
      productStatus: ProductStatus.approved,
      quantity: 100,
    ),
  ];
});
