import 'package:freezed_annotation/freezed_annotation.dart';

part 'voice_command_config_model.freezed.dart';
part 'voice_command_config_model.g.dart';

@freezed
sealed class VoiceCommandConfigModel with _$VoiceCommandConfigModel {
  const factory VoiceCommandConfigModel({
    required bool enabled,
    required String wakeWord,
    required String language,
    required double sensitivity,
    required List<String> supportedCommands,
    required Map<String, String> commandMappings,
  }) = _VoiceCommandConfigModel;

  factory VoiceCommandConfigModel.fromJson(Map<String, dynamic> json) =>
      _$VoiceCommandConfigModelFromJson(json);
}
