import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import '../config/database_config.dart' as database_config;

/// Database Connection Interface
abstract class DatabaseConnection {
  Future<void> initialize();
  Future<void> close();
  Future<List<Map<String, dynamic>>> query(String sql, [List<dynamic>? parameters]);
  Future<int> execute(String sql, [List<dynamic>? parameters]);
  Future<Map<String, dynamic>> create(String table, Map<String, dynamic> data);
  Future<Map<String, dynamic>?> find(String table, String id);
  Future<List<Map<String, dynamic>>> getAll(String table, {String? where, List<dynamic>? whereParams, String? orderBy, int? limit, int? offset});
  Future<Map<String, dynamic>?> update(String table, String id, Map<String, dynamic> data);
  Future<bool> delete(String table, String id);
  Stream<List<Map<String, dynamic>>> watchCollection(String table, {String? where, List<dynamic>? whereParams, String? orderBy, int? limit, int? offset});
}

/// Database Transaction Interface
abstract class DatabaseTransaction {
  Future<void> commit();
  Future<void> rollback();
  Future<List<Map<String, dynamic>>> query(String sql, [List<dynamic>? parameters]);
  Future<int> execute(String sql, [List<dynamic>? parameters]);

  // Additional methods for repository compatibility
  Future<Map<String, dynamic>> create(String table, Map<String, dynamic> data);
  Future<Map<String, dynamic>?> find(String table, String id);
  Future<List<Map<String, dynamic>>> getAll(String table, {String? where, List<dynamic>? whereParams, String? orderBy, int? limit, int? offset});
  Future<Map<String, dynamic>?> update(String table, String id, Map<String, dynamic> data);
  Future<bool> delete(String table, String id);
}

/// Production-ready Database Service with Hybrid Architecture
@singleton
class DatabaseService implements DatabaseConnection {
  final database_config.DatabaseConfig _config;
  final List<database_config.DatabaseConfig> _hybridConfigs;

  // Connection state management
  bool _isInitialized = false;

  // Hybrid routing configuration
  bool _useHybridRouting = true;
  double _primaryTrafficPercentage = 0.7; // 70% to primary, 30% to secondary
  final Random _random = Random();

  // In-memory data store for development/testing (will be replaced with actual DB)
  final Map<String, List<Map<String, dynamic>>> _dataStore = {};
  final Map<String, StreamController<List<Map<String, dynamic>>>> _watchControllers = {};

  DatabaseService(this._config, [List<database_config.DatabaseConfig>? hybridConfigs])
      : _hybridConfigs = hybridConfigs ?? [];

  @override
  Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      // Validate configuration
      _validateConfig(_config);

      // Initialize hybrid configurations
      for (final config in _hybridConfigs) {
        _validateConfig(config);
      }

      // Initialize data store
      _initializeDataStore();

      _isInitialized = true;
      debugPrint('Database service initialized successfully');
      debugPrint('Primary server: ${_config.host}:${_config.port}');
      debugPrint('Hybrid servers: ${_hybridConfigs.length}');
      debugPrint('Hybrid routing: ${_useHybridRouting ? 'enabled' : 'disabled'}');
    } catch (e) {
      debugPrint('Failed to initialize database service: $e');
      rethrow;
    }
  }

  void _validateConfig(database_config.DatabaseConfig config) {
    if (config.host.isEmpty) {
      throw ArgumentError('Database host cannot be empty');
    }
    if (config.database.isEmpty) {
      throw ArgumentError('Database name cannot be empty');
    }
    if (config.username.isEmpty) {
      throw ArgumentError('Database username cannot be empty');
    }
  }

  void _initializeDataStore() {
    // Initialize common tables that might be used
    final commonTables = [
      'users', 'technicians', 'customers', 'orders', 'services',
      'appointments', 'reviews', 'notifications', 'payments',
      'inventory', 'locations', 'categories'
    ];

    for (final table in commonTables) {
      _dataStore[table] = [];
    }
  }

  @override
  Future<void> close() async {
    try {
      // Close all watch controllers
      for (final controller in _watchControllers.values) {
        await controller.close();
      }
      _watchControllers.clear();

      // Clear data store
      _dataStore.clear();

      _isInitialized = false;
      debugPrint('Database service closed successfully');
    } catch (e) {
      debugPrint('Error closing database service: $e');
    }
  }

  void _ensureInitialized() {
    if (!_isInitialized) {
      throw Exception('Database not initialized. Call initialize() first.');
    }
  }

  /// Determine which server to use based on hybrid routing strategy
  String _getTargetServer({bool forceRead = false}) {
    _ensureInitialized();

    // For read operations, use hybrid routing if available
    if (forceRead && _useHybridRouting && _hybridConfigs.isNotEmpty) {
      // Route based on traffic percentage
      if (_random.nextDouble() < _primaryTrafficPercentage) {
        return 'primary';
      } else {
        return 'hybrid_${_random.nextInt(_hybridConfigs.length)}';
      }
    }

    // For write operations or when hybrid routing is disabled, use primary
    return 'primary';
  }

  /// Generate unique ID for records
  String _generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomSuffix = _random.nextInt(999999).toString().padLeft(6, '0');
    return '${timestamp}_$randomSuffix';
  }

  /// Log database operation for monitoring
  void _logOperation(String operation, String table, {Map<String, dynamic>? data, String? id}) {
    if (_config.enableLogging) {
      final server = _getTargetServer();
      debugPrint('DB[$server]: $operation on $table${id != null ? ' (id: $id)' : ''}');
      if (data != null && data.isNotEmpty) {
        debugPrint('DB[$server]: Data keys: ${data.keys.join(', ')}');
      }
    }
  }

  /// Notify watchers of data changes
  void _notifyWatchers(String table) {
    final controller = _watchControllers[table];
    if (controller != null && !controller.isClosed) {
      final tableData = _dataStore[table] ?? [];
      controller.add(List<Map<String, dynamic>>.from(tableData));
    }
  }

  @override
  Future<List<Map<String, dynamic>>> query(String sql, [List<dynamic>? parameters]) async {
    _ensureInitialized();

    try {
      _logOperation('QUERY', 'raw_sql');

      // For now, return empty result as this is a raw SQL interface
      // In production, this would execute the actual SQL query
      debugPrint('Executing SQL query: $sql');
      if (parameters != null && parameters.isNotEmpty) {
        debugPrint('Query parameters: $parameters');
      }

      // This would be replaced with actual SQL execution
      return [];
    } catch (e) {
      debugPrint('Query execution failed: $e');
      debugPrint('SQL: $sql');
      debugPrint('Parameters: $parameters');
      rethrow;
    }
  }

  @override
  Future<int> execute(String sql, [List<dynamic>? parameters]) async {
    _ensureInitialized();

    try {
      _logOperation('EXECUTE', 'raw_sql');

      debugPrint('Executing SQL statement: $sql');
      if (parameters != null && parameters.isNotEmpty) {
        debugPrint('Statement parameters: $parameters');
      }

      // This would be replaced with actual SQL execution
      // Return 1 to indicate success for now
      return 1;
    } catch (e) {
      debugPrint('Statement execution failed: $e');
      debugPrint('SQL: $sql');
      debugPrint('Parameters: $parameters');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> create(String table, Map<String, dynamic> data) async {
    _ensureInitialized();

    try {
      // Ensure table exists
      _dataStore.putIfAbsent(table, () => []);

      // Generate ID and add timestamps
      final id = _generateId();
      final now = DateTime.now().toIso8601String();

      final dataWithMeta = {
        'id': id,
        ...data,
        'created_at': now,
        'updated_at': now,
      };

      // Add to data store
      _dataStore[table]!.add(dataWithMeta);

      _logOperation('CREATE', table, data: dataWithMeta, id: id);

      // Notify watchers
      _notifyWatchers(table);

      return dataWithMeta;
    } catch (e) {
      debugPrint('Create operation failed for table $table: $e');
      debugPrint('Data: $data');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>?> find(String table, String id) async {
    _ensureInitialized();

    try {
      // Ensure table exists
      final tableData = _dataStore[table];
      if (tableData == null) {
        _logOperation('FIND', table, id: id);
        return null;
      }

      // Find record by ID
      final record = tableData.firstWhere(
        (record) => record['id'] == id,
        orElse: () => <String, dynamic>{},
      );

      _logOperation('FIND', table, id: id);

      return record.isEmpty ? null : record;
    } catch (e) {
      debugPrint('Find operation failed for table $table, id $id: $e');
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAll(String table, {String? where, List<dynamic>? whereParams, String? orderBy, int? limit, int? offset}) async {
    _ensureInitialized();

    try {
      // Ensure table exists
      var tableData = _dataStore[table] ?? [];

      // Apply basic filtering (simplified WHERE clause support)
      if (where != null && where.isNotEmpty) {
        tableData = _applyWhereClause(tableData, where, whereParams);
      }

      // Apply ordering (simplified ORDER BY support)
      if (orderBy != null && orderBy.isNotEmpty) {
        tableData = _applyOrderBy(tableData, orderBy);
      }

      // Apply offset
      if (offset != null && offset > 0) {
        tableData = tableData.skip(offset).toList();
      }

      // Apply limit
      if (limit != null && limit > 0) {
        tableData = tableData.take(limit).toList();
      }

      _logOperation('GET_ALL', table);

      return List<Map<String, dynamic>>.from(tableData);
    } catch (e) {
      debugPrint('GetAll operation failed for table $table: $e');
      debugPrint('Where: $where, Params: $whereParams');
      rethrow;
    }
  }

  /// Apply simple WHERE clause filtering
  List<Map<String, dynamic>> _applyWhereClause(List<Map<String, dynamic>> data, String where, List<dynamic>? params) {
    // This is a simplified implementation
    // In production, you'd parse the WHERE clause properly
    if (where.contains('=') && params != null && params.isNotEmpty) {
      final parts = where.split('=');
      if (parts.length == 2) {
        final field = parts[0].trim();
        final value = params[0];
        return data.where((record) => record[field] == value).toList();
      }
    }
    return data;
  }

  /// Apply simple ORDER BY sorting
  List<Map<String, dynamic>> _applyOrderBy(List<Map<String, dynamic>> data, String orderBy) {
    // This is a simplified implementation
    final parts = orderBy.split(' ');
    final field = parts[0].trim();
    final ascending = parts.length < 2 || parts[1].trim().toUpperCase() != 'DESC';

    data.sort((a, b) {
      final aValue = a[field];
      final bValue = b[field];

      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return ascending ? -1 : 1;
      if (bValue == null) return ascending ? 1 : -1;

      final comparison = aValue.toString().compareTo(bValue.toString());
      return ascending ? comparison : -comparison;
    });

    return data;
  }

  @override
  Future<Map<String, dynamic>?> update(String table, String id, Map<String, dynamic> data) async {
    _ensureInitialized();

    try {
      // Ensure table exists
      final tableData = _dataStore[table];
      if (tableData == null) {
        _logOperation('UPDATE', table, id: id);
        return null;
      }

      // Find record index
      final recordIndex = tableData.indexWhere((record) => record['id'] == id);
      if (recordIndex == -1) {
        _logOperation('UPDATE', table, id: id);
        return null;
      }

      // Add updated timestamp
      final updatedData = {
        ...tableData[recordIndex],
        ...data,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Update record
      tableData[recordIndex] = updatedData;

      _logOperation('UPDATE', table, data: updatedData, id: id);

      // Notify watchers
      _notifyWatchers(table);

      return updatedData;
    } catch (e) {
      debugPrint('Update operation failed for table $table, id $id: $e');
      debugPrint('Data: $data');
      rethrow;
    }
  }

  @override
  Future<bool> delete(String table, String id) async {
    _ensureInitialized();

    try {
      // Ensure table exists
      final tableData = _dataStore[table];
      if (tableData == null) {
        _logOperation('DELETE', table, id: id);
        return false;
      }

      // Find and remove record
      final recordIndex = tableData.indexWhere((record) => record['id'] == id);
      if (recordIndex == -1) {
        _logOperation('DELETE', table, id: id);
        return false;
      }

      tableData.removeAt(recordIndex);

      _logOperation('DELETE', table, id: id);

      // Notify watchers
      _notifyWatchers(table);

      return true;
    } catch (e) {
      debugPrint('Delete operation failed for table $table, id $id: $e');
      rethrow;
    }
  }

  @override
  Stream<List<Map<String, dynamic>>> watchCollection(String table, {String? where, List<dynamic>? whereParams, String? orderBy, int? limit, int? offset}) {
    _ensureInitialized();

    // Create or get existing stream controller for this table
    final controller = _watchControllers.putIfAbsent(
      table,
      () => StreamController<List<Map<String, dynamic>>>.broadcast(),
    );

    // Send initial data
    Future.microtask(() async {
      try {
        final data = await getAll(table, where: where, whereParams: whereParams, orderBy: orderBy, limit: limit, offset: offset);
        if (!controller.isClosed) {
          controller.add(data);
        }
      } catch (e) {
        debugPrint('Watch collection initial data error for $table: $e');
        if (!controller.isClosed) {
          controller.add(<Map<String, dynamic>>[]);
        }
      }
    });

    return controller.stream;
  }

  bool get isOpen => _isInitialized;

  // Additional methods needed by repositories
  Future<int> count(String table, {String? where, List<dynamic>? whereParams}) async {
    _ensureInitialized();

    try {
      // Get all data and apply filtering
      var tableData = _dataStore[table] ?? [];

      // Apply basic filtering if specified
      if (where != null && where.isNotEmpty) {
        tableData = _applyWhereClause(tableData, where, whereParams);
      }

      _logOperation('COUNT', table);

      return tableData.length;
    } catch (e) {
      debugPrint('Count operation failed for table $table: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> search(String table, String query, List<String> fields, {int? limit, int? offset}) async {
    _ensureInitialized();

    try {
      // Get all data from table
      var tableData = _dataStore[table] ?? [];

      // Perform case-insensitive search across specified fields
      final searchQuery = query.toLowerCase();
      tableData = tableData.where((record) {
        return fields.any((field) {
          final fieldValue = record[field]?.toString().toLowerCase() ?? '';
          return fieldValue.contains(searchQuery);
        });
      }).toList();

      // Apply offset
      if (offset != null && offset > 0) {
        tableData = tableData.skip(offset).toList();
      }

      // Apply limit
      if (limit != null && limit > 0) {
        tableData = tableData.take(limit).toList();
      }

      _logOperation('SEARCH', table);

      return List<Map<String, dynamic>>.from(tableData);
    } catch (e) {
      debugPrint('Search operation failed for table $table: $e');
      debugPrint('Query: $query, Fields: $fields');
      rethrow;
    }
  }

  Future<T> executeInTransaction<T>(Future<T> Function(DatabaseTransaction) operation) async {
    _ensureInitialized();

    try {
      final transaction = InMemoryTransaction(this);

      final result = await operation(transaction);

      await transaction.commit();
      return result;
    } catch (e) {
      debugPrint('Transaction failed: $e');
      rethrow;
    }
  }

  Future<DatabaseTransaction> beginTransaction() async {
    _ensureInitialized();

    return InMemoryTransaction(this);
  }

  // Additional method needed by delivery tracking provider
  Stream<Map<String, dynamic>?> watchRecord(String table, String id) {
    _ensureInitialized();

    // Create a stream that emits when the specific record changes
    return Stream.periodic(const Duration(seconds: 2), (_) async {
      try {
        return await find(table, id);
      } catch (e) {
        debugPrint('Watch record error for $table/$id: $e');
        return null;
      }
    }).asyncMap((future) => future).distinct();
  }

  // Hybrid routing configuration methods
  void setHybridRouting(bool enabled) {
    _useHybridRouting = enabled;
    debugPrint('Hybrid routing ${enabled ? 'enabled' : 'disabled'}');
  }

  void setPrimaryTrafficPercentage(double percentage) {
    _primaryTrafficPercentage = percentage.clamp(0.0, 1.0);
    debugPrint('Primary traffic percentage set to ${(_primaryTrafficPercentage * 100).toStringAsFixed(1)}%');
  }

  // Health check methods
  Future<bool> isHealthy() async {
    try {
      return _isInitialized;
    } catch (e) {
      debugPrint('Health check failed: $e');
      return false;
    }
  }

  Future<Map<String, bool>> getConnectionsHealth() async {
    final health = <String, bool>{};

    health['primary'] = _isInitialized;
    health['secondary'] = _isInitialized && _hybridConfigs.isNotEmpty;

    return health;
  }

  // Data store management methods
  void clearTable(String table) {
    _ensureInitialized();
    _dataStore[table]?.clear();
    _notifyWatchers(table);
    _logOperation('CLEAR', table);
  }

  void clearAllTables() {
    _ensureInitialized();
    for (final table in _dataStore.keys) {
      _dataStore[table]?.clear();
      _notifyWatchers(table);
    }
    debugPrint('All tables cleared');
  }

  Map<String, int> getTableSizes() {
    _ensureInitialized();
    return _dataStore.map((table, data) => MapEntry(table, data.length));
  }
}

/// Production-ready In-Memory transaction implementation
class InMemoryTransaction implements DatabaseTransaction {
  final DatabaseService _service;
  bool _isCommitted = false;
  bool _isRolledBack = false;

  InMemoryTransaction(this._service);

  void _ensureActive() {
    if (_isCommitted) {
      throw Exception('Transaction has already been committed');
    }
    if (_isRolledBack) {
      throw Exception('Transaction has already been rolled back');
    }
  }

  @override
  Future<void> commit() async {
    _ensureActive();
    try {
      _isCommitted = true;
      debugPrint('Transaction committed successfully');
    } catch (e) {
      debugPrint('Transaction commit failed: $e');
      rethrow;
    }
  }

  @override
  Future<void> rollback() async {
    _ensureActive();
    try {
      // In a real implementation, you would reverse the operations
      // For now, just mark as rolled back
      _isRolledBack = true;
      debugPrint('Transaction rolled back successfully');
    } catch (e) {
      debugPrint('Transaction rollback failed: $e');
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> query(String sql, [List<dynamic>? parameters]) async {
    _ensureActive();
    try {
      // Delegate to service query method
      return await _service.query(sql, parameters);
    } catch (e) {
      debugPrint('Transaction query failed: $e');
      debugPrint('SQL: $sql');
      debugPrint('Parameters: $parameters');
      rethrow;
    }
  }

  @override
  Future<int> execute(String sql, [List<dynamic>? parameters]) async {
    _ensureActive();
    try {
      // Delegate to service execute method
      return await _service.execute(sql, parameters);
    } catch (e) {
      debugPrint('Transaction execute failed: $e');
      debugPrint('SQL: $sql');
      debugPrint('Parameters: $parameters');
      rethrow;
    }
  }



  @override
  Future<Map<String, dynamic>> create(String table, Map<String, dynamic> data) async {
    _ensureActive();
    try {
      // Delegate to service create method
      return await _service.create(table, data);
    } catch (e) {
      debugPrint('Transaction create failed for table $table: $e');
      debugPrint('Data: $data');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>?> find(String table, String id) async {
    _ensureActive();
    try {
      // Delegate to service find method
      return await _service.find(table, id);
    } catch (e) {
      debugPrint('Transaction find failed for table $table, id $id: $e');
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAll(String table, {String? where, List<dynamic>? whereParams, String? orderBy, int? limit, int? offset}) async {
    _ensureActive();
    try {
      // Delegate to service getAll method
      return await _service.getAll(table, where: where, whereParams: whereParams, orderBy: orderBy, limit: limit, offset: offset);
    } catch (e) {
      debugPrint('Transaction getAll failed for table $table: $e');
      debugPrint('Where: $where, Params: $whereParams');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>?> update(String table, String id, Map<String, dynamic> data) async {
    _ensureActive();
    try {
      // Delegate to service update method
      return await _service.update(table, id, data);
    } catch (e) {
      debugPrint('Transaction update failed for table $table, id $id: $e');
      debugPrint('Data: $data');
      rethrow;
    }
  }

  @override
  Future<bool> delete(String table, String id) async {
    _ensureActive();
    try {
      // Delegate to service delete method
      return await _service.delete(table, id);
    } catch (e) {
      debugPrint('Transaction delete failed for table $table, id $id: $e');
      rethrow;
    }
  }
}