import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/refund.dart';

part 'refund_state.freezed.dart';

@freezed
sealed class RefundState with _$RefundState {
  const factory RefundState.initial() = Initial;
  const factory RefundState.loading() = Loading;
  const factory RefundState.loaded(List<Refund> refunds) = Loaded;
  const factory RefundState.error(String message) = Error;
}
