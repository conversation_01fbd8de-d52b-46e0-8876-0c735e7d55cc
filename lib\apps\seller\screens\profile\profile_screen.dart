import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/seller/application/seller_provider.dart';
import 'package:shivish/apps/seller/presentation/widgets/seller_qr_dialog.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/apps/seller/screens/profile/business_profile_setup_screen.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/apps/seller/seller_routes.dart';

class SellerProfileScreen extends ConsumerStatefulWidget {
  const SellerProfileScreen({super.key});

  @override
  ConsumerState<SellerProfileScreen> createState() =>
      _SellerProfileScreenState();
}

class _SellerProfileScreenState extends ConsumerState<SellerProfileScreen> {
  @override
  void initState() {
    super.initState();
    // We'll load the seller data in a post-frame callback to avoid
    // modifying the provider during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSellerData();
    });
  }

  Future<void> _loadSellerData() async {
    try {
      final authService = AuthService();
      final user = await authService.getCurrentUser();

      if (user == null) {
        debugPrint('ProfileScreen: No authenticated user found');
        return;
      }

      if (!mounted) return;

      debugPrint('ProfileScreen: Loading seller data for user: ${user.id}');

      // Use a try-catch to prevent errors from propagating to the UI
      try {
        final seller =
            await ref.read(sellerProvider.notifier).getSeller(user.id);

        if (seller == null && mounted) {
          debugPrint('ProfileScreen: Seller is still null after getSeller');
          // Show an error message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to load seller profile. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        } else {
          debugPrint(
              'ProfileScreen: Seller loaded successfully: ${seller?.businessName}');
        }
      } catch (e) {
        debugPrint('ProfileScreen: Error loading seller data: $e');
        if (mounted) {
          // Show an error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading profile: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('ProfileScreen: Error getting current user: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(sellerProvider);

    if (state.isLoading) {
      return const Scaffold(
        body: Center(
          child: LoadingIndicator(),
        ),
      );
    }

    if (state.value == null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Seller profile not found'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadSellerData,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    final seller = state.value!;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(SellerRoutes.home),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.qr_code),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => SellerQRDialog(seller: seller),
              );
            },
            tooltip: 'Show QR Code',
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BusinessProfileSetupScreen(),
                ),
              );
            },
            tooltip: 'Edit Profile',
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: 4, // Profile tab
        onTap: (index) {
          switch (index) {
            case 0:
              context.go(SellerRoutes.home);
              break;
            case 1:
              context.go(SellerRoutes.products);
              break;
            case 2:
              context.go(SellerRoutes.orders);
              break;
            case 3:
              context.go(SellerRoutes.analyticsDashboard);
              break;
            case 4:
              // Already on profile
              break;
          }
        },
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory),
            label: 'Products',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: 'Orders',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Analytics',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Stack(
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundImage: seller.businessLogo != null
                        ? NetworkImage(seller.businessLogo!)
                        : null,
                    child: seller.businessLogo == null
                        ? Text(
                            seller.businessName[0].toUpperCase(),
                            style: const TextStyle(fontSize: 32),
                          )
                        : null,
                  ),
                  if (seller.isApproved)
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.verified,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Center(
              child: Text(
                seller.businessName,
                style: Theme.of(context).textTheme.headlineSmall,
              ),
            ),
            const SizedBox(height: 8),
            Center(
              child: Text(
                seller.category.displayName,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.grey,
                    ),
              ),
            ),
            const SizedBox(height: 24),
            _buildInfoCard(
              context,
              title: 'Business Information',
              children: [
                _buildInfoRow(
                  context,
                  icon: Icons.email,
                  label: 'Email',
                  value: seller.email,
                ),
                if (seller.phoneNumber != null)
                  _buildInfoRow(
                    context,
                    icon: Icons.phone,
                    label: 'Phone',
                    value: seller.phoneNumber!,
                  ),
                if (seller.businessDescription != null)
                  _buildInfoRow(
                    context,
                    icon: Icons.info,
                    label: 'Description',
                    value: seller.businessDescription!,
                  ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoCard(
              context,
              title: 'Performance',
              children: [
                _buildInfoRow(
                  context,
                  icon: Icons.star,
                  label: 'Rating',
                  value: '${seller.rating} (${seller.totalReviews} reviews)',
                ),
                _buildInfoRow(
                  context,
                  icon: Icons.shopping_bag,
                  label: 'Total Orders',
                  value: seller.totalOrders.toString(),
                ),
                _buildInfoRow(
                  context,
                  icon: Icons.inventory,
                  label: 'Total Products',
                  value: seller.totalProducts.toString(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(
    BuildContext context, {
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey,
                      ),
                ),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
