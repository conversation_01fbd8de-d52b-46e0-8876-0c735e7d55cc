import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/models/product/product_model.dart';
import 'package:shivish/shared/services/product/product_service.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/apps/seller/screens/products/product_details_screen.dart';

class ProductApprovalStatusScreen extends ConsumerStatefulWidget {
  const ProductApprovalStatusScreen({super.key});

  @override
  ConsumerState<ProductApprovalStatusScreen> createState() =>
      _ProductApprovalStatusScreenState();
}

class _ProductApprovalStatusScreenState
    extends ConsumerState<ProductApprovalStatusScreen> {
  ProductStatus _selectedStatus = ProductStatus.pending;
  String _searchQuery = '';
  bool _isLoading = true;
  String? _error;
  List<ProductModel> _products = [];

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  Future<void> _loadProducts() async {
    try {
      final productsStream = ref.read(productServiceProvider).getProducts();
      final products = await productsStream.first;
      if (mounted) {
        setState(() {
          _products = products;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  List<ProductModel> get _filteredProducts {
    return _products.where((product) {
      final matchesStatus = product.productStatus == _selectedStatus;
      final matchesSearch =
          product.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              product.description
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase());
      return matchesStatus && matchesSearch;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: LoadingIndicator(),
      );
    }

    if (_error != null) {
      return Scaffold(
        body: ErrorMessage(
          message: _error!,
          onRetry: _loadProducts,
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Product Approval Status'),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Search Products',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                SegmentedButton<ProductStatus>(
                  segments: const [
                    ButtonSegment(
                      value: ProductStatus.pending,
                      label: Text('Pending'),
                    ),
                    ButtonSegment(
                      value: ProductStatus.approved,
                      label: Text('Approved'),
                    ),
                    ButtonSegment(
                      value: ProductStatus.rejected,
                      label: Text('Rejected'),
                    ),
                  ],
                  selected: {_selectedStatus},
                  onSelectionChanged: (value) {
                    setState(() {
                      _selectedStatus = value.first;
                    });
                  },
                ),
              ],
            ),
          ),
          Expanded(
            child: _filteredProducts.isEmpty
                ? Center(
                    child: Text(
                      'No products found',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredProducts.length,
                    itemBuilder: (context, index) {
                      final product = _filteredProducts[index];
                      return Card(
                        child: ListTile(
                          leading: product.images.isNotEmpty
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    product.images.first,
                                    width: 56,
                                    height: 56,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        width: 56,
                                        height: 56,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .surfaceContainerHighest,
                                        child: Icon(
                                          Icons.error_outline,
                                          size: 24,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .error,
                                        ),
                                      );
                                    },
                                  ),
                                )
                              : Container(
                                  width: 56,
                                  height: 56,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .surfaceContainerHighest,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.image_not_supported,
                                    size: 24,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                          title: Text(product.name),
                          subtitle: Text(
                            '₹${product.price.toStringAsFixed(2)} • ${product.quantity} in stock',
                          ),
                          trailing: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getStatusColor(product.productStatus)
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              product.productStatus.name.toUpperCase(),
                              style: TextStyle(
                                color: _getStatusColor(product.productStatus),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ProductDetailsScreen(
                                  productId: product.id,
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(ProductStatus status) {
    switch (status) {
      case ProductStatus.pending:
        return Colors.orange;
      case ProductStatus.approved:
        return Colors.green;
      case ProductStatus.rejected:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
