import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final holidaySettingsProvider =
    AsyncNotifierProvider<HolidaySettingsNotifier, Set<DateTime>>(() {
  return HolidaySettingsNotifier();
});

class HolidaySettingsNotifier extends AsyncNotifier<Set<DateTime>> {
  late final DatabaseService _databaseService;

  @override
  Future<Set<DateTime>> build() async {
    _databaseService = ref.read(databaseServiceProvider);
    return _loadHolidays();
  }

  Future<Set<DateTime>> _loadHolidays() async {
    final user = ref.read(authProvider);
    if (user == null) throw Exception('User not authenticated');

    try {
      final userData = await _databaseService.find('users', user.id);
      final holidays = userData?['holidays'] as List<dynamic>?;
      return holidays?.map((dateStr) => DateTime.parse(dateStr as String)).toSet() ??
          {};
    } catch (e) {
      debugPrint('Failed to load holidays: $e');
      throw Exception('Failed to load holidays: $e');
    }
  }

  Future<void> saveHolidays(Set<DateTime> holidays) async {
    state = const AsyncLoading();

    try {
      final user = ref.read(authProvider);
      if (user == null) throw Exception('User not authenticated');

      final holidayStrings =
          holidays.map((date) => date.toIso8601String()).toList();

      await _databaseService.update('users', user.id, {
        'holidays': holidayStrings,
        'updated_at': DateTime.now().toIso8601String(),
      });

      state = AsyncData(holidays);
    } catch (e) {
      debugPrint('Failed to save holidays: $e');
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }
}
