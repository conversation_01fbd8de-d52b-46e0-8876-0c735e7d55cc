import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/shared/models/hospital/doctor_model.dart';
import 'package:shivish/shared/models/hospital/doctor_specialty.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/providers/hospital/hospital_provider.dart';

class DoctorsScreen extends ConsumerStatefulWidget {
  final String? specialty;
  final String? query;

  const DoctorsScreen({
    super.key,
    this.specialty,
    this.query,
  });

  @override
  ConsumerState<DoctorsScreen> createState() => _DoctorsScreenState();
}

class _DoctorsScreenState extends ConsumerState<DoctorsScreen> {
  final _searchController = TextEditingController();
  String? _searchQuery;
  List<DoctorSpecialty>? _selectedSpecialties;
  String? _selectedSortOption;
  RangeValues _priceRange = const RangeValues(0, 5000);
  final double _maxPrice = 5000;

  @override
  void initState() {
    super.initState();
    _searchQuery = widget.query;
    if (_searchQuery != null) {
      _searchController.text = _searchQuery!;
    }

    if (widget.specialty != null) {
      _selectedSpecialties = [_getSpecialtyFromString(widget.specialty!)];
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final doctorsAsync = ref.watch(doctorsProvider(null));
    return doctorsAsync.when(
      data: (doctors) {
        // Filter doctors based on search query, selected specialties, and price range
        final filteredDoctors = doctors.where((doctor) {
          // Filter by search query
          final searchMatches = _searchQuery == null || _searchQuery!.isEmpty ||
              doctor.name.toLowerCase().contains(_searchQuery!.toLowerCase()) ||
              doctor.designation.toLowerCase().contains(_searchQuery!.toLowerCase());

          // Filter by specialties
          final specialtyMatches = _selectedSpecialties == null || _selectedSpecialties!.isEmpty ||
              doctor.specialties.any((specialty) => _selectedSpecialties!.contains(specialty));

          // Filter by price range
          final priceMatches = doctor.consultationFee >= _priceRange.start &&
              doctor.consultationFee <= _priceRange.end;

          return searchMatches && specialtyMatches && priceMatches;
        }).toList();

        // Sort doctors
        if (_selectedSortOption != null) {
          switch (_selectedSortOption) {
            case 'price_low_high':
              filteredDoctors.sort((a, b) => a.consultationFee.compareTo(b.consultationFee));
              break;
            case 'price_high_low':
              filteredDoctors.sort((a, b) => b.consultationFee.compareTo(a.consultationFee));
              break;
            case 'rating':
              filteredDoctors.sort((a, b) => b.rating.compareTo(a.rating));
              break;
            case 'experience':
              filteredDoctors.sort((a, b) => b.experienceYears.compareTo(a.experienceYears));
              break;
          }
        }

        return Scaffold(
          appBar: AppBar(
            title: const Text('Find Doctors'),
            actions: [
              IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: _showFilterDialog,
              ),
            ],
          ),
          body: Column(
            children: [
              // Search bar
              Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search doctors, specialties...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  onSubmitted: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),

              // Filter chips for selected specialties
              if (_selectedSpecialties != null && _selectedSpecialties!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _selectedSpecialties!.map((specialty) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: Chip(
                            label: Text(_getSpecialtyName(specialty)),
                            onDeleted: () {
                              setState(() {
                                _selectedSpecialties!.remove(specialty);
                                if (_selectedSpecialties!.isEmpty) {
                                  _selectedSpecialties = null;
                                }
                              });
                            },
                            backgroundColor: Colors.blue.shade100,
                            deleteIconColor: Colors.blue.shade700,
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),

              // Sort dropdown
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    const Text(
                      'Sort by:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    DropdownButton<String>(
                      value: _selectedSortOption,
                      hint: const Text('Relevance'),
                      underline: Container(),
                      items: [
                        DropdownMenuItem(
                          value: 'price_low_high',
                          child: Text('Price: Low to High'),
                        ),
                        DropdownMenuItem(
                          value: 'price_high_low',
                          child: Text('Price: High to Low'),
                        ),
                        DropdownMenuItem(
                          value: 'rating',
                          child: Text('Rating'),
                        ),
                        DropdownMenuItem(
                          value: 'experience',
                          child: Text('Experience'),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedSortOption = value;
                        });
                      },
                    ),
                  ],
                ),
              ),

              // Doctor list
              Expanded(
                child: filteredDoctors.isEmpty
                    ? _buildEmptyState()
                    : _buildDoctorList(filteredDoctors),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorMessage(message: 'Failed to load doctors: $error'),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No doctors found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your filters or search query',
            style: TextStyle(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _searchQuery = null;
                _searchController.clear();
                _selectedSpecialties = null;
                _priceRange = RangeValues(0, _maxPrice);
                _selectedSortOption = null;
              });
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Reset Filters'),
          ),
        ],
      ),
    );
  }

  Widget _buildDoctorList(List<DoctorModel> doctors) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: doctors.length,
      itemBuilder: (context, index) {
        final doctor = doctors[index];
        return _buildDoctorCard(doctor);
      },
    );
  }

  Widget _buildDoctorCard(DoctorModel doctor) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          context.push('/buyer/healthcare/doctors/${doctor.id}');
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Doctor image
                  CircleAvatar(
                    radius: 40,
                    backgroundImage: doctor.profileImage.isNotEmpty
                        ? NetworkImage(doctor.profileImage)
                        : null,
                    child: doctor.profileImage.isEmpty
                        ? const Icon(Icons.person, size: 40)
                        : null,
                  ),
                  const SizedBox(width: 16),

                  // Doctor details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Dr. ${doctor.name}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          doctor.designation,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: Colors.amber[700],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${doctor.rating} (${doctor.totalReviews})',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.work,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${doctor.experienceYears} years experience',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Specialties
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: doctor.specialties.map((specialty) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue.shade100),
                    ),
                    child: Text(
                      _getSpecialtyName(specialty),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),

              // Consultation fee and book button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Consultation Fee',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        '₹${doctor.consultationFee.toStringAsFixed(0)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                  ElevatedButton(
                    onPressed: () {
                      context.push('/buyer/healthcare/book-appointment/${doctor.id}');
                    },
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    child: const Text('Book Appointment'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              padding: const EdgeInsets.all(16),
              height: MediaQuery.of(context).size.height * 0.8,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Filter Doctors',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  ),
                  const Divider(),
                  const SizedBox(height: 16),

                  // Specialties
                  const Text(
                    'Specialties',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: DoctorSpecialty.values.map((specialty) {
                      final isSelected = _selectedSpecialties?.contains(specialty) ?? false;
                      return FilterChip(
                        label: Text(_getSpecialtyName(specialty)),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedSpecialties ??= [];

                            if (selected) {
                              _selectedSpecialties!.add(specialty);
                            } else {
                              _selectedSpecialties!.remove(specialty);
                              if (_selectedSpecialties!.isEmpty) {
                                _selectedSpecialties = null;
                              }
                            }
                          });
                        },
                        backgroundColor: Colors.grey.shade100,
                        selectedColor: Colors.blue.shade100,
                        checkmarkColor: Colors.blue.shade700,
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 24),

                  // Price range
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Consultation Fee',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '₹${_priceRange.start.toInt()} - ₹${_priceRange.end.toInt()}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  RangeSlider(
                    values: _priceRange,
                    min: 0,
                    max: _maxPrice,
                    divisions: 10,
                    labels: RangeLabels(
                      '₹${_priceRange.start.toInt()}',
                      '₹${_priceRange.end.toInt()}',
                    ),
                    onChanged: (values) {
                      setState(() {
                        _priceRange = values;
                      });
                    },
                  ),
                  const SizedBox(height: 24),

                  // Buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            setState(() {
                              _selectedSpecialties = null;
                              _priceRange = RangeValues(0, _maxPrice);
                            });
                          },
                          child: const Text('Reset'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            this.setState(() {});
                          },
                          child: const Text('Apply'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  String _getSpecialtyName(DoctorSpecialty specialty) {
    return specialty.displayName;
  }

  DoctorSpecialty _getSpecialtyFromString(String specialtyStr) {
    return DoctorSpecialty.fromString(specialtyStr);
  }

}



