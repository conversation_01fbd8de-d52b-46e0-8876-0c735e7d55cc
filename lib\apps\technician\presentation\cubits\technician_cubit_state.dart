import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../shared/models/technician/technician.dart';

part 'technician_cubit_state.freezed.dart';

@freezed
sealed class TechnicianCubitState with _$TechnicianCubitState {
  const factory TechnicianCubitState({
    @Default(false) bool isLoading,
    @Default(false) bool hasError,
    String? errorMessage,
    Technician? technician,
  }) = _TechnicianCubitState;

  factory TechnicianCubitState.initial() => const TechnicianCubitState();
}
