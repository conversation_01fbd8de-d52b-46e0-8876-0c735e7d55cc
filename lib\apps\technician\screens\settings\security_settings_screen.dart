import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/providers/settings_provider.dart';
import 'package:shivish/apps/technician/screens/settings/change_password_screen.dart';
import 'package:shivish/apps/technician/screens/settings/two_factor_auth_screen.dart';
import 'package:shivish/apps/technician/screens/settings/connected_devices_screen.dart';

class SecuritySettingsScreen extends ConsumerWidget {
  const SecuritySettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final biometricAuth = ref.watch(biometricAuthProvider);
    final setBiometricAuth =
        ref.read(biometricAuthProvider.notifier).setBiometricAuth;
    final autoLock = ref.watch(autoLockProvider);
    final setAutoLock = ref.read(autoLockProvider.notifier).setAutoLock;
    final autoLockTimeout = ref.watch(autoLockTimeoutProvider);
    final setAutoLockTimeout =
        ref.read(autoLockTimeoutProvider.notifier).setAutoLockTimeout;
    final showBalance = ref.watch(showBalanceProvider);
    final setShowBalance =
        ref.read(showBalanceProvider.notifier).setShowBalance;
    final transactionNotifications =
        ref.watch(transactionNotificationsProvider);
    final setTransactionNotifications = ref
        .read(transactionNotificationsProvider.notifier)
        .setTransactionNotifications;

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Security Settings',
      ),
      body: ListView(
        children: [
          _buildSecuritySection(
            context,
            'Biometric Authentication',
            'Use fingerprint or face ID to unlock',
            biometricAuth,
            setBiometricAuth,
            Icons.fingerprint,
          ),
          _buildSecuritySection(
            context,
            'Auto Lock',
            'Lock app when inactive',
            autoLock,
            setAutoLock,
            Icons.lock,
          ),
          if (autoLock) ...[
            _buildTimeoutSection(
              context,
              'Auto Lock Timeout',
              'Set time before app locks',
              autoLockTimeout,
              setAutoLockTimeout,
            ),
          ],
          _buildSecuritySection(
            context,
            'Show Balance',
            'Display balance on home screen',
            showBalance,
            setShowBalance,
            Icons.account_balance_wallet,
          ),
          _buildSecuritySection(
            context,
            'Transaction Notifications',
            'Get notified for all transactions',
            transactionNotifications,
            setTransactionNotifications,
            Icons.notifications_active,
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.password),
            title: const Text('Change Password'),
            subtitle: const Text('Update your account password'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ChangePasswordScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.security),
            title: const Text('Two-Factor Authentication'),
            subtitle: const Text('Add an extra layer of security'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TwoFactorAuthScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.devices),
            title: const Text('Connected Devices'),
            subtitle: const Text('Manage devices logged into your account'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ConnectedDevicesScreen(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySection(
    BuildContext context,
    String title,
    String subtitle,
    bool enabled,
    void Function(bool) setEnabled,
    IconData icon,
  ) {
    return SwitchListTile(
      secondary: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      value: enabled,
      onChanged: (value) {
        setEnabled(value);
        _showSnackBar(
          context,
          '$title ${value ? 'enabled' : 'disabled'}',
        );
      },
    );
  }

  Widget _buildTimeoutSection(
    BuildContext context,
    String title,
    String subtitle,
    int timeout,
    void Function(int) setTimeout,
  ) {
    return ListTile(
      leading: const Icon(Icons.timer),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: DropdownButton<int>(
        value: timeout,
        items: const [
          DropdownMenuItem(
            value: 1,
            child: Text('1 minute'),
          ),
          DropdownMenuItem(
            value: 5,
            child: Text('5 minutes'),
          ),
          DropdownMenuItem(
            value: 10,
            child: Text('10 minutes'),
          ),
          DropdownMenuItem(
            value: 30,
            child: Text('30 minutes'),
          ),
          DropdownMenuItem(
            value: 60,
            child: Text('1 hour'),
          ),
        ],
        onChanged: (value) {
          if (value != null) {
            setTimeout(value);
            _showSnackBar(
              context,
              'Auto lock timeout set to ${value == 1 ? '1 minute' : '$value minutes'}',
            );
          }
        },
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
