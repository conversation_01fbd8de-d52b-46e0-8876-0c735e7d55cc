import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/sos_alert_service.dart';
import '../../../shared/providers/auth_provider.dart';

/// Provider for SOS alert service with proper lifecycle management
final sosAlertServiceProvider = Provider<SOSAlertService>((ref) {
  final service = SOSAlertService();
  
  // Ensure proper disposal when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});

/// Provider for active SOS alerts
final activeSOSAlertsProvider = Provider<Map<String, SOSAlert>>((ref) {
  final service = ref.watch(sosAlertServiceProvider);
  return service.activeAlerts;
});

/// Provider for SOS alert actions
final sosAlertActionsProvider = Provider<SOSAlertActions>((ref) {
  final service = ref.watch(sosAlertServiceProvider);
  final auth = ref.watch(authProvider);
  return SOSAlertActions(service, auth?.id);
});

/// Actions for SOS alerts with error handling
class SOSAlertActions {
  final SOSAlertService _service;
  final String? _userId;

  SOSAlertActions(this._service, this._userId);

  /// Create panic alert
  Future<SOSAlert?> createPanicAlert({
    required String rideId,
    String? customMessage,
  }) async {
    if (_userId == null || _userId.isEmpty) {
      print('Cannot create panic alert: user not authenticated');
      return null;
    }

    if (rideId.isEmpty) {
      print('Cannot create panic alert: invalid ride ID');
      return null;
    }

    try {
      return await _service.createPanicAlert(
        rideId: rideId,
        userId: _userId,
        customMessage: customMessage,
      );
    } catch (e) {
      print('Error creating panic alert: $e');
      return null;
    }
  }

  /// Create safety check missed alert
  Future<SOSAlert?> createSafetyCheckMissedAlert({
    required String rideId,
    required int missedChecks,
  }) async {
    if (_userId == null || _userId.isEmpty) {
      print('Cannot create safety check alert: user not authenticated');
      return null;
    }

    if (rideId.isEmpty) {
      print('Cannot create safety check alert: invalid ride ID');
      return null;
    }

    try {
      return await _service.createSafetyCheckMissedAlert(
        rideId: rideId,
        userId: _userId,
        missedChecks: missedChecks,
      );
    } catch (e) {
      print('Error creating safety check alert: $e');
      return null;
    }
  }

  /// Create custom SOS alert
  Future<SOSAlert?> createSOSAlert({
    required String rideId,
    required SOSAlertType type,
    required String message,
    SOSPriority priority = SOSPriority.high,
    Map<String, dynamic> metadata = const {},
  }) async {
    if (_userId == null || _userId.isEmpty) {
      print('Cannot create SOS alert: user not authenticated');
      return null;
    }

    if (rideId.isEmpty) {
      print('Cannot create SOS alert: invalid ride ID');
      return null;
    }

    try {
      return await _service.createSOSAlert(
        rideId: rideId,
        userId: _userId,
        type: type,
        message: message,
        priority: priority,
        metadata: metadata,
      );
    } catch (e) {
      print('Error creating SOS alert: $e');
      return null;
    }
  }

  /// Acknowledge alert
  Future<void> acknowledgeAlert(String alertId) async {
    if (_userId == null || _userId.isEmpty) {
      print('Cannot acknowledge alert: user not authenticated');
      return;
    }

    try {
      await _service.acknowledgeAlert(alertId, _userId);
    } catch (e) {
      print('Error acknowledging alert: $e');
    }
  }

  /// Resolve alert
  Future<void> resolveAlert(String alertId) async {
    if (_userId == null || _userId.isEmpty) {
      print('Cannot resolve alert: user not authenticated');
      return;
    }

    try {
      await _service.resolveAlert(alertId, _userId);
    } catch (e) {
      print('Error resolving alert: $e');
    }
  }

  /// Cancel alert
  Future<void> cancelAlert(String alertId) async {
    if (_userId == null || _userId.isEmpty) {
      print('Cannot cancel alert: user not authenticated');
      return;
    }

    try {
      await _service.cancelAlert(alertId, _userId);
    } catch (e) {
      print('Error cancelling alert: $e');
    }
  }

  /// Get user's alert history
  Future<List<SOSAlert>> getUserAlerts() async {
    if (_userId == null || _userId.isEmpty) {
      print('Cannot get user alerts: user not authenticated');
      return [];
    }

    try {
      return await _service.getUserAlerts(_userId);
    } catch (e) {
      print('Error getting user alerts: $e');
      return [];
    }
  }

  /// Add emergency contact
  Future<void> addEmergencyContact({
    required String name,
    required String phone,
    String? email,
    String? relationship,
  }) async {
    if (_userId == null || _userId.isEmpty) {
      print('Cannot add emergency contact: user not authenticated');
      return;
    }

    try {
      await _service.addEmergencyContact(
        userId: _userId,
        name: name,
        phone: phone,
        email: email,
        relationship: relationship,
      );
    } catch (e) {
      print('Error adding emergency contact: $e');
    }
  }

  /// Remove emergency contact
  Future<void> removeEmergencyContact(String contactId) async {
    try {
      await _service.removeEmergencyContact(contactId);
    } catch (e) {
      print('Error removing emergency contact: $e');
    }
  }

  /// Get user's emergency contacts
  Future<List<Map<String, dynamic>>> getUserEmergencyContacts() async {
    if (_userId == null || _userId.isEmpty) {
      print('Cannot get emergency contacts: user not authenticated');
      return [];
    }

    try {
      return await _service.getUserEmergencyContacts(_userId);
    } catch (e) {
      print('Error getting emergency contacts: $e');
      return [];
    }
  }

  /// Check if user is authenticated
  bool get isUserAuthenticated => _userId != null && _userId.isNotEmpty;

  /// Get user ID
  String? get userId => _userId;

  /// Check if service has active alerts
  bool get hasActiveAlerts => _service.hasActiveAlerts;

  /// Get active alerts count
  int get activeAlertsCount => _service.activeAlertsCount;

  /// Get service health status
  Map<String, dynamic> get healthStatus => _service.healthStatus;
}
