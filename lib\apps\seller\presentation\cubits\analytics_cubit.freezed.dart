// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'analytics_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AnalyticsState {

 bool get isLoading; String? get error; SalesAnalytics? get salesAnalytics; ProductAnalytics? get productAnalytics; CustomerAnalytics? get customerAnalytics; PerformanceMetrics? get performanceMetrics;
/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AnalyticsStateCopyWith<AnalyticsState> get copyWith => _$AnalyticsStateCopyWithImpl<AnalyticsState>(this as AnalyticsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AnalyticsState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error)&&(identical(other.salesAnalytics, salesAnalytics) || other.salesAnalytics == salesAnalytics)&&(identical(other.productAnalytics, productAnalytics) || other.productAnalytics == productAnalytics)&&(identical(other.customerAnalytics, customerAnalytics) || other.customerAnalytics == customerAnalytics)&&(identical(other.performanceMetrics, performanceMetrics) || other.performanceMetrics == performanceMetrics));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,error,salesAnalytics,productAnalytics,customerAnalytics,performanceMetrics);

@override
String toString() {
  return 'AnalyticsState(isLoading: $isLoading, error: $error, salesAnalytics: $salesAnalytics, productAnalytics: $productAnalytics, customerAnalytics: $customerAnalytics, performanceMetrics: $performanceMetrics)';
}


}

/// @nodoc
abstract mixin class $AnalyticsStateCopyWith<$Res>  {
  factory $AnalyticsStateCopyWith(AnalyticsState value, $Res Function(AnalyticsState) _then) = _$AnalyticsStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, String? error, SalesAnalytics? salesAnalytics, ProductAnalytics? productAnalytics, CustomerAnalytics? customerAnalytics, PerformanceMetrics? performanceMetrics
});


$SalesAnalyticsCopyWith<$Res>? get salesAnalytics;$ProductAnalyticsCopyWith<$Res>? get productAnalytics;$CustomerAnalyticsCopyWith<$Res>? get customerAnalytics;$PerformanceMetricsCopyWith<$Res>? get performanceMetrics;

}
/// @nodoc
class _$AnalyticsStateCopyWithImpl<$Res>
    implements $AnalyticsStateCopyWith<$Res> {
  _$AnalyticsStateCopyWithImpl(this._self, this._then);

  final AnalyticsState _self;
  final $Res Function(AnalyticsState) _then;

/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? error = freezed,Object? salesAnalytics = freezed,Object? productAnalytics = freezed,Object? customerAnalytics = freezed,Object? performanceMetrics = freezed,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,salesAnalytics: freezed == salesAnalytics ? _self.salesAnalytics : salesAnalytics // ignore: cast_nullable_to_non_nullable
as SalesAnalytics?,productAnalytics: freezed == productAnalytics ? _self.productAnalytics : productAnalytics // ignore: cast_nullable_to_non_nullable
as ProductAnalytics?,customerAnalytics: freezed == customerAnalytics ? _self.customerAnalytics : customerAnalytics // ignore: cast_nullable_to_non_nullable
as CustomerAnalytics?,performanceMetrics: freezed == performanceMetrics ? _self.performanceMetrics : performanceMetrics // ignore: cast_nullable_to_non_nullable
as PerformanceMetrics?,
  ));
}
/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SalesAnalyticsCopyWith<$Res>? get salesAnalytics {
    if (_self.salesAnalytics == null) {
    return null;
  }

  return $SalesAnalyticsCopyWith<$Res>(_self.salesAnalytics!, (value) {
    return _then(_self.copyWith(salesAnalytics: value));
  });
}/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProductAnalyticsCopyWith<$Res>? get productAnalytics {
    if (_self.productAnalytics == null) {
    return null;
  }

  return $ProductAnalyticsCopyWith<$Res>(_self.productAnalytics!, (value) {
    return _then(_self.copyWith(productAnalytics: value));
  });
}/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CustomerAnalyticsCopyWith<$Res>? get customerAnalytics {
    if (_self.customerAnalytics == null) {
    return null;
  }

  return $CustomerAnalyticsCopyWith<$Res>(_self.customerAnalytics!, (value) {
    return _then(_self.copyWith(customerAnalytics: value));
  });
}/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PerformanceMetricsCopyWith<$Res>? get performanceMetrics {
    if (_self.performanceMetrics == null) {
    return null;
  }

  return $PerformanceMetricsCopyWith<$Res>(_self.performanceMetrics!, (value) {
    return _then(_self.copyWith(performanceMetrics: value));
  });
}
}


/// Adds pattern-matching-related methods to [AnalyticsState].
extension AnalyticsStatePatterns on AnalyticsState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AnalyticsState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AnalyticsState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AnalyticsState value)  $default,){
final _that = this;
switch (_that) {
case _AnalyticsState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AnalyticsState value)?  $default,){
final _that = this;
switch (_that) {
case _AnalyticsState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isLoading,  String? error,  SalesAnalytics? salesAnalytics,  ProductAnalytics? productAnalytics,  CustomerAnalytics? customerAnalytics,  PerformanceMetrics? performanceMetrics)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AnalyticsState() when $default != null:
return $default(_that.isLoading,_that.error,_that.salesAnalytics,_that.productAnalytics,_that.customerAnalytics,_that.performanceMetrics);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isLoading,  String? error,  SalesAnalytics? salesAnalytics,  ProductAnalytics? productAnalytics,  CustomerAnalytics? customerAnalytics,  PerformanceMetrics? performanceMetrics)  $default,) {final _that = this;
switch (_that) {
case _AnalyticsState():
return $default(_that.isLoading,_that.error,_that.salesAnalytics,_that.productAnalytics,_that.customerAnalytics,_that.performanceMetrics);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isLoading,  String? error,  SalesAnalytics? salesAnalytics,  ProductAnalytics? productAnalytics,  CustomerAnalytics? customerAnalytics,  PerformanceMetrics? performanceMetrics)?  $default,) {final _that = this;
switch (_that) {
case _AnalyticsState() when $default != null:
return $default(_that.isLoading,_that.error,_that.salesAnalytics,_that.productAnalytics,_that.customerAnalytics,_that.performanceMetrics);case _:
  return null;

}
}

}

/// @nodoc


class _AnalyticsState extends AnalyticsState {
  const _AnalyticsState({this.isLoading = false, this.error, this.salesAnalytics, this.productAnalytics, this.customerAnalytics, this.performanceMetrics}): super._();
  

@override@JsonKey() final  bool isLoading;
@override final  String? error;
@override final  SalesAnalytics? salesAnalytics;
@override final  ProductAnalytics? productAnalytics;
@override final  CustomerAnalytics? customerAnalytics;
@override final  PerformanceMetrics? performanceMetrics;

/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AnalyticsStateCopyWith<_AnalyticsState> get copyWith => __$AnalyticsStateCopyWithImpl<_AnalyticsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AnalyticsState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.error, error) || other.error == error)&&(identical(other.salesAnalytics, salesAnalytics) || other.salesAnalytics == salesAnalytics)&&(identical(other.productAnalytics, productAnalytics) || other.productAnalytics == productAnalytics)&&(identical(other.customerAnalytics, customerAnalytics) || other.customerAnalytics == customerAnalytics)&&(identical(other.performanceMetrics, performanceMetrics) || other.performanceMetrics == performanceMetrics));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,error,salesAnalytics,productAnalytics,customerAnalytics,performanceMetrics);

@override
String toString() {
  return 'AnalyticsState(isLoading: $isLoading, error: $error, salesAnalytics: $salesAnalytics, productAnalytics: $productAnalytics, customerAnalytics: $customerAnalytics, performanceMetrics: $performanceMetrics)';
}


}

/// @nodoc
abstract mixin class _$AnalyticsStateCopyWith<$Res> implements $AnalyticsStateCopyWith<$Res> {
  factory _$AnalyticsStateCopyWith(_AnalyticsState value, $Res Function(_AnalyticsState) _then) = __$AnalyticsStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, String? error, SalesAnalytics? salesAnalytics, ProductAnalytics? productAnalytics, CustomerAnalytics? customerAnalytics, PerformanceMetrics? performanceMetrics
});


@override $SalesAnalyticsCopyWith<$Res>? get salesAnalytics;@override $ProductAnalyticsCopyWith<$Res>? get productAnalytics;@override $CustomerAnalyticsCopyWith<$Res>? get customerAnalytics;@override $PerformanceMetricsCopyWith<$Res>? get performanceMetrics;

}
/// @nodoc
class __$AnalyticsStateCopyWithImpl<$Res>
    implements _$AnalyticsStateCopyWith<$Res> {
  __$AnalyticsStateCopyWithImpl(this._self, this._then);

  final _AnalyticsState _self;
  final $Res Function(_AnalyticsState) _then;

/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? error = freezed,Object? salesAnalytics = freezed,Object? productAnalytics = freezed,Object? customerAnalytics = freezed,Object? performanceMetrics = freezed,}) {
  return _then(_AnalyticsState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,salesAnalytics: freezed == salesAnalytics ? _self.salesAnalytics : salesAnalytics // ignore: cast_nullable_to_non_nullable
as SalesAnalytics?,productAnalytics: freezed == productAnalytics ? _self.productAnalytics : productAnalytics // ignore: cast_nullable_to_non_nullable
as ProductAnalytics?,customerAnalytics: freezed == customerAnalytics ? _self.customerAnalytics : customerAnalytics // ignore: cast_nullable_to_non_nullable
as CustomerAnalytics?,performanceMetrics: freezed == performanceMetrics ? _self.performanceMetrics : performanceMetrics // ignore: cast_nullable_to_non_nullable
as PerformanceMetrics?,
  ));
}

/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SalesAnalyticsCopyWith<$Res>? get salesAnalytics {
    if (_self.salesAnalytics == null) {
    return null;
  }

  return $SalesAnalyticsCopyWith<$Res>(_self.salesAnalytics!, (value) {
    return _then(_self.copyWith(salesAnalytics: value));
  });
}/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProductAnalyticsCopyWith<$Res>? get productAnalytics {
    if (_self.productAnalytics == null) {
    return null;
  }

  return $ProductAnalyticsCopyWith<$Res>(_self.productAnalytics!, (value) {
    return _then(_self.copyWith(productAnalytics: value));
  });
}/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CustomerAnalyticsCopyWith<$Res>? get customerAnalytics {
    if (_self.customerAnalytics == null) {
    return null;
  }

  return $CustomerAnalyticsCopyWith<$Res>(_self.customerAnalytics!, (value) {
    return _then(_self.copyWith(customerAnalytics: value));
  });
}/// Create a copy of AnalyticsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PerformanceMetricsCopyWith<$Res>? get performanceMetrics {
    if (_self.performanceMetrics == null) {
    return null;
  }

  return $PerformanceMetricsCopyWith<$Res>(_self.performanceMetrics!, (value) {
    return _then(_self.copyWith(performanceMetrics: value));
  });
}
}

// dart format on
