import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/presentation/cubits/orders_cubit.dart'
    as cubit;
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';
import 'package:shivish/shared/utils/date_formatter.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/models/order/order_model.dart';

class RecentOrdersScreen extends StatelessWidget {
  const RecentOrdersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<cubit.OrdersCubit, cubit.OrdersState>(
      builder: (context, state) {
        return Scaffold(
          appBar: AppToolbar.simple(title: 'Recent Orders'),
          body: state.maybeWhen(
            loading: () => const LoadingIndicator(),
            error: (message) => Center(
              child: Text(
                'Error: $message',
                style: const TextStyle(color: Colors.red),
              ),
            ),
            loaded: (orders) => _buildContent(context, orders),
            orElse: () => const Center(child: Text('Unknown State')),
          ),
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, List<OrderModel> orders) {
    if (orders.isEmpty) {
      return const Center(child: Text('No recent orders'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ListTile(
            title: Text('Order #${order.id}'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${order.items.length} items • ${CurrencyFormatter.format(order.total)}',
                ),
                Text(
                  DateFormatter.formatDateTime(order.createdAt),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            trailing: _buildOrderStatusChip(context, order.status),
            onTap: () => _navigateToOrderDetails(context, order),
          ),
        );
      },
    );
  }

  Widget _buildOrderStatusChip(BuildContext context, OrderStatus status) {
    final color = _getStatusColor(status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.name,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.processing:
        return Colors.blue;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
      case OrderStatus.confirmed:
        return Colors.blue;
      case OrderStatus.readyForPickup:
        return Colors.purple;
      case OrderStatus.inTransit:
        return Colors.indigo;
      case OrderStatus.outForDelivery:
        return Colors.deepPurple;
      case OrderStatus.refunded:
        return Colors.grey;
    }
  }

  void _navigateToOrderDetails(BuildContext context, OrderModel order) {
    Navigator.pushNamed(
      context,
      '/seller/orders/${order.id}',
      arguments: order,
    );
  }
}
