import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/booking/booking_model.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/providers/booking_provider.dart';
import '../../../../shared/ui_components/navigation/bottom_nav_back_handler.dart';
import '../../buyer_routes.dart';
import '../priest/priest_list_screen.dart';
import 'booking_details_screen.dart';

class PriestBookingScreen extends ConsumerStatefulWidget {
  const PriestBookingScreen({super.key});

  @override
  ConsumerState<PriestBookingScreen> createState() =>
      _PriestBookingScreenState();
}

class _PriestBookingScreenState extends ConsumerState<PriestBookingScreen> {
  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final userId = authState?.id ?? '';

    final bookingsAsync = ref.watch(bookingStateNotifierProvider(userId));

    return BottomNavBackHandler(
      fallbackRoute: BuyerRoutes.home,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Priest Bookings'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go(BuyerRoutes.home),
          ),
        ),
        body: bookingsAsync.when(
          data: (bookings) {
            final priestBookings = bookings
                .where((booking) => booking.type == BookingType.priest)
                .toList();

            if (priestBookings.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.person, size: 64),
                    const SizedBox(height: 16),
                    const Text(
                      'No Priest Bookings Yet',
                      style: TextStyle(fontSize: 18),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const PriestListScreen(),
                          ),
                        );
                      },
                      child: const Text('Book a Priest'),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              itemCount: priestBookings.length,
              itemBuilder: (context, index) {
                final booking = priestBookings[index];
                return Card(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ListTile(
                    leading: const CircleAvatar(
                      child: Icon(Icons.person),
                    ),
                    title: Text('Booking #${booking.bookingNumber}'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Date: ${booking.bookingDate.toString().split(' ')[0]}',
                        ),
                        Text('Status: ${booking.status.name}'),
                        if (booking.paymentStatus == PaymentStatus.completed)
                          const Text('Paid',
                              style: TextStyle(color: Colors.green)),
                      ],
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.arrow_forward_ios),
                      onPressed: () {
                        if (mounted) {
                          Navigator.of(context).pop();
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => BookingDetailsScreen(
                                bookingId: booking.id,
                              ),
                            ),
                          );
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Booking created successfully'),
                            ),
                          );
                        }
                      },
                    ),
                  ),
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Error: $error'),
          ),
        ),
      ),
    );
  }
}
