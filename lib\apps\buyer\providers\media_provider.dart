import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:just_audio/just_audio.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

import '../../../shared/services/navigation/navigation_service.dart';
import '../../../shared/services/media/media_service_provider.dart';
import '../../../shared/models/media/media_model.dart';
import '../screens/media/media_options_menu.dart';
import 'auth_provider.dart';

class MediaState {
  final String id;
  final String title;
  final String artist;
  final String duration;
  final String type;
  final String? thumbnail;
  final bool isFavorite;
  final bool isPlaying;
  final double progress;
  final double volume;
  final bool isShuffleEnabled;
  final bool isRepeatEnabled;
  final int currentIndex;
  final List<Map<String, dynamic>> playlist;
  final String? error;
  final List<Map<String, dynamic>>? playlistOptions;
  final List<Map<String, dynamic>>? queue;

  MediaState({
    required this.id,
    required this.title,
    required this.artist,
    required this.duration,
    required this.type,
    this.thumbnail,
    required this.isFavorite,
    required this.isPlaying,
    required this.progress,
    required this.volume,
    required this.isShuffleEnabled,
    required this.isRepeatEnabled,
    required this.currentIndex,
    required this.playlist,
    this.error,
    this.playlistOptions,
    this.queue,
  });

  MediaState copyWith({
    String? id,
    String? title,
    String? artist,
    String? duration,
    String? type,
    String? thumbnail,
    bool? isFavorite,
    bool? isPlaying,
    double? progress,
    double? volume,
    bool? isShuffleEnabled,
    bool? isRepeatEnabled,
    int? currentIndex,
    List<Map<String, dynamic>>? playlist,
    String? error,
    List<Map<String, dynamic>>? playlistOptions,
    List<Map<String, dynamic>>? queue,
  }) {
    return MediaState(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      duration: duration ?? this.duration,
      type: type ?? this.type,
      thumbnail: thumbnail ?? this.thumbnail,
      isFavorite: isFavorite ?? this.isFavorite,
      isPlaying: isPlaying ?? this.isPlaying,
      progress: progress ?? this.progress,
      volume: volume ?? this.volume,
      isShuffleEnabled: isShuffleEnabled ?? this.isShuffleEnabled,
      isRepeatEnabled: isRepeatEnabled ?? this.isRepeatEnabled,
      currentIndex: currentIndex ?? this.currentIndex,
      playlist: playlist ?? this.playlist,
      error: error ?? this.error,
      playlistOptions: playlistOptions ?? this.playlistOptions,
      queue: queue ?? this.queue,
    );
  }
}

class MediaNotifier extends StateNotifier<MediaState> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  final Ref ref;

  MediaNotifier(this.ref)
      : super(MediaState(
          id: '',
          title: '',
          artist: '',
          duration: '0:00',
          type: 'Audio',
          isFavorite: false,
          isPlaying: false,
          progress: 0.0,
          volume: 0.7,
          isShuffleEnabled: false,
          isRepeatEnabled: false,
          currentIndex: 0,
          playlist: [],
        )) {
    _initAudioPlayer();
  }

  void _initAudioPlayer() {
    _audioPlayer.playerStateStream.listen((playerState) {
      state = state.copyWith(
        isPlaying: playerState.playing,
        progress: _audioPlayer.position.inMilliseconds /
            (_audioPlayer.duration?.inMilliseconds ?? 1),
      );
    });

    _audioPlayer.currentIndexStream.listen((index) {
      if (index != null) {
        state = state.copyWith(currentIndex: index);
      }
    });
  }

  Future<String?> _generateVideoThumbnail(String videoUrl) async {
    try {
      // Download video to temporary file
      final tempDir = await getTemporaryDirectory();
      final videoFile = File('${tempDir.path}/temp_video.mp4');

      // Download video
      final response = await HttpClient().getUrl(Uri.parse(videoUrl));
      final httpResponse = await response.close();
      await httpResponse.pipe(videoFile.openWrite());
      await videoFile.create();

      // Generate thumbnail
      final thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: videoFile.path,
        imageFormat: ImageFormat.JPEG,
        quality: 75,
      );

      // Clean up temporary video file
      await videoFile.delete();

      return thumbnailPath;
    } catch (e) {
      debugPrint('Error generating video thumbnail: $e');
      return null;
    }
  }

  Future<void> loadMedia(String mediaId) async {
    try {
      final mediaService = ref.read(mediaServiceProvider);
      final mediaItem = await mediaService.getMediaById(mediaId);
      final user = ref.read(authStateProvider).user;
      final isFavorite = user != null
          ? await mediaService.isFavorite(mediaId, user.id)
          : false;

      if (mediaItem != null) {
        String? thumbnail;
        if (mediaItem.type == MediaType.video) {
          thumbnail = await _generateVideoThumbnail(mediaItem.url);
        }

        state = state.copyWith(
          id: mediaId,
          title: mediaItem.title,
          artist: mediaItem.uploadedBy,
          duration: '0:00', // Duration is not part of MediaModel
          type: mediaItem.type.toString(),
          thumbnail: thumbnail,
          isFavorite: isFavorite,
        );

        await _audioPlayer.setUrl(mediaItem.url);
      }
    } catch (e) {
      debugPrint('Error loading media: $e');
      state = state.copyWith(error: 'Failed to load media: $e');
    }
  }

  Future<void> uploadMedia(File file, String title, String artist) async {
    try {
      final mediaService = ref.read(mediaServiceProvider);
      final user = ref.read(authStateProvider).user;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Upload media file
      final mediaItem = await mediaService.uploadMediaAndCreateRecord(
        file: file,
        userId: user.id,
        title: title,
        description: '', // Required by MediaModel
        fileName: file.path.split('/').last,
        status: MediaStatus.pending,
        visibility: MediaVisibility.public,
      );

      // Generate thumbnail for video files
      if (mediaItem.type == MediaType.video) {
        final thumbnailPath = await _generateVideoThumbnail(mediaItem.url);
        if (thumbnailPath != null) {
          // Upload thumbnail to storage
          await mediaService.uploadThumbnail(
            mediaId: mediaItem.id,
            thumbnailFile: File(thumbnailPath),
          );

          // Clean up temporary thumbnail file
          await File(thumbnailPath).delete();
        }
      }

      // Add to playlist
      final newItem = {
        'id': mediaItem.id,
        'title': title,
        'artist': artist,
        'duration': '0:00', // Duration is not part of MediaModel
        'type': mediaItem.type.toString(),
        'thumbnail': null, // Thumbnail is not part of MediaModel
        'url': mediaItem.url,
        'addedAt': DateTime.now().toIso8601String(),
      };

      state = state.copyWith(
        playlist: [...state.playlist, newItem],
      );
    } catch (e) {
      debugPrint('Error uploading media: $e');
      state = state.copyWith(error: 'Failed to upload media: $e');
    }
  }

  Future<void> deleteMedia(String mediaId) async {
    try {
      final mediaService = ref.read(mediaServiceProvider);
      final user = ref.read(authStateProvider).user;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Delete media file and thumbnail
      await mediaService.deleteMedia(mediaId);

      // Remove from playlist
      state = state.copyWith(
        playlist:
            state.playlist.where((item) => item['id'] != mediaId).toList(),
      );
    } catch (e) {
      debugPrint('Error deleting media: $e');
      state = state.copyWith(error: 'Failed to delete media: $e');
    }
  }

  Future<void> toggleFavorite() async {
    try {
      final user = ref.read(authStateProvider).user;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final mediaService = ref.read(mediaServiceProvider);
      await mediaService.toggleFavorite(state.id, user.id);
      state = state.copyWith(isFavorite: !state.isFavorite);
    } catch (e) {
      debugPrint('Error toggling favorite: $e');
    }
  }

  void togglePlay() {
    if (state.isPlaying) {
      _audioPlayer.pause();
    } else {
      _audioPlayer.play();
    }
  }

  void toggleShuffle() {
    state = state.copyWith(isShuffleEnabled: !state.isShuffleEnabled);
    _audioPlayer.setShuffleModeEnabled(state.isShuffleEnabled);
  }

  void toggleRepeat() {
    state = state.copyWith(isRepeatEnabled: !state.isRepeatEnabled);
    _audioPlayer.setLoopMode(
      state.isRepeatEnabled ? LoopMode.one : LoopMode.off,
    );
  }

  void playPrevious() {
    if (state.currentIndex > 0) {
      _audioPlayer.seekToPrevious();
    }
  }

  void playNext() {
    if (state.currentIndex < state.playlist.length - 1) {
      _audioPlayer.seekToNext();
    }
  }

  void setVolume(double volume) {
    state = state.copyWith(volume: volume);
    _audioPlayer.setVolume(volume);
  }

  void seekTo(double position) {
    final duration = _audioPlayer.duration;
    if (duration != null) {
      _audioPlayer.seek(duration * position);
    }
  }

  void showOptionsMenu() {
    showModalBottomSheet(
      context: NavigationService.currentContext!,
      builder: (context) => const MediaOptionsMenu(),
    );
  }

  Future<void> playAllItems(List<Map<String, dynamic>> items) async {
    try {
      state = state.copyWith(
        playlist: items,
        currentIndex: 0,
        isPlaying: true,
      );
      await _playCurrentItem();
    } catch (e) {
      state = state.copyWith(error: 'Failed to play items: $e');
    }
  }

  Future<void> shufflePlaylist() async {
    try {
      final shuffledItems = List<Map<String, dynamic>>.from(state.playlist)
        ..shuffle();
      state = state.copyWith(
        playlist: shuffledItems,
        currentIndex: 0,
        isShuffleEnabled: true,
      );
      await _playCurrentItem();
    } catch (e) {
      state = state.copyWith(error: 'Failed to shuffle playlist: $e');
    }
  }

  Future<void> addItemsToPlaylist(List<Map<String, dynamic>> items) async {
    try {
      final updatedPlaylist = [...state.playlist, ...items];
      state = state.copyWith(playlist: updatedPlaylist);
    } catch (e) {
      state = state.copyWith(error: 'Failed to add items: $e');
    }
  }

  Future<void> togglePlaylistItemFavorite(String itemId) async {
    try {
      final updatedPlaylist = state.playlist.map((item) {
        if (item['id'] == itemId) {
          return {
            ...item,
            'isFavorite': !(item['isFavorite'] ?? false),
          };
        }
        return item;
      }).toList();
      state = state.copyWith(playlist: updatedPlaylist);
    } catch (e) {
      state = state.copyWith(error: 'Failed to toggle favorite: $e');
    }
  }

  Future<void> navigateToMediaPlayer(String itemId) async {
    try {
      final index = state.playlist.indexWhere((item) => item['id'] == itemId);
      if (index != -1) {
        state = state.copyWith(currentIndex: index);
        await _playCurrentItem();
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to navigate to media player: $e');
    }
  }

  Future<void> _playCurrentItem() async {
    if (state.playlist.isEmpty) return;

    final currentItem = state.playlist[state.currentIndex];
    try {
      await _audioPlayer.setUrl(currentItem['url']);
      await _audioPlayer.play();
    } catch (e) {
      state = state.copyWith(error: 'Failed to play current item: $e');
    }
  }

  Future<void> editPlaylistDetails(
      String playlistId, Map<String, dynamic> details) async {
    try {
      final mediaService = ref.read(mediaServiceProvider);
      final user = ref.read(authStateProvider).user;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Update playlist details in supabase
      await mediaService.updatePlaylistDetails(playlistId, details);

      // Update local state
      state = state.copyWith(
        playlist: state.playlist.map((item) {
          if (item['playlistId'] == playlistId) {
            return {...item, ...details};
          }
          return item;
        }).toList(),
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to edit playlist details: $e');
    }
  }

  Future<void> showPlaylistOptions(String playlistId) async {
    try {
      final mediaService = ref.read(mediaServiceProvider);
      final user = ref.read(authStateProvider).user;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get playlist options from hybrid database
      final options = await mediaService.getPlaylistOptions(playlistId);

      // Update local state with options
      state = state.copyWith(playlistOptions: options);
    } catch (e) {
      state = state.copyWith(error: 'Failed to get playlist options: $e');
    }
  }

  Future<void> createPlaylist(String name) async {
    try {
      final newPlaylist = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'name': name,
        'items': [],
        'createdAt': DateTime.now().toIso8601String(),
      };

      final updatedPlaylistOptions = [
        ...(state.playlistOptions ?? []),
        newPlaylist
      ];
      state = state.copyWith(playlistOptions: updatedPlaylistOptions);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> addToPlaylist(String mediaId, String playlistId) async {
    try {
      final updatedPlaylistOptions = state.playlistOptions?.map((playlist) {
        if (playlist['id'] == playlistId) {
          final items =
              List<Map<String, dynamic>>.from(playlist['items'] ?? []);
          items.add({
            'id': mediaId,
            'title': state.title,
            'artist': state.artist,
            'duration': state.duration,
            'type': state.type,
            'thumbnail': state.thumbnail,
            'addedAt': DateTime.now().toIso8601String(),
          });
          return {...playlist, 'items': items};
        }
        return playlist;
      }).toList();

      state = state.copyWith(playlistOptions: updatedPlaylistOptions);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> addToRecentlyPlayed(String mediaId) async {
    try {
      final recentPlaylist = {
        'id': 'recent',
        'name': 'Recently Played',
        'items': [
          {
            'id': mediaId,
            'title': state.title,
            'artist': state.artist,
            'duration': state.duration,
            'type': state.type,
            'thumbnail': state.thumbnail,
            'playedAt': DateTime.now().toIso8601String(),
          }
        ],
      };

      final updatedPlaylistOptions = [
        ...(state.playlistOptions ?? []),
        recentPlaylist
      ];
      state = state.copyWith(playlistOptions: updatedPlaylistOptions);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> addToQueue(String mediaId) async {
    try {
      final queueItem = {
        'id': mediaId,
        'title': state.title,
        'artist': state.artist,
        'duration': state.duration,
        'type': state.type,
        'thumbnail': state.thumbnail,
        'addedAt': DateTime.now().toIso8601String(),
      };

      final updatedQueue = [...(state.queue ?? []), queueItem];
      state = state.copyWith(queue: updatedQueue);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> removeFromPlaylist(String playlistId, String itemId) async {
    try {
      final updatedPlaylist = state.playlist.map((item) {
        if (item['id'] == playlistId) {
          final items = List<Map<String, dynamic>>.from(item['items'] ?? []);
          items.removeWhere((i) => i['id'] == itemId);
          return {...item, 'items': items};
        }
        return item;
      }).toList();

      state = state.copyWith(playlist: updatedPlaylist);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> deletePlaylist(String playlistId) async {
    try {
      final updatedPlaylist =
          state.playlist.where((item) => item['id'] != playlistId).toList();

      state = state.copyWith(playlist: updatedPlaylist);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> addItemsToPlaylistByIds(
      String playlistId, List<String> itemIds) async {
    try {
      final updatedPlaylist = state.playlist.map((item) {
        if (item['id'] == playlistId) {
          final items = List<Map<String, dynamic>>.from(item['items'] ?? []);
          final newItems = itemIds
              .map((id) {
                final mediaItem = state.playlist.firstWhere(
                  (i) => i['id'] == id,
                  orElse: () => {},
                );
                return mediaItem;
              })
              .where((item) => item.isNotEmpty)
              .toList();

          items.addAll(newItems);
          return {...item, 'items': items};
        }
        return item;
      }).toList();

      state = state.copyWith(playlist: updatedPlaylist);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}

final mediaProvider = StateNotifierProvider<MediaNotifier, MediaState>((ref) {
  return MediaNotifier(ref);
});
