import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:injectable/injectable.dart';
import '../../../shared/database/services/database_service.dart';
import '../models/traffic_config.dart';
import '../models/server_config.dart';
import '../../../shared/utils/logger.dart';

/// Service for managing infrastructure configuration and traffic routing
@injectable
class InfrastructureService {
  final DatabaseService _databaseService;
  final http.Client _httpClient;
  final _logger = getLogger('InfrastructureService');

  // Configuration endpoints
  static const String _trafficControllerUrl =
      'https://traffic-controller.your-domain.com';
  static const String _awsApiUrl = 'https://aws-api.your-domain.com';
  static const String _datacenterApiUrl =
      'https://datacenter-api.your-domain.com';

  InfrastructureService(this._databaseService, this._httpClient);

  /// Get current traffic configuration
  Future<TrafficConfig> getTrafficConfig() async {
    try {
      final data = await _databaseService.find(
        'infrastructure',
        'traffic_config',
      );

      if (data != null) {
        return TrafficConfig.fromJson(data);
      } else {
        // Return default configuration
        return TrafficConfig(
          routingMode: TrafficRoutingMode.hybrid,
          awsTrafficPercentage: 50.0,
          lastUpdated: DateTime.now(),
        );
      }
    } catch (e) {
      _logger.severe('Error getting traffic config: $e');
      rethrow;
    }
  }

  /// Update traffic configuration
  Future<void> updateTrafficConfig(TrafficConfig config) async {
    try {
      // Update database
      await _databaseService.update(
        'infrastructure',
        'traffic_config',
        config.toJson(),
      );

      // Update traffic controller
      await _updateTrafficController(config);

      _logger.info('Traffic configuration updated successfully');
    } catch (e) {
      _logger.severe('Error updating traffic config: $e');
      rethrow;
    }
  }

  /// Update traffic controller with new routing rules
  Future<void> _updateTrafficController(TrafficConfig config) async {
    try {
      final response = await _httpClient.post(
        Uri.parse('$_trafficControllerUrl/api/v1/routing/update'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: jsonEncode({
          'routing_mode': config.routingMode.name,
          'aws_percentage': config.awsTrafficPercentage,
          'maintenance_mode': config.maintenanceMode,
          'auto_failover': config.autoFailoverEnabled,
          'custom_rules': config.customRules,
          'timestamp': config.lastUpdated.toIso8601String(),
        }),
      );

      if (response.statusCode != 200) {
        throw Exception(
          'Failed to update traffic controller: ${response.body}',
        );
      }

      _logger.info('Traffic controller updated successfully');
    } catch (e) {
      _logger.severe('Error updating traffic controller: $e');
      rethrow;
    }
  }

  /// Get server status and health information
  Future<ServerStatus> getServerStatus() async {
    try {
      final futures = await Future.wait([
        _getAwsServerStatus(),
        _getDatacenterServerStatus(),
      ]);

      final awsServers = futures[0];
      final datacenterServers = futures[1];

      return ServerStatus(
        awsStatus: _calculateOverallHealth(awsServers),
        datacenterStatus: _calculateOverallHealth(datacenterServers),
        awsServers: awsServers,
        datacenterServers: datacenterServers,
        lastChecked: DateTime.now(),
      );
    } catch (e) {
      _logger.severe('Error getting server status: $e');
      rethrow;
    }
  }

  /// Get AWS server status
  Future<List<ServerInfo>> _getAwsServerStatus() async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_awsApiUrl/api/v1/servers/status'),
        headers: {'Authorization': 'Bearer ${await _getAuthToken()}'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as List;
        return data.map((server) => ServerInfo.fromJson(server)).toList();
      } else {
        throw Exception('Failed to get AWS server status: ${response.body}');
      }
    } catch (e) {
      _logger.warning('Error getting AWS server status: $e');
      return [];
    }
  }

  /// Get datacenter server status
  Future<List<ServerInfo>> _getDatacenterServerStatus() async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_datacenterApiUrl/api/v1/servers/status'),
        headers: {'Authorization': 'Bearer ${await _getAuthToken()}'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as List;
        return data.map((server) => ServerInfo.fromJson(server)).toList();
      } else {
        throw Exception(
          'Failed to get datacenter server status: ${response.body}',
        );
      }
    } catch (e) {
      _logger.warning('Error getting datacenter server status: $e');
      return [];
    }
  }

  /// Calculate overall health from server list
  ServerHealth _calculateOverallHealth(List<ServerInfo> servers) {
    if (servers.isEmpty) return ServerHealth.unknown;

    final healthCounts = <ServerHealth, int>{};
    for (final server in servers) {
      healthCounts[server.health] = (healthCounts[server.health] ?? 0) + 1;
    }

    if (healthCounts[ServerHealth.critical] != null &&
        healthCounts[ServerHealth.critical]! > 0) {
      return ServerHealth.critical;
    }

    if (healthCounts[ServerHealth.warning] != null &&
        healthCounts[ServerHealth.warning]! > servers.length / 2) {
      return ServerHealth.warning;
    }

    return ServerHealth.healthy;
  }

  /// Get server configurations for all environments
  Future<Map<String, ServerConfig>> getServerConfigs() async {
    try {
      final data = await _databaseService.find(
        'infrastructure',
        'server_configs',
      );

      if (data != null) {
        final configs = <String, ServerConfig>{};

        for (final entry in data.entries) {
          if (entry.key != 'id' &&
              entry.key != 'created_at' &&
              entry.key != 'updated_at') {
            configs[entry.key] = ServerConfig.fromJson(entry.value);
          }
        }

        return configs;
      } else {
        // Return empty map - no default configs, admin must add servers
        _logger.info(
          'No server configurations found. Admin must add servers through the interface.',
        );
        return <String, ServerConfig>{};
      }
    } catch (e) {
      _logger.severe('Error getting server configs: $e');
      rethrow;
    }
  }

  /// Add a new server configuration
  Future<void> addServerConfig(ServerConfig config) async {
    try {
      // Get existing configurations
      final existingConfigs = await getServerConfigs();

      // Check if environment already exists
      if (existingConfigs.containsKey(config.environment)) {
        throw Exception(
          'Server configuration for environment "${config.environment}" already exists. Use updateServerConfig instead.',
        );
      }

      // Add the new configuration
      existingConfigs[config.environment] = config;

      // Save back to database
      await _saveServerConfigs(existingConfigs);

      _logger.info(
        'Server configuration added for environment: ${config.environment}',
      );
    } catch (e) {
      _logger.severe('Error adding server config: $e');
      rethrow;
    }
  }

  /// Delete a server configuration
  Future<void> deleteServerConfig(String environment) async {
    try {
      // Get existing configurations
      final existingConfigs = await getServerConfigs();

      // Check if environment exists
      if (!existingConfigs.containsKey(environment)) {
        throw Exception(
          'Server configuration for environment "$environment" not found.',
        );
      }

      // Remove the configuration
      existingConfigs.remove(environment);

      // Save back to database
      await _saveServerConfigs(existingConfigs);

      _logger.info(
        'Server configuration deleted for environment: $environment',
      );
    } catch (e) {
      _logger.severe('Error deleting server config: $e');
      rethrow;
    }
  }

  /// Save server configurations to database
  Future<void> _saveServerConfigs(Map<String, ServerConfig> configs) async {
    final configData = <String, dynamic>{};
    for (final entry in configs.entries) {
      configData[entry.key] = entry.value.toJson();
    }
    configData['id'] = 'server_configs';
    configData['updated_at'] = DateTime.now().toIso8601String();

    // Check if document exists
    final existingData = await _databaseService.find(
      'infrastructure',
      'server_configs',
    );

    if (existingData != null) {
      await _databaseService.update(
        'infrastructure',
        'server_configs',
        configData,
      );
    } else {
      configData['created_at'] = DateTime.now().toIso8601String();
      await _databaseService.create('infrastructure', configData);
    }
  }

  /// Update server configuration
  Future<void> updateServerConfig(ServerConfig config) async {
    try {
      // Get existing configurations
      final existingConfigs = await getServerConfigs();

      // Check if environment exists
      if (!existingConfigs.containsKey(config.environment)) {
        throw Exception(
          'Server configuration for environment "${config.environment}" not found. Use addServerConfig instead.',
        );
      }

      // Update the configuration
      existingConfigs[config.environment] = config;

      // Save back to database
      await _saveServerConfigs(existingConfigs);

      // Apply configuration to servers
      await _applyServerConfiguration(config);

      _logger.info('Server configuration updated for ${config.environment}');
    } catch (e) {
      _logger.severe('Error updating server config: $e');
      rethrow;
    }
  }

  /// Apply configuration to actual servers
  Future<void> _applyServerConfiguration(ServerConfig config) async {
    try {
      final futures = <Future>[];

      // Update AWS configuration
      if (config.awsStatus == ServerState.online) {
        futures.add(_updateAwsConfiguration(config));
      }

      // Update datacenter configuration
      if (config.datacenterStatus == ServerState.online) {
        futures.add(_updateDatacenterConfiguration(config));
      }

      await Future.wait(futures);
      _logger.info('Server configuration applied successfully');
    } catch (e) {
      _logger.severe('Error applying server configuration: $e');
      rethrow;
    }
  }

  /// Update AWS server configuration
  Future<void> _updateAwsConfiguration(ServerConfig config) async {
    final response = await _httpClient.post(
      Uri.parse('$_awsApiUrl/api/v1/config/update'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${await _getAuthToken()}',
      },
      body: jsonEncode({
        'environment': config.environment,
        'region': config.awsRegion,
        'database_url': config.databaseUrl,
        'redis_url': config.redisUrl,
        'max_connections': config.maxConnections,
        'timeout': config.timeout,
        'auto_scaling': config.autoScaling,
        'load_balancing': config.loadBalancing,
        'caching_enabled': config.cachingEnabled,
        'ssl_enabled': config.sslEnabled,
        'rate_limiting_enabled': config.rateLimitingEnabled,
        'ddos_protection': config.ddosProtection,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to update AWS configuration: ${response.body}');
    }
  }

  /// Update datacenter server configuration
  Future<void> _updateDatacenterConfiguration(ServerConfig config) async {
    final response = await _httpClient.post(
      Uri.parse('$_datacenterApiUrl/api/v1/config/update'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${await _getAuthToken()}',
      },
      body: jsonEncode({
        'environment': config.environment,
        'ip_address': config.datacenterIp,
        'database_url': config.databaseUrl,
        'redis_url': config.redisUrl,
        'max_connections': config.maxConnections,
        'timeout': config.timeout,
        'auto_scaling': config.autoScaling,
        'load_balancing': config.loadBalancing,
        'caching_enabled': config.cachingEnabled,
        'ssl_enabled': config.sslEnabled,
        'rate_limiting_enabled': config.rateLimitingEnabled,
        'ddos_protection': config.ddosProtection,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception(
        'Failed to update datacenter configuration: ${response.body}',
      );
    }
  }

  /// Test server configuration
  Future<ConfigTestResult> testServerConfiguration(
    String environment,
    String datacenterIp,
    String databaseUrl,
  ) async {
    try {
      final futures = await Future.wait([
        _testDatabaseConnection(databaseUrl),
        _testServerConnectivity(datacenterIp),
      ]);

      final dbTest = futures[0];
      final serverTest = futures[1];

      if (dbTest && serverTest) {
        return ConfigTestResult(success: true, timestamp: DateTime.now());
      } else {
        return ConfigTestResult(
          success: false,
          error:
              'Database: ${dbTest ? 'OK' : 'FAILED'}, Server: ${serverTest ? 'OK' : 'FAILED'}',
          timestamp: DateTime.now(),
        );
      }
    } catch (e) {
      return ConfigTestResult(
        success: false,
        error: e.toString(),
        timestamp: DateTime.now(),
      );
    }
  }

  /// Test database connection
  Future<bool> _testDatabaseConnection(String databaseUrl) async {
    try {
      // This would typically use a database client to test the connection
      // For now, we'll simulate the test
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      _logger.warning('Database connection test failed: $e');
      return false;
    }
  }

  /// Test server connectivity
  Future<bool> _testServerConnectivity(String ipAddress) async {
    try {
      final response = await _httpClient
          .get(Uri.parse('http://$ipAddress/health'), headers: {'timeout': '5'})
          .timeout(const Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      _logger.warning('Server connectivity test failed: $e');
      return false;
    }
  }

  /// Get authentication token for API calls
  Future<String> _getAuthToken() async {
    // This would typically get a JWT token from your auth service
    // For now, return a placeholder
    return 'your-api-token';
  }
}
