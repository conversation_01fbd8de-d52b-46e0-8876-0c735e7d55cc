// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'technician_cubit_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$TechnicianCubitState {

 bool get isLoading; bool get hasError; String? get errorMessage; Technician? get technician;
/// Create a copy of TechnicianCubitState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TechnicianCubitStateCopyWith<TechnicianCubitState> get copyWith => _$TechnicianCubitStateCopyWithImpl<TechnicianCubitState>(this as TechnicianCubitState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TechnicianCubitState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasError, hasError) || other.hasError == hasError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.technician, technician) || other.technician == technician));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,hasError,errorMessage,technician);

@override
String toString() {
  return 'TechnicianCubitState(isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, technician: $technician)';
}


}

/// @nodoc
abstract mixin class $TechnicianCubitStateCopyWith<$Res>  {
  factory $TechnicianCubitStateCopyWith(TechnicianCubitState value, $Res Function(TechnicianCubitState) _then) = _$TechnicianCubitStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, bool hasError, String? errorMessage, Technician? technician
});


$TechnicianCopyWith<$Res>? get technician;

}
/// @nodoc
class _$TechnicianCubitStateCopyWithImpl<$Res>
    implements $TechnicianCubitStateCopyWith<$Res> {
  _$TechnicianCubitStateCopyWithImpl(this._self, this._then);

  final TechnicianCubitState _self;
  final $Res Function(TechnicianCubitState) _then;

/// Create a copy of TechnicianCubitState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? hasError = null,Object? errorMessage = freezed,Object? technician = freezed,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,technician: freezed == technician ? _self.technician : technician // ignore: cast_nullable_to_non_nullable
as Technician?,
  ));
}
/// Create a copy of TechnicianCubitState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TechnicianCopyWith<$Res>? get technician {
    if (_self.technician == null) {
    return null;
  }

  return $TechnicianCopyWith<$Res>(_self.technician!, (value) {
    return _then(_self.copyWith(technician: value));
  });
}
}


/// Adds pattern-matching-related methods to [TechnicianCubitState].
extension TechnicianCubitStatePatterns on TechnicianCubitState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TechnicianCubitState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TechnicianCubitState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TechnicianCubitState value)  $default,){
final _that = this;
switch (_that) {
case _TechnicianCubitState():
return $default(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TechnicianCubitState value)?  $default,){
final _that = this;
switch (_that) {
case _TechnicianCubitState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isLoading,  bool hasError,  String? errorMessage,  Technician? technician)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TechnicianCubitState() when $default != null:
return $default(_that.isLoading,_that.hasError,_that.errorMessage,_that.technician);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isLoading,  bool hasError,  String? errorMessage,  Technician? technician)  $default,) {final _that = this;
switch (_that) {
case _TechnicianCubitState():
return $default(_that.isLoading,_that.hasError,_that.errorMessage,_that.technician);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isLoading,  bool hasError,  String? errorMessage,  Technician? technician)?  $default,) {final _that = this;
switch (_that) {
case _TechnicianCubitState() when $default != null:
return $default(_that.isLoading,_that.hasError,_that.errorMessage,_that.technician);case _:
  return null;

}
}

}

/// @nodoc


class _TechnicianCubitState implements TechnicianCubitState {
  const _TechnicianCubitState({this.isLoading = false, this.hasError = false, this.errorMessage, this.technician});
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool hasError;
@override final  String? errorMessage;
@override final  Technician? technician;

/// Create a copy of TechnicianCubitState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TechnicianCubitStateCopyWith<_TechnicianCubitState> get copyWith => __$TechnicianCubitStateCopyWithImpl<_TechnicianCubitState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TechnicianCubitState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.hasError, hasError) || other.hasError == hasError)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.technician, technician) || other.technician == technician));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,hasError,errorMessage,technician);

@override
String toString() {
  return 'TechnicianCubitState(isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage, technician: $technician)';
}


}

/// @nodoc
abstract mixin class _$TechnicianCubitStateCopyWith<$Res> implements $TechnicianCubitStateCopyWith<$Res> {
  factory _$TechnicianCubitStateCopyWith(_TechnicianCubitState value, $Res Function(_TechnicianCubitState) _then) = __$TechnicianCubitStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, bool hasError, String? errorMessage, Technician? technician
});


@override $TechnicianCopyWith<$Res>? get technician;

}
/// @nodoc
class __$TechnicianCubitStateCopyWithImpl<$Res>
    implements _$TechnicianCubitStateCopyWith<$Res> {
  __$TechnicianCubitStateCopyWithImpl(this._self, this._then);

  final _TechnicianCubitState _self;
  final $Res Function(_TechnicianCubitState) _then;

/// Create a copy of TechnicianCubitState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? hasError = null,Object? errorMessage = freezed,Object? technician = freezed,}) {
  return _then(_TechnicianCubitState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,technician: freezed == technician ? _self.technician : technician // ignore: cast_nullable_to_non_nullable
as Technician?,
  ));
}

/// Create a copy of TechnicianCubitState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TechnicianCopyWith<$Res>? get technician {
    if (_self.technician == null) {
    return null;
  }

  return $TechnicianCopyWith<$Res>(_self.technician!, (value) {
    return _then(_self.copyWith(technician: value));
  });
}
}

// dart format on
