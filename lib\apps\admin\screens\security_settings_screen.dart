import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/admin/admin_routes.dart';
import 'package:shivish/shared/models/config/security_config_model.dart';
import 'package:shivish/shared/models/config/system_config_model.dart';
import 'package:shivish/shared/models/config/refund_config_model.dart';
import 'package:shivish/shared/models/config/ai_config_model.dart';
import 'package:shivish/shared/models/config/voice_command_config_model.dart';
import 'package:shivish/shared/models/config/chatbot_config_model.dart';
import 'package:shivish/shared/providers/system_config_provider.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

class SecuritySettingsScreen extends ConsumerStatefulWidget {
  const SecuritySettingsScreen({super.key});

  @override
  ConsumerState<SecuritySettingsScreen> createState() =>
      _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState
    extends ConsumerState<SecuritySettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _sessionTimeoutController;
  late TextEditingController _maxLoginAttemptsController;
  late TextEditingController _passwordMinLengthController;
  late TextEditingController _passwordExpiryDaysController;

  @override
  void initState() {
    super.initState();
    _sessionTimeoutController = TextEditingController();
    _maxLoginAttemptsController = TextEditingController();
    _passwordMinLengthController = TextEditingController();
    _passwordExpiryDaysController = TextEditingController();
  }

  @override
  void dispose() {
    _sessionTimeoutController.dispose();
    _maxLoginAttemptsController.dispose();
    _passwordMinLengthController.dispose();
    _passwordExpiryDaysController.dispose();
    super.dispose();
  }

  Widget _buildSecuritySettingsForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Session Settings',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    AppTextField(
                      controller: _sessionTimeoutController,
                      label: 'Session Timeout (minutes)',
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter session timeout';
                        }
                        final number = int.tryParse(value);
                        if (number == null || number < 1) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Login Security',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    AppTextField(
                      controller: _maxLoginAttemptsController,
                      label: 'Max Login Attempts',
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter max login attempts';
                        }
                        final number = int.tryParse(value);
                        if (number == null || number < 1) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Password Policy',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    AppTextField(
                      controller: _passwordMinLengthController,
                      label: 'Minimum Password Length',
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter minimum password length';
                        }
                        final number = int.tryParse(value);
                        if (number == null || number < 8) {
                          return 'Password must be at least 8 characters';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    AppTextField(
                      controller: _passwordExpiryDaysController,
                      label: 'Password Expiry (days)',
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter password expiry days';
                        }
                        final number = int.tryParse(value);
                        if (number == null || number < 1) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            AppButton(
              onPressed: _saveConfig,
              child: const Text('Save Security Settings'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Security Settings'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we can pop the current route
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else {
              // Navigate back to settings screen using GoRouter
              context.go(AdminRoutes.settings);
            }
          },
        ),
      ),
      body: ref.watch(systemConfigProvider).when(
            data: (config) {
              // If config is null, create a default security config
              if (config == null) {
                // Set default values
                _sessionTimeoutController.text = '30';
                _maxLoginAttemptsController.text = '5';
                _passwordMinLengthController.text = '8';
                _passwordExpiryDaysController.text = '90';

                return _buildSecuritySettingsForm();
              }

              // Set values from existing config
              _sessionTimeoutController.text =
                  config.security.sessionTimeoutMinutes.toString();
              _maxLoginAttemptsController.text =
                  config.security.maxLoginAttempts.toString();
              _passwordMinLengthController.text =
                  config.security.passwordMinLength.toString();
              _passwordExpiryDaysController.text =
                  config.security.passwordExpiryDays.toString();

              return _buildSecuritySettingsForm();
            },
            loading: () => const LoadingIndicator(),
            error: (error, stack) => Center(
              child: Text(
                'Error: $error',
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ),
    );
  }

  Future<void> _saveConfig() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final securityConfig = SecurityConfigModel(
        sessionTimeoutMinutes: int.parse(_sessionTimeoutController.text),
        maxLoginAttempts: int.parse(_maxLoginAttemptsController.text),
        passwordMinLength: int.parse(_passwordMinLengthController.text),
        passwordExpiryDays: int.parse(_passwordExpiryDaysController.text),
      );

      // Get the current config
      final currentConfig = ref.read(systemConfigProvider).value;

      if (currentConfig == null) {
        // If there's no existing config, create a new one with default values for other fields
        // and our security config
        final defaultConfig = _createDefaultSystemConfig(securityConfig);
        await ref
            .read(systemConfigStateProvider.notifier)
            .updateConfig(defaultConfig);
      } else {
        // If there's an existing config, just update the security part
        await ref
            .read(systemConfigStateProvider.notifier)
            .updateSecurityConfig(securityConfig);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Security settings updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating security settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Helper method to create a default system config with our security settings
  SystemConfigModel _createDefaultSystemConfig(SecurityConfigModel security) {
    return SystemConfigModel(
      security: security,
      refund: const RefundConfigModel(
        maxRefundDays: 7,
        maxRefundPercentage: 100.0,
        requireReason: true,
        requireApproval: true,
        validReasons: ['Defective product', 'Wrong item', 'Not as described', 'Other'],
      ),
      ai: const AIConfigModel(
        modelVersion: '1.0',
        languageModel: 'gpt-3.5-turbo',
        enableVoiceCommands: true,
        enableChatbot: true,
        enableRecommendations: true,
        modelParameters: {'temperature': 0.7, 'topP': 0.9},
      ),
      voiceCommand: const VoiceCommandConfigModel(
        enabled: true,
        wakeWord: 'Hey Assistant',
        sensitivity: 0.7,
        language: 'en-US',
        supportedCommands: ['search', 'navigate', 'help'],
        commandMappings: {'search': 'search_products', 'help': 'show_help'},
      ),
      chatbot: const ChatbotConfigModel(
        enabled: true,
        modelName: 'gpt-3.5-turbo',
        language: 'en',
        temperature: 0.7,
        maxTokens: 1000,
        supportedLanguages: ['en', 'es', 'fr'],
        responseTemplates: {'greeting': 'Hello, how can I help you?'},
        modelParameters: {'presence_penalty': 0.0, 'frequency_penalty': 0.0},
      ),
      additionalSettings: {},
    );
  }
}
