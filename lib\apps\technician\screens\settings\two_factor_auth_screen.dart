import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/providers/settings_provider.dart';
import 'package:shivish/apps/technician/screens/settings/phone_number_settings_screen.dart';
import 'package:shivish/apps/technician/screens/settings/email_settings_screen.dart';
import 'package:shivish/apps/technician/screens/settings/recovery_codes_screen.dart';

class TwoFactorAuthScreen extends ConsumerWidget {
  const TwoFactorAuthScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final twoFactorEnabled = ref.watch(twoFactorEnabledProvider);
    final setTwoFactorEnabled =
        ref.read(twoFactorEnabledProvider.notifier).setTwoFactorEnabled;
    final smsVerification = ref.watch(smsVerificationProvider);
    final setSmsVerification =
        ref.read(smsVerificationProvider.notifier).setSmsVerification;
    final authenticatorApp = ref.watch(authenticatorAppProvider);
    final setAuthenticatorApp =
        ref.read(authenticatorAppProvider.notifier).setAuthenticatorApp;

    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Two-Factor Authentication',
      ),
      body: ListView(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Add an extra layer of security to your account by enabling two-factor authentication.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          SwitchListTile(
            secondary: const Icon(Icons.security),
            title: const Text('Enable Two-Factor Authentication'),
            subtitle: const Text('Require a second form of verification'),
            value: twoFactorEnabled,
            onChanged: (value) {
              setTwoFactorEnabled(value);
              _showSnackBar(
                context,
                'Two-factor authentication ${value ? 'enabled' : 'disabled'}',
              );
            },
          ),
          if (twoFactorEnabled) ...[
            SwitchListTile(
              secondary: const Icon(Icons.sms),
              title: const Text('SMS Verification'),
              subtitle: const Text('Receive verification codes via SMS'),
              value: smsVerification,
              onChanged: (value) {
                setSmsVerification(value);
                _showSnackBar(
                  context,
                  'SMS verification ${value ? 'enabled' : 'disabled'}',
                );
              },
            ),
            SwitchListTile(
              secondary: const Icon(Icons.apps),
              title: const Text('Authenticator App'),
              subtitle: const Text('Use an authenticator app for verification'),
              value: authenticatorApp,
              onChanged: (value) {
                setAuthenticatorApp(value);
                _showSnackBar(
                  context,
                  'Authenticator app ${value ? 'enabled' : 'disabled'}',
                );
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.phone),
              title: const Text('Update Phone Number'),
              subtitle:
                  const Text('Change the phone number for SMS verification'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PhoneNumberSettingsScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.email),
              title: const Text('Email Verification'),
              subtitle: const Text('Configure email verification'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const EmailSettingsScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.backup),
              title: const Text('Recovery Codes'),
              subtitle: const Text('Generate or view recovery codes'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const RecoveryCodesScreen(),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
