import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shivish/shared/providers/auth_provider.dart';

final passwordSettingsProvider =
    AsyncNotifierProvider<PasswordSettingsNotifier, void>(() {
  return PasswordSettingsNotifier();
});

class PasswordSettingsNotifier extends AsyncNotifier<void> {
  @override
  Future<void> build() async {}

  Future<void> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    state = const AsyncLoading();

    try {
      final user = ref.read(authProvider);
      if (user == null) throw Exception('User not authenticated');

      // Re-authenticate user before changing password
      await Supabase.instance.client.auth.signInWithPassword(
        email: user.email!,
        password: currentPassword,
      );

      // Update password
      await Supabase.instance.client.auth.updateUser(
        UserAttributes(password: newPassword),
      );

      state = const AsyncData(null);
    } on AuthException catch (e) {
      state = AsyncError(
        _getErrorMessage(e.statusCode ?? ''),
        StackTrace.current,
      );
      rethrow;
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
      rethrow;
    }
  }

  String _getErrorMessage(String code) {
    switch (code) {
      case '400':
        return 'Current password is incorrect';
      case '401':
        return 'Please log in again before changing your password';
      case '422':
        return 'New password is too weak';
      default:
        return 'Failed to update password: $code';
    }
  }
}
