import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shivish/shared/models/product/product_model.dart';
import 'package:shivish/shared/core/widgets/price_input.dart';

class PriceList extends StatefulWidget {
  final List<ProductModel> products;
  final Function(String, double) onPriceUpdate;
  final Function(String, double) onDiscountUpdate;
  final Function(List<String>) onSelectionChange;

  const PriceList({
    super.key,
    required this.products,
    required this.onPriceUpdate,
    required this.onDiscountUpdate,
    required this.onSelectionChange,
  });

  @override
  State<PriceList> createState() => _PriceListState();
}

class _PriceListState extends State<PriceList> {
  final Set<String> _selectedProducts = {};

  void _toggleSelection(String productId) {
    setState(() {
      if (_selectedProducts.contains(productId)) {
        _selectedProducts.remove(productId);
      } else {
        _selectedProducts.add(productId);
      }
      widget.onSelectionChange(_selectedProducts.toList());
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.products.isEmpty) {
      return const Center(
        child: Text('No products found'),
      );
    }

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Checkbox(
                value: _selectedProducts.length == widget.products.length,
                tristate: _selectedProducts.isNotEmpty &&
                    _selectedProducts.length != widget.products.length,
                onChanged: (bool? value) {
                  setState(() {
                    if (value ?? false) {
                      _selectedProducts.addAll(
                        widget.products.map((p) => p.id),
                      );
                    } else {
                      _selectedProducts.clear();
                    }
                    widget.onSelectionChange(_selectedProducts.toList());
                  });
                },
              ),
              const SizedBox(width: 8),
              Text(
                'Select All (${_selectedProducts.length}/${widget.products.length})',
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: widget.products.length,
            itemBuilder: (context, index) {
              final product = widget.products[index];
              final isSelected = _selectedProducts.contains(product.id);

              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Checkbox(
                            value: isSelected,
                            onChanged: (_) => _toggleSelection(product.id),
                          ),
                          const SizedBox(width: 8),
                          if (product.images.isNotEmpty)
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: CachedNetworkImage(
                                imageUrl: product.images.first,
                                width: 60,
                                height: 60,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => Container(
                                  color: Colors.grey[200],
                                  child: const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                ),
                                errorWidget: (context, url, error) => Container(
                                  color: Colors.grey[200],
                                  child: const Icon(Icons.error),
                                ),
                              ),
                            ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  product.name,
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                ),
                                if (product.description.isNotEmpty) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    product.description,
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: PriceInput(
                              label: 'Regular Price',
                              value: product.price,
                              onChanged: (value) =>
                                  widget.onPriceUpdate(product.id, value),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: PriceInput(
                              label: 'Sale Price',
                              value: product.salePrice ?? product.price,
                              onChanged: (value) {
                                final discount =
                                    ((product.price - value) / product.price) *
                                        100;
                                widget.onDiscountUpdate(
                                    product.id, discount.clamp(0, 100));
                              },
                            ),
                          ),
                        ],
                      ),
                      if (product.compareAtPrice != null &&
                          product.compareAtPrice! > product.price) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Compare at: ₹${product.compareAtPrice!.toStringAsFixed(2)}',
                          style: Theme.of(context)
                              .textTheme
                              .bodySmall
                              ?.copyWith(
                                  decoration: TextDecoration.lineThrough),
                        ),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
