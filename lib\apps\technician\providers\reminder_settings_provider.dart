import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:shivish/shared/models/technician/reminder_settings_model.dart';
import 'package:shivish/shared/providers/auth_provider.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final reminderSettingsProvider =
    AsyncNotifierProvider<ReminderSettingsNotifier, ReminderSettingsModel?>(() {
  return ReminderSettingsNotifier();
});

class ReminderSettingsNotifier extends AsyncNotifier<ReminderSettingsModel?> {
  static const String _collection = 'technician_reminder_settings';

  @override
  Future<ReminderSettingsModel?> build() async {
    try {
      final authService = ref.read(authServiceProvider);
      final userId = authService.currentUser?.id;
      if (userId == null) return null;

      final databaseService = ref.read(databaseServiceProvider);
      final data = await databaseService.find(_collection, userId);

      if (data == null) {
        // Return default settings if none exist
        return const ReminderSettingsModel(
          enableReminders: true,
          enableEmailReminders: true,
          enablePushReminders: true,
          enableSMSReminders: false,
          defaultReminderTime: 30,
          enableCustomReminders: false,
          customReminders: [],
        );
      }

      return ReminderSettingsModel.fromJson(data);
    } catch (e) {
      debugPrint('Error loading reminder settings: $e');
      return null;
    }
  }

  Future<void> updateSettings(ReminderSettingsModel settings) async {
    state = const AsyncValue.loading();
    try {
      final authService = ref.read(authServiceProvider);
      final userId = authService.currentUser?.id;
      if (userId == null) throw Exception('User not authenticated');

      final databaseService = ref.read(databaseServiceProvider);
      final settingsData = settings.toJson();
      settingsData['updated_at'] = DateTime.now().toIso8601String();

      // Try to update first, if not found, create new record
      final existingData = await databaseService.find(_collection, userId);
      if (existingData != null) {
        await databaseService.update(_collection, userId, settingsData);
      } else {
        settingsData['id'] = userId;
        settingsData['created_at'] = DateTime.now().toIso8601String();
        await databaseService.create(_collection, settingsData);
      }

      state = AsyncValue.data(settings);
    } catch (e, st) {
      debugPrint('Error updating reminder settings: $e');
      state = AsyncValue.error(e, st);
    }
  }
}
