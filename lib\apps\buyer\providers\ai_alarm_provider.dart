import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/services/device/device_usage_service.dart';
import '../../../shared/models/alarm/alarm_model.dart';
import 'dart:async';

/// Provider that calculates the AI alarm time based on device usage
final aiAlarmTimeProvider = FutureProvider<DateTime?>((ref) async {
  final deviceUsageService = ref.watch(deviceUsageServiceProvider);
  return deviceUsageService.calculateAIAlarmTime();
});

/// Provider that checks if the AI alarm should be triggered
final shouldTriggerAIAlarmProvider = FutureProvider<bool>((ref) async {
  final deviceUsageService = ref.watch(deviceUsageServiceProvider);
  return deviceUsageService.shouldTriggerAIAlarm();
});

/// Provider that creates an AI alarm based on the last device usage
final createAIAlarmProvider =
    FutureProvider.autoDispose<AlarmModel?>((ref) async {
  final deviceUsageService = ref.watch(deviceUsageServiceProvider);
  final lastUsage = await deviceUsageService.getLastUsageTime();

  if (lastUsage == null) {
    return null;
  }

  // Calculate alarm time (8:30 hours after last usage)
  final alarmTime = lastUsage.add(const Duration(hours: 8, minutes: 30));

  // Create the alarm model
  return AlarmModel(
    id: DateTime.now().millisecondsSinceEpoch.toString(),
    userId: 'mock-user',
    type: AlarmType.aiAlarm,
    time: alarmTime,
    isEnabled: true,
    toneId: 'ai_tone',
    volume: 0.8,
    vibrate: true,
    repeat: AlarmRepeat.once, // AI alarms are one-time by default
    repeatDays: [],
    snoozeDuration: 10,
    maxSnoozeCount: 2,
    label: 'AI Alarm',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
});

/// A notifier that periodically checks if an AI alarm should be created
class AIAlarmNotifier extends StateNotifier<AsyncValue<void>> {
  final Ref ref;
  Timer? _timer;

  AIAlarmNotifier(this.ref) : super(const AsyncValue.data(null)) {
    // Start checking every 15 minutes
    _startPeriodicCheck();
  }

  void _startPeriodicCheck() {
    _timer = Timer.periodic(const Duration(minutes: 15), (_) {
      _checkAndCreateAlarm();
    });
  }

  Future<void> _checkAndCreateAlarm() async {
    try {
      final shouldTrigger = await ref.read(shouldTriggerAIAlarmProvider.future);

      if (shouldTrigger) {
        // Create the AI alarm
        final alarm = await ref.read(createAIAlarmProvider.future);

        if (alarm != null) {
          // In a real app, we would save this alarm to the database
          // For now, we just update the state
          state = const AsyncValue.data(null);
        }
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

final aiAlarmNotifierProvider =
    StateNotifierProvider<AIAlarmNotifier, AsyncValue<void>>((ref) {
  return AIAlarmNotifier(ref);
});
