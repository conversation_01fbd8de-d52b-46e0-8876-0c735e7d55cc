import 'package:flutter/material.dart';

class AnalyticsFilterData {
  final double? minSales;
  final double? maxSales;
  final int? minOrders;
  final int? maxOrders;
  final double? minOrderValue;
  final double? maxOrderValue;
  final double? minSatisfaction;
  final double? maxSatisfaction;
  final String sortBy;
  final bool sortAscending;

  const AnalyticsFilterData({
    this.minSales,
    this.maxSales,
    this.minOrders,
    this.maxOrders,
    this.minOrderValue,
    this.maxOrderValue,
    this.minSatisfaction,
    this.maxSatisfaction,
    this.sortBy = 'sales',
    this.sortAscending = false,
  });

  AnalyticsFilterData copyWith({
    double? minSales,
    double? maxSales,
    int? minOrders,
    int? maxOrders,
    double? minOrderValue,
    double? maxOrderValue,
    double? minSatisfaction,
    double? maxSatisfaction,
    String? sortBy,
    bool? sortAscending,
  }) {
    return AnalyticsFilterData(
      minSales: minSales ?? this.minSales,
      maxSales: maxSales ?? this.maxSales,
      minOrders: minOrders ?? this.minOrders,
      maxOrders: maxOrders ?? this.maxOrders,
      minOrderValue: minOrderValue ?? this.minOrderValue,
      maxOrderValue: maxOrderValue ?? this.maxOrderValue,
      minSatisfaction: minSatisfaction ?? this.minSatisfaction,
      maxSatisfaction: maxSatisfaction ?? this.maxSatisfaction,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }
}

class AnalyticsFilterDialog extends StatefulWidget {
  final AnalyticsFilterData currentFilters;
  final ValueChanged<AnalyticsFilterData> onApply;

  const AnalyticsFilterDialog({
    super.key,
    required this.currentFilters,
    required this.onApply,
  });

  @override
  State<AnalyticsFilterDialog> createState() => _AnalyticsFilterDialogState();
}

class _AnalyticsFilterDialogState extends State<AnalyticsFilterDialog> {
  late AnalyticsFilterData _filters;
  final _formKey = GlobalKey<FormState>();

  final _minSalesController = TextEditingController();
  final _maxSalesController = TextEditingController();
  final _minOrdersController = TextEditingController();
  final _maxOrdersController = TextEditingController();
  final _minOrderValueController = TextEditingController();
  final _maxOrderValueController = TextEditingController();
  final _minSatisfactionController = TextEditingController();
  final _maxSatisfactionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _filters = widget.currentFilters;
    _initializeControllers();
  }

  void _initializeControllers() {
    _minSalesController.text = _filters.minSales?.toString() ?? '';
    _maxSalesController.text = _filters.maxSales?.toString() ?? '';
    _minOrdersController.text = _filters.minOrders?.toString() ?? '';
    _maxOrdersController.text = _filters.maxOrders?.toString() ?? '';
    _minOrderValueController.text = _filters.minOrderValue?.toString() ?? '';
    _maxOrderValueController.text = _filters.maxOrderValue?.toString() ?? '';
    _minSatisfactionController.text =
        _filters.minSatisfaction?.toString() ?? '';
    _maxSatisfactionController.text =
        _filters.maxSatisfaction?.toString() ?? '';
  }

  @override
  void dispose() {
    _minSalesController.dispose();
    _maxSalesController.dispose();
    _minOrdersController.dispose();
    _maxOrdersController.dispose();
    _minOrderValueController.dispose();
    _maxOrderValueController.dispose();
    _minSatisfactionController.dispose();
    _maxSatisfactionController.dispose();
    super.dispose();
  }

  String? _validateNumericRange(String? value, String fieldName) {
    if (value == null || value.isEmpty) return null;
    final number = double.tryParse(value);
    if (number == null) {
      return 'Please enter a valid number for $fieldName';
    }
    if (number < 0) {
      return '$fieldName cannot be negative';
    }
    return null;
  }

  void _applyFilters() {
    if (_formKey.currentState?.validate() ?? false) {
      final newFilters = AnalyticsFilterData(
        minSales: double.tryParse(_minSalesController.text),
        maxSales: double.tryParse(_maxSalesController.text),
        minOrders: int.tryParse(_minOrdersController.text),
        maxOrders: int.tryParse(_maxOrdersController.text),
        minOrderValue: double.tryParse(_minOrderValueController.text),
        maxOrderValue: double.tryParse(_maxOrderValueController.text),
        minSatisfaction: double.tryParse(_minSatisfactionController.text),
        maxSatisfaction: double.tryParse(_maxSatisfactionController.text),
        sortBy: _filters.sortBy,
        sortAscending: _filters.sortAscending,
      );
      widget.onApply(newFilters);
      Navigator.of(context).pop();
    }
  }

  void _clearFilters() {
    setState(() {
      _minSalesController.clear();
      _maxSalesController.clear();
      _minOrdersController.clear();
      _maxOrdersController.clear();
      _minOrderValueController.clear();
      _maxOrderValueController.clear();
      _minSatisfactionController.clear();
      _maxSatisfactionController.clear();
      _filters = _filters.copyWith(
        sortBy: 'sales',
        sortAscending: false,
      );
    });
  }

  Widget _buildRangeFields(
    String label,
    TextEditingController minController,
    TextEditingController maxController,
    String fieldName, {
    bool isInteger = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: minController,
                decoration: const InputDecoration(
                  labelText: 'Min',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) => _validateNumericRange(value, fieldName),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: maxController,
                decoration: const InputDecoration(
                  labelText: 'Max',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) => _validateNumericRange(value, fieldName),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildSortingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Sort By', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _filters.sortBy,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'sales', child: Text('Total Sales')),
                  DropdownMenuItem(
                      value: 'orders', child: Text('Total Orders')),
                  DropdownMenuItem(
                    value: 'average_order',
                    child: Text('Average Order Value'),
                  ),
                  DropdownMenuItem(
                    value: 'satisfaction',
                    child: Text('Customer Satisfaction'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _filters = _filters.copyWith(sortBy: value);
                    });
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            IconButton(
              icon: Icon(
                _filters.sortAscending
                    ? Icons.arrow_upward
                    : Icons.arrow_downward,
              ),
              onPressed: () {
                setState(() {
                  _filters = _filters.copyWith(
                    sortAscending: !_filters.sortAscending,
                  );
                });
              },
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Filter Analytics'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildRangeFields(
                'Total Sales',
                _minSalesController,
                _maxSalesController,
                'sales',
              ),
              _buildRangeFields(
                'Total Orders',
                _minOrdersController,
                _maxOrdersController,
                'orders',
                isInteger: true,
              ),
              _buildRangeFields(
                'Average Order Value',
                _minOrderValueController,
                _maxOrderValueController,
                'order value',
              ),
              _buildRangeFields(
                'Customer Satisfaction',
                _minSatisfactionController,
                _maxSatisfactionController,
                'satisfaction',
              ),
              const SizedBox(height: 16),
              _buildSortingSection(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _clearFilters,
          child: const Text('CLEAR'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('CANCEL'),
        ),
        FilledButton(
          onPressed: _applyFilters,
          child: const Text('APPLY'),
        ),
      ],
    );
  }
}
